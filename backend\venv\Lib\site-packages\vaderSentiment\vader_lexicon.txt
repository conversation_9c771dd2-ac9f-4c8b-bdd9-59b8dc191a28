$:	-1.5	0.80623	[-1, -1, -1, -1, -3, -1, -3, -1, -2, -1]
%)	-0.4	1.0198	[-1, 0, -1, 0, 0, -2, -1, 2, -1, 0]
%-)	-1.5	1.43178	[-2, 0, -2, -2, -1, 2, -2, -3, -2, -3]
&-:	-0.4	1.42829	[-3, -1, 0, 0, -1, -1, -1, 2, -1, 2]
&:	-0.7	0.64031	[0, -1, -1, -1, 1, -1, -1, -1, -1, -1]
( '}{' )	1.6	0.66332	[1, 2, 2, 1, 1, 2, 2, 1, 3, 1]
(%	-0.9	0.9434	[0, 0, 1, -1, -1, -1, -2, -2, -1, -2]
('-:	2.2	1.16619	[4, 1, 4, 3, 1, 2, 3, 1, 2, 1]
(':	2.3	0.9	[1, 3, 3, 2, 2, 4, 2, 3, 1, 2]
((-:	2.1	0.53852	[2, 2, 2, 1, 2, 3, 2, 2, 3, 2]
(*	1.1	1.13578	[2, 1, 1, -1, 1, 2, 2, -1, 2, 2]
(-%	-0.7	1.26886	[-1, 2, 0, -1, -1, -2, 0, 0, -3, -1]
(-*	1.3	1.26886	[4, 1, 2, 0, 2, -1, 1, 2, 1, 1]
(-:	1.6	0.8	[2, 2, 1, 3, 1, 1, 1, 3, 1, 1]
(-:0	2.8	0.87178	[3, 2, 3, 4, 3, 2, 3, 1, 4, 3]
(-:<	-0.4	2.15407	[-3, 3, -1, -1, 2, -1, -2, 3, -3, -1]
(-:o	1.5	0.67082	[3, 1, 1, 2, 2, 2, 1, 1, 1, 1]
(-:O	1.5	0.67082	[3, 1, 1, 2, 2, 2, 1, 1, 1, 1]
(-:{	-0.1	1.57797	[-2, -3, 1, -2, 1, 1, 0, 0, 2, 1]
(-:|>*	1.9	0.83066	[3, 2, 2, 1, 0, 2, 3, 2, 2, 2]
(-;	1.3	1.18743	[3, 2, 3, 0, 1, -1, 1, 2, 1, 1]
(-;|	2.1	1.13578	[3, 2, 2, 4, 1, 1, 1, 4, 2, 1]
(8	2.6	1.0198	[4, 2, 1, 3, 3, 3, 3, 1, 2, 4]
(:	2.2	1.16619	[3, 1, 1, 2, 1, 2, 4, 3, 4, 1]
(:0	2.4	1.11355	[0, 2, 3, 4, 3, 2, 3, 3, 1, 3]
(:<	-0.2	2.03961	[-2, -3, 1, 1, 2, -1, 2, 1, -4, 1]
(:o	2.5	0.92195	[3, 3, 1, 3, 3, 1, 2, 2, 4, 3]
(:O	2.5	0.92195	[3, 3, 1, 3, 3, 1, 2, 2, 4, 3]
(;	1.1	1.22066	[3, 1, 1, -1, 1, 2, 2, -1, 1, 2]
(;<	0.3	1.00499	[1, 2, -1, -1, 0, 0, 1, -1, 1, 1]
(=	2.2	1.16619	[3, 1, 2, 2, 1, 1, 4, 3, 4, 1]
(?:	2.1	0.83066	[2, 2, 1, 3, 2, 2, 4, 1, 2, 2]
(^:	1.5	0.67082	[1, 2, 2, 1, 3, 2, 1, 1, 1, 1]
(^;	1.5	0.5	[1, 2, 2, 1, 2, 1, 2, 1, 1, 2]
(^;0	2.0	0.7746	[2, 2, 1, 2, 1, 4, 2, 2, 2, 2]
(^;o	1.9	0.83066	[2, 2, 1, 2, 1, 4, 2, 1, 2, 2]
(o:	1.6	0.8	[2, 1, 3, 1, 1, 1, 2, 3, 1, 1]
)':	-2.0	0.44721	[-2, -2, -2, -2, -1, -3, -2, -2, -2, -2]
)-':	-2.1	0.53852	[-2, -2, -3, -2, -1, -2, -3, -2, -2, -2]
)-:	-2.1	0.9434	[-3, -2, -4, -1, -3, -2, -2, -2, -1, -1]
)-:<	-2.2	0.4	[-2, -2, -2, -2, -2, -2, -3, -3, -2, -2]
)-:{	-2.1	0.9434	[-1, -3, -2, -1, -2, -2, -3, -4, -1, -2]
):	-1.8	0.87178	[-1, -3, -1, -2, -1, -3, -1, -3, -1, -2]
):<	-1.9	0.53852	[-1, -3, -2, -2, -2, -1, -2, -2, -2, -2]
):{	-2.3	0.78102	[-1, -2, -3, -3, -2, -2, -4, -2, -2, -2]
);<	-2.6	0.8	[-2, -2, -2, -3, -2, -3, -2, -2, -4, -4]
*)	0.6	1.42829	[1, -1, 1, -3, 1, 1, 2, 1, 1, 2]
*-)	0.3	1.61555	[1, -3, -2, 2, 1, 1, -1, 2, 1, 1]
*-:	2.1	1.51327	[2, 2, 4, 4, 2, 1, -1, 4, 1, 2]
*-;	2.4	1.62481	[2, 3, 4, 4, 2, 1, -1, 4, 1, 4]
*:	1.9	1.04403	[2, 1, 1, 3, 1, 2, 4, 3, 1, 1]
*<|:-)	1.6	1.28062	[0, 1, 3, 1, 1, 2, 3, 0, 4, 1]
*\0/*	2.3	1.00499	[2, 0, 3, 1, 3, 3, 2, 3, 3, 3]
*^:	1.6	1.42829	[2, 2, 1, 3, 2, 2, 3, 3, -1, -1]
,-:	1.2	0.4	[1, 1, 2, 1, 1, 1, 1, 1, 2, 1]
---'-;-{@	2.3	1.18743	[0, 1, 3, 4, 2, 3, 2, 2, 2, 4]
--<--<@	2.2	1.249	[0, 1, 2, 4, 2, 1, 3, 2, 3, 4]
.-:	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -2, -1, -2, -1]
..###-:	-1.7	0.78102	[-2, -3, -3, -2, -1, -1, -1, -1, -1, -2]
..###:	-1.9	1.04403	[-4, -1, -3, -1, -2, -2, -1, -3, -1, -1]
/-:	-1.3	0.64031	[-1, -1, -1, -1, -1, -1, -1, -2, -3, -1]
/:	-1.3	0.45826	[-2, -1, -1, -1, -2, -1, -1, -2, -1, -1]
/:<	-1.4	0.4899	[-1, -2, -2, -1, -1, -1, -1, -1, -2, -2]
/=	-0.9	0.53852	[-1, -1, -1, 0, -1, -2, -1, -1, -1, 0]
/^:	-1.0	0.7746	[-2, -1, -2, 1, -1, -1, -1, -1, -1, -1]
/o:	-1.4	0.66332	[0, -2, -1, -1, -2, -2, -1, -2, -1, -2]
0-8	0.1	1.44568	[2, -1, -2, 0, 2, 0, 2, 0, -2, 0]
0-|	-1.2	0.4	[-2, -1, -1, -1, -1, -1, -1, -1, -2, -1]
0:)	1.9	1.04403	[2, 2, 2, 1, 0, 2, 4, 1, 3, 2]
0:-)	1.4	0.91652	[2, 1, 0, 1, 2, 3, 2, 1, 2, 0]
0:-3	1.5	0.92195	[2, 1, 0, 2, 2, 3, 2, 1, 2, 0]
0:03	1.9	1.22066	[2, 3, 2, 0, 0, 1, 4, 2, 3, 2]
0;^)	1.6	0.91652	[0, 1, 3, 1, 2, 1, 2, 1, 2, 3]
0_o	-0.3	0.78102	[0, -2, 0, 1, 0, 0, -1, 0, -1, 0]
10q	2.1	1.22066	[1, 3, 1, 2, 1, 4, 3, 4, 1, 1]
1337	2.1	1.13578	[3, 1, 4, 0, 2, 3, 1, 2, 2, 3]
143	3.2	0.74833	[4, 4, 2, 3, 2, 3, 4, 3, 4, 3]
1432	2.6	0.8	[4, 3, 3, 2, 2, 4, 2, 2, 2, 2]
14aa41	2.4	0.91652	[3, 2, 2, 4, 2, 2, 1, 2, 4, 2]
182	-2.9	1.3	[-4, 0, -3, -3, -1, -3, -4, -4, -4, -3]
187	-3.1	1.22066	[-4, 0, -4, -3, -2, -4, -3, -3, -4, -4]
2g2b4g	2.8	0.6	[4, 2, 3, 2, 3, 3, 3, 3, 2, 3]
2g2bt	-0.1	1.57797	[-1, 2, -1, 1, 0, 2, 0, -3, -2, 1]
2qt	2.1	0.83066	[3, 3, 3, 3, 2, 1, 2, 1, 2, 1]
3:(	-2.2	0.87178	[-4, -3, -2, -3, -2, -1, -1, -2, -2, -2]
3:)	0.5	1.28452	[-2, 1, -2, 1, 1, 1, 1, 2, 1, 1]
3:-(	-2.3	0.78102	[-2, -3, -2, -2, -2, -2, -4, -1, -3, -2]
3:-)	-1.4	1.35647	[-1, -2, 1, 1, -2, -2, -3, -1, -3, -2]
4col	-2.2	1.16619	[-2, -3, -1, -3, -4, -1, -2, -1, -4, -1]
4q	-3.1	1.51327	[-3, -3, -4, -2, -4, -4, -4, 1, -4, -4]
5fs	1.5	1.11803	[1, 2, 1, 1, 2, 3, 2, 3, -1, 1]
8)	1.9	0.7	[2, 2, 2, 1, 1, 2, 2, 3, 3, 1]
8-d	1.7	0.64031	[1, 2, 0, 2, 2, 2, 2, 2, 2, 2]
8-o	-0.3	0.78102	[1, -1, 0, 0, 0, -1, 0, -2, 0, 0]
86	-1.6	1.0198	[-1, -1, -1, -1, -1, -4, -1, -2, -1, -3]
8d	2.9	0.53852	[3, 3, 4, 2, 3, 3, 3, 3, 2, 3]
:###..	-2.4	0.91652	[-3, -2, -4, -3, -1, -2, -2, -3, -1, -3]
:$	-0.2	1.83303	[-2, -1, 0, 0, -1, 1, 4, -3, 1, -1]
:&	-0.6	1.0198	[-2, -1, 0, 0, -1, -1, 1, -2, 1, -1]
:'(	-2.2	0.74833	[-2, -1, -2, -2, -2, -2, -4, -3, -2, -2]
:')	2.3	0.78102	[3, 1, 3, 2, 2, 2, 2, 4, 2, 2]
:'-(	-2.4	0.66332	[-2, -1, -2, -3, -2, -3, -3, -3, -2, -3]
:'-)	2.7	0.64031	[2, 1, 3, 3, 3, 3, 3, 3, 3, 3]
:(	-1.9	1.13578	[-2, -3, -2, 0, -1, -1, -2, -3, -1, -4]
:)	2.0	1.18322	[2, 2, 1, 1, 1, 1, 4, 3, 4, 1]
:*	2.5	1.0247	[3, 2, 1, 1, 2, 3, 4, 3, 4, 2]
:-###..	-2.5	0.92195	[-3, -2, -3, -2, -4, -3, -1, -3, -1, -3]
:-&	-0.5	0.92195	[-1, -1, 0, -1, -1, -1, -1, 0, 2, -1]
:-(	-1.5	0.5	[-2, -1, -1, -1, -2, -2, -2, -1, -2, -1]
:-)	1.3	0.45826	[1, 1, 1, 1, 2, 1, 2, 1, 2, 1]
:-))	2.8	1.07703	[3, 4, 4, 1, 2, 2, 4, 2, 4, 2]
:-*	1.7	0.64031	[1, 2, 1, 1, 1, 3, 2, 2, 2, 2]
:-,	1.1	0.53852	[1, 1, 1, 0, 1, 1, 1, 1, 2, 2]
:-.	-0.9	0.53852	[-1, -1, 0, -1, 0, -1, -1, -1, -2, -1]
:-/	-1.2	0.6	[0, -1, -1, -1, -1, -2, -2, -1, -1, -2]
:-<	-1.5	0.5	[-2, -1, -1, -2, -1, -2, -2, -1, -2, -1]
:-d	2.3	0.45826	[2, 2, 3, 3, 2, 3, 2, 2, 2, 2]
:-D	2.3	0.45826	[2, 2, 3, 3, 2, 3, 2, 2, 2, 2]
:-o	0.1	1.3	[2, -1, -2, 0, 1, 1, 2, 0, -1, -1]
:-p	1.2	0.4	[1, 2, 1, 1, 1, 1, 2, 1, 1, 1]
:-[	-1.6	0.4899	[-1, -2, -1, -2, -2, -1, -2, -1, -2, -2]
:-\	-0.9	0.3	[-1, -1, -1, -1, -1, -1, -1, 0, -1, -1]
:-c	-1.3	0.45826	[-1, -1, -1, -2, -2, -1, -2, -1, -1, -1]
:-p	1.5	0.5	[1, 1, 1, 1, 1, 2, 2, 2, 2, 2]
:-|	-0.7	0.64031	[-1, -1, 0, 0, 0, -1, -1, -2, 0, -1]
:-||	-2.5	0.67082	[-2, -2, -2, -3, -2, -3, -3, -2, -2, -4]
:-Þ	0.9	1.04403	[1, -1, 1, 2, 1, -1, 1, 2, 2, 1]
:/	-1.4	0.66332	[-1, -1, -1, -1, -1, -1, -3, -2, -2, -1]
:3	2.3	1.26886	[4, 1, 1, 1, 2, 2, 4, 3, 4, 1]
:<	-2.1	0.7	[-3, -1, -2, -2, -2, -2, -3, -3, -2, -1]
:>	2.1	1.22066	[3, 1, 1, 1, 1, 2, 4, 3, 4, 1]
:?)	1.3	0.64031	[3, 1, 1, 1, 1, 2, 1, 1, 1, 1]
:?c	-1.6	0.4899	[-1, -2, -1, -1, -2, -2, -1, -2, -2, -2]
:@	-2.5	0.80623	[-1, -3, -3, -2, -1, -3, -3, -3, -3, -3]
:d	2.3	1.1	[4, 2, 2, 1, 2, 1, 4, 3, 3, 1]
:D	2.3	1.1	[4, 2, 2, 1, 2, 1, 4, 3, 3, 1]
:l	-1.7	0.9	[-1, -3, -1, -1, -1, -3, -2, -3, -1, -1]
:o	-0.4	1.35647	[2, -1, -2, 0, 1, 0, -3, 0, -1, 0]
:p	1.0	0.7746	[-1, 1, 1, 1, 1, 1, 2, 1, 2, 1]
:s	-1.2	0.9798	[-2, -2, -1, -1, -1, 1, -3, -1, -1, -1]
:[	-2.0	0.63246	[-2, -2, -1, -2, -2, -3, -3, -2, -1, -2]
:\	-1.3	0.45826	[-2, -1, -1, -1, -1, -1, -2, -1, -1, -2]
:]	2.2	1.16619	[3, 1, 1, 1, 3, 1, 4, 2, 2, 4]
:^)	2.1	1.13578	[3, 2, 4, 1, 1, 1, 1, 2, 4, 2]
:^*	2.6	0.91652	[2, 1, 2, 3, 4, 4, 3, 2, 3, 2]
:^/	-1.2	0.6	[-2, -1, -2, 0, -1, -1, -1, -1, -2, -1]
:^\	-1.0	0.44721	[-1, -1, -1, -1, -1, -2, 0, -1, -1, -1]
:^|	-1.0	0.0	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
:c	-2.1	0.83066	[-3, -2, -1, -2, -2, -1, -3, -3, -3, -1]
:c)	2.0	1.18322	[2, 1, 1, 1, 1, 2, 3, 4, 4, 1]
:o)	2.1	0.9434	[1, 3, 3, 1, 1, 3, 2, 3, 1, 3]
:o/	-1.4	0.4899	[-1, -1, -1, -2, -1, -1, -2, -2, -1, -2]
:o\	-1.1	0.3	[-1, -1, -1, -2, -1, -1, -1, -1, -1, -1]
:o|	-0.6	1.0198	[0, 0, 0, 0, -1, 0, 0, -3, 0, -2]
:P	1.4	0.8	[3, 1, 0, 2, 1, 1, 2, 2, 1, 1]
:{	-1.9	0.83066	[-2, -1, -1, -2, -2, -1, -3, -3, -3, -1]
:|	-0.4	1.11355	[-1, -1, 0, -1, -1, -1, 1, -2, 2, 0]
:}	2.1	1.22066	[3, 1, 1, 1, 2, 1, 4, 3, 4, 1]
:Þ	1.1	0.53852	[1, 1, 1, 1, 0, 1, 1, 2, 2, 1]
;)	0.9	1.04403	[2, -1, 1, 1, 1, 1, -1, 2, 2, 1]
;-)	1.0	1.73205	[1, -2, 1, -2, 1, 4, 2, 1, 2, 2]
;-*	2.2	0.74833	[2, 2, 1, 3, 4, 2, 2, 2, 2, 2]
;-]	0.7	1.67631	[1, -2, 1, -3, 1, 2, 2, 1, 2, 2]
;d	0.8	1.249	[2, -1, 2, 1, 1, 1, -2, 2, 1, 1]
;D	0.8	1.249	[2, -1, 2, 1, 1, 1, -2, 2, 1, 1]
;]	0.6	1.11355	[1, -1, 1, 1, 1, 1, -2, 2, 1, 1]
;^)	1.4	0.91652	[2, 2, 1, 2, 1, 2, -1, 1, 2, 2]
</3	-3.0	1.0	[-3, -3, -4, -3, -4, -1, -2, -2, -4, -4]
<3	1.9	1.13578	[3, 2, 1, 3, 1, 4, 2, 0, 1, 2]
<:	2.1	1.13578	[3, 2, 4, 1, 1, 1, 1, 2, 4, 2]
<:-|	-1.4	0.66332	[-1, -1, -1, -2, -2, -1, -3, -1, -1, -1]
=)	2.2	1.16619	[4, 2, 3, 3, 2, 1, 1, 4, 1, 1]
=-3	2.0	0.63246	[2, 2, 2, 3, 1, 3, 1, 2, 2, 2]
=-d	2.4	0.91652	[2, 2, 1, 3, 2, 4, 2, 4, 2, 2]
=-D	2.4	0.91652	[2, 2, 1, 3, 2, 4, 2, 4, 2, 2]
=/	-1.4	0.91652	[-1, -1, -1, -1, -1, -2, -1, -1, -4, -1]
=3	2.1	0.7	[3, 1, 2, 1, 2, 3, 2, 2, 3, 2]
=d	2.3	0.45826	[3, 2, 2, 2, 2, 3, 2, 2, 2, 3]
=D	2.3	0.45826	[3, 2, 2, 2, 2, 3, 2, 2, 2, 3]
=l	-1.2	0.6	[-2, -1, -1, 0, -2, -1, -2, -1, -1, -1]
=\	-1.2	0.6	[-2, -1, -1, 0, -2, -1, -2, -1, -1, -1]
=]	1.6	0.8	[2, 1, 3, 1, 1, 1, 2, 3, 1, 1]
=p	1.3	0.9	[2, 1, 2, 1, 2, -1, 1, 2, 2, 1]
=|	-0.8	0.87178	[-2, -1, -2, 1, 0, -1, 0, -1, -1, -1]
>-:	-2.0	0.89443	[-2, -3, -2, -4, -1, -1, -1, -2, -2, -2]
>.<	-1.3	0.45826	[-1, -2, -1, -2, -2, -1, -1, -1, -1, -1]
>:	-2.1	1.13578	[-4, -1, -1, -4, -2, -3, -1, -1, -2, -2]
>:(	-2.7	0.64031	[-2, -3, -2, -3, -3, -2, -4, -2, -3, -3]
>:)	0.4	1.42829	[1, 1, 2, 1, -1, -2, 1, 2, -2, 1]
>:-(	-2.7	0.78102	[-3, -2, -3, -2, -4, -2, -3, -2, -2, -4]
>:-)	-0.4	1.68523	[1, 2, 1, -2, -2, -1, -1, -3, -1, 2]
>:/	-1.6	0.8	[-1, -2, -1, -3, -1, -1, -1, -1, -2, -3]
>:o	-1.2	1.16619	[-3, -1, -2, 0, -2, -2, 0, -1, 1, -2]
>:p	1.0	0.7746	[-1, 1, 1, 2, 1, 2, 1, 1, 1, 1]
>:[	-2.1	0.53852	[-2, -2, -2, -2, -3, -3, -2, -1, -2, -2]
>:\	-1.7	0.64031	[-1, -2, -1, -2, -2, -3, -1, -1, -2, -2]
>;(	-2.9	0.7	[-3, -4, -3, -2, -2, -3, -3, -3, -2, -4]
>;)	0.1	1.04403	[-1, 1, 0, -1, 2, 0, -1, 1, 1, -1]
>_>^	2.1	0.9434	[2, 2, 1, 4, 3, 2, 1, 3, 1, 2]
@:	-2.1	0.9434	[-3, -2, -3, -1, -2, -4, -1, -2, -1, -2]
@>-->--	2.1	1.22066	[1, 1, 0, 2, 4, 2, 4, 2, 3, 2]
@}-;-'---	2.2	1.32665	[0, 1, 3, 2, 1, 4, 4, 1, 3, 3]
aas	2.5	0.80623	[2, 3, 3, 4, 1, 2, 3, 2, 2, 3]
aayf	2.7	0.78102	[2, 3, 2, 4, 3, 2, 2, 3, 4, 2]
afu	-2.9	0.83066	[-3, -3, -3, -3, -3, -1, -4, -4, -2, -3]
alol	2.8	0.74833	[2, 2, 2, 3, 3, 2, 3, 4, 4, 3]
ambw	2.9	0.7	[2, 3, 4, 2, 3, 2, 3, 3, 4, 3]
aml	3.4	0.66332	[4, 3, 2, 4, 3, 3, 4, 4, 3, 4]
atab	-1.9	1.22066	[-2, 0, -1, -2, -1, -1, -2, -4, -4, -2]
awol	-1.3	0.78102	[0, -1, -1, -1, -1, -1, -2, -2, -3, -1]
ayc	0.2	0.9798	[0, 1, -1, 1, 0, 1, 0, -1, 2, -1]
ayor	-1.2	0.6	[-1, -1, -2, -2, -1, -1, -1, 0, -2, -1]
aug-00	0.3	1.18743	[2, 0, -2, 0, 0, 1, -1, 2, 1, 0]
bfd	-2.7	0.78102	[-3, -2, -4, -2, -3, -2, -3, -2, -4, -2]
bfe	-2.6	1.35647	[-3, -3, -4, -2, -3, -2, 1, -3, -4, -3]
bff	2.9	0.83066	[3, 3, 4, 2, 4, 2, 2, 3, 4, 2]
bffn	1.0	0.89443	[2, 1, -1, 1, 0, 1, 2, 1, 2, 1]
bl	2.3	1.1	[2, 1, 4, 1, 2, 2, 4, 3, 1, 3]
bsod	-2.2	1.07703	[-1, -4, -3, -3, 0, -2, -3, -2, -2, -2]
btd	-2.1	0.83066	[-1, -2, -3, -3, -3, -1, -3, -2, -1, -2]
btdt	-0.1	1.22066	[0, -1, 0, -1, 0, 3, 1, -1, -1, -1]
bz	0.4	1.35647	[-1, 0, 0, 0, 4, 1, -1, 1, 0, 0]
b^d	2.6	0.8	[3, 2, 2, 4, 3, 1, 3, 3, 3, 2]
cwot	-2.3	0.45826	[-3, -2, -2, -2, -2, -3, -2, -3, -2, -2]
d-':	-2.5	0.67082	[-3, -3, -2, -2, -2, -4, -2, -3, -2, -2]
d8	-3.2	0.6	[-3, -3, -3, -3, -4, -4, -2, -3, -3, -4]
d:	-2.9	0.83066	[-3, -3, -3, -3, -2, -4, -1, -3, -3, -4]
d:<	-3.2	0.9798	[-4, -4, -4, -1, -3, -3, -4, -2, -3, -4]
d;	-2.9	0.83066	[-1, -3, -3, -3, -3, -4, -2, -3, -3, -4]
d=	-3.0	0.89443	[-4, -3, -3, -3, -2, -4, -1, -3, -3, -4]
doa	-2.3	1.00499	[-2, -3, -3, -2, -2, -2, -4, 0, -2, -3]
dx	-3.0	0.63246	[-3, -2, -3, -3, -4, -3, -4, -2, -3, -3]
ez	1.5	0.67082	[3, 2, 2, 1, 1, 1, 2, 1, 1, 1]
fav	2.4	0.91652	[3, 1, 3, 2, 2, 3, 1, 2, 3, 4]
fcol	-1.8	0.74833	[-2, -2, -1, -2, -1, -2, -1, -3, -3, -1]
ff	1.8	1.249	[4, 2, 1, 2, 1, 3, 3, 0, 2, 0]
ffs	-2.8	0.9798	[-2, -2, -3, -3, -2, -4, -4, -4, -1, -3]
fkm	-2.4	1.35647	[-4, -1, -4, -2, -2, -3, -1, 0, -3, -4]
foaf	1.8	1.249	[2, 1, 2, 0, 4, 1, 1, 1, 2, 4]
ftw	2.0	0.7746	[2, 1, 1, 2, 2, 2, 3, 3, 1, 3]
fu	-3.7	0.45826	[-3, -4, -4, -3, -3, -4, -4, -4, -4, -4]
fubar	-3.0	1.09545	[-4, -3, -3, -4, -3, -3, -3, -4, 0, -3]
fwb	2.5	1.43178	[2, 3, 4, 0, 1, 2, 4, 1, 4, 4]
fyi	0.8	1.66132	[0, 1, 0, -1, 0, 0, 4, 4, 0, 0]
fysa	0.4	0.91652	[0, 0, 0, 1, 0, 3, 0, 0, 0, 0]
g1	1.4	0.4899	[2, 1, 1, 1, 2, 1, 2, 1, 1, 2]
gg	1.2	0.74833	[0, 2, 2, 1, 0, 1, 2, 2, 1, 1]
gga	1.7	0.45826	[2, 2, 1, 2, 2, 1, 2, 2, 1, 2]
gigo	-0.6	1.11355	[-2, -1, 1, 0, 0, 0, -1, -2, -2, 1]
gj	2.0	1.0	[2, 1, 2, 1, 1, 3, 4, 2, 3, 1]
gl	1.3	0.64031	[1, 1, 1, 1, 3, 1, 1, 2, 1, 1]
gla	2.5	0.92195	[1, 2, 2, 4, 2, 4, 2, 3, 3, 2]
gn	1.2	0.74833	[1, 1, 1, 1, 3, 1, 1, 2, 1, 0]
gr8	2.7	0.78102	[1, 3, 3, 4, 3, 2, 3, 2, 3, 3]
grrr	-0.4	1.42829	[-2, -1, 0, 1, -2, -1, -1, 3, 0, -1]
gt	1.1	0.53852	[1, 1, 1, 1, 1, 1, 2, 1, 0, 2]
h&k	2.3	0.78102	[2, 2, 2, 3, 4, 2, 3, 2, 1, 2]
hagd	2.2	0.87178	[2, 2, 3, 2, 1, 3, 4, 1, 2, 2]
hagn	2.2	0.87178	[2, 2, 3, 2, 1, 3, 4, 1, 2, 2]
hago	1.2	0.4	[1, 2, 1, 1, 1, 2, 1, 1, 1, 1]
hak	1.9	0.7	[3, 1, 2, 2, 1, 2, 3, 2, 1, 2]
hand	2.2	0.87178	[2, 2, 1, 3, 2, 3, 4, 1, 2, 2]
heart	3.2	0.63246	[3, 3, 4, 3, 4, 2, 3, 4, 3, 3]
hearts	3.3	0.48305	[4, 3, 3, 3, 4, 3, 3, 3, 4, 3]
hho1/2k	1.4	1.11355	[1, -1, 2, 3, 1, 1, 1, 2, 3, 1]
hhoj	2.0	1.09545	[4, 2, 1, 1, 2, 1, 1, 4, 2, 2]
hhok	0.9	0.9434	[1, 2, 1, 0, -1, 0, 2, 1, 1, 2]
hugz	2.0	0.7746	[2, 3, 1, 3, 1, 3, 1, 2, 2, 2]
hi5	1.9	0.53852	[2, 2, 2, 1, 3, 2, 1, 2, 2, 2]
idk	-0.4	0.66332	[0, 0, 0, 0, -1, -2, 0, 0, 0, -1]
ijs	0.7	1.84662	[0, -1, 0, -1, 0, 4, 0, 4, -1, 2]
ilu	3.4	0.66332	[3, 4, 3, 4, 2, 3, 4, 3, 4, 4]
iluaaf	2.7	1.1	[3, 3, 3, 2, 3, 0, 4, 3, 2, 4]
ily	3.4	0.66332	[3, 4, 3, 4, 2, 3, 4, 3, 4, 4]
ily2	2.6	0.66332	[3, 2, 3, 2, 3, 2, 3, 4, 2, 2]
iou	0.7	1.34536	[0, 0, -1, 2, 0, 0, 0, 4, 1, 1]
iyq	2.3	1.18743	[3, 3, 1, 1, 2, 1, 4, 4, 3, 1]
j/j	2.0	1.34164	[4, 1, 1, 1, 1, 4, 4, 1, 2, 1]
j/k	1.6	1.2	[1, 2, 1, 3, 0, 0, 2, 2, 1, 4]
j/p	1.4	0.66332	[1, 1, 0, 2, 1, 2, 2, 2, 1, 2]
j/t	-0.2	1.46969	[1, -1, -1, -2, 1, 1, 2, -2, 1, -2]
j/w	1.0	1.0	[1, 1, 1, 3, 0, 0, 0, 2, 0, 2]
j4f	1.4	0.8	[2, 1, 1, 0, 3, 1, 1, 1, 2, 2]
j4g	1.7	1.18743	[1, 4, 1, 1, 3, 1, 3, 0, 2, 1]
jho	0.8	0.4	[1, 1, 1, 1, 0, 1, 1, 1, 0, 1]
jhomf	1.0	0.63246	[1, 1, 1, 0, 1, 0, 2, 2, 1, 1]
jj	1.0	0.63246	[1, 1, 1, 1, 2, 0, 2, 1, 1, 0]
jk	0.9	1.22066	[1, 0, 0, 1, 0, 0, 2, 1, 4, 0]
jp	0.8	0.74833	[1, 1, 1, 0, 2, 0, 2, 0, 1, 0]
jt	0.9	0.83066	[1, 1, 0, 2, 2, 0, 2, 0, 1, 0]
jw	1.6	1.68523	[3, 0, 0, 0, 0, 0, 3, 4, 2, 4]
jealz	-1.2	0.9798	[-1, -1, -1, 1, -2, -2, -1, -3, -1, -1]
k4y	2.3	1.00499	[2, 1, 1, 2, 4, 2, 3, 4, 2, 2]
kfy	2.3	0.64031	[2, 2, 2, 1, 3, 2, 3, 3, 2, 3]
kia	-3.2	0.6	[-3, -3, -3, -4, -3, -2, -3, -3, -4, -4]
kk	1.5	1.0247	[2, 1, 0, 0, 1, 2, 3, 3, 2, 1]
kmuf	2.2	1.4	[2, 2, 2, 3, 4, 3, -1, 1, 4, 2]
l	2.0	0.7746	[2, 1, 2, 3, 2, 3, 1, 3, 2, 1]
l&r	2.2	0.74833	[3, 2, 2, 3, 1, 3, 1, 3, 2, 2]
laoj	1.3	1.73494	[1, -2, -1, 3, 3, 2, 4, 1, 1, 1]
lmao	2.0	1.18322	[3, 0, 3, 0, 3, 1, 3, 2, 3, 2]
lmbao	1.8	1.77764	[3, 2, 2, 2, 1, 3, -3, 2, 4, 2]
lmfao	2.5	1.28452	[3, 2, 3, 3, 3, -1, 4, 2, 3, 2]
lmso	2.7	0.78102	[3, 3, 4, 3, 3, 1, 3, 3, 2, 2]
lol	2.9	0.83066	[4, 2, 2, 2, 4, 2, 3, 3, 4, 3]
lolz	2.7	0.78102	[2, 3, 3, 2, 2, 4, 4, 3, 2, 2]
lts	1.6	0.66332	[1, 1, 2, 2, 1, 3, 1, 1, 2, 2]
ly	2.6	0.91652	[2, 2, 1, 3, 4, 4, 3, 2, 2, 3]
ly4e	2.7	0.78102	[3, 3, 3, 2, 1, 3, 3, 4, 2, 3]
lya	3.3	0.78102	[3, 4, 4, 4, 2, 2, 3, 4, 3, 4]
lyb	3.0	0.63246	[3, 3, 4, 3, 2, 3, 2, 4, 3, 3]
lyl	3.1	0.7	[4, 3, 4, 3, 2, 3, 3, 2, 4, 3]
lylab	2.7	0.78102	[3, 3, 3, 1, 3, 4, 2, 2, 3, 3]
lylas	2.6	0.8	[3, 3, 3, 1, 3, 4, 2, 2, 2, 3]
lylb	1.6	1.56205	[2, 2, 3, -2, 4, 1, 3, 1, 1, 1]
m8	1.4	1.0198	[3, 0, 1, 0, 1, 3, 2, 2, 1, 1]
mia	-1.2	0.4	[-2, -1, -1, -2, -1, -1, -1, -1, -1, -1]
mml	2.0	1.0	[1, 1, 2, 3, 3, 2, 1, 2, 4, 1]
mofo	-2.4	2.2	[-4, -4, -4, 0, -3, -2, -2, -4, 3, -4]
muah	2.8	1.07703	[1, 2, 4, 4, 4, 2, 4, 2, 2, 3]
mubar	-1.0	2.36643	[-4, -2, -3, -2, -2, -2, 1, 4, 2, -2]
musm	0.9	2.07123	[-1, 1, 1, 1, 4, 3, 1, -4, 1, 2]
mwah	2.5	0.80623	[2, 2, 2, 4, 2, 3, 2, 2, 4, 2]
n1	1.9	1.04403	[1, 1, 3, 2, 2, 3, 4, 1, 1, 1]
nbd	1.3	1.34536	[2, 1, 0, 0, 0, 4, 2, 0, 3, 1]
nbif	-0.5	0.67082	[-1, -2, 0, 0, 0, 0, -1, -1, 0, 0]
nfc	-2.7	0.9	[-3, -2, -2, -3, -1, -2, -4, -3, -4, -3]
nfw	-2.4	1.0198	[-2, -2, -1, -3, -1, -2, -4, -3, -4, -2]
nh	2.2	0.6	[2, 2, 2, 2, 1, 3, 3, 3, 2, 2]
nimby	-0.8	0.6	[0, 0, -1, 0, -1, -2, -1, -1, -1, -1]
nimjd	-0.7	0.78102	[0, -2, -1, -2, 0, -1, 0, 0, 0, -1]
nimq	-0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, -2, 0]
nimy	-1.4	1.68523	[-1, -2, -3, -2, -1, 2, -3, 0, 0, -4]
nitl	-1.5	0.92195	[-1, -1, -2, -3, -1, -3, -1, -2, 0, -1]
nme	-2.1	1.13578	[-1, -2, -2, -1, -4, -2, -3, -3, 0, -3]
noyb	-0.7	1.67631	[-1, -2, 0, -1, -1, -2, -2, -1, 4, -1]
np	1.4	1.0198	[0, 1, 1, 1, 1, 2, 2, 4, 1, 1]
ntmu	1.4	0.66332	[1, 1, 0, 1, 2, 2, 2, 2, 2, 1]
o-8	-0.5	1.5	[2, -1, 0, 0, -2, -2, 0, -2, 2, -2]
o-:	-0.3	1.18743	[2, -1, 0, 0, -1, -2, 0, -2, 1, 0]
o-|	-1.1	0.53852	[-1, -1, -1, 0, -1, -1, -1, -2, -2, -1]
o.o	-0.6	0.8	[-1, -1, -2, 0, 1, 0, -1, 0, -1, -1]
O.o	-0.6	0.8	[-1, -1, -2, 0, 1, 0, -1, 0, -1, -1]
o.O	-0.6	0.8	[-1, -1, -2, 0, 1, 0, -1, 0, -1, -1]
o:	-0.2	0.87178	[-1, 0, -1, -2, 0, 1, 0, 1, 0, 0]
o:)	1.5	0.67082	[3, 1, 1, 2, 2, 2, 1, 1, 1, 1]
o:-)	2.0	1.18322	[1, 4, 1, 2, 4, 1, 1, 2, 3, 1]
o:-3	2.2	0.9798	[1, 4, 2, 3, 3, 2, 1, 2, 3, 1]
o:3	2.3	0.78102	[3, 3, 2, 2, 1, 2, 4, 2, 2, 2]
o:<	-0.3	1.1	[-1, -1, -2, 0, -1, 0, 1, 2, 0, -1]
o;^)	1.6	0.8	[1, 2, 1, 2, 1, 2, 2, 0, 3, 2]
ok	1.6	1.42829	[0, 0, 1, 1, 1, 4, 3, 4, 1, 1]
o_o	-0.5	0.92195	[0, -1, 0, -2, -2, 0, -1, 1, 0, 0]
O_o	-0.5	0.92195	[0, -1, 0, -2, -2, 0, -1, 1, 0, 0]
o_O	-0.5	0.92195	[0, -1, 0, -2, -2, 0, -1, 1, 0, 0]
pita	-2.4	1.2	[-2, -1, -1, -4, -4, -2, -4, -2, -3, -1]
pls	0.3	0.45826	[0, 1, 1, 1, 0, 0, 0, 0, 0, 0]
plz	0.3	0.45826	[0, 1, 1, 1, 0, 0, 0, 0, 0, 0]
pmbi	0.8	1.32665	[3, 0, 0, 1, 1, -2, 2, 2, 0, 1]
pmfji	0.3	0.78102	[0, 0, 1, 0, 2, -1, 0, 1, 0, 0]
pmji	0.7	1.00499	[1, 2, 0, -1, 0, 0, 2, 2, 1, 0]
po	-2.6	0.91652	[-2, -3, -4, -3, -3, -3, -1, -3, -1, -3]
ptl	2.6	1.11355	[3, 4, 2, 4, 1, 2, 3, 1, 4, 2]
pu	-1.1	1.3	[-3, -1, -3, -2, -1, -1, -1, -1, 1, 1]
qq	-2.2	0.6	[-2, -2, -1, -3, -3, -2, -2, -3, -2, -2]
qt	1.8	0.6	[2, 2, 1, 2, 1, 3, 2, 1, 2, 2]
r&r	2.4	1.0198	[2, 4, 2, 3, 1, 4, 2, 2, 1, 3]
rofl	2.7	0.78102	[3, 2, 2, 2, 4, 4, 2, 3, 3, 2]
roflmao	2.5	1.11803	[4, 2, 2, 4, 1, 1, 2, 4, 3, 2]
rotfl	2.6	0.66332	[3, 2, 3, 3, 1, 3, 3, 3, 2, 3]
rotflmao	2.8	1.07703	[4, 3, 2, 4, 1, 1, 4, 3, 3, 3]
rotflmfao	2.5	1.11803	[3, 4, 1, 3, 3, 3, 0, 3, 2, 3]
rotflol	3.0	1.09545	[1, 4, 4, 4, 2, 2, 2, 3, 4, 4]
rotgl	2.9	0.7	[4, 3, 2, 2, 3, 3, 3, 2, 4, 3]
rotglmao	1.8	2.4	[3, 3, 4, 3, -1, 1, 4, -4, 2, 3]
s:	-1.1	0.83066	[-1, -1, -2, -2, -1, -1, -2, -1, 1, -1]
sapfu	-1.1	1.57797	[-2, 0, -3, -1, -1, 1, -2, 2, -2, -3]
sete	2.8	0.87178	[3, 3, 3, 2, 3, 3, 4, 4, 1, 2]
sfete	2.7	0.78102	[4, 3, 3, 3, 2, 4, 2, 2, 2, 2]
sgtm	2.4	1.0198	[2, 1, 1, 2, 3, 3, 2, 2, 4, 4]
slap	0.6	2.15407	[2, -1, 1, -1, 0, 4, -3, 4, 1, -1]
slaw	2.1	1.04403	[3, 2, 0, 2, 2, 2, 3, 1, 4, 2]
smh	-1.3	0.64031	[-2, -1, 0, -1, -1, -2, -2, -1, -2, -1]
snafu	-2.5	1.11803	[-3, -4, -3, -3, -1, 0, -2, -3, -3, -3]
sob	-2.8	0.9798	[-3, -4, -3, -2, -2, -1, -2, -4, -4, -3]
swak	2.3	1.00499	[2, 2, 2, 1, 4, 2, 3, 2, 1, 4]
tgif	2.3	1.34536	[1, 3, 3, 3, -1, 2, 4, 2, 3, 3]
thks	1.4	0.4899	[1, 2, 1, 2, 1, 2, 1, 1, 2, 1]
thx	1.5	0.92195	[0, 1, 3, 2, 1, 2, 1, 1, 3, 1]
tia	2.3	0.9	[3, 1, 2, 1, 4, 3, 2, 3, 2, 2]
tmi	-0.3	1.61555	[-1, -1, 2, -1, 1, -2, -2, -1, 3, -1]
tnx	1.1	0.53852	[2, 1, 1, 0, 1, 1, 2, 1, 1, 1]
true	1.8	1.32665	[2, 1, 1, 0, 1, 4, 3, 1, 4, 1]
tx	1.5	0.92195	[3, 2, 1, 0, 2, 1, 3, 1, 1, 1]
txs	1.1	0.7	[1, 2, 0, 1, 2, 0, 1, 2, 1, 1]
ty	1.6	0.66332	[1, 2, 3, 1, 2, 2, 1, 2, 1, 1]
tyvm	2.5	1.11803	[2, 2, 1, 3, 1, 4, 2, 4, 2, 4]
urw	1.9	1.13578	[1, 2, 1, 2, 4, 2, 4, 1, 1, 1]
vbg	2.1	1.75784	[2, 3, 3, 3, 3, -3, 3, 2, 2, 3]
vbs	3.1	0.53852	[2, 3, 3, 3, 4, 4, 3, 3, 3, 3]
vip	2.3	1.00499	[2, 1, 1, 3, 4, 2, 2, 4, 2, 2]
vwd	2.6	0.91652	[4, 2, 4, 2, 1, 3, 3, 2, 3, 2]
vwp	2.1	0.7	[3, 1, 2, 2, 3, 2, 2, 3, 1, 2]
wag	-0.2	0.74833	[-1, 0, 0, 0, 0, 0, -2, 1, 0, 0]
wd	2.7	1.1	[3, 1, 4, 3, 4, 2, 1, 3, 2, 4]
wilco	0.9	0.9434	[1, 3, 1, 0, 1, 0, 2, 1, 0, 0]
wp	1.0	0.0	[1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
wtf	-2.8	0.74833	[-4, -3, -2, -3, -2, -2, -2, -4, -3, -3]
wtg	2.1	0.7	[1, 3, 2, 3, 2, 2, 2, 1, 2, 3]
wth	-2.4	0.4899	[-2, -3, -2, -3, -2, -2, -2, -3, -3, -2]
x-d	2.7	0.78102	[1, 3, 4, 2, 3, 3, 3, 2, 3, 3]
x-p	1.8	0.87178	[2, 1, 3, 1, 3, 1, 3, 1, 2, 1]
xd	2.7	0.9	[1, 4, 4, 3, 2, 2, 3, 3, 2, 3]
xlnt	3.0	0.89443	[4, 3, 3, 1, 4, 4, 3, 3, 3, 2]
xoxo	3.0	0.7746	[2, 2, 4, 2, 3, 3, 4, 3, 3, 4]
xoxozzz	2.3	0.78102	[3, 1, 2, 2, 2, 2, 3, 2, 4, 2]
xp	1.2	0.4	[1, 1, 1, 1, 2, 1, 2, 1, 1, 1]
xqzt	1.6	1.42829	[0, 2, 1, 2, 4, -1, 3, 1, 1, 3]
xtc	0.8	1.93907	[2, 0, -3, 3, 3, -1, 3, 1, -1, 1]
yolo	1.1	0.83066	[0, 1, 1, 2, 1, 1, 1, 3, 0, 1]
yoyo	0.4	1.85472	[-1, 0, -1, -1, 4, 2, -2, 2, 2, -1]
yvw	1.6	0.4899	[1, 2, 1, 1, 2, 2, 2, 1, 2, 2]
yw	1.8	1.32665	[1, 1, 1, 4, 1, 1, 4, 0, 3, 2]
ywia	2.5	1.11803	[3, 2, 3, 4, 1, 1, 1, 3, 3, 4]
zzz	-1.2	0.87178	[0, -1, 0, -1, -3, -1, -1, -2, -2, -1]
[-;	0.5	1.28452	[1, -1, -1, 1, 1, 1, 2, -2, 2, 1]
[:	1.3	0.45826	[1, 1, 2, 1, 2, 2, 1, 1, 1, 1]
[;	1.0	1.34164	[2, 1, 2, 2, 1, 2, 2, -2, -1, 1]
[=	1.7	0.64031	[2, 2, 1, 1, 1, 2, 2, 3, 2, 1]
\-:	-1.0	1.18322	[-3, -1, -1, -1, -1, -1, 2, -2, -1, -1]
\:	-1.0	0.0	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
\:<	-1.7	1.18743	[-1, -3, -2, -2, -3, -3, -2, -1, 1, -1]
\=	-1.1	0.3	[-1, -1, -1, -1, -1, -1, -1, -2, -1, -1]
\^:	-1.3	0.45826	[-1, -1, -1, -2, -1, -1, -1, -2, -2, -1]
\o/	2.2	0.9798	[2, 1, 1, 2, 4, 2, 2, 4, 2, 2]
\o:	-1.2	0.4	[-1, -1, -1, -1, -2, -1, -1, -2, -1, -1]
]-:	-2.1	0.53852	[-2, -3, -3, -2, -2, -2, -1, -2, -2, -2]
]:	-1.6	0.66332	[-1, -2, -1, -2, -3, -2, -1, -1, -1, -2]
]:<	-2.5	0.80623	[-2, -2, -2, -3, -4, -2, -2, -2, -2, -4]
^<_<	1.4	1.11355	[3, 1, 3, 2, 1, 1, 1, -1, 2, 1]
^urs	-2.8	0.6	[-2, -3, -3, -2, -3, -3, -2, -3, -4, -3]
abandon	-1.9	0.53852	[-1, -2, -2, -2, -2, -3, -2, -2, -1, -2]
abandoned	-2.0	1.09545	[-1, -1, -3, -2, -1, -4, -1, -3, -3, -1]
abandoner	-1.9	0.83066	[-1, -1, -3, -2, -1, -3, -1, -2, -3, -2]
abandoners	-1.9	0.83066	[-2, -3, -2, -3, -2, -1, -2, -2, 0, -2]
abandoning	-1.6	0.8	[-3, -2, -3, -2, -1, -1, -1, -1, -1, -1]
abandonment	-2.4	1.0198	[-4, -2, -1, -4, -2, -1, -2, -3, -3, -2]
abandonments	-1.7	0.45826	[-2, -1, -2, -2, -1, -2, -1, -2, -2, -2]
abandons	-1.3	0.9	[-2, -1, -1, -2, -1, -2, -1, -2, 1, -2]
abducted	-2.3	1.18743	[-3, -1, 0, -3, -1, -3, -4, -2, -3, -3]
abduction	-2.8	0.87178	[-4, -3, -3, -4, -1, -3, -2, -2, -3, -3]
abductions	-2.0	1.41421	[-3, -4, -1, -3, -1, -3, 1, -2, -1, -3]
abhor	-2.0	1.09545	[-3, -3, -1, -1, -2, -1, -3, -3, 0, -3]
abhorred	-2.4	1.49666	[-4, -4, 0, -3, -2, -1, -4, -3, -3, 0]
abhorrent	-3.1	1.3	[-4, -4, -4, -2, 0, -4, -2, -3, -4, -4]
abhors	-2.9	1.51327	[0, -4, -3, -3, -4, -4, 0, -4, -3, -4]
abilities	1.0	0.63246	[1, 2, 0, 1, 0, 1, 1, 1, 1, 2]
ability	1.3	0.64031	[1, 1, 1, 0, 1, 2, 2, 2, 2, 1]
aboard	0.1	0.3	[0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
absentee	-1.1	0.53852	[-1, -1, 0, -2, -1, -1, -2, -1, -1, -1]
absentees	-0.8	0.6	[-1, 0, 0, -1, -1, 0, -2, -1, -1, -1]
absolve	1.2	1.46969	[2, -3, 2, 2, 1, 1, 2, 1, 2, 2]
absolved	1.5	0.92195	[3, 1, 2, 1, 0, 2, 3, 1, 1, 1]
absolves	1.3	1.00499	[3, 1, 1, 0, 0, 2, 3, 1, 1, 1]
absolving	1.6	0.8	[3, 1, 2, 1, 1, 2, 3, 1, 1, 1]
abuse	-3.2	0.6	[-4, -2, -3, -4, -3, -4, -3, -3, -3, -3]
abused	-2.3	0.64031	[-2, -2, -3, -2, -2, -4, -2, -2, -2, -2]
abuser	-2.6	0.4899	[-3, -2, -3, -3, -2, -3, -2, -2, -3, -3]
abusers	-2.6	1.0198	[-2, -3, -3, -3, -3, -2, -3, -4, -3, 0]
abuses	-2.6	0.66332	[-3, -2, -3, -3, -3, -3, -1, -2, -3, -3]
abusing	-2.0	1.41421	[-1, -2, -2, -4, -4, -2, -3, -1, 1, -2]
abusive	-3.2	0.74833	[-4, -3, -3, -4, -4, -3, -4, -2, -3, -2]
abusively	-2.8	0.6	[-3, -4, -3, -2, -3, -2, -2, -3, -3, -3]
abusiveness	-2.5	0.92195	[-2, -4, -2, -3, -2, -3, -4, -2, -1, -2]
abusivenesses	-3.0	0.63246	[-3, -3, -4, -3, -4, -2, -2, -3, -3, -3]
accept	1.6	0.91652	[2, 1, 2, 1, 1, 2, 4, 1, 1, 1]
acceptabilities	1.6	0.66332	[0, 2, 2, 2, 1, 2, 2, 2, 1, 2]
acceptability	1.1	0.53852	[1, 0, 1, 2, 1, 2, 1, 1, 1, 1]
acceptable	1.3	0.45826	[1, 2, 1, 1, 1, 2, 1, 1, 2, 1]
acceptableness	1.3	0.9	[1, 0, 2, 1, 2, 1, 1, 0, 2, 3]
acceptably	1.5	0.67082	[3, 2, 1, 1, 1, 2, 1, 1, 2, 1]
acceptance	2.0	0.63246	[3, 1, 3, 2, 1, 2, 2, 2, 2, 2]
acceptances	1.7	0.78102	[3, 1, 1, 1, 2, 2, 1, 2, 3, 1]
acceptant	1.6	0.8	[0, 1, 2, 2, 2, 1, 2, 1, 3, 2]
acceptation	1.3	0.78102	[0, 1, 2, 1, 1, 1, 1, 3, 2, 1]
acceptations	0.9	0.83066	[1, 2, 0, 2, 0, 1, 0, 2, 1, 0]
accepted	1.1	0.3	[1, 1, 1, 1, 1, 2, 1, 1, 1, 1]
accepting	1.6	0.66332	[2, 2, 2, 1, 1, 2, 1, 3, 1, 1]
accepts	1.3	0.45826	[1, 2, 1, 1, 1, 2, 2, 1, 1, 1]
accident	-2.1	0.83066	[-2, -2, -1, -3, -4, -2, -2, -1, -2, -2]
accidental	-0.3	0.45826	[-1, -1, 0, 0, 0, 0, 0, 0, -1, 0]
accidentally	-1.4	0.91652	[-2, 0, -2, 0, -3, -1, -1, -1, -2, -2]
accidents	-1.3	0.78102	[-1, -1, -1, -1, -2, 0, -3, -1, -2, -1]
accomplish	1.8	0.6	[1, 2, 3, 2, 2, 2, 1, 1, 2, 2]
accomplished	1.9	0.53852	[2, 2, 2, 1, 2, 2, 3, 1, 2, 2]
accomplishes	1.7	0.9	[2, 2, 1, 0, 2, 3, 3, 1, 1, 2]
accusation	-1.0	1.09545	[-1, -1, -2, -2, -2, -1, -1, -1, 2, -1]
accusations	-1.3	1.26886	[-2, -2, -1, -3, -2, -1, -1, 2, -2, -1]
accuse	-0.8	1.53623	[-3, -1, -1, -2, 1, -2, 1, -2, 2, -1]
accused	-1.2	1.46969	[-2, -1, -2, 2, -2, -3, -2, -2, -1, 1]
accuses	-1.4	1.0198	[-2, -1, -2, 1, -2, -3, -1, -2, -1, -1]
accusing	-0.7	1.34536	[-2, -1, -1, 1, -3, -1, -1, 2, -1, 0]
ache	-1.6	1.2	[-1, -2, -2, -2, -1, -4, -1, 1, -2, -2]
ached	-1.6	0.8	[-2, -2, -1, -2, -1, -2, -3, 0, -1, -2]
aches	-1.0	0.7746	[-1, -2, -1, -1, -1, 1, -2, -1, -1, -1]
achievable	1.3	0.45826	[2, 1, 1, 1, 1, 1, 1, 2, 2, 1]
aching	-2.2	0.74833	[-2, -3, -2, -1, -3, -3, -2, -3, -1, -2]
acquit	0.8	1.72047	[-3, 3, -1, 3, 2, 1, 1, 1, 0, 1]
acquits	0.1	1.37477	[1, -3, -1, 0, 2, 0, -1, 1, 1, 1]
acquitted	1.0	0.89443	[2, 2, 1, 1, 2, 0, 1, 1, -1, 1]
acquitting	1.3	0.78102	[3, 2, 0, 1, 1, 1, 2, 1, 1, 1]
acrimonious	-1.7	1.73494	[-1, -3, -2, -3, 3, -3, -1, -2, -2, -3]
active	1.7	1.26886	[1, 2, 1, 1, 1, 4, 2, 4, 0, 1]
actively	1.3	0.78102	[0, 1, 0, 2, 2, 1, 1, 2, 2, 2]
activeness	0.6	0.8	[0, 2, 0, 0, 1, 0, 1, 0, 2, 0]
activenesses	0.8	0.74833	[2, 0, 1, 0, 0, 0, 1, 2, 1, 1]
actives	1.1	0.7	[2, 1, 0, 1, 1, 0, 1, 1, 2, 2]
adequate	0.9	0.7	[0, 0, 1, 1, 0, 2, 1, 1, 2, 1]
admirability	2.4	0.4899	[2, 3, 3, 3, 3, 2, 2, 2, 2, 2]
admirable	2.6	0.66332	[2, 3, 3, 3, 4, 3, 2, 2, 2, 2]
admirableness	2.2	0.87178	[2, 2, 3, 3, 3, 1, 3, 1, 3, 1]
admirably	2.5	0.67082	[2, 3, 3, 3, 4, 2, 2, 2, 2, 2]
admiral	1.3	1.18743	[0, 0, 1, 3, 3, 2, 2, 0, 2, 0]
admirals	1.5	0.80623	[2, 2, 0, 2, 2, 0, 1, 2, 2, 2]
admiralties	1.6	0.66332	[2, 2, 2, 1, 0, 2, 2, 2, 1, 2]
admiralty	1.2	1.53623	[0, 4, 0, 0, 0, 2, 2, 3, 2, -1]
admiration	2.5	0.80623	[3, 1, 1, 3, 3, 2, 3, 3, 3, 3]
admirations	1.6	0.66332	[2, 2, 1, 1, 2, 2, 2, 2, 2, 0]
admire	2.1	0.83066	[3, 3, 1, 3, 3, 2, 1, 2, 1, 2]
admired	2.3	0.78102	[4, 2, 2, 2, 2, 2, 3, 3, 1, 2]
admirer	1.8	0.74833	[2, 1, 1, 2, 3, 2, 3, 1, 1, 2]
admirers	1.7	1.00499	[2, 3, 2, 2, 2, 1, -1, 2, 2, 2]
admires	1.5	0.67082	[3, 1, 1, 2, 1, 2, 2, 1, 1, 1]
admiring	1.6	0.8	[1, 2, 1, 1, 3, 3, 2, 1, 1, 1]
admiringly	2.3	0.64031	[1, 3, 3, 2, 2, 2, 2, 3, 3, 2]
admit	0.8	1.07703	[0, 0, 0, 0, 0, 1, 3, 2, 2, 0]
admits	1.2	0.87178	[1, 2, 2, 2, 0, 0, 1, 2, 0, 2]
admitted	0.4	0.66332	[0, 1, 0, 1, 0, 0, 2, 0, 0, 0]
admonished	-1.9	0.9434	[-2, -2, -2, -1, -2, -3, -1, -1, -1, -4]
adopt	0.7	0.64031	[0, 0, 1, 1, 1, 0, 1, 0, 1, 2]
adopts	0.7	0.64031	[0, 0, 1, 2, 1, 0, 1, 1, 0, 1]
adorability	2.2	0.74833	[2, 2, 2, 2, 1, 2, 3, 2, 4, 2]
adorable	2.2	0.6	[3, 2, 2, 3, 2, 2, 1, 3, 2, 2]
adorableness	2.5	0.67082	[2, 3, 3, 2, 3, 2, 1, 3, 3, 3]
adorably	2.1	0.7	[3, 1, 2, 3, 2, 2, 1, 3, 2, 2]
adoration	2.9	0.7	[3, 3, 3, 2, 3, 3, 4, 2, 4, 2]
adorations	2.2	0.87178	[2, 2, 3, 1, 3, 1, 3, 3, 1, 3]
adore	2.6	0.91652	[3, 3, 1, 2, 3, 3, 3, 4, 1, 3]
adored	1.8	0.87178	[2, 3, 3, 2, 2, 1, 1, 0, 2, 2]
adorer	1.7	1.1	[2, 4, 3, 1, 2, 1, 1, 0, 2, 1]
adorers	2.1	0.7	[3, 2, 1, 2, 2, 2, 3, 2, 3, 1]
adores	1.6	0.66332	[2, 1, 3, 2, 2, 1, 1, 1, 2, 1]
adoring	2.6	0.66332	[2, 3, 3, 3, 1, 3, 3, 2, 3, 3]
adoringly	2.4	0.8	[2, 3, 2, 3, 3, 3, 3, 1, 1, 3]
adorn	0.9	0.53852	[1, 1, 1, 0, 2, 1, 1, 0, 1, 1]
adorned	0.8	1.249	[1, 1, 0, 2, -1, 3, -1, 2, 1, 0]
adorner	1.3	0.78102	[1, 1, 1, 2, 1, 3, 1, 2, 1, 0]
adorners	0.9	0.9434	[2, 2, 0, 1, -1, 2, 1, 1, 0, 1]
adorning	1.0	0.7746	[0, 0, 1, 1, 1, 2, 2, 1, 0, 2]
adornment	1.3	0.78102	[1, 3, 1, 0, 2, 2, 1, 1, 1, 1]
adornments	0.8	1.16619	[2, -1, 0, 0, 2, 1, 2, -1, 1, 2]
adorns	0.5	1.56525	[3, -1, 1, 0, 2, -1, 3, -1, 0, -1]
advanced	1.0	0.63246	[1, 0, 1, 1, 1, 0, 1, 2, 1, 2]
advantage	1.0	0.63246	[1, 2, 1, 1, 2, 0, 1, 0, 1, 1]
advantaged	1.4	0.91652	[1, 0, 3, 0, 1, 1, 2, 2, 2, 2]
advantageous	1.5	0.67082	[2, 0, 2, 2, 2, 1, 1, 1, 2, 2]
advantageously	1.9	0.53852	[2, 2, 2, 3, 2, 2, 2, 1, 1, 2]
advantageousness	1.6	1.28062	[-2, 2, 3, 1, 2, 2, 2, 2, 2, 2]
advantages	1.5	0.80623	[1, 0, 3, 1, 1, 1, 2, 2, 2, 2]
advantaging	1.6	0.66332	[3, 1, 1, 2, 1, 1, 2, 2, 2, 1]
adventure	1.3	0.45826	[1, 2, 1, 1, 2, 1, 1, 1, 1, 2]
adventured	1.3	0.45826	[1, 2, 1, 2, 1, 2, 1, 1, 1, 1]
adventurer	1.2	0.6	[1, 2, 0, 2, 1, 2, 1, 1, 1, 1]
adventurers	0.9	0.9434	[0, 1, 0, 1, 0, 1, 0, 1, 3, 2]
adventures	1.4	1.2	[2, 2, 1, 2, -2, 2, 2, 1, 2, 2]
adventuresome	1.7	1.1	[0, 3, 0, 1, 2, 2, 3, 1, 2, 3]
adventuresomeness	1.3	1.00499	[1, 0, 0, 2, 3, 2, 2, 0, 1, 2]
adventuress	0.8	1.72047	[3, -1, 2, 2, 0, 0, 1, 2, -3, 2]
adventuresses	1.4	1.11355	[1, 0, 0, 3, 2, 2, 3, 0, 1, 2]
adventuring	2.3	0.78102	[2, 3, 2, 3, 1, 3, 3, 1, 2, 3]
adventurism	1.5	0.67082	[1, 0, 2, 2, 2, 2, 1, 1, 2, 2]
adventurist	1.4	0.4899	[1, 1, 2, 1, 2, 2, 2, 1, 1, 1]
adventuristic	1.7	0.64031	[2, 1, 1, 2, 2, 1, 3, 2, 2, 1]
adventurists	1.2	0.9798	[3, 1, 0, 0, 1, 2, 1, 0, 2, 2]
adventurous	1.4	1.11355	[0, 1, 2, 1, 2, 0, 3, 2, 3, 0]
adventurously	1.3	0.9	[0, 1, 2, 2, 1, 2, 1, 1, 0, 3]
adventurousness	1.8	0.87178	[0, 1, 3, 2, 1, 3, 2, 2, 2, 2]
adversarial	-1.5	0.92195	[-2, 0, -1, -3, -2, -2, 0, -1, -2, -2]
adversaries	-1.0	0.63246	[-1, -1, -1, -1, 0, -2, -2, -1, -1, 0]
adversary	-0.8	1.72047	[-3, -1, -2, -2, -2, 2, 1, -1, 2, -2]
adversative	-1.2	0.74833	[-1, -1, -2, -1, -1, -1, -3, -1, 0, -1]
adversatively	-0.1	1.37477	[0, -2, -1, 0, -1, -1, 1, 1, 3, -1]
adversatives	-1.0	0.7746	[-1, -1, -2, -1, -2, 1, -1, -1, -1, -1]
adverse	-1.5	0.80623	[-2, -2, -2, -1, -1, 0, -1, -3, -1, -2]
adversely	-0.8	1.6	[-2, -2, 0, -2, -2, 2, -2, 2, -2, 0]
adverseness	-0.6	1.35647	[-1, -2, -1, 2, -1, -2, -1, 2, -1, -1]
adversities	-1.5	0.67082	[-2, -2, -1, -1, -2, 0, -2, -1, -2, -2]
adversity	-1.8	0.6	[-3, -2, -1, -2, -2, -2, -1, -1, -2, -2]
affected	-0.6	1.35647	[-1, -2, 0, -2, 0, 0, 2, -2, 1, -2]
affection	2.4	0.8	[3, 2, 2, 3, 4, 1, 3, 2, 2, 2]
affectional	1.9	1.04403	[3, 3, 2, 0, 2, 2, 2, 3, 2, 0]
affectionally	1.5	0.92195	[1, 1, 3, 1, 1, 0, 2, 2, 3, 1]
affectionate	1.9	1.13578	[1, 0, 3, 1, 3, 2, 2, 1, 2, 4]
affectionately	2.2	0.87178	[4, 1, 1, 2, 2, 2, 3, 3, 2, 2]
affectioned	1.8	0.4	[2, 2, 1, 2, 2, 1, 2, 2, 2, 2]
affectionless	-2.0	0.44721	[-2, -1, -2, -2, -2, -2, -2, -2, -2, -3]
affections	1.5	1.11803	[-1, 3, 2, 2, 1, 1, 2, 1, 3, 1]
afflicted	-1.5	1.0247	[-1, -2, -3, -2, 1, -1, -1, -2, -2, -2]
affronted	0.2	2.03961	[1, -2, 2, -2, -2, 4, 0, 2, 1, -2]
aggravate	-2.5	0.80623	[-3, -3, -2, -3, -2, -4, -3, -2, -2, -1]
aggravated	-1.9	1.04403	[-4, -3, -3, -1, -1, -1, -1, -2, -2, -1]
aggravates	-1.9	0.83066	[-3, -2, -2, -2, -3, -1, -2, -2, 0, -2]
aggravating	-1.2	0.9798	[-2, -2, -1, -2, -2, -1, 0, 1, -1, -2]
aggress	-1.3	1.55242	[-2, -2, 2, -2, -1, -3, -3, -1, 1, -2]
aggressed	-1.4	0.4899	[-1, -1, -1, -1, -2, -1, -2, -2, -2, -1]
aggresses	-0.5	1.43178	[-1, -2, -1, -1, -3, 0, -1, 1, 2, 1]
aggressing	-0.6	1.28062	[-1, -2, -1, 0, -1, -2, 2, -2, 1, 0]
aggression	-1.2	1.77764	[-2, -2, 1, 1, -4, 2, -2, -2, -2, -2]
aggressions	-1.3	1.48661	[-1, -2, -2, -2, -2, -3, 1, -2, 2, -2]
aggressive	-0.6	1.28062	[-2, 1, -2, -2, 0, -2, -1, 1, 1, 0]
aggressively	-1.3	1.55242	[-1, -2, 3, -2, -3, -2, -1, -1, -2, -2]
aggressiveness	-1.8	0.74833	[-1, -2, -1, -2, -2, -1, -3, -1, -3, -2]
aggressivities	-1.4	1.28062	[-1, -1, -1, -2, 2, -2, -2, -2, -3, -2]
aggressivity	-0.6	1.35647	[-3, -1, 1, 0, 0, 0, -3, -1, 1, 0]
aggressor	-0.8	1.32665	[-2, 0, -1, 2, -2, -2, -1, -1, -2, 1]
aggressors	-0.9	1.13578	[-2, -2, -1, -1, -1, 1, 1, 0, -2, -2]
aghast	-1.9	1.04403	[-2, -3, -1, 0, -2, -1, -4, -2, -2, -2]
agitate	-1.7	0.64031	[-2, -2, -3, -1, -1, -1, -2, -1, -2, -2]
agitated	-2.0	0.63246	[-2, -2, -2, -2, -1, -3, -3, -2, -2, -1]
agitatedly	-1.6	0.8	[-1, -2, -1, -3, -1, -3, -1, -1, -2, -1]
agitates	-1.4	0.8	[-2, 0, -1, -2, -1, -1, -3, -1, -2, -1]
agitating	-1.8	0.87178	[-2, -1, -1, -1, -2, -3, -1, -3, -3, -1]
agitation	-1.0	1.09545	[-2, -1, 1, -1, -2, -1, -2, -1, 1, -2]
agitational	-1.2	1.66132	[-3, -3, -2, 1, -1, -2, 0, 2, -1, -3]
agitations	-1.3	1.18743	[-1, -2, -1, -3, -2, -3, 0, -1, 1, -1]
agitative	-1.3	1.26886	[-2, -2, -1, -2, -1, -3, 1, -2, 1, -2]
agitato	-0.1	1.13578	[1, 2, 0, 0, 0, 0, -2, 0, -2, 0]
agitator	-1.4	0.8	[-1, -1, -1, -2, -1, -1, -2, -3, -2, 0]
agitators	-2.1	0.9434	[-2, -3, -3, -2, -2, -1, -3, -2, 0, -3]
agog	1.9	0.7	[2, 1, 3, 3, 2, 1, 2, 2, 1, 2]
agonise	-2.1	0.9434	[-3, -3, -2, -3, -1, -3, -1, -3, -1, -1]
agonised	-2.3	0.64031	[-2, -3, -3, -2, -2, -2, -2, -3, -1, -3]
agonises	-2.4	0.91652	[-1, -4, -3, -3, -2, -2, -2, -3, -1, -3]
agonising	-1.5	1.43178	[-3, -2, -3, -3, -1, 1, 0, 0, -1, -3]
agonize	-2.3	0.9	[-2, -3, -1, -2, -2, -2, -4, -1, -3, -3]
agonized	-2.2	1.249	[-2, -3, -3, -3, -3, -2, -1, -3, 1, -3]
agonizes	-2.3	1.18743	[-1, -3, -4, -3, -3, -2, -1, -3, 0, -3]
agonizing	-2.7	0.78102	[-3, -2, -2, -2, -4, -3, -3, -2, -4, -2]
agonizingly	-2.3	1.48661	[-3, -1, -3, -4, -4, -2, -3, 1, -1, -3]
agony	-1.8	1.16619	[-3, -1, -2, -4, -2, -1, 0, -3, -1, -1]
agree	1.5	1.11803	[1, 0, 3, 1, 2, 1, 4, 1, 1, 1]
agreeability	1.9	1.04403	[1, 1, 1, 3, 3, 2, 1, 2, 4, 1]
agreeable	1.8	0.4	[2, 1, 2, 1, 2, 2, 2, 2, 2, 2]
agreeableness	1.8	0.9798	[2, 3, 2, 1, 1, 1, 4, 1, 2, 1]
agreeablenesses	1.3	0.45826	[2, 1, 1, 2, 1, 2, 1, 1, 1, 1]
agreeably	1.6	0.4899	[1, 1, 2, 1, 1, 2, 2, 2, 2, 2]
agreed	1.1	0.53852	[1, 1, 2, 1, 1, 2, 1, 0, 1, 1]
agreeing	1.4	0.4899	[1, 1, 1, 1, 2, 1, 1, 2, 2, 2]
agreement	2.2	0.74833	[2, 1, 1, 3, 3, 3, 2, 2, 2, 3]
agreements	1.1	1.13578	[0, 1, 0, 1, 1, 2, 1, 0, 4, 1]
agrees	0.8	1.4	[1, 1, 1, 1, 3, 1, -3, 1, 1, 1]
alarm	-1.4	0.91652	[-1, -1, -2, -2, -2, -2, -1, 1, -2, -2]
alarmed	-1.4	0.4899	[-2, -1, -2, -2, -1, -1, -1, -2, -1, -1]
alarming	-0.5	1.62788	[-1, 1, -1, 1, -3, 1, -2, -3, 1, 1]
alarmingly	-2.6	0.91652	[-3, -3, -4, -3, -2, -1, -3, -3, -1, -3]
alarmism	-0.3	1.26886	[-2, 0, -2, -1, 0, 1, 1, -1, 2, -1]
alarmists	-1.1	1.3	[-1, -2, -3, -2, -1, -2, 1, 1, 0, -2]
alarms	-1.1	1.04403	[-2, 0, -2, -1, 0, 0, 0, -1, -2, -3]
alas	-1.1	1.22066	[-1, -2, -1, 0, -1, 0, 1, -3, -1, -3]
alert	1.2	0.87178	[1, 2, 0, 1, 0, 2, 2, 0, 2, 2]
alienation	-1.1	1.51327	[-3, -2, -3, -1, -2, -1, 2, -1, 1, -1]
alive	1.6	0.8	[1, 1, 2, 3, 3, 2, 1, 1, 1, 1]
allergic	-1.2	0.4	[-1, -2, -1, -2, -1, -1, -1, -1, -1, -1]
allow	0.9	0.83066	[0, 0, 0, 1, 2, 2, 1, 1, 2, 0]
alone	-1.0	0.63246	[-2, -1, -1, -1, -2, -1, 0, -1, -1, 0]
alright	1.0	0.7746	[0, 1, 0, 3, 1, 1, 1, 1, 1, 1]
amaze	2.5	1.0247	[3, 2, 3, 4, 2, 3, 1, 4, 2, 1]
amazed	2.2	1.07703	[1, 0, 3, 2, 2, 4, 3, 3, 2, 2]
amazedly	2.1	0.53852	[2, 2, 2, 2, 3, 2, 2, 3, 1, 2]
amazement	2.5	0.80623	[3, 3, 2, 4, 1, 2, 2, 3, 2, 3]
amazements	2.2	0.87178	[3, 1, 1, 3, 3, 2, 1, 2, 3, 3]
amazes	2.2	0.9798	[1, 0, 3, 2, 2, 3, 3, 3, 2, 3]
amazing	2.8	0.87178	[1, 4, 3, 2, 4, 2, 3, 3, 3, 3]
amazon	0.7	0.64031	[0, 1, 1, 0, 1, 0, 1, 0, 1, 2]
amazonite	0.2	0.6	[0, 0, 2, 0, 0, 0, 0, 0, 0, 0]
amazons	-0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, -1, 0]
amazonstone	1.0	1.61245	[0, 0, 0, 0, 4, 4, 2, 0, 0, 0]
amazonstones	0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, 2, 0]
ambitious	2.1	0.53852	[2, 3, 2, 2, 2, 2, 2, 2, 3, 1]
ambivalent	0.5	0.92195	[0, 0, -1, 2, 1, 1, 1, -1, 1, 1]
amor	3.0	0.63246	[3, 3, 2, 4, 3, 2, 4, 3, 3, 3]
amoral	-1.6	0.66332	[-1, -2, 0, -2, -2, -2, -2, -2, -1, -2]
amoralism	-0.7	1.34536	[-2, -1, -2, 0, 0, -3, 2, 0, -1, 0]
amoralisms	-0.7	1.00499	[-2, 0, 1, -1, -2, -1, -1, 1, -1, -1]
amoralities	-1.2	1.6	[-3, 0, 0, -2, 1, -1, 0, -4, -3, 0]
amorality	-1.5	0.92195	[0, -1, -1, -2, -1, -3, -1, -3, -2, -1]
amorally	-1.0	1.61245	[-2, 0, 1, -4, -1, 1, 1, -2, -2, -2]
amoretti	0.2	0.4	[0, 0, 0, 1, 0, 0, 0, 0, 0, 1]
amoretto	0.6	0.8	[0, 1, 0, 1, 0, 0, 2, 0, 0, 2]
amorettos	0.3	0.64031	[0, 1, 0, 1, 0, 0, 0, 1, 1, -1]
amorino	1.2	0.87178	[2, 1, 1, 0, 3, 1, 1, 0, 1, 2]
amorist	1.6	1.0198	[3, 0, 0, 2, 2, 1, 3, 1, 2, 2]
amoristic	1.0	1.67332	[1, 0, 2, 3, -1, 3, 1, 3, 0, -2]
amorists	0.1	0.9434	[0, 2, 0, 0, 1, 0, -2, 0, 0, 0]
amoroso	2.3	0.78102	[3, 1, 1, 2, 3, 3, 3, 3, 2, 2]
amorous	1.8	0.9798	[3, 1, 2, 2, 1, 1, 3, 0, 2, 3]
amorously	2.3	0.78102	[1, 1, 3, 2, 3, 3, 2, 3, 2, 3]
amorousness	2.0	0.89443	[2, 3, 1, 2, 0, 2, 2, 3, 3, 2]
amorphous	-0.2	0.4	[0, 0, 0, 0, 0, 0, -1, 0, 0, -1]
amorphously	0.1	0.7	[-1, 0, 0, 0, 0, 0, 2, 0, 0, 0]
amorphousness	0.3	0.45826	[0, 0, 0, 0, 0, 1, 1, 1, 0, 0]
amort	-2.1	0.83066	[-3, -1, -2, -2, -2, -2, -2, -4, -1, -2]
amortise	0.5	1.43178	[0, 1, 1, 0, -3, 2, 0, 0, 2, 2]
amortised	-0.2	1.16619	[-1, -3, 0, 0, 0, 2, 0, 0, 0, 0]
amortises	0.1	0.83066	[-1, -1, 0, 0, 0, 2, 1, 0, 0, 0]
amortizable	0.5	1.0247	[2, 0, 1, 0, 1, 1, -2, 1, 0, 1]
amortization	0.6	1.0198	[0, 0, 0, 0, 1, 0, 0, 0, 3, 2]
amortizations	0.2	1.07703	[-1, 1, 0, 0, 1, 0, 2, 1, -2, 0]
amortize	-0.1	1.04403	[0, 0, 0, 0, 2, -2, -1, 1, -1, 0]
amortized	0.8	0.74833	[0, 2, 0, 0, 1, 1, 1, 0, 1, 2]
amortizes	0.6	0.8	[0, 2, 0, 0, 1, 1, 0, 0, 0, 2]
amortizing	0.8	1.249	[0, 3, 0, 0, 0, 0, 0, 0, 3, 2]
amusable	0.7	1.18743	[2, 1, 1, 1, 1, 1, 2, -2, -1, 1]
amuse	1.7	0.78102	[1, 2, 1, 1, 2, 1, 1, 3, 3, 2]
amused	1.8	0.6	[1, 2, 2, 2, 2, 2, 1, 2, 3, 1]
amusedly	2.2	0.74833	[3, 3, 2, 2, 3, 2, 2, 3, 1, 1]
amusement	1.5	1.11803	[3, 2, 3, 1, 2, 2, -1, 1, 1, 1]
amusements	1.5	1.0247	[2, 1, 2, 1, 2, 2, 3, -1, 2, 1]
amuser	1.1	1.7	[2, 1, -3, 2, 2, 3, -1, 1, 2, 2]
amusers	1.3	0.45826	[1, 1, 2, 1, 2, 1, 2, 1, 1, 1]
amuses	1.7	0.64031	[1, 2, 1, 2, 2, 2, 1, 2, 3, 1]
amusia	0.3	1.48661	[0, -1, 1, -1, 2, 2, -1, -1, -1, 3]
amusias	-0.4	0.66332	[-1, 0, 0, 1, 0, 0, -1, -1, -1, -1]
amusing	1.6	0.91652	[2, 2, 2, -1, 2, 2, 1, 2, 2, 2]
amusingly	0.8	1.249	[1, 2, 1, 1, 1, 2, 2, -2, -1, 1]
amusingness	1.8	0.6	[1, 2, 3, 2, 1, 2, 2, 1, 2, 2]
amusive	1.7	1.1	[3, 1, 3, 1, 2, 2, 2, 2, -1, 2]
anger	-2.7	1.18743	[-1, -2, -3, -2, -4, -4, -2, -1, -4, -4]
angered	-2.3	0.78102	[-2, -3, -2, -4, -2, -2, -3, -2, -2, -1]
angering	-2.2	0.6	[-3, -2, -1, -3, -2, -2, -3, -2, -2, -2]
angerly	-1.9	0.53852	[-2, -2, -1, -1, -3, -2, -2, -2, -2, -2]
angers	-2.3	0.9	[-3, -1, -2, -3, -2, -2, -4, -1, -3, -2]
angrier	-2.3	0.64031	[-2, -3, -2, -3, -1, -3, -2, -3, -2, -2]
angriest	-3.1	0.83066	[-4, -3, -2, -2, -2, -4, -3, -4, -4, -3]
angrily	-1.8	0.4	[-2, -1, -2, -2, -2, -1, -2, -2, -2, -2]
angriness	-1.7	0.64031	[-2, 0, -2, -2, -1, -2, -2, -2, -2, -2]
angry	-2.3	0.9	[-2, -2, -1, -3, -1, -2, -4, -2, -3, -3]
anguish	-2.9	0.83066	[-3, -3, -2, -3, -4, -1, -3, -3, -4, -3]
anguished	-1.8	1.4	[-3, -4, -1, -3, -2, -1, -1, 1, -1, -3]
anguishes	-2.1	1.44568	[-4, -4, -2, -3, 1, -2, -1, -1, -2, -3]
anguishing	-2.7	0.9	[-2, -2, -1, -3, -2, -4, -4, -3, -3, -3]
animosity	-1.9	1.75784	[-2, -3, -3, -3, -2, 2, 1, -3, -3, -3]
annoy	-1.9	0.53852	[-2, -2, -1, -2, -2, -1, -2, -2, -2, -3]
annoyance	-1.3	1.55242	[-2, -3, -2, -2, -1, 1, -3, -2, 2, -1]
annoyances	-1.8	0.6	[-2, -2, -2, -1, -1, -2, -3, -1, -2, -2]
annoyed	-1.6	1.11355	[-3, -1, 1, -3, -1, -1, -2, -2, -2, -2]
annoyer	-2.2	0.87178	[-3, -2, -1, -3, -2, -4, -2, -1, -2, -2]
annoyers	-1.5	1.0247	[-2, -1, -2, -3, -2, -1, -1, -2, 1, -2]
annoying	-1.7	0.64031	[-1, -2, -1, -2, -1, -1, -2, -2, -3, -2]
annoys	-1.8	0.6	[-1, -2, -3, -2, -2, -2, -1, -2, -1, -2]
antagonism	-1.9	1.04403	[-1, -1, -3, -2, -4, -2, -2, 0, -2, -2]
antagonisms	-1.2	1.53623	[0, -2, -2, -2, -2, -2, 3, -2, -1, -2]
antagonist	-1.9	0.7	[-3, -1, -2, -3, -2, -2, -1, -2, -2, -1]
antagonistic	-1.7	0.9	[-2, -2, -2, 1, -2, -2, -2, -2, -2, -2]
antagonistically	-2.2	0.87178	[-2, -3, -4, -2, -2, -3, -2, -2, -1, -1]
antagonists	-1.7	0.64031	[-2, -1, -1, -2, -1, -2, -2, -3, -1, -2]
antagonize	-2.0	0.44721	[-2, -2, -2, -3, -2, -1, -2, -2, -2, -2]
antagonized	-1.4	0.66332	[-2, -1, -2, -2, -1, 0, -2, -1, -1, -2]
antagonizes	-0.5	1.9105	[-2, 4, -2, -2, 1, 0, -2, 1, -1, -2]
antagonizing	-2.7	0.64031	[-4, -2, -2, -3, -2, -3, -3, -2, -3, -3]
anti	-1.3	0.78102	[0, -2, -3, -1, -1, -2, -1, -1, -1, -1]
anticipation	0.4	1.28062	[1, 1, -1, 0, -1, 1, 1, 2, -2, 2]
anxieties	-0.6	1.85472	[-2, -3, -3, -2, -1, 2, -1, 1, 1, 2]
anxiety	-0.7	2.1	[-2, -2, -2, -3, 3, -1, -3, 2, 2, -1]
anxious	-1.0	0.44721	[-1, -2, -1, -1, 0, -1, -1, -1, -1, -1]
anxiously	-0.9	0.83066	[-1, -1, -2, -1, -1, -1, -1, 0, 1, -2]
anxiousness	-1.0	1.48324	[-2, -1, -1, -1, -1, -2, 3, -3, -1, -1]
aok	2.0	0.89443	[2, 3, 2, 1, 2, 1, 1, 4, 2, 2]
apathetic	-1.2	0.87178	[-1, -1, 0, -2, -2, -1, -1, 0, -3, -1]
apathetically	-0.4	1.28062	[-1, -1, 0, -1, -2, 2, -1, -1, 2, -1]
apathies	-0.6	1.0198	[-1, -1, -1, -2, 0, 1, -1, -2, 1, 0]
apathy	-1.2	1.32665	[-2, -2, -1, 1, -1, -3, -1, 1, -1, -3]
apeshit	-0.9	2.21133	[-4, -3, 2, -3, -2, -3, 1, 0, 2, 1]
apocalyptic	-3.4	0.66332	[-4, -2, -3, -4, -3, -4, -4, -3, -3, -4]
apologise	1.6	0.66332	[2, 3, 2, 2, 1, 1, 2, 1, 1, 1]
apologised	0.4	0.91652	[-1, 0, 2, 0, 0, 1, 0, 2, 0, 0]
apologises	0.8	1.07703	[2, 0, 2, 0, 0, 1, 0, 3, 0, 0]
apologising	0.2	1.6	[0, -1, -1, 2, 2, 1, -2, -1, 3, -1]
apologize	0.4	0.8	[1, -1, 0, 1, 1, 1, -1, 1, 0, 1]
apologized	1.3	0.64031	[1, 1, 1, 2, 0, 2, 1, 2, 2, 1]
apologizes	1.5	0.80623	[2, 1, 1, 2, 0, 2, 1, 2, 3, 1]
apologizing	-0.3	1.34536	[1, 2, -1, 1, -1, 0, -3, 0, -1, -1]
apology	0.2	1.249	[1, 1, 1, 1, -1, 1, -1, -1, -2, 2]
appall	-2.4	0.66332	[-3, -2, -2, -3, -2, -3, -1, -3, -2, -3]
appalled	-2.0	0.63246	[-3, -2, -3, -2, -2, -1, -1, -2, -2, -2]
appalling	-1.5	1.5	[-2, -4, 1, -1, 1, -1, -2, -3, -2, -2]
appallingly	-2.0	1.67332	[-3, -2, 0, -2, 2, -3, -4, -3, -2, -3]
appalls	-1.9	1.37477	[0, -3, -2, -3, -3, -2, -1, -3, 1, -3]
appease	1.1	0.9434	[1, 1, 1, -1, 2, 0, 1, 2, 2, 2]
appeased	0.9	0.53852	[0, 1, 1, 1, 1, 1, 0, 1, 2, 1]
appeases	0.9	0.53852	[0, 1, 1, 1, 1, 1, 0, 1, 2, 1]
appeasing	1.0	1.09545	[1, 2, -1, 1, 2, 1, 2, 2, 1, -1]
applaud	2.0	0.63246	[3, 2, 2, 2, 1, 2, 3, 1, 2, 2]
applauded	1.5	0.5	[2, 2, 1, 2, 2, 2, 1, 1, 1, 1]
applauding	2.1	0.83066	[2, 2, 4, 1, 2, 2, 2, 1, 2, 3]
applauds	1.4	0.66332	[1, 1, 2, 1, 3, 1, 1, 1, 2, 1]
applause	1.8	0.6	[2, 1, 1, 2, 3, 2, 1, 2, 2, 2]
appreciate	1.7	0.78102	[2, 1, 2, 1, 1, 3, 1, 2, 3, 1]
appreciated	2.3	0.78102	[2, 1, 3, 2, 3, 4, 2, 2, 2, 2]
appreciates	2.3	0.9	[3, 1, 3, 1, 2, 4, 2, 3, 2, 2]
appreciating	1.9	0.7	[1, 1, 2, 2, 2, 2, 1, 3, 2, 3]
appreciation	2.3	0.9	[3, 3, 2, 1, 1, 3, 3, 1, 3, 3]
appreciations	1.7	0.78102	[3, 2, 1, 2, 2, 1, 3, 1, 1, 1]
appreciative	2.6	0.8	[3, 3, 3, 2, 1, 3, 4, 2, 2, 3]
appreciatively	1.8	0.6	[2, 2, 2, 2, 2, 1, 3, 2, 1, 1]
appreciativeness	1.6	0.8	[2, 1, 1, 2, 1, 1, 3, 3, 1, 1]
appreciator	2.6	0.8	[2, 3, 2, 3, 3, 2, 1, 3, 4, 3]
appreciators	1.5	0.80623	[1, 3, 1, 3, 1, 1, 2, 1, 1, 1]
appreciatory	1.7	0.78102	[1, 2, 1, 3, 1, 3, 2, 2, 1, 1]
apprehensible	1.1	1.04403	[2, 0, -1, 3, 1, 2, 1, 1, 1, 1]
apprehensibly	-0.2	1.16619	[0, 0, -1, 2, -1, 2, -1, -1, -1, -1]
apprehension	-2.1	0.83066	[-1, -2, -2, -1, -2, -3, -3, -3, -1, -3]
apprehensions	-0.9	1.04403	[-1, -1, -1, -1, -2, -2, -1, -1, -1, 2]
apprehensively	-0.3	1.18743	[-1, -1, -1, 0, 1, -2, -1, 1, -1, 2]
apprehensiveness	-0.7	0.9	[-1, 1, -1, -1, 1, -1, -2, -1, -1, -1]
approval	2.1	0.53852	[2, 2, 2, 2, 2, 1, 3, 3, 2, 2]
approved	1.8	0.6	[1, 1, 1, 2, 3, 2, 2, 2, 2, 2]
approves	1.7	0.64031	[1, 1, 1, 2, 3, 2, 2, 2, 2, 1]
ardent	2.1	0.7	[3, 3, 2, 2, 3, 1, 2, 2, 1, 2]
arguable	-1.0	0.63246	[-1, -1, -2, -1, -1, -2, 0, 0, -1, -1]
arguably	-1.0	1.09545	[0, -2, 0, -2, 0, 1, -1, -2, -2, -2]
argue	-1.4	0.66332	[-1, -2, -1, -3, -2, -1, -1, -1, -1, -1]
argued	-1.5	0.5	[-2, -2, -1, -1, -1, -1, -2, -2, -2, -1]
arguer	-1.6	0.4899	[-2, -2, -1, -2, -1, -1, -2, -2, -2, -1]
arguers	-1.4	0.4899	[-2, -1, -1, -2, -1, -1, -2, -2, -1, -1]
argues	-1.6	0.4899	[-2, -2, -1, -2, -1, -1, -2, -2, -2, -1]
arguing	-2.0	0.63246	[-3, -3, -1, -1, -2, -2, -2, -2, -2, -2]
argument	-1.5	0.80623	[-3, -1, -1, -1, -1, -2, 0, -2, -2, -2]
argumentative	-1.5	0.67082	[-3, -2, -1, -1, -1, -1, -1, -2, -2, -1]
argumentatively	-1.8	0.9798	[-4, -2, -1, -1, -3, -1, -2, -2, -1, -1]
argumentive	-1.5	0.80623	[-3, -2, -2, -1, 0, -1, -1, -1, -2, -2]
arguments	-1.7	0.64031	[-1, -1, -2, -2, -2, -3, -2, -1, -1, -2]
arrest	-1.4	1.42829	[-1, 0, -1, 0, -2, -3, 0, -4, -3, 0]
arrested	-2.1	1.04403	[-2, -2, -2, -1, -1, -4, -1, -4, -2, -2]
arrests	-1.9	0.83066	[-4, -2, -1, -1, -1, -2, -2, -2, -2, -2]
arrogance	-2.4	0.66332	[-3, -2, -2, -3, -1, -2, -3, -3, -2, -3]
arrogances	-1.9	0.53852	[-1, -2, -3, -2, -2, -2, -2, -1, -2, -2]
arrogant	-2.2	0.6	[-2, -2, -2, -3, -2, -3, -3, -2, -1, -2]
arrogantly	-1.8	1.4	[-3, -2, 2, -3, -1, -3, -2, -2, -2, -2]
ashamed	-2.1	1.3	[-3, -3, -3, -2, -2, 1, -2, -4, -1, -2]
ashamedly	-1.7	0.64031	[-2, -2, -2, -1, -2, -3, -1, -2, -1, -1]
ass	-2.5	1.43178	[-4, -1, -2, -1, -3, 0, -2, -4, -4, -4]
assassination	-2.9	0.9434	[-2, -4, -4, -3, -3, -4, -2, -3, -3, -1]
assassinations	-2.7	1.34536	[-4, -2, -3, -2, -3, -1, -4, -4, 0, -4]
assault	-2.8	0.9798	[-3, -4, -2, -2, -4, -3, -1, -3, -2, -4]
assaulted	-2.4	1.28062	[-3, -3, 1, -3, -3, -3, -1, -3, -3, -3]
assaulting	-2.3	1.1	[-4, -3, -2, -1, -2, -1, -1, -4, -3, -2]
assaultive	-2.8	0.87178	[-3, -4, -2, -4, -3, -2, -3, -1, -3, -3]
assaults	-2.5	0.92195	[-1, -3, -3, -3, -4, -3, -1, -2, -2, -3]
asset	1.5	0.80623	[2, 1, 1, 3, 2, 0, 2, 2, 1, 1]
assets	0.7	1.00499	[0, 0, 1, 3, 0, 1, 0, 0, 2, 0]
assfucking	-2.5	1.43178	[-3, -3, 0, -3, 0, -2, -4, -4, -4, -2]
assholes	-2.8	0.74833	[-3, -3, -3, -3, -4, -3, -2, -3, -1, -3]
assurance	1.4	0.4899	[1, 1, 2, 2, 1, 1, 1, 2, 2, 1]
assurances	1.4	0.4899	[2, 2, 1, 1, 1, 2, 2, 1, 1, 1]
assure	1.4	0.4899	[1, 1, 1, 1, 2, 1, 1, 2, 2, 2]
assured	1.5	0.67082	[1, 1, 2, 1, 1, 3, 2, 1, 2, 1]
assuredly	1.6	0.66332	[1, 1, 1, 3, 2, 2, 2, 1, 2, 1]
assuredness	1.4	0.8	[2, 2, 2, 1, 1, 0, 2, 0, 2, 2]
assurer	0.9	1.13578	[2, 1, 0, 1, -2, 2, 2, 1, 1, 1]
assurers	1.1	0.9434	[2, 0, 0, 1, 3, 2, 0, 1, 1, 1]
assures	1.3	0.45826	[2, 1, 1, 1, 2, 1, 2, 1, 1, 1]
assurgent	1.3	0.9	[2, 1, 0, 0, 1, 2, 1, 2, 3, 1]
assuring	1.6	0.66332	[1, 2, 2, 3, 1, 1, 1, 1, 2, 2]
assuror	0.5	0.67082	[0, 1, 0, 1, 2, 0, 0, 1, 0, 0]
assurors	0.7	1.34536	[2, -1, 0, 2, 0, -2, 2, 1, 1, 2]
astonished	1.6	0.8	[3, 1, 0, 2, 2, 1, 2, 2, 1, 2]
astound	1.7	1.26886	[2, 2, 2, 2, 0, 3, 4, 0, 0, 2]
astounded	1.8	0.9798	[1, 3, 0, 1, 2, 2, 3, 2, 1, 3]
astounding	1.8	1.4	[3, 4, 2, 0, 1, 2, -1, 3, 2, 2]
astoundingly	2.1	1.44568	[3, 0, 4, 1, 4, 3, 1, 3, 0, 2]
astounds	2.1	1.22066	[3, 3, 1, 0, 3, 2, 3, 3, 0, 3]
attachment	1.2	0.9798	[2, 0, 1, 2, 3, 1, 1, 2, 0, 0]
attachments	1.1	0.7	[1, 1, 2, 0, 2, 1, 2, 0, 1, 1]
attack	-2.1	0.83066	[-1, -3, -2, -3, -3, -1, -2, -1, -2, -3]
attacked	-2.0	1.78885	[-2, 3, -2, -2, -3, -3, -3, -4, -2, -2]
attacker	-2.7	0.9	[-2, -3, -2, -1, -3, -3, -4, -4, -2, -3]
attackers	-2.7	0.64031	[-3, -3, -3, -2, -3, -3, -4, -2, -2, -2]
attacking	-2.0	0.89443	[-3, -1, -1, -3, -3, -1, -3, -1, -2, -2]
attacks	-1.9	0.9434	[-2, -2, 0, -2, -2, -2, -1, -4, -2, -2]
attract	1.5	0.92195	[1, 3, 1, 1, 3, 1, 1, 2, 0, 2]
attractancy	0.9	0.7	[1, 0, 2, 1, 0, 2, 1, 1, 0, 1]
attractant	1.3	0.9	[0, 1, 0, 1, 1, 2, 3, 2, 1, 2]
attractants	1.4	0.8	[1, 1, 0, 2, 2, 2, 3, 1, 1, 1]
attracted	1.8	0.6	[1, 3, 1, 2, 2, 2, 2, 2, 2, 1]
attracting	2.1	0.83066	[3, 1, 2, 2, 3, 1, 1, 3, 2, 3]
attraction	2.0	0.7746	[2, 2, 1, 1, 2, 3, 3, 2, 3, 1]
attractions	1.8	0.87178	[1, 3, 0, 2, 2, 2, 2, 3, 1, 2]
attractive	1.9	0.53852	[2, 2, 2, 1, 3, 2, 1, 2, 2, 2]
attractively	2.2	0.6	[3, 2, 2, 3, 2, 2, 2, 3, 1, 2]
attractiveness	1.8	1.16619	[3, 2, 2, 1, 4, 2, 0, 0, 2, 2]
attractivenesses	2.1	0.7	[2, 1, 2, 3, 2, 3, 3, 1, 2, 2]
attractor	1.2	1.16619	[1, 1, 2, 2, 2, -2, 2, 1, 2, 1]
attractors	1.2	0.87178	[1, 1, 2, 2, 0, 1, 3, 1, 0, 1]
attracts	1.7	1.00499	[2, 1, 2, 0, 2, 4, 2, 1, 1, 2]
audacious	0.9	2.02237	[3, -1, -2, 2, 1, 2, -3, 2, 2, 3]
authority	0.3	0.64031	[0, 0, 0, 1, 0, 0, 2, 0, 0, 0]
aversion	-1.9	1.04403	[-3, -3, -1, -2, 0, -3, -1, -2, -1, -3]
aversions	-1.1	1.13578	[-2, -1, -2, -2, -2, 1, -1, -2, 1, -1]
aversive	-1.6	0.66332	[-2, -1, -1, -1, -2, -2, -1, -2, -3, -1]
aversively	-0.8	1.53623	[-3, -1, -2, -1, 1, -2, 2, -2, 1, -1]
avert	-0.7	0.78102	[-1, 0, -2, -1, -1, 1, -1, 0, -1, -1]
averted	-0.3	1.00499	[-1, 1, 0, 0, 1, 0, -2, 0, 0, -2]
averts	-0.4	1.0198	[-2, -2, -1, -1, 0, 0, 1, 0, 1, 0]
avid	1.2	0.87178	[-1, 2, 2, 1, 1, 1, 2, 2, 1, 1]
avoid	-1.2	0.6	[-1, -1, -1, -1, -2, -2, -1, -2, 0, -1]
avoidance	-1.7	0.45826	[-2, -2, -1, -1, -1, -2, -2, -2, -2, -2]
avoidances	-1.1	0.53852	[-1, -1, -1, -1, -2, 0, -1, -2, -1, -1]
avoided	-1.4	0.4899	[-2, -1, -2, -1, -1, -1, -2, -1, -2, -1]
avoider	-1.8	0.6	[-2, -1, -3, -1, -2, -2, -2, -1, -2, -2]
avoiders	-1.4	0.66332	[-2, -2, -1, -2, -1, -1, 0, -1, -2, -2]
avoiding	-1.4	0.91652	[-2, 1, -2, -2, -1, -2, -1, -1, -2, -2]
avoids	-0.7	0.45826	[-1, -1, -1, -1, -1, -1, 0, 0, 0, -1]
await	0.4	0.4899	[0, 0, 0, 1, 0, 1, 0, 1, 1, 0]
awaited	-0.1	0.83066	[1, 0, 0, 0, 1, -1, 0, 0, -2, 0]
awaits	0.3	0.78102	[0, 0, 0, 1, 2, 1, 0, 0, -1, 0]
award	2.5	0.92195	[2, 1, 1, 3, 3, 2, 4, 3, 3, 3]
awardable	2.4	0.8	[3, 3, 3, 1, 3, 1, 2, 3, 2, 3]
awarded	1.7	0.78102	[2, 0, 1, 3, 2, 2, 2, 1, 2, 2]
awardee	1.8	0.6	[2, 2, 1, 3, 2, 2, 1, 1, 2, 2]
awardees	1.2	0.74833	[1, 1, 1, 1, 0, 1, 1, 1, 3, 2]
awarder	0.9	1.04403	[2, 0, 1, 3, 0, 2, 1, 0, 0, 0]
awarders	1.3	1.18743	[2, 1, 0, 2, 2, 0, 0, 4, 1, 1]
awarding	1.9	0.7	[3, 2, 1, 2, 1, 2, 3, 1, 2, 2]
awards	2.0	0.44721	[2, 2, 2, 2, 1, 2, 2, 3, 2, 2]
awesome	3.1	0.83066	[3, 4, 2, 3, 2, 2, 4, 4, 4, 3]
awful	-2.0	2.04939	[-2, -2, -3, -3, -2, -3, 4, -3, -3, -3]
awkward	-0.6	1.56205	[-2, -1, -1, -1, -1, -1, -1, -1, 4, -1]
awkwardly	-1.3	0.45826	[-1, -1, -2, -1, -1, -1, -1, -2, -2, -1]
awkwardness	-0.7	1.41774	[-1, -2, -2, -1, -2, 2, -1, -1, 2, -1]
axe	-0.4	0.8	[-2, 0, 0, 0, 0, 0, -1, -1, -1, 1]
axed	-1.3	0.78102	[-1, -2, 0, -3, -1, -2, -1, -1, -1, -1]
backed	0.1	0.3	[0, 0, 1, 0, 0, 0, 0, 0, 0, 0]
backing	0.1	0.83066	[1, 1, -1, 0, 0, 0, -1, 1, -1, 1]
backs	-0.2	0.4	[0, 0, -1, 0, 0, 0, 0, -1, 0, 0]
bad	-2.5	0.67082	[-3, -2, -4, -3, -2, -2, -3, -2, -2, -2]
badass	1.4	1.26491	[1, 3, 2, 0, -1, 1, 3, 2, 1, 2]
badly	-2.1	0.7	[-2, -3, -2, -1, -3, -2, -3, -2, -1, -2]
bailout	-0.4	1.35647	[-1, 0, 0, 2, -2, -1, -2, 2, -1, -1]
bamboozle	-1.5	1.0247	[-3, -2, -2, -1, -2, -2, 1, -1, -1, -2]
bamboozled	-1.5	1.11803	[-1, 0, -2, -4, -2, -1, -2, -1, 0, -2]
bamboozles	-1.5	1.0247	[-1, 0, -2, -4, -2, -1, -2, -1, -1, -1]
ban	-2.6	1.0198	[-4, -3, -4, -3, -2, -1, -3, -2, -1, -3]
banish	-1.9	0.9434	[-2, -2, -2, -2, -1, -3, -1, -1, -1, -4]
bankrupt	-2.6	1.0198	[-4, -4, -2, -2, -4, -3, -2, -2, -1, -2]
bankster	-2.1	0.53852	[-3, -1, -2, -2, -2, -2, -2, -3, -2, -2]
banned	-2.0	1.0	[-2, -1, -1, -1, -2, -2, -4, -3, -3, -1]
bargain	0.8	1.16619	[1, 1, 1, 1, 0, 1, 2, 3, -1, -1]
barrier	-0.5	0.92195	[-2, 0, 0, -2, -1, -1, 1, 0, 0, 0]
bashful	-0.1	1.13578	[-1, 2, -1, 0, -1, 2, 0, 0, -1, -1]
bashfully	0.2	0.9798	[0, 0, 0, -1, 1, 1, 1, 1, -2, 1]
bashfulness	-0.8	0.9798	[-2, -1, 1, 1, -1, -2, -1, -1, -1, -1]
bastard	-2.5	0.67082	[-2, -4, -2, -3, -2, -2, -3, -3, -2, -2]
bastardies	-1.8	0.87178	[-2, -1, -3, -2, -2, -2, -1, 0, -2, -3]
bastardise	-2.1	0.83066	[-2, -1, -4, -2, -2, -3, -2, -2, -2, -1]
bastardised	-2.3	0.9	[-3, -2, -3, -1, -2, -3, -4, -1, -2, -2]
bastardises	-2.3	1.18743	[-1, -4, -2, -3, -3, 0, -2, -4, -2, -2]
bastardising	-2.6	0.8	[-3, -2, -3, -2, -2, -1, -3, -4, -3, -3]
bastardization	-2.4	1.28062	[-1, -3, -4, -4, -2, -2, -2, 0, -2, -4]
bastardizations	-2.1	0.7	[-2, -1, -3, -3, -2, -2, -3, -1, -2, -2]
bastardize	-2.4	0.66332	[-2, -2, -3, -2, -2, -3, -4, -2, -2, -2]
bastardized	-2.0	0.7746	[-2, -1, -1, -2, -3, -2, -3, -2, -1, -3]
bastardizes	-1.8	0.87178	[-2, -1, -1, -2, -2, -2, -3, -3, 0, -2]
bastardizing	-2.3	0.9	[-2, -2, -1, -4, -3, -2, -1, -2, -3, -3]
bastardly	-2.7	0.64031	[-3, -3, -2, -2, -3, -2, -2, -4, -3, -3]
bastards	-3.0	0.63246	[-4, -2, -3, -3, -4, -3, -3, -3, -3, -2]
bastardy	-2.7	1.1	[-4, -3, -2, -2, -4, -3, -3, -3, -3, 0]
battle	-1.6	1.28062	[-1, -3, 0, -3, -2, -3, -2, -2, 1, -1]
battled	-1.2	0.87178	[0, 0, -2, 0, -2, -1, -2, -1, -2, -2]
battlefield	-1.6	0.8	[-2, -2, 0, -1, -2, -1, -3, -2, -1, -2]
battlefields	-0.9	1.22066	[-1, -1, 0, 0, -4, -1, 0, 0, 0, -2]
battlefront	-1.2	0.87178	[-1, 0, -2, -1, 0, -3, -1, -2, -1, -1]
battlefronts	-0.8	1.16619	[0, 0, -2, -1, 0, -3, -1, 1, -2, 0]
battleground	-1.7	0.78102	[-2, 0, -2, -2, -1, -2, -2, -3, -1, -2]
battlegrounds	-0.6	1.35647	[2, -2, 0, -1, 0, -2, -2, -2, 0, 1]
battlement	-0.4	0.8	[0, -1, 0, -1, 0, -2, 0, 1, 0, -1]
battlements	-0.4	0.66332	[0, 0, 0, 0, 0, -1, -2, 0, -1, 0]
battler	-0.8	1.4	[2, 0, -2, 0, -2, 1, -2, -1, -2, -2]
battlers	-0.2	0.9798	[-1, 0, 2, -2, 0, 0, 0, -1, 0, 0]
battles	-1.6	0.4899	[-1, -1, -2, -1, -2, -2, -2, -1, -2, -2]
battleship	-0.1	1.3	[2, -3, -1, -1, 0, 0, 1, 1, 0, 0]
battleships	-0.5	0.80623	[-2, 0, 0, 0, 0, -1, -2, 0, 0, 0]
battlewagon	-0.3	0.64031	[0, 0, -1, -2, 0, 0, 0, 0, 0, 0]
battlewagons	-0.5	0.67082	[0, 0, 0, -1, -2, -1, 0, -1, 0, 0]
battling	-1.1	1.04403	[0, -1, -2, -2, -1, 1, -2, 0, -2, -2]
beaten	-1.8	0.6	[-1, -2, -2, -2, -3, -2, -2, -2, -1, -1]
beatific	1.8	1.6	[3, 0, 2, 4, -2, 2, 2, 2, 2, 3]
beating	-2.0	0.63246	[-2, -3, -2, -2, -1, -1, -2, -3, -2, -2]
beaut	1.6	1.2	[2, 2, 2, 0, 1, -1, 2, 3, 3, 2]
beauteous	2.5	1.0247	[2, 1, 4, 3, 3, 2, 1, 4, 2, 3]
beauteously	2.6	0.8	[2, 3, 3, 3, 2, 1, 3, 4, 2, 3]
beauteousness	2.7	1.00499	[1, 3, 4, 3, 4, 1, 2, 3, 3, 3]
beautician	1.2	0.9798	[0, 0, 3, 2, 2, 0, 1, 1, 1, 2]
beauticians	0.4	0.66332	[0, 1, 0, 0, 0, 0, 1, 0, 2, 0]
beauties	2.4	0.8	[2, 3, 3, 3, 3, 1, 2, 1, 3, 3]
beautification	1.9	0.7	[2, 2, 2, 1, 2, 1, 3, 1, 2, 3]
beautifications	2.4	0.8	[3, 2, 3, 3, 1, 3, 2, 3, 1, 3]
beautified	2.1	0.7	[2, 2, 3, 1, 2, 2, 3, 1, 2, 3]
beautifier	1.7	0.64031	[2, 1, 2, 1, 2, 1, 3, 1, 2, 2]
beautifiers	1.7	0.78102	[3, 3, 1, 2, 1, 2, 2, 1, 1, 1]
beautifies	1.8	0.74833	[2, 1, 2, 1, 2, 1, 3, 1, 2, 3]
beautiful	2.9	0.7	[2, 3, 2, 3, 2, 3, 4, 4, 3, 3]
beautifuler	2.1	0.83066	[2, 0, 2, 2, 3, 2, 2, 2, 3, 3]
beautifulest	2.6	0.8	[3, 3, 3, 3, 2, 2, 4, 2, 1, 3]
beautifully	2.7	0.64031	[3, 3, 2, 2, 3, 3, 2, 2, 4, 3]
beautifulness	2.6	0.8	[3, 3, 3, 2, 3, 4, 3, 2, 2, 1]
beautify	2.3	0.45826	[2, 3, 3, 2, 2, 3, 2, 2, 2, 2]
beautifying	2.3	0.78102	[1, 2, 1, 3, 2, 2, 3, 3, 3, 3]
beauts	1.7	0.78102	[1, 2, 0, 3, 1, 2, 2, 2, 2, 2]
beauty	2.8	0.74833	[3, 3, 2, 3, 4, 4, 2, 2, 3, 2]
belittle	-1.9	0.53852	[-2, -2, -2, -1, -2, -2, -1, -2, -3, -2]
belittled	-2.0	1.0	[-3, -2, -3, -3, -2, -3, -1, -1, -2, 0]
beloved	2.3	0.45826	[2, 2, 3, 2, 2, 2, 2, 3, 3, 2]
benefic	1.4	0.4899	[2, 2, 1, 1, 1, 2, 2, 1, 1, 1]
benefice	0.4	0.66332	[0, 2, 0, 1, 0, 0, 1, 0, 0, 0]
beneficed	1.1	0.7	[1, 1, 1, 0, 2, 0, 1, 1, 2, 2]
beneficence	2.8	0.87178	[1, 4, 3, 4, 2, 2, 3, 3, 3, 3]
beneficences	1.5	0.67082	[1, 1, 2, 2, 1, 1, 1, 3, 2, 1]
beneficent	2.3	0.45826	[3, 2, 3, 2, 2, 2, 2, 3, 2, 2]
beneficently	2.2	0.6	[3, 2, 3, 3, 2, 2, 1, 2, 2, 2]
benefices	1.1	0.83066	[1, 1, 1, 0, 1, 0, 1, 1, 2, 3]
beneficial	1.9	0.53852	[2, 2, 1, 2, 2, 1, 3, 2, 2, 2]
beneficially	2.4	0.8	[3, 3, 2, 2, 2, 1, 3, 4, 2, 2]
beneficialness	1.7	0.64031	[2, 1, 2, 2, 1, 1, 2, 2, 1, 3]
beneficiaries	1.8	1.16619	[0, 1, 2, 1, 3, 4, 3, 1, 2, 1]
beneficiary	2.1	0.83066	[1, 3, 2, 2, 2, 1, 3, 3, 1, 3]
beneficiate	1.0	1.18322	[0, 0, 1, 3, 1, 0, 2, 0, 3, 0]
beneficiation	0.4	1.0198	[2, 2, 0, -1, 1, 0, -1, 0, 0, 1]
benefit	2.0	0.63246	[2, 3, 1, 2, 2, 3, 2, 1, 2, 2]
benefits	1.6	0.4899	[2, 2, 2, 1, 2, 1, 2, 2, 1, 1]
benefitted	1.7	0.64031	[2, 2, 1, 2, 2, 2, 2, 2, 0, 2]
benefitting	1.9	0.7	[1, 2, 2, 3, 1, 1, 2, 3, 2, 2]
benevolence	1.7	1.1	[2, 1, 3, 2, 2, 1, 2, 2, -1, 3]
benevolences	1.9	1.64012	[3, 2, -1, -1, 3, 1, 3, 4, 2, 3]
benevolent	2.7	0.78102	[2, 2, 3, 2, 4, 2, 3, 2, 4, 3]
benevolently	1.4	1.11355	[2, 1, 2, 2, 1, -1, 2, 3, 0, 2]
benevolentness	1.2	1.249	[2, 2, 1, -1, 2, 2, 3, -1, 1, 1]
benign	1.3	0.9	[1, 3, 2, 2, 1, 1, 2, 0, 0, 1]
benignancy	0.6	1.2	[2, -1, -2, 1, 0, 1, 1, 2, 1, 1]
benignant	2.2	0.9798	[2, 2, 2, 2, 2, 2, 4, 0, 3, 3]
benignantly	1.1	1.3	[3, 2, 3, 0, 1, 1, -1, 2, 0, 0]
benignities	0.9	0.9434	[-1, 2, 1, 0, 1, 1, 2, 2, 1, 0]
benignity	1.3	1.18743	[2, -2, 2, 2, 2, 1, 2, 1, 1, 2]
benignly	0.2	1.07703	[0, -1, 1, 1, 1, 0, 2, 0, -2, 0]
bereave	-2.1	1.13578	[0, -2, -2, -3, -3, -4, -3, -2, -1, -1]
bereaved	-2.1	0.9434	[-2, -2, -2, -1, -2, -1, -4, -1, -3, -3]
bereaves	-1.9	1.22066	[0, -3, 0, -1, -3, -2, -3, -1, -3, -3]
bereaving	-1.3	1.84662	[-3, -4, -3, 1, -2, 0, -3, -1, 1, 1]
best	3.2	0.6	[2, 4, 4, 3, 4, 3, 3, 3, 3, 3]
betray	-3.2	0.6	[-3, -4, -4, -3, -2, -3, -4, -3, -3, -3]
betrayal	-2.8	0.74833	[-3, -4, -4, -2, -3, -2, -3, -3, -2, -2]
betrayed	-3.0	0.63246	[-2, -3, -3, -3, -4, -4, -3, -3, -2, -3]
betraying	-2.5	0.67082	[-2, -2, -3, -2, -3, -2, -4, -2, -2, -3]
betrays	-2.5	0.67082	[-2, -3, -3, -2, -2, -2, -4, -2, -3, -2]
better	1.9	0.7	[2, 1, 2, 1, 1, 3, 2, 2, 3, 2]
bias	-0.4	1.11355	[-1, -2, 0, -2, -1, 1, -1, 1, 0, 1]
biased	-1.1	0.83066	[-2, -2, -1, -1, -1, -1, -1, 1, -2, -1]
bitch	-2.8	0.87178	[-1, -4, -2, -4, -3, -2, -3, -3, -3, -3]
bitched	-2.6	1.0198	[-1, -3, -2, -3, -2, -1, -4, -3, -3, -4]
bitcheries	-2.3	0.78102	[-2, -2, -2, -4, -2, -2, -3, -1, -3, -2]
bitchery	-2.7	1.18743	[-2, -2, -4, -2, -4, -4, -1, -1, -3, -4]
bitches	-2.9	0.9434	[-2, -1, -3, -3, -2, -4, -4, -3, -3, -4]
bitchier	-2.0	0.63246	[-2, -3, -1, -2, -3, -1, -2, -2, -2, -2]
bitchiest	-3.0	0.7746	[-2, -4, -2, -3, -4, -3, -3, -4, -2, -3]
bitchily	-2.6	1.11355	[-4, -4, -2, -2, -1, -4, -1, -2, -3, -3]
bitchiness	-2.6	0.66332	[-3, -3, -2, -2, -3, -3, -1, -3, -3, -3]
bitching	-1.1	1.64012	[-2, 2, -2, -1, 2, -1, -2, -2, -3, -2]
bitchy	-2.3	1.00499	[-4, -1, -2, -3, -3, -2, -1, -1, -3, -3]
bitter	-1.8	0.4	[-2, -2, -2, -1, -2, -2, -1, -2, -2, -2]
bitterbrush	-0.2	0.74833	[0, 0, 0, 0, -2, 1, 0, 0, -1, 0]
bitterbrushes	-0.6	0.8	[-1, 0, -2, -1, -2, 0, 0, 0, 0, 0]
bittered	-1.8	1.07703	[-1, -1, -3, -1, -2, -4, -2, 0, -2, -2]
bitterer	-1.9	1.04403	[-1, -2, -3, -1, -1, -4, -3, -1, -2, -1]
bitterest	-2.3	1.41774	[-4, -4, -2, -1, 1, -2, -3, -3, -2, -3]
bittering	-1.2	0.87178	[0, 0, -1, -2, 0, -2, -1, -2, -2, -2]
bitterish	-1.6	0.8	[0, -2, -1, -1, -2, -2, -2, -1, -3, -2]
bitterly	-2.0	0.63246	[-2, -2, -1, -1, -2, -2, -3, -2, -2, -3]
bittern	-0.2	0.6	[0, 0, -2, 0, 0, 0, 0, 0, 0, 0]
bitterness	-1.7	0.45826	[-2, -2, -1, -2, -1, -2, -1, -2, -2, -2]
bitterns	-0.4	1.11355	[0, 0, 0, -3, 0, 0, 0, 1, 0, -2]
bitterroots	-0.2	0.4	[0, 0, 0, -1, -1, 0, 0, 0, 0, 0]
bitters	-0.4	0.4899	[-1, -1, -1, 0, 0, 0, 0, -1, 0, 0]
bittersweet	-0.3	0.64031	[0, -1, 0, 0, 0, 0, 0, 0, -2, 0]
bittersweetness	-0.6	0.91652	[0, 0, 0, -2, 0, -2, 0, 0, -2, 0]
bittersweets	-0.2	0.9798	[-2, 1, 0, 0, 0, -2, 0, 0, 0, 1]
bitterweeds	-0.5	0.67082	[0, -2, 0, -1, -1, 0, 0, -1, 0, 0]
bizarre	-1.3	1.00499	[-2, 0, 0, -2, 0, -1, -3, -2, -1, -2]
blah	-0.4	1.49666	[-2, -1, -1, -1, -1, -1, -1, 3, 2, -1]
blam	-0.2	1.16619	[-1, 0, 0, -1, -1, -2, -1, 1, 2, 1]
blamable	-1.8	0.4	[-2, -2, -2, -2, -1, -1, -2, -2, -2, -2]
blamably	-1.8	0.4	[-2, -2, -2, -2, -1, -1, -2, -2, -2, -2]
blame	-1.4	1.42829	[-4, -2, -2, -1, -1, -1, -2, 2, -2, -1]
blamed	-2.1	0.53852	[-2, -2, -2, -2, -3, -2, -2, -2, -3, -1]
blameful	-1.7	0.45826	[-2, -2, -2, -2, -2, -2, -1, -1, -2, -1]
blamefully	-1.6	0.66332	[-1, -2, -1, -1, -2, -1, -1, -2, -2, -3]
blameless	0.7	1.73494	[3, 1, 2, 3, -2, 1, -1, 1, 1, -2]
blamelessly	0.9	1.37477	[2, 0, 2, 0, 1, -1, 1, 4, 0, 0]
blamelessness	0.6	1.35647	[0, 2, 1, 2, 1, 0, 1, -3, 1, 1]
blamer	-2.1	0.83066	[-2, -2, -1, -2, -3, -3, -3, -1, -1, -3]
blamers	-2.0	0.63246	[-3, -2, -2, -3, -1, -2, -2, -1, -2, -2]
blames	-1.7	0.45826	[-2, -2, -1, -2, -2, -2, -2, -1, -1, -2]
blameworthiness	-1.6	0.66332	[-2, -2, -1, -2, -2, -1, -3, -1, -1, -1]
blameworthy	-2.3	0.78102	[-3, -3, -2, -2, -2, -1, -1, -3, -3, -3]
blaming	-2.2	0.6	[-2, -3, -1, -3, -2, -2, -3, -2, -2, -2]
bless	1.8	0.6	[3, 2, 2, 1, 2, 2, 1, 2, 2, 1]
blessed	2.9	0.3	[2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
blesseder	2.0	0.63246	[3, 3, 2, 2, 1, 1, 2, 2, 2, 2]
blessedest	2.8	0.87178	[2, 4, 1, 4, 3, 3, 2, 3, 3, 3]
blessedly	1.7	1.1	[2, 2, 1, -1, 3, 2, 3, 1, 2, 2]
blessedness	1.6	1.35647	[2, 2, 2, 2, 2, 3, 3, -1, -1, 2]
blesser	2.6	0.66332	[1, 3, 2, 3, 3, 3, 2, 3, 3, 3]
blessers	1.9	0.7	[2, 2, 1, 2, 2, 2, 3, 1, 3, 1]
blesses	2.6	0.66332	[1, 3, 3, 3, 3, 3, 2, 3, 2, 3]
blessing	2.2	1.07703	[3, 1, 0, 3, 1, 3, 2, 3, 3, 3]
blessings	2.5	0.92195	[3, 3, 3, 2, 1, 3, 2, 3, 4, 1]
blind	-1.7	1.00499	[-4, -1, -1, -2, -1, -1, -3, -2, -1, -1]
bliss	2.7	0.78102	[3, 3, 3, 2, 1, 2, 4, 3, 3, 3]
blissful	2.9	0.83066	[4, 4, 2, 3, 3, 3, 1, 3, 3, 3]
blithe	1.2	1.16619	[2, 2, -1, 1, 1, 2, 2, 2, -1, 2]
block	-1.9	1.13578	[-3, -2, -1, -2, 0, 0, -3, -2, -3, -3]
blockbuster	2.9	0.9434	[3, 4, 3, 2, 3, 4, 3, 2, 1, 4]
blocked	-1.1	1.13578	[-1, 0, 0, -1, -2, -1, -1, -4, -1, 0]
blocking	-1.6	0.91652	[-1, -1, -1, -1, -2, -2, -4, -1, -2, -1]
blocks	-0.9	1.13578	[-1, 0, -1, -2, -2, -2, -1, -1, 2, -1]
bloody	-1.9	0.7	[-2, -2, -2, 0, -2, -2, -3, -2, -2, -2]
blurry	-0.4	1.28062	[-1, -1, -1, -1, 2, -2, -1, 2, 0, -1]
bold	1.6	0.66332	[2, 2, 2, 2, 1, 2, 0, 2, 1, 2]
bolder	1.2	0.6	[2, 1, 1, 1, 1, 2, 1, 2, 0, 1]
boldest	1.6	1.11355	[1, 3, 2, 3, 0, 0, 2, 3, 1, 1]
boldface	0.3	0.64031	[1, 0, 0, 0, 0, 0, 0, 2, 0, 0]
boldfaced	-0.1	1.22066	[0, 0, 0, 2, -1, -2, 2, -1, -1, 0]
boldfaces	0.1	1.3	[0, 0, 0, 2, -2, -2, 2, 1, 0, 0]
boldfacing	0.1	0.7	[0, 0, -1, 2, 0, 0, 0, 0, 0, 0]
boldly	1.5	1.62788	[3, -2, -1, 1, 2, 3, 2, 3, 2, 2]
boldness	1.5	1.0247	[0, 1, 3, 1, 2, 0, 3, 1, 2, 2]
boldnesses	0.9	0.83066	[0, 0, 1, 0, 2, 1, 1, 2, 0, 2]
bolds	1.3	0.78102	[2, 2, 1, 1, 0, 2, 0, 2, 1, 2]
bomb	-2.2	0.87178	[-2, -2, -1, -3, -4, -3, -2, -1, -2, -2]
bonus	2.5	0.67082	[2, 2, 2, 3, 4, 3, 2, 2, 3, 2]
bonuses	2.6	0.91652	[2, 2, 3, 4, 3, 4, 3, 1, 2, 2]
boost	1.7	0.64031	[1, 1, 2, 2, 2, 3, 1, 1, 2, 2]
boosted	1.5	1.5	[-1, 1, 0, 2, 1, 2, 3, 3, 4, 0]
boosting	1.4	0.91652	[1, 1, 3, 0, 2, 2, 0, 2, 1, 2]
boosts	1.3	0.9	[1, 1, 1, 1, 3, 0, 1, 1, 3, 1]
bore	-1.0	0.44721	[-1, -2, 0, -1, -1, -1, -1, -1, -1, -1]
boreal	-0.3	0.9	[0, 0, 0, 0, 1, -2, 0, 0, -2, 0]
borecole	-0.2	0.74833	[1, 0, 0, 0, -1, -2, 0, 0, 0, 0]
borecoles	-0.3	0.45826	[-1, 0, 0, 0, 0, 0, -1, 0, -1, 0]
bored	-1.1	0.9434	[-2, -1, -2, -1, 0, 0, -3, 0, -1, -1]
boredom	-1.3	0.45826	[-1, -1, -1, -1, -2, -1, -1, -2, -2, -1]
boredoms	-1.1	0.83066	[-1, -1, -1, -2, 1, -1, -2, -1, -1, -2]
boreen	0.1	0.3	[0, 0, 0, 0, 0, 1, 0, 0, 0, 0]
boreens	0.2	0.6	[0, 0, 0, 2, 0, 0, 0, 0, 0, 0]
boreholes	-0.2	0.74833	[0, 0, 0, 0, 0, 1, -1, 0, -2, 0]
borer	-0.4	0.4899	[0, -1, 0, 0, -1, -1, 0, 0, 0, -1]
borers	-1.2	0.9798	[-1, 0, -2, -1, -1, -3, -2, 0, -2, 0]
bores	-1.3	0.78102	[-2, -1, -2, -1, -1, -1, -3, 0, -1, -1]
borescopes	-0.1	0.83066	[-1, 0, -1, 2, 0, 0, 0, 0, -1, 0]
boresome	-1.3	0.45826	[-2, -1, -1, -1, -1, -1, -2, -1, -2, -1]
boring	-1.3	0.45826	[-1, -1, -1, -1, -1, -2, -1, -1, -2, -2]
bother	-1.4	0.91652	[-1, -1, -1, -2, -3, -3, -1, -1, -1, 0]
botheration	-1.7	0.64031	[-1, -1, -1, -2, -2, -1, -2, -2, -2, -3]
botherations	-1.3	0.64031	[-2, -2, -2, -1, -2, 0, -1, -1, -1, -1]
bothered	-1.3	0.45826	[-1, -1, -2, -1, -2, -1, -1, -1, -1, -2]
bothering	-1.6	0.4899	[-2, -1, -2, -2, -1, -1, -1, -2, -2, -2]
bothers	-0.8	0.9798	[-1, -2, -1, -1, 2, -1, -1, -1, -1, -1]
bothersome	-1.3	0.45826	[-2, -1, -1, -1, -1, -1, -2, -2, -1, -1]
boycott	-1.3	0.45826	[-2, -1, -1, -1, -1, -2, -1, -1, -2, -1]
boycotted	-1.7	0.64031	[-1, -2, -2, -3, -1, -1, -2, -2, -2, -1]
boycotting	-1.7	0.64031	[-2, -2, -2, 0, -2, -1, -2, -2, -2, -2]
boycotts	-1.4	0.91652	[-2, -2, -2, -2, -1, -2, -1, 1, -1, -2]
brainwashing	-1.5	1.28452	[-2, -2, -2, -3, -1, -2, 2, -1, -2, -2]
brave	2.4	0.8	[2, 3, 4, 3, 1, 3, 2, 2, 2, 2]
braved	1.9	0.83066	[3, 1, 3, 1, 1, 2, 3, 2, 2, 1]
bravely	2.3	0.78102	[1, 3, 2, 2, 2, 4, 2, 2, 2, 3]
braver	2.4	0.8	[3, 2, 2, 1, 4, 2, 3, 3, 2, 2]
braveries	2.0	1.0	[2, 4, 2, 3, 1, 1, 2, 3, 1, 1]
bravery	2.2	0.74833	[1, 2, 2, 2, 3, 2, 3, 3, 1, 3]
braves	1.9	0.83066	[3, 1, 3, 1, 2, 2, 3, 2, 1, 1]
bravest	2.3	0.64031	[1, 3, 3, 2, 3, 2, 3, 2, 2, 2]
breathtaking	2.0	1.26491	[3, 1, 3, 2, -1, 1, 2, 3, 3, 3]
bribe	-0.8	1.98997	[-3, 0, -1, -2, -1, -2, -4, 1, 3, 1]
bright	1.9	0.7	[2, 2, 1, 2, 1, 2, 1, 3, 3, 2]
brighten	1.9	0.7	[2, 1, 1, 2, 3, 2, 1, 2, 2, 3]
brightened	2.1	0.83066	[2, 3, 1, 1, 2, 2, 3, 1, 3, 3]
brightener	1.0	1.18322	[0, 0, 0, 1, 0, 1, 2, 0, 3, 3]
brighteners	1.0	0.89443	[0, 1, 0, 1, 1, 3, 0, 1, 2, 1]
brightening	2.5	0.92195	[2, 3, 2, 1, 2, 3, 4, 4, 2, 2]
brightens	1.5	0.5	[2, 1, 1, 2, 2, 1, 2, 2, 1, 1]
brighter	1.6	0.66332	[1, 1, 1, 2, 2, 2, 1, 2, 1, 3]
brightest	3.0	0.63246	[3, 3, 2, 3, 4, 3, 2, 3, 4, 3]
brightly	1.5	0.67082	[2, 3, 1, 2, 1, 1, 2, 1, 1, 1]
brightness	1.6	0.91652	[2, 2, 1, 1, 1, 3, 3, 0, 2, 1]
brightnesses	1.4	0.91652	[2, 3, 1, 2, 1, 1, 0, 0, 2, 2]
brights	0.4	0.66332	[0, 0, 2, 0, 0, 1, 0, 0, 1, 0]
brightwork	1.1	0.83066	[1, 0, 1, 2, 1, 0, 3, 1, 1, 1]
brilliance	2.9	0.83066	[4, 3, 2, 4, 4, 3, 2, 3, 2, 2]
brilliances	2.9	0.83066	[3, 4, 3, 4, 4, 2, 3, 2, 2, 2]
brilliancies	2.3	1.18743	[1, 4, 1, 3, 3, 2, 1, 3, 4, 1]
brilliancy	2.6	1.0198	[4, 3, 2, 4, 2, 3, 1, 3, 1, 3]
brilliant	2.8	0.6	[2, 3, 3, 2, 3, 3, 4, 2, 3, 3]
brilliantine	0.8	1.16619	[-1, 3, 1, 0, 1, 0, 2, 0, 2, 0]
brilliantines	2.0	1.34164	[0, 1, 4, 2, 3, 1, 3, 0, 3, 3]
brilliantly	3.0	0.44721	[3, 2, 3, 3, 3, 3, 3, 3, 4, 3]
brilliants	1.9	0.83066	[3, 1, 2, 1, 2, 1, 3, 2, 1, 3]
brisk	0.6	0.8	[0, 0, 0, 0, 1, 1, 0, 2, 0, 2]
broke	-1.8	0.4	[-2, -2, -2, -2, -1, -2, -2, -1, -2, -2]
broken	-2.1	0.53852	[-2, -2, -2, -2, -3, -2, -1, -3, -2, -2]
brooding	0.1	1.3	[3, 0, -1, -1, -1, 1, 1, -1, 1, -1]
brutal	-3.1	0.7	[-3, -3, -4, -2, -3, -4, -3, -4, -3, -2]
brutalise	-2.7	1.1	[-4, -3, -3, -4, -3, -2, -2, -3, 0, -3]
brutalised	-2.9	0.83066	[-3, -3, -2, -3, -3, -4, -4, -1, -3, -3]
brutalises	-3.2	0.4	[-3, -3, -3, -3, -3, -4, -4, -3, -3, -3]
brutalising	-2.8	0.74833	[-3, -3, -4, -3, -2, -3, -3, -3, -1, -3]
brutalities	-2.6	1.0198	[-4, -2, -2, -4, -2, -4, -2, -3, -1, -2]
brutality	-3.0	0.63246	[-2, -3, -4, -2, -3, -3, -3, -4, -3, -3]
brutalization	-2.1	2.16564	[-3, -2, -4, -2, -4, -4, 2, -3, 2, -3]
brutalizations	-2.3	0.64031	[-2, -2, -3, -2, -1, -3, -3, -2, -2, -3]
brutalize	-2.9	0.7	[-3, -4, -3, -3, -2, -2, -3, -4, -2, -3]
brutalized	-2.4	0.4899	[-3, -2, -2, -3, -2, -2, -3, -3, -2, -2]
brutalizes	-3.2	0.6	[-4, -4, -3, -2, -3, -4, -3, -3, -3, -3]
brutalizing	-3.4	0.66332	[-4, -3, -4, -3, -4, -4, -3, -4, -2, -3]
brutally	-3.0	0.44721	[-3, -3, -3, -3, -3, -3, -3, -2, -3, -4]
bullied	-3.1	0.9434	[-4, -4, -4, -2, -2, -4, -4, -3, -2, -2]
bullshit	-2.8	0.6	[-3, -3, -3, -3, -3, -4, -2, -3, -2, -2]
bully	-2.2	1.6	[-2, -3, -3, -4, -2, -1, 2, -3, -3, -3]
bullying	-2.9	0.7	[-3, -2, -3, -2, -4, -2, -3, -3, -4, -3]
bummer	-1.6	0.8	[-3, -1, -1, -1, -3, -1, -1, -2, -2, -1]
buoyant	0.9	0.83066	[0, 1, 1, 2, 1, 0, 0, 2, 0, 2]
burden	-1.9	0.53852	[-2, -2, -1, -2, -3, -2, -1, -2, -2, -2]
burdened	-1.7	0.45826	[-2, -2, -2, -2, -1, -2, -1, -2, -2, -1]
burdener	-1.3	0.45826	[-1, -1, -2, -1, -1, -1, -2, -1, -2, -1]
burdeners	-1.7	1.00499	[-2, -2, -2, -1, -3, -2, 0, 0, -2, -3]
burdening	-1.4	0.66332	[-2, -1, -2, -2, -1, -1, -1, 0, -2, -2]
burdens	-1.5	0.5	[-2, -2, -1, -1, -2, -1, -1, -1, -2, -2]
burdensome	-1.8	0.9798	[-1, -1, -3, -2, -1, -2, -2, -1, -4, -1]
bwahaha	0.4	1.0198	[0, 1, 0, 1, 0, 2, -1, -1, 2, 0]
bwahahah	2.5	0.92195	[3, 4, 2, 2, 2, 3, 1, 2, 2, 4]
calm	1.3	0.78102	[1, 1, 0, 1, 2, 3, 2, 1, 1, 1]
calmative	1.1	0.9434	[3, 2, -1, 1, 1, 1, 1, 1, 1, 1]
calmatives	0.5	0.80623	[-1, 1, 0, 1, 0, 0, 1, 2, 0, 1]
calmed	1.6	0.4899	[2, 2, 2, 1, 1, 1, 2, 1, 2, 2]
calmer	1.5	0.67082	[1, 2, 3, 1, 1, 1, 2, 1, 2, 1]
calmest	1.6	0.8	[3, 2, 2, 2, 1, 1, 0, 2, 1, 2]
calming	1.7	0.78102	[2, 1, 3, 2, 2, 1, 3, 1, 1, 1]
calmly	1.3	0.9	[0, 0, 1, 3, 2, 2, 1, 1, 2, 1]
calmness	1.7	0.9	[1, 1, 1, 2, 2, 2, 1, 4, 1, 2]
calmnesses	1.6	0.4899	[1, 2, 1, 2, 1, 2, 2, 2, 1, 2]
calmodulin	0.2	0.4	[0, 0, 0, 0, 0, 0, 1, 0, 0, 1]
calms	1.3	0.64031	[2, 1, 1, 2, 0, 1, 1, 2, 2, 1]
can't stand	-2.0	0.63246	[-2, -2, -2, -1, -1, -2, -3, -2, -2, -3]
cancel	-1.0	0.63246	[-2, -1, -1, -1, -1, 0, -1, 0, -2, -1]
cancelled	-1.0	0.44721	[-1, -1, -1, -1, 0, -1, -1, -2, -1, -1]
cancelling	-0.8	0.74833	[0, 0, -1, 0, -1, -1, 0, -2, -1, -2]
cancels	-0.9	0.53852	[0, -1, 0, -1, -1, -1, -1, -1, -2, -1]
cancer	-3.4	0.8	[-2, -4, -3, -4, -4, -2, -3, -4, -4, -4]
capable	1.6	0.4899	[1, 2, 2, 2, 1, 1, 2, 2, 1, 2]
captivated	1.6	0.4899	[2, 1, 2, 2, 2, 2, 1, 2, 1, 1]
care	2.2	0.74833	[2, 1, 2, 1, 2, 2, 3, 3, 3, 3]
cared	1.8	0.74833	[1, 2, 1, 3, 1, 2, 2, 2, 3, 1]
carefree	1.7	0.64031	[1, 1, 2, 2, 2, 2, 1, 1, 2, 3]
careful	0.6	1.11355	[3, 0, 0, 0, 1, 0, -1, 2, 1, 0]
carefully	0.5	0.67082	[1, 0, 1, 1, 0, 1, 0, 1, 1, -1]
carefulness	2.0	0.44721	[3, 2, 2, 1, 2, 2, 2, 2, 2, 2]
careless	-1.5	0.5	[-2, -1, -1, -2, -2, -1, -2, -2, -1, -1]
carelessly	-1.0	0.44721	[-1, -1, -1, -2, -1, 0, -1, -1, -1, -1]
carelessness	-1.4	0.4899	[-2, -1, -1, -2, -1, -2, -1, -1, -2, -1]
carelessnesses	-1.6	1.11355	[-4, -2, -2, -3, -1, 0, -1, -1, -1, -1]
cares	2.0	0.7746	[2, 3, 1, 3, 1, 2, 2, 2, 3, 1]
caring	2.2	0.4	[2, 3, 2, 2, 2, 2, 2, 3, 2, 2]
casual	0.8	0.74833	[1, 1, 0, 1, 0, 2, 0, 1, 2, 0]
casually	0.7	1.00499	[1, 0, 0, 0, 1, 0, 0, 3, 2, 0]
casualty	-2.4	0.91652	[-4, -3, -3, -2, -1, -2, -3, -1, -2, -3]
catastrophe	-3.4	0.4899	[-3, -3, -3, -4, -4, -3, -3, -3, -4, -4]
catastrophic	-2.2	2.22711	[-3, -2, -4, -4, -4, -3, -2, -4, 2, 2]
cautious	-0.4	0.66332	[0, 1, -1, 0, 0, 0, -1, -1, -1, -1]
celebrate	2.7	1.00499	[4, 4, 3, 2, 4, 2, 2, 2, 3, 1]
celebrated	2.7	0.78102	[2, 3, 3, 2, 3, 4, 3, 3, 1, 3]
celebrates	2.7	0.64031	[2, 3, 3, 2, 2, 3, 3, 3, 4, 2]
celebrating	2.7	0.64031	[3, 3, 4, 2, 2, 2, 3, 3, 2, 3]
censor	-2.0	1.34164	[0, -3, -2, -3, -3, 0, -4, -1, -1, -3]
censored	-0.6	1.68523	[-1, -1, -1, 2, -3, -2, -1, -1, -1, 3]
censors	-1.2	1.07703	[-1, 0, -3, 0, -1, 0, -1, -2, -1, -3]
certain	1.1	0.7	[1, 0, 2, 0, 2, 2, 1, 1, 1, 1]
certainly	1.4	1.0198	[3, 2, 0, 1, 3, 1, 0, 1, 1, 2]
certainties	0.9	1.44568	[0, -2, 4, 0, 1, 1, 1, 1, 2, 1]
certainty	1.0	0.89443	[2, 1, 0, 1, 0, 0, 2, 2, 2, 0]
chagrin	-1.9	0.53852	[-1, -2, -3, -2, -1, -2, -2, -2, -2, -2]
chagrined	-1.4	1.2	[-1, -2, 2, -1, -2, -2, -2, -2, -2, -2]
challenge	0.3	1.00499	[1, 0, -1, 1, 1, -1, 1, 0, 2, -1]
challenged	-0.4	1.62481	[0, -2, 1, -1, -3, -1, 3, -1, 1, -1]
challenger	0.5	1.43178	[0, 0, 2, -1, -2, 1, 3, 0, 2, 0]
challengers	0.4	1.56205	[0, -2, -1, 1, 1, 2, 3, 2, -1, -1]
challenges	0.3	1.48661	[0, -1, 2, -1, -2, 0, 3, 0, 2, 0]
challenging	0.6	0.91652	[0, 0, 0, 1, 1, -1, 0, 2, 2, 1]
challengingly	-0.6	1.68523	[0, -1, -2, 1, -3, 2, -2, -1, 2, -2]
champ	2.1	0.83066	[2, 2, 2, 3, 2, 3, 2, 0, 3, 2]
champac	-0.2	0.6	[0, 0, -2, 0, 0, 0, 0, 0, 0, 0]
champagne	1.2	1.07703	[1, 2, 2, 3, 0, 2, 0, 0, 2, 0]
champagnes	0.5	0.92195	[0, 0, 0, 0, 0, 1, 1, 3, 0, 0]
champaign	0.2	0.6	[0, 0, 0, 0, 2, 0, 0, 0, 0, 0]
champaigns	0.5	0.67082	[1, 0, 0, 0, 0, 0, 0, 1, 2, 1]
champaks	-0.2	0.6	[0, 0, 0, 0, 0, 0, -2, 0, 0, 0]
champed	1.0	0.63246	[1, 1, 2, 1, 1, 2, 1, 0, 0, 1]
champer	-0.1	0.53852	[0, -1, 1, 0, 0, 0, 0, -1, 0, 0]
champers	0.5	0.67082	[1, 0, 0, 0, 0, 0, 0, 1, 1, 2]
champerties	-0.1	0.83066	[0, -1, 1, 1, 0, 0, 0, 0, -2, 0]
champertous	0.3	0.78102	[0, 0, 0, 1, -1, 2, 1, 0, 0, 0]
champerty	-0.2	1.32665	[-2, -1, 0, -1, 0, 0, 0, -2, 2, 2]
champignon	0.4	0.8	[0, 0, 0, 0, 0, 2, 0, 2, 0, 0]
champignons	0.2	0.6	[0, 2, 0, 0, 0, 0, 0, 0, 0, 0]
champing	0.7	1.34536	[0, 2, 0, 3, 1, 1, 2, 0, -2, 0]
champion	2.9	0.83066	[3, 2, 3, 4, 4, 3, 2, 2, 4, 2]
championed	1.2	1.53623	[2, 1, 3, 1, 1, -3, 2, 2, 1, 2]
championing	1.8	0.9798	[1, 3, 2, 0, 3, 1, 2, 2, 1, 3]
champions	2.4	1.42829	[4, 0, 0, 3, 1, 3, 4, 3, 3, 3]
championship	1.9	1.04403	[3, 1, 1, 3, 2, 1, 3, 3, 0, 2]
championships	2.2	0.74833	[2, 2, 1, 2, 3, 2, 4, 2, 2, 2]
champs	1.8	0.4	[2, 2, 2, 2, 1, 2, 1, 2, 2, 2]
champy	1.0	1.0	[3, 0, 0, 0, 0, 2, 1, 2, 1, 1]
chance	1.0	0.7746	[1, 1, 0, 0, 0, 2, 1, 2, 1, 2]
chances	0.8	0.4	[0, 1, 1, 0, 1, 1, 1, 1, 1, 1]
chaos	-2.7	0.9	[-2, -2, -3, -1, -4, -3, -3, -2, -3, -4]
chaotic	-2.2	1.4	[-3, -2, -1, -2, -3, 1, -2, -2, -4, -4]
charged	-0.8	0.87178	[-1, -2, -2, -1, -1, 0, -1, 1, 0, -1]
charges	-1.1	0.7	[-2, -2, -2, -1, -1, 0, -1, -1, 0, -1]
charitable	1.7	0.64031	[1, 2, 1, 2, 2, 1, 2, 1, 3, 2]
charitableness	1.9	0.9434	[3, 1, 1, 3, 1, 3, 3, 2, 1, 1]
charitablenesses	1.6	1.74356	[2, 2, 3, 4, 1, -1, -2, 3, 2, 2]
charitably	1.4	0.66332	[1, 2, 1, 2, 2, 1, 0, 1, 2, 2]
charities	2.2	0.6	[3, 3, 2, 2, 1, 2, 2, 3, 2, 2]
charity	1.8	0.87178	[1, 3, 2, 2, 2, 1, 2, 0, 2, 3]
charm	1.7	0.78102	[3, 1, 1, 3, 2, 2, 1, 1, 1, 2]
charmed	2.0	0.63246	[3, 1, 2, 2, 2, 3, 1, 2, 2, 2]
charmer	1.9	0.53852	[3, 2, 2, 2, 2, 2, 1, 1, 2, 2]
charmers	2.1	0.83066	[2, 1, 2, 2, 4, 3, 2, 1, 2, 2]
charmeuse	0.3	0.78102	[0, 0, 0, 1, 0, 2, 1, 0, -1, 0]
charmeuses	0.4	0.66332	[0, 0, 1, 0, 1, 0, 0, 0, 0, 2]
charming	2.8	0.4	[3, 3, 3, 3, 3, 3, 2, 3, 2, 3]
charminger	1.5	0.67082	[2, 3, 1, 2, 1, 1, 2, 1, 1, 1]
charmingest	2.4	0.66332	[2, 3, 3, 1, 3, 2, 3, 3, 2, 2]
charmingly	2.2	0.87178	[2, 2, 2, 1, 2, 2, 3, 3, 4, 1]
charmless	-1.8	0.87178	[-3, -1, -3, -1, -1, -1, -2, -1, -3, -2]
charms	1.9	0.7	[1, 2, 3, 2, 1, 2, 3, 1, 2, 2]
chastise	-2.5	0.92195	[-4, -3, -2, -1, -4, -3, -2, -2, -2, -2]
chastised	-2.2	1.16619	[-2, -3, -2, -4, -1, -1, -3, 0, -3, -3]
chastises	-1.7	1.61555	[-3, -3, -3, -1, 1, -2, 1, -1, -2, -4]
chastising	-1.7	0.78102	[-2, -3, -2, -2, -2, 0, -1, -1, -2, -2]
cheat	-2.0	0.7746	[-2, -3, -3, -2, -2, -1, -1, -1, -2, -3]
cheated	-2.3	0.64031	[-2, -4, -2, -2, -2, -2, -3, -2, -2, -2]
cheater	-2.5	0.67082	[-2, -4, -2, -3, -2, -2, -3, -2, -3, -2]
cheaters	-1.9	0.83066	[-2, -2, -2, -1, -1, -4, -2, -1, -2, -2]
cheating	-2.6	0.91652	[-2, -3, -3, -2, -4, -4, -3, -2, -1, -2]
cheats	-1.8	0.6	[-3, -1, -2, -1, -2, -1, -2, -2, -2, -2]
cheer	2.3	0.64031	[2, 1, 2, 2, 2, 3, 3, 3, 2, 3]
cheered	2.3	0.78102	[2, 3, 3, 4, 2, 1, 2, 2, 2, 2]
cheerer	1.7	0.45826	[1, 2, 2, 2, 1, 1, 2, 2, 2, 2]
cheerers	1.8	0.87178	[2, 2, 3, 2, 1, 2, 0, 1, 3, 2]
cheerful	2.5	0.67082	[3, 2, 3, 2, 2, 2, 4, 2, 3, 2]
cheerfuller	1.9	0.83066	[3, 3, 2, 3, 2, 1, 1, 2, 1, 1]
cheerfullest	3.2	0.87178	[4, 4, 4, 4, 3, 2, 2, 3, 2, 4]
cheerfully	2.1	0.83066	[3, 2, 2, 2, 1, 3, 1, 3, 1, 3]
cheerfulness	2.1	0.9434	[3, 2, 1, 2, 3, 4, 1, 2, 1, 2]
cheerier	2.6	0.4899	[2, 2, 3, 3, 2, 3, 3, 2, 3, 3]
cheeriest	2.2	0.6	[3, 2, 3, 1, 2, 2, 3, 2, 2, 2]
cheerily	2.5	0.67082	[3, 3, 2, 3, 2, 4, 2, 2, 2, 2]
cheeriness	2.5	0.67082	[3, 2, 4, 2, 3, 2, 3, 2, 2, 2]
cheering	2.3	0.64031	[3, 3, 2, 1, 3, 2, 2, 2, 3, 2]
cheerio	1.2	0.6	[2, 1, 1, 1, 2, 1, 1, 1, 2, 0]
cheerlead	1.7	0.78102	[1, 2, 0, 2, 2, 2, 2, 3, 1, 2]
cheerleader	0.9	0.9434	[1, 1, 0, 2, 1, 0, 0, 1, 0, 3]
cheerleaders	1.2	1.07703	[2, 0, 0, 1, 1, 0, 3, 3, 1, 1]
cheerleading	1.2	1.07703	[2, 2, 0, 0, 1, 0, 3, 2, 0, 2]
cheerleads	1.2	1.07703	[2, 3, 0, 3, 1, 0, 0, 1, 1, 1]
cheerled	1.5	1.11803	[0, 2, 1, 4, 2, 2, 2, 1, 1, 0]
cheerless	-1.7	1.1	[-2, -3, -2, -2, -3, -2, -1, -1, 1, -2]
cheerlessly	-0.8	1.98997	[-2, 4, -1, -2, -1, -2, -2, -2, 2, -2]
cheerlessness	-1.7	1.48661	[-2, -1, -2, -3, -2, -4, -1, 2, -2, -2]
cheerly	2.4	0.66332	[2, 2, 3, 2, 2, 3, 4, 2, 2, 2]
cheers	2.1	1.3	[2, 2, 1, 3, 2, 3, 3, 4, -1, 2]
cheery	2.6	0.66332	[3, 2, 2, 3, 2, 3, 4, 2, 3, 2]
cherish	1.6	1.49666	[0, 3, 3, 3, 2, 2, 2, 1, -2, 2]
cherishable	2.0	1.41421	[-2, 2, 2, 2, 3, 2, 3, 3, 2, 3]
cherished	2.3	0.64031	[3, 2, 2, 3, 2, 2, 1, 3, 2, 3]
cherisher	2.2	0.4	[2, 2, 3, 2, 2, 2, 2, 3, 2, 2]
cherishers	1.9	0.7	[3, 3, 2, 2, 1, 1, 2, 2, 2, 1]
cherishes	2.2	0.74833	[2, 2, 3, 2, 2, 2, 2, 4, 2, 1]
cherishing	2.0	0.7746	[3, 3, 2, 2, 1, 2, 1, 3, 2, 1]
chic	1.1	1.3	[1, 2, 2, -2, 2, 0, 1, 1, 3, 1]
childish	-1.2	0.74833	[-1, -1, -2, -3, -1, 0, -1, -1, -1, -1]
chilling	-0.1	1.92094	[3, -2, 0, 1, -2, -2, -1, -2, 1, 3]
choke	-2.5	0.92195	[-1, -2, -3, -3, -2, -4, -2, -4, -2, -2]
choked	-2.1	1.3	[-4, -3, 0, -2, -1, -3, -3, -2, 0, -3]
chokes	-2.0	0.89443	[-4, -3, -1, -2, -1, -2, -2, -2, -1, -2]
choking	-2.0	1.26491	[-4, -2, -2, -3, -2, -2, -3, -1, 1, -2]
chuckle	1.7	0.45826	[2, 1, 2, 2, 2, 2, 1, 1, 2, 2]
chuckled	1.2	0.9798	[2, 2, 1, 1, 2, 0, 1, 2, -1, 2]
chucklehead	-1.9	0.53852	[-2, -2, -1, -3, -2, -2, -2, -2, -1, -2]
chuckleheaded	-1.3	1.84662	[-3, -4, -2, 0, 3, -1, -2, 0, -2, -2]
chuckleheads	-1.1	0.9434	[-1, -2, 0, -1, -1, -3, 0, 0, -2, -1]
chuckler	0.8	1.07703	[2, 1, -1, 0, 2, 1, 1, 2, -1, 1]
chucklers	1.2	0.87178	[1, 1, 2, 3, 1, 0, 1, 0, 2, 1]
chuckles	1.1	1.13578	[2, 2, -1, 1, 2, 1, 1, 2, -1, 2]
chucklesome	1.1	0.53852	[1, 1, 2, 1, 1, 1, 0, 2, 1, 1]
chuckling	1.4	0.4899	[1, 2, 1, 2, 1, 1, 2, 2, 1, 1]
chucklingly	1.2	0.4	[1, 1, 1, 1, 2, 1, 1, 1, 2, 1]
clarifies	0.9	1.13578	[-2, 1, 0, 2, 1, 2, 2, 1, 1, 1]
clarity	1.7	0.78102	[2, 1, 2, 3, 3, 1, 1, 2, 1, 1]
classy	1.9	0.53852	[1, 2, 2, 1, 3, 2, 2, 2, 2, 2]
clean	1.7	0.78102	[3, 1, 2, 1, 2, 1, 3, 2, 1, 1]
cleaner	0.7	0.78102	[1, 0, 1, 0, 0, 0, 2, 1, 2, 0]
clear	1.6	1.2	[2, 1, 1, 0, 3, 1, 2, 4, 2, 0]
cleared	0.4	0.4899	[0, 0, 1, 1, 0, 0, 0, 0, 1, 1]
clearly	1.7	0.78102	[2, 2, 2, 2, 1, 2, 0, 2, 1, 3]
clears	0.3	0.78102	[0, 1, 0, 0, 0, -1, 1, 2, 0, 0]
clever	2.0	0.7746	[2, 1, 2, 2, 2, 1, 3, 1, 3, 3]
cleverer	2.0	0.44721	[2, 2, 2, 3, 2, 2, 1, 2, 2, 2]
cleverest	2.6	0.91652	[4, 3, 2, 2, 4, 3, 2, 1, 2, 3]
cleverish	1.0	1.18322	[1, 1, 1, 1, 1, 1, 2, 1, -2, 3]
cleverly	2.3	0.45826	[2, 3, 2, 2, 2, 3, 2, 2, 3, 2]
cleverness	2.3	0.9	[2, 4, 2, 2, 1, 3, 3, 3, 1, 2]
clevernesses	1.4	0.66332	[1, 1, 1, 2, 2, 2, 2, 0, 2, 1]
clouded	-0.2	0.9798	[-2, 0, 2, 0, 0, -1, 0, -1, 0, 0]
clueless	-1.5	0.5	[-1, -2, -1, -2, -2, -1, -1, -1, -2, -2]
cock	-0.6	1.49666	[0, 0, -4, 0, 0, 1, 0, -3, 0, 0]
cocksucker	-3.1	0.83066	[-3, -4, -2, -2, -4, -4, -4, -2, -3, -3]
cocksuckers	-2.6	1.42829	[-4, -3, -4, -3, -3, 1, -3, -1, -3, -3]
cocky	-0.5	1.0247	[2, 0, -1, 0, -1, -2, 0, -1, -1, -1]
coerced	-1.5	0.67082	[-1, -2, -2, -2, -1, 0, -1, -2, -2, -2]
collapse	-2.2	0.87178	[-3, -1, -2, -4, -3, -2, -2, -2, -1, -2]
collapsed	-1.1	1.64012	[-1, -2, -2, -1, -2, -2, 2, 2, -3, -2]
collapses	-1.2	0.87178	[-2, -1, -2, 0, -2, 0, 0, -2, -1, -2]
collapsing	-1.2	0.6	[-1, -1, -2, -2, -2, -1, 0, -1, -1, -1]
collide	-0.3	1.61555	[-3, 0, -1, 3, 0, -1, -1, 2, -1, -1]
collides	-1.1	1.22066	[-2, -2, -1, -1, 0, -2, 2, -2, -1, -2]
colliding	-0.5	1.36015	[-2, 2, -1, -1, 0, -1, -2, 2, -1, -1]
collision	-1.5	0.67082	[-1, -1, -2, -1, -2, -1, -1, -3, -2, -1]
collisions	-1.1	0.7	[0, -2, -1, 0, -1, -1, -2, -2, -1, -1]
colluding	-1.2	1.32665	[-1, -1, -2, -2, 0, -3, 2, -2, -1, -2]
combat	-1.4	1.0198	[0, -2, 0, -2, -2, -2, -1, -3, 0, -2]
combats	-0.8	1.16619	[0, -2, -3, -1, 0, 0, 0, -1, 1, -2]
comedian	1.6	1.0198	[1, 0, 2, 3, 2, 0, 3, 1, 2, 2]
comedians	1.2	1.16619	[0, 0, 0, 2, 3, 1, 3, 2, 0, 1]
comedic	1.7	0.64031	[2, 1, 1, 3, 1, 2, 2, 2, 1, 2]
comedically	2.1	0.7	[3, 2, 2, 1, 2, 3, 3, 2, 1, 2]
comedienne	0.6	0.66332	[0, 2, 1, 0, 1, 1, 0, 1, 0, 0]
comediennes	1.6	1.11355	[2, 1, 3, 0, 0, 3, 1, 1, 2, 3]
comedies	1.7	1.00499	[0, 2, 1, 3, 3, 3, 1, 1, 2, 1]
comedo	0.3	0.9	[0, 0, 0, 0, -1, 0, 2, 0, 2, 0]
comedones	-0.8	0.9798	[0, 0, 0, -1, 0, -3, 0, -1, -1, -2]
comedown	-0.8	1.07703	[-1, -1, -1, 0, -2, -1, 2, -1, -2, -1]
comedowns	-0.9	0.53852	[-1, -1, -1, -1, 0, -2, 0, -1, -1, -1]
comedy	1.5	0.67082	[1, 1, 2, 1, 3, 2, 1, 1, 2, 1]
comfort	1.5	0.67082	[1, 3, 1, 2, 1, 1, 2, 1, 1, 2]
comfortable	2.3	0.64031	[1, 2, 3, 2, 2, 3, 3, 3, 2, 2]
comfortableness	1.3	1.48661	[4, 2, 2, 3, 1, 1, 1, -1, -1, 1]
comfortably	1.8	0.74833	[1, 2, 2, 1, 3, 3, 1, 2, 1, 2]
comforted	1.8	0.6	[2, 2, 2, 0, 2, 2, 2, 2, 2, 2]
comforter	1.9	0.53852	[2, 3, 2, 2, 2, 1, 2, 2, 2, 1]
comforters	1.2	0.6	[2, 1, 0, 2, 1, 1, 2, 1, 1, 1]
comforting	1.7	0.64031	[1, 2, 1, 1, 2, 2, 2, 3, 2, 1]
comfortingly	1.7	0.45826	[1, 2, 1, 2, 2, 1, 2, 2, 2, 2]
comfortless	-1.8	0.6	[-3, -2, -1, -2, -1, -2, -1, -2, -2, -2]
comforts	2.1	0.7	[3, 1, 3, 1, 2, 2, 3, 2, 2, 2]
commend	1.9	0.7	[1, 2, 2, 1, 2, 1, 2, 3, 2, 3]
commended	1.9	0.9434	[1, 3, 2, 3, 2, 2, 0, 1, 3, 2]
commit	1.2	0.87178	[1, 0, 2, 2, 0, 0, 2, 2, 1, 2]
commitment	1.6	0.91652	[3, 1, 2, 0, 3, 1, 2, 1, 2, 1]
commitments	0.5	0.92195	[1, 3, 0, 0, 0, 0, 0, 0, 0, 1]
commits	0.1	1.13578	[0, -1, 0, 2, 1, -1, 1, 1, -2, 0]
committed	1.1	0.7	[0, 1, 1, 2, 0, 2, 1, 1, 2, 1]
committing	0.3	1.18743	[0, 1, 0, 3, 0, -2, 1, 0, 0, 0]
compassion	2.0	0.7746	[3, 2, 1, 1, 2, 1, 3, 2, 2, 3]
compassionate	2.2	0.87178	[0, 3, 3, 2, 2, 3, 3, 2, 2, 2]
compassionated	1.6	0.66332	[3, 1, 2, 2, 1, 2, 2, 1, 1, 1]
compassionately	1.7	1.41774	[1, 3, 3, 2, 1, 2, 3, 2, -2, 2]
compassionateness	0.9	1.37477	[-3, 2, 2, 1, 1, 1, 2, 1, 1, 1]
compassionates	1.6	0.4899	[2, 1, 2, 2, 1, 2, 2, 1, 1, 2]
compassionating	1.6	0.91652	[3, 0, 2, 1, 3, 1, 2, 1, 2, 1]
compassionless	-2.6	0.8	[-3, -2, -2, -3, -4, -3, -1, -2, -3, -3]
compelled	0.2	1.07703	[-1, 0, 0, 0, 1, 2, -1, -1, 2, 0]
compelling	0.9	0.94339	[1, 1, 1, 0, 1, 2, 2, -1, 2, 0]
competent	1.3	0.78102	[1, 3, 1, 1, 2, 1, 1, 0, 2, 1]
competitive	0.7	0.9	[0, 2, 0, 2, 0, 1, 0, 0, 0, 2]
complacent	-0.3	1.1	[2, -1, -1, 1, -1, -1, -1, 1, -1, -1]
complain	-1.5	0.67082	[-1, -1, -1, -2, -1, -2, -3, -2, -1, -1]
complainant	-0.7	0.78102	[0, 0, -1, 0, 0, -2, 0, -1, -2, -1]
complainants	-1.1	1.3	[-2, -1, 0, -2, -3, -2, -1, 1, -2, 1]
complained	-1.7	0.64031	[-1, -3, -2, -2, -1, -1, -2, -2, -1, -2]
complainer	-1.8	0.4	[-2, -2, -2, -2, -1, -2, -2, -2, -1, -2]
complainers	-1.3	1.00499	[-2, -1, -1, -2, -3, 1, -1, -1, -2, -1]
complaining	-0.8	1.249	[-2, -1, -1, -1, -2, -1, -1, 2, 1, -2]
complainingly	-1.7	0.64031	[-1, -2, -1, -2, -1, -2, -1, -2, -3, -2]
complains	-1.6	0.66332	[-1, -2, -1, -2, -2, -3, -1, -1, -1, -2]
complaint	-1.2	1.249	[-1, -1, -2, -2, -1, -3, -1, -1, 2, -2]
complaints	-1.7	0.45826	[-2, -2, -2, -1, -2, -2, -2, -1, -1, -2]
compliment	2.1	0.7	[2, 2, 3, 1, 2, 3, 3, 1, 2, 2]
complimentarily	1.7	0.45826	[2, 2, 2, 2, 1, 1, 2, 1, 2, 2]
complimentary	1.9	0.7	[1, 2, 2, 1, 2, 1, 2, 3, 3, 2]
complimented	1.8	1.4	[3, 2, 2, 2, 3, 2, -2, 3, 1, 2]
complimenting	2.3	0.64031	[2, 2, 1, 3, 3, 2, 2, 3, 3, 2]
compliments	1.7	0.45826	[2, 1, 2, 2, 1, 2, 2, 1, 2, 2]
comprehensive	1.0	0.63246	[1, 1, 1, 0, 2, 2, 1, 0, 1, 1]
conciliate	1.0	1.18322	[2, 1, 1, 0, 2, 2, -2, 2, 1, 1]
conciliated	1.1	0.9434	[1, 3, 0, 0, 2, 0, 2, 1, 1, 1]
conciliates	1.1	0.9434	[1, 3, 0, 0, 2, 0, 2, 1, 1, 1]
conciliating	1.3	0.78102	[2, 2, 1, 1, 2, 0, 2, 1, 2, 0]
condemn	-1.6	1.0198	[-2, -2, -1, -2, -2, 1, -2, -3, -1, -2]
condemnation	-2.8	0.9798	[-3, -4, -2, -4, -2, -4, -2, -1, -3, -3]
condemned	-1.9	1.81384	[2, -2, -2, -3, -2, -3, -3, -4, 1, -3]
condemns	-2.3	0.64031	[-2, -2, -3, -3, -3, -2, -2, -3, -1, -2]
confidence	2.3	0.64031	[3, 3, 3, 3, 1, 2, 2, 2, 2, 2]
confident	2.2	0.87178	[1, 3, 3, 2, 3, 1, 3, 2, 1, 3]
confidently	2.1	0.53852	[2, 2, 3, 1, 2, 2, 2, 2, 3, 2]
conflict	-1.3	0.45826	[-2, -2, -1, -2, -1, -1, -1, -1, -1, -1]
conflicting	-1.7	0.64031	[-1, -2, -3, -1, -2, -1, -2, -1, -2, -2]
conflictive	-1.8	0.6	[-2, -2, -2, -2, -1, -1, -3, -2, -1, -2]
conflicts	-1.6	0.8	[-1, -1, -1, -2, -1, -1, -3, -2, -1, -3]
confront	-0.7	0.78102	[0, -1, -1, 0, 1, -1, -1, -2, -1, -1]
confrontation	-1.3	1.55242	[-3, -2, -3, -2, -1, -2, 1, 2, -1, -2]
confrontational	-1.6	0.66332	[-1, -2, -1, -2, -1, -2, -1, -2, -3, -1]
confrontationist	-1.0	1.34164	[-1, -2, -2, 2, 1, -1, -2, -1, -2, -2]
confrontationists	-1.2	1.46969	[-2, -3, -1, -2, 2, 1, -1, -2, -2, -2]
confrontations	-1.5	1.0247	[-1, -3, -2, -1, -3, 0, -2, -2, 0, -1]
confronted	-0.8	0.74833	[-1, -2, -1, -1, -1, -1, -1, -1, 1, 0]
confronter	-0.3	1.1	[0, -2, 1, -2, 0, -1, -1, 0, 1, 1]
confronters	-1.3	1.26886	[-3, -2, -2, -2, -1, -2, 1, 1, -1, -2]
confronting	-0.6	1.11355	[-2, -1, -1, 0, -2, 2, -1, 0, 0, -1]
confronts	-0.9	0.53852	[-1, -1, -1, -1, 0, -1, 0, -1, -2, -1]
confuse	-0.9	0.3	[-1, -1, -1, -1, -1, -1, -1, -1, 0, -1]
confused	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -1, -1, -2, -2]
confusedly	-0.6	1.42829	[-3, -1, -1, 1, -1, -1, -2, 2, 1, -1]
confusedness	-1.5	0.67082	[-3, -1, -1, -2, -1, -2, -2, -1, -1, -1]
confuses	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -1, -1, -2, -2]
confusing	-0.9	0.7	[-1, -1, -1, -2, -1, -1, 1, -1, -1, -1]
confusingly	-1.4	0.66332	[-1, -1, -1, -1, -3, -1, -2, -1, -1, -2]
confusion	-1.2	0.6	[-1, -1, -1, -1, -1, -2, 0, -1, -2, -2]
confusional	-1.2	0.6	[-2, -2, 0, -1, -2, -1, -1, -1, -1, -1]
confusions	-0.9	1.04403	[-1, -2, -1, -1, -1, 2, -2, -1, -1, -1]
congrats	2.4	0.91652	[1, 3, 3, 3, 2, 2, 4, 1, 2, 3]
congratulate	2.2	1.249	[3, 3, 2, 3, -1, 2, 1, 3, 3, 3]
congratulation	2.9	0.9434	[3, 3, 3, 2, 4, 2, 4, 3, 4, 1]
congratulations	2.9	0.53852	[2, 3, 3, 3, 3, 3, 3, 3, 4, 2]
consent	0.9	0.7	[0, 0, 1, 1, 1, 2, 1, 2, 0, 1]
consents	1.0	0.63246	[0, 2, 1, 0, 1, 1, 1, 2, 1, 1]
considerate	1.9	1.22066	[2, -1, 3, 2, 2, 2, 4, 1, 2, 2]
consolable	1.1	0.53852	[1, 1, 2, 1, 0, 1, 1, 1, 2, 1]
conspiracy	-2.4	0.66332	[-2, -2, -3, -3, -2, -2, -4, -2, -2, -2]
constrained	-0.4	1.0198	[-1, 0, -1, -1, -1, -1, 2, -1, 1, -1]
contagion	-2.0	1.18322	[-1, 0, -2, -1, -2, -4, -2, -4, -2, -2]
contagions	-1.5	0.92195	[-2, -2, -2, -2, 1, -2, -1, -1, -2, -2]
contagious	-1.4	0.91652	[-2, -2, -2, 0, -1, -1, -2, 0, -1, -3]
contempt	-2.8	0.6	[-3, -3, -4, -2, -3, -2, -3, -3, -2, -3]
contemptibilities	-2.0	1.09545	[-2, 1, -2, -2, -3, -3, -3, -2, -2, -2]
contemptibility	-0.9	1.51327	[-2, -1, -3, -1, 0, -3, -1, 1, 2, -1]
contemptible	-1.6	1.68523	[-2, -2, -3, -3, -2, -3, -1, -3, 1, 2]
contemptibleness	-1.9	0.7	[-2, -2, -1, -2, -2, -1, -1, -2, -3, -3]
contemptibly	-1.4	1.49666	[-3, 0, -2, -3, -2, -2, -1, -3, 1, 1]
contempts	-1.0	1.48324	[-2, -2, -1, 0, 1, -1, -2, -3, 2, -2]
contemptuous	-2.2	1.83303	[-2, -3, -4, -2, -3, -3, 3, -2, -3, -3]
contemptuously	-2.4	0.8	[-1, -3, -2, -3, -3, -2, -1, -3, -3, -3]
contemptuousness	-1.1	1.57797	[-3, -1, -2, 3, -2, 0, -1, -1, -2, -2]
contend	0.2	1.07703	[0, 0, 1, 1, -2, 1, -1, 0, 2, 0]
contender	0.5	1.0247	[1, 3, -1, 0, 0, 1, 0, 1, 0, 0]
contented	1.4	1.56205	[1, 1, 3, 2, 2, 2, -3, 2, 2, 2]
contentedly	1.9	0.9434	[3, 1, 2, 3, 2, 3, 0, 2, 1, 2]
contentedness	1.4	0.4899	[1, 2, 1, 1, 2, 2, 2, 1, 1, 1]
contentious	-1.2	1.4	[-2, -2, -2, -2, -2, -2, -1, 2, 1, -2]
contentment	1.5	1.74642	[2, 1, 3, 2, 2, 1, 2, 4, 1, -3]
contestable	0.6	1.0198	[2, -1, 1, 0, 1, -1, 1, 1, 2, 0]
contradict	-1.3	0.78102	[-2, -1, -1, 0, -2, 0, -1, -2, -2, -2]
contradictable	-1.0	0.63246	[-1, -1, -2, 0, -1, -1, 0, -1, -1, -2]
contradicted	-1.3	0.45826	[-1, -1, -1, -1, -2, -1, -2, -1, -1, -2]
contradicting	-1.3	0.9	[-2, -2, -2, -1, -2, -1, 1, -1, -1, -2]
contradiction	-1.0	0.89443	[-1, 0, 0, -1, 0, -3, -2, -1, -1, -1]
contradictions	-1.3	0.78102	[0, -2, -1, -2, -1, -2, -1, 0, -2, -2]
contradictious	-1.9	1.04403	[-2, -3, 0, -3, -3, -1, -2, -1, -1, -3]
contradictor	-1.0	0.63246	[-1, -1, -1, -2, -1, -2, 0, 0, -1, -1]
contradictories	-0.5	1.11803	[-1, 0, -1, -2, 2, 1, -1, -1, -1, -1]
contradictorily	-0.9	1.3	[0, -1, -1, -1, -3, -1, 1, 1, -3, -1]
contradictoriness	-1.4	0.4899	[-2, -1, -1, -1, -2, -1, -1, -2, -2, -1]
contradictors	-1.6	0.66332	[-1, -2, -1, -2, -1, -1, -3, -1, -2, -2]
contradictory	-1.4	0.4899	[-1, -2, -1, -2, -1, -1, -1, -1, -2, -2]
contradicts	-1.4	0.66332	[-1, -2, -1, -2, -2, -2, 0, -1, -1, -2]
controversial	-0.8	0.87178	[0, 0, -1, 1, -2, -1, -1, -1, -1, -2]
controversially	-1.1	1.04403	[0, -1, -1, -2, -1, -2, -1, 1, -3, -1]
convince	1.0	0.89443	[-1, 2, 2, 1, 1, 0, 1, 1, 2, 1]
convinced	1.7	0.64031	[2, 1, 1, 2, 1, 2, 2, 2, 1, 3]
convincer	0.6	0.66332	[2, 0, 1, 0, 1, 1, 0, 0, 0, 1]
convincers	0.3	0.64031	[0, 0, 0, 0, 0, 1, 0, 0, 0, 2]
convinces	0.7	0.78102	[1, 0, 2, 0, 1, 0, 0, 0, 1, 2]
convincing	1.7	0.9	[2, 2, 0, 3, 3, 1, 1, 2, 2, 1]
convincingly	1.6	0.66332	[3, 2, 1, 1, 1, 2, 2, 1, 2, 1]
convincingness	0.7	1.34536	[0, 0, 0, 1, -2, 2, 0, 3, 1, 2]
convivial	1.2	1.16619	[1, 2, -2, 2, 1, 2, 2, 1, 2, 1]
cool	1.3	0.64031	[1, 1, 2, 1, 1, 1, 2, 2, 2, 0]
cornered	-1.1	0.83066	[-1, -1, -3, -1, 0, -1, 0, -1, -2, -1]
corpse	-2.7	1.18743	[-3, -4, 0, -4, -3, -3, -3, -1, -3, -3]
costly	-0.4	1.0198	[-1, 0, -1, -1, 2, -1, -1, 1, -1, -1]
courage	2.2	0.74833	[2, 3, 1, 2, 3, 2, 3, 3, 1, 2]
courageous	2.4	0.4899	[2, 3, 3, 3, 3, 2, 2, 2, 2, 2]
courageously	2.3	0.78102	[3, 3, 3, 1, 3, 3, 2, 2, 2, 1]
courageousness	2.1	0.7	[3, 3, 1, 2, 1, 2, 2, 2, 3, 2]
courteous	2.3	0.45826	[2, 2, 2, 3, 3, 2, 3, 2, 2, 2]
courtesy	1.5	0.67082	[1, 1, 2, 3, 1, 2, 1, 1, 2, 1]
cover-up	-1.2	1.16619	[-1, -1, -4, -1, 0, 0, -2, 0, -1, -2]
coward	-2.0	0.63246	[-3, -3, -1, -2, -2, -1, -2, -2, -2, -2]
cowardly	-1.6	0.8	[-1, -1, -1, -3, -1, -2, -3, -2, -1, -1]
coziness	1.5	1.11803	[2, 3, 1, 3, 1, 2, 2, -1, 1, 1]
cramp	-0.8	1.66132	[0, -1, -1, -3, -3, 1, -2, 1, -2, 2]
crap	-1.6	0.66332	[-1, -1, -2, -2, -1, -2, -3, -1, -1, -2]
crappy	-2.6	0.8	[-1, -3, -3, -2, -3, -4, -2, -2, -3, -3]
crash	-1.7	1.18743	[-2, -3, -2, -1, -2, 0, 0, -1, -4, -2]
craze	-0.6	1.49666	[0, -3, -1, -1, -2, 0, 0, -1, 3, -1]
crazed	-0.5	2.24722	[-2, -1, 3, -3, -3, 1, 1, -1, 3, -3]
crazes	0.2	1.6	[-2, -1, 3, -1, -1, 1, 2, 0, 2, -1]
crazier	-0.1	1.7	[2, -2, -2, 0, -1, 1, 3, 1, -1, -2]
craziest	-0.2	2.13542	[2, -2, -3, 3, -2, 0, 2, 2, -2, -2]
crazily	-1.5	0.67082	[-2, -1, -1, -2, -1, -3, -2, -1, -1, -1]
craziness	-1.6	0.66332	[-2, 0, -2, -1, -2, -1, -2, -2, -2, -2]
crazinesses	-1.0	1.48324	[1, -2, -2, 2, -1, 0, -2, -1, -3, -2]
crazing	-0.5	0.80623	[0, 0, 0, 0, -2, -1, -2, 0, 0, 0]
crazy	-1.4	1.35647	[-2, -1, -1, -2, -3, -2, -3, -1, 2, -1]
crazyweed	0.8	0.9798	[2, 0, 0, 0, 0, 2, 2, 0, 2, 0]
create	1.1	1.13578	[1, 1, 1, 2, 3, 0, 0, 0, 3, 0]
created	1.0	0.7746	[2, 0, 0, 1, 1, 2, 0, 1, 1, 2]
creates	1.1	0.83066	[2, 0, 0, 1, 1, 2, 0, 1, 2, 2]
creatin	0.1	0.53852	[0, 0, 0, 1, 0, 0, 0, 1, -1, 0]
creatine	0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, 0, 2]
creating	1.2	1.249	[0, 0, 0, 0, 4, 1, 1, 2, 2, 2]
creatinine	0.4	1.2	[0, 0, 0, 4, 0, 0, 0, 0, 0, 0]
creation	1.1	0.83066	[2, 1, 1, 2, 0, 0, 0, 1, 2, 2]
creationism	0.7	0.9	[2, 0, 0, 0, 0, 0, 2, 0, 2, 1]
creationisms	1.1	1.37477	[2, 4, 0, 3, 0, 0, 1, 1, 0, 0]
creationist	0.8	0.9798	[2, 0, 0, 0, 0, 0, 2, 0, 2, 2]
creationists	0.5	0.67082	[0, 0, 0, 1, 1, 0, 2, 0, 1, 0]
creations	1.6	0.91652	[3, 2, 1, 1, 1, 3, 1, 2, 2, 0]
creative	1.9	0.53852	[2, 1, 2, 2, 2, 1, 3, 2, 2, 2]
creatively	1.5	0.80623	[2, 2, 1, 0, 2, 2, 2, 0, 2, 2]
creativeness	1.8	1.07703	[3, 2, 1, 0, 2, 2, 2, 0, 3, 3]
creativities	1.7	1.00499	[2, 2, 1, 0, 3, 2, 2, 0, 2, 3]
creativity	1.6	0.8	[2, 1, 2, 2, 3, 2, 0, 2, 1, 1]
credit	1.6	0.91652	[1, 1, 0, 2, 3, 3, 2, 2, 1, 1]
creditabilities	1.4	1.28062	[0, 3, -1, 2, 2, 2, 1, 2, 0, 3]
creditability	1.9	1.3	[3, 0, 4, 0, 1, 1, 3, 2, 2, 3]
creditable	1.8	0.6	[2, 1, 1, 1, 2, 2, 3, 2, 2, 2]
creditableness	1.2	0.74833	[1, 1, 1, 3, 2, 1, 0, 1, 1, 1]
creditably	1.7	0.78102	[3, 1, 1, 0, 2, 2, 2, 2, 2, 2]
credited	1.5	1.11803	[1, 1, 1, 0, 3, 2, 0, 3, 1, 3]
crediting	0.6	0.4899	[1, 0, 0, 0, 1, 1, 1, 0, 1, 1]
creditor	-0.1	1.44568	[-2, -1, 1, -2, -1, 1, 2, -1, 2, 0]
credits	1.5	1.0247	[2, -1, 2, 0, 2, 2, 2, 2, 2, 2]
creditworthiness	1.9	1.3	[4, 0, 3, 1, 2, 1, 4, 2, 1, 1]
creditworthy	2.4	0.66332	[3, 3, 3, 1, 2, 3, 3, 2, 2, 2]
crestfallen	-2.5	0.67082	[-2, -3, -3, -2, -3, -2, -4, -2, -2, -2]
cried	-1.6	0.8	[-1, -3, -1, -2, -2, -2, 0, -2, -1, -2]
cries	-1.7	0.64031	[-1, -3, -1, -2, -2, -2, -1, -2, -1, -2]
crime	-2.5	0.80623	[-3, -3, -4, -2, -2, -1, -2, -2, -3, -3]
criminal	-2.4	0.91652	[-3, -2, -4, -3, -1, -2, -3, -2, -1, -3]
criminals	-2.7	0.9	[-2, -2, -1, -3, -3, -4, -4, -3, -2, -3]
crisis	-3.1	0.7	[-3, -2, -4, -3, -3, -2, -4, -4, -3, -3]
critic	-1.1	0.53852	[-1, 0, -2, -1, -1, -1, -1, -1, -2, -1]
critical	-1.3	0.78102	[-2, 0, -1, -1, -1, -2, -1, -3, -1, -1]
criticise	-1.9	0.53852	[-2, -1, -2, -2, -2, -2, -3, -1, -2, -2]
criticised	-1.8	0.4	[-2, -2, -1, -2, -2, -2, -1, -2, -2, -2]
criticises	-1.3	1.26886	[-2, -1, -1, -1, 2, -1, -3, -2, -2, -2]
criticising	-1.7	0.78102	[-1, -1, -1, -3, -1, -2, -2, -2, -1, -3]
criticism	-1.9	0.53852	[-2, -1, -2, -2, -2, -2, -3, -1, -2, -2]
criticisms	-0.9	1.37477	[-1, -1, 1, -1, -2, -2, -1, -3, 2, -1]
criticizable	-1.0	0.63246	[-1, -2, 0, -1, -2, -1, 0, -1, -1, -1]
criticize	-1.6	1.0198	[-2, -1, 0, -2, -1, -3, 0, -3, -2, -2]
criticized	-1.5	0.92195	[-1, -2, -3, -1, -1, -1, -3, -1, -2, 0]
criticizer	-1.5	0.67082	[-1, -2, -2, -1, -1, -1, -3, -1, -2, -1]
criticizers	-1.6	0.4899	[-1, -1, -1, -2, -2, -2, -1, -2, -2, -2]
criticizes	-1.4	0.66332	[-1, -1, -2, -1, -1, -1, -3, -1, -2, -1]
criticizing	-1.5	0.67082	[-1, -1, -1, -2, -2, -1, -2, -1, -3, -1]
critics	-1.2	0.6	[-2, 0, -1, -1, -1, -1, -2, -1, -2, -1]
crude	-2.7	0.64031	[-2, -2, -3, -4, -2, -3, -3, -2, -3, -3]
crudely	-1.2	0.4	[-1, -2, -2, -1, -1, -1, -1, -1, -1, -1]
crudeness	-2.0	0.7746	[-3, -1, -1, -1, -3, -2, -3, -2, -2, -2]
crudenesses	-2.0	1.0	[-3, -1, -1, -2, -4, -1, -1, -2, -2, -3]
cruder	-2.0	0.89443	[-3, -2, -2, -1, -1, -2, -2, -1, -4, -2]
crudes	-1.1	0.83066	[-2, -1, -1, -1, 0, 0, -1, -1, -3, -1]
crudest	-2.4	1.0198	[-2, -1, -3, -3, -2, -1, -4, -4, -2, -2]
cruel	-2.8	1.16619	[-2, -4, -1, -4, -1, -4, -2, -3, -3, -4]
crueler	-2.3	0.45826	[-2, -3, -2, -2, -2, -3, -2, -2, -3, -2]
cruelest	-2.6	0.8	[-4, -3, -2, -4, -2, -2, -2, -2, -2, -3]
crueller	-2.4	0.4899	[-2, -3, -3, -3, -2, -2, -3, -2, -2, -2]
cruellest	-2.9	1.04403	[-2, -3, -4, -4, -4, -3, -1, -2, -2, -4]
cruelly	-2.8	0.4	[-3, -3, -3, -3, -3, -3, -2, -2, -3, -3]
cruelness	-2.9	0.3	[-3, -3, -3, -3, -3, -3, -3, -3, -2, -3]
cruelties	-2.3	1.00499	[-4, -3, -2, -1, -2, -2, -1, -4, -2, -2]
cruelty	-2.9	0.83066	[-3, -3, -3, -3, -4, -3, -1, -3, -4, -2]
crush	-0.6	1.11355	[0, 0, 0, -1, 1, 0, -2, -3, -1, 0]
crushed	-1.8	0.6	[-2, -2, -2, -1, -2, -1, -1, -3, -2, -2]
crushes	-1.9	0.53852	[-2, -2, -2, -2, -2, -1, -1, -3, -2, -2]
crushing	-1.5	1.85742	[-2, -3, -1, -3, 2, -2, -2, -3, 2, -3]
cry	-2.1	0.53852	[-2, -2, -2, -1, -2, -2, -3, -3, -2, -2]
crying	-2.1	0.7	[-3, -1, -2, -2, -3, -2, -3, -2, -1, -2]
cunt	-2.2	2.08806	[-4, -1, -4, -2, 2, -4, -4, -3, 1, -3]
cunts	-2.9	1.44568	[-3, -4, -3, -4, -4, -4, -3, 1, -3, -2]
curious	1.3	0.78102	[0, 1, 0, 2, 2, 2, 1, 1, 2, 2]
curse	-2.5	0.67082	[-3, -3, -4, -2, -2, -3, -2, -2, -2, -2]
cut	-1.1	0.53852	[-2, -1, -1, -1, 0, -1, -1, -1, -2, -1]
cute	2.0	0.63246	[1, 3, 1, 2, 2, 2, 2, 3, 2, 2]
cutely	1.3	1.00499	[3, 1, 1, 2, 2, 1, 1, 2, -1, 1]
cuteness	2.3	0.45826	[2, 2, 2, 3, 2, 3, 3, 2, 2, 2]
cutenesses	1.9	0.53852	[1, 2, 2, 1, 3, 2, 2, 2, 2, 2]
cuter	2.3	0.9	[1, 2, 3, 4, 1, 2, 2, 3, 3, 2]
cutes	1.8	0.87178	[1, 2, 3, 0, 1, 2, 2, 2, 3, 2]
cutesie	1.0	1.18322	[2, 2, 1, 1, 0, 2, 2, -1, 2, -1]
cutesier	1.5	1.20416	[3, -1, 2, 1, 1, 2, 2, 3, 0, 2]
cutesiest	2.2	1.4	[3, 3, 4, 2, 2, 4, 2, -1, 2, 1]
cutest	2.8	0.87178	[2, 3, 3, 4, 4, 3, 1, 3, 2, 3]
cutesy	2.1	0.83066	[2, 1, 2, 2, 4, 3, 1, 2, 2, 2]
cutey	2.1	1.04403	[1, 2, 3, 4, 1, 1, 3, 2, 3, 1]
cuteys	1.5	1.0247	[3, 2, 2, 0, 1, 2, 1, 1, 3, 0]
cutie	1.5	0.80623	[3, 1, 1, 2, 1, 1, 1, 3, 1, 1]
cutiepie	2.0	1.09545	[3, 1, 2, 4, 0, 2, 1, 3, 2, 2]
cuties	2.2	0.6	[3, 2, 2, 2, 3, 1, 2, 2, 3, 2]
cuts	-1.2	0.6	[-1, -2, -1, 0, -1, -1, -2, -2, -1, -1]
cutting	-0.5	0.67082	[0, -1, 0, 0, 0, -1, -1, -2, 0, 0]
cynic	-1.4	0.8	[-1, -2, -1, 0, -2, -3, -1, -1, -1, -2]
cynical	-1.6	0.66332	[-1, -1, -2, -2, -1, -1, -2, -2, -1, -3]
cynically	-1.3	1.00499	[-2, -1, -1, -1, -3, -2, -1, -2, 1, -1]
cynicism	-1.7	0.64031	[-2, -3, -1, -1, -2, -1, -2, -1, -2, -2]
cynicisms	-1.7	0.78102	[-1, -1, -3, -1, -2, -3, -1, -2, -2, -1]
cynics	-0.3	1.1	[-1, 0, -2, 0, 0, -1, 1, -1, 2, -1]
d-:	1.6	0.66332	[1, 1, 1, 2, 2, 3, 1, 1, 2, 2]
d:	1.2	0.87178	[1, 1, 1, 2, 1, 1, 2, 2, -1, 2]
d=	1.5	0.67082	[1, 1, 1, 2, 3, 2, 1, 1, 1, 2]
damage	-2.2	0.4	[-2, -3, -2, -2, -3, -2, -2, -2, -2, -2]
damaged	-1.9	0.53852	[-2, -2, -2, -1, -2, -1, -2, -2, -3, -2]
damager	-1.9	0.53852	[-2, -2, -2, -2, -2, -1, -2, -2, -3, -1]
damagers	-2.0	0.63246	[-2, -3, -1, -2, -2, -1, -3, -2, -2, -2]
damages	-1.9	1.04403	[-1, 0, -2, -2, -2, -4, -2, -1, -3, -2]
damaging	-2.3	0.9	[-4, -1, -2, -4, -2, -2, -2, -2, -2, -2]
damagingly	-2.0	0.7746	[-2, -2, -2, -2, -1, -3, -3, -1, -3, -1]
damn	-1.7	0.64031	[-2, -1, -2, -1, -1, -3, -1, -2, -2, -2]
damnable	-1.7	0.45826	[-2, -1, -2, -2, -1, -2, -1, -2, -2, -2]
damnableness	-1.8	0.74833	[-2, -2, -2, -2, -1, -2, -2, 0, -3, -2]
damnably	-1.7	0.45826	[-2, -1, -2, -2, -1, -2, -1, -2, -2, -2]
damnation	-2.6	1.0198	[-3, -2, -3, -4, -4, -3, -1, -2, -1, -3]
damnations	-1.4	1.11355	[-2, -1, -2, -3, -2, 0, -1, 1, -2, -2]
damnatory	-2.6	1.42829	[-4, -1, -4, -2, -1, -3, -3, 0, -4, -4]
damned	-1.6	0.66332	[-1, -1, -3, -1, -2, -2, -1, -2, -1, -2]
damnedest	-0.5	1.5	[1, 0, 0, 0, -1, -1, 0, 2, -3, -3]
damnified	-2.8	0.9798	[-1, -3, -3, -2, -2, -3, -4, -4, -2, -4]
damnifies	-1.8	0.87178	[-2, -2, -1, -2, -3, -2, -2, 0, -1, -3]
damnify	-2.2	0.74833	[-2, -2, -4, -2, -3, -2, -1, -2, -2, -2]
damnifying	-2.4	0.66332	[-3, -1, -2, -2, -2, -2, -3, -3, -3, -3]
damning	-1.4	0.8	[-1, -3, -1, -1, -1, -1, -3, -1, -1, -1]
damningly	-2.0	1.61245	[-3, -2, -1, -4, -3, -3, -3, -2, 2, -1]
damnit	-2.4	1.0198	[-3, -3, -2, -2, -3, -2, -4, -2, 0, -3]
damns	-2.2	0.74833	[-2, -3, -2, -1, -3, -2, -1, -2, -3, -3]
danger	-2.4	0.91652	[-3, -1, -2, -3, -3, -3, -2, -1, -2, -4]
dangered	-2.4	0.66332	[-3, -3, -2, -3, -3, -2, -1, -2, -3, -2]
dangering	-2.5	0.80623	[-1, -2, -2, -3, -4, -3, -3, -2, -2, -3]
dangerous	-2.1	0.3	[-2, -3, -2, -2, -2, -2, -2, -2, -2, -2]
dangerously	-2.0	0.44721	[-2, -2, -2, -1, -2, -3, -2, -2, -2, -2]
dangerousness	-2.0	0.44721	[-2, -3, -2, -1, -2, -2, -2, -2, -2, -2]
dangers	-2.2	0.87178	[-1, -1, -2, -4, -2, -3, -3, -2, -2, -2]
daredevil	0.5	0.92195	[0, 1, -1, 2, 0, 0, 1, 0, 0, 2]
daring	1.5	1.5	[3, 0, 1, 1, 2, -2, 3, 2, 2, 3]
daringly	2.1	0.7	[3, 1, 2, 1, 3, 2, 2, 3, 2, 2]
daringness	1.4	0.8	[0, 2, 1, 3, 1, 2, 2, 1, 1, 1]
darings	0.4	0.91652	[0, 1, 1, 1, 0, 1, 0, 1, -2, 1]
darkest	-2.2	0.6	[-3, -3, -2, -2, -3, -2, -1, -2, -2, -2]
darkness	-1.0	0.44721	[-2, -1, -1, -1, -1, 0, -1, -1, -1, -1]
darling	2.8	0.6	[3, 3, 2, 2, 3, 3, 4, 2, 3, 3]
darlingly	1.6	0.66332	[1, 1, 2, 3, 1, 1, 2, 2, 2, 1]
darlingness	2.3	0.45826	[3, 3, 2, 2, 2, 2, 2, 2, 3, 2]
darlings	2.2	0.4	[3, 2, 2, 2, 2, 2, 2, 2, 3, 2]
dauntless	2.3	0.78102	[3, 3, 2, 1, 3, 3, 2, 1, 2, 3]
daze	-0.7	0.78102	[-1, 0, -1, -1, 1, -1, -1, 0, -1, -2]
dazed	-0.7	0.64031	[0, -1, -1, 0, -1, -1, 0, -1, 0, -2]
dazedly	-0.4	1.0198	[-1, -1, -1, -1, -1, 2, -1, 1, 0, -1]
dazedness	-0.5	1.11803	[-1, 2, 0, -1, -1, -1, 1, -2, -1, -1]
dazes	-0.3	0.78102	[0, -1, 0, 0, 0, -1, 0, 1, 0, -2]
dead	-3.3	1.00499	[-4, -4, -1, -4, -4, -3, -4, -2, -3, -4]
deadlock	-1.4	0.8	[-2, -2, -1, -3, -1, 0, -2, -1, -1, -1]
deafening	-1.2	1.6	[-4, -2, 0, -3, 1, -2, 1, -2, -1, 0]
dear	1.6	1.35647	[3, 3, 2, 2, 2, 2, 1, -2, 1, 2]
dearer	1.9	0.7	[2, 2, 1, 2, 1, 1, 2, 3, 2, 3]
dearest	2.6	0.8	[1, 2, 3, 3, 2, 2, 3, 3, 4, 3]
dearie	2.2	0.6	[2, 2, 2, 3, 2, 1, 3, 2, 3, 2]
dearies	1.0	1.0	[0, 1, 2, -1, 1, 1, 1, 1, 1, 3]
dearly	1.8	1.07703	[3, 4, 2, 1, 1, 0, 1, 2, 2, 2]
dearness	2.0	0.7746	[1, 1, 2, 3, 2, 2, 2, 3, 1, 3]
dears	1.9	0.83066	[3, 2, 2, 2, 1, 2, 2, 2, 0, 3]
dearth	-2.3	1.00499	[-2, -2, -1, -4, -2, -1, -2, -4, -3, -2]
dearths	-0.9	0.7	[0, -1, 0, -1, -2, -1, -2, 0, -1, -1]
deary	1.9	0.83066	[3, 2, 2, 1, 2, 2, 3, 2, 0, 2]
death	-2.9	1.04403	[-3, -4, -4, -3, -3, -1, -1, -4, -3, -3]
debonair	0.8	1.72047	[1, -1, -1, -3, 2, 2, 2, 2, 2, 2]
debt	-1.5	1.0247	[-2, -1, -2, -3, 1, -2, -2, -1, -1, -2]
decay	-1.7	0.45826	[-2, -2, -2, -1, -2, -1, -1, -2, -2, -2]
decayed	-1.6	0.91652	[-2, -2, -2, -2, -2, 1, -1, -2, -2, -2]
decayer	-1.6	0.4899	[-2, -2, -2, -1, -2, -1, -1, -1, -2, -2]
decayers	-1.6	0.4899	[-2, -1, -1, -2, -1, -2, -2, -2, -1, -2]
decaying	-1.7	0.64031	[-1, -1, -2, -2, -1, -2, -3, -2, -2, -1]
decays	-1.7	0.45826	[-2, -1, -2, -2, -1, -2, -1, -2, -2, -2]
deceit	-2.0	1.34164	[-3, -2, -2, -3, -3, 0, 1, -2, -3, -3]
deceitful	-1.9	1.22066	[-3, -2, -2, -3, -1, -3, -1, -3, -2, 1]
deceive	-1.7	0.64031	[-1, -2, -2, -1, -2, -1, -1, -3, -2, -2]
deceived	-1.9	1.3	[1, -3, -3, -1, -1, -2, -2, -2, -4, -2]
deceives	-1.6	1.56205	[-1, 2, -2, -2, -3, -2, 0, -2, -4, -2]
deceiving	-1.4	1.68523	[-3, -2, -2, -2, -1, -1, 1, -4, 2, -2]
deception	-1.9	1.04403	[-1, -2, -2, -4, -2, 0, -2, -1, -3, -2]
decisive	0.9	0.83066	[2, 2, 0, 1, -1, 1, 1, 1, 1, 1]
dedicated	2.0	0.44721	[2, 2, 2, 2, 2, 2, 2, 1, 3, 2]
defeat	-2.0	1.67332	[0, -4, -3, -4, -2, -1, -2, -1, 1, -4]
defeated	-2.1	0.83066	[-1, -2, -1, -2, -3, -2, -2, -2, -2, -4]
defeater	-1.4	0.8	[-2, -1, -1, -3, -2, -1, -2, -1, 0, -1]
defeaters	-0.9	0.9434	[-2, 0, -1, -2, -2, -1, 0, -1, 1, -1]
defeating	-1.6	0.66332	[-2, -2, -1, -1, -1, -1, -2, -2, -3, -1]
defeatism	-1.3	1.00499	[-1, -2, -2, -1, -1, -3, 1, -2, -1, -1]
defeatist	-1.7	0.9	[0, -1, -2, -3, -1, -2, -1, -3, -2, -2]
defeatists	-2.1	0.9434	[-3, -2, -1, -2, -2, -1, -3, -4, -1, -2]
defeats	-1.3	1.1	[-1, 0, -1, -2, -1, -2, -3, 1, -2, -2]
defeature	-1.9	1.22066	[1, -2, -2, -2, -2, -1, -2, -3, -4, -2]
defeatures	-1.5	1.20416	[-3, -2, -3, 0, -1, -1, -2, 0, -3, 0]
defect	-1.4	0.8	[-2, -1, -2, -1, 0, -2, -3, -1, -1, -1]
defected	-1.7	0.64031	[-3, -2, -2, -2, -1, -1, -1, -2, -1, -2]
defecting	-1.8	0.6	[-2, -1, -2, -2, -2, -1, -3, -1, -2, -2]
defection	-1.4	0.66332	[-1, -2, -1, -2, 0, -1, -2, -2, -2, -1]
defections	-1.5	0.67082	[-2, -2, -2, -1, 0, -1, -2, -2, -1, -2]
defective	-1.9	0.53852	[-2, -1, -2, -3, -2, -2, -2, -2, -1, -2]
defectively	-2.1	0.83066	[-1, -3, -2, -1, -3, -2, -1, -3, -2, -3]
defectiveness	-1.8	0.74833	[-1, -1, -2, -2, -2, -3, -1, -1, -2, -3]
defectives	-1.8	0.74833	[-3, -3, -1, -2, -2, -1, -2, -1, -1, -2]
defector	-1.9	0.53852	[-2, -3, -2, -2, -1, -2, -1, -2, -2, -2]
defectors	-1.3	1.26886	[-2, -1, -2, 0, -1, -1, -2, -4, 1, -1]
defects	-1.7	0.9	[-1, -2, -1, 0, -2, -3, -2, -2, -1, -3]
defence	0.4	0.91652	[0, 0, 0, 1, 0, 0, 0, 2, 2, -1]
defenceman	0.4	1.11355	[3, 0, 0, 0, 0, -1, 2, 0, 0, 0]
defencemen	0.6	0.91652	[0, 0, 0, 2, 0, 2, 0, 2, 0, 0]
defences	-0.2	1.16619	[0, 0, 0, 1, -2, -1, 0, -2, 0, 2]
defender	0.4	1.0198	[0, 0, 2, 1, -1, 2, 0, 1, -1, 0]
defenders	0.3	0.78102	[0, 1, 1, 0, 0, 0, 0, -1, 2, 0]
defense	0.5	0.67082	[0, 1, 0, 1, 2, 1, 0, 0, 0, 0]
defenseless	-1.4	0.8	[0, -1, -1, -2, -1, -1, -3, -1, -2, -2]
defenselessly	-1.1	0.9434	[-2, -2, -2, -1, 0, -1, 1, -1, -1, -2]
defenselessness	-1.3	1.18743	[-3, -1, -3, 0, -2, -1, -1, -2, 1, -1]
defenseman	0.1	1.13578	[2, 0, 0, 0, 0, 0, -1, 0, 2, -2]
defensemen	-0.4	0.66332	[0, 0, -2, 0, -1, -1, 0, 0, 0, 0]
defenses	0.7	1.41774	[3, 0, 2, 0, -1, 1, -2, 1, 1, 2]
defensibility	0.4	1.56205	[1, -2, 1, 0, 4, 0, -1, 1, -1, 1]
defensible	0.8	0.87178	[0, 2, 0, 0, 1, 0, 2, 0, 2, 1]
defensibly	0.1	1.13578	[0, -1, 0, 0, -1, 0, 3, 0, -1, 1]
defensive	0.1	1.13578	[2, -1, 0, -1, 2, 0, -1, 1, 0, -1]
defensively	-0.6	0.91652	[1, 1, -1, -1, -1, -1, -2, -1, 0, -1]
defensiveness	-0.4	1.11355	[2, -1, -1, -1, -1, 0, 0, 1, -2, -1]
defensives	-0.3	1.00499	[-1, 0, 0, -1, 0, -1, 0, -2, 2, 0]
defer	-1.2	0.6	[-1, -2, -1, 0, -1, -2, -2, -1, -1, -1]
deferring	-0.7	0.64031	[0, 0, -2, -1, -1, -1, -1, 0, -1, 0]
defiant	-0.9	1.44568	[-1, -2, -1, 2, 1, -2, 0, -1, -3, -2]
deficit	-1.7	0.78102	[-3, -3, -2, -1, -2, -2, -1, -1, -1, -1]
definite	1.1	0.7	[2, 1, 0, 1, 0, 1, 1, 2, 2, 1]
definitely	1.7	0.64031	[2, 2, 2, 2, 2, 1, 2, 2, 0, 2]
degradable	-1.0	1.26491	[-1, -2, 0, -2, 0, -1, -2, -2, 2, -2]
degradation	-2.4	1.0198	[-4, -3, -3, -3, -3, -2, -2, -2, 0, -2]
degradations	-1.5	0.67082	[-2, -1, -3, -1, -2, -2, -1, -1, -1, -1]
degradative	-2.0	0.63246	[-2, -1, -3, -2, -2, -2, -1, -2, -3, -2]
degrade	-1.9	0.7	[-3, -2, -1, -3, -1, -2, -2, -2, -1, -2]
degraded	-1.8	0.87178	[-2, -3, -1, 0, -2, -1, -2, -2, -3, -2]
degrader	-2.0	0.63246	[-2, -3, -1, -1, -2, -2, -3, -2, -2, -2]
degraders	-2.0	0.63246	[-2, -3, -1, -2, -2, -2, -3, -2, -1, -2]
degrades	-2.1	0.83066	[-3, -1, -3, -3, -2, -3, -1, -1, -2, -2]
degrading	-2.8	0.87178	[-3, -3, -2, -3, -4, -2, -3, -4, -3, -1]
degradingly	-2.7	0.64031	[-3, -2, -3, -4, -2, -3, -3, -2, -3, -2]
dehumanize	-1.8	2.18174	[-2, -4, -1, -3, 2, -4, -3, 2, -1, -4]
dehumanized	-1.9	0.7	[-2, -2, -2, -2, -1, -3, -2, -1, -3, -1]
dehumanizes	-1.5	0.67082	[-1, -1, -1, -2, -1, -3, -2, -1, -2, -1]
dehumanizing	-2.4	0.91652	[-2, -3, -4, -2, -3, -2, -3, -1, -1, -3]
deject	-2.2	0.6	[-2, -3, -2, -3, -2, -2, -3, -2, -1, -2]
dejected	-2.2	0.74833	[-2, -1, -2, -3, -2, -2, -2, -2, -2, -4]
dejecting	-2.3	0.64031	[-2, -2, -3, -2, -3, -2, -1, -2, -3, -3]
dejects	-2.0	0.63246	[-2, -2, -3, -1, -2, -1, -3, -2, -2, -2]
delay	-1.3	0.45826	[-1, -1, -1, -2, -1, -1, -2, -1, -2, -1]
delayed	-0.9	0.3	[-1, -1, 0, -1, -1, -1, -1, -1, -1, -1]
delectable	2.9	0.83066	[3, 4, 3, 2, 3, 4, 3, 1, 3, 3]
delectables	1.4	1.35647	[1, 2, 0, 4, 2, 1, 3, -1, 1, 1]
delectably	2.8	0.74833	[3, 4, 3, 2, 3, 3, 3, 1, 3, 3]
delicate	0.2	0.74833	[2, 0, 0, 0, 0, 0, 1, 0, -1, 0]
delicately	1.0	1.26491	[1, 1, 2, 0, 1, 3, -1, 2, -1, 2]
delicates	0.6	1.35647	[3, 0, 2, 1, -1, -1, 2, 0, 1, -1]
delicatessen	0.4	0.8	[0, 0, 0, 0, 0, 0, 0, 2, 2, 0]
delicatessens	0.4	0.8	[0, 0, 2, 0, 0, 2, 0, 0, 0, 0]
delicious	2.7	0.64031	[3, 2, 3, 4, 2, 3, 3, 3, 2, 2]
deliciously	1.9	0.83066	[2, 2, 1, 3, 1, 3, 2, 3, 1, 1]
deliciousness	1.8	0.87178	[1, 2, 3, 3, 2, 2, 1, 2, 2, 0]
delight	2.9	0.7	[2, 3, 4, 4, 3, 2, 3, 3, 2, 3]
delighted	2.3	0.64031	[3, 3, 2, 3, 2, 1, 3, 2, 2, 2]
delightedly	2.4	0.4899	[2, 3, 3, 3, 2, 2, 3, 2, 2, 2]
delightedness	2.1	0.53852	[2, 2, 2, 2, 3, 2, 3, 2, 1, 2]
delighter	2.0	0.63246	[3, 2, 2, 3, 1, 2, 2, 1, 2, 2]
delighters	2.6	0.66332	[3, 2, 2, 2, 3, 2, 3, 2, 4, 3]
delightful	2.8	0.6	[4, 3, 2, 3, 3, 3, 2, 2, 3, 3]
delightfully	2.7	0.45826	[3, 2, 2, 2, 3, 3, 3, 3, 3, 3]
delightfulness	2.1	0.7	[3, 2, 3, 1, 2, 3, 2, 2, 2, 1]
delighting	1.6	1.90788	[3, 3, 3, 2, 3, 2, 3, -2, -2, 1]
delights	2.0	1.54919	[2, 3, 1, -2, 2, 4, 2, 3, 3, 2]
delightsome	2.3	0.45826	[3, 3, 2, 2, 2, 3, 2, 2, 2, 2]
demand	-0.5	0.67082	[0, 0, 0, 0, 0, -1, -1, -1, 0, -2]
demanded	-0.9	0.7	[-2, 0, 0, 0, -2, -1, -1, -1, -1, -1]
demanding	-0.9	0.53852	[-1, -2, 0, -1, -1, -1, -1, -1, 0, -1]
demonstration	0.4	0.91652	[0, 0, 0, 0, 0, 0, 3, 1, 0, 0]
demoralized	-1.6	1.62481	[-2, -2, -2, -2, -3, 2, -2, -3, 1, -3]
denied	-1.9	0.53852	[-2, -3, -1, -2, -2, -1, -2, -2, -2, -2]
denier	-1.5	0.67082	[-1, -1, -1, -1, -2, -3, -2, -2, -1, -1]
deniers	-1.1	1.13578	[-2, 0, -2, -1, -3, 1, 0, -1, -2, -1]
denies	-1.8	0.6	[-1, -2, -1, -1, -2, -3, -2, -2, -2, -2]
denounce	-1.4	0.91652	[-2, -1, -2, -1, 1, -2, -2, -1, -2, -2]
denounces	-1.9	0.7	[-1, -3, -2, -3, -2, -1, -2, -2, -2, -1]
deny	-1.4	0.4899	[-1, -1, -1, -1, -2, -1, -2, -2, -1, -2]
denying	-1.4	0.4899	[-1, -1, -1, -2, -2, -2, -1, -2, -1, -1]
depress	-2.2	0.74833	[-2, -1, -3, -3, -2, -2, -3, -3, -1, -2]
depressant	-1.6	1.11355	[-3, -1, 0, -1, 0, -2, -3, -1, -3, -2]
depressants	-1.6	0.91652	[-1, -1, -3, -3, -2, 0, -1, -2, -2, -1]
depressed	-2.3	0.45826	[-2, -2, -2, -2, -2, -3, -3, -3, -2, -2]
depresses	-2.2	0.6	[-2, -2, -2, -2, -3, -3, -1, -2, -3, -2]
depressible	-1.7	0.78102	[-2, -1, 0, -3, -2, -2, -2, -1, -2, -2]
depressing	-1.6	1.28062	[-2, -2, -2, 2, -3, -1, -2, -2, -2, -2]
depressingly	-2.3	0.45826	[-2, -3, -2, -2, -2, -3, -3, -2, -2, -2]
depression	-2.7	0.64031	[-3, -2, -2, -2, -2, -3, -4, -3, -3, -3]
depressions	-2.2	0.6	[-2, -3, -3, -2, -2, -2, -3, -2, -2, -1]
depressive	-1.6	1.11355	[-2, -1, -1, -2, -1, -2, -3, 1, -3, -2]
depressively	-2.1	0.53852	[-3, -2, -3, -2, -2, -2, -2, -1, -2, -2]
depressives	-1.5	0.5	[-2, -1, -1, -2, -1, -1, -1, -2, -2, -2]
depressor	-1.8	1.16619	[-2, -4, -3, 0, 0, -2, -2, -2, -1, -2]
depressors	-1.7	0.9	[-1, -1, -1, -2, -1, -2, -4, -2, -2, -1]
depressurization	-0.3	0.78102	[1, 0, 0, -1, -1, 0, -2, 0, 0, 0]
depressurizations	-0.4	0.91652	[0, 0, 0, 0, 1, -2, -1, 0, -2, 0]
depressurize	-0.5	0.80623	[0, 0, -2, 0, -2, 0, 0, 0, 0, -1]
depressurized	-0.3	0.64031	[0, 0, 0, 0, 0, 0, -1, 0, -2, 0]
depressurizes	-0.3	0.64031	[0, 0, 0, 0, 0, 0, -1, 0, -2, 0]
depressurizing	-0.7	1.34536	[2, 0, -1, -1, -2, -2, 1, -2, 0, -2]
deprival	-2.1	0.7	[-1, -2, -2, -2, -1, -3, -3, -3, -2, -2]
deprivals	-1.2	0.87178	[0, -1, -2, -1, 0, -2, -1, -1, -3, -1]
deprivation	-1.8	1.4	[-3, -2, -3, -2, -1, -2, -2, 2, -2, -3]
deprivations	-1.8	0.74833	[-1, -2, -3, -1, -2, -1, -2, -2, -3, -1]
deprive	-2.1	0.7	[-3, -2, -1, -3, -1, -2, -2, -3, -2, -2]
deprived	-2.1	0.7	[-2, -2, -2, -2, -2, -4, -2, -2, -1, -2]
depriver	-1.6	0.91652	[-1, -2, -1, -2, -1, -4, -2, -1, -1, -1]
deprivers	-1.4	0.66332	[-2, -1, -1, -3, -1, -1, -1, -1, -2, -1]
deprives	-1.7	0.64031	[-2, -2, -1, -2, -1, -3, -2, -1, -1, -2]
depriving	-2.0	0.0	[-2, -2, -2, -2, -2, -2, -2, -2, -2, -2]
derail	-1.2	1.07703	[-1, 1, -1, -2, -1, -2, -1, -3, -2, 0]
derailed	-1.4	0.66332	[-1, -2, -2, 0, -1, -1, -2, -1, -2, -2]
derails	-1.3	0.78102	[-2, -3, -1, -2, -1, -1, 0, -1, -1, -1]
deride	-1.1	1.22066	[-3, -2, -2, -1, -1, -1, 1, -2, -1, 1]
derided	-0.8	0.87178	[-2, -1, -2, 0, 0, -1, -1, -1, 1, -1]
derides	-1.0	1.0	[-1, -2, -2, 0, 0, 1, -1, -2, -1, -2]
deriding	-1.5	0.80623	[-2, -2, 0, 0, -2, -1, -2, -2, -2, -2]
derision	-1.2	1.249	[-1, -2, -2, -2, -1, -1, 1, 1, -3, -2]
desirable	1.3	0.45826	[2, 1, 1, 1, 1, 1, 1, 2, 2, 1]
desire	1.7	0.78102	[1, 1, 2, 1, 3, 3, 1, 2, 2, 1]
desired	1.1	1.04403	[1, 1, 0, 3, 1, 0, 1, 1, 3, 0]
desirous	1.3	0.64031	[1, 2, 1, 2, 2, 1, 2, 1, 0, 1]
despair	-1.3	1.9	[2, -1, -3, -1, -3, 1, -3, 1, -3, -3]
despaired	-2.7	0.45826	[-2, -2, -3, -3, -3, -3, -3, -2, -3, -3]
despairer	-1.3	1.1	[-2, -2, -1, -3, -2, 1, -1, -1, 0, -2]
despairers	-1.3	1.00499	[-2, -1, -1, 1, -2, -1, -2, -1, -1, -3]
despairing	-2.3	0.64031	[-2, -1, -3, -2, -3, -2, -2, -2, -3, -3]
despairingly	-2.2	0.74833	[-2, -2, -2, -2, -4, -2, -3, -1, -2, -2]
despairs	-2.7	1.00499	[-3, -4, -1, -3, -4, -2, -1, -3, -3, -3]
desperate	-1.3	1.34536	[-2, -1, -2, 1, -1, -2, -3, -3, -1, 1]
desperately	-1.6	1.11355	[-3, -3, -2, -1, -2, -1, -2, -2, -1, 1]
desperateness	-1.5	1.36015	[-1, -2, -2, -2, -3, -3, 1, -2, 1, -2]
desperation	-2.0	1.0	[-2, -1, -1, -2, -3, -3, -1, -4, -1, -2]
desperations	-2.2	1.66132	[-1, -4, -2, -4, -4, -1, -1, -2, 1, -4]
despise	-1.4	1.35647	[-2, -3, -1, -2, -1, 1, -3, 1, -2, -2]
despised	-1.7	1.48661	[-2, -1, -3, -2, -3, 1, -3, 1, -2, -3]
despisement	-2.4	0.91652	[-3, -3, -2, -1, -3, -1, -4, -2, -2, -3]
despisements	-2.5	1.0247	[-2, -2, -3, -2, 0, -3, -4, -3, -3, -3]
despiser	-1.8	1.07703	[-2, -1, -3, 1, -2, -2, -3, -2, -2, -2]
despisers	-1.6	1.35647	[-3, -3, -1, -3, -2, -1, -1, 1, 0, -3]
despises	-2.0	1.26491	[-3, -1, -3, 1, -2, -1, -3, -2, -3, -3]
despising	-2.7	0.9	[-4, -3, -3, -4, -1, -2, -3, -2, -2, -3]
despondent	-2.1	0.53852	[-2, -2, -2, -3, -2, -2, -1, -3, -2, -2]
destroy	-2.5	0.67082	[-2, -3, -3, -1, -3, -3, -3, -2, -2, -3]
destroyed	-2.2	0.87178	[-1, -3, -3, -2, -1, -2, -1, -3, -3, -3]
destroyer	-2.0	0.89443	[-2, -3, -3, -3, -1, -2, -2, -2, 0, -2]
destroyers	-2.3	0.78102	[-1, -3, -3, -3, -2, -2, -3, -3, -1, -2]
destroying	-2.6	0.91652	[-2, -4, -4, -2, -1, -3, -2, -3, -2, -3]
destroys	-2.6	0.4899	[-3, -3, -3, -2, -2, -2, -3, -3, -2, -3]
destruct	-2.4	0.4899	[-3, -3, -3, -2, -2, -2, -3, -2, -2, -2]
destructed	-1.9	1.04403	[-4, -1, -2, -2, -2, -1, -3, -2, 0, -2]
destructibility	-1.8	1.07703	[-1, -2, -1, -1, -2, -3, -2, 0, -4, -2]
destructible	-1.5	1.11803	[-2, -2, -2, -1, 1, -1, -3, -1, -3, -1]
destructing	-2.5	0.67082	[-2, -3, -2, -3, -2, -2, -2, -3, -2, -4]
destruction	-2.7	0.9	[-4, -3, -4, -3, -2, -2, -3, -1, -2, -3]
destructionist	-2.6	0.8	[-3, -4, -2, -2, -2, -3, -3, -1, -3, -3]
destructionists	-2.1	0.53852	[-3, -1, -2, -2, -3, -2, -2, -2, -2, -2]
destructions	-2.3	0.78102	[-3, -2, -2, -2, -1, -3, -4, -2, -2, -2]
destructive	-3.0	0.63246	[-3, -4, -3, -2, -3, -3, -3, -2, -4, -3]
destructively	-2.4	0.91652	[-2, -3, -1, -4, -2, -3, -3, -3, -1, -2]
destructiveness	-2.4	0.91652	[-3, -3, -2, -4, -1, -2, -3, -2, -1, -3]
destructivity	-2.2	1.53623	[2, -3, -3, -4, -3, -2, -2, -2, -2, -3]
destructs	-2.4	0.91652	[-2, -1, -2, -4, -4, -2, -3, -2, -2, -2]
detached	-0.5	1.20416	[-1, 2, -1, -1, -2, 0, 1, -2, -1, 0]
detain	-1.8	0.9798	[-3, -1, -2, -2, -4, -2, -1, -1, -1, -1]
detained	-1.7	0.9	[-1, -1, -1, -1, -1, -2, -2, -2, -2, -4]
detention	-1.5	0.67082	[-1, -2, -1, -2, -1, -1, -3, -2, -1, -1]
determinable	0.9	0.7	[2, 1, 1, 1, 0, 1, 2, 0, 0, 1]
determinableness	0.2	1.07703	[0, 0, 0, 1, 0, 1, -1, 2, -2, 1]
determinably	0.9	0.83066	[2, 0, 1, 1, 0, 1, 2, 2, 0, 0]
determinacy	1.0	1.0	[0, 0, 0, 3, 1, 1, 2, 1, 0, 2]
determinant	0.2	0.6	[0, 1, -1, 0, 1, 1, 0, 0, 0, 0]
determinantal	-0.3	1.41774	[0, 0, 0, -4, 0, -1, 0, 0, 2, 0]
determinate	0.8	0.87178	[2, 1, 0, 0, 0, 2, 2, 0, 1, 0]
determinately	1.2	0.6	[1, 2, 0, 1, 1, 1, 2, 1, 2, 1]
determinateness	1.1	0.9434	[1, 1, 1, 0, 0, 2, 3, 1, 0, 2]
determination	1.7	0.78102	[2, 3, 1, 1, 1, 2, 1, 1, 2, 3]
determinations	0.8	1.16619	[0, 3, 1, 0, 1, 3, 0, 0, 0, 0]
determinative	1.1	1.04403	[2, 0, 3, 2, 1, 1, 0, 0, 2, 0]
determinatives	0.9	1.3	[2, 0, -2, 1, 2, 2, 2, 0, 0, 2]
determinator	1.1	1.04403	[3, 0, 1, 0, 1, 0, 2, 2, 0, 2]
determined	1.4	1.35647	[-2, 1, 3, 2, 2, 2, 3, 1, 1, 1]
devastate	-3.1	0.9434	[-4, -4, -1, -4, -4, -3, -3, -3, -2, -3]
devastated	-3.0	0.89443	[-4, -3, -3, -4, -3, -1, -3, -4, -3, -2]
devastates	-2.8	0.9798	[-4, -3, -2, -4, -2, -1, -3, -4, -3, -2]
devastating	-3.3	0.9	[-4, -3, -4, -4, -1, -4, -3, -3, -3, -4]
devastatingly	-2.4	1.28062	[-3, -4, -3, 0, -3, 0, -2, -3, -3, -3]
devastation	-1.8	2.13542	[-1, -1, -3, -3, -4, -3, -4, 2, 2, -3]
devastations	-1.9	1.92094	[-3, -3, -2, -2, 1, -1, 2, -4, -4, -3]
devastative	-3.2	1.16619	[-4, -3, 0, -4, -4, -3, -3, -4, -3, -4]
devastator	-2.8	0.74833	[-3, -3, -2, -2, -3, -2, -3, -4, -2, -4]
devastators	-2.9	1.22066	[-3, -2, -3, -4, -4, -4, 0, -3, -2, -4]
devil	-3.4	0.8	[-4, -3, -4, -4, -4, -4, -2, -3, -2, -4]
deviled	-1.6	1.0198	[-1, -2, 0, -4, -1, -2, -2, -2, -1, -1]
devilfish	-0.8	1.07703	[-2, -3, -1, 0, 0, 0, 0, -2, 0, 0]
devilfishes	-0.6	1.0198	[-3, 0, -2, 0, 0, 0, 0, -1, 0, 0]
deviling	-2.2	0.87178	[-1, -3, -1, -2, -3, -2, -2, -4, -2, -2]
devilish	-2.1	1.04403	[-1, -2, -4, -1, -1, -2, -2, -4, -2, -2]
devilishly	-1.6	0.8	[-2, -2, -3, -1, -2, -2, 0, -2, -1, -1]
devilishness	-2.3	0.9	[-4, -1, -4, -2, -2, -2, -2, -2, -2, -2]
devilkin	-2.4	0.91652	[-3, -1, -2, -3, -2, -4, -2, -3, -3, -1]
devilled	-2.3	1.1	[-3, -1, -2, -3, 0, -4, -2, -3, -2, -3]
devilling	-1.8	1.249	[-3, -1, -1, -2, -2, -3, -4, -2, 0, 0]
devilment	-1.9	0.9434	[-2, -1, -2, -1, -3, -2, -2, -1, -1, -4]
devilments	-1.1	0.7	[-2, -1, -2, -2, -1, 0, 0, -1, -1, -1]
devilries	-1.6	1.35647	[-1, -1, -2, -4, -3, 0, -2, 0, 0, -3]
devilry	-2.8	1.249	[-4, -3, 0, -2, -2, -2, -3, -4, -4, -4]
devils	-2.7	0.9	[-3, -1, -3, -2, -2, -3, -4, -3, -2, -4]
deviltries	-1.5	1.11803	[-1, -2, 1, -2, 0, -2, -2, -3, -2, -2]
deviltry	-2.8	1.32665	[-4, -4, -3, -3, -3, -3, 1, -3, -3, -3]
devilwood	-0.8	1.07703	[0, -1, -2, 0, 0, 0, 0, -3, -2, 0]
devilwoods	-1.0	0.7746	[-2, 0, -2, -1, -1, 0, -1, -2, 0, -1]
devote	1.4	1.28062	[3, 0, 2, -1, 0, 3, 2, 2, 1, 2]
devoted	1.7	1.34536	[2, -1, 3, 2, 1, 0, 1, 3, 3, 3]
devotedly	1.6	1.35647	[1, 1, 2, 2, 3, 2, 2, -2, 2, 3]
devotedness	2.0	1.0	[0, 3, 2, 1, 3, 2, 2, 3, 1, 3]
devotee	1.6	1.11355	[1, 3, 0, 2, 1, 2, 1, 1, 4, 1]
devotees	0.5	1.0247	[0, 0, 3, 0, 0, 0, 2, 0, 0, 0]
devotement	1.5	1.36015	[2, -1, 3, 2, -1, 1, 2, 3, 2, 2]
devotements	1.1	1.04403	[0, 1, 0, 0, 0, 2, 2, 2, 3, 1]
devotes	1.6	0.91652	[2, 3, 0, 2, 2, 3, 1, 1, 1, 1]
devoting	2.1	0.7	[2, 2, 3, 1, 2, 3, 2, 3, 1, 2]
devotion	2.0	1.0	[2, 0, 1, 2, 4, 3, 2, 2, 2, 2]
devotional	1.2	1.16619	[0, 1, 0, 2, 2, 1, 0, 0, 3, 3]
devotionally	2.2	0.4	[2, 2, 2, 3, 2, 3, 2, 2, 2, 2]
devotionals	1.2	1.07703	[3, 1, 1, 0, -1, 2, 2, 2, 1, 1]
devotions	1.8	0.74833	[2, 3, 1, 2, 1, 3, 1, 2, 1, 2]
diamond	1.4	1.42829	[0, 0, 2, 0, 1, 3, 1, 3, 0, 4]
dick	-2.3	1.18743	[-2, -3, -4, -2, 0, -4, -2, -1, -3, -2]
dickhead	-3.1	0.53852	[-4, -3, -3, -3, -4, -2, -3, -3, -3, -3]
die	-2.9	0.9434	[-4, -3, -1, -2, -4, -3, -3, -3, -2, -4]
died	-2.6	1.28062	[-3, -1, -3, -4, -2, -4, -3, -4, -2, 0]
difficult	-1.5	0.5	[-2, -2, -1, -1, -2, -1, -1, -2, -1, -2]
difficulties	-1.2	0.4	[-1, -2, -1, -1, -1, -2, -1, -1, -1, -1]
difficultly	-1.7	0.45826	[-1, -2, -1, -2, -2, -1, -2, -2, -2, -2]
difficulty	-1.4	0.66332	[-2, -2, -2, -2, 0, -1, -1, -2, -1, -1]
diffident	-1.0	0.44721	[-1, -1, -1, -1, -1, 0, -2, -1, -1, -1]
dignified	2.2	0.6	[1, 2, 2, 2, 3, 3, 3, 2, 2, 2]
dignifies	2.0	0.7746	[1, 3, 2, 2, 3, 3, 2, 2, 1, 1]
dignify	1.8	0.74833	[1, 2, 2, 1, 3, 3, 1, 2, 2, 1]
dignifying	2.1	1.04403	[1, 1, 1, 3, 1, 4, 2, 3, 2, 3]
dignitaries	0.6	0.91652	[0, 0, 1, 0, 0, 0, 3, 0, 1, 1]
dignitary	1.9	1.3	[0, 3, 4, 2, 3, 1, 1, 3, 0, 2]
dignities	1.4	0.66332	[1, 2, 1, 1, 3, 2, 1, 1, 1, 1]
dignity	1.7	0.9	[0, 3, 2, 1, 2, 1, 2, 3, 1, 2]
dilemma	-0.7	1.48661	[2, -1, -2, -2, -1, -1, 2, 0, -2, -2]
dipshit	-2.1	0.7	[-1, -2, -2, -3, -2, -3, -3, -2, -2, -1]
dire	-2.0	1.26491	[-2, -3, -3, -2, -3, -1, -1, 1, -3, -3]
direful	-3.1	0.83066	[-3, -3, -3, -3, -4, -1, -4, -3, -4, -3]
dirt	-1.4	0.91652	[-1, -1, -1, 0, -3, -1, -1, -2, -3, -1]
dirtier	-1.4	0.4899	[-2, -1, -1, -2, -1, -2, -1, -1, -2, -1]
dirtiest	-2.4	1.0198	[-4, -3, -2, -1, -2, -3, -1, -2, -4, -2]
dirty	-1.9	0.83066	[-2, -1, -1, -1, -2, -2, -1, -3, -3, -3]
disabling	-2.1	0.53852	[-2, -1, -3, -3, -2, -2, -2, -2, -2, -2]
disadvantage	-1.8	0.4	[-2, -2, -1, -2, -2, -1, -2, -2, -2, -2]
disadvantaged	-1.7	0.64031	[-2, -2, -3, -1, -2, -2, -2, -1, -1, -1]
disadvantageous	-1.8	0.74833	[-1, -2, -2, -1, -3, -1, -1, -2, -3, -2]
disadvantageously	-2.1	0.83066	[-2, -4, -1, -2, -1, -2, -2, -2, -3, -2]
disadvantageousness	-1.6	0.66332	[-1, -1, -3, -2, -1, -1, -2, -1, -2, -2]
disadvantages	-1.7	0.64031	[-2, -2, -3, -1, -2, -2, -2, -1, -1, -1]
disagree	-1.6	0.4899	[-1, -2, -1, -1, -2, -2, -2, -2, -1, -2]
disagreeable	-1.7	0.64031	[-1, -2, -1, -1, -1, -2, -2, -2, -3, -2]
disagreeableness	-1.7	0.64031	[-1, -1, -2, -2, -1, -2, -3, -1, -2, -2]
disagreeablenesses	-1.9	0.9434	[-2, 0, -3, -1, -2, -3, -2, -1, -3, -2]
disagreeably	-1.5	0.67082	[-3, -2, -1, -1, -1, -2, -1, -2, -1, -1]
disagreed	-1.3	0.64031	[-1, -2, -1, -1, -2, 0, -1, -2, -1, -2]
disagreeing	-1.4	0.8	[-1, 0, -1, -2, -2, -1, -3, -1, -1, -2]
disagreement	-1.5	0.67082	[-2, -1, -1, -1, -1, -1, -1, -3, -2, -2]
disagreements	-1.8	0.6	[-2, -3, -1, -2, -2, -1, -2, -2, -1, -2]
disagrees	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -1, -2, -1, -2]
disappear	-0.9	0.7	[-2, -1, 0, 0, -2, -1, -1, -1, 0, -1]
disappeared	-0.9	0.7	[-2, 0, -1, -1, 0, 0, -2, -1, -1, -1]
disappears	-1.4	0.8	[-2, -2, 0, 0, -2, -1, -2, -1, -2, -2]
disappoint	-1.7	0.64031	[-1, -1, -1, -3, -2, -2, -2, -1, -2, -2]
disappointed	-2.1	0.83066	[-1, -3, -2, -2, -3, -1, -1, -2, -3, -3]
disappointedly	-1.7	0.78102	[-3, -1, -3, -1, -2, -1, -1, -2, -2, -1]
disappointing	-2.2	0.6	[-1, -2, -2, -3, -3, -2, -2, -2, -3, -2]
disappointingly	-1.9	0.7	[-2, -1, -1, -3, -2, -3, -2, -2, -1, -2]
disappointment	-2.3	1.00499	[-3, -1, -4, -1, -3, -1, -3, -2, -2, -3]
disappointments	-2.0	1.09545	[-1, -2, -4, -3, -2, -2, -3, 0, -2, -1]
disappoints	-1.6	0.4899	[-2, -1, -1, -1, -2, -1, -2, -2, -2, -2]
disaster	-3.1	0.83066	[-2, -4, -4, -3, -3, -2, -4, -3, -2, -4]
disasters	-2.6	0.8	[-2, -2, -3, -1, -3, -3, -2, -4, -3, -3]
disastrous	-2.9	0.53852	[-2, -2, -3, -3, -3, -3, -4, -3, -3, -3]
disbelieve	-1.2	0.87178	[-1, -2, -1, -2, -1, 0, 0, -1, -3, -1]
discard	-1.0	0.44721	[-1, -1, -1, -1, 0, -1, -1, -1, -2, -1]
discarded	-1.4	0.91652	[-1, -1, -1, -1, 0, -1, -2, -3, -3, -1]
discarding	-0.7	0.45826	[-1, 0, -1, -1, -1, 0, -1, 0, -1, -1]
discards	-1.0	0.63246	[0, -1, -1, -1, -2, 0, -2, -1, -1, -1]
discomfort	-1.8	0.6	[-2, -2, -2, -1, -1, -3, -2, -2, -1, -2]
discomfortable	-1.6	0.8	[-1, -1, -1, -2, -3, -1, -2, -1, -3, -1]
discomforted	-1.6	0.8	[-1, -1, -1, -2, -3, -3, -1, -1, -1, -2]
discomforting	-1.6	1.11355	[-1, -2, -1, -1, -2, 1, -3, -2, -3, -2]
discomforts	-1.3	0.9	[-2, -1, -2, -1, -1, -2, -2, -1, 1, -2]
disconsolate	-2.3	0.78102	[-1, -2, -2, -3, -2, -2, -2, -4, -3, -2]
disconsolation	-1.7	0.45826	[-2, -2, -1, -2, -1, -1, -2, -2, -2, -2]
discontented	-1.8	0.9798	[-1, -3, -1, -2, -4, -2, -1, -2, -1, -1]
discord	-1.7	0.64031	[-3, -2, -2, -2, -2, -1, -1, -1, -1, -2]
discounted	0.2	1.249	[-1, 0, 3, -1, 0, 1, 1, 1, -1, -1]
discourage	-1.8	0.6	[-2, -2, -1, -2, -1, -1, -2, -2, -3, -2]
discourageable	-1.2	0.9798	[-1, -2, -1, 1, -1, -1, -1, -2, -3, -1]
discouraged	-1.7	0.45826	[-2, -1, -2, -2, -2, -2, -1, -1, -2, -2]
discouragement	-2.0	0.89443	[-4, -1, -2, -2, -1, -1, -3, -2, -2, -2]
discouragements	-1.8	0.6	[-2, -2, -2, -1, -1, -3, -2, -1, -2, -2]
discourager	-1.7	0.78102	[-2, -1, -3, -2, -1, -3, -1, -2, -1, -1]
discouragers	-1.9	0.53852	[-2, -2, -2, -2, -1, -1, -3, -2, -2, -2]
discourages	-1.9	0.53852	[-2, -1, -2, -2, -2, -2, -1, -3, -2, -2]
discouraging	-1.9	0.7	[-2, -2, -2, -3, -1, -1, -2, -1, -2, -3]
discouragingly	-1.8	0.87178	[-2, -1, -3, -1, -1, -3, -2, -1, -3, -1]
discredited	-1.9	0.53852	[-2, -2, -2, -2, -1, -3, -1, -2, -2, -2]
disdain	-2.1	0.3	[-3, -2, -2, -2, -2, -2, -2, -2, -2, -2]
disgrace	-2.2	0.74833	[-2, -4, -1, -2, -2, -2, -2, -3, -2, -2]
disgraced	-2.0	0.44721	[-3, -2, -2, -2, -1, -2, -2, -2, -2, -2]
disguise	-1.0	1.09545	[-2, -1, 0, 0, 0, 0, -3, -2, -2, 0]
disguised	-1.1	1.04403	[-3, 0, 0, -1, -1, 0, -3, -1, -1, -1]
disguises	-1.0	0.63246	[-2, 0, 0, -1, -1, -1, -2, -1, -1, -1]
disguising	-1.3	0.78102	[0, -2, -1, -1, -1, -2, -1, -1, -3, -1]
disgust	-2.9	0.7	[-3, -3, -4, -2, -3, -3, -4, -2, -3, -2]
disgusted	-2.4	0.91652	[-4, -3, -3, -1, -3, -1, -2, -2, -2, -3]
disgustedly	-3.0	0.89443	[-2, -3, -4, -4, -2, -4, -4, -2, -3, -2]
disgustful	-2.6	0.4899	[-3, -3, -2, -2, -2, -2, -3, -3, -3, -3]
disgusting	-2.4	1.11355	[-3, -2, -3, -4, -1, -3, -1, -4, -1, -2]
disgustingly	-2.9	0.7	[-3, -3, -4, -3, -3, -2, -2, -4, -2, -3]
disgusts	-2.1	0.53852	[-2, -2, -3, -2, -2, -2, -2, -3, -1, -2]
dishearten	-2.0	0.63246	[-3, -1, -2, -3, -2, -2, -1, -2, -2, -2]
disheartened	-2.2	0.74833	[-2, -2, -2, -1, -2, -2, -4, -3, -2, -2]
disheartening	-1.8	1.32665	[-2, -2, -2, -3, -2, 2, -2, -2, -3, -2]
dishearteningly	-2.0	0.63246	[-2, -3, -2, -1, -2, -2, -2, -3, -2, -1]
disheartenment	-2.3	0.45826	[-3, -2, -3, -2, -2, -2, -2, -3, -2, -2]
disheartenments	-2.2	0.87178	[-2, -3, -3, -3, -3, -1, -1, -1, -2, -3]
disheartens	-2.2	0.4	[-3, -2, -2, -2, -3, -2, -2, -2, -2, -2]
dishonest	-2.7	0.9	[-3, -2, -1, -4, -3, -2, -4, -3, -3, -2]
disillusion	-1.0	1.18322	[-2, 0, -2, -1, -2, 1, -2, -1, 1, -2]
disillusioned	-1.9	0.7	[-2, -2, -3, -2, -3, -1, -1, -1, -2, -2]
disillusioning	-1.3	1.00499	[-2, -2, 1, -2, 0, -2, -2, -1, -1, -2]
disillusionment	-1.7	0.78102	[-1, -3, -2, -3, -1, -2, -2, -1, -1, -1]
disillusionments	-1.5	1.0247	[-2, 1, -3, -2, -1, -1, -2, -1, -2, -2]
disillusions	-1.6	0.4899	[-2, -2, -2, -1, -1, -1, -2, -2, -1, -2]
disinclined	-1.1	0.53852	[0, -1, -1, -1, -1, -1, -1, -2, -2, -1]
disjointed	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -1, -2, -1, -1]
dislike	-1.6	0.4899	[-2, -1, -1, -2, -2, -1, -2, -1, -2, -2]
disliked	-1.7	0.64031	[-2, -3, -2, -1, -1, -1, -2, -2, -1, -2]
dislikes	-1.7	0.78102	[-2, -2, -1, -1, -2, -1, -3, -3, -1, -1]
disliking	-1.3	0.45826	[-1, -1, -2, -2, -2, -1, -1, -1, -1, -1]
dismal	-3.0	1.0	[-2, -1, -4, -4, -3, -2, -3, -4, -4, -3]
dismay	-1.8	0.87178	[-3, -1, -1, -3, -1, -1, -3, -1, -2, -2]
dismayed	-1.9	0.9434	[-1, -2, -1, -3, -4, -1, -2, -2, -1, -2]
dismaying	-2.2	0.9798	[-2, -3, -2, -3, -3, 0, -2, -1, -3, -3]
dismayingly	-1.9	0.83066	[-2, -3, -2, -3, -2, -1, -2, -2, 0, -2]
dismays	-1.8	1.07703	[-1, -1, -4, -3, -2, -1, -2, 0, -2, -2]
disorder	-1.7	0.64031	[-2, -1, -1, -2, -2, -1, -3, -1, -2, -2]
disorganized	-1.2	0.4	[-1, -1, -1, -1, -1, -2, -1, -2, -1, -1]
disoriented	-1.5	0.67082	[-2, -2, -1, 0, -1, -2, -2, -1, -2, -2]
disparage	-2.0	0.44721	[-2, -2, -2, -1, -2, -2, -2, -3, -2, -2]
disparaged	-1.4	0.8	[-1, -2, -2, -3, -1, -1, -1, -2, 0, -1]
disparages	-1.6	0.8	[-1, -2, -3, -2, -1, -1, -2, -2, 0, -2]
disparaging	-2.2	0.6	[-3, -1, -2, -2, -2, -3, -3, -2, -2, -2]
displeased	-1.9	0.7	[-3, -2, -1, -1, -3, -2, -2, -1, -2, -2]
dispute	-1.7	0.78102	[-1, -3, -1, -1, -2, -1, -2, -2, -3, -1]
disputed	-1.4	0.66332	[-2, -2, -2, -2, 0, -1, -1, -1, -1, -2]
disputes	-1.1	1.64012	[-2, -2, -2, 2, -3, -1, -2, 2, -1, -2]
disputing	-1.7	0.64031	[-2, -2, -2, -2, -1, -1, -3, -1, -1, -2]
disqualified	-1.8	0.6	[-1, -2, -1, -2, -1, -2, -2, -3, -2, -2]
disquiet	-1.3	0.9	[-1, -2, -2, -1, -1, -1, 1, -2, -2, -2]
disregard	-1.1	0.53852	[-1, -1, -2, -1, -1, -2, -1, -1, 0, -1]
disregarded	-1.6	0.4899	[-1, -1, -2, -2, -2, -2, -1, -2, -1, -2]
disregarding	-0.9	0.53852	[-1, 0, -1, 0, -2, -1, -1, -1, -1, -1]
disregards	-1.4	0.4899	[-1, -1, -2, -1, -2, -2, -2, -1, -1, -1]
disrespect	-1.8	0.6	[-2, -2, -2, -1, -2, -2, -1, -3, -1, -2]
disrespected	-2.0	0.63246	[-2, -2, -2, -2, -2, -3, -3, -1, -1, -2]
disruption	-1.5	0.67082	[-1, -1, -1, -2, -1, -3, -2, -2, -1, -1]
disruptions	-1.4	0.4899	[-1, -2, -1, -1, -1, -2, -2, -2, -1, -1]
disruptive	-1.3	1.00499	[-4, 0, -1, -1, -1, -1, -1, -1, -2, -1]
dissatisfaction	-2.2	0.74833	[-4, -2, -2, -2, -1, -3, -2, -2, -2, -2]
dissatisfactions	-1.9	0.83066	[-1, -3, -3, -1, -2, -1, -2, -2, -1, -3]
dissatisfactory	-2.0	0.63246	[-2, -2, -3, -1, -2, -3, -2, -2, -1, -2]
dissatisfied	-1.6	0.66332	[-2, -3, -1, -2, -1, -1, -2, -2, -1, -1]
dissatisfies	-1.8	0.74833	[-3, -3, -1, -1, -2, -1, -2, -2, -2, -1]
dissatisfy	-2.2	0.6	[-2, -3, -2, -2, -2, -2, -3, -3, -1, -2]
dissatisfying	-2.4	0.91652	[-3, -1, -4, -3, -2, -1, -2, -2, -3, -3]
distort	-1.3	0.45826	[-2, -1, -1, -1, -2, -1, -1, -1, -1, -2]
distorted	-1.7	0.78102	[-3, -1, -3, -1, -2, -1, -2, -2, -1, -1]
distorting	-1.1	0.53852	[0, -1, -1, -1, -2, -1, -1, -1, -2, -1]
distorts	-1.4	0.4899	[-2, -1, -1, -1, -2, -2, -2, -1, -1, -1]
distract	-1.2	0.6	[-1, -1, 0, -2, -1, -1, -1, -2, -1, -2]
distractable	-1.3	1.00499	[-2, 0, 1, -2, -2, -1, -1, -2, -2, -2]
distracted	-1.4	0.66332	[-1, -3, -1, -2, -2, -1, -1, -1, -1, -1]
distractedly	-0.9	0.7	[-1, -1, 0, -2, -1, 0, -1, 0, -1, -2]
distractibility	-1.3	1.1	[-1, -1, -3, -1, 0, -3, -2, 0, -2, 0]
distractible	-1.5	0.92195	[-1, -2, -1, -1, -4, -1, -1, -1, -2, -1]
distracting	-1.2	0.4	[-2, -1, -1, -1, -1, -1, -2, -1, -1, -1]
distractingly	-1.4	1.0198	[-4, 0, -1, -1, -1, -2, -1, -1, -2, -1]
distraction	-1.6	0.66332	[-1, -2, -2, -1, -1, -3, -1, -1, -2, -2]
distractions	-1.0	0.0	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
distractive	-1.6	0.4899	[-2, -2, -1, -1, -1, -1, -2, -2, -2, -2]
distracts	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -1, -2, -1, -1]
distraught	-2.6	0.8	[-2, -3, -2, -3, -4, -2, -3, -3, -1, -3]
distress	-2.4	0.8	[-1, -2, -2, -3, -3, -4, -3, -2, -2, -2]
distressed	-1.8	0.6	[-2, -2, -2, -3, -2, -2, -1, -1, -2, -1]
distresses	-1.6	0.66332	[-2, -1, -1, -2, -2, -2, -1, -1, -3, -1]
distressful	-2.2	0.6	[-1, -3, -3, -2, -2, -3, -2, -2, -2, -2]
distressfully	-1.7	1.1	[-1, -3, -2, 1, -3, -1, -2, -2, -2, -2]
distressfulness	-2.4	0.66332	[-2, -3, -2, -3, -3, -3, -2, -1, -3, -2]
distressing	-1.7	1.18743	[-3, -3, -1, -1, -2, -2, -3, -2, 1, -1]
distressingly	-2.2	0.74833	[-3, -1, -2, -3, -3, -3, -2, -2, -2, -1]
distrust	-1.8	0.87178	[-1, -2, -2, -2, -1, -1, -2, -4, -1, -2]
distrusted	-2.4	0.66332	[-1, -3, -3, -3, -2, -2, -3, -2, -2, -3]
distrustful	-2.1	0.83066	[-1, -3, -2, -2, -3, -1, -1, -3, -2, -3]
distrustfully	-1.8	0.6	[-2, -1, -1, -2, -1, -2, -3, -2, -2, -2]
distrustfulness	-1.6	0.66332	[-2, -1, -2, -1, -2, -1, -1, -3, -1, -2]
distrusting	-2.1	0.83066	[-1, -2, -2, -2, -3, -3, -1, -3, -1, -3]
distrusts	-1.3	0.45826	[-1, -1, -2, -1, -2, -1, -2, -1, -1, -1]
disturb	-1.7	0.45826	[-2, -1, -1, -2, -2, -2, -1, -2, -2, -2]
disturbance	-1.6	0.8	[-1, -2, -1, -2, -2, -3, -1, -2, 0, -2]
disturbances	-1.4	0.66332	[-1, -1, -1, -2, -1, -1, -1, -1, -3, -2]
disturbed	-1.6	0.4899	[-2, -2, -1, -1, -2, -2, -1, -2, -1, -2]
disturber	-1.4	0.4899	[-2, -1, -1, -2, -2, -1, -1, -2, -1, -1]
disturbers	-2.1	0.53852	[-2, -2, -2, -2, -2, -3, -3, -2, -1, -2]
disturbing	-2.3	0.45826	[-2, -2, -3, -3, -2, -2, -3, -2, -2, -2]
disturbingly	-2.3	0.78102	[-2, -2, -1, -3, -4, -3, -2, -2, -2, -2]
disturbs	-1.9	0.53852	[-2, -2, -1, -2, -3, -2, -1, -2, -2, -2]
dithering	-0.5	0.92195	[0, 0, 0, 0, 1, -1, -2, -2, -1, 0]
divination	1.7	1.1	[2, 3, 0, 1, 2, 1, 3, 3, 2, 0]
divinations	1.1	1.04403	[1, 0, 1, 2, 2, 0, 3, 2, 0, 0]
divinatory	1.6	1.42829	[4, 1, 0, 0, 1, 3, 3, 0, 1, 3]
divine	2.6	0.8	[3, 3, 3, 2, 1, 2, 3, 4, 2, 3]
divined	0.8	1.16619	[1, 0, 3, 0, 0, 1, 0, 3, 0, 0]
divinely	2.9	0.7	[3, 2, 3, 3, 2, 4, 3, 2, 4, 3]
diviner	0.3	0.9	[0, 0, 3, 0, 0, 0, 0, 0, 0, 0]
diviners	1.2	1.16619	[0, 1, 0, 2, 2, 0, 3, 1, 3, 0]
divines	0.8	1.249	[0, 0, 3, 0, 0, 0, 3, 2, 0, 0]
divinest	2.7	0.78102	[3, 4, 2, 4, 2, 2, 2, 3, 2, 3]
diving	0.3	0.45826	[1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
divining	0.9	1.37477	[0, -1, 2, 0, 1, 0, 2, 4, 1, 0]
divinise	0.5	1.36015	[0, 2, 0, 0, 0, 0, 0, -2, 2, 3]
divinities	1.8	1.46969	[1, 3, 3, 4, 0, 0, 1, 0, 3, 3]
divinity	2.7	1.00499	[4, 4, 2, 3, 3, 1, 2, 4, 2, 2]
divinize	2.3	1.00499	[4, 2, 2, 3, 4, 1, 1, 2, 2, 2]
dizzy	-0.9	0.3	[-1, -1, -1, -1, -1, -1, -1, -1, 0, -1]
dodging	-0.4	0.8	[-1, -1, 0, 1, 0, -1, 0, 0, -2, 0]
dodgy	-0.9	0.9434	[-1, -1, -1, -3, -1, 1, -1, -1, -1, 0]
dolorous	-2.2	0.6	[-2, -2, -2, -3, -3, -3, -2, -2, -1, -2]
dominance	0.8	0.87178	[2, 0, 0, 2, 1, 0, 0, 1, 2, 0]
dominances	-0.1	0.9434	[-1, 0, 1, 1, 0, -1, 0, 1, 0, -2]
dominantly	0.2	1.16619	[-2, 0, 0, -1, 2, 1, 2, 0, 0, 0]
dominants	0.2	1.16619	[0, 2, -1, 0, -1, -1, -1, 1, 1, 2]
dominate	-0.5	0.92195	[0, -1, 1, -1, 1, -1, -2, 0, -1, -1]
dominates	0.2	1.249	[1, 0, -2, -1, 1, -1, 2, 0, 0, 2]
dominating	-1.2	1.98997	[-4, -1, -4, -1, -3, -1, -1, 2, 2, -1]
domination	-0.2	0.9798	[0, 1, 0, -1, -1, -1, 0, 2, -1, -1]
dominations	-0.3	0.45826	[0, 0, 0, 0, 0, -1, 0, -1, -1, 0]
dominative	-0.7	1.18743	[-1, -1, -2, -2, -1, -1, -1, 2, 1, -1]
dominators	-0.4	1.8	[-1, -2, -2, -2, 0, 2, 2, -3, 2, 0]
dominatrices	-0.2	1.6	[-3, 0, 2, 0, -2, -2, 0, 1, 2, 0]
dominatrix	-0.5	0.92195	[0, 0, -1, 0, 0, 1, 0, -1, -2, -2]
dominatrixes	0.6	1.35647	[0, 4, 0, -1, 0, 2, 1, 0, 0, 0]
doom	-1.7	1.26886	[-2, -1, -1, -4, -2, -2, 1, -3, -1, -2]
doomed	-3.2	0.74833	[-3, -3, -4, -4, -4, -2, -4, -3, -3, -2]
doomful	-2.1	0.7	[-3, -2, -3, -1, -2, -3, -2, -1, -2, -2]
dooming	-2.8	0.4	[-2, -3, -2, -3, -3, -3, -3, -3, -3, -3]
dooms	-1.1	1.57797	[1, -3, -1, -3, -2, -1, -3, 1, 1, -1]
doomsayer	-0.7	1.41774	[2, -1, -2, -1, 1, -2, -2, -1, 1, -2]
doomsayers	-1.7	0.78102	[-1, -2, -3, 0, -2, -2, -2, -1, -2, -2]
doomsaying	-1.5	1.28452	[-3, -2, -2, 0, 1, 0, -3, -2, -2, -2]
doomsayings	-1.5	0.92195	[-2, -1, -1, -2, -2, 0, 0, -2, -3, -2]
doomsday	-2.8	1.249	[-3, -1, -3, -4, -3, -4, 0, -4, -3, -3]
doomsdayer	-2.2	1.249	[-3, -1, -4, -3, -4, -3, -1, -1, -1, -1]
doomsdays	-2.4	1.85472	[-3, -2, -4, 1, -4, -3, -2, -4, 1, -4]
doomster	-2.2	0.87178	[-2, -1, -2, -3, -1, -3, -1, -3, -3, -3]
doomsters	-1.6	0.8	[-3, -1, -2, -2, 0, -2, -2, -1, -1, -2]
doomy	-1.1	1.37477	[2, -2, -1, -2, -2, -2, -2, 1, -1, -2]
dork	-1.4	0.66332	[-1, -2, -2, -1, -1, -1, -3, -1, -1, -1]
dorkier	-1.1	0.53852	[-1, -1, -1, -1, -2, 0, -1, -2, -1, -1]
dorkiest	-1.2	0.74833	[-1, -2, -1, -3, -1, 0, -1, -1, -1, -1]
dorks	-0.5	0.67082	[-1, 1, -1, -1, -1, -1, 0, 0, -1, 0]
dorky	-1.1	1.04403	[-1, 0, -1, 1, -1, -1, -3, -2, -2, -1]
doubt	-1.5	0.5	[-1, -1, -2, -2, -1, -1, -2, -1, -2, -2]
doubtable	-1.5	0.5	[-1, -1, -2, -1, -2, -1, -2, -2, -1, -2]
doubted	-1.1	1.22066	[-1, -2, -2, 2, -1, -1, -2, -2, -2, 0]
doubter	-1.6	0.91652	[-1, -3, -2, -1, -1, -1, -2, -2, -3, 0]
doubters	-1.3	0.45826	[-1, -1, -1, -1, -1, -2, -1, -2, -2, -1]
doubtful	-1.4	0.4899	[-1, -1, -2, -1, -2, -2, -1, -1, -2, -1]
doubtfully	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -2]
doubtfulness	-1.2	0.4	[-2, -1, -1, -1, -1, -1, -1, -1, -1, -2]
doubting	-1.4	0.4899	[-1, -1, -1, -2, -2, -1, -1, -1, -2, -2]
doubtingly	-1.4	0.4899	[-2, -2, -1, -1, -1, -1, -1, -2, -2, -1]
doubtless	0.9	1.51327	[2, 2, 1, 2, -2, 2, -2, 1, 1, 2]
doubtlessly	1.2	0.9798	[2, 1, 1, 2, 0, -1, 2, 1, 2, 2]
doubtlessness	0.8	0.9798	[2, 1, 2, 0, 0, 0, 2, -1, 1, 1]
doubts	-1.2	0.6	[-2, -1, -1, -1, -2, -2, -1, 0, -1, -1]
douche	-1.5	1.68819	[-3, -2, -3, 1, 1, -2, -3, -2, 1, -3]
douchebag	-3.0	0.44721	[-3, -3, -3, -3, -3, -3, -2, -3, -4, -3]
downcast	-1.8	0.74833	[-1, -1, -1, -2, -2, -2, -1, -3, -3, -2]
downhearted	-2.3	0.78102	[-1, -2, -2, -4, -2, -2, -2, -3, -3, -2]
downside	-1.0	0.7746	[-1, -1, -1, -1, -1, -1, -2, 1, -2, -1]
drag	-0.9	0.83066	[-1, -2, -1, -1, -2, -1, -1, 1, 0, -1]
dragged	-0.2	1.07703	[-2, -1, 0, 0, -1, 0, 0, 1, 2, -1]
drags	-0.7	0.64031	[0, -1, 0, -1, -1, -2, -1, 0, 0, -1]
drained	-1.5	0.5	[-1, -1, -2, -2, -1, -2, -1, -2, -1, -2]
dread	-2.0	0.63246	[-2, -3, -2, -2, -2, -2, -3, -1, -1, -2]
dreaded	-2.7	0.64031	[-2, -3, -3, -3, -4, -3, -2, -2, -2, -3]
dreadful	-1.9	1.86815	[-4, -2, -2, 2, -1, -4, -1, 0, -3, -4]
dreadfully	-2.7	1.26886	[-4, -4, -3, -4, -3, -1, -2, -1, -1, -4]
dreadfulness	-3.2	0.87178	[-3, -4, -2, -3, -4, -4, -2, -2, -4, -4]
dreadfuls	-2.4	1.2	[-4, -3, -3, -2, -3, -2, -4, 0, -1, -2]
dreading	-2.4	0.8	[-3, -2, -2, -2, -2, -2, -3, -4, -3, -1]
dreadlock	-0.4	0.66332	[0, 0, 0, 0, 0, -1, -2, 0, -1, 0]
dreadlocks	-0.2	0.9798	[0, 0, 0, 0, 0, -1, -2, 2, 0, -1]
dreadnought	-0.6	1.35647	[-2, 0, 0, 0, -3, 0, -1, -2, 0, 2]
dreadnoughts	-0.4	0.66332	[0, -1, -1, 0, 0, 0, 0, 0, -2, 0]
dreads	-1.4	1.42829	[0, -1, 0, 0, -3, -3, 0, -4, -2, -1]
dream	1.0	1.18322	[0, 1, 2, 0, 0, 3, 0, 3, 1, 0]
dreams	1.7	1.1	[2, 2, 3, 0, 1, 1, 1, 4, 1, 2]
dreary	-1.4	0.4899	[-1, -1, -2, -1, -1, -2, -2, -1, -2, -1]
droopy	-0.8	0.74833	[-1, -1, 0, -1, -2, 0, 0, -1, 0, -2]
drop	-1.1	0.53852	[0, -1, -1, -1, -2, -1, -2, -1, -1, -1]
drown	-2.7	1.00499	[-4, -2, -2, -4, -4, -2, -3, -1, -3, -2]
drowned	-2.9	0.7	[-2, -3, -3, -3, -2, -4, -4, -2, -3, -3]
drowns	-2.2	1.6	[-3, -3, -3, -4, -2, -3, -1, -2, 2, -3]
drunk	-1.4	0.91652	[-3, -1, 0, -2, 0, -1, -1, -2, -2, -2]
dubious	-1.5	0.5	[-1, -2, -2, -1, -1, -2, -1, -1, -2, -2]
dud	-1.0	0.89443	[-1, -1, -1, 0, -3, 0, -1, 0, -1, -2]
dull	-1.7	0.45826	[-2, -2, -2, -1, -2, -2, -2, -1, -1, -2]
dullard	-1.6	0.66332	[-2, -1, -1, -2, -2, -1, -1, -2, -1, -3]
dullards	-1.8	0.87178	[-1, -3, -1, -1, -3, -1, -1, -2, -3, -2]
dulled	-1.5	0.5	[-2, -1, -2, -1, -1, -1, -2, -2, -1, -2]
duller	-1.7	0.64031	[-3, -1, -2, -2, -2, -1, -2, -1, -1, -2]
dullest	-1.7	1.00499	[-1, -4, -1, -1, -2, -3, -2, -1, -1, -1]
dulling	-1.1	0.7	[-1, -2, 0, -1, -2, -2, 0, -1, -1, -1]
dullish	-1.1	0.53852	[-2, -1, -1, -1, -1, -1, -1, -1, 0, -2]
dullness	-1.4	0.8	[-1, -1, -1, -1, -1, -1, -3, -1, -3, -1]
dullnesses	-1.9	1.04403	[-3, -2, -1, -1, -3, -1, -4, -1, -2, -1]
dulls	-1.0	0.44721	[-1, -1, -1, -1, -1, -1, 0, -1, -1, -2]
dullsville	-2.4	0.8	[-2, -2, -4, -3, -2, -2, -3, -2, -3, -1]
dully	-1.1	0.3	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -2]
dumb	-2.3	0.9	[-4, -2, -2, -2, -2, -2, -4, -2, -2, -1]
dumbass	-2.6	1.0198	[-3, -3, -4, -4, -1, -2, -3, -3, -1, -2]
dumbbell	-0.8	0.9798	[0, -1, -3, -2, 0, -1, 0, 0, -1, 0]
dumbbells	-0.2	0.4	[0, -1, 0, 0, 0, 0, 0, -1, 0, 0]
dumbcane	-0.3	0.45826	[0, 0, 0, 0, 0, -1, -1, 0, -1, 0]
dumbcanes	-0.6	1.2	[0, 0, -1, -1, -1, 2, 0, -1, -3, -1]
dumbed	-1.4	0.4899	[-2, -1, -2, -2, -2, -1, -1, -1, -1, -1]
dumber	-1.5	0.5	[-2, -1, -2, -1, -2, -1, -1, -2, -2, -1]
dumbest	-2.3	1.00499	[-3, -1, -3, -4, -2, -1, -2, -3, -1, -3]
dumbfound	-0.1	1.92094	[3, -2, -1, -1, 1, 1, -3, 3, -1, -1]
dumbfounded	-1.6	1.11355	[-2, 0, -2, 0, -2, -1, -4, -1, -2, -2]
dumbfounder	-1.0	0.89443	[-2, 0, 0, 0, -2, -1, -2, 0, -2, -1]
dumbfounders	-1.0	0.89443	[-1, -3, -1, 0, -2, 0, -1, 0, -1, -1]
dumbfounding	-0.8	0.74833	[-1, -2, 0, -1, -1, 0, 0, -1, 0, -2]
dumbfounds	-0.3	1.26886	[0, -1, -1, 0, 0, -1, -1, -2, 0, 3]
dumbhead	-2.6	0.66332	[-3, -4, -3, -2, -2, -3, -3, -2, -2, -2]
dumbheads	-1.9	0.83066	[-2, -2, -2, -1, -2, -1, -2, -4, -1, -2]
dumbing	-0.5	1.0247	[-1, 2, -1, 0, -1, -2, -1, 0, 0, -1]
dumbly	-1.3	1.00499	[-2, -1, -2, -3, 1, -2, -1, -1, -1, -1]
dumbness	-1.9	0.53852	[-2, -2, -2, -2, -2, -3, -2, -2, -1, -1]
dumbs	-1.5	0.67082	[-1, -1, -1, -3, -2, -1, -2, -1, -2, -1]
dumbstruck	-1.0	1.34164	[-1, -2, 0, 0, -2, 1, -3, 1, -2, -2]
dumbwaiter	0.2	1.07703	[0, 0, 0, 0, 2, 0, 2, 0, -2, 0]
dumbwaiters	-0.1	0.3	[0, 0, 0, 0, 0, 0, -1, 0, 0, 0]
dump	-1.6	0.91652	[-3, -2, -1, -2, -1, -3, -2, -1, -1, 0]
dumpcart	-0.6	0.8	[0, -2, -1, 0, 0, 0, -1, 0, -2, 0]
dumped	-1.7	0.78102	[-2, -3, -2, -1, -1, -1, -3, -2, -1, -1]
dumper	-1.2	0.87178	[-2, -3, -1, -1, -1, -2, 0, -1, 0, -1]
dumpers	-0.8	0.6	[0, 0, -2, -1, -1, 0, -1, -1, -1, -1]
dumpier	-1.4	0.66332	[-2, -1, -2, -1, -1, -2, 0, -2, -1, -2]
dumpiest	-1.6	1.35647	[-1, -2, -2, -3, -2, -4, 1, 0, -1, -2]
dumpiness	-1.2	0.6	[-2, -1, 0, -1, -1, -2, -1, -2, -1, -1]
dumping	-1.3	1.1	[-3, -2, -2, 0, -2, -1, -1, 1, -1, -2]
dumpings	-1.1	0.83066	[-2, 0, -1, -1, 0, -1, -2, -2, 0, -2]
dumpish	-1.8	0.6	[-2, -1, -2, -3, -2, -2, -1, -2, -2, -1]
dumpling	0.4	0.91652	[0, 0, 1, 0, -1, 2, 2, 0, 0, 0]
dumplings	-0.3	1.26886	[0, 0, 0, 0, 2, 0, 0, 0, -2, -3]
dumps	-1.7	0.9	[-3, -2, -3, -1, -1, -3, -1, -1, -1, -1]
dumpster	-0.6	0.91652	[0, -2, 0, -2, 0, 0, 0, -2, 0, 0]
dumpsters	-1.0	0.89443	[0, -1, 0, -2, -2, -2, 0, 0, -1, -2]
dumpy	-1.7	0.78102	[-3, -2, -3, -1, -1, -2, -2, -1, -1, -1]
dupe	-1.5	0.5	[-2, -2, -1, -1, -2, -1, -2, -1, -2, -1]
duped	-1.8	0.4	[-2, -2, -1, -2, -2, -2, -2, -1, -2, -2]
dwell	0.5	0.92195	[2, 1, -1, 0, -1, 1, 1, 1, 1, 0]
dwelled	0.4	0.66332	[2, 0, 0, 0, 0, 1, 1, 0, 0, 0]
dweller	0.3	0.64031	[2, 0, 0, 0, 0, 0, 1, 0, 0, 0]
dwellers	-0.3	0.9	[-3, 0, 0, 0, 0, 0, 0, 0, 0, 0]
dwelling	0.1	0.53852	[0, 1, 0, 1, 0, 0, 0, -1, 0, 0]
dwells	-0.1	0.53852	[0, 0, 0, 0, -1, 1, -1, 0, 0, 0]
dynamic	1.6	0.8	[1, 1, 1, 3, 1, 1, 2, 3, 2, 1]
dynamical	1.2	0.87178	[1, 2, 0, 1, 0, 2, 2, 2, 2, 0]
dynamically	1.5	1.0247	[2, 0, 3, 2, 0, 0, 2, 2, 2, 2]
dynamics	1.1	1.13578	[2, 3, 0, 0, 0, 2, 2, 0, 2, 0]
dynamism	1.6	1.11355	[0, 2, 0, 2, 0, 2, 3, 3, 2, 2]
dynamisms	1.2	0.9798	[2, 0, 2, 0, 0, 2, 2, 2, 0, 2]
dynamist	1.4	1.0198	[0, 2, 0, 2, 0, 1, 3, 2, 2, 2]
dynamistic	1.5	1.0247	[3, 1, 1, 2, 1, 3, 2, 0, 2, 0]
dynamists	0.9	0.83066	[1, 0, 0, 0, 0, 2, 2, 1, 2, 1]
dynamite	0.7	2.2383	[-3, 2, 3, 1, 2, 0, 2, 2, 2, -4]
dynamited	-0.9	1.04403	[0, 0, 0, -1, -1, 0, -2, 0, -2, -3]
dynamiter	-1.2	0.87178	[-1, 0, -1, -1, -2, 0, -1, -1, -2, -3]
dynamiters	0.4	1.42829	[0, 0, 0, -3, 1, 0, 2, 2, 2, 0]
dynamites	-0.3	1.73494	[0, 0, 4, -1, -1, 0, 0, 0, -2, -3]
dynamitic	0.9	1.3	[2, 0, 1, -2, 2, 1, 1, 3, 0, 1]
dynamiting	0.2	1.32665	[-2, 0, 0, 2, -1, -1, 0, 2, 2, 0]
dynamometer	0.3	0.64031	[0, 0, 0, 0, 1, 0, 0, 2, 0, 0]
dynamometers	0.3	0.45826	[0, 0, 0, 0, 0, 1, 0, 1, 1, 0]
dynamometric	0.3	0.9	[0, 0, 0, 0, 0, 2, 0, 0, 2, -1]
dynamometry	0.6	1.28062	[-2, 0, 0, 0, 0, 2, 2, 2, 0, 2]
dynamos	0.3	0.64031	[1, 0, 0, 0, 0, 0, 0, 0, 2, 0]
dynamotor	0.6	0.91652	[0, 2, 0, 0, 0, 0, 2, 0, 2, 0]
dysfunction	-1.8	0.6	[-2, -3, -2, -1, -2, -1, -2, -1, -2, -2]
eager	1.5	0.67082	[1, 3, 1, 2, 2, 2, 1, 1, 1, 1]
eagerly	1.6	0.66332	[0, 1, 2, 2, 2, 2, 2, 1, 2, 2]
eagerness	1.7	0.45826	[2, 2, 2, 2, 1, 1, 2, 2, 2, 1]
eagers	1.6	0.66332	[2, 2, 3, 1, 2, 1, 1, 1, 2, 1]
earnest	2.3	0.64031	[3, 2, 3, 1, 2, 2, 2, 3, 3, 2]
ease	1.5	0.92195	[1, 1, 1, 0, 2, 1, 2, 3, 3, 1]
eased	1.2	0.74833	[2, 0, 1, 0, 2, 2, 1, 1, 1, 2]
easeful	1.5	1.0247	[2, 1, 1, 2, 1, 0, 3, 2, 0, 3]
easefully	1.4	0.4899	[2, 2, 1, 1, 1, 1, 2, 2, 1, 1]
easel	0.3	0.45826	[0, 0, 0, 0, 1, 1, 0, 0, 0, 1]
easement	1.6	0.91652	[0, 1, 2, 3, 2, 1, 2, 1, 3, 1]
easements	0.4	1.11355	[0, 0, 0, 1, 2, -2, 1, 0, 2, 0]
eases	1.3	0.78102	[2, 0, 1, 0, 2, 2, 2, 1, 1, 2]
easier	1.8	0.9798	[1, 1, 2, 2, 4, 3, 2, 1, 1, 1]
easiest	1.8	1.07703	[2, 4, 1, 3, 2, 0, 2, 2, 1, 1]
easily	1.4	0.4899	[2, 1, 1, 2, 1, 2, 1, 2, 1, 1]
easiness	1.6	0.66332	[2, 1, 1, 2, 3, 2, 1, 1, 2, 1]
easing	1.0	0.63246	[0, 0, 2, 1, 1, 2, 1, 1, 1, 1]
easy	1.9	1.04403	[1, 4, 2, 1, 1, 3, 1, 3, 2, 1]
easygoing	1.3	0.45826	[1, 1, 1, 1, 1, 1, 2, 2, 2, 1]
easygoingness	1.5	0.67082	[1, 2, 1, 2, 1, 3, 1, 2, 1, 1]
ecstacy	3.3	1.18743	[4, 4, 3, 4, 4, 0, 3, 3, 4, 4]
ecstasies	2.3	1.34536	[3, 3, 2, 4, 3, 1, 3, -1, 2, 3]
ecstasy	2.9	1.75784	[4, 3, 3, 4, 4, 2, -2, 3, 4, 4]
ecstatic	2.3	1.34536	[3, 4, 3, 4, 1, 1, 1, 4, 1, 1]
ecstatically	2.8	1.93907	[3, 4, -1, 4, 4, 4, -1, 3, 4, 4]
ecstatics	2.9	0.83066	[1, 3, 4, 4, 3, 3, 3, 2, 3, 3]
eerie	-1.5	0.67082	[-1, -1, -2, -2, -1, -1, -2, -1, -3, -1]
eery	-0.9	1.04403	[-3, -1, -2, -1, -2, 0, 0, 0, 0, 0]
effective	2.1	0.83066	[2, 2, 2, 1, 3, 4, 2, 1, 2, 2]
effectively	1.9	0.7	[1, 2, 1, 2, 2, 1, 3, 3, 2, 2]
efficiencies	1.6	0.4899	[2, 1, 1, 2, 2, 2, 1, 2, 2, 1]
efficiency	1.5	0.5	[2, 1, 2, 2, 1, 2, 1, 1, 2, 1]
efficient	1.8	0.9798	[1, 2, 1, 1, 2, 3, 3, 0, 2, 3]
efficiently	1.7	0.78102	[1, 3, 2, 1, 3, 1, 1, 2, 2, 1]
effin	-2.3	1.18743	[0, -3, -3, -3, -2, -1, -4, -1, -3, -3]
egotism	-1.4	0.91652	[-2, -3, -1, -2, -2, 0, 0, -1, -2, -1]
egotisms	-1.0	0.7746	[-1, -1, -1, -1, -1, 0, 0, -1, -3, -1]
egotist	-2.3	0.9	[-2, -1, -2, -3, -4, -2, -3, -3, -1, -2]
egotistic	-1.4	1.0198	[-2, -1, -1, -1, -2, 1, -3, -2, -1, -2]
egotistical	-0.9	1.57797	[-1, -2, -2, -1, -2, 1, -3, 2, 1, -2]
egotistically	-1.8	0.87178	[-2, -1, -1, -2, -1, -3, -3, -1, -1, -3]
egotists	-1.7	0.78102	[-1, -2, 0, -2, -2, -2, -3, -1, -2, -2]
elated	3.2	0.74833	[2, 4, 4, 3, 4, 3, 3, 2, 3, 4]
elation	1.5	1.43178	[1, 2, -2, 2, 2, 3, 0, 3, 2, 2]
elegance	2.1	0.53852	[3, 2, 2, 1, 2, 2, 3, 2, 2, 2]
elegances	1.8	0.6	[2, 2, 1, 1, 2, 2, 2, 3, 2, 1]
elegancies	1.6	1.0198	[2, 1, 2, 1, 1, 0, 4, 1, 2, 2]
elegancy	2.1	0.53852	[3, 2, 2, 1, 2, 2, 3, 2, 2, 2]
elegant	2.1	0.83066	[2, 2, 2, 1, 4, 1, 2, 3, 2, 2]
elegantly	1.9	0.83066	[2, 1, 1, 3, 2, 2, 1, 3, 3, 1]
embarrass	-1.2	1.66132	[-2, -2, -3, -1, -2, -2, 2, 2, -2, -2]
embarrassable	-1.6	0.8	[-3, -2, -1, -3, -1, -1, -1, -2, -1, -1]
embarrassed	-1.5	0.67082	[-2, -2, -1, -2, -1, -3, -1, -1, -1, -1]
embarrassedly	-1.1	1.44568	[-2, -1, -2, -3, 1, -1, -1, -2, -2, 2]
embarrasses	-1.7	0.78102	[-2, -3, -1, -2, -1, -3, -2, -1, -1, -1]
embarrassing	-1.6	0.8	[-3, -1, -1, -1, -1, -2, -1, -2, -3, -1]
embarrassingly	-1.7	0.64031	[-2, -1, -1, -2, -1, -2, -1, -3, -2, -2]
embarrassment	-1.9	0.53852	[-2, -2, -1, -2, -2, -2, -2, -1, -3, -2]
embarrassments	-1.7	0.64031	[-2, -1, -2, -1, -1, -2, -2, -1, -2, -3]
embittered	-0.4	1.35647	[1, -2, -1, 1, -2, 2, 0, -1, 0, -2]
embrace	1.3	1.34536	[3, 2, 1, 3, 2, -1, 2, 1, -1, 1]
emergency	-1.6	2.05913	[-3, -3, -3, -3, -4, 2, 1, -1, 1, -3]
emotional	0.6	1.0198	[1, -1, 0, 0, 0, 2, 0, 2, 2, 0]
empathetic	1.7	1.1	[-1, 3, 2, 2, 2, 1, 3, 1, 2, 2]
emptied	-0.7	0.64031	[-1, 0, 0, 0, -1, -1, -1, -2, 0, -1]
emptier	-0.7	0.64031	[-1, 0, 0, 0, -1, -1, -1, -2, 0, -1]
emptiers	-0.7	0.78102	[0, 0, -1, 0, -1, -1, -2, 0, -2, 0]
empties	-0.7	0.64031	[-1, 0, 0, 0, -1, -1, -1, -2, 0, -1]
emptiest	-1.8	1.07703	[-1, -2, -2, -1, -1, -2, -3, 0, -4, -2]
emptily	-1.0	1.41421	[-2, 2, -1, -1, -1, 0, -4, -1, -1, -1]
emptiness	-1.9	0.7	[-2, -2, -3, -2, -2, -1, -1, -3, -1, -2]
emptinesses	-1.5	1.11803	[-1, -1, -3, -1, -1, -1, -2, 0, -4, -1]
emptins	-0.3	0.45826	[0, 0, 0, -1, 0, 0, 0, -1, -1, 0]
empty	-0.8	0.74833	[-1, -1, -1, -1, -1, -1, -2, 0, -1, 1]
emptying	-0.6	1.0198	[2, -1, -1, 0, -1, 0, -1, -1, -2, -1]
enchanted	1.6	0.8	[1, 3, 1, 2, 1, 3, 1, 2, 1, 1]
encourage	2.3	0.78102	[2, 1, 3, 2, 3, 4, 2, 2, 2, 2]
encouraged	1.5	0.5	[1, 2, 2, 2, 2, 2, 1, 1, 1, 1]
encouragement	1.8	0.9798	[2, 1, 2, 1, 1, 3, 1, 2, 4, 1]
encouragements	2.1	0.7	[3, 2, 3, 2, 1, 2, 3, 2, 2, 1]
encourager	1.5	0.5	[2, 1, 2, 1, 1, 2, 1, 1, 2, 2]
encouragers	1.5	0.5	[2, 2, 1, 1, 2, 2, 1, 1, 2, 1]
encourages	1.9	0.53852	[2, 2, 1, 2, 2, 1, 2, 2, 2, 3]
encouraging	2.4	0.66332	[2, 3, 2, 3, 3, 2, 3, 2, 1, 3]
encouragingly	2.0	0.7746	[1, 1, 1, 3, 2, 2, 2, 3, 2, 3]
endorse	1.3	0.9	[0, 1, 0, 1, 3, 2, 2, 2, 1, 1]
endorsed	1.0	0.89443	[1, 2, 0, 1, 1, 0, 1, 0, 3, 1]
endorsement	1.3	0.9	[0, 1, 2, 2, 1, 2, 0, 1, 3, 1]
endorses	1.4	0.4899	[1, 2, 1, 2, 1, 1, 1, 2, 2, 1]
enemies	-2.2	0.6	[-2, -3, -1, -2, -2, -3, -2, -3, -2, -2]
enemy	-2.5	0.92195	[-3, -2, -3, -3, -3, -4, -1, -3, -1, -2]
energetic	1.9	0.53852	[2, 1, 3, 2, 2, 2, 1, 2, 2, 2]
energetically	1.8	0.6	[2, 2, 1, 1, 2, 2, 3, 1, 2, 2]
energetics	0.3	0.64031	[1, 0, 0, 0, 0, 0, 2, 0, 0, 0]
energies	0.9	1.04403	[1, 0, 0, 2, 0, 1, 3, 2, 0, 0]
energise	2.2	0.4	[2, 2, 2, 2, 2, 2, 3, 2, 3, 2]
energised	2.1	0.53852	[2, 3, 1, 2, 2, 2, 3, 2, 2, 2]
energises	2.2	0.6	[3, 3, 2, 2, 1, 3, 2, 2, 2, 2]
energising	1.9	0.7	[2, 3, 1, 2, 2, 3, 2, 1, 1, 2]
energization	1.6	0.66332	[1, 2, 1, 3, 1, 1, 2, 2, 2, 1]
energizations	1.5	1.11803	[1, 0, 3, 1, 3, 0, 1, 1, 2, 3]
energize	2.1	0.7	[2, 2, 2, 1, 3, 2, 3, 2, 3, 1]
energized	2.3	0.64031	[3, 2, 3, 3, 3, 2, 2, 2, 1, 2]
energizer	2.1	0.53852	[3, 2, 2, 2, 2, 2, 2, 3, 1, 2]
energizers	1.7	0.9	[2, 0, 2, 3, 3, 1, 1, 2, 2, 1]
energizes	2.1	0.53852	[3, 2, 3, 2, 2, 2, 2, 2, 1, 2]
energizing	2.0	0.63246	[3, 3, 2, 1, 2, 2, 1, 2, 2, 2]
energy	1.1	0.83066	[0, 2, 0, 2, 1, 1, 2, 1, 2, 0]
engage	1.4	0.8	[1, 2, 3, 2, 1, 1, 0, 1, 2, 1]
engaged	1.7	1.1	[1, 1, 2, 2, 1, 0, 2, 3, 4, 1]
engagement	2.0	1.34164	[0, 0, 3, 4, 4, 2, 1, 2, 2, 2]
engagements	0.6	0.8	[1, 0, 0, 2, 0, 2, 0, 0, 1, 0]
engager	1.1	0.7	[1, 1, 0, 2, 1, 0, 2, 1, 2, 1]
engagers	1.0	0.7746	[1, 1, 1, 0, 2, 1, 0, 2, 2, 0]
engages	1.0	0.7746	[1, 1, 0, 2, 1, 0, 1, 2, 2, 0]
engaging	1.4	0.4899	[2, 2, 1, 1, 2, 1, 1, 1, 1, 2]
engagingly	1.5	0.67082	[1, 2, 3, 1, 1, 1, 1, 1, 2, 2]
engrossed	0.6	1.49666	[0, 2, 0, 2, -2, 2, 3, -1, 0, 0]
enjoy	2.2	0.6	[3, 2, 2, 2, 3, 2, 2, 3, 2, 1]
enjoyable	1.9	0.53852	[3, 2, 2, 1, 2, 1, 2, 2, 2, 2]
enjoyableness	1.9	1.13578	[2, 2, 2, 2, 1, 3, 3, 3, -1, 2]
enjoyably	1.8	0.4	[2, 2, 2, 1, 2, 1, 2, 2, 2, 2]
enjoyed	2.3	0.64031	[2, 2, 1, 3, 3, 3, 2, 2, 2, 3]
enjoyer	2.2	0.6	[2, 2, 1, 3, 3, 2, 2, 2, 2, 3]
enjoyers	2.2	0.74833	[2, 4, 2, 2, 2, 2, 2, 3, 2, 1]
enjoying	2.4	0.66332	[2, 2, 2, 3, 3, 3, 1, 3, 2, 3]
enjoyment	2.6	0.4899	[2, 3, 2, 3, 2, 3, 2, 3, 3, 3]
enjoyments	2.0	0.7746	[3, 1, 1, 3, 2, 1, 2, 2, 2, 3]
enjoys	2.3	0.45826	[2, 3, 2, 2, 2, 3, 2, 3, 2, 2]
enlighten	2.3	1.1	[2, 2, 1, 3, 2, 1, 1, 4, 3, 4]
enlightened	2.2	0.87178	[4, 2, 3, 1, 2, 2, 1, 3, 2, 2]
enlightening	2.3	0.64031	[3, 2, 2, 2, 2, 2, 2, 4, 2, 2]
enlightens	1.7	1.00499	[2, 1, 1, 1, 1, 2, 4, 1, 3, 1]
ennui	-1.2	0.6	[-1, -1, -1, -2, -1, -1, -2, -1, 0, -2]
enrage	-2.6	0.91652	[-3, -3, -3, -4, -1, -1, -3, -2, -3, -3]
enraged	-1.7	1.79165	[-3, -3, -3, -3, 2, -1, -3, -1, 1, -3]
enrages	-1.8	1.6	[-3, -3, -3, -3, 1, -1, -3, -1, 1, -3]
enraging	-2.8	0.74833	[-4, -2, -3, -2, -2, -3, -3, -2, -4, -3]
enrapture	3.0	0.63246	[2, 4, 3, 3, 4, 3, 3, 2, 3, 3]
enslave	-3.1	0.9434	[-3, -4, -2, -4, -4, -2, -4, -2, -4, -2]
enslaved	-1.7	2.41039	[3, -3, -3, -3, -4, -4, -4, 1, -1, 1]
enslaves	-1.6	2.15407	[2, -2, -3, -2, -4, -4, -4, 1, -1, 1]
ensure	1.6	0.91652	[2, 1, 3, 1, 1, 2, 3, 2, 0, 1]
ensuring	1.1	0.9434	[0, 1, 3, 1, 1, 2, 1, 0, 2, 0]
enterprising	2.3	0.78102	[3, 2, 1, 3, 3, 2, 1, 2, 3, 3]
entertain	1.3	0.64031	[1, 2, 1, 1, 2, 1, 2, 0, 1, 2]
entertained	1.7	0.64031	[1, 2, 2, 1, 2, 1, 1, 3, 2, 2]
entertainer	1.6	1.2	[1, 4, 2, 2, 0, 0, 1, 3, 1, 2]
entertainers	1.0	0.7746	[0, 1, 2, 2, 0, 0, 1, 1, 2, 1]
entertaining	1.9	0.83066	[1, 2, 1, 1, 3, 2, 3, 2, 3, 1]
entertainingly	1.9	0.53852	[2, 1, 2, 3, 2, 2, 1, 2, 2, 2]
entertainment	1.8	0.9798	[2, 0, 4, 2, 2, 1, 2, 2, 1, 2]
entertainments	2.3	1.18743	[3, 3, 3, 2, 1, 0, 3, 4, 1, 3]
entertains	2.4	0.66332	[2, 2, 2, 2, 2, 3, 4, 3, 2, 2]
enthral	0.4	1.42829	[2, 2, 0, 2, 0, -1, -2, 2, 0, -1]
enthuse	1.6	0.66332	[1, 2, 1, 1, 3, 1, 2, 2, 2, 1]
enthused	2.0	0.63246	[3, 3, 1, 2, 2, 2, 2, 1, 2, 2]
enthuses	1.7	0.78102	[2, 3, 1, 2, 1, 3, 1, 1, 1, 2]
enthusiasm	1.9	0.9434	[3, 3, 3, 2, 1, 0, 2, 1, 2, 2]
enthusiasms	2.0	0.89443	[1, 3, 2, 2, 3, 2, 0, 2, 3, 2]
enthusiast	1.5	0.67082	[1, 2, 2, 2, 0, 1, 1, 2, 2, 2]
enthusiastic	2.2	0.9798	[1, 2, 3, 4, 2, 3, 2, 1, 1, 3]
enthusiastically	2.6	0.66332	[3, 3, 3, 2, 3, 3, 3, 3, 2, 1]
enthusiasts	1.4	0.91652	[1, 1, 0, 3, 3, 2, 1, 1, 1, 1]
enthusing	1.9	0.7	[2, 1, 2, 1, 2, 3, 2, 1, 2, 3]
entitled	1.1	0.83066	[2, 2, 1, 1, 2, 1, 1, -1, 1, 1]
entrusted	0.8	1.46969	[3, 0, 2, 2, 1, 1, -1, 0, -2, 2]
envied	-1.1	0.83066	[-1, -2, -2, 1, -2, -1, -1, -1, -1, -1]
envier	-1.0	0.7746	[-1, -2, -2, -1, -1, 1, -1, -1, -1, -1]
enviers	-1.1	1.13578	[-3, -1, 0, -3, -1, -1, -1, -1, 1, -1]
envies	-0.8	0.9798	[-1, -2, -2, 1, -1, 1, -1, -1, -1, -1]
envious	-1.1	0.83066	[-2, -1, -1, -1, -2, -1, -1, 1, -2, -1]
envy	-1.1	0.83066	[-2, -1, -1, -2, -1, -1, -1, 1, -1, -2]
envying	-0.8	1.32665	[-1, -1, -1, -1, -3, 2, -2, -1, 1, -1]
envyingly	-1.3	1.55242	[-2, 3, -2, -2, -1, -3, -1, -1, -2, -2]
erroneous	-1.8	0.6	[-2, -3, -2, -2, -2, -2, -1, -1, -1, -2]
error	-1.7	0.64031	[-2, -1, -2, -1, -2, -1, -1, -2, -3, -2]
errors	-1.4	0.66332	[-2, -1, -2, 0, -2, -2, -1, -1, -1, -2]
escape	0.7	1.00499	[2, 0, 0, 1, 0, 1, 0, 3, 0, 0]
escapes	0.5	1.36015	[4, 1, 1, 0, -1, 0, -1, 0, 1, 0]
escaping	0.2	1.46969	[-2, 2, -1, 0, 1, 0, 2, 2, -2, 0]
esteemed	1.9	0.83066	[3, 2, 1, 2, 3, 1, 1, 2, 3, 1]
ethical	2.3	0.78102	[3, 3, 3, 3, 2, 2, 1, 3, 2, 1]
euphoria	3.3	0.9	[4, 4, 3, 3, 3, 4, 4, 4, 1, 3]
euphoric	3.2	0.87178	[3, 4, 3, 3, 3, 4, 4, 4, 1, 3]
eviction	-2.0	0.63246	[-2, -2, -3, -2, -3, -2, -1, -2, -1, -2]
evil	-3.4	0.91652	[-4, -4, -4, -3, -3, -4, -1, -4, -3, -4]
evildoer	-3.1	0.7	[-2, -3, -3, -3, -4, -4, -3, -2, -3, -4]
evildoers	-2.4	0.4899	[-3, -3, -2, -2, -2, -2, -2, -2, -3, -3]
evildoing	-3.1	0.7	[-4, -4, -3, -3, -3, -4, -2, -3, -2, -3]
evildoings	-2.5	1.0247	[-3, -1, -1, -3, -4, -2, -4, -2, -2, -3]
eviler	-2.1	1.13578	[-2, -1, -3, -2, -4, -3, -1, -2, 0, -3]
evilest	-2.5	1.0247	[-3, -4, -1, -3, -2, -3, -1, -4, -2, -2]
eviller	-2.9	0.83066	[-3, -3, -4, -2, -2, -3, -2, -4, -2, -4]
evillest	-3.3	0.78102	[-3, -4, -2, -3, -4, -2, -4, -4, -3, -4]
evilly	-3.4	0.8	[-2, -4, -4, -4, -3, -4, -4, -4, -3, -2]
evilness	-3.1	1.04403	[-3, -4, -4, -4, -4, -2, -3, -2, -1, -4]
evils	-2.7	0.78102	[-3, -2, -2, -4, -4, -2, -3, -2, -3, -2]
exaggerate	-0.6	0.66332	[-1, -1, -1, 0, -1, 0, 1, -1, -1, -1]
exaggerated	-0.4	1.2	[-1, -1, -1, -1, -1, 2, 1, 1, -2, -1]
exaggerates	-0.6	1.28062	[-1, -1, -1, -1, -1, 1, 0, 2, -3, -1]
exaggerating	-0.7	0.9	[-1, -2, 0, -1, 0, 0, -2, -1, 1, -1]
exasperated	-1.8	1.53623	[-4, -3, -3, -1, -1, -1, 1, -1, -4, -1]
excel	2.0	1.0	[3, 0, 2, 3, 1, 1, 3, 3, 2, 2]
excelled	2.2	0.87178	[1, 2, 2, 2, 3, 2, 4, 3, 2, 1]
excellence	3.1	0.9434	[4, 3, 4, 3, 2, 3, 1, 4, 3, 4]
excellences	2.5	0.92195	[4, 2, 2, 2, 4, 3, 2, 2, 3, 1]
excellencies	2.4	0.4899	[3, 2, 3, 3, 2, 2, 2, 2, 3, 2]
excellency	2.5	0.80623	[4, 2, 3, 3, 2, 3, 1, 3, 2, 2]
excellent	2.7	0.64031	[2, 3, 3, 3, 3, 2, 3, 2, 2, 4]
excellently	3.1	0.7	[4, 3, 3, 3, 2, 3, 3, 4, 4, 2]
excelling	2.5	0.67082	[2, 2, 3, 3, 3, 2, 2, 4, 2, 2]
excels	2.5	0.92195	[4, 2, 4, 2, 2, 1, 2, 3, 3, 2]
excelsior	0.7	0.64031	[1, 0, 0, 2, 0, 1, 1, 1, 1, 0]
excitabilities	1.5	1.0247	[2, 0, 1, 1, 3, 1, 2, 3, 2, 0]
excitability	1.2	0.87178	[0, 1, 1, 0, 1, 2, 3, 1, 2, 1]
excitable	1.5	0.92195	[2, 3, 1, 0, 1, 2, 2, 0, 2, 2]
excitableness	1.0	1.09545	[0, 0, 2, 0, 2, 0, 2, 0, 1, 3]
excitant	1.8	1.16619	[1, 0, 1, 3, 2, 0, 3, 3, 2, 3]
excitants	1.2	0.9798	[1, 0, 1, 2, 2, 2, 1, -1, 2, 2]
excitation	1.8	0.87178	[2, 0, 3, 1, 3, 2, 2, 2, 1, 2]
excitations	1.8	1.16619	[3, 3, -1, 2, 2, 2, 1, 1, 3, 2]
excitative	0.3	0.78102	[0, 1, 1, 0, 0, 0, 2, 0, -1, 0]
excitatory	1.1	1.7	[-1, 2, 2, 1, 2, 2, 2, 3, -3, 1]
excite	2.1	1.22066	[1, 2, 2, 1, 2, 0, 4, 4, 3, 2]
excited	1.4	0.4899	[1, 1, 2, 1, 2, 1, 2, 1, 1, 2]
excitedly	2.3	0.9	[3, 3, 2, 3, 1, 3, 1, 3, 1, 3]
excitement	2.2	0.4	[2, 2, 2, 3, 3, 2, 2, 2, 2, 2]
excitements	1.9	0.53852	[2, 1, 2, 3, 2, 2, 2, 2, 2, 1]
exciter	1.9	0.9434	[3, 2, 3, 1, 0, 1, 2, 3, 2, 2]
exciters	1.4	1.42829	[1, 2, 0, 1, 2, 4, 0, -1, 3, 2]
excites	2.1	0.83066	[2, 3, 3, 2, 0, 2, 2, 3, 2, 2]
exciting	2.2	0.87178	[3, 2, 1, 1, 1, 3, 3, 3, 2, 3]
excitingly	1.9	0.9434	[3, 2, 3, 0, 1, 2, 1, 2, 2, 3]
exciton	0.3	0.64031	[2, 0, 0, 0, 0, 0, 1, 0, 0, 0]
excitonic	0.2	0.6	[0, 0, 0, 0, 2, 0, 0, 0, 0, 0]
excitons	0.8	0.6	[1, 2, 0, 1, 1, 0, 0, 1, 1, 1]
excitor	0.5	0.67082	[2, 0, 0, 0, 1, 1, 1, 0, 0, 0]
exclude	-0.9	1.13578	[-1, -2, -1, -3, -1, 1, -1, -1, 1, -1]
excluded	-1.4	1.62481	[-2, -1, -3, -3, -2, -3, -2, 1, -1, 2]
exclusion	-1.2	1.249	[-2, -2, -3, -1, -2, -1, -1, -1, 2, -1]
exclusive	0.5	0.92195	[0, 0, 0, -1, 2, 0, 1, 2, 1, 0]
excruciate	-2.7	0.64031	[-2, -3, -2, -3, -3, -2, -4, -3, -2, -3]
excruciated	-1.3	1.9	[-4, -1, -4, 0, 2, -2, -1, -1, -3, 1]
excruciates	-1.0	2.19089	[-4, 1, -4, 0, 1, -3, -1, 1, -3, 2]
excruciating	-3.3	0.9	[-3, -4, -4, -4, -4, -4, -4, -2, -2, -2]
excruciatingly	-2.9	1.04403	[-2, -4, -3, -2, -4, -1, -2, -3, -4, -4]
excruciation	-3.4	0.66332	[-4, -3, -2, -4, -4, -3, -4, -3, -3, -4]
excruciations	-1.9	1.51327	[-3, -3, -2, -4, -1, -1, 1, -4, -1, -1]
excuse	0.3	1.73494	[0, 0, 3, -1, 0, -1, -2, 0, 4, 0]
exempt	0.4	0.91652	[1, 0, 0, 0, 2, -1, 0, 0, 2, 0]
exhaust	-1.2	0.87178	[0, -1, 0, -1, -1, -3, -2, -2, -1, -1]
exhausted	-1.5	1.28452	[-2, -1, 2, -2, -3, -2, -2, -1, -2, -2]
exhauster	-1.3	0.64031	[-1, -1, 0, -1, -2, -1, -1, -2, -2, -2]
exhausters	-1.3	0.45826	[-2, -1, -2, -1, -1, -2, -1, -1, -1, -1]
exhaustibility	-0.8	1.07703	[0, -2, -3, 0, 1, -1, -1, -1, 0, -1]
exhaustible	-1.0	0.63246	[-1, -1, 0, -1, -2, -2, -1, 0, -1, -1]
exhausting	-1.5	0.5	[-1, -2, -1, -1, -2, -1, -2, -2, -1, -2]
exhaustion	-1.5	0.92195	[-2, -1, 1, -2, -2, -2, -2, -1, -2, -2]
exhaustions	-1.1	0.9434	[-1, -3, -2, -1, -1, -1, -1, 1, -1, -1]
exhaustive	-0.5	0.67082	[-1, -1, 0, -1, -2, 0, 0, 0, 0, 0]
exhaustively	-0.7	0.78102	[-2, 0, -1, -1, -1, -2, 0, 0, 0, 0]
exhaustiveness	-1.1	1.3	[-2, -2, 0, -1, -1, 1, -4, -1, 0, -1]
exhaustless	0.2	1.8868	[1, 1, 0, 2, 3, 0, -2, 2, -2, -3]
exhaustlessness	0.9	1.75784	[2, 2, 1, 1, -4, 2, 1, 2, 0, 2]
exhausts	-1.1	0.53852	[-2, -1, 0, -1, -1, -2, -1, -1, -1, -1]
exhilarated	3.0	0.63246	[3, 3, 3, 3, 2, 2, 4, 4, 3, 3]
exhilarates	2.8	1.07703	[4, 3, 3, 3, 0, 2, 3, 4, 3, 3]
exhilarating	1.7	1.61555	[3, 4, 3, 2, -1, 1, 1, 3, -1, 2]
exonerate	1.8	0.74833	[2, 2, 2, 2, 3, 2, 1, 0, 2, 2]
exonerated	1.8	1.83303	[3, -2, 2, 3, 2, 4, 3, 1, -1, 3]
exonerates	1.6	1.90788	[3, -2, 1, 2, 3, 4, 0, 3, -1, 3]
exonerating	1.0	1.41421	[2, -2, 3, 0, 0, 2, 0, 2, 2, 1]
expand	1.3	0.64031	[0, 2, 2, 1, 1, 1, 2, 1, 2, 1]
expands	0.4	0.66332	[0, 1, 0, 0, 0, 0, 0, 0, 2, 1]
expel	-1.9	1.44568	[0, -4, -3, -1, -3, -1, 0, -2, -4, -1]
expelled	-1.0	1.94936	[-1, -2, 2, -2, -4, -3, -2, 1, 2, -1]
expelling	-1.6	1.49666	[-2, -2, -2, -2, -2, -3, -4, -1, 1, 1]
expels	-1.6	1.11355	[-4, -2, 0, -3, -1, -1, -1, -2, -1, -1]
exploit	-0.4	1.62481	[-2, -1, -1, 2, -2, -2, 2, -1, 2, -1]
exploited	-2.0	1.0	[-3, -3, -1, -1, -1, -4, -2, -2, -1, -2]
exploiting	-1.9	1.22066	[0, -3, -3, 0, -2, -3, -1, -1, -3, -3]
exploits	-1.4	0.8	[-2, -2, -2, -1, -1, 0, -2, 0, -2, -2]
exploration	0.9	0.7	[2, 2, 0, 1, 1, 0, 1, 0, 1, 1]
explorations	0.3	1.1	[1, 1, 0, 0, 1, 2, 1, -2, -1, 0]
expose	-0.6	0.8	[-1, -1, 0, 0, -2, 1, -1, 0, -1, -1]
exposed	-0.3	1.18743	[-2, -1, 0, 2, 0, 0, -1, 1, -2, 0]
exposes	-0.5	0.92195	[-1, -1, 0, 2, -1, 0, -1, -1, -1, -1]
exposing	-1.1	0.7	[-2, -2, -1, -1, 0, -2, -1, -1, 0, -1]
extend	0.7	0.78102	[2, 0, 0, 0, 1, 2, 1, 1, 0, 0]
extends	0.5	0.80623	[2, 0, 2, 0, 0, 0, 1, 0, 0, 0]
exuberant	2.8	0.6	[2, 3, 2, 3, 2, 4, 3, 3, 3, 3]
exultant	3.0	1.18322	[4, 4, 3, 0, 3, 4, 3, 3, 2, 4]
exultantly	1.4	1.49666	[3, 2, 4, 1, 1, 2, -2, 1, 1, 1]
fab	2.0	1.0	[2, 1, 1, 3, 1, 2, 2, 4, 3, 1]
fabulous	2.4	0.8	[2, 2, 3, 4, 3, 3, 1, 2, 2, 2]
fabulousness	2.8	1.07703	[4, 1, 4, 3, 1, 3, 3, 2, 4, 3]
fad	0.9	0.83066	[2, 0, 1, 1, 1, 0, 0, 2, 2, 0]
fag	-2.1	0.83066	[-3, -1, -2, -4, -2, -2, -2, -1, -2, -2]
faggot	-3.4	0.8	[-2, -4, -4, -3, -2, -4, -4, -4, -3, -4]
faggots	-3.2	0.9798	[-2, -4, -4, -4, -3, -4, -4, -3, -1, -3]
fail	-2.5	0.67082	[-2, -3, -3, -3, -4, -2, -2, -2, -2, -2]
failed	-2.3	0.9	[-2, -3, -1, -2, -2, -1, -3, -3, -4, -2]
failing	-2.3	1.1	[-2, -3, -3, -3, -4, -1, -2, -2, 0, -3]
failingly	-1.4	0.8	[-1, -3, -2, -1, 0, -1, -2, -1, -2, -1]
failings	-2.2	1.07703	[-2, -2, -3, -4, -1, -2, -1, -1, -4, -2]
faille	0.1	0.3	[0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
fails	-1.8	0.74833	[-2, -3, -2, -3, -2, -2, -1, -1, -1, -1]
failure	-2.3	1.00499	[-3, -1, -3, -2, -1, -2, -3, -1, -4, -3]
failures	-2.0	0.63246	[-1, -2, -1, -2, -3, -2, -2, -3, -2, -2]
fainthearted	-0.3	1.34536	[3, -1, -1, -1, -2, -1, 0, 1, -1, 0]
fair	1.3	1.00499	[0, 1, 1, 2, 4, 1, 1, 1, 1, 1]
faith	1.8	0.6	[1, 3, 2, 2, 1, 2, 2, 2, 1, 2]
faithed	1.3	1.00499	[3, 2, 1, 2, 1, 2, -1, 1, 1, 1]
faithful	1.9	0.83066	[1, 3, 2, 2, 1, 1, 3, 3, 2, 1]
faithfully	1.8	1.07703	[3, 1, 2, 2, 4, 1, 0, 1, 2, 2]
faithfulness	1.9	0.53852	[1, 2, 2, 3, 2, 2, 2, 1, 2, 2]
faithless	-1.0	0.89443	[-1, -1, -1, -2, -2, -1, -1, 0, 1, -2]
faithlessly	-0.9	1.51327	[-1, -2, -1, -3, -2, -1, 3, 0, -1, -1]
faithlessness	-1.8	1.249	[-3, -2, 1, -2, -2, -1, -2, -1, -4, -2]
faiths	1.8	0.9798	[2, 3, 1, 1, 3, 3, 0, 2, 2, 1]
fake	-2.1	0.9434	[-2, -2, -1, -1, -1, -3, -3, -4, -2, -2]
fakes	-1.8	1.07703	[-2, -3, -2, -2, -3, 1, -2, -2, -1, -2]
faking	-1.8	0.87178	[-3, -1, -2, -1, -2, -2, -3, -2, 0, -2]
fallen	-1.5	0.80623	[-1, -1, -2, -2, -1, -3, 0, -1, -2, -2]
falling	-0.6	1.8	[-2, -2, -1, 0, -1, 3, -3, 2, 0, -2]
falsified	-1.6	0.91652	[-4, -1, -1, -2, -1, -1, -2, -2, -1, -1]
falsify	-2.0	0.7746	[-2, -1, -3, -3, -1, -2, -2, -2, -1, -3]
fame	1.9	1.13578	[0, 2, 1, 2, 2, 4, 1, 3, 3, 1]
fan	1.3	0.78102	[2, 0, 1, 1, 2, 1, 1, 3, 1, 1]
fantastic	2.6	0.91652	[1, 3, 3, 3, 2, 4, 4, 2, 2, 2]
fantastical	2.0	1.18322	[-1, 3, 2, 2, 2, 2, 3, 1, 3, 3]
fantasticalities	2.1	1.04403	[2, 4, 2, 2, 1, 1, 2, 4, 1, 2]
fantasticality	1.7	1.26886	[4, 1, 0, 2, 2, 1, 4, 1, 1, 1]
fantasticalness	1.3	1.9	[2, 3, -3, 0, 3, 2, 2, 3, -1, 2]
fantasticate	1.5	1.96214	[1, 2, -3, 1, 4, 1, 3, 4, 0, 2]
fantastico	0.4	1.49666	[0, 0, 2, 0, 2, -2, 1, -1, -1, 3]
farce	-1.7	0.45826	[-2, -2, -1, -2, -2, -1, -1, -2, -2, -2]
fascinate	2.4	1.0198	[4, 2, 2, 3, 1, 2, 3, 4, 1, 2]
fascinated	2.1	0.83066	[2, 2, 2, 3, 1, 2, 4, 2, 1, 2]
fascinates	2.0	0.44721	[2, 3, 1, 2, 2, 2, 2, 2, 2, 2]
fascination	2.2	0.74833	[2, 1, 3, 3, 2, 3, 2, 2, 1, 3]
fascinating	2.5	0.92195	[3, 3, 3, 4, 2, 3, 2, 3, 1, 1]
fascist	-2.6	0.8	[-3, -3, -2, -2, -2, -3, -1, -3, -4, -3]
fascists	-0.8	1.6	[-2, -3, -1, 1, -1, 2, -3, 1, -1, -1]
fatal	-2.5	1.62788	[-2, -3, -3, -4, -3, -3, 2, -4, -2, -3]
fatalism	-0.6	1.8	[0, 0, -3, -4, -1, 1, 0, 2, -2, 1]
fatalisms	-1.7	0.9	[-2, -4, -2, -2, -2, -1, -1, -1, -1, -1]
fatalist	-0.5	1.56525	[0, 0, -1, -4, -1, 2, 0, 1, -2, 0]
fatalistic	-1.0	1.34164	[-3, -1, -3, -1, 1, 0, 1, -2, -1, -1]
fatalists	-1.2	0.87178	[0, -2, -1, -2, -1, 0, -1, -1, -3, -1]
fatalities	-2.9	0.7	[-2, -3, -3, -4, -2, -2, -3, -4, -3, -3]
fatality	-3.5	0.67082	[-2, -4, -4, -4, -3, -4, -4, -4, -3, -3]
fatally	-3.2	0.74833	[-3, -2, -4, -2, -3, -4, -4, -3, -3, -4]
fatigue	-1.0	0.7746	[-2, -1, -1, -1, 1, -1, -2, -1, -1, -1]
fatigued	-1.4	0.4899	[-2, -1, -1, -2, -1, -1, -2, -2, -1, -1]
fatigues	-1.3	1.00499	[-2, -1, -1, -2, -1, 1, -3, -2, -1, -1]
fatiguing	-1.2	0.6	[-1, -2, -2, -1, 0, -2, -1, -1, -1, -1]
fatiguingly	-1.5	0.80623	[-1, 0, -3, -2, -1, -1, -2, -1, -2, -2]
fault	-1.7	0.64031	[-1, -2, -2, -2, -1, -1, -2, -2, -1, -3]
faulted	-1.4	0.4899	[-2, -2, -1, -2, -1, -1, -1, -1, -2, -1]
faultfinder	-0.8	1.32665	[-3, -2, -2, -1, 1, 1, 1, -1, -1, -1]
faultfinders	-1.5	0.80623	[-2, -2, -1, -3, -2, -1, 0, -1, -1, -2]
faultfinding	-2.1	0.83066	[-3, -2, -1, -2, -1, -3, -1, -3, -2, -3]
faultier	-2.1	0.7	[-3, -3, -3, -1, -2, -2, -2, -1, -2, -2]
faultiest	-2.1	0.53852	[-3, -1, -2, -2, -2, -2, -2, -2, -3, -2]
faultily	-2.0	0.89443	[-2, -2, -2, -3, -2, -1, 0, -3, -2, -3]
faultiness	-1.5	0.92195	[-1, -1, -3, -1, 0, -2, -3, -1, -2, -1]
faulting	-1.4	0.4899	[-1, -2, -1, -1, -2, -1, -1, -2, -1, -2]
faultless	2.0	1.41421	[3, 2, 0, 1, 3, 1, 4, 0, 2, 4]
faultlessly	2.0	1.09545	[3, 3, 2, 2, 0, 3, 2, 3, 2, 0]
faultlessness	1.1	2.02237	[3, 2, 4, 2, 1, -1, -2, 1, -2, 3]
faults	-2.1	0.3	[-2, -2, -2, -2, -2, -2, -3, -2, -2, -2]
faulty	-1.3	0.45826	[-1, -1, -1, -1, -2, -2, -2, -1, -1, -1]
fav	2.0	0.63246	[2, 1, 2, 2, 2, 1, 3, 2, 3, 2]
fave	1.9	1.51327	[1, 3, -2, 3, 2, 3, 1, 3, 2, 3]
favor	1.7	0.64031	[2, 2, 2, 2, 2, 2, 0, 1, 2, 2]
favorable	2.1	0.7	[2, 1, 2, 3, 1, 2, 2, 3, 3, 2]
favorableness	2.2	0.87178	[1, 2, 1, 3, 2, 2, 2, 4, 2, 3]
favorably	1.6	0.66332	[2, 1, 1, 1, 1, 2, 2, 2, 3, 1]
favored	1.8	0.6	[2, 2, 1, 1, 3, 1, 2, 2, 2, 2]
favorer	1.3	1.18743	[2, 2, 1, 1, -2, 1, 2, 2, 2, 2]
favorers	1.4	0.4899	[2, 2, 1, 2, 1, 2, 1, 1, 1, 1]
favoring	1.8	0.6	[2, 1, 1, 3, 2, 2, 1, 2, 2, 2]
favorite	2.0	0.63246	[2, 1, 3, 1, 2, 2, 2, 3, 2, 2]
favorited	1.7	0.45826	[1, 2, 2, 1, 2, 2, 2, 1, 2, 2]
favorites	1.8	0.6	[1, 2, 1, 3, 2, 2, 1, 2, 2, 2]
favoritism	0.7	1.79165	[-1, 3, -1, -1, 2, -2, 2, 2, 3, 0]
favoritisms	0.7	0.9	[0, 2, -1, 1, 0, 0, 1, 1, 2, 1]
favors	1.0	0.7746	[2, 1, 1, 1, 2, 1, -1, 1, 1, 1]
favour	1.9	0.53852	[2, 1, 2, 3, 2, 2, 2, 1, 2, 2]
favoured	1.8	0.4	[1, 2, 2, 2, 1, 2, 2, 2, 2, 2]
favourer	1.6	0.4899	[1, 2, 2, 1, 1, 2, 2, 2, 1, 2]
favourers	1.6	0.66332	[2, 2, 1, 0, 2, 2, 1, 2, 2, 2]
favouring	1.3	0.45826	[1, 2, 1, 1, 2, 1, 1, 1, 1, 2]
favours	1.8	0.6	[3, 2, 1, 2, 1, 2, 2, 1, 2, 2]
fear	-2.2	0.6	[-2, -3, -2, -3, -2, -1, -2, -3, -2, -2]
feared	-2.2	0.6	[-2, -3, -2, -1, -3, -2, -3, -2, -2, -2]
fearful	-2.2	0.87178	[-2, -2, -3, -1, -2, -3, -2, -2, -4, -1]
fearfuller	-2.2	0.87178	[-1, -4, -2, -2, -1, -2, -3, -2, -3, -2]
fearfullest	-2.5	1.0247	[-3, -3, -4, -4, -3, -2, -1, -2, -2, -1]
fearfully	-2.2	0.87178	[-2, -2, -2, -1, -1, -3, -4, -2, -3, -2]
fearfulness	-1.8	0.87178	[-1, -1, -1, -2, -2, -1, -4, -2, -2, -2]
fearing	-2.7	0.9	[-3, -3, -3, -2, -4, -2, -1, -3, -2, -4]
fearless	1.9	0.83066	[3, 3, 3, 2, 2, 2, 1, 1, 1, 1]
fearlessly	1.1	1.22066	[2, -2, 1, 1, 2, 1, 3, 1, 1, 1]
fearlessness	1.1	1.13578	[3, 2, -1, 2, 0, 2, 1, 1, 1, 0]
fears	-1.8	0.6	[-2, -2, -3, -2, -2, -1, -1, -2, -1, -2]
fearsome	-1.7	0.64031	[-3, -1, -1, -2, -1, -2, -2, -2, -1, -2]
fed up	-1.8	1.249	[-2, -2, -3, -4, -2, -2, -1, -2, -1, 1]
feeble	-1.2	1.249	[-2, -2, -3, -1, -1, -2, -1, -1, 2, -1]
feeling	0.5	1.0247	[0, 0, 0, 3, 0, 2, 0, 0, 0, 0]
felonies	-2.5	0.5	[-3, -3, -3, -2, -2, -2, -2, -3, -2, -3]
felony	-2.5	1.36015	[-4, -3, -4, -2, -3, -3, -2, -3, -2, 1]
ferocious	-0.4	1.56205	[1, 1, -2, -3, -1, 2, 1, -2, -1, 0]
ferociously	-1.1	1.86815	[-3, -4, 2, -2, 0, -2, -1, -1, 2, -2]
ferociousness	-1.0	1.89737	[2, -4, -2, 0, -2, -3, -1, 0, 2, -2]
ferocities	-1.0	1.54919	[1, 0, -2, 0, 0, -3, 0, 0, -2, -4]
ferocity	-0.7	1.67631	[-2, -1, -4, 0, 0, -3, 1, 1, 0, 1]
fervent	1.1	1.44568	[3, 0, 0, 1, -2, 2, 2, 3, 1, 1]
fervid	0.5	2.20227	[4, -2, 1, -2, 4, 0, -1, 2, 1, -2]
festival	2.2	0.6	[2, 2, 3, 2, 3, 2, 2, 2, 3, 1]
festivalgoer	1.3	1.1	[1, 3, 2, 3, 0, 0, 1, 2, 0, 1]
festivalgoers	1.2	0.9798	[2, 2, 0, 0, 0, 2, 2, 0, 2, 2]
festivals	1.5	1.11803	[2, 0, 2, 3, 0, 3, 2, 1, 2, 0]
festive	2.0	0.63246	[2, 3, 2, 3, 1, 2, 2, 2, 1, 2]
festively	2.2	0.6	[2, 2, 3, 1, 3, 2, 3, 2, 2, 2]
festiveness	2.4	0.8	[1, 3, 2, 1, 3, 3, 3, 3, 2, 3]
festivities	2.1	0.7	[1, 2, 1, 2, 3, 2, 3, 3, 2, 2]
festivity	2.2	1.07703	[3, 3, 3, 0, 2, 1, 3, 3, 1, 3]
feud	-1.4	0.66332	[-3, -2, -1, -2, -1, -1, -1, -1, -1, -1]
feudal	-0.8	0.87178	[-1, -2, -2, 0, 0, 0, -2, -1, 0, 0]
feudalism	-0.9	1.37477	[-2, -3, -2, -3, 0, 0, 1, 0, 0, 0]
feudalisms	-0.2	0.74833	[0, 0, 0, 0, 0, 0, -1, 0, -2, 1]
feudalist	-0.9	1.13578	[-2, -2, -2, -2, 0, 0, 1, 0, 0, -2]
feudalistic	-1.1	0.7	[0, -1, -1, 0, -2, -1, -2, -1, -2, -1]
feudalities	-0.4	1.0198	[-1, 0, -1, -2, -1, 0, -1, 0, 0, 2]
feudality	-0.5	1.28452	[1, -1, 0, -1, 1, 0, -3, -2, 1, -1]
feudalization	-0.3	1.18743	[-2, -2, 0, 0, 2, 0, -1, -1, 1, 0]
feudalize	-0.5	0.92195	[-1, 0, -1, 0, -2, 0, 1, 0, 0, -2]
feudalized	-0.8	1.07703	[-1, -1, -3, 0, 0, 0, 1, -1, -2, -1]
feudalizes	-0.1	0.53852	[0, 0, -1, 0, -1, 0, 0, 0, 1, 0]
feudalizing	-0.7	1.34536	[2, -2, -1, 0, 0, -2, -1, -2, 1, -2]
feudally	-0.6	0.66332	[0, 0, -1, -1, 0, -1, 0, -1, -2, 0]
feudaries	-0.3	0.9	[0, 0, 0, -2, -2, 0, 0, 0, 0, 1]
feudary	-0.8	0.74833	[0, 0, -1, -1, -2, 0, -1, 0, -2, -1]
feudatories	-0.5	0.92195	[-3, 0, -1, 0, 0, 0, 0, -1, 0, 0]
feudatory	-0.1	0.83066	[-2, 0, 0, 0, 1, 0, 1, -1, 0, 0]
feuded	-2.2	0.6	[-2, -2, -2, -1, -3, -3, -2, -3, -2, -2]
feuding	-1.6	0.66332	[-2, -1, -2, -2, -1, -1, -3, -1, -2, -1]
feudist	-1.1	0.83066	[-2, 0, -3, -1, -1, -1, 0, -1, -1, -1]
feudists	-0.7	0.9	[0, 0, -1, 0, 0, -2, -2, 0, -2, 0]
feuds	-1.4	1.0198	[-2, -2, -2, -1, -1, -1, -3, -2, 1, -1]
fiasco	-2.3	0.64031	[-2, -2, -3, -3, -2, -3, -1, -3, -2, -2]
fidgety	-1.4	0.66332	[-1, -2, -2, 0, -1, -1, -2, -1, -2, -2]
fiery	-1.4	0.91652	[0, -1, -1, -2, -2, 0, -3, -1, -2, -2]
fiesta	2.1	0.7	[3, 2, 1, 2, 2, 1, 3, 3, 2, 2]
fiestas	1.5	1.0247	[3, 0, 3, 1, 0, 2, 2, 1, 2, 1]
fight	-1.6	1.56205	[-1, -2, -3, -3, -1, 2, -4, -1, -2, -1]
fighter	0.6	1.11355	[3, 0, 0, -1, 1, 0, 1, 0, 2, 0]
fighters	-0.2	1.46969	[-1, 2, -2, 0, 2, 0, -2, -2, 0, 1]
fighting	-1.5	1.11803	[-1, -2, -3, -2, -2, -2, 0, -3, 0, 0]
fightings	-1.9	0.53852	[-2, -3, -1, -2, -2, -2, -2, -1, -2, -2]
fights	-1.7	0.64031	[-2, -2, -1, -2, -2, -1, -3, -1, -1, -2]
fine	0.8	0.6	[1, 0, 1, 2, 1, 1, 1, 1, 0, 0]
fire	-1.4	1.49666	[-2, 0, -4, 0, -2, -1, -1, 0, -4, 0]
fired	-2.6	0.91652	[-2, -3, -4, -3, -3, -1, -3, -3, -1, -3]
firing	-1.4	0.8	[0, -1, -1, -1, -1, -2, -2, -3, -2, -1]
fit	1.5	1.0247	[2, 1, 2, 0, 4, 2, 1, 1, 1, 1]
fitness	1.1	0.9434	[0, 2, 1, 0, 1, 3, 2, 1, 0, 1]
flagship	0.4	0.91652	[0, 0, 0, 0, 0, 0, 0, 1, 3, 0]
flatter	0.4	1.42829	[-2, -2, 1, 2, 1, -1, 1, 2, 1, 1]
flattered	1.6	2.00998	[2, 3, 3, 1, 2, 3, 3, 1, 2, -4]
flatterer	-0.3	1.9	[-4, 2, -1, 1, 2, 2, -2, -1, -1, -1]
flatterers	0.3	1.84662	[2, 1, -2, 1, 0, -4, 2, 1, 2, 0]
flatteries	1.2	1.16619	[2, 2, 3, 1, -1, 0, 0, 1, 2, 2]
flattering	1.3	2.19317	[3, 2, 2, 3, -4, 4, -1, 2, 1, 1]
flatteringly	1.0	1.61245	[2, 2, 1, 2, 1, -3, -1, 2, 2, 2]
flatters	0.6	2.10713	[1, 1, 2, 2, 3, -1, -2, 2, -4, 2]
flattery	0.4	1.49666	[1, -1, -2, 1, 3, -2, 1, 1, 1, 1]
flawed	-2.1	0.99443	[-1, -2, -2, -4, -3, -2, -3, -1, -2, -1]
flawless	2.3	2.14709	[4, 3, 4, 4, 1, 2, 4, 4, -2, -1]
flawlessly	0.8	1.83303	[-2, 2, 3, 1, 0, -2, 2, 2, -1, 3]
flees	-0.7	1.18743	[-3, 0, -2, 0, -1, -1, -1, 1, -1, 1]
flexibilities	1.0	1.09545	[1, 3, 1, 0, 1, -1, 2, 0, 2, 1]
flexibility	1.4	0.8	[2, 2, 0, 2, 1, 2, 1, 0, 2, 2]
flexible	0.9	0.83066	[2, 1, 0, 0, 1, 2, 0, 1, 2, 0]
flexibly	1.3	0.78102	[2, 1, 0, 0, 1, 2, 2, 1, 2, 2]
flirtation	1.7	0.64031	[3, 1, 1, 2, 2, 1, 2, 1, 2, 2]
flirtations	-0.1	1.64012	[0, -4, 1, 0, 0, -2, 1, 1, 2, 0]
flirtatious	0.5	2.5	[-4, 1, 2, 1, 2, -3, 3, 4, -2, 1]
flirtatiously	-0.1	1.86815	[2, -1, 1, 2, 1, 1, -3, -3, -2, 1]
flirtatiousness	0.6	1.85472	[1, 0, 2, 1, 1, 2, 1, -4, -1, 3]
flirted	-0.2	1.98997	[1, 2, -1, 1, 1, 1, -3, -3, -3, 2]
flirter	-0.4	1.85472	[1, 2, -1, 0, 1, 1, -3, -3, -3, 1]
flirters	0.6	2.10713	[1, 4, 0, 1, 2, 1, -4, 2, 1, -2]
flirtier	-0.1	1.37477	[-2, 1, 1, 1, 1, -2, 1, -1, -2, 1]
flirtiest	0.4	1.74356	[3, 1, 2, 1, 1, -1, -3, -2, 1, 1]
flirting	0.8	1.83303	[1, 2, 2, 2, 2, -1, 1, 2, -4, 1]
flirts	0.7	1.18743	[2, -1, 2, 1, 1, -2, 1, 1, 1, 1]
flirty	0.6	1.35647	[2, -2, 2, 1, 1, -2, 1, 1, 1, 1]
flop	-1.4	0.4899	[-1, -1, -2, -1, -2, -1, -2, -1, -2, -1]
flops	-1.4	0.8	[-2, -2, -2, 0, -2, -1, -2, -1, 0, -2]
flu	-1.6	0.8	[0, -2, -1, -2, -1, -2, -3, -1, -2, -2]
flunk	-1.3	0.78102	[-2, -1, -1, -3, -1, -1, 0, -2, -1, -1]
flunked	-2.1	0.9434	[-4, -1, -3, -2, -1, -1, -3, -2, -2, -2]
flunker	-1.9	1.04403	[-4, -1, -3, -2, -1, -1, -3, -2, -1, -1]
flunkers	-1.6	0.8	[-2, -1, -3, -2, 0, -1, -1, -2, -2, -2]
flunkey	-1.8	0.9798	[-4, -1, -3, -2, -1, -1, -2, -2, -1, -1]
flunkeys	-0.6	1.2	[-2, -1, -1, 2, -1, 0, -1, -2, 1, -1]
flunkies	-1.4	1.11355	[-2, -2, 0, -3, 1, -2, -1, -2, -1, -2]
flunking	-1.5	0.92195	[-2, -1, -1, -3, -1, -1, 0, -2, -3, -1]
flunks	-1.8	1.32665	[0, -2, -3, -3, -2, -3, -2, -1, 1, -3]
flunky	-1.8	1.4	[-2, -3, -3, -3, -2, -1, -2, -2, 2, -2]
flustered	-1.0	1.18322	[-1, -1, -1, -1, -3, -2, 2, -1, -1, -1]
focused	1.6	0.91652	[2, 2, 1, 3, 0, 1, 1, 2, 3, 1]
foe	-1.9	1.22066	[-1, -3, -2, -2, -2, -2, -4, 1, -2, -2]
foehns	0.2	0.4	[1, 0, 0, 0, 0, 0, 0, 0, 1, 0]
foeman	-1.8	0.6	[-2, -3, -1, -2, -2, -2, -2, -1, -1, -2]
foemen	-0.3	1.18743	[0, 0, -1, 0, 0, -2, -2, -1, 2, 1]
foes	-2.0	0.89443	[-2, -1, -2, -2, -4, -3, -2, -1, -1, -2]
foetal	-0.1	0.3	[0, -1, 0, 0, 0, 0, 0, 0, 0, 0]
foetid	-2.3	1.41774	[-1, -3, 0, -2, -3, 0, -3, -3, -4, -4]
foetor	-3.0	0.89443	[-3, -4, -2, -3, -3, -1, -4, -4, -3, -3]
foetors	-2.1	1.04403	[-2, -3, -1, -3, -2, -2, 0, -4, -2, -2]
foetus	0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, 2, 0]
foetuses	0.2	0.6	[2, 0, 0, 0, 0, 0, 0, 0, 0, 0]
fond	1.9	0.83066	[2, 3, 2, 3, 1, 1, 3, 2, 1, 1]
fondly	1.9	0.7	[1, 2, 3, 2, 2, 3, 2, 1, 1, 2]
fondness	2.5	0.67082	[3, 2, 2, 2, 2, 3, 4, 3, 2, 2]
fool	-1.9	0.53852	[-2, -2, -2, -2, -1, -2, -2, -3, -1, -2]
fooled	-1.6	0.4899	[-2, -1, -1, -2, -2, -1, -2, -1, -2, -2]
fooleries	-1.8	1.07703	[-1, -2, 0, -2, -1, -2, -4, -2, -3, -1]
foolery	-1.8	0.87178	[-2, -2, -3, 0, -3, -1, -2, -2, -2, -1]
foolfish	-0.8	0.87178	[-1, 0, -1, -2, 0, 0, 0, -2, 0, -2]
foolfishes	-0.4	0.4899	[-1, -1, -1, 0, 0, 0, 0, 0, 0, -1]
foolhardier	-1.5	0.67082	[-2, -1, -2, -1, -2, -1, -2, -2, 0, -2]
foolhardiest	-1.3	0.64031	[0, -2, -1, -1, -1, -1, -2, -1, -2, -2]
foolhardily	-1.0	1.41421	[-1, -1, -2, -2, -2, -2, -1, -1, 3, -1]
foolhardiness	-1.6	0.66332	[-3, -1, -1, -1, -2, -1, -2, -2, -2, -1]
foolhardy	-1.4	0.4899	[-2, -2, -1, -1, -1, -2, -1, -1, -2, -1]
fooling	-1.7	0.64031	[-2, -2, -1, -2, -2, -2, -1, -3, -1, -1]
foolish	-1.1	0.83066	[-1, -1, -1, -2, -2, -1, -1, -2, 1, -1]
foolisher	-1.7	0.64031	[-2, -1, -2, -1, -2, -1, -2, -3, -2, -1]
foolishest	-1.4	1.28062	[-2, 1, -2, -3, -2, -2, -1, 1, -2, -2]
foolishly	-1.8	0.6	[-2, -1, -2, -2, -3, -1, -1, -2, -2, -2]
foolishness	-1.8	0.6	[-1, -1, -2, -2, -2, -1, -3, -2, -2, -2]
foolishnesses	-2.0	0.89443	[-1, -1, -1, -2, -3, -1, -3, -3, -2, -3]
foolproof	1.6	0.91652	[2, 1, 3, 1, 1, 0, 1, 2, 2, 3]
fools	-2.2	0.74833	[-2, -4, -2, -2, -1, -2, -2, -2, -2, -3]
foolscaps	-0.8	0.6	[0, -1, -1, -1, 0, -1, -2, 0, -1, -1]
forbid	-1.3	0.78102	[-2, -2, -1, -1, -1, -1, 0, -1, -1, -3]
forbiddance	-1.4	1.35647	[-3, -1, -2, -3, -1, 2, -1, -2, -2, -1]
forbiddances	-1.0	2.0	[-2, 3, 1, -2, -4, -2, -3, -1, -1, 1]
forbidden	-1.8	0.74833	[-1, -2, -2, -2, -1, -1, -3, -3, -2, -1]
forbidder	-1.6	0.66332	[-1, -2, -2, -2, -2, 0, -2, -2, -2, -1]
forbidders	-1.5	0.80623	[-1, -2, -1, -3, -2, -2, 0, -2, -1, -1]
forbidding	-1.9	0.7	[-3, -1, -1, -2, -2, -1, -3, -2, -2, -2]
forbiddingly	-1.9	0.53852	[-2, -1, -2, -2, -2, -2, -3, -2, -1, -2]
forbids	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -1, -2, -1, -1]
forced	-2.0	0.63246	[-1, -2, -3, -3, -1, -2, -2, -2, -2, -2]
foreclosure	-0.5	1.43178	[-1, -2, -3, 2, -1, 1, -1, 0, 1, -1]
foreclosures	-2.4	0.66332	[-3, -2, -3, -2, -3, -3, -1, -2, -2, -3]
forgave	1.4	0.91652	[2, 2, 1, 2, 1, -1, 1, 2, 2, 2]
forget	-0.9	0.53852	[-1, -1, -1, -1, 0, -1, -1, 0, -2, -1]
forgetful	-1.1	1.3	[-3, -1, -2, 1, 0, -2, 1, -1, -2, -2]
forgivable	1.7	0.64031	[2, 1, 2, 1, 2, 2, 1, 2, 3, 1]
forgivably	1.6	0.66332	[1, 1, 2, 1, 2, 2, 1, 2, 3, 1]
forgive	1.1	1.22066	[2, 1, 1, 3, 1, 1, 1, 2, -2, 1]
forgiven	1.6	0.66332	[2, 1, 1, 3, 2, 2, 1, 2, 1, 1]
forgiveness	1.1	1.22066	[2, 1, 1, 1, 3, 2, -2, 1, 1, 1]
forgiver	1.7	0.78102	[3, 1, 1, 3, 1, 2, 1, 2, 2, 1]
forgivers	1.2	0.6	[2, 1, 2, 1, 0, 1, 1, 2, 1, 1]
forgives	1.7	0.78102	[2, 1, 3, 1, 1, 3, 2, 1, 1, 2]
forgiving	1.9	0.7	[2, 3, 2, 1, 2, 3, 1, 1, 2, 2]
forgivingly	1.4	0.4899	[2, 1, 2, 1, 1, 1, 2, 1, 2, 1]
forgivingness	1.8	0.6	[3, 1, 2, 1, 2, 2, 2, 2, 2, 1]
forgotten	-0.9	0.53852	[-1, 0, -1, 0, -1, -1, -1, -1, -2, -1]
fortunate	1.9	0.53852	[2, 2, 1, 1, 2, 2, 2, 3, 2, 2]
fought	-1.3	0.78102	[-2, -1, -2, -1, 0, -1, -1, -1, -1, -3]
foughten	-1.9	1.3	[-1, -3, 0, -2, -4, -3, -2, 0, -1, -3]
frantic	-1.9	0.7	[-3, -2, -2, -1, -2, -1, -2, -3, -1, -2]
frantically	-1.4	0.4899	[-2, -1, -2, -1, -1, -1, -1, -1, -2, -2]
franticness	-0.7	1.18743	[-1, -2, 2, -1, -1, -1, 1, -2, -1, -1]
fraud	-2.8	0.6	[-3, -3, -2, -3, -2, -3, -3, -2, -4, -3]
frauds	-2.3	0.45826	[-2, -2, -2, -3, -3, -2, -3, -2, -2, -2]
fraudster	-2.5	0.92195	[-4, -1, -2, -2, -4, -2, -3, -2, -3, -2]
fraudsters	-2.4	0.91652	[-4, -3, -3, -3, -1, -2, -2, -2, -1, -3]
fraudulence	-2.3	0.78102	[-3, -2, -2, -3, -2, -1, -2, -4, -2, -2]
fraudulent	-2.2	0.6	[-2, -3, -2, -2, -2, -1, -3, -3, -2, -2]
freak	-1.9	0.9434	[-3, -2, -1, -2, -4, -1, -2, -1, -2, -1]
freaked	-1.2	1.32665	[-2, -2, 1, 0, -1, -2, -3, 1, -2, -2]
freakier	-1.3	1.1	[-3, -2, -2, 1, -2, -2, -1, 0, -1, -1]
freakiest	-1.6	1.0198	[-2, -3, -3, -3, -1, -1, -1, 0, -1, -1]
freakiness	-1.4	0.8	[-1, -1, -1, -2, -3, 0, -2, -1, -2, -1]
freaking	-1.8	1.16619	[0, -4, -3, -2, -3, -1, -1, -1, -2, -1]
freakish	-2.1	1.37477	[-1, -3, -2, -1, -2, -3, -4, -3, 1, -3]
freakishly	-0.8	1.249	[-2, -2, 1, -2, -1, 1, -1, 1, -1, -2]
freakishness	-1.4	1.68523	[-2, -4, -1, 0, -1, -2, 1, 1, -4, -2]
freakout	-1.8	1.6	[-3, 1, -3, -2, -3, -1, -4, 1, -2, -2]
freakouts	-1.5	0.92195	[-2, -2, -1, -1, -2, -2, -2, 1, -2, -2]
freaks	-0.4	1.35647	[2, 0, -2, -1, 0, 1, -2, 1, -1, -2]
freaky	-1.5	1.20416	[-2, -2, -2, -2, -1, -2, 2, -2, -2, -2]
free	2.3	0.9	[2, 4, 3, 3, 2, 1, 2, 3, 1, 2]
freebase	-0.1	1.44568	[2, 0, -2, 1, -3, 0, -1, 0, 1, 1]
freebased	0.8	1.16619	[2, 0, -1, 0, 3, 0, 1, 2, 1, 0]
freebases	0.8	1.249	[2, 0, -1, 0, 2, 0, 0, 3, 2, 0]
freebasing	-0.4	1.56205	[-4, -2, 0, 0, 1, 2, 0, -1, 0, 0]
freebee	1.3	0.78102	[1, 2, 1, 1, 1, 0, 3, 2, 1, 1]
freebees	1.3	1.26886	[2, 2, -2, 2, 2, 2, 0, 2, 1, 2]
freebie	1.8	0.9798	[2, 1, 3, 2, 2, 4, 1, 1, 1, 1]
freebies	1.8	0.9798	[2, 3, 1, 1, 2, 4, 1, 1, 2, 1]
freeboard	0.3	0.64031	[0, 0, 0, 0, 0, 1, 0, 0, 0, 2]
freeboards	0.7	0.9	[0, 0, 0, 1, 0, 0, 2, 2, 2, 0]
freeboot	-0.7	1.67631	[1, -2, -3, -2, -1, -2, -1, 2, -1, 2]
freebooter	-1.7	1.00499	[-2, -2, -3, -3, -2, -2, 0, -1, 0, -2]
freebooters	-0.2	1.32665	[-1, -3, 1, 0, -2, 1, 0, 1, 0, 1]
freebooting	-0.8	1.249	[-2, -2, -1, 2, -1, -1, -1, 1, -2, -1]
freeborn	1.2	0.74833	[1, 0, 3, 1, 1, 1, 1, 1, 2, 1]
freed	1.7	1.34536	[-2, 2, 3, 2, 2, 2, 3, 1, 2, 2]
freedman	1.1	0.9434	[1, 2, 0, 1, 2, 1, 3, 0, 0, 1]
freedmen	0.7	0.78102	[1, 2, 1, 2, 0, 1, 0, 0, 0, 0]
freedom	3.2	0.9798	[4, 4, 1, 3, 2, 4, 4, 3, 3, 4]
freedoms	1.2	1.07703	[2, 1, 1, 3, 2, 1, 2, 1, -1, 0]
freedwoman	1.6	1.74356	[3, 2, 0, 4, 4, 3, 1, -1, 0, 0]
freedwomen	1.3	0.78102	[2, 0, 2, 0, 2, 1, 1, 1, 2, 2]
freeform	0.9	0.83066	[0, 0, 0, 2, 1, 2, 1, 1, 2, 0]
freehand	0.5	0.80623	[0, 0, 0, 1, 0, 2, 0, 2, 0, 0]
freehanded	1.4	0.91652	[3, 0, 0, 2, 1, 2, 2, 1, 2, 1]
freehearted	1.5	0.67082	[2, 0, 1, 2, 2, 2, 2, 1, 1, 2]
freehold	0.7	0.78102	[1, 1, 0, 0, 2, 0, 2, 0, 1, 0]
freeholder	0.5	0.5	[1, 1, 0, 0, 0, 1, 1, 0, 1, 0]
freeholders	0.1	0.83066	[0, 0, 1, 0, 0, 1, 0, -2, 0, 1]
freeholds	1.0	0.63246	[1, 0, 2, 0, 1, 2, 1, 1, 1, 1]
freeing	2.1	1.04403	[2, 1, 1, 1, 4, 2, 2, 4, 2, 2]
freelance	1.2	1.66132	[3, 0, 0, 0, 0, 1, 4, 0, 4, 0]
freelanced	0.7	1.00499	[3, 0, 0, 0, 0, 2, 1, 0, 1, 0]
freelancer	1.1	1.04403	[2, 0, 3, 2, 1, 0, 0, 0, 2, 1]
freelancers	0.4	0.66332	[0, 0, 0, 2, 0, 0, 0, 1, 1, 0]
freelances	0.7	0.9	[1, 0, 0, 0, 0, 2, 2, 0, 2, 0]
freelancing	0.4	0.66332	[2, 0, 0, 0, 1, 0, 0, 0, 1, 0]
freeload	-1.9	1.04403	[-2, -3, -1, -2, -1, 0, -1, -3, -3, -3]
freeloaded	-1.6	0.8	[-2, 0, -3, -1, -1, -2, -2, -2, -2, -1]
freeloader	-0.7	1.00499	[-2, 0, -1, -2, -1, 1, -1, 1, -1, -1]
freeloaders	-0.1	1.57797	[-2, -2, -1, 0, 0, 2, -2, 0, 2, 2]
freeloading	-1.3	2.0025	[1, -2, -4, -2, -3, -3, 1, 2, 0, -3]
freeloads	-1.3	0.9	[-1, -1, -2, -2, -2, 0, 0, -1, -1, -3]
freely	1.9	0.53852	[2, 1, 2, 2, 2, 3, 2, 2, 1, 2]
freeman	1.7	0.78102	[2, 2, 2, 1, 2, 0, 3, 1, 2, 2]
freemartin	-0.5	0.92195	[0, 0, 0, -1, 0, -1, 0, 0, -3, 0]
freemasonries	0.7	0.78102	[0, 1, 0, 0, 0, 1, 1, 0, 2, 2]
freemasonry	0.3	0.64031	[1, 0, 2, 0, 0, 0, 0, 0, 0, 0]
freemen	1.5	0.67082	[1, 1, 0, 2, 2, 2, 2, 2, 1, 2]
freeness	1.6	0.66332	[1, 2, 3, 2, 2, 1, 1, 1, 2, 1]
freenesses	1.7	0.78102	[1, 2, 1, 2, 2, 2, 3, 2, 2, 0]
freer	1.1	0.7	[2, 2, 0, 2, 1, 1, 1, 0, 1, 1]
freers	1.0	0.89443	[0, 1, 1, 2, 3, 1, 0, 1, 0, 1]
frees	1.2	0.6	[1, 2, 0, 1, 1, 1, 2, 1, 1, 2]
freesia	0.4	0.91652	[0, 0, 3, 0, 1, 0, 0, 0, 0, 0]
freesias	0.4	0.66332	[0, 0, 0, 1, 1, 2, 0, 0, 0, 0]
freest	1.6	1.28062	[3, 4, 0, 3, 0, 1, 1, 2, 1, 1]
freestanding	1.1	0.83066	[2, 1, 1, 2, 0, 0, 0, 2, 1, 2]
freestyle	0.7	0.9	[2, 0, 1, 0, 2, 0, 0, 2, 0, 0]
freestyler	0.4	0.91652	[0, 0, 0, 0, 1, 0, 0, 0, 0, 3]
freestylers	0.8	0.87178	[2, 1, 0, 0, 0, 1, 0, 2, 2, 0]
freestyles	0.3	0.64031	[0, 0, 0, 0, 1, 0, 0, 0, 0, 2]
freethinker	1.0	0.63246	[1, 2, 1, 1, 2, 1, 0, 0, 1, 1]
freethinkers	1.0	0.7746	[1, 0, 0, 1, 2, 2, 1, 2, 0, 1]
freethinking	1.1	0.7	[1, 0, 1, 1, 2, 2, 0, 2, 1, 1]
freeware	0.7	1.48661	[1, 0, 0, 2, 4, 0, 1, -2, 1, 0]
freeway	0.2	0.6	[0, 0, 0, 0, 0, 0, 2, 0, 0, 0]
freewheel	0.5	1.11803	[2, 0, 1, 2, 0, -2, 0, 0, 1, 1]
freewheeled	0.3	0.78102	[1, 0, 0, 0, 0, 1, 2, -1, 0, 0]
freewheeler	0.2	0.87178	[-2, 1, 0, 0, 0, 1, 1, 1, 0, 0]
freewheelers	-0.3	1.00499	[-2, 0, 0, 0, 2, -1, -1, 0, -1, 0]
freewheeling	0.5	1.11803	[-1, 2, 0, -1, 1, 2, 2, 0, 0, 0]
freewheelingly	0.8	0.87178	[1, 2, 0, 0, 1, 2, 1, -1, 1, 1]
freewheels	0.6	0.91652	[0, 3, 1, 1, 1, 0, 0, 0, 0, 0]
freewill	1.0	0.7746	[1, 1, 1, -1, 1, 1, 1, 2, 2, 1]
freewriting	0.8	1.07703	[0, 2, 0, 0, 1, 0, 3, 0, 2, 0]
freeze	0.2	0.9798	[-1, 0, 1, 1, -1, 0, 0, -1, 2, 1]
freezers	-0.1	0.3	[0, 0, 0, -1, 0, 0, 0, 0, 0, 0]
freezes	-0.1	1.13578	[0, -2, -2, 1, 0, 0, 0, 0, 2, 0]
freezing	-0.4	1.28062	[-2, -1, -2, -2, 0, 0, 0, 1, 2, 0]
freezingly	-1.6	0.4899	[-1, -2, -2, -1, -2, -2, -2, -2, -1, -1]
frenzy	-1.3	1.48661	[-1, -1, -2, -2, 2, -4, -1, -2, 0, -2]
fresh	1.3	0.45826	[2, 2, 1, 1, 1, 1, 2, 1, 1, 1]
friend	2.2	0.6	[2, 2, 3, 2, 3, 3, 1, 2, 2, 2]
friended	1.7	0.78102	[2, 2, 1, 3, 1, 1, 2, 3, 1, 1]
friending	1.8	1.07703	[1, 4, 2, 0, 1, 3, 2, 2, 1, 2]
friendless	-1.5	0.67082	[-1, -2, -2, -1, 0, -2, -1, -2, -2, -2]
friendlessness	-0.3	2.05183	[-2, -2, 2, 1, -4, -1, 2, 1, -2, 2]
friendlier	2.0	0.63246	[3, 2, 2, 1, 2, 2, 2, 1, 3, 2]
friendlies	2.2	0.74833	[3, 2, 2, 3, 2, 2, 1, 3, 1, 3]
friendliest	2.6	0.91652	[3, 3, 1, 3, 2, 1, 3, 3, 4, 3]
friendlily	1.8	0.74833	[1, 2, 2, 1, 3, 1, 1, 2, 3, 2]
friendliness	2.0	0.7746	[3, 1, 3, 2, 2, 3, 2, 1, 2, 1]
friendly	2.2	0.6	[2, 1, 3, 3, 2, 2, 3, 2, 2, 2]
friends	2.1	0.53852	[3, 3, 2, 2, 2, 1, 2, 2, 2, 2]
friendship	1.9	0.53852	[1, 2, 2, 1, 3, 2, 2, 2, 2, 2]
friendships	1.6	0.91652	[2, 1, 1, 0, 3, 2, 2, 3, 1, 1]
fright	-1.6	1.35647	[-2, -1, 0, -3, -2, -4, 1, -1, -2, -2]
frighted	-1.4	0.91652	[0, -1, -1, -1, -2, -1, -3, -1, -1, -3]
frighten	-1.4	0.8	[0, -1, -1, -1, -2, -1, -3, -2, -1, -2]
frightened	-1.9	0.7	[-2, -3, -1, -1, -2, -2, -3, -1, -2, -2]
frightening	-2.2	0.9798	[-1, -1, -4, -3, -3, -3, -2, -2, -1, -2]
frighteningly	-2.1	0.7	[-2, -2, -2, -2, -4, -2, -1, -2, -2, -2]
frightens	-1.7	0.78102	[-2, -1, -2, -1, -1, -1, -2, -3, -3, -1]
frightful	-2.3	0.78102	[-2, -2, -2, -2, -3, -2, -3, -1, -4, -2]
frightfully	-1.7	0.78102	[-1, -1, -1, -2, -2, -1, -2, -3, -1, -3]
frightfulness	-1.9	0.7	[-1, -1, -2, -3, -3, -2, -1, -2, -2, -2]
frighting	-1.5	0.67082	[-1, -2, -2, -3, -1, -1, -1, -1, -2, -1]
frights	-1.1	0.83066	[-2, 0, -2, 0, -2, -1, 0, -1, -1, -2]
frisky	1.0	1.48324	[1, -2, 1, 1, 1, 3, 3, 2, -1, 1]
frowning	-1.4	1.42829	[-1, -1, -1, -3, -3, 1, -2, -3, 1, -2]
frustrate	-2.0	0.63246	[-2, -3, -2, -1, -3, -2, -2, -1, -2, -2]
frustrated	-2.4	0.66332	[-2, -1, -3, -3, -2, -2, -3, -2, -3, -3]
frustrates	-1.9	0.7	[-1, -1, -3, -2, -1, -2, -2, -2, -3, -2]
frustrating	-1.9	0.83066	[-2, -2, -1, -1, -1, -2, -2, -2, -4, -2]
frustratingly	-2.0	0.63246	[-1, -3, -2, -2, -2, -2, -2, -3, -2, -1]
frustration	-2.1	0.7	[-3, -2, -3, -2, -1, -2, -3, -2, -1, -2]
frustrations	-2.0	0.7746	[-2, -1, -1, -3, -2, -2, -3, -3, -2, -1]
fuck	-2.5	1.20416	[0, -3, -4, -3, -2, -3, -4, -1, -2, -3]
fucked	-3.4	0.66332	[-2, -3, -4, -3, -4, -4, -3, -4, -3, -4]
fucker	-3.3	0.78102	[-3, -4, -4, -2, -3, -4, -4, -2, -4, -3]
fuckers	-2.9	0.9434	[-3, -3, -4, -3, -4, -4, -2, -1, -2, -3]
fuckface	-3.2	1.07703	[-4, -2, -4, -1, -4, -4, -2, -4, -3, -4]
fuckhead	-3.1	1.04403	[-1, -4, -2, -4, -3, -3, -2, -4, -4, -4]
fucks	-2.1	1.13578	[-3, -2, -1, -1, -1, -2, -4, -2, -4, -1]
fucktard	-3.1	0.9434	[-4, -4, -4, -3, -4, -2, -4, -2, -2, -2]
fud	-1.1	1.37477	[-1, -3, -1, -1, -3, -2, 2, 0, -1, -1]
fuked	-2.5	0.92195	[-2, -3, -3, -3, -3, -3, 0, -3, -3, -2]
fuking	-3.2	0.9798	[-4, -1, -3, -4, -3, -2, -4, -3, -4, -4]
fulfill	1.9	1.04403	[1, 1, 2, 4, 1, 1, 2, 1, 3, 3]
fulfilled	1.8	0.87178	[1, 2, 1, 0, 2, 2, 3, 2, 2, 3]
fulfills	1.0	1.09545	[2, 1, 1, 1, 1, -2, 2, 2, 1, 1]
fume	-1.2	1.16619	[0, 0, 0, -2, -3, -2, -1, -3, -1, 0]
fumed	-1.8	0.87178	[-3, -2, -3, -2, -2, 0, -2, -1, -1, -2]
fumeless	0.3	0.64031	[0, 0, 0, 1, 0, 0, 0, 2, 0, 0]
fumelike	-0.7	1.18743	[0, 0, -3, 0, 0, -2, -1, -2, 1, 0]
fumer	0.7	0.9	[2, 1, 0, 1, 0, 2, 0, -1, 1, 1]
fumers	-0.8	0.6	[0, -1, -1, -2, -1, 0, 0, -1, -1, -1]
fumes	-0.1	1.13578	[0, -2, 0, 0, -1, 1, -1, -1, 1, 2]
fumet	0.4	1.0198	[2, 0, 0, -1, 0, 1, -1, 0, 2, 1]
fumets	-0.4	0.66332	[0, -1, 0, -1, 0, 0, -2, 0, 0, 0]
fumette	-0.6	1.11355	[-2, -2, 0, -1, -1, 2, 0, 0, -1, -1]
fuming	-2.7	0.64031	[-2, -3, -3, -2, -3, -4, -3, -2, -2, -3]
fun	2.3	0.45826	[2, 3, 2, 3, 2, 2, 3, 2, 2, 2]
funeral	-1.5	1.74642	[-2, -3, -2, -1, -3, -1, -4, 1, 2, -2]
funerals	-1.6	2.2891	[-3, -4, -1, -4, 2, -3, -3, 2, 1, -3]
funky	-0.4	1.62481	[0, -1, 2, -1, 2, 0, -4, -1, 0, -1]
funned	2.3	0.9	[2, 3, 3, 4, 1, 2, 2, 2, 3, 1]
funnel	0.1	0.53852	[1, 0, 0, 0, 0, -1, 0, 0, 1, 0]
funneled	0.1	0.3	[0, 0, 0, 0, 0, 1, 0, 0, 0, 0]
funnelform	0.5	0.80623	[0, 0, 1, 0, 0, 0, 0, 2, 2, 0]
funneling	-0.1	0.3	[0, 0, 0, 0, -1, 0, 0, 0, 0, 0]
funnelled	-0.1	0.3	[0, 0, 0, 0, -1, 0, 0, 0, 0, 0]
funnelling	0.1	0.3	[0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
funnels	0.4	0.66332	[0, 0, 0, 1, 0, 0, 0, 0, 2, 1]
funner	2.2	0.74833	[2, 2, 3, 4, 1, 2, 2, 2, 2, 2]
funnest	2.9	0.7	[4, 3, 4, 3, 2, 3, 2, 3, 2, 3]
funnier	1.7	1.00499	[2, 2, -1, 2, 2, 2, 3, 1, 2, 2]
funnies	1.3	1.00499	[2, 1, -1, 2, 1, 2, 3, 1, 1, 1]
funniest	2.6	0.8	[3, 3, 3, 3, 2, 2, 1, 2, 4, 3]
funnily	1.9	0.53852	[2, 3, 1, 2, 2, 2, 2, 1, 2, 2]
funniness	1.8	0.9798	[3, 2, 3, 2, 2, 1, 3, 0, 1, 1]
funninesses	1.6	0.91652	[1, 3, 0, 3, 1, 2, 1, 2, 2, 1]
funning	1.8	0.9798	[2, 2, 1, 2, 1, 4, 0, 2, 2, 2]
funny	1.9	0.53852	[3, 2, 2, 1, 2, 2, 1, 2, 2, 2]
funnyman	1.4	0.4899	[2, 2, 1, 2, 1, 1, 1, 1, 2, 1]
funnymen	1.3	1.1	[2, 1, 1, 0, 0, 3, 3, 1, 2, 0]
furious	-2.7	1.41774	[-3, -3, -3, -4, -4, -4, 1, -2, -3, -2]
furiously	-1.9	1.04403	[-2, -2, -4, -2, -1, -2, -1, -3, 0, -2]
fury	-2.7	0.78102	[-4, -3, -2, -2, -2, -3, -4, -2, -3, -2]
futile	-1.9	0.83066	[0, -3, -2, -2, -2, -2, -3, -1, -2, -2]
gag	-1.4	1.0198	[-2, -2, -1, -2, 0, -2, -3, 0, 0, -2]
gagged	-1.3	1.55242	[-2, -2, -1, -2, 2, -3, -1, -3, 1, -2]
gain	2.4	0.4899	[2, 3, 2, 2, 3, 2, 3, 2, 3, 2]
gained	1.6	0.66332	[2, 1, 2, 1, 1, 3, 2, 1, 2, 1]
gaining	1.8	0.4	[1, 2, 2, 2, 1, 2, 2, 2, 2, 2]
gains	1.4	0.4899	[1, 2, 1, 2, 2, 2, 1, 1, 1, 1]
gallant	1.7	1.1	[4, 0, 3, 1, 1, 2, 2, 2, 1, 1]
gallantly	1.9	0.53852	[2, 2, 3, 2, 2, 2, 2, 1, 2, 1]
gallantry	2.6	0.8	[3, 3, 1, 2, 2, 3, 4, 2, 3, 3]
geek	-0.8	0.9798	[0, 0, 0, -2, -3, -1, -1, 0, 0, -1]
geekier	0.2	1.4	[0, 3, -1, -1, -1, 1, 2, 1, -1, -1]
geekiest	-0.1	0.9434	[1, -1, -1, -1, -1, 1, 1, 1, 0, -1]
geeks	-0.4	0.91652	[0, 0, -1, 0, 0, 0, 0, 0, -3, 0]
geeky	-0.6	0.91652	[0, 0, -1, 0, -1, 0, -1, 0, -3, 0]
generosities	2.6	0.4899	[3, 3, 2, 3, 2, 3, 2, 3, 2, 3]
generosity	2.3	0.64031	[3, 1, 2, 2, 3, 2, 3, 3, 2, 2]
generous	2.3	0.78102	[3, 2, 4, 2, 2, 1, 2, 3, 2, 2]
generously	1.8	0.74833	[3, 3, 2, 1, 2, 1, 1, 2, 1, 2]
generousness	2.4	0.91652	[3, 2, 3, 1, 3, 4, 3, 2, 2, 1]
genial	1.8	0.6	[3, 1, 2, 1, 2, 2, 1, 2, 2, 2]
gentle	1.9	0.53852	[2, 2, 2, 1, 2, 3, 2, 2, 1, 2]
gentler	1.4	0.4899	[2, 1, 1, 1, 1, 2, 2, 1, 2, 1]
gentlest	1.8	0.4	[2, 1, 2, 2, 2, 1, 2, 2, 2, 2]
gently	2.0	0.7746	[2, 2, 2, 1, 1, 3, 3, 2, 1, 3]
ghost	-1.3	1.34536	[-2, 0, 0, 0, 0, -3, -2, -3, 0, -3]
giddy	-0.6	1.62481	[-2, -2, 3, -1, -1, -1, -1, -2, 2, -1]
gift	1.9	0.53852	[2, 1, 2, 2, 2, 1, 2, 3, 2, 2]
giggle	1.8	0.9798	[2, 2, 2, 2, 2, 3, 2, -1, 2, 2]
giggled	1.5	1.20416	[1, 3, 1, 3, 3, 1, 1, -1, 2, 1]
giggler	0.6	0.8	[1, 1, 1, 1, -1, 1, 1, 1, -1, 1]
gigglers	1.4	0.4899	[1, 2, 2, 1, 2, 1, 1, 1, 1, 2]
giggles	0.8	1.249	[1, 2, 2, 1, -1, 1, 1, 2, -2, 1]
gigglier	1.0	1.09545	[2, 1, 1, 2, 2, 1, -1, 2, 1, -1]
giggliest	1.7	1.26886	[4, 2, 1, 2, 1, 3, 2, 2, -1, 1]
giggling	1.5	0.5	[2, 2, 1, 2, 2, 1, 1, 1, 1, 2]
gigglingly	1.1	1.37477	[1, 2, 1, 1, -1, 2, 1, -1, 4, 1]
giggly	1.0	1.41421	[1, 2, -2, 1, 2, 3, 1, -1, 2, 1]
giver	1.4	0.66332	[1, 2, 2, 0, 1, 2, 1, 2, 1, 2]
givers	1.7	1.34536	[2, -1, 3, 3, 3, 1, 1, 2, 0, 3]
giving	1.4	1.0198	[1, 1, 3, 1, 1, 2, 3, 0, 2, 0]
glad	2.0	0.63246	[3, 2, 1, 2, 2, 2, 1, 3, 2, 2]
gladly	1.4	0.4899	[2, 2, 1, 2, 1, 1, 2, 1, 1, 1]
glamor	2.1	0.9434	[1, 2, 2, 2, 1, 3, 2, 4, 3, 1]
glamorise	1.3	1.1	[0, 1, 4, 1, 0, 2, 1, 2, 1, 1]
glamorised	1.8	0.74833	[1, 2, 2, 2, 2, 2, 0, 2, 3, 2]
glamorises	2.1	1.04403	[1, 3, 2, 4, 2, 2, 0, 2, 3, 2]
glamorising	1.2	1.16619	[3, 2, 0, 3, 1, 2, 0, 1, 0, 0]
glamorization	1.6	0.91652	[2, 2, 3, 0, 3, 1, 2, 1, 1, 1]
glamorize	1.7	1.1	[1, 2, 1, 4, 2, 0, 3, 1, 2, 1]
glamorized	2.1	1.04403	[3, 2, 1, 2, 4, 2, 2, 0, 3, 2]
glamorizer	2.4	1.0198	[3, 2, 2, 3, 4, 3, 2, 0, 3, 2]
glamorizers	1.6	1.11355	[4, 1, 1, 1, 1, 2, 0, 2, 3, 1]
glamorizes	2.4	1.2	[3, 2, 2, 4, 4, 1, 2, 0, 3, 3]
glamorizing	1.8	1.16619	[3, 0, 1, 2, 3, 2, 1, 0, 3, 3]
glamorous	2.3	0.78102	[3, 2, 4, 2, 2, 2, 3, 2, 2, 1]
glamorously	2.1	1.04403	[1, 3, 2, 1, 2, 1, 4, 1, 3, 3]
glamors	1.4	0.66332	[1, 1, 2, 2, 2, 1, 2, 2, 0, 1]
glamour	2.4	0.91652	[2, 4, 2, 1, 3, 2, 2, 2, 2, 4]
glamourize	0.8	1.32665	[2, 1, 0, 1, 0, 4, 0, 0, -1, 1]
glamourless	-1.6	1.49666	[-4, -1, -1, -2, -2, -3, -2, -1, 2, -2]
glamourous	2.0	0.7746	[1, 3, 3, 2, 1, 2, 2, 2, 3, 1]
glamours	1.9	0.83066	[1, 3, 2, 2, 3, 2, 1, 1, 3, 1]
glee	3.2	0.4	[3, 4, 3, 3, 4, 3, 3, 3, 3, 3]
gleeful	2.9	0.53852	[3, 3, 3, 3, 3, 4, 2, 2, 3, 3]
gloom	-2.6	0.66332	[-4, -2, -3, -3, -2, -3, -2, -2, -2, -3]
gloomed	-1.9	0.7	[-1, -2, -3, -1, -2, -2, -1, -2, -3, -2]
gloomful	-2.1	0.9434	[-3, -1, -4, -2, -1, -1, -2, -3, -2, -2]
gloomier	-1.5	1.20416	[-3, -2, -2, -3, -1, -2, 0, 1, -1, -2]
gloomiest	-1.8	2.03961	[-2, -2, 2, -4, -2, -3, -4, -3, 2, -2]
gloominess	-1.8	0.6	[-2, -1, -2, -3, -2, -1, -2, -2, -2, -1]
gloominesses	-1.0	1.09545	[-1, -2, -2, -1, -1, -2, 2, -1, -1, -1]
glooming	-1.8	0.74833	[-1, -2, -2, -1, -2, -1, -3, -1, -3, -2]
glooms	-0.9	1.57797	[3, -2, -1, -2, -2, -2, 1, -2, -1, -1]
gloomy	-0.6	1.56205	[2, -1, -2, -2, -2, -1, 1, -2, 2, -1]
gloried	2.4	1.0198	[2, 3, 3, 4, 4, 1, 1, 2, 2, 2]
glories	2.1	1.3	[4, 1, 4, 1, 1, 4, 2, 2, 1, 1]
glorification	2.0	0.89443	[3, 1, 3, 2, 3, 1, 1, 3, 2, 1]
glorified	2.3	0.9	[1, 4, 2, 2, 4, 2, 2, 2, 2, 2]
glorifier	2.3	1.00499	[1, 4, 1, 2, 4, 2, 3, 2, 2, 2]
glorifiers	1.6	1.0198	[2, 1, 2, -1, 2, 2, 2, 1, 2, 3]
glorifies	2.2	0.9798	[1, 4, 2, 2, 4, 2, 1, 2, 2, 2]
glorify	2.7	0.78102	[3, 3, 4, 3, 1, 3, 2, 3, 2, 3]
glorifying	2.4	1.28062	[3, 4, 2, 2, 4, 4, 0, 1, 2, 2]
gloriole	1.5	1.36015	[2, 4, 1, 2, 0, -1, 1, 3, 2, 1]
glorioles	1.2	0.87178	[0, 2, 0, 2, 0, 1, 2, 2, 1, 2]
glorious	3.2	0.6	[4, 3, 3, 2, 3, 3, 3, 4, 3, 4]
gloriously	2.9	0.83066	[3, 4, 3, 2, 4, 2, 2, 2, 4, 3]
gloriousness	2.6	1.0198	[3, 2, 3, 4, 3, 2, 1, 4, 3, 1]
glory	2.5	0.80623	[1, 3, 3, 1, 3, 3, 3, 2, 3, 3]
glum	-2.1	0.7	[-2, -1, -3, -3, -1, -2, -2, -3, -2, -2]
gn8	0.6	0.66332	[1, 1, 0, 0, 0, 0, 2, 1, 0, 1]
god	1.1	1.51327	[0, 0, 0, 1, 0, 3, 0, 3, 0, 4]
goddam	-2.5	1.28452	[0, -3, -3, -4, -3, -1, -4, -1, -3, -3]
goddammed	-2.4	0.91652	[-2, -3, -1, -1, -2, -2, -4, -3, -3, -3]
goddamn	-2.1	1.75784	[-3, -3, -2, -4, -4, -3, -3, -1, 1, 1]
goddamned	-1.8	2.03961	[-3, -3, -3, -4, -1, 2, -2, -3, 2, -3]
goddamns	-2.1	1.51327	[-3, -2, -4, 2, -2, -3, -3, -2, -2, -2]
goddams	-1.9	1.92094	[-3, -3, -2, -4, -4, -2, -3, -1, 2, 1]
godsend	2.8	0.87178	[2, 3, 3, 2, 4, 3, 3, 1, 4, 3]
good	1.9	0.9434	[2, 1, 1, 3, 2, 4, 2, 2, 1, 1]
goodness	2.0	1.54919	[2, 2, 2, 3, 1, 2, -2, 4, 3, 3]
gorgeous	3.0	0.63246	[3, 3, 2, 3, 3, 3, 4, 4, 3, 2]
gorgeously	2.3	0.78102	[2, 2, 2, 3, 1, 2, 4, 3, 2, 2]
gorgeousness	2.9	0.9434	[3, 4, 3, 1, 4, 4, 2, 2, 3, 3]
gorgeousnesses	2.1	0.7	[3, 2, 1, 3, 2, 2, 1, 2, 3, 2]
gossip	-0.7	0.45826	[-1, -1, -1, 0, 0, -1, -1, 0, -1, -1]
gossiped	-1.1	0.53852	[-1, -1, -1, -1, -1, -1, 0, -1, -2, -2]
gossiper	-1.1	0.7	[-1, -1, -1, 0, -2, -1, -1, -2, 0, -2]
gossipers	-1.1	0.53852	[-1, 0, -1, -1, -1, -1, -1, -1, -2, -2]
gossiping	-1.6	0.4899	[-1, -2, -1, -2, -1, -2, -2, -1, -2, -2]
gossipmonger	-1.0	1.41421	[-1, -2, 1, -3, -2, 1, -1, -2, -2, 1]
gossipmongers	-1.4	0.66332	[-2, -1, -1, -1, -2, -2, -1, 0, -2, -2]
gossipped	-1.3	0.9	[-2, -2, -1, -2, -1, -1, -2, -1, 1, -2]
gossipping	-1.8	0.6	[-2, -1, -2, -2, -1, -2, -2, -1, -3, -2]
gossipries	-0.8	0.6	[-1, -1, -1, 0, -1, 0, -1, 0, -2, -1]
gossipry	-1.2	1.16619	[1, 0, -1, -2, -1, -1, -1, -3, -3, -1]
gossips	-1.3	0.64031	[-1, -2, -1, -1, -2, -1, 0, -1, -2, -2]
gossipy	-1.3	0.78102	[-1, -2, -2, -1, -2, -1, 0, 0, -2, -2]
grace	1.8	0.4	[2, 1, 2, 2, 1, 2, 2, 2, 2, 2]
graced	0.9	1.04403	[1, 1, 2, -2, 1, 1, 1, 2, 1, 1]
graceful	2.0	0.63246	[2, 1, 2, 2, 2, 3, 3, 2, 2, 1]
gracefuller	2.2	0.74833	[2, 3, 2, 2, 1, 2, 3, 1, 3, 3]
gracefullest	2.8	0.74833	[3, 3, 3, 3, 1, 3, 3, 3, 4, 2]
gracefully	2.4	0.66332	[3, 3, 2, 1, 3, 2, 2, 2, 3, 3]
gracefulness	2.2	0.6	[3, 2, 1, 2, 2, 2, 3, 2, 2, 3]
graces	1.6	0.4899	[2, 2, 2, 1, 1, 2, 1, 2, 1, 2]
gracile	1.7	0.78102	[1, 3, 2, 1, 2, 3, 2, 1, 1, 1]
graciles	0.6	0.8	[0, 0, 0, 0, 0, 0, 2, 1, 2, 1]
gracilis	0.4	0.66332	[0, 0, 0, 0, 0, 1, 0, 1, 0, 2]
gracility	1.2	0.87178	[1, 1, 0, 1, 1, 3, 2, 2, 0, 1]
gracing	1.3	0.9	[1, 0, 3, 2, 1, 2, 0, 1, 2, 1]
gracioso	1.0	0.63246	[2, 2, 1, 1, 1, 1, 0, 1, 0, 1]
gracious	2.6	0.8	[2, 2, 3, 3, 3, 3, 2, 4, 1, 3]
graciously	2.3	0.9	[2, 4, 1, 3, 3, 2, 1, 2, 3, 2]
graciousness	2.4	0.66332	[2, 2, 2, 2, 2, 3, 4, 3, 2, 2]
grand	2.0	0.63246	[2, 2, 3, 1, 2, 2, 2, 2, 3, 1]
grandee	1.1	0.83066	[0, 1, 1, 0, 1, 1, 2, 1, 3, 1]
grandees	1.2	0.87178	[0, 2, 2, 1, 0, 1, 0, 2, 2, 2]
grander	1.7	0.9	[3, 1, 1, 2, 2, 2, 0, 2, 3, 1]
grandest	2.4	1.2	[3, 3, 1, 2, 2, 0, 4, 2, 4, 3]
grandeur	2.4	1.11355	[3, 2, 3, 2, 4, 1, 1, 4, 1, 3]
grandeurs	2.1	1.3	[2, 2, 1, 0, 0, 4, 3, 3, 3, 3]
grant	1.5	0.80623	[2, 2, 1, 0, 3, 1, 1, 1, 2, 2]
granted	1.0	1.09545	[3, 0, 0, 0, 0, 1, 2, 2, 2, 0]
granting	1.3	0.45826	[2, 1, 1, 2, 1, 2, 1, 1, 1, 1]
grants	0.9	0.7	[2, 1, 2, 1, 0, 1, 0, 0, 1, 1]
grateful	2.0	0.63246	[2, 3, 1, 2, 2, 2, 3, 1, 2, 2]
gratefuller	1.8	0.87178	[2, 3, 0, 3, 2, 1, 1, 2, 2, 2]
gratefully	2.1	0.53852	[2, 3, 2, 2, 2, 2, 2, 3, 1, 2]
gratefulness	2.2	0.6	[2, 3, 2, 1, 2, 2, 3, 2, 3, 2]
graticule	0.1	0.3	[0, 0, 0, 1, 0, 0, 0, 0, 0, 0]
graticules	0.2	0.6	[0, 0, 0, 0, 2, 0, 0, 0, 0, 0]
gratification	1.6	0.8	[1, 2, 3, 2, 2, 1, 2, 0, 2, 1]
gratifications	1.8	0.4	[2, 2, 1, 2, 2, 1, 2, 2, 2, 2]
gratified	1.6	0.91652	[3, 1, 1, 1, 3, 1, 1, 1, 1, 3]
gratifies	1.5	0.80623	[3, 1, 1, 1, 2, 1, 1, 1, 1, 3]
gratify	1.3	1.00499	[2, -1, 1, 3, 1, 1, 1, 1, 2, 2]
gratifying	2.3	0.45826	[2, 3, 2, 2, 2, 2, 3, 2, 2, 3]
gratifyingly	2.0	0.63246	[2, 2, 2, 1, 3, 2, 1, 3, 2, 2]
gratin	0.4	0.91652	[0, 1, 0, 0, 0, 0, 2, 0, -1, 2]
grating	-0.4	0.4899	[-1, -1, -1, 0, 0, -1, 0, 0, 0, 0]
gratingly	-0.2	1.6	[1, -3, -2, -2, 0, 2, 1, -1, 1, 1]
gratings	-0.8	0.9798	[0, -2, -1, -1, 0, 0, -3, 0, 0, -1]
gratins	0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, 2, 0]
gratis	0.2	0.9798	[-2, 1, 1, 0, 0, 0, -1, 1, 1, 1]
gratitude	2.3	0.64031	[2, 2, 3, 1, 2, 3, 2, 3, 3, 2]
gratz	2.0	0.89443	[2, 3, 2, 1, 1, 1, 2, 2, 4, 2]
grave	-1.6	1.62481	[-2, -2, 0, -2, -3, 2, -3, -1, -1, -4]
graved	-0.9	1.13578	[0, 0, -1, 0, -1, -1, -1, -1, -4, 0]
gravel	-0.5	0.5	[0, -1, 0, -1, -1, -1, -1, 0, 0, 0]
graveled	-0.5	0.80623	[0, 0, 0, 0, -1, -2, 0, 0, -2, 0]
graveless	-1.3	1.34536	[-2, -2, 0, -1, 0, -2, -4, 1, -1, -2]
graveling	-0.4	1.28062	[-3, 0, -2, 0, 2, -1, 0, 0, 0, 0]
gravelled	-0.9	0.53852	[0, -1, -1, -2, 0, -1, -1, -1, -1, -1]
gravelling	-0.4	1.11355	[0, -1, -2, -2, 0, -1, 0, 0, 2, 0]
gravelly	-0.9	0.7	[0, -2, -1, -1, 0, -1, -1, 0, -1, -2]
gravels	-0.5	0.80623	[0, -1, 0, 0, 0, -2, 0, 0, 0, -2]
gravely	-1.5	1.0247	[0, -3, -1, 0, -3, -2, -1, -1, -2, -2]
graven	-0.9	1.22066	[0, 0, -1, 0, -2, -1, -1, 0, -4, 0]
graveness	-1.5	0.67082	[-1, -2, -2, -3, -2, -1, -1, -1, -1, -1]
graver	-1.1	1.22066	[0, -2, -1, 0, 0, -2, -1, 0, -4, -1]
gravers	-1.2	1.6	[-4, 0, -2, 0, -2, -2, -2, 0, 2, -2]
graves	-1.2	1.07703	[0, 0, -1, -1, -2, -1, -1, -1, -4, -1]
graveside	-0.8	0.6	[-1, -1, -1, 0, 0, 0, -1, -2, -1, -1]
gravesides	-1.6	1.2	[-2, -1, 0, -3, -1, 0, -1, -2, -2, -4]
gravest	-1.3	1.9	[-3, -1, -2, -2, 1, 1, -2, 2, -3, -4]
gravestone	-0.7	0.78102	[0, -1, 0, -1, -1, -2, 0, 0, -2, 0]
gravestones	-0.5	0.5	[-1, -1, 0, -1, -1, -1, 0, 0, 0, 0]
graveyard	-1.2	0.87178	[0, 0, -1, -2, -1, -1, -2, -1, -3, -1]
graveyards	-1.2	0.87178	[0, -1, -3, -1, -1, -2, 0, -1, -2, -1]
great	3.1	0.7	[2, 4, 4, 4, 3, 3, 3, 3, 2, 3]
greater	1.5	0.67082	[2, 1, 2, 1, 2, 2, 2, 1, 0, 2]
greatest	3.2	0.74833	[3, 4, 3, 3, 3, 4, 4, 2, 2, 4]
greed	-1.7	1.61555	[-2, -1, -2, -1, -2, -4, -1, 2, -4, -2]
greedier	-2.0	0.63246	[-2, -2, -2, -2, -2, -3, -2, -1, -1, -3]
greediest	-2.8	0.87178	[-3, -4, -3, -2, -2, -4, -4, -2, -2, -2]
greedily	-1.9	1.22066	[-2, -1, -3, -3, -3, -2, -1, -2, 1, -3]
greediness	-1.7	1.00499	[-2, -1, -2, -1, -2, -4, -1, 0, -2, -2]
greeds	-1.0	1.18322	[-1, -2, -2, -2, -2, -2, 1, 0, -1, 1]
greedy	-1.3	1.48661	[-2, -2, -2, -2, -3, -2, 2, -1, -2, 1]
greenwash	-1.8	1.4	[-1, 0, -2, -2, -3, -4, 0, 0, -3, -3]
greenwashing	-0.4	0.91652	[-1, 0, 0, 1, 1, -2, -1, -1, 0, -1]
greet	1.3	0.64031	[1, 1, 1, 1, 0, 2, 2, 2, 2, 1]
greeted	1.1	0.9434	[2, 0, 0, 1, 1, 1, 0, 2, 1, 3]
greeting	1.6	0.4899	[2, 1, 2, 2, 1, 2, 2, 1, 1, 2]
greetings	1.8	1.07703	[1, 2, 3, 3, 1, 1, 1, 4, 1, 1]
greets	0.6	0.66332	[0, 0, 2, 0, 1, 1, 0, 1, 1, 0]
grey	0.2	0.4	[0, 0, 1, 0, 0, 0, 1, 0, 0, 0]
grief	-2.2	0.6	[-2, -1, -3, -2, -3, -3, -2, -2, -2, -2]
grievance	-2.1	0.53852	[-2, -2, -3, -2, -2, -3, -2, -1, -2, -2]
grievances	-1.5	0.5	[-2, -2, -1, -1, -2, -1, -1, -2, -2, -1]
grievant	-0.8	1.249	[-2, 1, -1, -2, -1, 2, -2, -1, -1, -1]
grievants	-1.1	0.83066	[-1, -1, -1, -1, -1, -1, 0, -2, 0, -3]
grieve	-1.6	1.49666	[-2, -3, -1, -3, 1, -3, -1, -2, 1, -3]
grieved	-2.0	0.89443	[-3, -3, -3, -2, -2, -3, -1, -1, -1, -1]
griever	-1.9	0.83066	[-2, -2, -3, -2, -3, -3, -1, -1, -1, -1]
grievers	-0.3	1.55242	[-1, 2, -2, -1, 1, -2, 2, -2, 1, -1]
grieves	-2.1	0.9434	[-3, -3, -3, -2, -3, -3, -1, -1, -1, -1]
grieving	-2.3	1.1	[-2, -4, -2, -3, -3, 0, -3, -2, -1, -3]
grievous	-2.0	1.84391	[-3, -4, -2, -3, -1, 3, -2, -3, -2, -3]
grievously	-1.7	1.55242	[-3, -1, -4, -2, -2, 1, -3, 1, -2, -2]
grievousness	-2.7	0.78102	[-3, -3, -3, -2, -3, -4, -2, -1, -3, -3]
grim	-2.7	0.64031	[-2, -4, -3, -3, -2, -2, -3, -3, -2, -3]
grimace	-1.0	2.14476	[-4, -3, -2, -2, 2, -1, -1, -3, 2, 2]
grimaced	-2.0	0.63246	[-2, -3, -2, -2, -2, -2, -1, -3, -1, -2]
grimaces	-1.8	0.74833	[-1, -2, -1, -2, -2, -1, -3, -3, -1, -2]
grimacing	-1.4	1.56205	[-3, -2, -3, -3, -1, -1, 1, 0, 1, -3]
grimalkin	-0.9	1.04403	[-1, -2, -1, 1, -2, -2, -1, -1, 1, -1]
grimalkins	-0.9	0.9434	[0, 0, 0, -2, -1, -1, -1, 0, -3, -1]
grime	-1.5	0.92195	[-1, -2, -1, -1, -1, -3, 0, -2, -1, -3]
grimed	-1.2	0.74833	[-2, -2, 0, -2, -1, -1, -1, -2, -1, 0]
grimes	-1.0	0.7746	[-2, -2, 0, 0, -1, -1, -1, -2, -1, 0]
grimier	-1.6	1.0198	[-1, -2, -2, -2, -1, 0, -4, -1, -1, -2]
grimiest	-0.7	2.05183	[-2, -3, 1, -3, 2, -2, -2, -2, 2, 2]
grimily	-0.7	1.61555	[-2, -1, -1, -2, -2, 2, -2, 2, 1, -2]
griminess	-1.6	0.4899	[-1, -2, -2, -2, -1, -2, -2, -1, -1, -2]
griming	-0.7	1.55242	[-3, -1, -1, -2, 0, 2, -1, -2, -1, 2]
grimly	-1.3	1.55242	[-2, -1, 2, -2, -2, -1, -3, -3, 1, -2]
grimmer	-1.5	1.36015	[1, -2, -3, 1, -2, -1, -2, -2, -3, -2]
grimmest	-0.8	1.72047	[-2, -1, -3, 0, -2, -1, 0, 2, 2, -3]
grimness	-0.8	1.98997	[2, -2, -3, -2, -3, 2, -1, 1, 1, -3]
grimy	-1.8	0.87178	[-2, -2, -2, -1, -1, -3, 0, -2, -2, -3]
grin	2.1	0.83066	[2, 4, 2, 2, 2, 1, 2, 3, 1, 2]
grinned	1.1	0.9434	[1, 1, 2, 1, 3, 1, -1, 1, 1, 1]
grinner	1.1	0.83066	[1, 1, 2, 2, 2, 1, -1, 1, 1, 1]
grinners	1.6	0.8	[2, 2, 2, 3, 1, 0, 2, 2, 1, 1]
grinning	1.5	1.0247	[3, 2, 2, 1, 1, -1, 1, 2, 2, 2]
grins	0.9	1.92094	[1, 4, -3, 1, 2, 1, 2, -2, 2, 1]
gross	-2.1	1.51327	[-1, -3, -2, -3, -3, -3, -3, 2, -2, -3]
grossed	-0.4	1.11355	[-1, -2, -1, -2, 0, 0, 1, 1, 1, -1]
grosser	-0.3	1.41774	[1, -2, -1, -2, 0, -1, 1, 2, 1, -2]
grosses	-0.8	1.77764	[-2, -2, 3, -2, -3, -1, 1, -1, 1, -2]
grossest	-2.1	0.83066	[-2, -1, -2, -3, -3, -1, -3, -3, -1, -2]
grossing	-0.3	1.79165	[-1, -3, -1, 1, 0, -1, -3, 1, 1, 3]
grossly	-0.9	1.44568	[1, -2, 0, -3, -1, -2, 1, 1, -2, -2]
grossness	-1.8	0.6	[-2, -2, -1, -1, -1, -3, -2, -2, -2, -2]
grossular	-0.3	1.34536	[0, -2, 0, -1, -2, -1, 0, 0, 0, 3]
grossularite	-0.1	1.13578	[2, 0, 0, 0, 0, -3, 0, 0, 0, 0]
grossularites	-0.7	1.26886	[0, -4, -1, -2, 0, 0, 0, 0, 0, 0]
grossulars	-0.3	0.45826	[0, 0, 0, 0, -1, -1, 0, 0, 0, -1]
grouch	-2.2	0.87178	[-2, -3, -3, -3, -2, -1, -3, -1, -1, -3]
grouched	-0.8	0.9798	[-2, -1, -2, -2, 1, -1, 0, 0, 0, -1]
grouches	-0.9	1.13578	[-2, -1, -2, -1, -1, -1, -1, 2, 0, -2]
grouchier	-2.0	0.63246	[-2, -3, -3, -2, -2, -2, -2, -1, -2, -1]
grouchiest	-2.3	0.78102	[-3, -3, -2, -2, -1, -3, -1, -3, -3, -2]
grouchily	-1.4	1.56205	[-2, -1, -2, -3, -3, 2, -2, -2, 1, -2]
grouchiness	-2.0	0.7746	[-2, -2, -3, -2, -3, -2, -2, 0, -2, -2]
grouching	-1.7	1.1	[-1, -3, -2, 1, -2, -3, -2, -1, -2, -2]
grouchy	-1.9	0.7	[-1, -3, -2, -2, -1, -3, -1, -2, -2, -2]
growing	0.7	0.64031	[0, 1, 1, 2, 1, 0, 0, 1, 1, 0]
growth	1.6	1.0198	[2, 0, 3, 0, 2, 3, 1, 1, 2, 2]
guarantee	1.0	1.0	[2, 3, 1, 0, 2, 0, 1, 0, 0, 1]
guilt	-1.1	1.22066	[-1, -3, 2, -1, -1, -2, -1, -2, -1, -1]
guiltier	-2.0	0.63246	[-2, -1, -3, -1, -3, -2, -2, -2, -2, -2]
guiltiest	-1.7	1.79165	[-3, -2, -2, -4, -1, -2, 3, -1, -3, -2]
guiltily	-1.1	1.57797	[-3, -1, -1, -2, 0, -2, 3, -2, -1, -2]
guiltiness	-1.8	0.6	[-2, -2, -1, -2, -1, -1, -2, -2, -3, -2]
guiltless	0.8	1.53623	[3, 2, 1, 2, -2, 1, 1, -2, 1, 1]
guiltlessly	0.7	1.18743	[-1, 1, -2, 2, 1, 1, 1, 1, 2, 1]
guiltlessness	0.6	1.42829	[1, 1, -1, -1, -1, 1, 2, 3, -1, 2]
guilts	-1.4	0.66332	[-1, -2, -1, -1, -3, -1, -1, -2, -1, -1]
guilty	-1.8	0.6	[-1, -2, -2, -3, -2, -2, -1, -2, -2, -1]
gullibility	-1.6	0.66332	[-1, -1, -2, -2, -1, -2, -1, -2, -1, -3]
gullible	-1.5	0.67082	[-1, -2, -2, -1, -1, -3, -2, -1, -1, -1]
gun	-1.4	1.49666	[0, -4, 0, -3, 0, -3, -2, 0, -2, 0]
h8	-2.7	1.00499	[-4, -3, -4, -1, -1, -3, -3, -3, -2, -3]
ha	1.4	0.8	[1, 0, 1, 1, 3, 2, 2, 1, 2, 1]
hacked	-1.7	1.00499	[-2, -1, -2, -4, 0, -2, -2, -1, -1, -2]
haha	2.0	0.89443	[1, 1, 2, 3, 2, 1, 4, 2, 2, 2]
hahaha	2.6	1.0198	[2, 4, 2, 4, 1, 2, 3, 4, 2, 2]
hahas	1.8	0.9798	[1, 2, 2, 4, 3, 2, 1, 1, 1, 1]
hail	0.3	0.9	[0, 0, 0, 2, 0, 0, 2, 0, -1, 0]
hailed	0.9	0.83066	[1, 2, 1, 1, 0, 0, 0, 2, 2, 0]
hallelujah	3.0	0.7746	[3, 4, 3, 1, 3, 3, 3, 3, 3, 4]
handsome	2.2	0.74833	[2, 2, 2, 2, 2, 3, 4, 1, 2, 2]
handsomely	1.9	0.7	[1, 3, 1, 1, 2, 2, 2, 2, 2, 3]
handsomeness	2.4	1.28062	[2, 4, 1, 4, 0, 2, 4, 3, 2, 2]
handsomer	2.0	0.63246	[2, 3, 2, 2, 2, 2, 1, 3, 1, 2]
handsomest	2.6	0.91652	[3, 2, 1, 2, 3, 4, 4, 3, 2, 2]
hapless	-1.4	1.11355	[-3, -1, -1, -1, -2, -1, 1, -2, -3, -1]
haplessness	-1.4	1.0198	[-1, -2, 0, -1, -1, -1, -1, -2, -1, -4]
happier	2.4	0.66332	[3, 1, 2, 3, 2, 2, 3, 3, 3, 2]
happiest	3.2	0.74833	[4, 3, 2, 4, 4, 3, 3, 3, 2, 4]
happily	2.6	0.91652	[4, 1, 2, 4, 2, 3, 2, 2, 3, 3]
happiness	2.6	0.4899	[2, 3, 3, 3, 3, 2, 2, 3, 2, 3]
happing	1.1	0.83066	[1, 1, 1, 0, 2, 2, 0, 2, 0, 2]
happy	2.7	0.9	[2, 2, 2, 4, 2, 4, 3, 4, 2, 2]
harass	-2.2	0.6	[-2, -3, -2, -2, -2, -3, -2, -3, -1, -2]
harassed	-2.5	0.80623	[-4, -2, -3, -3, -2, -2, -3, -2, -3, -1]
harasser	-2.4	0.8	[-3, -2, -2, -3, -2, -2, -4, -2, -3, -1]
harassers	-2.8	0.6	[-3, -3, -2, -2, -3, -3, -3, -4, -2, -3]
harasses	-2.5	0.80623	[-3, -3, -2, -3, -2, -2, -4, -2, -3, -1]
harassing	-2.5	1.62788	[-2, -4, -3, -4, -3, -3, 2, -2, -3, -3]
harassment	-2.5	0.67082	[-2, -3, -2, -2, -3, -2, -4, -3, -2, -2]
harassments	-2.6	0.4899	[-3, -3, -2, -3, -2, -3, -3, -2, -2, -3]
hard	-0.4	1.2	[0, -1, 0, -1, 0, 1, -2, -1, -2, 2]
hardier	-0.6	1.68523	[-3, -2, -2, 1, 1, 2, 1, 0, -2, -2]
hardship	-1.3	1.84662	[-2, 2, -4, -1, -2, -3, -1, -2, 2, -2]
hardy	1.7	1.00499	[1, 2, 1, 0, 2, 2, 4, 1, 2, 2]
harm	-2.5	0.80623	[-2, -2, -2, -3, -2, -1, -3, -3, -3, -4]
harmed	-2.1	0.83066	[-1, -4, -2, -2, -2, -2, -1, -2, -3, -2]
harmfully	-2.6	0.91652	[-3, -4, -3, -1, -3, -3, -3, -3, -1, -2]
harmfulness	-2.6	0.8	[-3, -1, -3, -3, -3, -2, -4, -2, -2, -3]
harming	-2.6	0.66332	[-3, -3, -2, -2, -3, -3, -2, -2, -2, -4]
harmless	1.0	0.7746	[2, 1, 1, 1, 1, 0, 0, 2, 0, 2]
harmlessly	1.4	1.2	[4, 0, 1, 0, 1, 1, 1, 2, 3, 1]
harmlessness	0.8	1.16619	[1, 2, 0, 1, 0, 1, 2, 1, 2, -2]
harmonic	1.8	0.87178	[2, 1, 2, 3, 0, 1, 3, 2, 2, 2]
harmonica	0.6	0.8	[0, 1, 0, 0, 0, 2, 1, 0, 2, 0]
harmonically	2.1	1.13578	[3, 3, 4, 1, 0, 1, 2, 2, 2, 3]
harmonicas	0.1	0.3	[0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
harmonicist	0.5	0.92195	[0, 0, 0, 0, 1, 0, 0, 0, 1, 3]
harmonicists	0.9	1.3	[2, 1, 0, 0, 2, 0, 0, 0, 4, 0]
harmonics	1.5	1.0247	[2, 2, 2, 0, 0, 2, 3, 2, 0, 2]
harmonies	1.3	0.9	[2, 0, 2, 1, 2, 2, 0, 2, 0, 2]
harmonious	2.0	1.09545	[3, 4, 2, 2, 2, 0, 2, 3, 1, 1]
harmoniously	1.9	0.9434	[4, 2, 2, 1, 2, 1, 1, 3, 2, 1]
harmoniousness	1.8	0.6	[1, 2, 2, 2, 2, 1, 2, 3, 2, 1]
harmonise	1.8	0.74833	[1, 1, 2, 3, 2, 1, 1, 3, 2, 2]
harmonised	1.3	0.9	[2, 3, 2, 0, 2, 1, 1, 1, 1, 0]
harmonising	1.4	0.66332	[1, 2, 1, 1, 2, 1, 1, 1, 1, 3]
harmonium	0.9	1.22066	[0, 3, 0, 2, 3, 0, 0, 0, 0, 1]
harmoniums	0.8	0.9798	[0, 0, 0, 2, 0, 2, 0, 2, 2, 0]
harmonization	1.9	0.83066	[3, 1, 2, 2, 2, 2, 3, 0, 2, 2]
harmonizations	0.9	0.9434	[0, 0, 2, 0, 2, 2, 0, 2, 1, 0]
harmonize	1.7	0.78102	[3, 2, 2, 1, 2, 2, 0, 1, 2, 2]
harmonized	1.6	0.91652	[1, 2, 1, 1, 2, 2, 3, 3, 0, 1]
harmonizer	1.6	0.8	[1, 2, 1, 1, 2, 2, 2, 3, 0, 2]
harmonizers	1.6	1.11355	[2, 2, 2, 1, 2, 0, 4, 0, 2, 1]
harmonizes	1.5	0.92195	[0, 2, 2, 2, 0, 1, 1, 2, 3, 2]
harmonizing	1.4	0.66332	[0, 1, 1, 2, 2, 1, 2, 2, 1, 2]
harmony	1.7	0.45826	[2, 2, 2, 2, 1, 2, 2, 2, 1, 1]
harms	-2.2	1.6	[2, -3, -2, -2, -3, -2, -4, -4, -2, -2]
harried	-1.4	0.4899	[-1, -1, -2, -1, -2, -2, -1, -1, -1, -2]
harsh	-1.9	0.7	[-1, -1, -2, -2, -1, -3, -3, -2, -2, -2]
harsher	-2.2	0.6	[-2, -3, -2, -3, -2, -2, -1, -3, -2, -2]
harshest	-2.9	0.83066	[-4, -2, -2, -2, -2, -3, -3, -4, -4, -3]
hate	-2.7	1.00499	[-4, -3, -4, -4, -2, -2, -2, -2, -1, -3]
hated	-3.2	0.6	[-3, -3, -4, -3, -2, -3, -3, -4, -4, -3]
hateful	-2.2	1.249	[-3, 1, -3, -3, -1, -2, -2, -3, -3, -3]
hatefully	-2.3	0.78102	[-1, -3, -3, -3, -1, -2, -2, -2, -3, -3]
hatefulness	-3.6	0.4899	[-4, -4, -3, -3, -3, -4, -4, -4, -4, -3]
hater	-1.8	0.6	[-2, -1, -2, -2, -2, -1, -1, -2, -2, -3]
haters	-2.2	0.6	[-2, -1, -3, -2, -3, -2, -3, -2, -2, -2]
hates	-1.9	0.7	[-2, -1, -2, -2, -3, -1, -1, -2, -2, -3]
hating	-2.3	1.1	[-4, -3, -4, -1, -2, -2, -1, -2, -1, -3]
hatred	-3.2	0.9798	[-1, -3, -2, -4, -3, -3, -4, -4, -4, -4]
haunt	-1.7	1.00499	[-1, -1, -3, -1, -2, -2, -1, -4, -1, -1]
haunted	-2.1	0.7	[-2, -2, -1, -3, -3, -2, -2, -3, -1, -2]
haunting	-1.1	0.83066	[-3, 0, -2, -1, 0, -1, -1, -1, -1, -1]
haunts	-1.0	1.41421	[0, -2, -2, -2, -2, -1, 2, -2, 1, -2]
havoc	-2.9	0.7	[-2, -4, -4, -3, -2, -3, -3, -3, -2, -3]
healthy	1.7	0.9	[1, 3, 1, 1, 3, 3, 1, 2, 1, 1]
heartbreak	-2.7	0.78102	[-1, -3, -3, -3, -2, -4, -2, -3, -3, -3]
heartbreaker	-2.2	1.07703	[-2, -3, 0, -3, -2, -1, -4, -3, -2, -2]
heartbreakers	-2.1	0.9434	[-3, -2, -3, -2, -1, -1, -4, -1, -2, -2]
heartbreaking	-2.0	1.73205	[-3, -1, -3, -3, -4, 2, -3, -2, 0, -3]
heartbreakingly	-1.8	2.08806	[-3, 3, 1, -3, -3, -2, -3, -3, -4, -1]
heartbreaks	-1.8	1.77764	[-2, 1, -3, -2, -3, -2, -3, 2, -4, -2]
heartbroken	-3.3	0.45826	[-4, -3, -3, -4, -3, -3, -4, -3, -3, -3]
heartfelt	2.5	0.5	[3, 3, 2, 3, 2, 2, 3, 2, 2, 3]
heartless	-2.2	0.74833	[-2, -2, -2, -4, -2, -1, -2, -3, -2, -2]
heartlessly	-2.8	0.6	[-3, -2, -3, -3, -2, -3, -4, -2, -3, -3]
heartlessness	-2.8	0.87178	[-3, -3, -2, -3, -4, -4, -1, -3, -2, -3]
heartwarming	2.1	1.22066	[3, 2, 3, 3, 2, 2, 3, 3, -1, 1]
heaven	2.3	1.18743	[1, 1, 2, 4, 3, 3, 3, 4, 1, 1]
heavenlier	3.0	0.63246	[3, 2, 3, 3, 4, 3, 3, 4, 2, 3]
heavenliest	2.7	1.1	[3, 2, 3, 4, 2, 4, 3, 0, 3, 3]
heavenliness	2.7	0.9	[3, 2, 1, 4, 3, 2, 3, 4, 3, 2]
heavenlinesses	2.3	2.2383	[4, 4, 4, 3, -2, 3, 3, 4, -2, 2]
heavenly	3.0	0.63246	[3, 3, 3, 3, 2, 3, 3, 4, 2, 4]
heavens	1.7	1.18743	[4, 0, 1, 2, 0, 3, 2, 2, 2, 1]
heavenward	1.4	1.35647	[0, 3, 0, 4, 1, 2, 2, 0, 2, 0]
heavenwards	1.2	1.32665	[1, 4, 0, 0, 2, 1, 1, 0, 3, 0]
heavyhearted	-2.1	0.83066	[-2, -3, -3, -2, -3, -1, -1, -1, -2, -3]
heh	-0.6	1.28062	[0, 1, -1, 1, -1, -2, -1, -3, 1, -1]
hell	-3.6	0.66332	[-4, -4, -4, -4, -4, -2, -3, -4, -3, -4]
hellish	-3.2	0.74833	[-3, -3, -2, -2, -4, -3, -4, -4, -3, -4]
help	1.7	0.78102	[3, 2, 1, 2, 1, 2, 3, 1, 1, 1]
helper	1.4	0.8	[1, 1, 0, 1, 1, 2, 1, 2, 3, 2]
helpers	1.1	0.83066	[1, 1, 0, 2, 1, 1, 1, 1, 3, 0]
helpful	1.8	0.87178	[2, 1, 3, 1, 1, 3, 1, 2, 3, 1]
helpfully	2.3	0.9	[1, 2, 2, 3, 2, 3, 3, 2, 4, 1]
helpfulness	1.9	1.13578	[1, 4, 1, 2, 2, 1, 1, 2, 4, 1]
helping	1.2	0.6	[2, 1, 1, 2, 0, 1, 1, 1, 2, 1]
helpless	-2.0	0.63246	[-2, -3, -2, -2, -2, -3, -1, -2, -1, -2]
helplessly	-1.4	0.4899	[-1, -1, -2, -2, -1, -1, -1, -2, -2, -1]
helplessness	-2.1	0.9434	[-2, -4, -1, -2, -1, -3, -3, -2, -1, -2]
helplessnesses	-1.7	0.64031	[-2, -1, -2, -1, -2, -1, -3, -2, -1, -2]
helps	1.6	0.4899	[1, 1, 1, 2, 2, 2, 1, 2, 2, 2]
hero	2.6	0.8	[2, 3, 2, 2, 4, 4, 2, 3, 2, 2]
heroes	2.3	0.9	[3, 4, 3, 1, 3, 2, 1, 2, 2, 2]
heroic	2.6	0.8	[3, 3, 1, 4, 2, 3, 2, 3, 2, 3]
heroical	2.9	1.04403	[4, 4, 2, 4, 2, 3, 1, 4, 2, 3]
heroically	2.4	0.8	[2, 2, 2, 3, 3, 3, 4, 1, 2, 2]
heroicomic	1.0	1.0	[1, 0, 1, 0, 2, 0, 3, 1, 2, 0]
heroicomical	1.1	0.83066	[2, 0, 0, 2, 1, 2, 1, 2, 1, 0]
heroics	2.4	0.8	[2, 1, 2, 2, 2, 3, 3, 3, 4, 2]
heroin	-2.2	1.83303	[0, -2, -4, 2, -4, -2, -2, -3, -3, -4]
heroine	2.7	1.1	[0, 2, 4, 4, 3, 3, 3, 3, 2, 3]
heroines	1.8	1.32665	[2, 1, 1, 4, 3, 1, 0, 3, 3, 0]
heroinism	-2.0	2.0	[-3, -4, 2, -2, -4, -3, -2, -1, 1, -4]
heroism	2.8	0.6	[3, 3, 4, 3, 2, 2, 3, 3, 2, 3]
heroisms	2.2	0.87178	[3, 1, 2, 4, 3, 2, 2, 2, 1, 2]
heroize	2.1	0.7	[3, 2, 3, 1, 2, 2, 2, 3, 1, 2]
heroized	2.0	1.18322	[1, 0, 3, 3, 2, 0, 3, 3, 2, 3]
heroizes	2.2	0.9798	[1, 3, 2, 3, 4, 3, 2, 1, 2, 1]
heroizing	1.9	1.64012	[2, 3, -2, 2, 4, 3, 2, 2, 3, 0]
heron	0.1	0.3	[0, 0, 0, 0, 0, 1, 0, 0, 0, 0]
heronries	0.7	1.1	[2, 0, 0, 0, 2, 0, 3, 0, 0, 0]
heronry	0.1	0.9434	[0, 0, 0, 0, 0, 2, 0, 1, -2, 0]
herons	0.5	1.0247	[0, 0, 0, 3, 0, 2, 0, 0, 0, 0]
heros	1.3	1.18743	[3, 0, 0, 2, 0, 2, 2, 3, 1, 0]
hesitance	-0.9	0.3	[-1, -1, 0, -1, -1, -1, -1, -1, -1, -1]
hesitancies	-1.0	0.63246	[-1, -1, -1, -2, -1, -1, -2, 0, 0, -1]
hesitancy	-0.9	0.53852	[0, -1, 0, -2, -1, -1, -1, -1, -1, -1]
hesitant	-1.0	0.7746	[0, -1, 0, -1, -2, 0, -1, -2, -1, -2]
hesitantly	-1.2	0.4	[-1, -1, -2, -1, -1, -1, -1, -2, -1, -1]
hesitate	-1.1	0.53852	[-2, -1, -1, -1, -2, -1, -1, 0, -1, -1]
hesitated	-1.3	0.9	[-1, -2, -1, -2, -2, -2, -1, 1, -1, -2]
hesitater	-1.4	0.66332	[-1, -1, -1, -1, -1, -2, -1, -3, -2, -1]
hesitaters	-1.4	0.4899	[-1, -2, -1, -1, -1, -1, -2, -2, -2, -1]
hesitates	-1.4	0.4899	[-1, -1, -1, -2, -1, -2, -1, -2, -2, -1]
hesitating	-1.4	0.66332	[-1, -1, -2, -1, -1, -3, -2, -1, -1, -1]
hesitatingly	-1.5	0.80623	[-1, -1, -1, -1, -3, -2, -3, -1, -1, -1]
hesitation	-1.1	0.53852	[-2, 0, -1, -1, -1, -1, -1, -2, -1, -1]
hesitations	-1.1	0.53852	[-1, -1, -1, 0, -2, -1, -1, -2, -1, -1]
hid	-0.4	0.4899	[0, -1, 0, 0, -1, -1, -1, 0, 0, 0]
hide	-0.7	0.64031	[0, -1, -1, -1, -1, 0, 0, -2, -1, 0]
hides	-0.7	0.9	[-1, -2, -1, 0, -1, 0, 0, -2, 1, -1]
hiding	-1.2	0.4	[-1, -1, -2, -1, -1, -1, -1, -1, -2, -1]
highlight	1.4	0.91652	[3, 0, 1, 1, 2, 1, 0, 2, 2, 2]
hilarious	1.7	1.41774	[2, 2, 2, 3, 3, 1, -2, 2, 3, 1]
hindrance	-1.7	0.78102	[-2, -3, -2, -1, -1, -1, -3, -1, -2, -1]
hoax	-1.1	1.04403	[-3, -1, -2, -1, 1, -2, -1, -1, -1, 0]
holiday	1.7	1.18743	[1, 3, 2, 2, 0, 0, 1, 2, 4, 2]
holidays	1.6	1.0198	[2, 0, 1, 2, 3, 0, 1, 2, 3, 2]
homesick	-0.7	1.67631	[-2, -1, -1, -2, -1, -2, 2, -1, 3, -2]
homesickness	-1.8	1.249	[-3, -2, -1, -1, -3, -2, -1, 1, -3, -3]
homesicknesses	-1.8	0.6	[-1, -2, -2, -2, -1, -2, -1, -2, -3, -2]
honest	2.3	0.9	[3, 2, 1, 2, 3, 1, 2, 3, 2, 4]
honester	1.9	0.7	[2, 3, 2, 2, 1, 3, 1, 1, 2, 2]
honestest	3.0	0.7746	[1, 3, 3, 3, 3, 3, 4, 4, 3, 3]
honesties	1.8	1.07703	[4, 3, 1, 1, 1, 1, 3, 2, 1, 1]
honestly	2.0	0.63246	[2, 3, 2, 2, 1, 2, 2, 1, 3, 2]
honesty	2.2	0.6	[2, 3, 2, 2, 1, 2, 3, 2, 3, 2]
honor	2.2	1.16619	[3, 2, 1, 2, 0, 4, 3, 1, 3, 3]
honorability	2.2	0.4	[3, 3, 2, 2, 2, 2, 2, 2, 2, 2]
honorable	2.5	0.67082	[2, 2, 3, 2, 4, 3, 2, 2, 2, 3]
honorableness	2.2	0.87178	[2, 4, 1, 3, 3, 2, 1, 2, 2, 2]
honorably	2.4	0.66332	[2, 2, 3, 2, 3, 3, 3, 1, 2, 3]
honoraria	0.6	0.8	[1, 0, 1, 0, 0, 0, 2, 0, 2, 0]
honoraries	1.5	1.5	[2, 2, 1, 3, 3, 1, -2, 2, 3, 0]
honorarily	1.9	0.7	[2, 2, 3, 1, 2, 1, 2, 1, 3, 2]
honorarium	0.7	1.48661	[3, 2, -1, 2, 1, -2, 1, 1, -1, 1]
honorariums	1.0	1.0	[0, 0, 0, 0, 1, 2, 1, 3, 2, 1]
honorary	1.4	0.91652	[2, 2, 2, 3, 1, 1, 0, 1, 2, 0]
honored	2.8	0.87178	[3, 4, 4, 3, 1, 2, 2, 3, 3, 3]
honoree	2.1	0.7	[3, 2, 3, 3, 1, 2, 1, 2, 2, 2]
honorees	2.3	0.78102	[1, 3, 3, 4, 2, 2, 2, 2, 2, 2]
honorer	1.7	0.78102	[2, 1, 3, 3, 1, 2, 1, 2, 1, 1]
honorers	1.3	0.45826	[1, 1, 2, 2, 1, 1, 2, 1, 1, 1]
honorific	1.4	1.2	[2, 2, 2, 2, 2, 1, 2, 1, 2, -2]
honorifically	2.2	0.74833	[3, 4, 1, 2, 2, 2, 2, 2, 2, 2]
honorifics	1.7	0.78102	[1, 2, 0, 1, 2, 2, 2, 3, 2, 2]
honoring	2.3	0.64031	[3, 3, 1, 2, 2, 3, 3, 2, 2, 2]
honors	2.3	0.64031	[3, 2, 2, 2, 1, 3, 3, 2, 3, 2]
honour	2.7	0.78102	[2, 3, 2, 2, 2, 3, 4, 4, 3, 2]
honourable	2.1	0.53852	[2, 3, 2, 2, 2, 1, 3, 2, 2, 2]
honoured	2.2	1.249	[3, 3, 4, 3, -1, 2, 2, 2, 2, 2]
honourer	1.8	0.87178	[2, 3, 2, 1, 2, 3, 1, 0, 2, 2]
honourers	1.6	1.0198	[0, 2, 2, 1, 2, 2, 1, 3, 0, 3]
honouring	2.1	0.3	[2, 2, 2, 3, 2, 2, 2, 2, 2, 2]
honours	2.2	0.87178	[4, 3, 2, 2, 3, 2, 2, 1, 2, 1]
hooligan	-1.5	0.5	[-1, -2, -1, -2, -2, -2, -1, -1, -2, -1]
hooliganism	-2.1	0.83066	[-3, -2, -3, -3, -2, -2, -2, -2, 0, -2]
hooligans	-1.1	1.22066	[-2, -1, -1, 2, -1, -1, -1, -3, -2, -1]
hooray	2.3	0.9	[3, 2, 3, 2, 1, 3, 1, 2, 2, 4]
hope	1.9	0.53852	[3, 2, 2, 1, 2, 2, 1, 2, 2, 2]
hoped	1.6	0.4899	[1, 2, 1, 1, 2, 2, 1, 2, 2, 2]
hopeful	2.3	0.78102	[2, 1, 1, 3, 3, 3, 2, 3, 2, 3]
hopefully	1.7	0.78102	[1, 3, 1, 3, 1, 2, 1, 1, 2, 2]
hopefulness	1.6	1.35647	[2, 1, -2, 2, 3, 2, 1, 3, 2, 2]
hopeless	-2.0	1.78885	[-3, -3, -3, -3, 3, -1, -3, -3, -2, -2]
hopelessly	-2.2	0.74833	[-3, -4, -2, -2, -1, -2, -2, -2, -2, -2]
hopelessness	-3.1	0.7	[-3, -4, -4, -3, -3, -3, -4, -3, -2, -2]
hopes	1.8	0.6	[1, 3, 1, 2, 2, 2, 1, 2, 2, 2]
hoping	1.8	0.4	[2, 2, 2, 1, 2, 2, 2, 2, 2, 1]
horrendous	-2.8	0.87178	[-3, -3, -4, -4, -3, -3, -2, -2, -1, -3]
horrendously	-1.9	1.92094	[-3, -2, -4, -4, 2, -1, -3, -2, 1, -3]
horrent	-0.9	1.04403	[0, -1, -2, 0, 0, -3, -1, 0, -2, 0]
horrible	-2.5	0.67082	[-2, -2, -3, -2, -2, -4, -3, -3, -2, -2]
horribleness	-2.4	0.4899	[-3, -3, -2, -2, -3, -2, -2, -3, -2, -2]
horribles	-2.1	0.7	[-2, -4, -2, -2, -1, -2, -2, -2, -2, -2]
horribly	-2.4	0.66332	[-2, -2, -2, -2, -2, -4, -3, -3, -2, -2]
horrid	-2.5	1.0247	[-2, -3, -3, -4, -4, -1, -2, -2, -3, -1]
horridly	-1.4	1.49666	[-2, 0, -3, -2, 0, -3, 2, -2, -2, -2]
horridness	-2.3	1.26886	[-3, -3, -2, 1, -3, -3, -3, -3, -1, -3]
horridnesses	-3.0	1.09545	[-2, -3, -4, -4, -4, -4, -2, -2, -1, -4]
horrific	-3.4	0.91652	[-2, -4, -4, -4, -2, -4, -2, -4, -4, -4]
horrifically	-2.9	0.7	[-2, -3, -2, -4, -2, -4, -3, -3, -3, -3]
horrified	-2.5	0.92195	[-2, -3, -2, -2, -1, -4, -2, -2, -3, -4]
horrifies	-2.9	1.22066	[-1, -3, -2, -1, -4, -4, -4, -4, -2, -4]
horrify	-2.5	0.67082	[-2, -2, -3, -2, -2, -2, -4, -2, -3, -3]
horrifying	-2.7	0.9	[-3, -4, -3, -2, -3, -3, -4, -2, -2, -1]
horrifyingly	-3.3	0.9	[-3, -3, -4, -4, -1, -4, -3, -3, -4, -4]
horror	-2.7	1.1	[-3, -1, -4, -4, -4, -3, -1, -2, -3, -2]
horrors	-2.7	0.64031	[-3, -3, -4, -3, -2, -2, -2, -3, -2, -3]
hostile	-1.6	1.74356	[-4, -3, -3, -1, 1, -2, 2, -2, -2, -2]
hostilely	-2.2	0.6	[-1, -3, -3, -2, -2, -2, -2, -2, -2, -3]
hostiles	-1.3	1.9	[-3, -3, -3, -3, -1, 2, -3, -1, 1, 1]
hostilities	-2.1	0.53852	[-2, -3, -2, -2, -2, -2, -1, -2, -3, -2]
hostility	-2.5	0.80623	[-2, -2, -3, -3, -3, -2, -4, -2, -3, -1]
huckster	-0.9	0.7	[-1, 0, -1, 0, -1, -2, -2, 0, -1, -1]
hug	2.1	1.04403	[1, 3, 2, 1, 1, 2, 3, 3, 1, 4]
huge	1.3	0.9	[0, 2, 2, 0, 0, 2, 2, 2, 1, 2]
huggable	1.6	0.66332	[2, 2, 1, 1, 1, 3, 1, 2, 2, 1]
hugged	1.7	0.78102	[2, 2, 2, 2, 3, 1, 1, 2, 0, 2]
hugger	1.6	0.91652	[3, 2, 2, 2, 3, 1, 1, 1, 0, 1]
huggers	1.8	0.6	[1, 3, 1, 2, 1, 2, 2, 2, 2, 2]
hugging	1.8	0.74833	[2, 1, 1, 3, 3, 2, 2, 1, 2, 1]
hugs	2.2	0.74833	[3, 2, 1, 2, 2, 2, 3, 1, 3, 3]
humerous	1.4	1.11355	[-1, 2, 2, 3, 1, 1, 2, 0, 2, 2]
humiliate	-2.5	1.28452	[-3, -3, -3, -3, -2, -2, -4, -3, 1, -3]
humiliated	-1.4	2.498	[3, -3, -4, -3, -3, -4, -3, 0, 1, 2]
humiliates	-1.0	2.09762	[1, -2, -3, -3, -3, -2, -3, 1, 2, 2]
humiliating	-1.2	1.8868	[-4, -1, -3, -1, 2, -1, 2, -3, -2, -1]
humiliatingly	-2.6	0.4899	[-3, -3, -2, -2, -3, -3, -3, -3, -2, -2]
humiliation	-2.7	0.78102	[-2, -3, -4, -2, -2, -2, -3, -2, -4, -3]
humiliations	-2.4	0.66332	[-3, -2, -2, -2, -2, -2, -2, -3, -2, -4]
humor	1.1	0.53852	[1, 0, 2, 2, 1, 1, 1, 1, 1, 1]
humoral	0.6	1.0198	[2, 0, 0, 0, 0, 0, 3, 1, 0, 0]
humored	1.2	0.87178	[1, 1, 2, 2, 1, 1, 2, 1, 2, -1]
humoresque	1.2	0.6	[1, 1, 1, 2, 1, 2, 2, 1, 1, 0]
humoresques	0.9	1.04403	[1, 0, 2, 0, 0, 1, 2, 0, 3, 0]
humoring	2.1	0.7	[3, 2, 2, 2, 1, 2, 2, 1, 3, 3]
humorist	1.2	0.74833	[1, 2, 1, 1, 2, 1, 2, 0, 0, 2]
humoristic	1.5	0.80623	[3, 0, 2, 1, 2, 1, 2, 2, 1, 1]
humorists	1.3	0.78102	[2, 1, 1, 1, 0, 1, 2, 3, 1, 1]
humorless	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -2, -1, -1, -2]
humorlessness	-1.4	1.11355	[1, -3, -3, -1, -1, -2, -1, -1, -2, -1]
humorous	1.6	0.4899	[1, 2, 1, 2, 2, 1, 2, 2, 1, 2]
humorously	2.3	0.78102	[3, 3, 2, 2, 1, 3, 3, 1, 2, 3]
humorousness	2.4	0.66332	[1, 2, 3, 2, 3, 2, 3, 3, 2, 3]
humors	1.6	0.4899	[2, 1, 2, 2, 2, 2, 1, 2, 1, 1]
humour	2.1	0.9434	[1, 2, 2, 4, 2, 1, 1, 3, 3, 2]
humoured	1.1	0.53852	[1, 0, 1, 2, 2, 1, 1, 1, 1, 1]
humouring	1.7	0.78102	[2, 3, 1, 2, 1, 0, 2, 2, 2, 2]
humourous	2.0	0.7746	[1, 2, 3, 2, 2, 3, 3, 2, 1, 1]
hunger	-1.0	1.67332	[-4, 0, 2, -2, -1, -2, 1, -2, 0, -2]
hurrah	2.6	0.8	[3, 2, 3, 3, 1, 3, 4, 2, 2, 3]
hurrahed	1.9	0.53852	[2, 2, 1, 1, 2, 2, 3, 2, 2, 2]
hurrahing	2.4	0.4899	[3, 2, 2, 2, 3, 2, 3, 3, 2, 2]
hurrahs	2.1	1.44568	[2, 3, 0, 3, 2, 3, -1, 2, 3, 4]
hurray	2.7	0.78102	[3, 2, 3, 3, 1, 3, 4, 2, 3, 3]
hurrayed	1.8	1.32665	[3, 3, 2, 3, 3, 3, 0, 0, 1, 0]
hurraying	1.2	1.66132	[2, -3, 2, 3, 0, 2, 2, 2, 0, 2]
hurrays	2.4	1.11355	[2, 3, 2, 4, 0, 3, 2, 2, 2, 4]
hurt	-2.4	0.8	[-3, -3, -2, -2, -4, -3, -2, -1, -2, -2]
hurter	-2.3	0.78102	[-2, -4, -1, -2, -3, -3, -2, -2, -2, -2]
hurters	-1.9	1.04403	[-2, -3, -1, -2, -1, 0, -3, -3, -3, -1]
hurtful	-2.4	1.0198	[-2, -4, -2, -2, -1, -3, -2, -3, -1, -4]
hurtfully	-2.6	0.66332	[-3, -2, -3, -2, -2, -3, -4, -3, -2, -2]
hurtfulness	-1.9	1.51327	[2, -2, -2, -2, -3, -1, -2, -3, -4, -2]
hurting	-1.7	0.78102	[-3, -1, -1, -2, -1, -1, -3, -2, -2, -1]
hurtle	-0.3	1.1	[-2, 1, 0, -1, -1, -1, 0, 2, 0, -1]
hurtled	-0.6	0.8	[0, -2, 0, 0, 0, 0, -1, -1, -2, 0]
hurtles	-1.0	0.63246	[-1, -2, 0, 0, -1, -1, -1, -1, -2, -1]
hurtless	0.3	1.55242	[-1, -3, 2, 1, 2, 1, 0, 0, -1, 2]
hurtling	-1.4	0.8	[-3, -2, -1, -1, -1, -1, -1, -2, 0, -2]
hurts	-2.1	0.83066	[-4, -2, -2, -2, -3, -1, -2, -2, -2, -1]
hypocritical	-2.0	0.89443	[-4, -2, -1, -3, -2, -1, -2, -1, -2, -2]
hysteria	-1.9	0.7	[-1, -3, -1, -2, -2, -2, -2, -2, -1, -3]
hysterical	-0.1	1.97231	[2, 0, 0, 3, -2, -2, 3, -1, -2, -2]
hysterics	-1.8	1.77764	[-3, -3, -3, -1, -3, -1, -2, 3, -3, -2]
ideal	2.4	1.2	[4, 3, 4, 4, 2, 2, 1, 2, 1, 1]
idealess	-1.9	1.3	[-4, -2, -3, -2, -1, 0, -4, -1, -1, -1]
idealise	1.4	0.91652	[2, 2, 1, 0, 1, 3, 2, 0, 1, 2]
idealised	2.1	0.83066	[1, 4, 1, 3, 2, 2, 2, 2, 2, 2]
idealises	2.0	0.89443	[1, 4, 1, 2, 2, 2, 2, 3, 1, 2]
idealising	0.6	0.4899	[0, 1, 1, 1, 1, 1, 1, 0, 0, 0]
idealism	1.7	1.1	[1, 2, 0, 3, 3, 2, 1, 2, 3, 0]
idealisms	0.8	0.9798	[0, 2, 3, 1, 0, 0, 1, 0, 0, 1]
idealist	1.6	1.56205	[2, 3, -2, 1, 3, 3, 1, 0, 2, 3]
idealistic	1.8	0.9798	[2, 1, 0, 2, 4, 2, 2, 2, 1, 2]
idealistically	1.7	1.1	[0, 3, 3, 1, 2, 3, 0, 2, 1, 2]
idealists	0.7	1.1	[0, -2, 1, 2, 1, 1, 2, 0, 1, 1]
idealities	1.5	0.67082	[2, 1, 1, 2, 1, 0, 2, 2, 2, 2]
ideality	1.9	0.9434	[0, 1, 2, 3, 2, 1, 2, 3, 3, 2]
idealization	1.8	0.9798	[2, 2, 2, 1, 4, 2, 2, 0, 1, 2]
idealizations	1.4	0.66332	[2, 1, 1, 2, 1, 2, 1, 0, 2, 2]
idealize	1.2	0.9798	[1, 2, 2, 0, 0, 1, 3, 0, 2, 1]
idealized	1.8	0.74833	[2, 3, 1, 1, 2, 1, 3, 1, 2, 2]
idealizer	1.3	0.9	[1, 1, 1, 1, 1, 3, 0, 1, 3, 1]
idealizers	1.9	1.37477	[1, 4, 1, 2, 0, 2, 3, 2, 4, 0]
idealizes	2.0	1.0	[3, 2, 2, 1, 4, 2, 2, 0, 2, 2]
idealizing	1.4	1.0198	[1, 1, 2, 3, 0, 1, 3, 0, 2, 1]
idealless	-1.7	1.00499	[-2, -2, -2, -2, -4, -1, 0, -1, -2, -1]
ideally	1.8	1.16619	[1, 0, 2, 2, 3, 2, 4, 0, 2, 2]
idealogues	0.5	0.92195	[1, 0, -1, 2, 0, 0, 2, 0, 1, 0]
idealogy	0.8	1.16619	[0, 3, 1, 0, 2, 0, 0, 1, 2, -1]
ideals	0.8	0.6	[0, 1, 0, 2, 1, 1, 1, 0, 1, 1]
idiot	-2.3	0.64031	[-2, -3, -1, -3, -2, -3, -3, -2, -2, -2]
idiotic	-2.6	0.91652	[-3, -4, -2, -3, -2, -2, -4, -1, -3, -2]
ignorable	-1.0	0.63246	[-1, -1, -1, -2, 0, -1, 0, -1, -2, -1]
ignorami	-1.9	0.83066	[-2, -2, -3, -2, 0, -1, -2, -2, -3, -2]
ignoramus	-1.9	0.83066	[-1, -2, -1, -3, -1, -3, -1, -3, -2, -2]
ignoramuses	-2.3	1.1	[-2, -2, -3, -4, 0, -1, -3, -2, -3, -3]
ignorance	-1.5	1.20416	[-2, -2, -3, -2, 0, -2, -1, -3, -1, 1]
ignorances	-1.2	0.9798	[0, -1, -2, -1, -2, -2, -1, 1, -2, -2]
ignorant	-1.1	0.83066	[-1, -1, -1, -2, 1, -2, -1, -2, -1, -1]
ignorantly	-1.6	0.91652	[-1, -2, -2, -2, -2, -2, -2, 1, -2, -2]
ignorantness	-1.1	1.44568	[3, -1, -2, -1, -1, -2, -2, -2, -1, -2]
ignore	-1.5	0.67082	[-1, -2, -1, -1, -1, -1, -2, -3, -2, -1]
ignored	-1.3	0.45826	[-2, -1, -2, -1, -1, -2, -1, -1, -1, -1]
ignorer	-1.3	0.45826	[-1, -1, -2, -1, -2, -1, -1, -1, -2, -1]
ignorers	-0.7	1.00499	[-1, -2, -1, -1, -1, 1, 0, -2, 1, -1]
ignores	-1.1	0.3	[-1, -1, -2, -1, -1, -1, -1, -1, -1, -1]
ignoring	-1.7	0.64031	[-1, -1, -1, -2, -2, -3, -2, -1, -2, -2]
ill	-1.8	0.9798	[-2, 0, -2, -1, -4, -2, -2, -2, -1, -2]
illegal	-2.6	0.8	[-3, -4, -3, -2, -1, -2, -3, -3, -2, -3]
illiteracy	-1.9	0.7	[-2, -1, -2, -2, -3, -2, -1, -2, -1, -3]
illness	-1.7	0.64031	[-2, -1, -2, -2, -3, -2, -1, -2, -1, -1]
illnesses	-2.2	0.74833	[-2, -2, -2, -4, -2, -3, -2, -1, -2, -2]
imbecile	-2.2	0.9798	[-3, -2, -3, -3, -3, -1, -3, 0, -2, -2]
immobilized	-1.2	0.87178	[-1, -3, 0, -1, -1, 0, -2, -1, -2, -1]
immoral	-2.0	1.09545	[-4, -1, -1, -3, -2, -3, -3, -1, -1, -1]
immoralism	-1.6	0.91652	[-2, -2, -2, -2, 1, -2, -2, -2, -1, -2]
immoralist	-2.1	0.3	[-2, -2, -3, -2, -2, -2, -2, -2, -2, -2]
immoralists	-1.7	0.78102	[-2, -2, -2, -3, -1, -1, -1, -1, -3, -1]
immoralities	-1.1	1.51327	[-2, -1, -4, -3, 1, 1, -1, 0, -1, -1]
immorality	-0.6	2.2891	[3, -3, 1, 2, 2, -2, -4, -1, -2, -2]
immorally	-2.1	0.7	[-1, -1, -2, -2, -3, -2, -3, -2, -2, -3]
immortal	1.0	1.73205	[3, 3, 2, 2, -2, -2, 2, 1, 1, 0]
immune	1.2	0.74833	[2, 2, 0, 1, 2, 1, 2, 1, 1, 0]
impatience	-1.8	0.4	[-2, -2, -1, -2, -2, -2, -2, -1, -2, -2]
impatiens	-0.2	0.6	[0, 0, -1, 0, 0, 0, 1, -1, -1, 0]
impatient	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -2]
impatiently	-1.7	0.64031	[-1, -1, -2, -3, -1, -2, -1, -2, -2, -2]
imperfect	-1.3	0.64031	[-1, -1, -1, -1, -1, -2, -3, -1, -1, -1]
impersonal	-1.3	0.45826	[-2, -2, -1, -1, -2, -1, -1, -1, -1, -1]
impolite	-1.6	0.66332	[-2, -1, -3, -1, -1, -1, -1, -2, -2, -2]
impolitely	-1.8	0.6	[-2, -2, -1, -1, -2, -1, -2, -2, -3, -2]
impoliteness	-1.8	0.87178	[-1, -3, -2, -3, -2, -3, -1, -1, -1, -1]
impolitenesses	-2.3	0.78102	[-3, -2, -2, -1, -1, -3, -3, -2, -3, -3]
importance	1.5	0.80623	[2, 1, 2, 3, 1, 2, 1, 0, 2, 1]
importancies	0.4	1.42829	[0, 0, 1, 2, 0, 0, 2, 2, 0, -3]
importancy	1.4	0.66332	[2, 1, 2, 2, 1, 2, 1, 0, 2, 1]
important	0.8	1.07703	[0, 0, 0, 2, 1, 0, 0, 3, 2, 0]
importantly	1.3	0.78102	[2, 1, 2, 1, 2, 2, 0, 0, 1, 2]
impose	-1.2	0.4	[-1, -1, -1, -1, -2, -1, -2, -1, -1, -1]
imposed	-0.3	1.41774	[2, -1, -1, -1, 1, 0, -2, 2, -2, -1]
imposes	-0.4	1.42829	[-2, -1, 1, -2, -1, -1, -2, 1, 2, 1]
imposing	-0.4	1.0198	[1, -1, -2, -1, 1, 1, 0, -1, -1, -1]
impotent	-1.1	0.3	[-1, -1, -1, -2, -1, -1, -1, -1, -1, -1]
impress	1.9	0.53852	[2, 2, 1, 2, 2, 2, 2, 1, 2, 3]
impressed	2.1	0.3	[2, 2, 2, 2, 2, 2, 3, 2, 2, 2]
impresses	2.1	0.3	[2, 2, 2, 2, 2, 2, 3, 2, 2, 2]
impressibility	1.2	1.249	[-1, 2, 0, 2, 0, 2, 2, 3, 2, 0]
impressible	0.8	1.16619	[2, -1, 1, 0, -1, 2, 1, 0, 2, 2]
impressing	2.5	0.92195	[3, 4, 1, 2, 3, 2, 3, 1, 3, 3]
impression	0.9	0.9434	[0, 1, 0, 0, 2, 2, 0, 2, 0, 2]
impressionable	0.2	1.07703	[0, 0, -1, 1, -1, -1, 0, 2, 0, 2]
impressionism	0.8	1.07703	[0, 2, 3, 0, 0, 2, 0, 0, 1, 0]
impressionisms	0.5	0.80623	[0, 0, 0, 0, 0, 2, 0, 1, 0, 2]
impressionist	1.0	1.09545	[0, 2, 3, 0, 0, 2, 2, 0, 1, 0]
impressionistic	1.5	1.20416	[2, 2, 0, 0, 1, 0, 2, 2, 4, 2]
impressionistically	1.6	0.8	[2, 0, 1, 1, 2, 2, 3, 2, 2, 1]
impressionists	0.5	1.43178	[2, 0, 0, 1, 1, 2, -2, 2, 1, -2]
impressions	0.9	1.13578	[2, 0, 0, 3, 0, 2, 0, 2, 0, 0]
impressive	2.3	0.78102	[1, 2, 3, 2, 3, 2, 3, 3, 3, 1]
impressively	2.0	0.89443	[3, 1, 1, 2, 2, 2, 2, 4, 2, 1]
impressiveness	1.7	0.64031	[2, 1, 2, 3, 1, 2, 2, 2, 1, 1]
impressment	-0.4	1.85472	[-2, 1, 3, -2, -1, -3, 0, 0, 2, -2]
impressments	0.5	1.20416	[2, 0, 0, 1, 2, 2, 0, -2, 0, 0]
impressure	0.6	1.0198	[-1, 0, 0, 0, 0, 2, 0, 2, 1, 2]
imprisoned	-2.0	1.0	[-4, -2, -3, -1, -3, -2, -1, -2, -1, -1]
improve	1.9	0.7	[2, 3, 3, 2, 1, 1, 2, 2, 1, 2]
improved	2.1	0.7	[3, 1, 2, 3, 2, 3, 1, 2, 2, 2]
improvement	2.0	0.63246	[2, 3, 3, 2, 1, 1, 2, 2, 2, 2]
improvements	1.3	0.64031	[0, 2, 2, 1, 2, 2, 1, 1, 1, 1]
improver	1.8	0.6	[2, 1, 2, 1, 2, 3, 1, 2, 2, 2]
improvers	1.3	0.78102	[1, 2, 2, 1, 2, 2, 0, 0, 1, 2]
improves	1.8	1.07703	[3, -1, 2, 2, 2, 3, 1, 2, 2, 2]
improving	1.8	0.4	[2, 2, 1, 1, 2, 2, 2, 2, 2, 2]
inability	-1.7	0.9	[-2, -2, -3, 0, -2, -3, -2, -1, -1, -1]
inaction	-1.0	0.63246	[-2, -1, 0, -1, -1, 0, -1, -2, -1, -1]
inadequacies	-1.7	0.64031	[-1, -3, -2, -1, -2, -1, -2, -2, -1, -2]
inadequacy	-1.7	0.78102	[-1, -3, -1, -1, -3, -2, -2, -1, -2, -1]
inadequate	-1.7	0.64031	[-2, -1, -1, -3, -1, -1, -2, -2, -2, -2]
inadequately	-1.0	1.26491	[-3, -1, -1, -2, 2, -1, -2, -1, 0, -1]
inadequateness	-1.7	0.45826	[-2, -2, -1, -1, -1, -2, -2, -2, -2, -2]
inadequatenesses	-1.6	0.91652	[-1, -1, -2, -1, -2, -4, -2, -1, -1, -1]
incapable	-1.6	0.4899	[-1, -2, -1, -1, -2, -1, -2, -2, -2, -2]
incapacitated	-1.9	0.9434	[-2, -2, -1, -1, -2, -4, -1, -1, -2, -3]
incensed	-2.0	1.0	[-2, -1, -4, 0, -2, -2, -2, -3, -2, -2]
incentive	1.5	1.0247	[1, 2, 1, 2, 1, 0, 4, 2, 1, 1]
incentives	1.3	1.34536	[2, 2, 1, 1, 3, 1, -2, 3, 1, 1]
incompetence	-2.3	0.45826	[-3, -2, -2, -3, -2, -3, -2, -2, -2, -2]
incompetent	-2.1	0.83066	[-1, -1, -2, -2, -3, -3, -2, -3, -1, -3]
inconsiderate	-1.9	0.7	[-2, -1, -1, -1, -2, -2, -2, -3, -3, -2]
inconvenience	-1.5	0.5	[-1, -2, -1, -1, -1, -2, -2, -1, -2, -2]
inconvenient	-1.4	0.4899	[-2, -2, -1, -2, -1, -2, -1, -1, -1, -1]
increase	1.3	0.64031	[1, 2, 2, 1, 2, 0, 1, 1, 2, 1]
increased	1.1	1.04403	[2, 0, 3, 2, 2, 1, 0, 1, 0, 0]
indecision	-0.8	0.6	[-1, 0, -1, 0, -1, -1, -2, 0, -1, -1]
indecisions	-1.1	0.53852	[-1, -1, -1, -1, -2, -1, -2, -1, 0, -1]
indecisive	-1.0	0.44721	[0, -1, -1, -1, -1, -2, -1, -1, -1, -1]
indecisively	-0.7	1.18743	[-2, -1, -1, -1, -1, -1, -2, 2, 1, -1]
indecisiveness	-1.3	0.64031	[-1, -3, -1, -2, -1, -1, -1, -1, -1, -1]
indecisivenesses	-0.9	0.53852	[-1, -1, 0, -1, -1, 0, -1, -2, -1, -1]
indestructible	0.6	1.85472	[3, 4, -1, -2, 1, 1, 2, 0, -1, -1]
indifference	-0.2	0.74833	[0, -1, 0, 0, 0, 1, -2, 0, 0, 0]
indifferent	-0.8	0.6	[-1, 0, 0, -1, -2, -1, -1, 0, -1, -1]
indignant	-1.8	0.74833	[-1, -3, -2, -2, -2, -1, -1, -1, -3, -2]
indignation	-2.4	0.8	[-2, -1, -2, -3, -3, -3, -1, -3, -3, -3]
indoctrinate	-1.4	0.91652	[-1, -3, -1, 0, -2, -1, -2, -2, 0, -2]
indoctrinated	-0.4	1.35647	[1, -2, -3, 1, -1, 1, 1, -1, -1, 0]
indoctrinates	-0.6	1.2	[1, -1, -3, 1, -1, 1, -1, -1, -1, -1]
indoctrinating	-0.7	1.41774	[-1, 0, -3, -1, -2, -1, 2, -2, 0, 1]
ineffective	-0.5	1.74642	[-2, -1, -1, -1, -2, 2, -2, 1, 3, -2]
ineffectively	-1.3	0.9	[-2, -2, -2, 1, -1, -1, -1, -2, -1, -2]
ineffectiveness	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -1, -1, -2, -1]
ineffectual	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -1, -2, -2, -1]
ineffectuality	-1.6	0.66332	[-2, -1, -1, -1, -2, -1, -3, -2, -2, -1]
ineffectually	-1.1	0.9434	[-1, -2, -2, -1, 0, -1, -2, -1, 1, -2]
ineffectualness	-1.3	0.45826	[-2, -1, -1, -1, -1, -2, -1, -1, -2, -1]
infatuated	0.2	1.72047	[3, 2, 0, 3, -1, -1, -2, 0, -1, -1]
infatuation	0.6	1.74356	[1, -1, -1, 2, -1, 1, 4, 2, 1, -2]
infected	-2.2	0.6	[-3, -2, -2, -2, -2, -2, -3, -1, -2, -3]
inferior	-1.7	0.78102	[-1, -2, -2, -1, -1, -3, -2, -1, -1, -3]
inferiorities	-1.9	0.7	[-2, -3, -1, -2, -2, -3, -1, -1, -2, -2]
inferiority	-1.1	1.7	[-2, -3, -2, -2, -2, -3, 2, 1, -1, 1]
inferiorly	-2.0	0.63246	[-3, -2, -2, -2, -2, -3, -1, -2, -1, -2]
inferiors	-0.5	1.43178	[-1, -1, -1, -2, -1, 0, 3, -1, 1, -2]
inflamed	-1.4	1.28062	[-2, -2, -1, -2, -2, -1, 2, -3, -2, -1]
influential	1.9	1.04403	[3, 1, 2, 3, 4, 1, 1, 2, 1, 1]
infringement	-2.1	0.83066	[-3, -1, -2, -1, -2, -1, -3, -3, -2, -3]
infuriate	-2.2	0.87178	[-2, -1, -3, -3, -2, -3, -3, -1, -1, -3]
infuriated	-3.0	0.7746	[-1, -4, -3, -3, -3, -3, -3, -3, -3, -4]
infuriates	-2.6	0.8	[-4, -2, -1, -3, -3, -3, -2, -3, -3, -2]
infuriating	-2.4	1.42829	[-1, -3, -3, -3, -3, -4, 0, 0, -4, -3]
inhibin	-0.2	0.4	[-1, 0, 0, 0, 0, 0, 0, 0, -1, 0]
inhibit	-1.6	0.4899	[-1, -2, -1, -2, -1, -2, -2, -2, -1, -2]
inhibited	-0.4	0.4899	[0, 0, -1, 0, -1, -1, 0, -1, 0, 0]
inhibiting	-0.4	1.42829	[1, -1, -1, -1, -2, -1, 2, -2, 2, -1]
inhibition	-1.5	0.67082	[-1, -1, -1, -1, -2, -1, -1, -2, -2, -3]
inhibitions	-0.8	0.74833	[-2, -1, -2, 0, 0, 0, 0, -1, -1, -1]
inhibitive	-1.4	0.4899	[-2, -2, -1, -1, -2, -1, -2, -1, -1, -1]
inhibitor	-0.3	1.00499	[-2, 1, 1, -1, 1, 0, -1, -1, 0, -1]
inhibitors	-1.0	0.7746	[-1, 0, -2, -1, -2, -2, 0, 0, -1, -1]
inhibitory	-1.0	0.7746	[0, -1, -2, -2, -1, -2, 0, 0, -1, -1]
inhibits	-0.9	0.53852	[-1, -1, -2, -1, -1, 0, -1, -1, -1, 0]
injured	-1.7	0.64031	[-2, -1, -1, -1, -2, -1, -2, -2, -3, -2]
injury	-1.8	0.6	[-2, -2, -1, -2, -1, -1, -2, -3, -2, -2]
injustice	-2.7	0.64031	[-3, -2, -3, -4, -3, -3, -2, -2, -3, -2]
innocence	1.6	0.91652	[3, 1, 1, 1, 2, 2, 0, 3, 2, 1]
innocency	1.9	0.83066	[3, 1, 2, 0, 2, 2, 2, 3, 2, 2]
innocent	1.4	1.2	[1, -1, 2, 2, 2, 0, 1, 3, 3, 1]
innocenter	0.9	1.37477	[1, 1, 1, 2, 2, 1, 1, -3, 1, 2]
innocently	1.4	0.8	[0, 2, 1, 1, 1, 1, 3, 2, 2, 1]
innocents	1.1	1.04403	[0, 0, 2, 2, 2, -1, 1, 1, 2, 2]
innovate	2.2	0.74833	[2, 4, 3, 2, 2, 1, 2, 2, 2, 2]
innovates	2.0	0.89443	[2, 2, 2, 0, 1, 3, 3, 2, 3, 2]
innovation	1.6	0.91652	[1, 0, 3, 2, 1, 2, 3, 2, 1, 1]
innovative	1.9	0.83066	[1, 1, 2, 1, 2, 4, 2, 2, 2, 2]
inquisition	-1.2	1.249	[-2, 1, -1, 0, 0, -3, -1, -3, -2, -1]
inquisitive	0.7	1.18743	[2, 1, 1, 2, -2, -1, 1, 1, 1, 1]
insane	-1.7	0.78102	[-2, 0, -2, -1, -2, -3, -2, -2, -1, -2]
insanity	-2.7	1.00499	[-2, -4, -1, -1, -3, -4, -3, -3, -3, -3]
insecure	-1.8	0.74833	[-1, -2, -2, -1, -3, -2, -3, -2, -1, -1]
insecurely	-1.4	0.66332	[-3, -1, -1, -1, -2, -1, -2, -1, -1, -1]
insecureness	-1.8	0.87178	[-1, -1, -1, -3, -3, -3, -2, -2, -1, -1]
insecurities	-1.8	0.6	[-3, -2, -2, -2, -1, -2, -1, -1, -2, -2]
insecurity	-1.8	0.74833	[-2, -2, -2, -1, -3, -3, -2, -1, -1, -1]
insensitive	-0.9	1.81384	[2, -3, -2, -1, 2, -3, -2, 1, -1, -2]
insensitivity	-1.8	0.6	[-2, -2, -1, -3, -2, -2, -1, -2, -1, -2]
insignificant	-1.4	0.8	[-3, -2, -1, -2, -1, -2, 0, -1, -1, -1]
insincere	-1.8	0.6	[-2, -1, -2, -1, -2, -2, -2, -2, -1, -3]
insincerely	-1.9	0.7	[-2, -1, -2, -2, -1, -3, -2, -3, -1, -2]
insincerity	-1.4	1.35647	[-1, -1, -3, -2, -1, 2, -2, -1, -2, -3]
insipid	-2.0	0.7746	[-1, -2, -2, -1, -3, -3, -3, -2, -2, -1]
inspiration	2.4	0.8	[3, 3, 3, 3, 1, 3, 1, 3, 2, 2]
inspirational	2.3	0.64031	[2, 3, 2, 3, 2, 3, 2, 3, 1, 2]
inspirationally	2.3	0.64031	[3, 2, 2, 3, 3, 1, 2, 2, 3, 2]
inspirations	2.1	0.53852	[2, 2, 2, 2, 1, 2, 2, 3, 3, 2]
inspirator	1.9	1.22066	[2, 2, 3, 0, 3, 4, 0, 2, 1, 2]
inspirators	1.2	0.74833	[3, 1, 1, 2, 1, 1, 1, 1, 0, 1]
inspiratory	1.5	0.67082	[2, 2, 3, 1, 1, 1, 1, 1, 2, 1]
inspire	2.7	0.78102	[2, 3, 3, 3, 3, 3, 3, 1, 4, 2]
inspired	2.2	0.87178	[3, 2, 1, 3, 1, 1, 3, 2, 3, 3]
inspirer	2.2	1.07703	[3, 2, 2, 4, 0, 2, 3, 1, 3, 2]
inspirers	2.0	0.63246	[2, 2, 3, 2, 3, 2, 1, 2, 1, 2]
inspires	1.9	1.04403	[2, 2, 2, 4, 0, 2, 3, 1, 1, 2]
inspiring	1.8	1.07703	[2, 2, 2, -1, 2, 3, 2, 2, 1, 3]
inspiringly	2.6	0.4899	[2, 3, 3, 2, 3, 3, 2, 3, 2, 3]
inspirit	1.9	0.7	[1, 3, 2, 1, 2, 1, 3, 2, 2, 2]
inspirited	1.3	1.18743	[2, 2, 0, 0, 3, 0, 2, 0, 3, 1]
inspiriting	1.8	0.4	[1, 2, 1, 2, 2, 2, 2, 2, 2, 2]
inspiritingly	2.1	1.44568	[3, 2, 2, 2, 4, 1, -1, 3, 4, 1]
inspirits	0.8	1.46969	[3, 0, 3, -2, 0, 1, 1, 0, 2, 0]
insult	-2.3	1.00499	[-2, -1, -2, -3, -4, -1, -3, -1, -3, -3]
insulted	-2.3	0.45826	[-2, -2, -2, -2, -3, -2, -2, -2, -3, -3]
insulter	-2.0	0.63246	[-2, -1, -2, -2, -1, -3, -3, -2, -2, -2]
insulters	-2.0	0.44721	[-2, -3, -2, -2, -2, -2, -2, -1, -2, -2]
insulting	-2.2	0.74833	[-3, -2, -3, -3, -2, -3, -1, -2, -2, -1]
insultingly	-2.3	0.78102	[-3, -3, -1, -2, -2, -3, -2, -1, -3, -3]
insults	-1.8	0.6	[-2, -3, -2, -2, -1, -1, -1, -2, -2, -2]
intact	0.8	0.6	[1, 1, 0, 0, 1, 1, 0, 1, 1, 2]
integrity	1.6	0.66332	[2, 1, 1, 1, 2, 1, 3, 1, 2, 2]
intellect	2.0	1.09545	[2, 1, 4, 2, 1, 3, 2, 3, 0, 2]
intellection	0.6	1.0198	[0, 0, 0, 1, 0, 0, 0, 0, 2, 3]
intellections	0.8	0.87178	[1, 0, 1, 0, 1, 1, 1, 0, 0, 3]
intellective	1.7	0.78102	[3, 2, 1, 2, 2, 1, 0, 2, 2, 2]
intellectively	0.8	0.9798	[0, 0, 1, 1, 0, 3, 0, 1, 2, 0]
intellects	1.8	0.87178	[1, 0, 2, 1, 3, 2, 2, 2, 3, 2]
intellectual	2.3	0.9	[3, 3, 1, 2, 3, 4, 2, 2, 1, 2]
intellectualism	2.2	1.07703	[4, 0, 3, 1, 3, 2, 2, 3, 2, 2]
intellectualist	2.0	1.0	[4, 0, 2, 1, 3, 2, 2, 2, 2, 2]
intellectualistic	1.3	1.73494	[1, 4, 0, 3, -2, 0, 2, 3, 0, 2]
intellectualists	0.8	0.74833	[0, 1, 1, 1, 0, 2, 0, 1, 2, 0]
intellectualities	1.7	1.34536	[3, 3, 0, 2, 1, 0, 0, 4, 2, 2]
intellectuality	1.7	1.1	[3, 2, 2, 1, 2, 1, 0, 3, 0, 3]
intellectualization	1.5	1.11803	[2, 1, 2, 1, 4, 2, 0, 0, 2, 1]
intellectualize	1.5	0.92195	[1, 2, 1, 1, 3, 2, 3, 0, 1, 1]
intellectualized	1.2	0.74833	[1, 0, 1, 1, 2, 0, 1, 2, 2, 2]
intellectualizes	1.8	0.87178	[2, 3, 2, 0, 2, 3, 2, 2, 1, 1]
intellectualizing	0.8	1.77764	[0, 1, 2, 1, 0, 2, 2, -4, 2, 2]
intellectually	1.4	0.8	[2, 0, 0, 1, 2, 2, 2, 2, 2, 1]
intellectualness	1.5	0.80623	[2, 2, 2, 2, 0, 0, 1, 2, 2, 2]
intellectuals	1.6	0.8	[0, 1, 2, 1, 3, 2, 1, 2, 2, 2]
intelligence	2.1	0.9434	[3, 2, 2, 1, 3, 3, 3, 2, 2, 0]
intelligencer	1.5	0.80623	[2, 0, 0, 2, 2, 2, 2, 1, 2, 2]
intelligencers	1.6	0.91652	[2, 2, 0, 2, 2, 0, 3, 2, 2, 1]
intelligences	1.6	0.91652	[3, 0, 0, 2, 2, 2, 2, 1, 2, 2]
intelligent	2.0	0.7746	[1, 2, 2, 1, 4, 2, 2, 2, 2, 2]
intelligential	1.9	0.9434	[3, 2, 2, 1, 3, 2, 2, 3, 0, 1]
intelligently	2.0	0.63246	[2, 3, 2, 3, 1, 2, 2, 1, 2, 2]
intelligentsia	1.5	1.20416	[0, 1, 2, 0, 4, 3, 2, 1, 1, 1]
intelligibility	1.5	0.80623	[2, 2, 0, 2, 2, 1, 2, 0, 2, 2]
intelligible	1.4	0.8	[1, 2, 2, 1, 0, 2, 1, 1, 3, 1]
intelligibleness	1.5	1.20416	[2, 1, 3, -1, 2, 2, 2, 0, 1, 3]
intelligibly	1.2	0.87178	[1, 2, 1, 2, 1, -1, 2, 2, 1, 1]
intense	0.3	0.45826	[0, 1, 1, 0, 0, 0, 0, 0, 1, 0]
interest	2.0	1.18322	[2, 3, 3, 1, 1, 3, 4, 1, 2, 0]
interested	1.7	0.45826	[2, 1, 2, 2, 1, 2, 2, 2, 1, 2]
interestedly	1.5	0.67082	[2, 2, 2, 1, 2, 1, 2, 1, 0, 2]
interesting	1.7	0.78102	[1, 2, 1, 3, 1, 1, 1, 3, 2, 2]
interestingly	1.7	0.45826	[2, 2, 2, 1, 2, 1, 1, 2, 2, 2]
interestingness	1.8	0.87178	[2, 3, 1, 3, 1, 3, 2, 1, 1, 1]
interests	1.0	0.89443	[1, 0, 1, 2, 0, 0, 1, 1, 1, 3]
interrogated	-1.6	1.0198	[-3, -1, -1, -1, -2, -1, -3, -1, -3, 0]
interrupt	-1.4	0.4899	[-2, -1, -1, -1, -2, -2, -1, -1, -2, -1]
interrupted	-1.2	0.6	[-1, -2, -1, 0, -1, -1, -2, -1, -2, -1]
interrupter	-1.1	0.53852	[-1, -2, -1, 0, -1, -1, -1, -1, -2, -1]
interrupters	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -2, -1, -2, -1]
interruptible	-1.3	1.00499	[-2, -1, 0, -2, 0, -3, -2, -1, 0, -2]
interrupting	-1.2	0.4	[-2, -1, -1, -1, -1, -2, -1, -1, -1, -1]
interruption	-1.5	0.67082	[-1, -1, -2, -3, -2, -1, -1, -1, -2, -1]
interruptions	-1.7	0.45826	[-2, -2, -2, -2, -2, -1, -1, -1, -2, -2]
interruptive	-1.4	0.66332	[-2, -2, -1, -2, -2, 0, -1, -1, -1, -2]
interruptor	-1.3	0.64031	[-2, -1, -1, -1, 0, -2, -2, -2, -1, -1]
interrupts	-1.3	0.64031	[-1, -1, -2, -1, -1, -1, -1, -1, -3, -1]
intimidate	-0.8	1.46969	[-1, -2, -2, -2, -2, -1, 2, 1, 1, -2]
intimidated	-1.9	0.7	[-2, -1, -3, -3, -1, -2, -2, -1, -2, -2]
intimidates	-1.3	0.78102	[-2, -1, -1, -1, -3, -2, -1, -1, -1, 0]
intimidating	-1.9	1.04403	[0, -2, -1, -3, -2, -1, -1, -3, -3, -3]
intimidatingly	-1.1	1.64012	[2, -2, -3, -1, -2, -2, 2, -2, -1, -2]
intimidation	-1.8	1.249	[1, -2, -3, -1, -2, -3, -1, -3, -1, -3]
intimidations	-1.4	1.49666	[1, -2, -2, -2, -1, -1, -1, -4, 1, -3]
intimidator	-1.6	0.4899	[-1, -1, -2, -2, -1, -2, -2, -1, -2, -2]
intimidators	-1.6	0.8	[-1, -1, -3, -2, -1, -1, -3, -1, -2, -1]
intimidatory	-1.1	1.22066	[-1, -2, -3, -1, -1, -1, -2, 2, -1, -1]
intricate	0.6	0.66332	[1, 0, 2, 1, 0, 1, 1, 0, 0, 0]
intrigues	0.9	0.9434	[2, -1, 2, 1, 2, 0, 1, 1, 0, 1]
invigorate	1.9	0.83066	[2, 2, 2, 0, 2, 3, 3, 2, 1, 2]
invigorated	0.8	1.8868	[-2, 3, 2, 2, -2, -2, 2, 2, 1, 2]
invigorates	2.1	0.53852	[3, 2, 3, 2, 1, 2, 2, 2, 2, 2]
invigorating	2.1	0.7	[2, 1, 1, 3, 3, 3, 2, 2, 2, 2]
invigoratingly	2.0	0.63246	[2, 2, 1, 2, 1, 3, 2, 2, 2, 3]
invigoration	1.5	1.36015	[2, 2, 1, -2, 1, 3, 3, 2, 1, 2]
invigorations	1.2	0.87178	[1, -1, 2, 2, 1, 1, 2, 1, 2, 1]
invigorator	1.1	1.3	[3, 1, 0, 2, 2, -2, 1, 1, 2, 1]
invigorators	1.2	0.87178	[1, 1, 1, 1, 3, 2, 0, 0, 1, 2]
invincible	2.2	1.77764	[4, 1, 3, 2, 4, 1, 4, -1, 0, 4]
invite	0.6	0.66332	[2, 1, 1, 0, 0, 0, 0, 1, 1, 0]
inviting	1.3	0.45826	[1, 1, 1, 2, 1, 2, 2, 1, 1, 1]
invulnerable	1.3	1.73494	[2, 3, 4, 2, 0, 3, 0, 1, -2, 0]
irate	-2.9	0.53852	[-3, -3, -3, -2, -3, -4, -3, -3, -2, -3]
ironic	-0.5	1.28452	[1, 0, 0, 0, 0, 0, -4, -1, -1, 0]
irony	-0.2	1.07703	[-1, 0, -3, 0, 0, 0, 1, 0, 1, 0]
irrational	-1.4	0.4899	[-1, -1, -1, -2, -2, -2, -1, -1, -2, -1]
irrationalism	-1.5	0.5	[-1, -2, -1, -1, -2, -1, -2, -2, -1, -2]
irrationalist	-2.1	0.9434	[-1, -4, -2, -2, -3, -3, -2, -1, -1, -2]
irrationalists	-1.5	0.92195	[-2, -2, -1, -2, -2, 1, -1, -2, -2, -2]
irrationalities	-1.5	0.80623	[-2, -2, 0, -1, -1, -1, -2, -3, -1, -2]
irrationality	-1.7	0.9	[-3, -3, -1, -2, -1, -1, -1, -1, -3, -1]
irrationally	-1.6	0.4899	[-1, -2, -1, -2, -1, -2, -2, -2, -1, -2]
irrationals	-1.1	0.83066	[-2, 0, -1, 0, -1, -1, -3, -1, -1, -1]
irresistible	1.4	2.2	[2, 3, 2, 3, 4, 4, 1, -1, -2, -2]
irresolute	-1.4	0.66332	[-2, -2, -1, -2, -1, -1, -1, -2, -2, 0]
irresponsible	-1.9	0.3	[-2, -2, -2, -2, -2, -2, -1, -2, -2, -2]
irreversible	-0.8	0.87178	[-2, -2, 0, -1, 0, 0, 0, -1, -2, 0]
irritabilities	-1.7	0.64031	[-2, -2, -2, -1, -1, -1, -1, -2, -3, -2]
irritability	-1.4	1.28062	[-2, -1, -2, -1, 2, -2, -2, -2, -1, -3]
irritable	-2.1	0.7	[-2, -2, -3, -1, -2, -1, -3, -2, -3, -2]
irritableness	-1.7	0.64031	[-2, -2, -2, -1, -2, -1, -1, -3, -1, -2]
irritably	-1.8	0.6	[-2, -2, -1, -1, -3, -2, -1, -2, -2, -2]
irritant	-2.3	0.78102	[-3, -3, -3, -3, -3, -1, -2, -2, -2, -1]
irritants	-2.1	0.83066	[-2, -3, -1, -4, -2, -1, -2, -2, -2, -2]
irritate	-1.8	0.6	[-3, -2, -2, -2, -1, -2, -1, -2, -1, -2]
irritated	-2.0	0.63246	[-1, -2, -2, -2, -3, -2, -2, -3, -1, -2]
irritates	-1.7	0.78102	[-1, -2, -1, -1, -3, -2, -2, -3, -1, -1]
irritating	-2.0	0.63246	[-2, -2, -2, -1, -1, -3, -2, -2, -3, -2]
irritatingly	-2.0	0.44721	[-2, -2, -2, -3, -1, -2, -2, -2, -2, -2]
irritation	-2.3	0.78102	[-3, -2, -2, -2, -1, -3, -3, -1, -3, -3]
irritations	-1.5	0.67082	[-2, -2, -1, -1, -1, -1, -1, -2, -3, -1]
irritative	-2.0	0.63246	[-3, -2, -3, -2, -1, -2, -2, -2, -1, -2]
isolatable	0.2	1.249	[1, 0, -2, 0, -1, -1, 2, 0, 2, 1]
isolate	-0.8	0.74833	[-1, -1, -1, 0, 0, 0, 0, -2, -1, -2]
isolated	-1.3	0.64031	[-1, -1, -1, -1, -1, -1, -2, -1, -3, -1]
isolates	-1.3	0.64031	[-1, -1, -1, -1, -2, -1, -3, -1, -1, -1]
isolation	-1.7	0.78102	[-1, -3, -1, -2, -2, -1, -3, -1, -2, -1]
isolationism	0.4	1.62481	[2, 0, -1, -2, -1, 3, 0, 2, -1, 2]
isolationist	0.7	1.55242	[2, 0, 0, -1, -1, 3, 0, 2, -1, 3]
isolations	-0.5	1.11803	[-1, -2, -2, -1, 1, 1, -1, 0, 1, -1]
isolator	-0.4	0.66332	[0, 0, -1, 0, -1, 0, -2, 0, 0, 0]
isolators	-0.4	1.42829	[-2, -1, -1, -2, 2, 2, -1, 1, -1, -1]
itchy	-1.1	0.53852	[-1, -1, -1, -1, -2, 0, -1, -1, -2, -1]
jackass	-1.8	1.07703	[-1, 0, -3, -2, -2, -3, 0, -2, -2, -3]
jackasses	-2.8	0.9798	[-2, -2, -4, -3, -4, -4, -3, -1, -2, -3]
jaded	-1.6	0.66332	[-1, -1, -1, -2, -2, -1, -2, -2, -1, -3]
jailed	-2.2	0.87178	[-4, -3, -2, -2, -1, -2, -2, -3, -1, -2]
jaunty	1.2	0.6	[2, 1, 0, 2, 1, 1, 1, 2, 1, 1]
jealous	-2.0	0.63246	[-2, -2, -3, -2, -3, -2, -1, -1, -2, -2]
jealousies	-2.0	0.63246	[-2, -3, -2, -1, -2, -3, -2, -1, -2, -2]
jealously	-2.0	0.89443	[-1, -3, -1, -4, -2, -2, -1, -2, -2, -2]
jealousness	-1.7	0.45826	[-1, -2, -2, -2, -2, -1, -1, -2, -2, -2]
jealousy	-1.3	1.73494	[-2, -3, -2, -2, -2, 2, -3, -1, 2, -2]
jeopardy	-2.1	0.9434	[-3, -3, -3, -1, -1, -3, -2, -1, -1, -3]
jerk	-1.4	0.8	[-1, -1, -1, -2, -3, 0, -2, -1, -1, -2]
jerked	-0.8	0.74833	[0, -1, -1, 0, -2, -1, 0, -1, 0, -2]
jerks	-1.1	1.51327	[-2, -2, -1, -2, -1, 0, -2, 3, -2, -2]
jewel	1.5	1.20416	[1, 3, 2, 0, 2, 1, 3, 0, 3, 0]
jewels	2.0	1.34164	[3, 1, 0, 0, 4, 3, 3, 2, 3, 1]
jocular	1.2	1.249	[0, 1, 2, -2, 1, 2, 2, 2, 2, 2]
join	1.2	0.74833	[2, 2, 1, 2, 1, 0, 1, 2, 0, 1]
joke	1.2	0.74833	[1, 1, 1, 1, 1, 1, 0, 2, 1, 3]
joked	1.3	0.64031	[1, 1, 2, 2, 0, 2, 1, 1, 2, 1]
joker	0.5	0.92195	[1, 1, -1, 1, 2, -1, 1, 1, 0, 0]
jokes	1.0	0.7746	[1, 1, -1, 1, 1, 2, 1, 1, 2, 1]
jokester	1.5	0.67082	[2, 2, 2, 2, 1, 1, 0, 1, 2, 2]
jokesters	0.9	0.83066	[0, 0, 2, 1, 1, 2, 2, 0, 1, 0]
jokey	1.1	0.3	[1, 1, 2, 1, 1, 1, 1, 1, 1, 1]
joking	0.9	0.53852	[1, 2, 1, 1, 1, 0, 1, 1, 0, 1]
jollied	2.4	0.66332	[3, 3, 2, 2, 3, 3, 2, 2, 1, 3]
jollier	2.4	0.4899	[2, 2, 3, 3, 2, 2, 2, 3, 2, 3]
jollies	2.0	0.63246	[1, 2, 3, 2, 2, 2, 1, 2, 2, 3]
jolliest	2.9	0.7	[3, 3, 2, 4, 3, 2, 3, 4, 2, 3]
jollification	2.2	0.74833	[2, 3, 3, 1, 2, 1, 3, 2, 3, 2]
jollifications	2.0	0.7746	[2, 3, 2, 2, 2, 2, 3, 2, 0, 2]
jollify	2.1	0.53852	[2, 3, 2, 2, 2, 2, 2, 3, 1, 2]
jollily	2.7	0.64031	[3, 3, 3, 3, 3, 3, 3, 1, 3, 2]
jolliness	2.5	0.67082	[3, 1, 2, 3, 2, 3, 3, 3, 2, 3]
jollities	1.7	0.64031	[2, 1, 2, 2, 1, 2, 2, 3, 1, 1]
jollity	1.8	1.6	[3, 2, 1, 1, 4, 3, 1, 2, 3, -2]
jolly	2.3	1.00499	[4, 3, 3, 1, 1, 1, 3, 2, 3, 2]
jollying	2.3	0.64031	[2, 3, 3, 1, 3, 3, 2, 2, 2, 2]
jovial	1.9	0.53852	[2, 2, 1, 2, 1, 2, 3, 2, 2, 2]
joy	2.8	0.74833	[3, 2, 3, 4, 3, 3, 3, 1, 3, 3]
joyance	2.3	0.9	[1, 3, 4, 2, 2, 1, 2, 3, 2, 3]
joyed	2.9	0.3	[3, 3, 3, 3, 3, 3, 3, 2, 3, 3]
joyful	2.9	0.53852	[3, 2, 3, 3, 2, 3, 4, 3, 3, 3]
joyfuller	2.4	0.66332	[2, 4, 3, 2, 2, 2, 3, 2, 2, 2]
joyfully	2.5	0.67082	[2, 2, 3, 3, 2, 2, 3, 4, 2, 2]
joyfulness	2.7	1.00499	[4, 3, 1, 3, 3, 3, 4, 2, 1, 3]
joying	2.5	0.67082	[2, 2, 1, 3, 3, 3, 3, 3, 3, 2]
joyless	-2.5	0.67082	[-1, -2, -3, -3, -3, -3, -2, -2, -3, -3]
joylessly	-1.7	1.1	[-2, -2, -3, -3, -2, -1, 1, -2, -1, -2]
joylessness	-2.7	0.9	[-4, -3, -3, -3, -3, -3, -3, -1, -1, -3]
joyous	3.1	0.7	[3, 4, 3, 2, 4, 3, 3, 4, 2, 3]
joyously	2.9	0.7	[2, 3, 4, 3, 4, 2, 2, 3, 3, 3]
joyousness	2.8	0.74833	[3, 3, 1, 3, 3, 3, 4, 2, 3, 3]
joypop	-0.2	1.93907	[-3, 1, 2, 2, -3, -1, -2, -1, 1, 2]
joypoppers	-0.1	1.22066	[2, -1, 1, -1, -1, 1, 1, 0, -2, -1]
joyridden	0.6	1.8	[-2, -1, 4, 0, 0, 2, 1, -1, 0, 3]
joyride	1.1	1.22066	[-1, 1, 2, 0, 2, 0, 2, 2, 3, 0]
joyrider	0.7	1.26886	[2, -2, 1, 2, 0, 2, -1, 1, 1, 1]
joyriders	1.3	1.18743	[1, 0, 0, 1, 4, 3, 1, 1, 1, 1]
joyrides	0.8	1.32665	[2, -2, 1, 2, 0, 2, -1, 1, 2, 1]
joyriding	0.9	1.04403	[1, -1, 1, 1, 0, 2, 0, 1, 3, 1]
joyrode	1.0	1.48324	[4, 0, -2, 0, 2, 1, 2, 1, 1, 1]
joys	2.2	0.4	[2, 2, 2, 2, 2, 3, 2, 3, 2, 2]
joystick	0.7	0.78102	[1, 0, 2, 2, 0, 0, 1, 1, 0, 0]
joysticks	0.2	0.4	[0, 0, 0, 0, 1, 0, 0, 1, 0, 0]
jubilant	3.0	0.63246	[3, 3, 4, 3, 3, 2, 3, 4, 2, 3]
jumpy	-1.0	0.63246	[0, 0, -2, -1, -1, -1, -1, -1, -2, -1]
justice	2.4	1.0198	[3, 2, 1, 2, 3, 2, 4, 4, 2, 1]
justifiably	1.0	0.7746	[0, 1, 0, 1, 1, 1, 0, 2, 2, 2]
justified	1.7	0.64031	[1, 2, 2, 3, 1, 1, 1, 2, 2, 2]
keen	1.5	0.67082	[1, 1, 3, 1, 2, 1, 1, 2, 2, 1]
keened	0.3	1.00499	[-2, 0, 1, 0, 1, 1, 0, 2, 0, 0]
keener	0.5	1.20416	[-1, -1, 0, -1, 2, 1, 2, 2, 1, 0]
keeners	0.6	0.4899	[1, 0, 0, 1, 1, 0, 1, 1, 0, 1]
keenest	1.9	0.83066	[3, 3, 1, 1, 3, 2, 2, 1, 2, 1]
keening	-0.7	1.41774	[0, -3, -1, -1, -3, 1, -1, 1, 1, -1]
keenly	1.0	0.7746	[2, 1, 1, 0, 1, 0, 2, 1, 2, 0]
keenness	1.4	0.4899	[1, 1, 2, 2, 1, 1, 1, 2, 2, 1]
keens	0.1	1.22066	[1, -3, 0, 0, 0, 2, 1, 0, 0, 0]
kewl	1.3	0.45826	[2, 1, 1, 1, 2, 1, 2, 1, 1, 1]
kidding	0.4	0.8	[0, 1, 0, -1, 1, 1, 1, 1, -1, 1]
kill	-3.7	0.45826	[-4, -4, -4, -4, -3, -4, -4, -4, -3, -3]
killdeer	-1.1	1.04403	[-3, 0, 0, -1, -2, 0, -1, -2, -2, 0]
killdeers	-0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, 0, -1]
killdees	-0.6	0.66332	[-2, 0, 0, 0, -1, 0, -1, -1, -1, 0]
killed	-3.5	0.67082	[-3, -3, -2, -4, -4, -4, -3, -4, -4, -4]
killer	-3.3	0.64031	[-4, -4, -3, -3, -4, -4, -3, -3, -2, -3]
killers	-3.3	0.45826	[-3, -3, -4, -3, -3, -3, -4, -3, -4, -3]
killick	0.1	0.3	[0, 1, 0, 0, 0, 0, 0, 0, 0, 0]
killie	-0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, 0, -1]
killifish	-0.1	0.7	[0, 0, 0, 0, -2, 0, 0, 0, 1, 0]
killifishes	-0.1	0.3	[0, -1, 0, 0, 0, 0, 0, 0, 0, 0]
killing	-3.4	1.2	[-4, 0, -4, -4, -4, -3, -4, -3, -4, -4]
killingly	-2.6	1.0198	[-3, -2, -4, -2, -1, -3, -4, -3, -3, -1]
killings	-3.5	0.67082	[-4, -3, -4, -4, -2, -3, -4, -4, -3, -4]
killjoy	-2.1	0.83066	[-1, -2, -3, -2, -1, -3, -2, -1, -3, -3]
killjoys	-1.7	0.9	[-1, -3, -3, -1, -2, -1, -2, 0, -2, -2]
killock	-0.3	0.64031	[0, 0, 0, 0, -2, 0, 0, 0, -1, 0]
killocks	-0.4	0.66332	[0, 0, 0, -2, -1, 0, -1, 0, 0, 0]
kills	-2.5	0.92195	[-2, -3, -2, -3, -4, -1, -3, -3, -1, -3]
kind	2.4	0.66332	[2, 2, 3, 3, 2, 3, 3, 2, 1, 3]
kinder	2.2	0.6	[3, 3, 3, 2, 2, 2, 2, 1, 2, 2]
kindly	2.2	0.4	[2, 2, 2, 3, 2, 2, 2, 2, 2, 3]
kindness	2.0	0.63246	[2, 1, 3, 3, 2, 1, 2, 2, 2, 2]
kindnesses	2.3	0.64031	[3, 1, 3, 2, 3, 2, 3, 2, 2, 2]
kiss	1.8	1.6	[4, 0, 3, 3, 2, 0, 4, 2, 0, 0]
kissable	2.0	0.89443	[2, 2, 2, 2, 4, 2, 3, 1, 1, 1]
kissably	1.9	1.04403	[1, 3, 4, 1, 2, 1, 3, 2, 1, 1]
kissed	1.6	1.11355	[2, 4, 1, 1, 1, 2, 3, 0, 1, 1]
kisser	1.7	1.34536	[2, 4, 1, 2, -1, 2, 3, 0, 2, 2]
kissers	1.5	0.80623	[1, 1, 1, 0, 2, 2, 3, 2, 1, 2]
kisses	2.3	0.9	[2, 4, 2, 2, 1, 2, 4, 2, 2, 2]
kissing	2.7	0.78102	[3, 3, 4, 1, 3, 2, 2, 3, 3, 3]
kissy	1.8	0.6	[2, 2, 2, 3, 1, 1, 2, 2, 1, 2]
kudos	2.3	0.64031	[2, 4, 2, 3, 2, 2, 2, 2, 2, 2]
lack	-1.3	0.45826	[-1, -1, -1, -1, -2, -1, -2, -1, -1, -2]
lackadaisical	-1.6	0.66332	[-1, -1, -2, -1, -2, -2, -3, -1, -2, -1]
lag	-1.4	0.66332	[-1, -1, -1, -1, -1, -3, -1, -2, -2, -1]
lagged	-1.2	0.6	[-2, -2, -1, -2, -1, -1, 0, -1, -1, -1]
lagging	-1.1	0.83066	[-2, -2, -2, -1, 0, 0, -1, 0, -1, -2]
lags	-1.5	0.67082	[-2, -1, -1, -2, -1, -3, -1, -2, -1, -1]
laidback	0.5	1.28452	[1, -1, 1, -2, -1, 1, 2, 1, 1, 2]
lame	-1.8	0.74833	[-1, -3, -1, -2, -2, -1, -2, -3, -1, -2]
lamebrain	-1.6	0.91652	[-3, -2, -1, 0, -3, -1, -1, -2, -2, -1]
lamebrained	-2.5	0.67082	[-3, -3, -2, -2, -2, -2, -4, -3, -2, -2]
lamebrains	-1.2	1.46969	[-2, -1, -3, -1, 2, -2, 1, -2, -2, -2]
lamedh	0.1	0.53852	[0, 0, 0, 0, 0, 1, 1, -1, 0, 0]
lamella	-0.1	0.3	[0, -1, 0, 0, 0, 0, 0, 0, 0, 0]
lamellae	-0.1	0.3	[0, 0, 0, 0, 0, 0, -1, 0, 0, 0]
lamellas	0.1	0.53852	[0, 0, 0, 0, 0, 1, 0, 1, 0, -1]
lamellibranch	0.2	0.4	[0, 0, 0, 0, 0, 1, 0, 1, 0, 0]
lamellibranchs	-0.1	0.3	[0, 0, -1, 0, 0, 0, 0, 0, 0, 0]
lamely	-2.0	0.89443	[-2, -3, 0, -1, -2, -3, -2, -3, -2, -2]
lameness	-0.8	1.07703	[-2, -2, -1, -1, -1, 0, 2, -1, -1, -1]
lament	-2.0	1.26491	[-3, -3, 1, -1, -2, -2, -1, -3, -3, -3]
lamentable	-1.5	1.0247	[-2, 0, -2, -1, 0, -2, -3, -1, -1, -3]
lamentableness	-1.3	0.64031	[-2, -1, -1, 0, -2, -1, -1, -1, -2, -2]
lamentably	-1.5	0.80623	[-2, 0, -3, -1, -1, -2, -2, -1, -1, -2]
lamentation	-1.4	1.49666	[-3, -2, 0, -3, -1, -1, 2, -2, -1, -3]
lamentations	-1.9	1.44568	[-2, -2, -2, -3, 2, -1, -3, -3, -2, -3]
lamented	-1.4	0.91652	[-1, 0, -1, -2, -2, -1, -2, -3, -2, 0]
lamenter	-1.2	0.87178	[-1, 0, -1, -1, -1, -1, -2, -3, -2, 0]
lamenters	-0.5	0.67082	[-1, 0, 0, -1, -1, 0, 0, 0, -2, 0]
lamenting	-2.0	1.09545	[0, -2, -1, -4, -1, -3, -2, -2, -3, -2]
laments	-1.5	0.80623	[-1, -3, 0, -1, -2, -1, -2, -2, -2, -1]
lamer	-1.4	0.4899	[-2, -1, -1, -2, -1, -1, -1, -2, -2, -1]
lames	-1.2	0.6	[-2, -1, -1, -2, 0, -1, -1, -2, -1, -1]
lamest	-1.5	1.28452	[-3, -2, -1, -4, 1, -1, -1, -1, -1, -2]
landmark	0.3	0.64031	[2, 0, 0, 0, 0, 0, 0, 0, 1, 0]
laugh	2.6	0.66332	[3, 2, 3, 1, 3, 3, 3, 3, 2, 3]
laughable	0.2	1.72047	[-2, -1, 3, -1, 2, 2, -1, -1, -1, 2]
laughableness	1.2	1.6	[2, 0, 3, 2, 2, 1, 3, -2, 2, -1]
laughably	1.2	1.249	[2, 1, 2, 1, 1, 1, 2, 3, -2, 1]
laughed	2.0	0.63246	[2, 2, 3, 1, 2, 1, 2, 2, 3, 2]
laugher	1.7	0.45826	[2, 2, 1, 1, 2, 1, 2, 2, 2, 2]
laughers	1.7	0.9	[1, 2, 4, 1, 2, 1, 2, 1, 1, 2]
laughing	2.2	0.87178	[2, 4, 1, 1, 3, 2, 2, 2, 2, 3]
laughingly	2.3	1.1	[2, 4, 0, 2, 3, 1, 3, 2, 3, 3]
laughings	1.9	0.7	[2, 1, 1, 1, 2, 3, 2, 2, 3, 2]
laughingstocks	-1.3	1.26886	[-3, -2, 1, -2, -1, -1, 1, -2, -2, -2]
laughs	2.2	0.6	[1, 2, 2, 3, 3, 2, 3, 2, 2, 2]
laughter	2.2	0.6	[2, 3, 2, 2, 2, 2, 3, 1, 3, 2]
laughters	2.2	0.6	[3, 1, 2, 2, 2, 3, 3, 2, 2, 2]
launched	0.5	0.80623	[2, 0, 0, 0, 0, 0, 0, 1, 2, 0]
lawl	1.4	1.42829	[0, 2, 2, 1, 3, -2, 3, 1, 2, 2]
lawsuit	-0.9	1.22066	[-2, -2, -1, -3, -1, 1, 0, 1, -1, -1]
lawsuits	-0.6	1.68523	[-2, -1, 0, 3, 2, -1, -1, -2, -2, -2]
lazier	-2.3	0.64031	[-3, -2, -3, -2, -1, -2, -2, -3, -2, -3]
laziest	-2.7	0.64031	[-2, -2, -3, -4, -3, -3, -2, -2, -3, -3]
lazy	-1.5	1.36015	[-3, -1, -3, -2, 2, -1, -1, -2, -2, -2]
leak	-1.4	0.66332	[-1, -2, -1, -1, -1, -1, -1, -2, -3, -1]
leaked	-1.3	0.78102	[-2, -2, -1, -3, -1, 0, -1, -1, -1, -1]
leave	-0.2	0.9798	[1, -1, -1, 0, 0, -1, 2, -1, 0, -1]
leet	1.3	1.48661	[2, 4, 2, 2, 0, 1, -2, 2, 1, 1]
legal	0.5	0.80623	[0, 0, 0, 2, 0, 1, 0, 0, 2, 0]
legally	0.4	0.8	[1, 0, 0, 0, 1, 0, 2, 1, 0, -1]
lenient	1.1	1.04403	[1, 3, 1, 2, 1, 1, 2, -1, 0, 1]
lethargic	-1.2	0.74833	[-2, -1, -2, -2, 0, -1, -1, -2, 0, -1]
lethargy	-1.4	0.91652	[-1, 0, -2, -1, -2, 0, -2, -1, -3, -2]
liabilities	-0.8	0.9798	[-1, 2, -1, -1, -1, -1, -1, -1, -2, -1]
liability	-0.8	1.83303	[-2, -3, -1, -1, 3, -3, -1, -1, 2, -1]
liar	-2.3	0.78102	[-1, -2, -3, -3, -2, -2, -3, -1, -3, -3]
liards	-0.4	0.91652	[-2, 0, -1, 0, 0, 1, 0, 0, -2, 0]
liars	-2.4	0.66332	[-3, -2, -2, -3, -2, -1, -3, -3, -2, -3]
libelous	-2.1	1.3	[-3, -1, -1, -2, 1, -3, -3, -3, -3, -3]
libertarian	0.9	0.9434	[1, 2, 0, 1, 0, 0, 0, 3, 1, 1]
libertarianism	0.4	0.8	[0, 0, 2, 0, 2, 0, 0, 0, 0, 0]
libertarianisms	0.1	1.13578	[0, 0, 0, 2, 2, 0, -2, 0, -1, 0]
libertarians	0.1	0.83066	[0, 0, 1, 0, 0, -1, -1, 0, 2, 0]
liberties	2.3	0.78102	[2, 4, 3, 2, 2, 1, 2, 3, 2, 2]
libertinage	0.2	1.53623	[-1, -1, 0, 0, 4, 0, -2, 1, 1, 0]
libertine	-0.9	1.44568	[0, -1, 0, 0, -1, -3, 2, -2, -1, -3]
libertines	0.4	1.35647	[-1, 3, -1, -1, 1, 1, 1, 2, 0, -1]
libertinisms	1.2	1.249	[0, 0, 3, 1, 1, 1, 4, 0, 1, 1]
liberty	2.4	0.91652	[2, 2, 3, 3, 3, 3, 2, 4, 1, 1]
lied	-1.6	1.2	[-3, 0, -3, 1, -2, -1, -2, -2, -2, -2]
lies	-1.8	0.9798	[-1, -1, -1, 0, -2, -3, -3, -2, -3, -2]
lifesaver	2.8	0.74833	[3, 3, 4, 2, 3, 1, 3, 3, 3, 3]
lighthearted	1.8	0.4	[2, 2, 2, 2, 1, 2, 2, 1, 2, 2]
like	1.5	0.67082	[1, 2, 2, 2, 1, 3, 1, 1, 1, 1]
likeable	2.0	0.63246	[1, 3, 2, 2, 2, 3, 2, 1, 2, 2]
liked	1.8	0.6	[2, 2, 1, 2, 2, 1, 3, 1, 2, 2]
likes	1.8	0.6	[2, 2, 1, 2, 2, 2, 3, 1, 1, 2]
liking	1.7	0.78102	[3, 1, 2, 1, 1, 2, 3, 1, 2, 1]
limitation	-1.2	0.6	[-2, -1, 0, -1, -1, -2, -1, -2, -1, -1]
limited	-0.9	0.53852	[-1, -1, 0, -2, -1, 0, -1, -1, -1, -1]
litigation	-0.8	0.6	[0, -2, -1, 0, -1, -1, -1, -1, 0, -1]
litigious	-0.8	0.9798	[-2, -1, 0, -2, 0, -2, 1, -1, 0, -1]
livelier	1.7	0.78102	[2, 2, 1, 3, 1, 3, 2, 1, 1, 1]
liveliest	2.1	0.9434	[2, 1, 2, 3, 1, 1, 2, 4, 3, 2]
livelihood	0.8	1.07703	[0, 3, 1, 0, 2, 2, 0, 0, 0, 0]
livelihoods	0.9	1.13578	[0, 0, 0, 3, 2, 2, 0, 2, 0, 0]
livelily	1.8	0.6	[2, 2, 2, 3, 1, 2, 1, 2, 2, 1]
liveliness	1.6	0.8	[1, 3, 0, 2, 2, 2, 2, 2, 1, 1]
livelong	1.7	0.78102	[3, 0, 1, 1, 2, 2, 2, 2, 2, 2]
lively	1.9	0.7	[2, 2, 3, 1, 1, 2, 3, 1, 2, 2]
livid	-2.5	0.92195	[-2, -3, -1, -2, -4, -3, -1, -3, -3, -3]
lmao	2.9	0.9434	[3, 4, 3, 1, 2, 4, 3, 3, 2, 4]
loathe	-2.2	2.08806	[-1, -4, -3, -2, -4, 2, 1, -3, -4, -4]
loathed	-2.1	1.44568	[-4, -3, -3, -3, -1, 1, -1, -1, -3, -3]
loathes	-1.9	1.13578	[-1, -4, -1, -3, -3, -1, -1, -3, -1, -1]
loathing	-2.7	0.78102	[-3, -3, -3, -1, -4, -2, -3, -3, -2, -3]
lobby	0.1	0.53852	[0, 0, 0, 1, 0, 1, 0, -1, 0, 0]
lobbying	-0.3	0.45826	[0, -1, 0, 0, 0, 0, 0, -1, -1, 0]
lol	1.8	1.46969	[1, 3, 4, 1, 2, 4, 1, 2, -1, 1]
lone	-1.1	0.3	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -1]
lonelier	-1.4	0.66332	[-2, -1, -2, -2, 0, -2, -1, -1, -1, -2]
loneliest	-2.4	0.8	[-3, -1, -2, -4, -2, -2, -3, -3, -2, -2]
loneliness	-1.8	0.6	[-2, -2, -1, -3, -2, -2, -1, -2, -1, -2]
lonelinesses	-1.5	1.36015	[-2, -2, -1, -1, 2, -1, -3, -2, -3, -2]
lonely	-1.5	0.5	[-1, -2, -2, -1, -1, -1, -1, -2, -2, -2]
loneness	-1.1	0.83066	[-1, -2, -1, -2, -1, -1, -2, 1, -1, -1]
loner	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -1, -2, -2, -1]
loners	-0.9	0.53852	[-1, -1, -2, -1, -1, -1, 0, 0, -1, -1]
lonesome	-1.5	0.67082	[-2, -1, -2, -1, -2, -1, -2, 0, -2, -2]
lonesomely	-1.3	1.00499	[-2, -2, -2, -2, -1, -2, 0, -2, -1, 1]
lonesomeness	-1.8	0.6	[-2, -2, -1, -3, -1, -2, -2, -2, -1, -2]
lonesomes	-1.4	0.4899	[-2, -1, -1, -1, -1, -1, -2, -2, -2, -1]
longing	-0.1	0.9434	[0, -1, 0, -1, -1, 0, 2, -1, 1, 0]
longingly	0.7	0.45826	[1, 0, 1, 0, 1, 1, 1, 0, 1, 1]
longings	0.4	1.2	[2, 0, -1, -1, 0, 1, 3, 0, 0, 0]
loom	-0.9	0.53852	[-1, -1, -1, -1, -1, -2, 0, -1, -1, 0]
loomed	-1.1	1.04403	[-2, -2, -1, -1, -1, -1, 0, -1, -3, 1]
looming	-0.5	1.5	[-2, -1, -1, 0, -2, -2, 3, -1, 0, 1]
looms	-0.6	1.0198	[-1, -2, -1, -1, 0, 0, 1, -2, 1, -1]
loose	-1.3	1.18743	[-2, -1, 0, -2, 0, -1, 0, -2, -4, -1]
looses	-0.6	0.91652	[0, -1, 0, -1, 0, 0, 0, 0, -3, -1]
lose	-1.7	0.45826	[-1, -2, -1, -2, -1, -2, -2, -2, -2, -2]
loser	-2.4	0.66332	[-3, -2, -2, -2, -2, -3, -3, -1, -3, -3]
losers	-2.4	0.8	[-3, -1, -2, -2, -4, -2, -3, -3, -2, -2]
loses	-1.3	1.00499	[0, -1, -1, -1, -1, -4, -1, -1, -2, -1]
losing	-1.6	0.8	[-1, -1, -1, -2, -1, -2, -3, -3, -1, -1]
loss	-1.3	0.45826	[-1, -2, -1, -1, -1, -2, -2, -1, -1, -1]
losses	-1.7	0.9	[-2, -1, -2, -3, 0, -1, -3, -1, -2, -2]
lossy	-1.2	0.87178	[-2, -2, -1, -2, 0, 0, 0, -1, -2, -2]
lost	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -2, -1, -1, -1]
louse	-1.6	1.2	[-1, -1, -3, -4, 0, -3, -1, -1, -1, -1]
loused	-1.0	0.7746	[-1, -1, -1, -1, 0, 0, -2, 0, -2, -2]
louses	-1.3	0.78102	[-2, -1, -2, -1, -1, 0, -2, 0, -2, -2]
lousewort	0.1	1.3	[-2, -2, 1, 0, 0, 0, 0, 2, 2, 0]
louseworts	-0.6	0.66332	[0, 0, 0, -2, 0, -1, 0, -1, -1, -1]
lousier	-2.2	0.4	[-2, -3, -2, -2, -3, -2, -2, -2, -2, -2]
lousiest	-2.6	0.8	[-4, -2, -3, -3, -1, -2, -3, -3, -2, -3]
lousily	-1.2	0.9798	[-1, -1, -2, -2, -2, -2, 0, -1, 1, -2]
lousiness	-1.7	0.64031	[-2, -2, -1, -1, -2, -2, -3, -2, -1, -1]
lousing	-1.1	0.9434	[-3, 0, 0, 0, -1, -2, -1, -2, -1, -1]
lousy	-2.5	0.67082	[-2, -4, -2, -3, -2, -3, -2, -2, -3, -2]
lovable	3.0	0.63246	[3, 3, 3, 4, 3, 2, 3, 3, 2, 4]
love	3.2	0.4	[3, 3, 3, 3, 3, 3, 3, 4, 4, 3]
loved	2.9	0.7	[3, 3, 4, 2, 2, 4, 3, 2, 3, 3]
lovelies	2.2	0.74833	[3, 3, 3, 1, 2, 2, 3, 2, 1, 2]
lovely	2.8	0.6	[2, 3, 3, 3, 2, 3, 4, 3, 2, 3]
lover	2.8	0.87178	[3, 1, 2, 3, 4, 3, 2, 3, 4, 3]
loverly	2.8	0.74833	[3, 2, 4, 3, 3, 2, 3, 2, 2, 4]
lovers	2.4	1.11355	[2, 3, 2, 4, 4, 1, 1, 3, 3, 1]
loves	2.7	0.9	[3, 3, 3, 2, 2, 4, 4, 2, 1, 3]
loving	2.9	0.53852	[3, 2, 3, 3, 3, 2, 4, 3, 3, 3]
lovingly	3.2	0.6	[3, 3, 3, 4, 4, 4, 2, 3, 3, 3]
lovingness	2.7	1.67631	[4, 4, 3, 3, 2, 3, -2, 4, 3, 3]
low	-1.1	0.53852	[-1, -1, -1, -1, -1, -2, -1, -2, 0, -1]
lowball	-0.8	0.87178	[-1, -2, 0, -2, -1, -1, 0, 1, -1, -1]
lowballed	-1.5	0.67082	[-2, 0, -1, -1, -2, -2, -1, -2, -2, -2]
lowballing	-0.7	0.78102	[-2, -1, -1, 1, 0, -1, -1, 0, -1, -1]
lowballs	-1.2	0.74833	[-1, -1, -1, -1, -3, -1, -2, 0, -1, -1]
lowborn	-0.7	1.1	[-1, 0, -1, 0, -2, -1, -2, -1, -1, 2]
lowboys	-0.6	1.0198	[-1, 0, 0, 0, -3, -1, 1, 0, -1, -1]
lowbred	-2.6	1.0198	[-2, -1, -2, -4, -2, -2, -4, -4, -3, -2]
lowbrow	-1.9	0.7	[-1, -3, -1, -2, -2, -2, -1, -3, -2, -2]
lowbrows	-0.6	0.66332	[0, 0, -1, 0, -2, -1, -1, 0, 0, -1]
lowdown	-0.8	0.9798	[-1, -1, 0, 0, 0, -2, -3, 0, -1, 0]
lowdowns	-0.2	0.4	[0, 0, 0, -1, 0, 0, 0, 0, -1, 0]
lowe	0.5	0.80623	[0, 0, 0, 0, 0, 1, 0, 0, 2, 2]
lowed	-0.8	0.6	[0, -1, -1, -2, -1, 0, -1, 0, -1, -1]
lower	-1.2	0.87178	[0, -2, -1, -1, -2, 0, -2, 0, -2, -2]
lowercase	0.3	0.45826	[0, 0, 0, 0, 1, 0, 1, 0, 1, 0]
lowercased	-0.2	0.4	[0, -1, 0, 0, 0, 0, -1, 0, 0, 0]
lowerclassman	-0.4	0.4899	[0, -1, -1, -1, 0, 0, 0, 0, 0, -1]
lowered	-0.5	1.11803	[-1, -1, -2, -1, -1, 2, -1, -1, 1, 0]
lowering	-1.0	0.7746	[0, -1, -1, -1, -1, 0, -3, -1, -1, -1]
lowermost	-1.4	1.11355	[-1, -1, -3, -1, -1, -1, -2, -3, 1, -2]
lowers	-0.5	0.5	[-1, -1, 0, -1, 0, 0, 0, -1, -1, 0]
lowery	-1.8	0.87178	[-1, -1, -2, -3, -1, -2, -3, -3, -1, -1]
lowest	-1.6	0.4899	[-2, -2, -2, -2, -1, -2, -1, -1, -1, -2]
lowing	-0.5	0.67082	[0, 0, 0, -1, 0, 0, -1, -2, -1, 0]
lowish	-0.9	0.53852	[-2, -1, -1, 0, -1, 0, -1, -1, -1, -1]
lowland	-0.1	0.3	[0, 0, -1, 0, 0, 0, 0, 0, 0, 0]
lowlander	-0.4	0.66332	[0, 0, 0, 0, 0, 0, -1, -1, -2, 0]
lowlanders	-0.3	0.64031	[0, 0, 0, -2, 0, -1, 0, 0, 0, 0]
lowlands	-0.1	0.7	[0, -1, -1, 0, -1, 1, 0, 1, 0, 0]
lowlier	-1.7	0.78102	[-2, -2, -2, -3, -1, -2, -2, -1, 0, -2]
lowliest	-1.8	1.6	[-1, -3, -3, -4, -2, -1, -1, 0, 1, -4]
lowlife	-1.5	0.67082	[-2, -2, -2, -1, -1, -2, -1, 0, -2, -2]
lowlifes	-2.2	1.249	[-3, -2, -3, -4, -2, 1, -2, -3, -2, -2]
lowlight	-2.0	1.26491	[-3, -2, -1, -3, 1, -2, -1, -3, -3, -3]
lowlights	-0.3	0.78102	[0, 0, -1, 1, -2, -1, 0, 0, 0, 0]
lowlihead	-0.3	1.34536	[-1, -1, 1, -3, 0, 0, 1, -1, 2, -1]
lowliness	-1.1	0.53852	[-1, -2, -1, -2, -1, -1, -1, 0, -1, -1]
lowlinesses	-1.2	1.07703	[-3, -1, -2, -2, 0, -1, -1, -1, -2, 1]
lowlives	-2.1	0.7	[-2, -3, -3, -2, -1, -2, -3, -2, -1, -2]
lowly	-1.0	1.34164	[-1, -2, -1, -2, 2, -2, -1, 1, -2, -2]
lown	0.9	1.13578	[2, 2, 1, -1, 0, 1, 2, -1, 1, 2]
lowness	-1.3	0.45826	[-1, -2, -2, -1, -1, -2, -1, -1, -1, -1]
lowrider	-0.2	0.4	[0, 0, 0, -1, -1, 0, 0, 0, 0, 0]
lowriders	0.1	0.53852	[0, 0, 0, 0, 1, 0, -1, 0, 1, 0]
lows	-0.8	0.9798	[0, -1, -1, -1, 0, 1, -3, -1, -1, -1]
lowse	-0.7	0.78102	[0, -1, 1, -1, -1, -1, 0, -1, -2, -1]
loyal	2.1	0.7	[3, 2, 1, 1, 2, 2, 3, 2, 3, 2]
loyalism	1.0	0.89443	[1, 2, 1, -1, 0, 1, 2, 1, 1, 2]
loyalisms	0.9	0.83066	[2, 0, 0, 1, 1, 0, 2, 2, 1, 0]
loyalist	1.5	0.92195	[1, 0, 2, 2, 3, 0, 2, 2, 1, 2]
loyalists	1.1	0.83066	[1, 1, 1, 3, 0, 2, 1, 0, 1, 1]
loyally	2.1	0.7	[3, 2, 2, 2, 1, 3, 1, 2, 3, 2]
loyalties	1.9	0.7	[3, 1, 2, 2, 2, 3, 2, 1, 1, 2]
loyalty	2.5	0.67082	[1, 3, 3, 3, 3, 2, 2, 2, 3, 3]
luck	2.0	0.63246	[2, 2, 1, 3, 3, 2, 1, 2, 2, 2]
lucked	1.9	0.7	[2, 2, 2, 1, 1, 2, 3, 3, 1, 2]
luckie	1.6	0.66332	[1, 1, 1, 2, 2, 1, 1, 2, 2, 3]
luckier	1.9	0.7	[1, 3, 1, 2, 2, 1, 2, 3, 2, 2]
luckiest	2.9	0.7	[3, 3, 4, 2, 2, 4, 3, 3, 2, 3]
luckily	2.3	0.45826	[2, 2, 3, 2, 2, 3, 2, 2, 2, 3]
luckiness	1.0	1.61245	[2, 2, 1, -2, 3, 1, 1, 2, 2, -2]
lucking	1.2	0.6	[2, 1, 1, 0, 1, 1, 1, 2, 2, 1]
luckless	-1.3	0.45826	[-2, -1, -2, -1, -1, -1, -2, -1, -1, -1]
lucks	1.6	0.91652	[0, 3, 1, 1, 3, 2, 2, 1, 2, 1]
lucky	1.8	0.74833	[2, 1, 1, 1, 3, 2, 1, 2, 2, 3]
ludicrous	-1.5	1.36015	[2, -2, -2, -1, -1, -1, -3, -3, -2, -2]
ludicrously	-0.2	1.83303	[3, -1, 0, -2, -1, 0, 3, -3, -1, 0]
ludicrousness	-1.9	1.57797	[-1, 2, -2, -3, -2, -2, -3, -4, -1, -3]
lugubrious	-2.1	1.37477	[-3, -2, -3, -3, -3, 1, -2, -3, 0, -3]
lulz	2.0	1.0	[2, 2, 2, 3, 4, 1, 3, 1, 1, 1]
lunatic	-2.2	1.32665	[-2, -3, -4, -2, -3, -2, -1, -3, 1, -3]
lunatics	-1.6	1.95959	[-4, -2, -3, -4, -2, 2, -1, -3, 1, 0]
lurk	-0.8	0.87178	[-1, -1, 0, -3, -1, 0, -1, 0, -1, 0]
lurking	-0.5	1.11803	[1, -1, -1, 0, -1, -2, 1, -2, 1, -1]
lurks	-0.9	1.04403	[-1, -1, -1, -2, -2, -1, -1, -1, 2, -1]
lying	-2.4	0.8	[-2, -4, -3, -1, -2, -2, -3, -2, -2, -3]
mad	-2.2	0.74833	[-3, -3, -1, -1, -2, -3, -2, -2, -3, -2]
maddening	-2.2	0.74833	[-3, -3, -1, -1, -3, -2, -2, -3, -2, -2]
madder	-1.2	1.16619	[-2, 0, -3, 0, 0, -1, -3, -1, -2, 0]
maddest	-2.8	1.16619	[-4, -4, -4, -4, -1, -3, -2, -1, -2, -3]
madly	-1.7	1.1	[-2, 0, -2, -2, -2, -1, -4, -2, 0, -2]
madness	-1.9	0.53852	[-1, -2, -1, -3, -2, -2, -2, -2, -2, -2]
magnific	2.3	1.1	[2, 2, 0, 2, 4, 4, 2, 2, 3, 2]
magnifical	2.4	1.28062	[1, 4, 1, 2, 4, 3, 1, 1, 4, 3]
magnifically	2.4	1.2	[3, 3, 3, 2, 4, 0, 1, 2, 2, 4]
magnification	1.0	0.89443	[2, 3, 1, 0, 0, 1, 1, 1, 0, 1]
magnifications	1.2	1.249	[-1, 0, 0, 2, 2, 2, 0, 2, 3, 2]
magnificence	2.4	1.0198	[3, 2, 1, 3, 2, 1, 4, 4, 2, 2]
magnificences	2.3	0.9	[3, 4, 3, 1, 3, 2, 1, 2, 2, 2]
magnificent	2.9	0.7	[2, 4, 2, 4, 3, 3, 3, 3, 2, 3]
magnificently	3.4	0.66332	[3, 3, 3, 4, 4, 2, 4, 4, 3, 4]
magnifico	1.8	0.87178	[2, 1, 1, 2, 1, 1, 3, 3, 3, 1]
magnificoes	1.4	0.8	[2, 2, 0, 2, 1, 1, 3, 1, 1, 1]
mandatory	0.3	0.9	[1, 0, -1, 0, -1, 0, 1, 2, 0, 1]
maniac	-2.1	0.7	[-1, -2, -2, -2, -3, -3, -1, -2, -2, -3]
maniacal	-0.3	1.73494	[2, -1, -3, -3, 0, 2, -1, 1, 1, -1]
maniacally	-1.7	0.78102	[-1, -2, -1, -2, -3, -2, -1, -3, -1, -1]
maniacs	-1.2	1.8868	[-3, 1, -2, 1, -3, -2, 0, 2, -3, -3]
manipulated	-1.6	0.4899	[-2, -1, -1, -2, -1, -2, -2, -1, -2, -2]
manipulating	-1.5	0.80623	[-1, -1, -1, -2, -2, -1, -2, -2, -3, 0]
manipulation	-1.2	1.72047	[2, -3, -2, -3, -1, -1, -2, 2, -2, -2]
marvel	1.8	0.6	[2, 1, 1, 2, 1, 2, 3, 2, 2, 2]
marvelous	2.9	0.7	[2, 2, 3, 2, 3, 3, 4, 3, 4, 3]
marvels	2.0	0.89443	[3, 1, 1, 3, 1, 2, 1, 2, 3, 3]
masochism	-1.6	1.11355	[-1, 0, -2, -2, -2, -2, 0, -4, -1, -2]
masochisms	-1.1	1.57797	[3, -3, -2, -2, -1, -1, -2, -1, 0, -2]
masochist	-1.7	0.9	[-1, 0, -3, -1, -2, -2, -2, -2, -1, -3]
masochistic	-2.2	1.16619	[-1, -1, -1, -2, -3, -2, -4, -3, -4, -1]
masochistically	-1.6	1.35647	[-1, -1, -3, -3, -3, -3, 0, -2, 1, -1]
masochists	-1.2	1.07703	[1, -2, 0, -2, -3, -1, -2, -1, -1, -1]
masterpiece	3.1	0.83066	[3, 4, 4, 4, 2, 2, 2, 4, 3, 3]
masterpieces	2.5	0.67082	[2, 2, 3, 2, 2, 2, 4, 3, 3, 2]
matter	0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 1, 0, 0]
matters	0.1	0.3	[0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
mature	1.8	0.4	[2, 2, 2, 2, 2, 1, 2, 2, 1, 2]
meaningful	1.3	0.9	[0, 0, 2, 1, 1, 3, 1, 2, 2, 1]
meaningless	-1.9	0.7	[-1, -2, -2, -2, -1, -1, -2, -3, -3, -2]
medal	2.1	1.22066	[2, 4, 3, 1, 0, 2, 2, 2, 1, 4]
mediocrity	-0.3	1.1	[-1, 0, -2, 0, -1, 2, -1, -1, 0, 1]
meditative	1.4	0.66332	[2, 0, 1, 2, 2, 1, 2, 1, 2, 1]
meh	-0.3	0.78102	[-1, 0, -1, 0, -1, -1, 1, 0, 1, -1]
melancholia	-0.5	1.28452	[0, -2, -2, 0, 1, -1, 2, -2, 0, -1]
melancholiac	-2.0	0.63246	[-2, -2, -3, -2, -2, -3, -1, -1, -2, -2]
melancholias	-1.6	0.8	[-1, -2, -3, -2, 0, -2, -1, -1, -2, -2]
melancholic	-0.3	1.18743	[0, 2, -2, 0, 1, -1, 0, -2, 0, -1]
melancholics	-1.0	1.0	[0, -1, -3, 1, -1, -2, -1, -1, -1, -1]
melancholies	-1.1	0.83066	[-1, 0, -1, 0, -3, -1, -2, -1, -1, -1]
melancholy	-1.9	1.13578	[-3, -2, -3, -2, -3, -2, -2, 1, -1, -2]
menace	-2.2	0.87178	[-3, -3, -2, -1, -1, -2, -3, -1, -3, -3]
menaced	-1.7	1.48661	[-3, -2, -2, -3, -3, -1, 1, -3, 1, -2]
mercy	1.5	0.67082	[1, 2, 1, 1, 1, 3, 2, 2, 1, 1]
merit	1.8	0.74833	[2, 2, 2, 2, 0, 2, 3, 2, 2, 1]
merited	1.4	0.4899	[1, 2, 2, 1, 1, 1, 2, 1, 2, 1]
meriting	1.1	1.13578	[1, 2, 1, -2, 1, 1, 2, 2, 1, 2]
meritocracy	0.6	1.35647	[2, 4, 0, 0, 1, -1, 0, 0, 0, 0]
meritocrat	0.4	0.8	[0, 0, 0, 1, -1, 0, 0, 1, 2, 1]
meritocrats	1.1	1.13578	[2, 1, 1, 0, 0, 1, 1, 4, 0, 1]
meritorious	2.1	0.53852	[3, 2, 2, 2, 2, 2, 2, 2, 3, 1]
meritoriously	1.3	1.95192	[3, -1, 3, -2, 2, 0, -1, 3, 3, 3]
meritoriousness	1.7	1.18743	[4, 1, 2, 1, 2, 0, 3, 2, 2, 0]
merits	1.7	0.78102	[1, 3, 1, 1, 2, 2, 1, 1, 3, 2]
merrier	1.7	1.41774	[3, 2, 2, 3, 2, 2, -1, -1, 3, 2]
merriest	2.7	1.41774	[3, 4, 4, 4, 2, 3, -1, 2, 3, 3]
merrily	2.4	0.66332	[3, 3, 2, 2, 2, 3, 2, 3, 1, 3]
merriment	2.4	1.35647	[1, 3, 3, 4, 2, -1, 3, 3, 3, 3]
merriments	2.0	0.89443	[2, 2, 3, 3, 0, 2, 2, 1, 2, 3]
merriness	2.2	0.74833	[2, 1, 2, 3, 2, 2, 3, 3, 1, 3]
merry	2.5	0.80623	[2, 4, 2, 2, 3, 3, 3, 3, 2, 1]
merrymaker	2.2	1.4	[3, 1, 3, 3, 3, -1, 2, 1, 3, 4]
merrymakers	1.7	1.34536	[1, 4, 1, 3, 1, 3, -1, 2, 1, 2]
merrymaking	2.2	0.6	[3, 2, 2, 2, 2, 1, 3, 3, 2, 2]
merrymakings	2.4	1.11355	[3, 3, 2, 3, 4, 2, 0, 3, 1, 3]
merrythought	1.1	0.9434	[1, 1, 3, 0, 2, 0, 0, 1, 2, 1]
merrythoughts	1.6	1.11355	[1, 3, 2, 1, 2, 1, 0, 3, 3, 0]
mess	-1.5	0.92195	[-1, -3, -1, -1, -3, -1, -2, -1, 0, -2]
messed	-1.4	0.8	[-2, -1, -1, -3, 0, -2, -2, -1, -1, -1]
messy	-1.5	0.80623	[-1, -2, -2, -2, -1, -1, -2, 0, -1, -3]
methodical	0.6	0.8	[0, 0, 0, 2, 2, 0, 1, 1, 0, 0]
mindless	-1.9	0.7	[-2, -1, -2, -1, -1, -2, -3, -3, -2, -2]
miracle	2.8	0.87178	[4, 4, 3, 2, 3, 4, 2, 2, 2, 2]
mirth	2.6	0.66332	[3, 3, 3, 2, 3, 3, 3, 2, 1, 3]
mirthful	2.7	0.45826	[3, 3, 3, 2, 3, 3, 3, 2, 3, 2]
mirthfully	2.0	1.48324	[2, 3, 4, 3, 0, -1, 2, 1, 3, 3]
misbehave	-1.9	0.7	[-3, -3, -1, -2, -2, -1, -1, -2, -2, -2]
misbehaved	-1.6	0.4899	[-1, -2, -1, -2, -2, -2, -1, -2, -1, -2]
misbehaves	-1.6	0.4899	[-1, -2, -2, -1, -2, -2, -1, -2, -1, -2]
misbehaving	-1.7	0.64031	[-1, -2, -1, -2, -3, -2, -1, -2, -1, -2]
mischief	-1.5	0.67082	[-2, -1, -1, -1, -1, -2, -3, -2, -1, -1]
mischiefs	-0.8	1.72047	[-2, -1, -2, -2, 3, -1, -2, 2, -1, -2]
miser	-1.8	0.87178	[-1, -2, 0, -3, -2, -2, -3, -2, -2, -1]
miserable	-2.2	1.32665	[-2, -2, -3, -3, -4, -3, -1, -2, 1, -3]
miserableness	-2.8	0.6	[-3, -3, -3, -3, -2, -2, -3, -4, -2, -3]
miserably	-2.1	1.37477	[-2, -1, -3, -3, -4, -3, -1, -2, 1, -3]
miserere	-0.8	1.07703	[-1, -1, 0, -3, 0, -1, 0, 1, -2, -1]
misericorde	0.1	1.64012	[1, 1, -1, 2, 1, 2, -3, -1, -2, 1]
misericordes	-0.5	1.36015	[0, 0, -1, 0, 0, 1, -3, 0, 1, -3]
miseries	-2.7	0.78102	[-3, -4, -1, -2, -3, -3, -2, -3, -3, -3]
miserliness	-2.6	1.0198	[-3, -1, -3, -2, -2, -1, -3, -4, -4, -3]
miserly	-1.4	0.91652	[0, -2, -2, 0, -1, -2, -2, -1, -3, -1]
misers	-1.5	0.92195	[0, -1, -1, -3, -1, -1, -2, -3, -2, -1]
misery	-2.7	0.45826	[-2, -2, -3, -3, -2, -3, -3, -3, -3, -3]
misgiving	-1.4	0.4899	[-2, -2, -2, -1, -1, -1, -2, -1, -1, -1]
misinformation	-1.3	0.64031	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -3]
misinformed	-1.6	0.4899	[-1, -1, -1, -2, -2, -1, -2, -2, -2, -2]
misinterpreted	-1.3	0.64031	[-1, -2, -1, 0, -2, -1, -1, -1, -2, -2]
misleading	-1.7	0.64031	[-2, -1, -3, -1, -1, -2, -2, -2, -1, -2]
misread	-1.1	0.3	[-1, -1, -1, -2, -1, -1, -1, -1, -1, -1]
misreporting	-1.5	0.5	[-2, -1, -1, -1, -2, -1, -2, -2, -1, -2]
misrepresentation	-2.0	0.63246	[-2, -3, -2, -1, -1, -3, -2, -2, -2, -2]
miss	-0.6	1.35647	[-1, -1, -1, -1, -2, -1, 2, 2, -2, -1]
missed	-1.2	0.74833	[-2, -1, 0, -1, -1, -1, -3, -1, -1, -1]
misses	-0.9	0.3	[-1, -1, 0, -1, -1, -1, -1, -1, -1, -1]
missing	-1.2	0.4	[-1, -2, -1, -1, -1, -1, -2, -1, -1, -1]
mistakable	-0.8	0.4	[-1, -1, -1, -1, -1, -1, -1, 0, 0, -1]
mistake	-1.4	0.4899	[-1, -2, -1, -1, -1, -1, -2, -2, -2, -1]
mistaken	-1.5	0.67082	[-2, -1, -2, -2, -1, -1, -1, -3, -1, -1]
mistakenly	-1.2	0.4	[-1, -1, -2, -1, -1, -1, -1, -2, -1, -1]
mistaker	-1.6	0.4899	[-2, -1, -2, -1, -2, -2, -1, -2, -1, -2]
mistakers	-1.6	0.8	[-3, -1, -1, -1, -3, -1, -1, -2, -2, -1]
mistakes	-1.5	0.67082	[-2, -1, -2, -1, -3, -2, -1, -1, -1, -1]
mistaking	-1.1	0.53852	[-1, -1, -1, -1, 0, -1, -1, -1, -2, -2]
misunderstand	-1.5	0.67082	[-3, -2, -1, -1, -1, -1, -1, -2, -2, -1]
misunderstanding	-1.8	0.6	[-1, -1, -1, -3, -2, -2, -2, -2, -2, -2]
misunderstands	-1.3	0.45826	[-1, -2, -1, -2, -1, -1, -2, -1, -1, -1]
misunderstood	-1.4	0.66332	[-1, -1, -1, -3, -1, -1, -2, -1, -2, -1]
mlm	-1.4	1.68523	[0, -2, -2, -3, -4, -2, 0, 1, 1, -3]
mmk	0.6	1.0198	[0, 0, 0, 0, 0, 3, 1, 0, 0, 2]
moan	-0.6	1.62481	[-2, -1, 0, -3, 2, -2, 0, 0, -2, 2]
moaned	-0.4	1.35647	[-2, 0, -3, 0, -1, -1, 2, 1, 0, 0]
moaning	-0.4	1.28062	[-1, -1, 1, -1, 0, 0, 2, 0, -3, -1]
moans	-0.6	0.8	[-2, 0, 0, -2, 0, -1, 0, 0, 0, -1]
mock	-1.8	0.74833	[-3, -1, -1, -2, -2, -1, -3, -1, -2, -2]
mocked	-1.3	1.26886	[-2, -2, -1, -2, -2, -3, -2, 1, 1, -1]
mocker	-0.8	1.46969	[-1, -2, 2, -2, -2, -2, -2, 1, 1, -1]
mockeries	-1.6	0.8	[-3, -1, -2, -2, -2, 0, -1, -2, -1, -2]
mockers	-1.3	0.78102	[-2, -3, 0, -1, -1, -1, -2, -1, -1, -1]
mockery	-1.3	0.45826	[-2, -1, -1, -1, -1, -1, -2, -2, -1, -1]
mocking	-1.7	0.64031	[-2, -1, -1, -2, -1, -1, -3, -2, -2, -2]
mocks	-2.0	0.63246	[-1, -3, -2, -2, -2, -2, -2, -1, -3, -2]
molest	-2.1	1.81384	[-4, -1, -2, -4, -3, 1, -3, 1, -2, -4]
molestation	-1.9	1.57797	[-2, -2, -3, -2, -3, -2, -4, 1, 1, -3]
molestations	-2.9	1.04403	[-3, -4, -4, -4, -3, -2, -2, -1, -2, -4]
molested	-1.9	1.92094	[-4, 1, -1, -4, -2, -1, -4, -4, 1, -1]
molester	-2.3	1.61555	[-4, -2, -1, -4, -2, -1, -4, -4, 1, -2]
molesters	-2.2	1.66132	[-2, -4, -3, -3, -3, 1, 1, -3, -3, -3]
molesting	-2.8	1.72047	[-4, -4, -4, -4, -3, -4, -1, -1, 1, -4]
molests	-3.1	1.13578	[-3, -4, -4, -4, -3, -4, 0, -3, -3, -3]
mongering	-0.8	1.16619	[-3, -2, 0, 0, -1, 1, -2, 0, -1, 0]
monopolize	-0.8	1.6	[-1, -3, -2, -2, 0, -1, -1, -2, 2, 2]
monopolized	-0.9	0.53852	[0, -2, -1, 0, -1, -1, -1, -1, -1, -1]
monopolizes	-1.1	0.83066	[0, -3, -1, 0, -1, -1, -2, -1, -1, -1]
monopolizing	-0.5	1.56525	[2, -1, -2, -1, -3, -1, -1, 2, 1, -1]
mooch	-1.7	0.9	[-1, -1, -1, -3, -1, -3, -3, -2, -1, -1]
mooched	-1.4	0.4899	[-2, -1, -2, -1, -1, -2, -1, -1, -2, -1]
moocher	-1.5	0.67082	[-2, -1, -2, -1, -1, -2, -1, -1, -3, -1]
moochers	-1.9	0.7	[-3, -2, -2, -3, -1, -1, -1, -2, -2, -2]
mooches	-1.4	0.66332	[-2, -1, -2, -1, 0, -2, -1, -1, -2, -2]
mooching	-1.7	0.64031	[-3, -2, -1, -1, -2, -2, -1, -2, -1, -2]
moodier	-1.1	1.13578	[-2, -1, -1, -1, -2, -1, -2, 2, -1, -2]
moodiest	-2.1	0.9434	[-1, -2, -2, -2, -1, -2, -4, -3, -1, -3]
moodily	-1.3	0.45826	[-1, -1, -1, -1, -1, -2, -2, -1, -2, -1]
moodiness	-1.4	0.66332	[-2, -1, -2, -2, -1, 0, -2, -1, -1, -2]
moodinesses	-1.4	0.4899	[-2, -1, -1, -2, -1, -1, -1, -2, -2, -1]
moody	-1.5	0.67082	[-1, -1, -1, -2, -2, -3, -1, -1, -1, -2]
mope	-1.9	0.53852	[-2, -2, -1, -2, -2, -2, -2, -3, -1, -2]
moping	-1.0	1.48324	[-2, -2, -1, -1, 1, 2, -3, -2, 0, -2]
moron	-2.2	0.6	[-2, -1, -2, -3, -3, -2, -2, -3, -2, -2]
moronic	-2.7	0.64031	[-3, -3, -3, -3, -2, -4, -2, -3, -2, -2]
moronically	-1.4	1.8	[-4, -2, -1, 2, -2, -2, -3, -3, 0, 1]
moronity	-1.1	1.22066	[-2, -1, -2, -1, 0, -2, 2, -2, -2, -1]
morons	-1.3	1.1	[-1, -1, -1, 0, -3, -2, 0, 0, -2, -3]
motherfucker	-3.6	0.66332	[-3, -4, -4, -4, -4, -2, -4, -3, -4, -4]
motherfucking	-2.8	1.249	[-3, -1, -4, 0, -3, -3, -4, -4, -3, -3]
motivate	1.6	0.4899	[1, 1, 1, 1, 2, 2, 2, 2, 2, 2]
motivated	2.0	0.63246	[3, 3, 1, 2, 1, 2, 2, 2, 2, 2]
motivating	2.2	0.6	[2, 2, 3, 3, 3, 2, 2, 2, 1, 2]
motivation	1.4	0.66332	[1, 1, 2, 0, 2, 1, 2, 1, 2, 2]
mourn	-1.8	0.6	[-2, -2, -2, -2, -2, -1, -2, -3, -1, -1]
mourned	-1.3	1.55242	[-1, -2, -3, -3, -2, -1, 1, -2, 2, -2]
mourner	-1.6	1.35647	[-3, 0, -2, -1, -1, 0, -2, 0, -3, -4]
mourners	-1.8	0.74833	[-1, -2, -3, -1, -2, -1, -2, -1, -2, -3]
mournful	-1.6	1.62481	[-2, -3, -3, 2, -2, -3, -2, 1, -2, -2]
mournfuller	-1.9	0.9434	[-2, -3, -3, -3, 0, -2, -1, -2, -2, -1]
mournfully	-1.7	1.55242	[-4, -3, -2, -3, 1, -2, -2, -2, 1, -1]
mournfulness	-1.8	1.4	[-4, -3, 1, -3, -1, -2, 0, -2, -2, -2]
mourning	-1.9	1.04403	[-1, -1, -1, -1, -2, -3, -4, -3, -2, -1]
mourningly	-2.3	1.18743	[-3, -3, -3, -3, -2, -3, -2, 1, -2, -3]
mourns	-2.4	0.66332	[-2, -2, -3, -2, -3, -3, -3, -1, -2, -3]
muah	2.3	1.26886	[0, 3, 1, 2, 3, 1, 4, 4, 3, 2]
mumpish	-1.4	0.66332	[-1, -1, -2, -2, 0, -1, -2, -2, -1, -2]
murder	-3.7	0.64031	[-4, -4, -4, -4, -2, -4, -4, -4, -3, -4]
murdered	-3.4	0.66332	[-4, -3, -2, -3, -4, -4, -3, -3, -4, -4]
murderee	-3.2	0.6	[-3, -3, -2, -3, -4, -4, -3, -3, -4, -3]
murderees	-3.1	0.7	[-2, -4, -3, -4, -3, -2, -3, -3, -4, -3]
murderer	-3.6	0.4899	[-4, -3, -3, -3, -4, -4, -4, -3, -4, -4]
murderers	-3.3	0.78102	[-3, -4, -4, -4, -4, -2, -4, -3, -3, -2]
murderess	-2.2	1.72047	[-2, -2, -3, -4, -3, -3, -4, 1, 1, -3]
murderesses	-2.6	0.8	[-2, -4, -3, -4, -2, -2, -2, -2, -3, -2]
murdering	-3.3	0.78102	[-4, -2, -4, -3, -4, -3, -2, -4, -4, -3]
murderous	-3.2	0.74833	[-3, -3, -4, -3, -4, -4, -4, -2, -2, -3]
murderously	-3.1	0.9434	[-3, -4, -4, -4, -3, -4, -1, -2, -3, -3]
murderousness	-2.9	0.7	[-3, -3, -4, -2, -2, -2, -4, -3, -3, -3]
murders	-3.0	1.84391	[-4, -4, -4, 2, -4, -2, -4, -4, -2, -4]
n00b	-1.6	0.8	[-1, -3, -1, -2, -2, -2, -2, -2, -1, 0]
nag	-1.5	0.80623	[-1, -1, -3, -1, -3, -2, -1, -1, -1, -1]
nagana	-1.7	0.9	[-2, -2, 0, 0, -2, -3, -2, -2, -2, -2]
nagged	-1.7	0.45826	[-2, -1, -2, -1, -2, -2, -1, -2, -2, -2]
nagger	-1.8	0.4	[-2, -1, -2, -2, -2, -2, -1, -2, -2, -2]
naggers	-1.5	0.67082	[-1, -1, -2, -2, -2, -1, -1, -3, -1, -1]
naggier	-1.4	1.11355	[-2, -3, -2, -2, 1, -1, -2, -1, 0, -2]
naggiest	-2.4	0.91652	[-3, -2, -2, -1, -4, -2, -3, -3, -3, -1]
nagging	-1.7	0.64031	[-1, -1, -1, -1, -2, -2, -2, -2, -3, -2]
naggingly	-0.9	1.37477	[-2, 2, -1, -1, -1, -3, 1, -1, -1, -2]
naggy	-1.7	0.64031	[-2, -2, -1, -1, -3, -2, -1, -2, -1, -2]
nags	-1.1	0.53852	[-1, -1, -2, -1, -1, -2, -1, -1, 0, -1]
nah	-0.4	1.28062	[0, 0, 1, -1, -3, -1, 0, 2, -1, -1]
naive	-1.1	1.22066	[-2, -2, -1, 2, -1, -1, -1, -3, -1, -1]
nastic	0.2	0.6	[2, 0, 0, 0, 0, 0, 0, 0, 0, 0]
nastier	-2.3	0.45826	[-2, -2, -2, -3, -3, -2, -3, -2, -2, -2]
nasties	-2.1	0.3	[-2, -2, -2, -2, -3, -2, -2, -2, -2, -2]
nastiest	-2.4	1.85472	[-4, -4, -4, -2, -3, -2, 1, 1, -3, -4]
nastily	-1.9	0.7	[-2, -3, -1, -2, -2, -3, -1, -1, -2, -2]
nastiness	-1.1	1.44568	[-2, -2, -3, -2, 1, -1, -1, -1, 2, -2]
nastinesses	-2.6	0.66332	[-2, -3, -3, -2, -3, -3, -3, -1, -3, -3]
nasturtium	0.4	0.66332	[0, 0, 0, 0, 0, 1, 1, 0, 2, 0]
nasturtiums	0.1	0.3	[0, 0, 0, 0, 0, 0, 1, 0, 0, 0]
nasty	-2.6	1.0198	[-3, -1, -2, -4, -3, -3, -1, -2, -4, -3]
natural	1.5	1.0247	[2, 0, 0, 1, 1, 3, 2, 2, 3, 1]
neat	2.0	0.89443	[2, 1, 1, 2, 3, 1, 2, 2, 2, 4]
neaten	1.2	0.4	[1, 1, 1, 1, 1, 2, 2, 1, 1, 1]
neatened	2.0	1.09545	[0, 2, 1, 3, 1, 2, 4, 3, 2, 2]
neatening	1.3	0.45826	[1, 1, 1, 1, 2, 2, 2, 1, 1, 1]
neatens	1.1	0.83066	[0, 1, 0, 1, 1, 1, 1, 3, 2, 1]
neater	1.0	0.44721	[1, 1, 1, 1, 1, 1, 2, 0, 1, 1]
neatest	1.7	0.64031	[1, 2, 2, 1, 2, 2, 1, 2, 1, 3]
neath	0.2	0.9798	[0, 0, -2, 0, 0, 2, 0, 1, 1, 0]
neatherd	-0.4	0.8	[0, 0, 0, -2, 0, -1, 1, -1, -1, 0]
neatly	1.4	0.66332	[2, 1, 1, 2, 2, 2, 0, 1, 1, 2]
neatness	1.3	0.64031	[2, 1, 1, 0, 1, 1, 2, 2, 2, 1]
neats	1.1	0.53852	[2, 1, 2, 1, 0, 1, 1, 1, 1, 1]
needy	-1.4	0.4899	[-1, -2, -2, -2, -1, -1, -1, -1, -2, -1]
negative	-2.7	0.9	[-1, -3, -3, -2, -4, -2, -2, -4, -3, -3]
negativity	-2.3	0.45826	[-2, -2, -2, -2, -3, -2, -3, -3, -2, -2]
neglect	-2.0	0.63246	[-2, -2, -1, -1, -3, -2, -3, -2, -2, -2]
neglected	-2.4	1.0198	[-1, -2, -4, -3, -2, -2, -1, -4, -3, -2]
neglecter	-1.7	0.64031	[-2, -2, -1, -1, -1, -3, -2, -2, -1, -2]
neglecters	-1.5	0.67082	[-2, -1, -1, -1, -2, -1, -1, -2, -3, -1]
neglectful	-2.0	0.63246	[-3, -3, -1, -2, -2, -2, -1, -2, -2, -2]
neglectfully	-2.1	0.9434	[-1, -3, -3, -1, -1, -2, -2, -2, -4, -2]
neglectfulness	-2.0	0.63246	[-2, -2, -2, -2, -1, -3, -2, -2, -1, -3]
neglecting	-1.7	0.78102	[-2, -1, -3, -2, -1, -3, -1, -1, -2, -1]
neglects	-2.2	0.4	[-2, -2, -2, -2, -2, -3, -2, -3, -2, -2]
nerd	-1.2	0.6	[-1, -2, -1, -2, -1, 0, -1, -1, -2, -1]
nerdier	-0.2	0.87178	[0, 0, -1, -1, 0, -1, 2, 0, 0, -1]
nerdiest	0.6	1.28062	[2, 3, 0, -1, 1, 0, 0, -1, 2, 0]
nerdish	-0.1	1.04403	[-1, -1, 2, 0, -1, -1, 1, 0, 1, -1]
nerdy	-0.2	1.249	[-1, -1, -1, 3, 0, 0, 0, 0, -2, 0]
nerves	-0.4	0.8	[0, -1, 0, 1, -2, -1, 0, -1, 0, 0]
nervous	-1.1	0.53852	[-1, -1, -1, -2, -2, -1, -1, 0, -1, -1]
nervously	-0.6	1.56205	[-1, -1, -1, -1, -1, -2, 4, -1, -1, -1]
nervousness	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -1, -2, -2, -1]
neurotic	-1.4	1.11355	[-3, -2, -2, -1, 1, -2, -2, -1, 0, -2]
neurotically	-1.8	1.16619	[0, -2, -1, -3, -3, -1, 0, -2, -3, -3]
neuroticism	-0.9	1.22066	[-2, -1, -1, -1, -2, 0, -3, 1, 1, -1]
neurotics	-0.7	1.61555	[-2, 0, -1, -2, -1, -3, 0, 2, 2, -2]
nice	1.8	0.74833	[3, 1, 1, 2, 2, 1, 3, 1, 2, 2]
nicely	1.9	0.83066	[2, 1, 2, 2, 4, 2, 1, 1, 2, 2]
niceness	1.6	0.66332	[1, 1, 3, 2, 1, 1, 2, 2, 2, 1]
nicenesses	2.1	1.22066	[4, 0, 3, 1, 2, 1, 4, 2, 2, 2]
nicer	1.9	0.53852	[2, 2, 1, 2, 3, 2, 2, 2, 1, 2]
nicest	2.2	0.87178	[1, 4, 1, 2, 2, 2, 3, 3, 2, 2]
niceties	1.5	1.20416	[1, 4, 1, 1, 2, 1, 0, 3, 2, 0]
nicety	1.2	1.07703	[1, 0, 4, 1, 1, 0, 2, 1, 1, 1]
nifty	1.7	0.64031	[2, 2, 1, 1, 1, 2, 3, 2, 2, 1]
niggas	-1.4	2.2	[-4, -3, 2, 1, -4, -2, 0, -1, 1, -4]
nigger	-3.3	1.18743	[-4, -4, -4, -4, -4, -4, -1, -3, -1, -4]
no	-1.2	0.74833	[-1, -1, -1, -1, -1, -1, 0, -1, -2, -3]
noble	2.0	0.89443	[2, 1, 2, 2, 3, 0, 2, 3, 2, 3]
noisy	-0.7	0.64031	[-2, 0, -1, -1, 0, -1, -1, 0, 0, -1]
nonsense	-1.7	0.64031	[-3, -1, -1, -1, -2, -1, -2, -2, -2, -2]
noob	-0.2	1.16619	[-2, 0, -1, 0, -1, 2, 1, 1, -1, -1]
nosey	-0.8	1.16619	[-2, -2, -2, 1, -1, -1, 0, 1, 0, -2]
notorious	-1.9	1.3	[-2, -4, -3, -2, -2, -2, -1, -1, -3, 1]
novel	1.3	0.64031	[2, 0, 1, 1, 1, 1, 2, 2, 2, 1]
numb	-1.4	0.66332	[-1, -1, -1, -1, -2, -1, -1, -3, -2, -1]
numbat	0.2	0.4	[0, 0, 0, 0, 1, 1, 0, 0, 0, 0]
numbed	-0.9	0.53852	[-1, -1, -1, -1, 0, 0, -2, -1, -1, -1]
number	0.3	0.64031	[0, 0, 1, 0, 0, 2, 0, 0, 0, 0]
numberable	0.6	0.91652	[0, 2, 0, 0, 0, 2, 2, 0, 0, 0]
numbest	-1.0	0.89443	[-2, -1, 0, 0, -3, -1, -1, -1, 0, -1]
numbfish	-0.4	0.66332	[-1, 0, 0, 0, -2, 0, 0, -1, 0, 0]
numbfishes	-0.7	0.9	[0, -1, 0, -1, -1, 0, -1, 0, 0, -3]
numbing	-1.1	0.83066	[-1, 0, -1, -1, -2, -1, -3, -1, 0, -1]
numbingly	-1.3	0.45826	[-1, -1, -2, -1, -1, -1, -2, -1, -2, -1]
numbles	0.4	0.66332	[0, 0, 0, 0, 1, 2, 0, 0, 1, 0]
numbly	-1.4	1.0198	[-3, -2, 0, -1, -1, -3, -1, -2, -1, 0]
numbness	-1.1	0.7	[-1, -1, -2, -1, -1, -2, -1, 0, -2, 0]
numbs	-0.7	1.00499	[-1, 0, -1, 0, 1, 0, -3, -1, -1, -1]
numbskull	-2.3	1.41774	[-2, -4, -3, 0, -2, -2, -4, -4, 0, -2]
numbskulls	-2.2	1.07703	[-2, -2, -4, -2, -2, -1, -3, -3, 0, -3]
nurtural	1.5	0.80623	[2, 1, 2, 2, 1, 3, 0, 1, 2, 1]
nurturance	1.6	0.8	[1, 2, 1, 1, 3, 0, 2, 2, 2, 2]
nurturances	1.3	1.55242	[0, -2, 3, 1, 1, 2, 3, 0, 2, 3]
nurturant	1.7	0.78102	[2, 1, 3, 2, 2, 2, 2, 1, 0, 2]
nurture	1.4	0.8	[3, 1, 2, 2, 1, 2, 1, 0, 1, 1]
nurtured	1.9	0.9434	[2, 1, 3, 3, 3, 1, 2, 2, 0, 2]
nurturer	1.9	0.83066	[2, 1, 3, 3, 3, 1, 2, 2, 1, 1]
nurturers	0.8	1.66132	[2, -1, 2, 2, 1, -2, -2, 2, 2, 2]
nurtures	1.9	0.83066	[2, 1, 3, 3, 3, 1, 2, 2, 1, 1]
nurturing	2.0	0.63246	[3, 3, 2, 2, 1, 2, 2, 1, 2, 2]
nuts	-1.3	1.26886	[-2, -1, -2, -3, 1, -1, 1, -2, -2, -2]
o.o	-0.8	1.07703	[-1, -1, 0, -2, -1, -1, -2, 1, -2, 1]
o/\o	2.1	1.04403	[1, 2, 3, 1, 3, 4, 1, 2, 3, 1]
o_0	-0.1	0.53852	[-1, -1, 0, 0, 0, 0, 0, 0, 1, 0]
obliterate	-2.9	0.83066	[-3, -4, -3, -3, -3, -3, -2, -1, -4, -3]
obliterated	-2.1	1.3	[-3, 0, -1, -4, -3, -2, 0, -2, -3, -3]
obnoxious	-2.0	0.44721	[-1, -2, -3, -2, -2, -2, -2, -2, -2, -2]
obnoxiously	-2.3	0.64031	[-3, -2, -1, -2, -2, -3, -2, -3, -3, -2]
obnoxiousness	-2.1	0.7	[-3, -1, -1, -2, -2, -3, -2, -2, -3, -2]
obscene	-2.8	0.87178	[-3, -3, -2, -1, -3, -4, -3, -4, -2, -3]
obsess	-1.0	0.89443	[-2, 0, -2, -1, 1, -1, -2, -1, -1, -1]
obsessed	-0.7	0.78102	[0, 0, -1, 0, -1, 0, -1, -2, -2, 0]
obsesses	-1.0	0.7746	[-2, -2, -1, -2, 0, -1, -1, -1, 0, 0]
obsessing	-1.4	0.66332	[-1, -1, -1, -2, -1, -1, -1, -3, -2, -1]
obsession	-1.4	0.8	[0, -2, -2, -1, -1, -1, -3, -1, -1, -2]
obsessional	-1.5	0.92195	[0, -2, -1, -1, -3, -1, -2, -1, -3, -1]
obsessionally	-1.3	0.9	[-1, -2, -2, -2, -2, -1, 1, -1, -1, -2]
obsessions	-0.9	1.44568	[-1, -4, -2, -1, 1, -1, 0, 1, 0, -2]
obsessive	-0.9	1.04403	[-2, 0, -1, 0, 1, -1, -3, -1, -1, -1]
obsessively	-0.4	1.2	[-1, -1, -1, -1, -1, 1, 2, -2, 1, -1]
obsessiveness	-1.2	1.32665	[0, -3, -1, -1, -1, 1, -4, -1, -1, -1]
obsessives	-0.7	0.9	[-1, -1, -1, -1, -1, 1, -2, 1, -1, -1]
obsolete	-1.2	0.74833	[-1, -1, -1, -1, -2, -1, -1, -3, -1, 0]
obstacle	-1.5	1.0247	[-1, -3, -1, -2, -2, -2, 1, -1, -2, -2]
obstacles	-1.6	0.8	[-3, -1, -2, -1, -2, -2, 0, -2, -1, -2]
obstinate	-1.2	0.74833	[-2, -1, -1, -1, 0, 0, -2, -1, -2, -2]
odd	-1.3	0.45826	[-1, -1, -2, -1, -1, -1, -2, -1, -2, -1]
offence	-1.2	1.93907	[-2, -2, -3, 3, -2, -2, -3, 2, -1, -2]
offences	-1.4	1.49666	[-2, -2, -4, -1, -2, -2, -1, 0, 2, -2]
offend	-1.2	1.4	[-2, -2, -2, -2, -1, -2, -2, 1, 2, -2]
offended	-1.0	1.41421	[-2, -2, -2, -2, -1, 0, -2, 2, 1, -2]
offender	-1.5	1.28452	[-3, -2, -2, -2, -2, -2, -2, 1, 1, -2]
offenders	-1.5	1.28452	[-2, -1, -2, -2, -2, -2, 2, -3, -2, -1]
offending	-2.3	0.64031	[-2, -2, -3, -2, -2, -4, -2, -2, -2, -2]
offends	-2.0	0.7746	[-3, -1, -1, -2, -2, -2, -3, -1, -2, -3]
offense	-1.0	1.61245	[-2, 1, -1, -2, -2, -2, -3, 2, 1, -2]
offenseless	0.7	1.79165	[3, 1, 0, 2, 1, 2, 1, 0, -4, 1]
offenses	-1.5	1.5	[-1, -2, -2, -4, -1, -2, 1, -3, 1, -2]
offensive	-2.0	1.48324	[-3, -3, -3, -2, -1, -2, 2, -2, -3, -3]
offensively	-2.8	0.87178	[-2, -4, -3, -3, -2, -4, -4, -2, -2, -2]
offensiveness	-2.3	0.45826	[-2, -2, -2, -2, -2, -3, -2, -3, -3, -2]
offensives	-0.8	1.249	[0, 0, -1, -2, 0, 2, -2, -1, -2, -2]
offline	-0.5	0.92195	[0, 0, 0, 0, -3, 0, -1, 0, -1, 0]
ok	1.2	0.4	[1, 2, 1, 1, 1, 1, 2, 1, 1, 1]
okay	0.9	0.53852	[1, 1, 0, 0, 1, 1, 1, 2, 1, 1]
okays	2.1	1.13578	[1, 1, 1, 4, 3, 2, 2, 1, 2, 4]
ominous	-1.4	1.49666	[-3, -2, -1, -2, -2, -1, -1, 1, 1, -4]
once-in-a-lifetime	1.8	1.4	[4, 2, 1, 0, 1, 1, 4, 3, 2, 0]
openness	1.4	0.8	[2, 1, 1, 2, 2, 1, 1, 1, 3, 0]
opportune	1.7	0.78102	[2, 2, 0, 1, 2, 3, 2, 2, 1, 2]
opportunely	1.5	1.0247	[1, 1, 4, 1, 2, 1, 1, 2, 2, 0]
opportuneness	1.2	1.249	[0, 1, 2, 2, 2, 2, 2, 2, -2, 1]
opportunism	0.4	1.11355	[-1, -1, 0, -1, 1, 2, 0, 1, 2, 1]
opportunisms	0.2	1.4	[2, -1, 0, -1, 1, -2, 2, 2, -1, 0]
opportunist	0.2	0.9798	[-1, -1, 0, -1, 0, 2, 0, 1, 1, 1]
opportunistic	-0.1	2.11896	[-2, 1, -1, 0, -4, 2, -1, 4, 1, -1]
opportunistically	0.9	1.51327	[1, -3, 1, 3, 2, 2, 1, 0, 1, 1]
opportunists	0.3	1.34536	[0, -1, -1, 2, 1, 3, -1, -1, 1, 0]
opportunities	1.6	0.4899	[1, 1, 2, 2, 2, 1, 2, 2, 1, 2]
opportunity	1.8	0.6	[2, 2, 2, 2, 3, 1, 2, 1, 1, 2]
oppressed	-2.1	0.53852	[-2, -2, -2, -1, -2, -2, -3, -2, -2, -3]
oppressive	-1.7	1.34536	[-3, -2, -1, -2, -2, -2, -3, 2, -2, -2]
optimal	1.5	0.67082	[1, 2, 1, 3, 1, 1, 1, 2, 2, 1]
optimality	1.9	0.7	[3, 3, 2, 1, 2, 1, 2, 2, 1, 2]
optimally	1.3	0.9	[0, 3, 0, 1, 1, 2, 1, 1, 2, 2]
optimisation	1.6	0.8	[3, 1, 1, 1, 2, 1, 1, 3, 2, 1]
optimisations	1.8	0.6	[3, 1, 1, 2, 1, 2, 2, 2, 2, 2]
optimise	1.9	0.83066	[1, 2, 3, 2, 3, 3, 2, 1, 1, 1]
optimised	1.7	1.26886	[2, 4, 1, 2, 1, 2, 3, 2, 1, -1]
optimises	1.6	1.0198	[3, 1, 1, 1, 1, 3, 2, 0, 3, 1]
optimising	1.7	1.00499	[1, 2, 3, 3, 1, 2, 1, 3, 0, 1]
optimism	2.5	0.67082	[2, 2, 3, 3, 3, 4, 2, 2, 2, 2]
optimisms	2.0	0.63246	[2, 3, 1, 3, 1, 2, 2, 2, 2, 2]
optimist	2.4	0.4899	[3, 2, 3, 2, 3, 3, 2, 2, 2, 2]
optimistic	1.3	1.48661	[2, 2, -3, 2, 2, 2, 1, 2, 1, 2]
optimistically	2.1	0.53852	[3, 2, 2, 2, 3, 2, 1, 2, 2, 2]
optimists	1.6	0.66332	[3, 2, 2, 1, 2, 1, 2, 1, 1, 1]
optimization	1.6	0.8	[2, 3, 1, 1, 1, 2, 0, 2, 2, 2]
optimizations	0.9	1.04403	[0, 2, 0, 2, 1, 0, 3, 1, 0, 0]
optimize	2.2	0.87178	[2, 3, 3, 2, 1, 2, 4, 2, 2, 1]
optimized	2.0	0.44721	[1, 3, 2, 2, 2, 2, 2, 2, 2, 2]
optimizer	1.5	0.67082	[1, 3, 1, 1, 2, 2, 1, 2, 1, 1]
optimizers	2.1	0.7	[3, 3, 1, 3, 2, 2, 1, 2, 2, 2]
optimizes	1.8	0.6	[1, 3, 2, 1, 2, 2, 1, 2, 2, 2]
optimizing	2.0	0.7746	[1, 1, 2, 2, 1, 3, 3, 3, 2, 2]
optionless	-1.7	0.64031	[-2, -1, -2, -3, -1, -2, -2, -2, -1, -1]
original	1.3	0.9	[0, 2, 0, 2, 2, 0, 2, 1, 2, 2]
outcry	-2.3	0.64031	[-3, -2, -2, -3, -2, -1, -3, -2, -2, -3]
outgoing	1.2	1.16619	[1, 1, 1, 2, -1, 1, 3, 3, 0, 1]
outmaneuvered	0.5	1.36015	[-2, 2, 0, 0, 3, 1, 1, 0, 1, -1]
outrage	-2.3	1.00499	[-2, -2, -4, -3, -1, -3, -3, -1, -1, -3]
outraged	-2.5	0.92195	[-3, -2, -2, -1, -3, -3, -1, -4, -3, -3]
outrageous	-2.0	1.34164	[-3, 0, -2, -1, -3, 0, -3, -3, -4, -1]
outrageously	-1.2	1.32665	[-1, -1, -3, -3, -2, -1, 2, -1, -1, -1]
outrageousness	-1.2	1.249	[-2, 0, 1, -2, -1, -1, -3, 0, -1, -3]
outrageousnesses	-1.3	1.67631	[-1, -3, -2, 1, 0, 1, 0, -3, -2, -4]
outrages	-2.3	1.00499	[-3, -2, -2, -1, -3, -3, -1, -4, -1, -3]
outraging	-2.0	1.18322	[-4, -1, -2, -3, -1, 0, -1, -3, -3, -2]
outreach	1.1	0.7	[2, 1, 0, 0, 2, 1, 1, 1, 2, 1]
outstanding	3.0	0.89443	[3, 1, 3, 3, 4, 4, 2, 3, 4, 3]
overjoyed	2.7	0.78102	[4, 3, 3, 4, 2, 2, 2, 2, 3, 2]
overload	-1.5	0.67082	[-2, 0, -1, -2, -2, -1, -1, -2, -2, -2]
overlooked	-0.1	1.44568	[-1, -2, -1, -1, 2, 0, -1, 2, -1, 2]
overreact	-1.0	1.73205	[-2, -2, -2, -2, 0, -3, 1, 3, -1, -2]
overreacted	-1.7	0.64031	[-2, -2, -2, -1, -1, -1, -1, -3, -2, -2]
overreaction	-0.7	1.34536	[-2, -1, 0, -1, -1, 3, -2, -1, -1, -1]
overreacts	-2.2	0.87178	[-2, -2, -2, -3, -4, -1, -3, -1, -2, -2]
oversell	-0.9	0.7	[0, -1, -1, 0, -2, -1, -1, -1, -2, 0]
overselling	-0.8	1.16619	[0, -1, 0, -1, -2, -1, -1, -2, -2, 2]
oversells	0.3	1.26886	[-1, -1, 0, 0, 2, 2, 2, -1, 1, -1]
oversimplification	0.2	1.53623	[-1, 1, -1, -1, 1, -1, -2, 1, 2, 3]
oversimplifies	0.1	1.37477	[3, 0, 0, -1, 1, -1, 2, -1, -1, -1]
oversimplify	-0.6	1.35647	[-3, -1, 0, -2, 0, 1, 2, -1, -1, -1]
overstatement	-1.1	0.7	[-2, 0, -1, -2, -1, 0, -1, -1, -2, -1]
overstatements	-0.7	1.34536	[-1, -1, -3, -1, 2, 0, -1, -1, -2, 1]
overweight	-1.5	0.67082	[-3, -2, -1, -1, -1, -2, -1, -2, -1, -1]
overwhelm	-0.7	1.26886	[-1, 2, -1, -1, -2, -2, 1, 0, -1, -2]
overwhelmed	0.2	1.53623	[-2, -1, 2, -2, 2, 1, -1, 2, 1, 0]
overwhelmingly	-0.5	1.28452	[0, -2, -1, 0, -2, -1, -2, 0, 1, 2]
overwhelms	-0.8	1.249	[-2, -2, -1, -1, -1, -2, 2, 1, -1, -1]
oxymoron	-0.5	0.80623	[0, 0, -1, -1, -1, -2, 1, 0, 0, -1]
pain	-2.3	0.64031	[-2, -3, -2, -2, -2, -2, -2, -2, -2, -4]
pained	-1.8	0.6	[-1, -2, -3, -2, -2, -2, -2, -1, -1, -2]
painful	-1.9	0.9434	[-2, -1, -1, -3, -4, -1, -1, -2, -2, -2]
painfuller	-1.7	1.34536	[-2, -2, -2, 2, -1, -3, -2, -2, -3, -2]
painfully	-2.4	0.4899	[-2, -3, -2, -3, -3, -2, -2, -2, -2, -3]
painfulness	-2.7	0.64031	[-3, -4, -3, -2, -3, -3, -3, -2, -2, -2]
paining	-1.7	0.45826	[-1, -2, -2, -2, -2, -2, -1, -2, -1, -2]
painless	1.2	0.87178	[1, 2, -1, 1, 2, 1, 2, 1, 2, 1]
painlessly	1.1	0.3	[1, 1, 1, 1, 1, 1, 1, 1, 1, 2]
painlessness	0.4	1.0198	[1, 1, 1, 1, -1, 1, -2, 0, 1, 1]
pains	-1.8	0.6	[-2, -2, -1, -1, -1, -2, -2, -2, -3, -2]
palatable	1.6	0.8	[2, 1, 2, 1, 2, 1, 3, 0, 2, 2]
palatableness	0.8	0.87178	[2, 1, 1, 0, -1, 2, 0, 1, 1, 1]
palatably	1.1	0.83066	[2, 1, 2, 1, 1, 1, 2, -1, 1, 1]
panic	-2.3	0.64031	[-3, -2, -1, -2, -2, -2, -3, -3, -2, -3]
panicked	-2.0	1.61245	[-2, -4, -3, -3, -3, 1, -2, -2, 1, -3]
panicking	-1.9	0.53852	[-2, -2, -3, -2, -2, -2, -2, -2, -1, -1]
panicky	-1.5	0.67082	[-2, -2, -1, -1, -1, -1, -1, -2, -3, -1]
panicle	0.5	0.67082	[2, 0, 0, 0, 0, 0, 0, 1, 1, 1]
panicled	0.1	0.83066	[2, 0, 0, 0, 0, -1, 0, -1, 1, 0]
panicles	-0.2	0.6	[1, 0, 0, 0, 0, -1, 0, -1, -1, 0]
panics	-1.9	1.3	[-2, -3, -2, -3, -1, -2, 1, -4, -1, -2]
paniculate	0.1	0.3	[0, 0, 0, 1, 0, 0, 0, 0, 0, 0]
panicums	-0.1	0.3	[0, 0, 0, 0, -1, 0, 0, 0, 0, 0]
paradise	3.2	0.9798	[4, 1, 4, 2, 4, 4, 3, 3, 3, 4]
paradox	-0.4	0.66332	[-1, -1, 0, 0, -1, 0, -1, 0, 1, -1]
paranoia	-1.0	1.48324	[1, -2, -2, -3, -1, 0, -2, -1, -2, 2]
paranoiac	-1.3	1.61555	[-2, -3, -2, -2, 1, -3, 2, -2, 0, -2]
paranoiacs	-0.7	1.18743	[-1, 0, -1, -1, -3, 1, -2, 0, 1, -1]
paranoias	-1.5	1.0247	[-2, -3, -2, 0, -1, -3, 0, -1, -1, -2]
paranoid	-1.0	1.41421	[0, -3, -3, -2, -1, -1, 0, 1, -2, 1]
paranoids	-1.6	0.91652	[-2, -1, -1, -2, -2, -1, -1, -1, -4, -1]
pardon	1.3	0.45826	[1, 2, 1, 2, 1, 2, 1, 1, 1, 1]
pardoned	0.9	0.9434	[1, 1, 1, 1, 1, 1, 1, 0, 3, -1]
pardoning	1.7	0.78102	[3, 2, 2, 2, 1, 1, 2, 2, 0, 2]
pardons	1.2	0.6	[2, 1, 1, 1, 1, 2, 1, 2, 0, 1]
parley	-0.4	0.66332	[0, -2, 0, 0, -1, -1, 0, 0, 0, 0]
partied	1.4	1.11355	[2, 3, 1, 1, 1, 2, 3, -1, 1, 1]
partier	1.4	0.8	[2, 2, 1, 1, 0, 2, 3, 1, 1, 1]
partiers	0.7	1.00499	[0, 0, 3, 0, 0, 0, 2, 1, 0, 1]
parties	1.7	0.78102	[2, 3, 1, 1, 1, 2, 3, 1, 2, 1]
party	1.7	0.78102	[3, 2, 2, 1, 3, 2, 1, 1, 1, 1]
partyer	1.2	1.07703	[1, 3, 1, 0, 1, 0, 2, 1, 3, 0]
partyers	1.1	0.83066	[1, 2, 0, 3, 1, 1, 1, 0, 1, 1]
partying	1.6	1.11355	[0, 3, 1, 0, 3, 2, 3, 1, 2, 1]
passion	2.0	0.44721	[2, 2, 1, 3, 2, 2, 2, 2, 2, 2]
passional	1.6	0.8	[2, 1, 3, 0, 2, 1, 1, 2, 2, 2]
passionate	2.4	1.35647	[0, 4, 3, 3, 4, 2, 0, 2, 3, 3]
passionately	2.4	0.91652	[3, 0, 3, 3, 2, 3, 2, 3, 2, 3]
passionateness	2.3	0.64031	[1, 3, 2, 3, 2, 3, 2, 3, 2, 2]
passionflower	0.3	0.45826	[0, 0, 0, 0, 1, 0, 1, 1, 0, 0]
passionflowers	0.4	0.66332	[0, 0, 0, 0, 2, 1, 1, 0, 0, 0]
passionless	-1.9	0.7	[-3, -2, -2, -2, -1, -1, -1, -3, -2, -2]
passions	2.2	0.6	[3, 3, 3, 2, 2, 1, 2, 2, 2, 2]
passive	0.8	1.46969	[4, 1, 1, 0, -2, 0, 1, 1, 2, 0]
passively	-0.7	0.64031	[0, -1, 0, 0, -1, -2, 0, -1, -1, -1]
pathetic	-2.7	1.48661	[-3, -4, -3, -4, -2, 1, -2, -2, -4, -4]
pathetical	-1.2	1.249	[-2, -1, -2, -1, -2, 1, -3, 1, -1, -2]
pathetically	-1.8	1.72047	[-3, -4, -2, 0, -3, -2, -3, -3, 1, 1]
pay	-0.4	0.91652	[0, 0, -3, 0, 0, 0, 0, 0, -1, 0]
peace	2.5	1.0247	[3, 2, 1, 4, 2, 3, 4, 1, 3, 2]
peaceable	1.7	0.45826	[2, 2, 1, 2, 1, 2, 2, 1, 2, 2]
peaceableness	1.8	1.16619	[2, 2, 4, 2, 2, 2, 2, 1, 2, -1]
peaceably	2.0	0.63246	[2, 3, 2, 2, 1, 2, 3, 1, 2, 2]
peaceful	2.2	0.74833	[4, 2, 1, 2, 2, 2, 3, 2, 2, 2]
peacefuller	1.9	0.7	[2, 2, 2, 1, 3, 3, 2, 2, 1, 1]
peacefullest	3.1	0.7	[3, 3, 2, 3, 4, 2, 4, 3, 4, 3]
peacefully	2.4	0.66332	[3, 2, 2, 2, 4, 2, 2, 3, 2, 2]
peacefulness	2.1	0.83066	[3, 2, 1, 2, 3, 1, 3, 3, 1, 2]
peacekeeper	1.6	1.11355	[1, 1, 0, 2, 1, 1, 4, 3, 2, 1]
peacekeepers	1.6	1.11355	[4, 1, 1, 2, 1, 1, 2, 3, 0, 1]
peacekeeping	2.0	0.63246	[2, 1, 1, 2, 3, 3, 2, 2, 2, 2]
peacekeepings	1.6	0.8	[0, 1, 1, 2, 2, 3, 2, 2, 2, 1]
peacemaker	2.0	0.89443	[2, 1, 2, 4, 2, 2, 1, 3, 1, 2]
peacemakers	2.4	1.0198	[0, 3, 4, 2, 3, 2, 3, 3, 2, 2]
peacemaking	1.7	0.78102	[1, 1, 1, 3, 3, 1, 2, 2, 2, 1]
peacenik	0.8	0.87178	[1, 1, 0, 1, 2, 0, 1, 1, -1, 2]
peaceniks	0.7	1.00499	[2, 0, 0, 1, 0, 2, 1, -1, 2, 0]
peaces	2.1	0.83066	[2, 2, 2, 2, 3, 0, 3, 3, 2, 2]
peacetime	2.2	1.16619	[3, 1, 4, 2, 4, 3, 1, 1, 2, 1]
peacetimes	2.1	0.83066	[3, 2, 2, 4, 1, 2, 2, 2, 1, 2]
peculiar	0.6	1.2	[-1, 0, -1, 1, 2, -1, 2, 1, 2, 1]
peculiarities	0.1	1.37477	[-1, -1, 0, -1, -1, 1, 3, 2, -1, 0]
peculiarity	0.6	1.2	[-1, 1, -1, 0, 2, -1, 1, 2, 2, 1]
peculiarly	-0.4	1.2	[-1, 2, -2, -1, 0, 0, -2, 1, -1, 0]
penalty	-2.0	0.63246	[-2, -3, -2, -2, -1, -2, -3, -2, -2, -1]
pensive	0.3	1.1	[1, 0, 0, 1, 0, -1, 3, 0, -1, 0]
perfect	2.7	0.78102	[2, 4, 2, 3, 4, 2, 3, 2, 3, 2]
perfecta	1.4	1.42829	[1, 0, 0, 3, 1, 0, 0, 4, 3, 2]
perfectas	0.6	1.11355	[0, 0, -1, 1, 0, 2, 3, 0, 1, 0]
perfected	2.7	0.78102	[1, 3, 3, 4, 2, 3, 3, 2, 3, 3]
perfecter	1.8	0.9798	[2, 1, 3, 1, 2, 1, 2, 4, 1, 1]
perfecters	1.4	1.11355	[2, 1, 3, 0, 0, 3, 2, 0, 1, 2]
perfectest	3.1	1.04403	[2, 4, 4, 4, 3, 2, 1, 3, 4, 4]
perfectibilities	2.1	1.04403	[3, 2, 2, 3, 4, 1, 2, 2, 0, 2]
perfectibility	1.8	1.249	[4, 3, 3, 0, 1, 0, 1, 2, 2, 2]
perfectible	1.5	0.67082	[1, 2, 1, 1, 2, 1, 1, 3, 2, 1]
perfecting	2.3	0.9	[1, 2, 3, 3, 1, 2, 2, 4, 2, 3]
perfection	2.7	1.1	[3, 3, 3, 1, 2, 4, 4, 1, 2, 4]
perfectionism	1.3	1.26886	[3, -1, 2, 1, 2, 2, 2, -1, 1, 2]
perfectionist	1.5	1.20416	[3, -1, 3, 1, 2, 2, 2, 0, 1, 2]
perfectionistic	0.7	1.67631	[-1, 0, 2, 0, 1, 0, 3, -1, 4, -1]
perfectionists	0.1	1.22066	[1, -1, 1, -1, 0, 0, -1, 0, 3, -1]
perfections	2.5	1.43178	[2, 4, 4, 3, 3, 4, -1, 2, 2, 2]
perfective	1.2	0.87178	[1, 0, 1, 1, 3, 2, 2, 0, 1, 1]
perfectively	2.1	1.13578	[3, 3, 1, 4, 0, 2, 1, 2, 2, 3]
perfectiveness	0.9	1.51327	[-2, 3, -1, 0, 2, 0, 2, 2, 2, 1]
perfectives	0.9	0.83066	[0, 2, 1, 1, 1, 0, 2, 0, 2, 0]
perfectivity	2.2	0.9798	[3, 2, 0, 3, 1, 2, 3, 3, 2, 3]
perfectly	3.2	0.4	[3, 4, 4, 3, 3, 3, 3, 3, 3, 3]
perfectness	3.0	0.63246	[4, 3, 3, 3, 3, 2, 4, 2, 3, 3]
perfecto	1.3	1.34536	[1, 0, 0, 2, 0, 0, 1, 4, 3, 2]
perfects	1.6	1.11355	[1, 1, 1, 2, 1, 0, 1, 4, 3, 2]
peril	-1.7	1.67631	[-3, -2, -2, 3, -3, -2, -3, -2, -1, -2]
perjury	-1.9	0.9434	[-3, -2, -3, -2, -2, 0, -1, -2, -3, -1]
perpetrator	-2.2	0.74833	[-2, -3, -2, -2, -2, -2, -4, -2, -1, -2]
perpetrators	-1.0	1.67332	[-1, -2, -2, -2, -2, 2, 0, -4, 1, 0]
perplexed	-1.3	0.64031	[-1, -1, -2, -1, -1, -1, 0, -2, -2, -2]
persecute	-2.1	1.37477	[-2, -3, -2, 1, -1, -4, -2, -2, -2, -4]
persecuted	-1.3	1.61555	[-2, -1, -2, -1, -2, 2, -4, 1, -2, -2]
persecutes	-1.2	1.4	[-2, -1, -2, 0, -2, 1, -3, 1, -1, -3]
persecuting	-1.5	1.62788	[-3, -2, -2, 0, -4, -3, -2, -1, 1, 1]
perturbed	-1.4	0.8	[-2, -1, -1, 0, -2, -2, -2, 0, -2, -2]
perverse	-1.8	1.8868	[-2, -4, -3, 1, -2, -4, 1, 0, -1, -4]
perversely	-2.2	0.87178	[-1, -2, -2, -2, -1, -3, -2, -3, -4, -2]
perverseness	-2.1	1.22066	[-3, -3, 1, -2, -3, -2, -3, -2, -1, -3]
perversenesses	-0.5	1.74642	[-2, 3, -1, -2, 1, 0, 1, 0, -2, -3]
perversion	-1.3	1.34536	[-3, -2, -2, 1, 1, -3, -1, -2, -1, -1]
perversions	-1.2	1.83303	[-2, 0, -4, -2, -3, -3, 0, 1, -1, 2]
perversities	-1.1	1.22066	[-2, -1, -2, 0, -2, -2, -2, 1, 1, -2]
perversity	-2.6	0.8	[-2, -3, -2, -2, -4, -3, -3, -1, -3, -3]
perversive	-2.1	0.7	[-3, -1, -3, -2, -2, -2, -2, -1, -3, -2]
pervert	-2.3	0.9	[-2, -2, -1, -3, -4, -2, -3, -1, -3, -2]
perverted	-2.5	1.56525	[-2, -4, -2, -4, -3, -4, 1, -2, -1, -4]
pervertedly	-1.2	1.77764	[-3, -2, -1, -2, 2, -3, 1, -3, 1, -2]
pervertedness	-1.2	1.46969	[1, -2, -3, -2, 0, -2, -3, 1, 0, -2]
perverter	-1.7	1.48661	[-3, -4, -2, -2, -1, -1, 2, -2, -2, -2]
perverters	-0.6	1.68523	[-2, -2, -1, 0, -1, 2, -3, 1, 2, -2]
perverting	-1.0	2.09762	[4, -2, -2, -4, -2, 1, 0, -1, -2, -2]
perverts	-2.8	0.6	[-3, -3, -2, -3, -3, -2, -2, -4, -3, -3]
pesky	-1.2	0.4	[-1, -2, -1, -1, -1, -1, -2, -1, -1, -1]
pessimism	-1.5	1.36015	[-2, -3, 1, -2, -2, -1, 1, -3, -2, -2]
pessimisms	-2.0	1.0	[-3, -1, -2, -2, -3, -1, 0, -2, -3, -3]
pessimist	-1.5	1.36015	[-1, -3, 1, -2, -2, -2, 1, -3, -2, -2]
pessimistic	-1.5	1.43178	[-3, -1, -1, 1, -3, -3, -2, -2, -2, 1]
pessimistically	-2.0	1.0	[-1, -1, -3, -4, -2, -1, -1, -3, -2, -2]
pessimists	-1.0	1.26491	[-1, 1, -1, 0, -2, -2, -2, -3, -1, 1]
petrifaction	-1.9	1.3	[-3, -2, -1, -4, -3, 0, -1, 0, -2, -3]
petrifactions	-0.3	1.00499	[0, -1, 0, 0, 0, 0, 0, 0, -3, 1]
petrification	-0.1	1.44568	[-1, 2, -1, -1, 0, 2, 2, -2, -1, -1]
petrifications	-0.4	0.8	[0, 0, -2, 0, 0, 0, 0, 0, -2, 0]
petrified	-2.5	0.92195	[-4, -3, -2, -1, -2, -3, -2, -2, -2, -4]
petrifies	-2.3	1.00499	[-4, -3, -2, -1, -2, -2, -1, -2, -2, -4]
petrify	-1.7	0.9	[-2, -3, -1, -2, -1, -1, -2, -3, 0, -2]
petrifying	-2.6	0.8	[-2, -3, -3, -2, -4, -2, -3, -1, -3, -3]
pettier	-0.3	1.41774	[-1, -1, -1, -1, 2, 0, 2, -2, 1, -2]
pettiest	-1.3	1.95192	[1, -3, -3, -3, -2, -1, 2, -1, 1, -4]
petty	-0.8	1.32665	[1, -3, -2, -1, -1, -1, -1, -1, 2, -1]
phobia	-1.6	1.0198	[-2, -2, 1, -2, -3, -1, -1, -2, -2, -2]
phobias	-2.0	1.0	[-3, -2, -2, -3, -3, 0, -1, -2, -1, -3]
phobic	-1.2	1.16619	[-2, -2, 1, -1, -2, -1, 1, -2, -2, -2]
phobics	-1.3	0.64031	[-1, -1, -1, -2, -2, -1, 0, -2, -2, -1]
picturesque	1.6	1.11355	[4, 1, 1, 2, 1, 2, 1, 0, 3, 1]
pileup	-1.1	1.13578	[-2, 1, 0, -1, 0, -1, -3, -2, -2, -1]
pique	-1.1	1.13578	[-2, -2, -2, 0, 0, -1, 1, -1, -1, -3]
piqued	0.1	1.04403	[0, -2, 0, 1, 1, 1, -1, 1, -1, 1]
piss	-1.7	0.9	[-2, -1, -1, -2, -1, -3, -3, 0, -2, -2]
pissant	-1.5	1.5	[-1, -3, -3, 1, -3, -1, -1, -2, 1, -3]
pissants	-2.5	0.80623	[-4, -3, -3, -2, -3, -1, -2, -3, -2, -2]
pissed	-3.2	0.6	[-3, -3, -4, -3, -2, -4, -4, -3, -3, -3]
pisser	-2.0	1.09545	[-2, -4, -1, -3, -2, -3, -1, -2, -2, 0]
pissers	-1.4	2.00998	[-1, -1, -4, -2, -3, 4, -2, -1, -2, -2]
pisses	-1.4	0.8	[-2, -2, -1, -3, -1, -1, -1, -2, -1, 0]
pissing	-1.7	1.26886	[0, 0, -2, -2, -3, 0, -3, -1, -3, -3]
pissoir	-0.8	1.4	[-2, 0, 0, -2, -1, -3, 0, -2, 0, 2]
piteous	-1.2	1.46969	[-2, -1, -2, -2, -2, -1, 3, -1, -2, -2]
pitiable	-1.1	1.22066	[-1, 0, -1, -1, 1, -2, -4, -1, -1, -1]
pitiableness	-1.1	1.64012	[-2, -1, -1, -2, -4, 2, 1, 0, -2, -2]
pitiably	-1.1	0.9434	[-1, 0, 0, -2, 0, -2, -3, -1, -1, -1]
pitied	-1.3	1.1	[-2, -1, -3, -1, 1, 0, -2, -2, -1, -2]
pitier	-1.2	1.32665	[-3, -1, -2, -3, -1, -1, 1, 1, -2, -1]
pitiers	-1.3	0.9	[0, -1, -2, -2, -1, -1, -1, -3, -2, 0]
pities	-1.2	1.249	[-2, -1, -2, -3, -1, -1, 1, 1, -2, -2]
pitiful	-2.2	0.9798	[-3, -2, -1, -3, -2, -2, -3, -3, -3, 0]
pitifuller	-1.8	1.07703	[-1, -1, -2, -3, -4, 0, -2, -2, -1, -2]
pitifullest	-1.1	2.11896	[-2, 1, -1, -4, -4, -1, -3, -1, 2, 2]
pitifully	-1.2	1.249	[-2, -1, -3, -2, -1, -1, -2, -1, 2, -1]
pitifulness	-1.2	1.77764	[-3, -2, -1, -3, -2, -1, 3, 1, -2, -2]
pitiless	-1.8	0.87178	[-2, 0, -2, -2, -2, -3, -1, -3, -1, -2]
pitilessly	-2.1	0.7	[-2, -2, -1, -3, -3, -1, -3, -2, -2, -2]
pitilessness	-0.5	1.62788	[1, 3, 1, -3, -2, -1, -1, -1, -1, -1]
pity	-1.2	0.4	[-2, -2, -1, -1, -1, -1, -1, -1, -1, -1]
pitying	-1.4	0.91652	[-1, -3, -1, -3, -2, -1, 0, -1, -1, -1]
pityingly	-1.0	1.26491	[-2, -2, -1, -2, -2, 0, 0, -1, 2, -2]
pityriasis	-0.8	0.87178	[-2, 0, 0, 0, -1, 0, -2, -1, -2, 0]
play	1.4	1.0198	[2, 0, 1, 1, 1, 2, 1, 4, 1, 1]
played	1.4	1.42829	[2, 1, 1, 1, 4, 0, 1, 0, 4, 0]
playful	1.9	0.83066	[4, 2, 2, 2, 1, 1, 1, 2, 2, 2]
playfully	1.6	0.4899	[2, 1, 1, 2, 1, 2, 2, 2, 1, 2]
playfulness	1.2	0.87178	[2, 1, 2, -1, 1, 2, 1, 2, 1, 1]
playing	0.8	0.87178	[0, 2, 2, 1, 0, 0, 2, 0, 0, 1]
plays	1.0	1.09545	[0, 2, 0, 2, 0, 1, 0, 2, 3, 0]
pleasant	2.3	0.64031	[2, 3, 3, 3, 2, 1, 2, 2, 3, 2]
pleasanter	1.5	0.67082	[2, 2, 2, 1, 0, 1, 1, 2, 2, 2]
pleasantest	2.6	0.8	[3, 3, 3, 1, 2, 4, 3, 2, 2, 3]
pleasantly	2.1	0.53852	[1, 2, 3, 2, 2, 2, 2, 2, 3, 2]
pleasantness	2.3	0.9	[3, 2, 3, 3, 3, 1, 1, 3, 1, 3]
pleasantnesses	2.3	0.78102	[3, 1, 2, 3, 1, 2, 3, 3, 2, 3]
pleasantries	1.3	0.45826	[1, 1, 1, 1, 1, 2, 1, 2, 2, 1]
pleasantry	2.0	0.7746	[2, 2, 1, 1, 4, 2, 2, 2, 2, 2]
please	1.3	0.78102	[2, 1, 2, 2, 2, 0, 2, 1, 0, 1]
pleased	1.9	0.53852	[2, 2, 2, 2, 1, 1, 2, 3, 2, 2]
pleaser	1.7	0.45826	[2, 2, 2, 2, 1, 1, 1, 2, 2, 2]
pleasers	1.0	1.0	[1, 1, 2, 0, 1, 3, 1, 1, -1, 1]
pleases	1.7	0.45826	[2, 1, 2, 2, 1, 1, 2, 2, 2, 2]
pleasing	2.4	0.91652	[2, 3, 2, 2, 2, 2, 4, 4, 1, 2]
pleasurability	1.9	0.83066	[1, 2, 2, 2, 2, 0, 2, 3, 3, 2]
pleasurable	2.4	0.4899	[2, 3, 3, 2, 3, 3, 2, 2, 2, 2]
pleasurableness	2.4	0.91652	[2, 3, 2, 1, 2, 3, 4, 3, 1, 3]
pleasurably	2.6	0.4899	[2, 2, 2, 3, 3, 3, 3, 2, 3, 3]
pleasure	2.7	0.9	[4, 4, 3, 2, 2, 3, 2, 1, 3, 3]
pleasured	2.3	0.64031	[3, 2, 3, 2, 3, 3, 1, 2, 2, 2]
pleasureless	-1.6	0.8	[-1, -1, -1, -1, -2, -3, -3, -2, -1, -1]
pleasures	1.9	1.37477	[3, -2, 3, 2, 3, 2, 2, 2, 2, 2]
pleasuring	2.8	0.4	[3, 2, 3, 3, 3, 3, 3, 2, 3, 3]
poised	1.0	0.44721	[1, 1, 1, 1, 1, 1, 0, 1, 1, 2]
poison	-2.5	0.92195	[-4, -3, -2, -4, -2, -2, -2, -3, -1, -2]
poisoned	-2.2	0.9798	[-4, -1, -4, -1, -2, -2, -2, -2, -2, -2]
poisoner	-2.7	0.78102	[-2, -3, -3, -4, -4, -2, -2, -2, -2, -3]
poisoners	-3.1	0.83066	[-3, -4, -3, -4, -3, -4, -3, -3, -1, -3]
poisoning	-2.8	1.249	[-4, -4, -2, 0, -2, -3, -4, -3, -2, -4]
poisonings	-2.4	1.11355	[-2, 0, -2, -1, -4, -3, -3, -3, -3, -3]
poisonous	-2.7	0.78102	[-3, -3, -4, -2, -1, -3, -3, -3, -3, -2]
poisonously	-2.9	0.53852	[-3, -2, -3, -3, -2, -3, -3, -4, -3, -3]
poisons	-2.7	0.9	[-3, -4, -3, -1, -2, -3, -3, -2, -2, -4]
poisonwood	-1.0	0.89443	[0, -2, 0, 0, 0, -1, -2, -2, -2, -1]
pollute	-2.3	0.64031	[-3, -2, -2, -2, -2, -3, -3, -1, -3, -2]
polluted	-2.0	0.44721	[-2, -2, -1, -2, -2, -2, -2, -2, -3, -2]
polluter	-1.8	0.6	[-2, -1, -2, -1, -2, -2, -3, -1, -2, -2]
polluters	-2.0	0.44721	[-2, -2, -2, -2, -2, -3, -2, -1, -2, -2]
pollutes	-2.2	0.87178	[-2, -1, -2, -1, -3, -1, -3, -3, -3, -3]
poor	-2.1	1.13578	[-2, -2, -1, -4, -2, 0, -2, -4, -2, -2]
poorer	-1.5	1.56525	[-2, -3, -2, -2, -2, -2, 3, -2, -1, -2]
poorest	-2.5	0.80623	[-2, -3, -3, -1, -2, -3, -3, -4, -2, -2]
popular	1.8	0.74833	[2, 3, 1, 2, 2, 1, 3, 1, 2, 1]
popularise	1.6	0.66332	[1, 1, 2, 2, 3, 2, 1, 2, 1, 1]
popularised	1.1	0.9434	[1, 1, 0, 0, 3, 2, 1, 2, 1, 0]
popularises	0.5	0.67082	[1, 0, 0, 0, 0, 1, 1, 2, 0, 0]
popularising	1.2	0.6	[1, 0, 2, 2, 1, 1, 1, 2, 1, 1]
popularities	1.6	0.8	[2, 1, 1, 2, 0, 2, 1, 2, 3, 2]
popularity	2.1	1.04403	[2, 1, 1, 1, 3, 2, 3, 4, 3, 1]
popularization	1.3	0.78102	[1, 2, 1, 2, 1, 0, 1, 1, 3, 1]
popularizations	0.9	0.7	[1, 1, 1, 0, 2, 0, 1, 1, 2, 0]
popularize	1.3	0.64031	[2, 2, 0, 2, 1, 1, 1, 1, 2, 1]
popularized	1.9	0.83066	[3, 2, 3, 2, 1, 0, 2, 2, 2, 2]
popularizer	1.8	0.74833	[2, 2, 3, 2, 1, 0, 2, 2, 2, 2]
popularizers	1.0	0.89443	[0, 0, 1, 2, 1, 1, 1, 1, 3, 0]
popularizes	1.4	0.8	[2, 1, 3, 2, 1, 0, 1, 2, 1, 1]
popularizing	1.5	0.67082	[2, 2, 1, 1, 1, 2, 2, 0, 2, 2]
popularly	1.8	0.74833	[1, 3, 2, 1, 3, 1, 2, 1, 2, 2]
positive	2.6	0.91652	[2, 1, 2, 2, 3, 3, 4, 3, 4, 2]
positively	2.4	0.66332	[2, 2, 3, 3, 4, 2, 2, 2, 2, 2]
positiveness	2.3	1.18743	[2, 1, 4, 3, 4, 2, 0, 3, 2, 2]
positivenesses	2.2	0.74833	[3, 3, 2, 2, 2, 2, 1, 3, 1, 3]
positiver	2.3	0.78102	[1, 4, 2, 2, 2, 3, 2, 2, 2, 3]
positives	2.4	0.4899	[3, 3, 2, 2, 2, 2, 3, 2, 3, 2]
positivest	2.9	1.04403	[4, 3, 2, 1, 3, 4, 4, 4, 2, 2]
positivism	1.6	1.35647	[3, 0, 2, 1, 2, 0, 3, 4, 1, 0]
positivisms	1.8	0.9798	[4, 1, 2, 2, 2, 0, 2, 2, 1, 2]
positivist	2.0	1.0	[3, 1, 2, 2, 2, 0, 2, 4, 2, 2]
positivistic	1.9	0.83066	[2, 3, 1, 3, 1, 1, 3, 2, 1, 2]
positivists	1.7	1.1	[1, 2, 1, 4, 2, 1, 3, 0, 1, 2]
positivities	2.6	0.91652	[2, 2, 4, 3, 2, 3, 4, 3, 1, 2]
positivity	2.3	0.9	[2, 2, 2, 3, 3, 3, 3, 0, 3, 2]
possessive	-0.9	1.22066	[-1, -1, -2, -2, 0, -1, 0, -2, 2, -2]
postpone	-0.9	0.83066	[1, -1, -2, -1, -1, -2, -1, -1, 0, -1]
postponed	-0.8	0.4	[-1, -1, 0, 0, -1, -1, -1, -1, -1, -1]
postpones	-1.1	0.83066	[-1, -1, -1, 0, -1, 0, -2, -1, -3, -1]
postponing	-0.5	0.5	[0, -1, 0, -1, 0, 0, 0, -1, -1, -1]
poverty	-2.3	1.00499	[-2, -4, -2, -4, -3, -1, -2, -2, -1, -2]
powerful	1.8	0.9798	[4, 0, 2, 1, 2, 2, 2, 2, 1, 2]
powerless	-2.2	0.6	[-2, -3, -2, -2, -3, -2, -3, -2, -1, -2]
praise	2.6	0.8	[2, 4, 3, 3, 2, 1, 3, 3, 2, 3]
praised	2.2	0.6	[3, 2, 2, 2, 2, 3, 1, 3, 2, 2]
praiser	2.0	0.89443	[3, 1, 3, 1, 1, 2, 1, 3, 2, 3]
praisers	2.0	0.63246	[2, 2, 1, 3, 3, 2, 1, 2, 2, 2]
praises	2.4	0.4899	[3, 2, 2, 3, 2, 3, 2, 3, 2, 2]
praiseworthily	1.9	0.7	[2, 1, 1, 3, 2, 2, 2, 3, 1, 2]
praiseworthiness	2.4	0.8	[1, 3, 2, 4, 2, 2, 3, 2, 3, 2]
praiseworthy	2.6	0.4899	[3, 3, 3, 3, 2, 2, 3, 2, 3, 2]
praising	2.5	0.67082	[2, 3, 3, 2, 2, 3, 3, 3, 1, 3]
pray	1.3	1.18743	[0, 3, 2, 1, 1, 1, -1, 3, 1, 2]
praying	1.5	0.92195	[2, 2, 0, 3, 1, 3, 1, 1, 1, 1]
prays	1.4	1.0198	[2, 0, 2, 0, 1, 1, 3, 1, 3, 1]
prblm	-1.6	0.8	[-1, -1, -1, -1, -1, -3, -3, -2, -2, -1]
prblms	-2.3	1.00499	[-2, -2, -2, -2, -4, -4, -3, -1, -2, -1]
precious	2.7	0.64031	[3, 3, 3, 3, 3, 1, 3, 3, 2, 3]
preciously	2.2	0.74833	[3, 3, 3, 2, 2, 1, 1, 3, 2, 2]
preciousness	1.9	0.83066	[2, 4, 2, 2, 2, 1, 1, 1, 2, 2]
prejudice	-2.3	0.78102	[-2, -1, -2, -2, -3, -2, -2, -3, -4, -2]
prejudiced	-1.9	0.53852	[-2, -3, -1, -2, -1, -2, -2, -2, -2, -2]
prejudices	-1.8	0.74833	[-2, -3, -1, -2, -1, -3, -2, -1, -1, -2]
prejudicial	-2.6	0.8	[-3, -3, -2, -2, -3, -4, -2, -1, -3, -3]
prejudicially	-1.5	1.74642	[-3, -1, -1, -3, -3, -1, -3, 1, 2, -3]
prejudicialness	-2.4	1.42829	[-2, -3, 1, -3, -2, -4, -4, -3, -1, -3]
prejudicing	-1.8	1.07703	[-2, -2, -2, -3, -2, -1, 1, -3, -2, -2]
prepared	0.9	0.3	[1, 1, 1, 1, 1, 0, 1, 1, 1, 1]
pressure	-1.2	0.6	[-1, -1, -1, -1, -2, -2, -1, -2, 0, -1]
pressured	-0.9	1.04403	[-2, -1, -2, -1, 2, -1, -1, -1, -1, -1]
pressureless	1.0	1.26491	[2, 0, 1, -1, -1, 2, 1, 2, 1, 3]
pressures	-1.3	0.64031	[-2, -1, -1, -1, -3, -1, -1, -1, -1, -1]
pressuring	-1.4	0.91652	[-3, -1, -1, -1, -1, -1, -1, -3, -2, 0]
pressurise	-0.6	1.0198	[0, -2, 0, 1, 0, -2, 0, -2, 0, -1]
pressurised	-0.4	0.66332	[0, 0, 0, -1, 0, -1, 0, -2, 0, 0]
pressurises	-0.8	0.6	[-1, -1, -1, 0, -1, -1, 0, -1, 0, -2]
pressurising	-0.6	1.28062	[-1, 0, 0, 2, -1, -1, -3, 0, -2, 0]
pressurizations	-0.3	1.00499	[0, 0, 1, -2, 0, 0, -1, -2, 0, 1]
pressurize	-0.7	1.34536	[0, -2, -2, 0, 0, -1, -3, 2, -1, 0]
pressurized	0.1	0.83066	[0, 0, 0, 2, 0, 0, -1, -1, 0, 1]
pressurizer	0.1	0.83066	[0, 0, 0, 1, 0, 0, -1, -1, 0, 2]
pressurizers	-0.7	0.9	[-1, 0, -2, -1, -2, -1, 0, 0, 1, -1]
pressurizes	-0.2	0.87178	[0, 0, -1, 1, 0, 0, -1, -2, 0, 1]
pressurizing	-0.2	0.9798	[1, 0, -1, -1, -1, 0, -1, 2, 0, -1]
pretend	-0.4	0.91652	[0, 1, -1, -1, -2, 0, -1, -1, 1, 0]
pretending	0.4	1.49666	[2, 0, -1, -1, 0, 3, -2, 1, 0, 2]
pretends	-0.4	0.66332	[0, -1, 0, -1, 1, 0, 0, -1, -1, -1]
prettied	1.6	0.66332	[2, 1, 1, 1, 1, 2, 3, 2, 1, 2]
prettier	2.1	0.53852	[2, 2, 2, 2, 2, 2, 3, 1, 2, 3]
pretties	1.7	0.78102	[2, 1, 1, 2, 1, 2, 3, 1, 1, 3]
prettiest	2.7	0.78102	[4, 3, 4, 2, 2, 2, 3, 2, 2, 3]
pretty	2.2	0.6	[3, 2, 2, 2, 3, 1, 2, 2, 2, 3]
prevent	0.1	1.22066	[-2, 1, 0, 0, -1, 2, -1, 2, 0, 0]
prevented	0.1	0.7	[0, -1, 0, 2, 0, 0, 0, 0, 0, 0]
preventing	-0.1	1.51327	[-2, 0, -2, 3, 0, 0, -1, -1, 2, 0]
prevents	0.3	1.34536	[0, -2, 0, 0, 0, 2, 0, -1, 3, 1]
prick	-1.4	1.42829	[-2, -2, -2, -1, 1, -1, -4, -2, 1, -2]
pricked	-0.6	1.0198	[-1, -1, -2, 1, 0, -1, -2, -1, 1, 0]
pricker	-0.3	0.9	[-1, -1, 0, 2, 0, -1, -1, -1, 0, 0]
prickers	-0.2	0.87178	[1, -1, -1, 0, 1, -1, 0, 1, -1, -1]
pricket	-0.5	0.67082	[0, -1, -1, -2, 0, 0, 0, 0, -1, 0]
prickets	0.3	0.64031	[0, 2, 0, 0, 0, 1, 0, 0, 0, 0]
pricking	-0.9	1.22066	[-2, -1, -2, 2, -2, -2, -1, 0, -1, 0]
prickle	-1.0	0.44721	[-1, -1, -1, -1, -1, -1, -2, 0, -1, -1]
prickled	-0.2	1.53623	[-1, -1, 3, -1, -2, 2, 0, 0, 0, -2]
prickles	-0.8	0.74833	[0, -1, -1, -1, -2, -1, 0, 0, 0, -2]
pricklier	-1.6	0.8	[-1, -2, -2, -2, -2, -1, -2, -1, 0, -3]
prickliest	-1.4	1.35647	[-4, -2, -1, 0, -3, -1, -1, 1, -2, -1]
prickliness	-0.6	0.91652	[-2, -1, -1, -1, -1, 1, -1, 1, 0, -1]
prickling	-0.8	0.6	[-2, -1, 0, -1, 0, 0, -1, -1, -1, -1]
prickly	-0.9	1.04403	[-2, -1, -1, -2, -1, 1, -2, -1, 1, -1]
pricks	-0.9	1.04403	[-2, 0, -1, -3, -1, 1, 0, -1, -1, -1]
pricky	-0.6	1.2	[-2, 1, -1, -2, -1, 2, 0, -1, -1, -1]
pride	1.4	1.11355	[0, 2, 2, 2, 3, 1, -1, 2, 2, 1]
prison	-2.3	0.64031	[-3, -3, -2, -2, -1, -2, -3, -3, -2, -2]
prisoner	-2.5	1.0247	[-3, -1, -4, -4, -3, -2, -3, -2, -1, -2]
prisoners	-2.3	0.78102	[-3, -2, -3, -3, -1, -3, -2, -1, -2, -3]
privilege	1.5	0.80623	[2, 1, 0, 1, 3, 1, 2, 2, 2, 1]
privileged	1.9	0.9434	[2, 3, 3, 2, 3, 1, 1, 2, 0, 2]
privileges	1.6	1.2	[2, 1, 3, 1, 2, 2, 0, 0, 4, 1]
privileging	0.7	1.1	[1, 1, 2, 2, -1, 2, 1, 0, 0, -1]
prize	2.3	1.1	[2, 2, 0, 4, 4, 3, 2, 2, 2, 2]
prized	2.4	0.8	[3, 3, 3, 2, 3, 2, 1, 3, 1, 3]
prizefight	-0.1	1.13578	[-2, 2, 0, 0, 0, -2, 0, 1, 0, 0]
prizefighter	1.0	1.0	[0, 2, 0, 0, 0, 2, 0, 2, 2, 2]
prizefighters	-0.1	1.04403	[0, 0, -3, 0, 0, 0, 0, 1, 1, 0]
prizefighting	0.4	0.91652	[0, 0, 0, 1, 0, 3, 0, 0, 0, 0]
prizefights	0.3	0.78102	[1, 2, 0, 0, -1, 0, 0, 1, 0, 0]
prizer	1.0	1.0	[0, 3, 0, 0, 2, 1, 2, 1, 0, 1]
prizers	0.8	0.9798	[2, 0, 0, 1, 3, 1, 0, 1, 0, 0]
prizes	2.0	1.09545	[3, 3, 2, 0, 2, 1, 2, 4, 1, 2]
prizewinner	2.3	1.00499	[3, 0, 3, 3, 2, 2, 2, 4, 2, 2]
prizewinners	2.4	1.11355	[4, 2, 2, 2, 0, 4, 2, 3, 3, 2]
prizewinning	3.0	0.7746	[3, 3, 3, 3, 3, 4, 3, 1, 3, 4]
proactive	1.8	0.87178	[2, 3, 3, 1, 1, 1, 1, 3, 1, 2]
problem	-1.7	0.64031	[-2, -2, -1, -1, -1, -3, -1, -2, -2, -2]
problematic	-1.9	0.53852	[-1, -2, -2, -2, -2, -3, -2, -1, -2, -2]
problematical	-1.8	0.6	[-3, -1, -1, -2, -2, -2, -2, -2, -1, -2]
problematically	-2.0	1.0	[-2, -1, -3, -1, -1, -2, -4, -3, -1, -2]
problematics	-1.3	1.1	[0, -1, -1, 0, -2, -1, -4, -1, -2, -1]
problems	-1.7	0.78102	[-2, -1, -1, -3, -2, -1, -3, -1, -1, -2]
profit	1.9	0.7	[2, 3, 2, 1, 2, 1, 3, 1, 2, 2]
profitabilities	1.1	0.7	[1, 1, 3, 1, 1, 1, 1, 0, 1, 1]
profitability	1.1	1.44568	[1, 1, -2, 2, 0, 3, 1, 3, 2, 0]
profitable	1.9	0.9434	[2, 3, 0, 3, 1, 2, 1, 3, 2, 2]
profitableness	2.4	1.11355	[1, 4, 2, 4, 2, 2, 4, 2, 1, 2]
profitably	1.6	0.91652	[1, 2, 0, 3, 1, 3, 1, 1, 2, 2]
profited	1.3	1.00499	[2, 2, 2, 1, 0, 0, 1, 3, 0, 2]
profiteer	0.8	1.6	[2, -2, -1, 3, -1, 2, 1, 2, 0, 2]
profiteered	-0.5	1.9105	[0, 0, 2, -2, -3, -2, 3, 0, -3, 0]
profiteering	-0.6	2.05913	[-1, 2, -2, -4, -2, 2, 1, -2, 2, -2]
profiteers	0.5	1.5	[-2, 1, -1, -1, 1, -1, 2, 2, 2, 2]
profiter	0.7	1.55242	[2, 2, 2, 1, -1, 1, -1, 3, 0, -2]
profiterole	0.4	0.66332	[0, 0, 0, 0, 0, 1, 0, 1, 2, 0]
profiteroles	0.5	0.92195	[0, 0, -1, 2, 2, 0, 0, 1, 0, 1]
profiting	1.6	0.91652	[1, 0, 3, 2, 1, 2, 3, 1, 1, 2]
profitless	-1.5	0.92195	[-2, -2, -3, -1, -1, -1, 0, -1, -1, -3]
profits	1.9	1.04403	[2, 2, 1, 0, 2, 1, 2, 4, 3, 2]
profitwise	0.9	0.83066	[1, 2, 0, 0, 1, 2, 2, 0, 1, 0]
progress	1.8	0.74833	[3, 2, 1, 2, 3, 1, 1, 2, 2, 1]
prominent	1.3	0.45826	[2, 1, 1, 2, 1, 1, 1, 2, 1, 1]
promiscuities	-0.8	1.32665	[-1, 0, -2, -2, -1, 2, -3, -1, 0, 0]
promiscuity	-1.8	1.07703	[-1, -2, -4, -2, -3, -1, 0, -2, -1, -2]
promiscuous	-0.3	1.34536	[2, -3, -1, -1, 0, -1, 0, -1, 1, 1]
promiscuously	-1.5	1.28452	[-2, -1, -3, 2, -2, -2, -1, -2, -2, -2]
promiscuousness	-0.9	1.37477	[-3, -1, 1, -2, -2, 1, 1, -1, -2, -1]
promise	1.3	0.64031	[1, 0, 1, 1, 2, 2, 1, 1, 2, 2]
promised	1.5	0.92195	[2, 1, 1, 0, 0, 2, 2, 3, 2, 2]
promisee	0.8	0.87178	[2, 1, 0, 0, 0, 2, 0, 2, 1, 0]
promisees	1.1	0.9434	[2, 0, 1, 0, 0, 0, 2, 2, 2, 2]
promiser	1.3	0.9	[2, 1, 1, 0, 0, 2, 2, 3, 1, 1]
promisers	1.6	0.4899	[2, 1, 2, 1, 2, 1, 2, 1, 2, 2]
promises	1.6	0.8	[2, 1, 1, 0, 2, 1, 3, 2, 2, 2]
promising	1.7	0.45826	[1, 2, 2, 1, 2, 2, 1, 2, 2, 2]
promisingly	1.2	0.6	[2, 2, 1, 1, 1, 2, 1, 0, 1, 1]
promisor	1.0	0.63246	[2, 2, 0, 1, 1, 1, 0, 1, 1, 1]
promisors	0.4	0.8	[0, 0, 0, 2, 0, 2, 0, 0, 0, 0]
promissory	0.9	1.13578	[2, 0, 0, 3, 0, 2, 2, 0, 0, 0]
promote	1.6	0.8	[2, 1, 2, 3, 0, 1, 2, 2, 1, 2]
promoted	1.8	0.74833	[2, 2, 1, 1, 1, 2, 2, 1, 3, 3]
promotes	1.4	0.91652	[1, 2, 0, 0, 1, 2, 2, 1, 3, 2]
promoting	1.5	0.67082	[1, 2, 2, 1, 2, 1, 2, 0, 2, 2]
propaganda	-1.0	1.54919	[-2, -3, -2, -3, -2, 1, -1, 1, 0, 1]
prosecute	-1.7	1.00499	[-2, -2, -2, -1, -1, -2, 0, -1, -4, -2]
prosecuted	-1.6	1.95959	[-2, -2, -2, -3, -3, -3, -4, 2, 2, -1]
prosecutes	-1.8	1.53623	[-2, -2, -2, -3, -2, -2, -4, 1, 1, -3]
prosecution	-2.2	1.07703	[-4, -1, -2, -2, -3, -2, -1, -4, -1, -2]
prospect	1.2	0.87178	[1, 3, 1, 0, 2, 2, 1, 0, 1, 1]
prospects	1.2	0.6	[2, 1, 1, 1, 1, 0, 1, 2, 2, 1]
prosperous	2.1	1.86815	[-3, 3, 3, 2, 4, 2, 3, 3, 3, 1]
protect	1.6	0.8	[1, 3, 1, 1, 1, 1, 3, 2, 1, 2]
protected	1.9	0.7	[1, 1, 2, 3, 2, 2, 2, 1, 3, 2]
protects	1.3	0.78102	[2, 2, 0, 2, 1, 1, 0, 2, 2, 1]
protest	-1.0	1.34164	[-2, -1, -1, -2, -1, -2, 1, -2, 2, -2]
protested	-0.5	1.62788	[-2, -3, -1, -1, 0, 2, 2, -2, 1, -1]
protesters	-0.9	0.7	[0, 0, 0, -1, -2, -2, -1, -1, -1, -1]
protesting	-1.8	0.74833	[-2, -2, -1, -2, -1, -1, -2, -1, -3, -3]
protests	-0.9	1.57797	[-2, -2, 2, 0, -1, -2, -2, -2, 2, -2]
proud	2.1	0.3	[2, 3, 2, 2, 2, 2, 2, 2, 2, 2]
prouder	2.2	1.16619	[2, 3, 3, 4, 0, 1, 1, 3, 2, 3]
proudest	2.6	0.66332	[4, 2, 2, 2, 2, 3, 2, 3, 3, 3]
proudful	1.9	1.51327	[3, 3, 0, 1, -1, 1, 4, 3, 2, 3]
proudhearted	1.4	0.91652	[2, -1, 1, 2, 2, 1, 2, 2, 2, 1]
proudly	2.6	0.4899	[2, 2, 3, 3, 2, 3, 3, 3, 2, 3]
provoke	-1.7	0.64031	[-2, -2, -1, -1, -1, -2, -2, -1, -3, -2]
provoked	-1.1	0.83066	[-1, -1, 0, -2, -2, -2, -2, 0, 0, -1]
provokes	-1.3	0.78102	[-2, -2, -1, -2, -1, -2, -2, 0, 0, -1]
provoking	-0.8	1.249	[-2, -2, -1, -2, 1, -1, 1, 1, -1, -2]
pseudoscience	-1.2	1.32665	[-1, -1, -3, -4, 0, 0, -2, 0, 0, -1]
puke	-2.4	1.0198	[-1, -3, -3, -2, -4, -4, -2, -1, -2, -2]
puked	-1.8	0.6	[-1, -2, -2, -3, -1, -1, -2, -2, -2, -2]
pukes	-1.9	0.7	[-1, -2, -3, -3, -1, -1, -2, -2, -2, -2]
puking	-1.8	1.46969	[0, -2, -4, -3, -2, -3, 1, -1, -1, -3]
pukka	2.8	0.4	[3, 2, 3, 3, 3, 2, 3, 3, 3, 3]
punish	-2.4	0.91652	[-1, -3, -3, -4, -3, -1, -2, -2, -2, -3]
punishabilities	-1.7	0.78102	[0, -2, -3, -2, -1, -2, -2, -1, -2, -2]
punishability	-1.6	1.49666	[-2, -2, -1, -1, -2, -2, -3, 2, -4, -1]
punishable	-1.9	0.7	[-3, -1, -1, -2, -2, -1, -3, -2, -2, -2]
punished	-2.0	0.44721	[-2, -2, -1, -2, -2, -2, -2, -2, -3, -2]
punisher	-1.9	0.53852	[-3, -1, -2, -2, -2, -2, -2, -1, -2, -2]
punishers	-2.6	0.8	[-3, -3, -3, -3, -2, -2, -4, -2, -1, -3]
punishes	-2.1	0.7	[-3, -2, -3, -2, -1, -2, -2, -3, -1, -2]
punishing	-2.6	0.8	[-2, -3, -3, -3, -4, -1, -3, -2, -2, -3]
punishment	-2.2	0.6	[-2, -1, -2, -2, -3, -3, -2, -2, -2, -3]
punishments	-1.8	0.6	[-2, -2, -2, -1, -1, -2, -2, -3, -1, -2]
punitive	-2.3	0.78102	[-4, -2, -3, -1, -2, -2, -2, -2, -3, -2]
pushy	-1.1	0.83066	[-2, -2, -1, -1, -1, 1, -1, -2, -1, -1]
puzzled	-0.7	0.45826	[0, 0, -1, -1, 0, -1, -1, -1, -1, -1]
quaking	-1.5	0.67082	[-1, -2, -2, -2, -1, 0, -2, -1, -2, -2]
questionable	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -1, -2, -2, -1]
questioned	-0.4	1.0198	[-2, 0, 0, -1, -1, 2, 0, 0, -1, -1]
questioning	-0.4	0.66332	[0, -1, -1, 0, 0, 0, -2, 0, 0, 0]
racism	-3.1	0.9434	[-4, -4, -4, -2, -3, -4, -2, -2, -2, -4]
racist	-3.0	0.89443	[-1, -3, -3, -4, -3, -4, -4, -2, -3, -3]
racists	-2.5	0.92195	[-3, 0, -3, -2, -2, -3, -3, -3, -3, -3]
radian	0.4	0.66332	[0, 0, 0, 0, 0, 0, 1, 2, 0, 1]
radiance	1.4	1.11355	[3, 1, 2, 3, 1, 1, -1, 1, 2, 1]
radiances	1.1	0.53852	[1, 2, 2, 1, 1, 1, 0, 1, 1, 1]
radiancies	0.8	1.16619	[-1, 0, 3, 1, 1, 1, 1, 2, -1, 1]
radiancy	1.4	0.66332	[2, 1, 1, 3, 1, 1, 1, 1, 2, 1]
radians	0.2	0.6	[0, 2, 0, 0, 0, 0, 0, 0, 0, 0]
radiant	2.1	0.83066	[4, 2, 2, 2, 2, 1, 1, 3, 2, 2]
radiantly	1.3	0.78102	[3, 2, 1, 1, 1, 1, 1, 0, 1, 2]
radiants	1.2	0.6	[2, 1, 0, 2, 1, 1, 1, 2, 1, 1]
rage	-2.6	0.8	[-3, -2, -2, -3, -3, -4, -1, -2, -3, -3]
raged	-2.0	0.63246	[-2, -1, -1, -3, -3, -2, -2, -2, -2, -2]
ragee	-0.4	1.42829	[-2, -1, 0, 3, 0, -2, -2, 0, 0, 0]
rageful	-2.8	0.6	[-3, -3, -3, -3, -2, -4, -2, -3, -2, -3]
rages	-2.1	0.7	[-3, -1, -1, -3, -3, -2, -2, -2, -2, -2]
raging	-2.4	1.0198	[-1, -3, -3, -1, -3, -3, -1, -4, -2, -3]
rainy	-0.3	0.64031	[-1, 0, 1, 0, 0, 0, -1, 0, -1, -1]
rancid	-2.5	1.11803	[-3, -3, -3, -2, -3, 0, -3, -1, -4, -3]
rancidity	-2.6	0.8	[-4, -3, -3, -2, -2, -2, -2, -2, -2, -4]
rancidly	-2.5	1.20416	[-2, -1, -4, -3, -4, -1, -2, -3, -4, -1]
rancidness	-2.6	0.91652	[-3, -4, -2, -3, -4, -2, -3, -1, -2, -2]
rancidnesses	-1.6	0.4899	[-2, -1, -2, -1, -1, -2, -2, -2, -1, -2]
rant	-1.4	0.66332	[-1, -2, -2, -1, -2, -2, -2, -1, 0, -1]
ranter	-1.2	1.16619	[-1, -2, -2, -2, -1, -1, -1, -2, 2, -2]
ranters	-1.2	0.87178	[-1, -1, -1, 0, -1, -1, -3, -2, 0, -2]
rants	-1.3	0.45826	[-1, -1, -1, -2, -1, -1, -1, -2, -2, -1]
rape	-3.7	0.64031	[-4, -2, -4, -4, -4, -4, -4, -4, -3, -4]
raped	-3.6	0.4899	[-3, -3, -4, -4, -3, -4, -4, -4, -3, -4]
raper	-3.4	0.66332	[-3, -3, -4, -4, -3, -3, -4, -4, -2, -4]
rapers	-3.6	0.66332	[-2, -4, -4, -3, -4, -3, -4, -4, -4, -4]
rapes	-3.5	0.67082	[-4, -3, -4, -4, -3, -4, -4, -3, -2, -4]
rapeseeds	-0.5	1.20416	[0, 0, 0, 0, 0, -4, 0, 0, -1, 0]
raping	-3.8	0.4	[-3, -4, -4, -4, -4, -4, -4, -4, -3, -4]
rapist	-3.9	0.3	[-4, -4, -4, -4, -4, -4, -3, -4, -4, -4]
rapists	-3.3	0.64031	[-3, -3, -4, -3, -4, -3, -4, -2, -4, -3]
rapture	0.6	2.2891	[-2, 2, -2, 3, 3, 4, -1, -1, 2, -2]
raptured	0.9	1.97231	[0, -2, 0, 2, 4, -1, 4, -1, 1, 2]
raptures	0.7	2.05183	[0, -2, 0, 2, 4, -2, 4, -1, 1, 1]
rapturous	1.7	1.95192	[3, 4, 0, 3, 2, 2, 4, -2, -1, 2]
rash	-1.7	0.78102	[-2, -1, -1, -3, -2, -2, -1, -1, -1, -3]
ratified	0.6	0.4899	[1, 0, 0, 1, 1, 0, 1, 1, 0, 1]
reach	0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 1, 0, 0]
reached	0.4	0.4899	[1, 0, 0, 0, 1, 1, 0, 1, 0, 0]
reaches	0.2	0.4	[0, 0, 0, 1, 0, 1, 0, 0, 0, 0]
reaching	0.8	0.6	[1, 0, 2, 0, 1, 0, 1, 1, 1, 1]
readiness	1.0	0.63246	[1, 1, 1, 1, 0, 1, 2, 1, 2, 0]
ready	1.5	1.0247	[2, 1, 1, 0, 2, 2, 1, 1, 1, 4]
reassurance	1.5	0.5	[1, 1, 1, 1, 2, 1, 2, 2, 2, 2]
reassurances	1.4	0.8	[0, 1, 1, 2, 1, 1, 1, 2, 3, 2]
reassure	1.4	0.4899	[2, 2, 1, 1, 2, 1, 2, 1, 1, 1]
reassured	1.7	0.45826	[2, 1, 1, 1, 2, 2, 2, 2, 2, 2]
reassures	1.5	0.92195	[2, 1, 1, 2, 2, 2, -1, 2, 2, 2]
reassuring	1.7	1.48661	[3, 3, 2, -2, 1, 3, 3, 2, 1, 1]
reassuringly	1.8	0.87178	[1, 3, 2, 1, 2, 3, 3, 1, 1, 1]
rebel	-0.6	1.49666	[-2, -1, -1, 0, -2, -1, 2, 1, 1, -3]
rebeldom	-1.5	1.0247	[-2, -2, -1, -3, -2, -2, -1, 1, -1, -2]
rebelled	-1.0	1.26491	[-2, -2, 0, -2, 0, -2, -2, -1, -1, 2]
rebelling	-1.1	1.51327	[-3, -1, -2, -1, -2, -1, -1, -3, 2, 1]
rebellion	-0.5	1.80278	[-2, -2, -1, -1, 3, -1, 1, -3, 2, -1]
rebellions	-1.1	1.57797	[-2, -4, -3, 0, 0, -1, 1, -2, -1, 1]
rebellious	-1.2	1.249	[-2, -1, -3, -1, -2, -2, 1, -1, 1, -2]
rebelliously	-1.8	0.87178	[-3, 0, -2, -2, -1, -2, -3, -1, -2, -2]
rebelliousness	-1.2	1.16619	[0, -3, -1, -2, 1, -1, -2, 0, -2, -2]
rebels	-0.8	1.07703	[-1, 0, 0, -2, 0, -3, 0, 0, 0, -2]
recession	-1.8	1.07703	[-3, -1, -4, -2, -1, -1, -1, -1, -3, -1]
reckless	-1.7	0.64031	[-2, -1, -1, -3, -1, -2, -1, -2, -2, -2]
recommend	1.5	0.67082	[1, 1, 1, 1, 2, 3, 2, 2, 1, 1]
recommended	0.8	1.07703	[1, 1, 0, -2, 1, 2, 1, 2, 1, 1]
recommends	0.9	0.9434	[1, 1, 2, 0, 0, 2, 1, -1, 2, 1]
redeemed	1.3	0.9	[2, 1, 2, 2, 1, -1, 2, 1, 2, 1]
reek	-2.4	0.66332	[-3, -3, -2, -3, -3, -2, -2, -2, -1, -3]
reeked	-2.0	1.09545	[-4, -3, -2, -3, -1, -2, 0, -1, -2, -2]
reeker	-1.7	1.1	[0, -2, -1, 0, -3, -3, -2, -1, -3, -2]
reekers	-1.5	1.0247	[-3, 0, 0, 0, -2, -2, -2, -2, -2, -2]
reeking	-2.0	1.48324	[2, -2, -2, -3, -3, -3, -1, -3, -3, -2]
refuse	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -1, -2, -2, -1]
refused	-1.2	0.74833	[0, -1, -1, -1, -1, -1, -2, -1, -3, -1]
refusing	-1.7	0.64031	[-1, -1, -1, -2, -2, -2, -3, -1, -2, -2]
regret	-1.8	0.6	[-2, -2, -2, -2, -1, -3, -1, -1, -2, -2]
regretful	-1.9	0.83066	[-1, -2, -1, -2, -1, -2, -1, -3, -3, -3]
regretfully	-1.9	0.83066	[-1, -1, -1, -3, -2, -3, -1, -3, -2, -2]
regretfulness	-1.6	0.66332	[-1, -3, -1, -1, -1, -2, -1, -2, -2, -2]
regrets	-1.5	0.5	[-2, -2, -2, -1, -2, -1, -1, -2, -1, -1]
regrettable	-2.3	0.78102	[-3, -1, -2, -1, -3, -2, -3, -3, -2, -3]
regrettably	-2.0	0.63246	[-2, -3, -1, -3, -2, -1, -2, -2, -2, -2]
regretted	-1.6	0.4899	[-2, -1, -2, -2, -2, -2, -1, -1, -1, -2]
regretter	-1.6	0.66332	[-2, -1, -2, -2, -3, -2, -1, -1, -1, -1]
regretters	-2.0	0.89443	[-1, -2, -2, -2, -4, -3, -1, -1, -2, -2]
regretting	-1.7	0.78102	[-3, -2, -2, -1, -3, -1, -1, -1, -2, -1]
reinvigorate	2.3	0.78102	[3, 3, 3, 2, 3, 1, 2, 1, 2, 3]
reinvigorated	1.9	1.13578	[2, 2, 3, 1, 2, 3, 3, -1, 2, 2]
reinvigorates	1.8	0.9798	[2, 2, 3, 0, 2, 2, 3, 0, 2, 2]
reinvigorating	1.7	0.64031	[1, 2, 1, 1, 2, 2, 2, 1, 3, 2]
reinvigoration	2.2	0.4	[2, 2, 2, 3, 2, 3, 2, 2, 2, 2]
reject	-1.7	0.64031	[-1, -2, -2, -2, -1, -3, -1, -1, -2, -2]
rejected	-2.3	0.45826	[-3, -2, -3, -3, -2, -2, -2, -2, -2, -2]
rejectee	-2.3	0.45826	[-3, -2, -2, -3, -2, -3, -2, -2, -2, -2]
rejectees	-1.8	0.4	[-2, -2, -1, -2, -2, -2, -2, -2, -2, -1]
rejecter	-1.6	0.66332	[-2, -1, -1, -3, -2, -2, -1, -1, -1, -2]
rejecters	-1.8	0.6	[-2, -3, -1, -1, -2, -1, -2, -2, -2, -2]
rejecting	-2.0	0.7746	[-1, -2, -3, -1, -2, -3, -2, -1, -3, -2]
rejectingly	-1.7	0.64031	[-1, -2, -2, -2, -1, -2, -2, -1, -3, -1]
rejection	-2.5	0.67082	[-3, -3, -2, -4, -3, -2, -2, -2, -2, -2]
rejections	-2.1	0.53852	[-3, -2, -2, -2, -1, -2, -3, -2, -2, -2]
rejective	-1.8	0.6	[-3, -2, -2, -2, -1, -1, -2, -2, -1, -2]
rejector	-1.8	0.74833	[-2, -1, -2, -3, -1, -2, -1, -2, -3, -1]
rejects	-2.2	0.4	[-3, -2, -2, -2, -2, -3, -2, -2, -2, -2]
rejoice	1.9	0.9434	[2, 3, 1, 3, 1, 3, 1, 3, 1, 1]
rejoiced	2.0	0.63246	[2, 1, 2, 3, 2, 2, 3, 1, 2, 2]
rejoices	2.1	0.7	[2, 1, 2, 3, 2, 3, 3, 2, 2, 1]
rejoicing	2.8	0.4	[3, 3, 2, 3, 3, 3, 2, 3, 3, 3]
relax	1.9	1.13578	[2, 1, 1, 1, 4, 4, 2, 1, 1, 2]
relaxant	1.0	0.89443	[2, 1, 0, 0, 1, 0, 1, 3, 1, 1]
relaxants	0.7	0.9	[0, 1, 1, 0, -1, 2, 1, 2, 1, 0]
relaxation	2.4	0.4899	[3, 2, 3, 2, 2, 3, 3, 2, 2, 2]
relaxations	1.0	0.89443	[-1, 0, 2, 1, 1, 2, 1, 2, 1, 1]
relaxed	2.2	0.87178	[2, 3, 1, 3, 3, 3, 1, 2, 1, 3]
relaxedly	1.5	0.5	[2, 2, 1, 1, 2, 1, 2, 2, 1, 1]
relaxedness	2.0	0.63246	[2, 2, 3, 1, 3, 2, 2, 2, 1, 2]
relaxer	1.6	0.8	[0, 2, 1, 2, 1, 3, 1, 2, 2, 2]
relaxers	1.4	0.4899	[2, 1, 1, 1, 2, 2, 2, 1, 1, 1]
relaxes	1.5	0.5	[2, 1, 1, 1, 1, 2, 2, 2, 1, 2]
relaxin	1.7	0.64031	[2, 2, 1, 2, 0, 2, 2, 2, 2, 2]
relaxing	2.2	0.6	[1, 2, 2, 2, 2, 3, 3, 3, 2, 2]
relaxins	1.2	1.4	[1, 3, 2, 1, 0, 1, -2, 3, 2, 1]
relentless	0.2	1.07703	[3, -1, 0, -1, 0, 1, 0, 0, 0, 0]
reliant	0.5	1.20416	[0, 2, -1, 1, 0, 1, 2, -1, 2, -1]
relief	2.1	0.53852	[2, 2, 2, 3, 2, 3, 2, 1, 2, 2]
reliefs	1.3	0.78102	[1, 2, 2, 2, 2, 2, 0, 0, 1, 1]
relievable	1.1	1.22066	[1, -2, 1, 1, 1, 1, 3, 2, 1, 2]
relieve	1.5	0.5	[1, 2, 1, 2, 1, 1, 2, 2, 1, 2]
relieved	1.6	0.66332	[2, 1, 2, 1, 1, 3, 2, 1, 1, 2]
relievedly	1.4	0.4899	[1, 2, 1, 1, 1, 2, 2, 2, 1, 1]
reliever	1.5	0.80623	[2, 1, 2, 2, 1, 2, 0, 1, 1, 3]
relievers	1.0	0.63246	[1, 1, 1, 1, 2, 0, 2, 0, 1, 1]
relieves	1.5	0.80623	[2, 1, 2, 2, 1, 2, 0, 1, 1, 3]
relieving	1.5	1.0247	[2, 2, 1, 2, 3, -1, 2, 1, 1, 2]
relievo	1.3	1.00499	[0, 2, 1, 2, 2, -1, 2, 2, 2, 1]
relishing	1.6	0.8	[1, 2, 1, 3, 2, 3, 1, 1, 1, 1]
reluctance	-1.4	0.4899	[-2, -2, -1, -1, -1, -1, -2, -1, -2, -1]
reluctancy	-1.6	0.8	[-3, -2, -1, -1, -1, -1, -2, -1, -3, -1]
reluctant	-1.0	0.7746	[0, -1, 0, -1, -1, 0, -1, -2, -2, -2]
reluctantly	-0.4	1.42829	[-1, 2, -1, -2, -2, -1, 1, -1, 2, -1]
remarkable	2.6	1.0198	[3, 4, 3, 2, 2, 3, 3, 0, 3, 3]
remorse	-1.1	1.51327	[-3, -1, -3, 1, 2, -2, -1, -1, -2, -1]
remorseful	-0.9	2.07123	[-1, 1, -2, -3, 2, -1, -3, -2, 3, -3]
remorsefully	-0.7	1.34536	[-1, -1, 0, -1, -2, -1, -1, 1, -3, 2]
remorsefulness	-0.7	1.61555	[-1, -2, 2, -2, -2, -1, 0, -3, 2, 0]
remorseless	-2.3	0.64031	[-4, -2, -2, -2, -3, -2, -2, -2, -2, -2]
remorselessly	-2.0	1.09545	[-3, -3, -3, -2, -1, -1, -4, -1, -1, -1]
remorselessness	-2.8	1.16619	[-3, -4, -2, -2, -3, -4, -1, -1, -4, -4]
repetitive	-1.0	0.06325	[-1, -1, 0, -2, -1, -2, -1, -1, 0, -1]
repress	-1.4	0.66332	[-1, -1, -3, -2, -1, -1, -2, -1, -1, -1]
repressed	-1.3	0.78102	[-1, 0, -3, -1, -1, -2, -1, -1, -2, -1]
represses	-1.3	1.00499	[-1, 0, -3, -1, 0, -3, -1, -1, -2, -1]
repressible	-1.5	0.92195	[-1, -2, -3, -1, 0, -1, -3, -1, -2, -1]
repressing	-1.8	0.6	[-1, -2, -2, -2, -1, -3, -2, -1, -2, -2]
repression	-1.6	0.91652	[-2, -2, -1, -1, -1, -1, -4, -1, -2, -1]
repressions	-1.7	1.00499	[-2, -3, -1, -2, -2, -3, -2, 0, 0, -2]
repressive	-1.4	1.11355	[-3, -1, -1, -2, -1, -3, 1, -2, -1, -1]
repressively	-1.7	0.45826	[-2, -1, -2, -2, -2, -2, -2, -2, -1, -1]
repressiveness	-1.0	1.73205	[-2, -1, -2, -3, -1, -1, 1, -3, 3, -1]
repressor	-1.4	1.28062	[-1, 0, -3, -1, -2, -4, 0, -1, 0, -2]
repressors	-2.2	1.66132	[-2, -4, 0, -2, -1, -3, -4, -3, 1, -4]
repressurize	-0.3	0.64031	[0, 0, 0, 0, 0, 0, -2, 0, -1, 0]
repressurized	0.1	0.3	[0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
repressurizes	0.1	0.3	[0, 0, 0, 0, 1, 0, 0, 0, 0, 0]
repressurizing	-0.1	0.3	[0, 0, 0, 0, -1, 0, 0, 0, 0, 0]
repulse	-2.8	0.4	[-3, -3, -3, -3, -2, -3, -3, -3, -2, -3]
repulsed	-2.2	0.9798	[0, -2, -2, -2, -3, -3, -1, -3, -3, -3]
rescue	2.3	0.78102	[1, 3, 1, 3, 2, 3, 3, 2, 2, 3]
rescued	1.8	1.53623	[3, 2, 2, -2, 3, 2, 1, 4, 1, 2]
rescues	1.3	0.78102	[3, 2, 0, 2, 1, 1, 1, 1, 1, 1]
resent	-0.7	1.26886	[-2, -2, 0, -1, -1, -1, -2, 1, 2, -1]
resented	-1.6	1.35647	[-2, -3, -2, -2, -1, -1, 2, -3, -2, -2]
resentence	-1.0	0.7746	[-1, 0, -1, -1, 0, -2, -1, 0, -2, -2]
resentenced	-0.8	0.9798	[0, -2, -1, 0, -1, 0, 0, 0, -3, -1]
resentences	-0.6	0.8	[0, -2, -1, 0, -1, 0, 0, 0, -2, 0]
resentencing	0.2	0.87178	[-1, -1, 0, 1, 0, 0, 1, 2, 0, 0]
resentful	-2.1	0.83066	[-3, -1, -2, -3, -1, -1, -3, -2, -2, -3]
resentfully	-1.4	1.11355	[-1, -2, -1, -1, -3, 1, -1, -3, -1, -2]
resentfulness	-2.0	0.7746	[-2, -2, -3, -3, -2, -3, -2, -1, -1, -1]
resenting	-1.2	1.72047	[-2, -1, -2, -2, -1, -3, -3, 2, 2, -2]
resentment	-1.9	0.83066	[-1, -3, -2, -3, -2, -3, -1, -1, -1, -2]
resentments	-1.9	0.7	[-2, -1, -2, -3, -1, -2, -2, -2, -1, -3]
resents	-1.2	1.32665	[-2, -1, -1, -3, 1, -1, 1, -3, -2, -1]
resign	-1.4	0.66332	[-2, -1, -3, -1, -1, -1, -1, -2, -1, -1]
resignation	-1.2	0.4	[-1, -1, -1, -2, -1, -1, -1, -1, -2, -1]
resignations	-1.2	0.6	[0, -1, -1, -2, -2, -2, -1, -1, -1, -1]
resigned	-1.0	0.63246	[-2, -1, 0, -1, -2, -1, -1, -1, 0, -1]
resignedly	-0.7	1.26886	[-3, -1, -1, -1, -1, 1, 1, 1, -2, -1]
resignedness	-0.8	1.07703	[-1, -2, 1, -2, -1, -1, 0, -2, -1, 1]
resigner	-1.2	0.6	[-2, -1, 0, -1, -2, -2, -1, -1, -1, -1]
resigners	-1.0	1.09545	[-1, 0, -1, -3, -1, 0, 0, -1, -3, 0]
resigning	-0.9	1.22066	[-1, -1, -1, -1, -1, 0, -1, -3, -2, 2]
resigns	-1.3	0.9	[0, -1, -1, 0, -2, -1, -3, -1, -2, -2]
resolute	1.1	0.53852	[2, 1, 0, 1, 1, 1, 2, 1, 1, 1]
resolvable	1.0	0.0	[1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
resolve	1.6	0.66332	[2, 1, 1, 2, 2, 1, 3, 1, 1, 2]
resolved	0.7	0.78102	[1, 2, 0, 1, 1, 0, 2, 0, 0, 0]
resolvent	0.7	0.78102	[1, 0, 1, 2, 0, -1, 1, 1, 1, 1]
resolvents	0.4	0.66332	[2, 0, 0, 1, 0, 0, 1, 0, 0, 0]
resolver	0.7	0.78102	[1, 2, 0, 1, 1, 0, 2, 0, 0, 0]
resolvers	1.4	0.4899	[2, 1, 2, 1, 1, 1, 2, 1, 2, 1]
resolves	0.7	0.78102	[1, 2, 0, 1, 1, 0, 2, 0, 0, 0]
resolving	1.6	0.4899	[2, 2, 1, 2, 2, 1, 1, 1, 2, 2]
respect	2.1	0.53852	[2, 2, 2, 2, 3, 3, 2, 2, 1, 2]
respectabilities	1.8	0.4	[2, 2, 2, 1, 2, 2, 2, 1, 2, 2]
respectability	2.4	0.8	[4, 1, 3, 2, 3, 3, 2, 2, 2, 2]
respectable	1.9	0.7	[2, 2, 2, 2, 1, 3, 3, 1, 1, 2]
respectableness	1.2	1.32665	[2, 1, 1, 0, 3, 2, -2, 2, 2, 1]
respectably	1.7	0.78102	[2, 2, 1, 3, 1, 3, 2, 1, 1, 1]
respected	2.1	0.7	[2, 2, 1, 2, 3, 1, 2, 3, 3, 2]
respecter	2.1	0.53852	[3, 2, 2, 2, 2, 2, 1, 3, 2, 2]
respecters	1.6	0.8	[2, 1, 2, 2, 3, 1, 2, 2, 0, 1]
respectful	2.0	0.7746	[1, 1, 3, 2, 2, 3, 1, 2, 3, 2]
respectfully	1.7	0.64031	[1, 2, 1, 2, 2, 2, 1, 3, 1, 2]
respectfulness	1.9	1.37477	[4, 2, 2, 1, 2, 4, -1, 1, 2, 2]
respectfulnesses	1.3	1.18743	[1, 1, 2, 2, 2, 2, 2, 1, -2, 2]
respecting	2.2	0.6	[1, 3, 2, 3, 2, 2, 3, 2, 2, 2]
respective	1.8	1.16619	[2, 2, 3, 0, 1, 3, 3, 1, 0, 3]
respectively	1.4	0.91652	[0, 0, 2, 2, 0, 2, 2, 2, 2, 2]
respectiveness	1.1	1.04403	[0, 2, 1, 0, 1, 0, 2, 2, 0, 3]
respects	1.3	1.00499	[2, 0, 0, 0, 2, 1, 2, 1, 2, 3]
responsible	1.3	1.1	[1, 1, 0, 4, 2, 2, 1, 0, 1, 1]
responsive	1.5	0.92195	[3, 1, 1, 0, 1, 1, 2, 3, 2, 1]
restful	1.5	0.67082	[1, 1, 1, 1, 2, 1, 2, 1, 2, 3]
restless	-1.1	0.3	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -1]
restlessly	-1.4	0.91652	[-1, -4, -1, -1, -1, -2, -1, -1, -1, -1]
restlessness	-1.2	0.74833	[-3, -1, -1, 0, -1, -2, -1, -1, -1, -1]
restore	1.2	0.9798	[1, 1, 2, 2, 2, 2, 1, -1, 0, 2]
restored	1.4	0.91652	[2, 2, 1, 2, 1, 2, 2, 1, -1, 2]
restores	1.2	0.6	[2, 1, 1, 2, 1, 0, 1, 1, 2, 1]
restoring	1.2	0.4	[2, 1, 1, 1, 1, 2, 1, 1, 1, 1]
restrict	-1.6	0.8	[-3, -1, -1, -1, -3, -1, -2, -1, -1, -2]
restricted	-1.6	0.4899	[-1, -2, -1, -1, -1, -2, -2, -2, -2, -2]
restricting	-1.6	0.4899	[-2, -2, -2, -2, -1, -2, -1, -1, -2, -1]
restriction	-1.1	0.9434	[-1, -1, -1, -1, -3, 1, -2, -1, -1, -1]
restricts	-1.3	1.1	[-2, -2, -3, -1, -1, -2, 1, -1, -2, 0]
retained	0.1	0.7	[-1, 1, 1, 1, 0, -1, 0, 0, 0, 0]
retard	-2.4	0.8	[-2, -2, -2, -1, -3, -2, -4, -3, -3, -2]
retarded	-2.7	1.26886	[-4, -1, -3, -3, -4, -4, -2, -1, -1, -4]
retreat	0.8	1.07703	[2, 2, 0, 0, -1, 1, 2, 0, 2, 0]
revenge	-2.4	0.66332	[-2, -3, -2, -3, -3, -3, -2, -1, -2, -3]
revenged	-0.9	1.37477	[-2, -3, -2, -1, -1, 2, -1, 1, -1, -1]
revengeful	-2.4	0.4899	[-2, -2, -2, -3, -3, -2, -3, -3, -2, -2]
revengefully	-1.4	2.00998	[-3, -2, -2, -2, 3, 2, -3, -3, -2, -2]
revengefulness	-2.2	0.87178	[-2, -3, -3, -2, -2, -2, -1, -4, -1, -2]
revenger	-2.1	0.83066	[-2, -3, -2, -1, -1, -2, -3, -3, -1, -3]
revengers	-2.0	0.44721	[-2, -2, -3, -2, -2, -2, -1, -2, -2, -2]
revenges	-1.9	0.7	[-2, -3, -2, -1, -1, -2, -3, -2, -1, -2]
revered	2.3	1.1	[3, 3, 1, 3, 3, 3, 0, 3, 3, 1]
revive	1.4	1.11355	[4, 0, 2, 1, 1, 0, 2, 2, 1, 1]
revives	1.6	0.4899	[1, 1, 2, 2, 1, 2, 2, 2, 1, 2]
reward	2.7	0.78102	[3, 4, 1, 2, 3, 3, 2, 3, 3, 3]
rewardable	2.0	1.0	[3, 1, 4, 3, 1, 2, 1, 2, 2, 1]
rewarded	2.2	0.74833	[1, 2, 3, 2, 2, 4, 2, 2, 2, 2]
rewarder	1.6	0.8	[1, 2, 3, 1, 1, 3, 2, 1, 1, 1]
rewarders	1.9	0.83066	[1, 2, 2, 1, 3, 3, 1, 2, 1, 3]
rewarding	2.4	0.8	[3, 4, 2, 2, 1, 3, 3, 2, 2, 2]
rewardingly	2.4	0.8	[3, 2, 3, 4, 3, 2, 2, 2, 1, 2]
rewards	2.1	0.83066	[2, 1, 3, 4, 2, 2, 2, 2, 1, 2]
rich	2.6	0.8	[2, 3, 2, 4, 4, 3, 2, 2, 2, 2]
richened	1.9	0.83066	[3, 2, 2, 1, 3, 1, 2, 3, 1, 1]
richening	1.0	1.34164	[2, 2, 0, 0, -1, 1, 2, 3, -1, 2]
richens	0.8	0.9798	[1, 0, 3, 0, 0, 0, 1, 2, 1, 0]
richer	2.4	1.2	[1, 4, 2, 1, 2, 4, 4, 1, 3, 2]
riches	2.4	1.0198	[2, 4, 1, 1, 2, 4, 3, 2, 3, 2]
richest	2.4	1.11355	[4, 4, 2, 2, 3, 0, 2, 2, 3, 2]
richly	1.9	0.53852	[2, 2, 2, 2, 3, 1, 2, 2, 1, 2]
richness	2.2	0.74833	[2, 3, 2, 2, 2, 2, 2, 1, 4, 2]
richnesses	2.1	0.9434	[2, 1, 2, 2, 3, 1, 3, 1, 4, 2]
richweed	0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 1, 0, 0]
richweeds	-0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, 0, -1]
ridicule	-2.0	0.63246	[-2, -2, -2, -2, -3, -2, -1, -2, -1, -3]
ridiculed	-1.5	0.5	[-1, -1, -1, -2, -2, -2, -1, -2, -1, -2]
ridiculer	-1.6	0.91652	[-1, -1, -1, -2, -2, 0, -1, -3, -2, -3]
ridiculers	-1.6	0.66332	[-2, -1, -1, -2, -3, -2, -1, -2, -1, -1]
ridicules	-1.8	0.6	[-1, -1, -2, -2, -2, -2, -1, -2, -2, -3]
ridiculing	-1.8	0.6	[-2, -3, -2, -1, -1, -2, -2, -1, -2, -2]
ridiculous	-1.5	0.67082	[-3, -2, -1, -1, -2, -1, -2, -1, -1, -1]
ridiculously	-1.4	0.8	[-1, -2, -1, 0, -2, -1, -1, -3, -1, -2]
ridiculousness	-1.1	1.51327	[-1, -1, -1, -2, -1, 3, -3, -2, -1, -2]
ridiculousnesses	-1.6	1.11355	[-3, 0, -2, -2, -1, -1, -1, -4, -1, -1]
rig	-0.5	1.0247	[0, 0, 0, 0, -2, 0, 0, 0, -3, 0]
rigged	-1.5	1.0247	[-2, -3, -2, -1, -1, -2, 1, -1, -2, -2]
rigid	-0.5	0.67082	[-2, -1, 0, 0, 0, 0, 0, -1, -1, 0]
rigidification	-1.1	0.9434	[-1, -2, 0, 0, 0, -2, -2, -2, 0, -2]
rigidifications	-0.8	0.9798	[-2, -1, 0, 0, -2, -1, 0, 1, -2, -1]
rigidified	-0.7	1.00499	[0, -1, 0, 0, 0, 0, -2, 0, -1, -3]
rigidifies	-0.6	0.8	[0, -1, 0, 0, 0, 0, -2, 0, -1, -2]
rigidify	-0.3	0.64031	[0, 0, 0, 0, -1, 0, 0, 0, -2, 0]
rigidities	-0.7	0.78102	[0, -1, 0, 0, 0, 0, -2, -1, -1, -2]
rigidity	-0.7	0.64031	[-1, 0, -1, 0, -1, 0, -2, 0, -1, -1]
rigidly	-0.7	0.45826	[-1, -1, -1, -1, 0, 0, -1, 0, -1, -1]
rigidness	-0.3	1.00499	[0, -1, -1, 0, 2, -1, -1, -1, 1, -1]
rigorous	-1.1	1.51327	[-2, -3, 1, 0, 1, 0, -1, -1, -3, -3]
rigorously	-0.4	1.28062	[0, 2, 1, 0, -1, 0, -1, -1, -3, -1]
riot	-2.6	1.0198	[-3, 0, -3, -2, -3, -3, -3, -4, -3, -2]
riots	-2.3	0.78102	[-2, -3, -3, -2, -1, -1, -3, -2, -3, -3]
risk	-1.1	0.7	[-1, -1, 0, -2, -1, 0, -2, -2, -1, -1]
risked	-0.9	0.7	[-1, -2, 0, 0, -1, 0, -2, -1, -1, -1]
risker	-0.8	0.4	[-1, -1, -1, 0, -1, 0, -1, -1, -1, -1]
riskier	-1.4	0.91652	[0, -3, -1, 0, -1, -2, -1, -2, -2, -2]
riskiest	-1.5	1.0247	[-2, -3, -1, -1, -3, 0, -1, -2, 0, -2]
riskily	-0.7	1.34536	[-1, -2, -1, -1, 3, 0, -1, -1, -1, -2]
riskiness	-1.3	1.00499	[-3, -1, -1, 0, -1, 0, -1, -3, -2, -1]
riskinesses	-1.6	0.91652	[-2, -1, -1, 0, -3, -1, -2, -1, -3, -2]
risking	-1.3	1.1	[0, 0, -2, 0, -1, -3, -3, -1, -2, -1]
riskless	1.3	0.9	[2, 1, 0, 1, 1, 1, 2, 3, 0, 2]
risks	-1.1	0.9434	[-1, 0, -1, -1, 0, 0, -2, -1, -3, -2]
risky	-0.8	0.9798	[-1, 1, -1, -1, -1, 0, 0, -1, -3, -1]
rob	-2.6	0.8	[-2, -4, -3, -2, -3, -4, -2, -2, -2, -2]
robber	-2.6	1.0198	[-4, -1, -2, -3, -2, -1, -4, -3, -3, -3]
robed	-0.7	0.9	[-2, 0, 0, 0, 0, -2, 0, -2, -1, 0]
robing	-1.5	1.56525	[0, 0, 0, 0, -2, -3, -3, -4, -3, 0]
robs	-2.0	1.0	[-2, -1, -3, -2, -2, -1, -4, -3, -1, -1]
robust	1.4	1.42829	[1, 0, 0, 1, 4, 0, 2, 3, 3, 0]
roflcopter	2.1	0.53852	[2, 2, 2, 1, 3, 2, 3, 2, 2, 2]
romance	2.6	0.66332	[2, 3, 4, 2, 3, 2, 3, 2, 3, 2]
romanced	2.2	0.87178	[2, 3, 2, 4, 2, 1, 2, 3, 1, 2]
romancer	1.3	1.1	[0, 0, 2, 2, 2, 2, 3, 2, 0, 0]
romancers	1.7	1.00499	[0, 2, 2, 2, 2, 2, 1, 3, 0, 3]
romances	1.3	0.9	[0, 1, 2, 2, 2, 2, 2, 2, 0, 0]
romancing	2.0	0.89443	[4, 1, 1, 2, 2, 2, 2, 1, 3, 2]
romantic	1.7	0.78102	[2, 2, 2, 1, 2, 3, 2, 2, 0, 1]
romantically	1.8	0.87178	[3, 1, 2, 0, 1, 2, 2, 2, 3, 2]
romanticise	1.7	1.61555	[-1, 4, 2, 1, 3, 1, 3, 3, 2, -1]
romanticised	1.7	0.9	[2, 2, 2, 2, 2, 2, 3, 0, 2, 0]
romanticises	1.3	1.1	[2, 2, 1, 1, 1, 2, 3, -1, 2, 0]
romanticising	2.7	0.78102	[3, 3, 3, 2, 1, 3, 2, 3, 4, 3]
romanticism	2.2	1.32665	[1, 4, 2, 1, 3, 1, 3, 3, 4, 0]
romanticisms	2.1	0.9434	[2, 3, 2, 1, 2, 3, 3, 2, 3, 0]
romanticist	1.9	1.3	[1, 4, 2, 0, 3, 1, 2, 3, 3, 0]
romanticists	1.3	1.00499	[2, 0, 0, 1, 3, 3, 1, 1, 1, 1]
romanticization	1.5	1.36015	[1, 3, 2, 2, -1, 2, 0, 1, 4, 1]
romanticizations	2.0	1.0	[4, 1, 3, 1, 2, 3, 1, 2, 2, 1]
romanticize	1.8	0.9798	[2, 2, 1, 1, 2, 1, 3, 3, 3, 0]
romanticized	0.9	1.22066	[0, 1, 1, 3, -1, 0, 1, 1, 3, 0]
romanticizes	1.8	0.87178	[2, 3, 2, 1, 2, 3, 0, 1, 2, 2]
romanticizing	1.2	1.07703	[0, 1, 2, 1, 3, 0, 1, 1, 0, 3]
romantics	1.9	0.83066	[2, 3, 2, 1, 0, 2, 3, 2, 2, 2]
rotten	-2.3	0.78102	[-2, -3, -1, -3, -3, -1, -3, -2, -3, -2]
rude	-2.0	0.44721	[-2, -1, -3, -2, -2, -2, -2, -2, -2, -2]
rudely	-2.2	0.87178	[-3, -2, -3, -1, -2, -4, -2, -2, -1, -2]
rudeness	-1.5	0.67082	[-2, -1, -1, -1, -1, -3, -2, -2, -1, -1]
ruder	-2.1	0.83066	[-2, -1, -4, -2, -3, -1, -2, -2, -2, -2]
ruderal	-0.8	1.46969	[-3, 0, -1, -2, 0, -1, -3, 0, 2, 0]
ruderals	-0.4	0.66332	[0, -1, 0, -2, 0, -1, 0, 0, 0, 0]
rudesby	-2.0	0.7746	[-2, -3, -2, -2, -1, -3, -1, -1, -2, -3]
rudest	-2.5	0.5	[-3, -2, -3, -3, -2, -2, -2, -3, -2, -3]
ruin	-2.8	0.87178	[-3, -3, -1, -4, -4, -3, -3, -2, -2, -3]
ruinable	-1.6	0.8	[-1, -2, -1, -3, -1, -3, -1, -1, -2, -1]
ruinate	-2.8	0.87178	[-4, -4, -3, -3, -1, -3, -3, -3, -2, -2]
ruinated	-1.5	1.56525	[0, -4, 0, -4, -1, -2, 0, -1, -3, 0]
ruinates	-1.5	1.56525	[0, -4, 0, -4, -1, -2, 0, -1, -3, 0]
ruinating	-1.5	1.20416	[-2, -2, -2, -3, 0, -1, 1, -3, -1, -2]
ruination	-2.7	1.00499	[-4, -4, -3, -3, -2, -1, -3, -3, -3, -1]
ruinations	-1.6	1.35647	[-2, -2, -3, -4, 0, -1, 1, -2, -1, -2]
ruined	-2.1	0.7	[-3, -2, -2, -1, -2, -2, -3, -3, -1, -2]
ruiner	-2.0	0.63246	[-1, -3, -2, -2, -1, -2, -3, -2, -2, -2]
ruing	-1.6	0.91652	[-2, -1, -2, -1, -1, -1, -3, 0, -3, -2]
ruining	-1.0	1.94936	[-3, -3, 0, -2, 1, -3, 2, -2, 2, -2]
ruinous	-2.7	0.78102	[-2, -3, -3, -3, -3, -4, -2, -3, -1, -3]
ruinously	-2.6	0.8	[-3, -2, -2, -3, -1, -3, -4, -2, -3, -3]
ruinousness	-1.0	1.09545	[-1, -2, -3, -1, -1, -2, 1, 0, -1, 0]
ruins	-1.9	0.9434	[-1, -2, -1, -2, -1, -2, -4, -1, -3, -2]
sabotage	-2.4	0.91652	[-3, -3, -3, -2, -2, -4, -2, -3, -1, -1]
sad	-2.1	0.9434	[-1, -1, -2, -2, -3, -2, -3, -2, -4, -1]
sadden	-2.6	0.4899	[-2, -3, -2, -3, -2, -3, -2, -3, -3, -3]
saddened	-2.4	0.4899	[-3, -2, -3, -2, -3, -3, -2, -2, -2, -2]
saddening	-2.2	0.4	[-3, -2, -2, -2, -2, -2, -3, -2, -2, -2]
saddens	-1.9	0.7	[-3, -1, -1, -1, -2, -2, -2, -2, -3, -2]
sadder	-2.4	0.91652	[-2, -2, -3, -4, -3, -1, -3, -1, -2, -3]
saddest	-3.0	0.63246	[-4, -3, -2, -3, -3, -2, -3, -4, -3, -3]
sadly	-1.8	0.6	[-2, -2, -2, -1, -2, -3, -2, -1, -1, -2]
sadness	-1.9	0.3	[-2, -1, -2, -2, -2, -2, -2, -2, -2, -2]
safe	1.9	0.3	[2, 1, 2, 2, 2, 2, 2, 2, 2, 2]
safecracker	-0.7	1.61555	[-3, -2, 0, -3, 0, -2, 0, 1, 2, 0]
safecrackers	-0.9	1.04403	[-1, 0, -2, 0, -1, 0, 0, -3, 0, -2]
safecracking	-0.9	0.9434	[0, 0, 0, 0, -2, 0, -1, -2, -2, -2]
safecrackings	-0.7	1.67631	[-2, -1, -2, -4, 2, -1, -1, 0, 1, 1]
safeguard	1.6	0.4899	[2, 2, 1, 1, 1, 2, 2, 2, 2, 1]
safeguarded	1.5	0.92195	[1, 2, 2, 0, 2, 0, 3, 1, 2, 2]
safeguarding	1.1	0.7	[2, 1, 1, 0, 1, 0, 2, 1, 1, 2]
safeguards	1.4	0.66332	[1, 2, 1, 1, 0, 2, 2, 2, 2, 1]
safekeeping	1.4	0.66332	[3, 1, 1, 2, 1, 1, 2, 1, 1, 1]
safelight	1.1	1.22066	[0, 3, 0, 2, 0, 3, 0, 2, 1, 0]
safelights	0.8	1.07703	[0, 3, 1, 0, 0, 2, 0, 2, 0, 0]
safely	2.2	0.74833	[2, 2, 2, 3, 4, 2, 2, 1, 2, 2]
safeness	1.5	0.67082	[1, 1, 1, 1, 3, 1, 2, 2, 2, 1]
safer	1.8	0.6	[2, 1, 2, 3, 2, 2, 1, 1, 2, 2]
safes	0.4	0.8	[0, 0, 2, 0, 0, 0, 0, 0, 2, 0]
safest	1.7	1.61555	[2, 2, 2, 2, -3, 3, 3, 2, 2, 2]
safeties	1.5	1.0247	[2, 0, 1, 3, 2, 1, 3, 1, 0, 2]
safety	1.8	0.6	[2, 2, 2, 2, 1, 1, 2, 3, 1, 2]
safetyman	0.3	0.64031	[0, 0, 0, 0, 2, 0, 1, 0, 0, 0]
salient	1.1	1.22066	[1, 3, 0, -1, 0, 1, 2, 1, 1, 3]
sappy	-1.0	1.18322	[-2, -1, 2, -2, -2, 0, -1, -1, -2, -1]
sarcasm	-0.9	0.7	[0, -2, -1, -1, 0, 0, -1, -1, -2, -1]
sarcasms	-0.9	0.7	[0, -1, 0, -1, -1, -2, 0, -1, -1, -2]
sarcastic	-1.0	0.7746	[-1, -1, -1, -1, -1, -1, -1, -2, 1, -2]
sarcastically	-1.1	1.37477	[-1, -4, 1, -1, -1, -2, 1, -2, -1, -1]
satisfaction	1.9	0.9434	[1, 3, 2, 4, 2, 1, 1, 1, 2, 2]
satisfactions	2.1	0.7	[3, 3, 3, 1, 2, 2, 1, 2, 2, 2]
satisfactorily	1.6	1.11355	[1, 2, 2, -1, 2, 1, 3, 3, 1, 2]
satisfactoriness	1.5	0.5	[1, 2, 1, 2, 2, 1, 2, 1, 2, 1]
satisfactory	1.5	0.67082	[2, 3, 1, 1, 1, 2, 2, 1, 1, 1]
satisfiable	1.9	0.83066	[3, 1, 2, 1, 2, 3, 1, 2, 3, 1]
satisfied	1.8	0.6	[2, 2, 2, 1, 1, 2, 3, 1, 2, 2]
satisfies	1.8	0.6	[3, 1, 2, 1, 1, 2, 2, 2, 2, 2]
satisfy	2.0	0.63246	[3, 2, 2, 2, 2, 1, 1, 2, 3, 2]
satisfying	2.0	1.48324	[2, 3, 2, 1, 3, 3, 3, 2, -2, 3]
satisfyingly	1.9	0.9434	[1, 2, 2, 1, 2, 1, 4, 1, 3, 2]
savage	-2.0	1.73205	[-3, -4, -3, 1, -2, -1, -3, -4, -2, 1]
savaged	-2.0	1.34164	[-1, 0, -4, -3, -3, -3, -2, 0, -1, -3]
savagely	-2.2	0.74833	[-2, -1, -3, -2, -2, -1, -2, -3, -3, -3]
savageness	-2.6	1.0198	[-3, -1, -2, -3, -2, -1, -4, -4, -3, -3]
savagenesses	-0.9	1.86815	[-2, 3, -1, -3, -2, 2, -3, -1, -1, -1]
savageries	-1.9	1.75784	[-3, 1, -3, -4, -3, 1, -2, -3, 0, -3]
savagery	-2.5	1.62788	[-2, -3, -3, -3, 2, -3, -3, -4, -4, -2]
savages	-2.4	1.0198	[-2, -2, -3, -4, -3, -3, -2, 0, -2, -3]
save	2.2	1.16619	[1, 3, 3, 1, 2, 1, 2, 4, 1, 4]
saved	1.8	0.6	[1, 2, 2, 2, 1, 3, 2, 2, 1, 2]
scam	-2.7	0.64031	[-2, -3, -3, -3, -2, -2, -4, -3, -3, -2]
scams	-2.8	0.87178	[-3, -1, -3, -4, -4, -3, -2, -2, -3, -3]
scandal	-1.9	1.81384	[-3, -2, -2, -4, 3, -3, -3, -1, -2, -2]
scandalous	-2.4	0.8	[-2, -1, -3, -2, -4, -2, -3, -3, -2, -2]
scandals	-2.2	0.9798	[-2, -3, -3, -2, -1, 0, -3, -3, -2, -3]
scapegoat	-1.7	0.64031	[-3, -2, -2, -2, -1, -1, -1, -2, -1, -2]
scapegoats	-1.4	0.8	[-1, -2, -2, -1, 0, -2, -2, 0, -2, -2]
scare	-2.2	0.87178	[-2, -2, -4, -2, -3, -1, -2, -1, -2, -3]
scarecrow	-0.8	0.9798	[-1, 0, -1, 0, 0, 0, -2, -3, -1, 0]
scarecrows	-0.7	1.1	[2, 0, -1, -1, -1, -2, -1, -2, 0, -1]
scared	-1.9	0.7	[-1, -1, -2, -3, -2, -3, -1, -2, -2, -2]
scaremonger	-2.1	0.53852	[-1, -2, -2, -3, -2, -2, -3, -2, -2, -2]
scaremongers	-2.0	1.0	[-2, -2, 0, -4, -2, -2, -1, -2, -3, -2]
scarer	-1.7	0.78102	[-2, -1, -1, -2, -3, -1, -3, -1, -2, -1]
scarers	-1.3	0.9	[-1, -2, -1, 0, 0, -1, -3, -2, -1, -2]
scares	-1.4	0.4899	[-1, -1, -2, -1, -1, -2, -1, -2, -2, -1]
scarey	-1.7	0.64031	[-1, -1, -2, -2, -1, -2, -1, -2, -3, -2]
scaring	-1.9	1.22066	[-3, -2, -1, -3, -1, -3, -2, -2, 1, -3]
scary	-2.2	0.87178	[-2, -1, -4, -3, -3, -2, -2, -2, -2, -1]
sceptic	-1.0	0.89443	[-3, 0, -1, -1, -1, 0, 0, -2, -1, -1]
sceptical	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -1, -2, -2, -1]
scepticism	-0.8	0.87178	[-1, -2, -2, 0, 0, -1, 1, -1, -1, -1]
sceptics	-0.7	0.78102	[0, 0, 0, -1, -2, 0, -1, -1, 0, -2]
scold	-1.7	0.78102	[-2, -1, -1, -1, -3, -3, -2, -2, -1, -1]
scoop	0.6	0.8	[0, 0, 1, 0, 2, 0, 2, 0, 1, 0]
scorn	-1.7	0.64031	[-2, -3, -2, -1, -1, -1, -1, -2, -2, -2]
scornful	-1.8	1.16619	[-3, -3, -2, -1, -4, 0, -2, -1, -1, -1]
scream	-1.7	0.78102	[0, -3, -1, -1, -2, -2, -2, -2, -2, -2]
screamed	-1.3	1.1	[-2, -3, -2, -1, -1, -2, -2, -1, 1, 0]
screamers	-1.5	0.92195	[-2, -1, -2, -2, -2, -2, -1, -2, 1, -2]
screaming	-1.6	0.8	[0, -1, -1, -2, -3, -1, -2, -2, -2, -2]
screams	-1.2	0.9798	[-1, -2, -2, -1, -1, -2, 1, -2, 0, -2]
screw	-0.4	0.91652	[-1, -2, -1, 0, 0, 1, 0, -1, 1, -1]
screwball	-0.2	0.87178	[0, -1, 0, 0, 1, 1, -1, 0, -2, 0]
screwballs	-0.3	1.00499	[-2, 0, -2, -1, 0, 1, 0, 1, 0, 0]
screwbean	0.3	0.64031	[0, 0, 0, 0, 0, 1, 0, 2, 0, 0]
screwdriver	0.3	0.45826	[1, 0, 0, 0, 0, 1, 0, 0, 1, 0]
screwdrivers	0.1	0.53852	[-1, 0, 0, 1, 0, 0, 0, 1, 0, 0]
screwed	-2.2	0.4	[-2, -2, -2, -2, -2, -3, -3, -2, -2, -2]
screwed up	-1.5	0.67082	[-2, -2, -2, -1, -1, 0, -2, -1, -2, -2]
screwer	-1.2	0.87178	[-1, -2, -1, -2, 0, 0, -2, 0, -2, -2]
screwers	-0.5	1.5	[-2, -2, 0, -2, 0, 2, 2, -1, 0, -2]
screwier	-0.6	1.2	[0, -1, 2, -2, -1, -2, -1, -1, 1, -1]
screwiest	-2.0	0.89443	[-3, -2, -2, -4, -1, -2, -1, -1, -2, -2]
screwiness	-0.5	1.80278	[-2, 0, -2, 3, -2, -1, -2, 1, 2, -2]
screwing	-0.9	0.9434	[-1, 0, 0, 0, -1, -1, -3, -2, 0, -1]
screwlike	0.1	1.04403	[2, -1, 0, 0, 0, 1, 0, 1, -2, 0]
screws	-1.0	1.09545	[0, -3, 0, 0, -1, 0, -2, -2, -2, 0]
screwup	-1.7	0.9	[-2, -2, -2, 1, -2, -2, -2, -2, -2, -2]
screwups	-1.0	1.61245	[-2, -2, -2, -2, 0, 2, -2, -2, 2, -2]
screwworm	-0.4	0.66332	[0, -1, 0, 0, 0, 0, -2, 0, -1, 0]
screwworms	-0.1	1.22066	[-3, 0, -1, 0, 0, 0, 0, 2, 1, 0]
screwy	-1.4	0.8	[-2, -2, -1, -1, -1, 0, -2, -1, -3, -1]
scrumptious	2.1	1.22066	[3, 3, 0, 3, 2, 3, 1, 3, 0, 3]
scrumptiously	1.5	1.43178	[2, 3, 3, -2, 1, 1, 2, 3, 1, 1]
scumbag	-3.2	0.6	[-4, -3, -3, -2, -3, -4, -3, -3, -4, -3]
secure	1.4	0.4899	[1, 2, 1, 1, 2, 1, 1, 2, 2, 1]
secured	1.7	0.78102	[2, 2, 3, 1, 1, 2, 2, 0, 2, 2]
securely	1.4	0.8	[2, 0, 1, 2, 1, 1, 1, 3, 2, 1]
securement	1.1	0.7	[0, 2, 1, 1, 1, 0, 2, 2, 1, 1]
secureness	1.4	0.66332	[2, 1, 1, 3, 1, 1, 1, 1, 2, 1]
securer	1.5	0.67082	[1, 2, 2, 2, 1, 2, 1, 0, 2, 2]
securers	0.6	0.91652	[1, 3, 0, 0, 1, 0, 0, 0, 0, 1]
secures	1.3	0.64031	[1, 2, 2, 1, 1, 2, 1, 0, 2, 1]
securest	2.6	0.8	[3, 3, 2, 3, 1, 4, 3, 2, 2, 3]
securing	1.3	1.00499	[0, 3, 1, 1, 1, 3, 1, 1, 2, 0]
securities	1.2	0.6	[1, 2, 2, 2, 1, 1, 1, 0, 1, 1]
securitization	0.2	1.07703	[0, 0, 1, -1, 1, 0, -2, 0, 2, 1]
securitizations	0.1	0.9434	[0, 0, 0, 0, 2, 0, -2, 0, 1, 0]
securitize	0.3	1.34536	[2, 1, 0, 0, 2, 0, 1, 0, -3, 0]
securitized	1.4	1.0198	[3, 0, 2, 2, 0, 1, 2, 2, 0, 2]
securitizes	1.6	1.0198	[3, 0, 2, 2, 0, 1, 3, 2, 1, 2]
securitizing	0.7	0.9	[2, 0, 0, 1, 2, 0, 2, 0, 0, 0]
security	1.4	0.8	[1, 2, 3, 2, 1, 1, 2, 0, 1, 1]
sedition	-1.8	1.249	[-3, -4, -2, -2, -2, -2, -1, -1, -2, 1]
seditious	-1.7	0.64031	[-1, -2, -2, -1, -1, -1, -3, -2, -2, -2]
seduced	-1.5	0.67082	[0, -1, -2, -2, -2, -2, -1, -1, -2, -2]
self-confident	2.5	0.80623	[1, 3, 3, 3, 2, 3, 1, 3, 3, 3]
selfish	-2.1	0.7	[-1, -2, -2, -3, -1, -2, -2, -2, -3, -3]
selfishly	-1.4	0.91652	[-3, 0, -1, -1, -1, -2, -3, -1, -1, -1]
selfishness	-1.7	0.64031	[-1, -1, -1, -2, -2, -1, -2, -2, -3, -2]
selfishnesses	-2.0	1.94936	[-4, -3, -1, -3, -2, -4, 2, 1, -3, -3]
sentence	0.3	0.64031	[0, 0, 0, 0, 1, 2, 0, 0, 0, 0]
sentenced	-0.1	1.3	[0, -2, 2, -1, 0, -2, 2, 0, 0, 0]
sentences	0.2	1.07703	[0, 0, 2, 0, 0, -2, 2, 0, 0, 0]
sentencing	-0.6	1.8	[-2, 0, -3, -2, 2, 3, 0, -1, -1, -2]
sentimental	1.3	0.64031	[2, 1, 1, 2, 1, 0, 1, 2, 2, 1]
sentimentalise	1.2	0.87178	[2, 1, 0, 3, 2, 1, 1, 0, 1, 1]
sentimentalised	0.8	1.16619	[2, 1, 1, 0, 0, 2, 3, -1, 0, 0]
sentimentalising	0.4	0.91652	[0, 2, 0, 0, 0, -1, 1, 0, 2, 0]
sentimentalism	1.0	0.63246	[2, 1, 0, 2, 1, 1, 1, 0, 1, 1]
sentimentalisms	0.4	0.8	[0, 1, 1, 0, 0, 2, 1, 0, 0, -1]
sentimentalist	0.8	0.87178	[2, 1, 0, 2, 0, 1, 2, 0, 0, 0]
sentimentalists	0.7	0.78102	[0, 0, 1, 0, 0, 1, 1, 2, 0, 2]
sentimentalities	0.9	0.83066	[2, 1, 1, 2, 1, 0, 0, 2, 0, 0]
sentimentality	1.2	1.46969	[-2, 1, 1, 2, 2, 0, 4, 2, 1, 1]
sentimentalization	1.2	0.87178	[0, 1, 2, 0, 1, 1, 3, 1, 2, 1]
sentimentalizations	0.4	0.8	[0, 1, 0, 1, 0, 0, 0, 2, -1, 1]
sentimentalize	0.8	1.07703	[2, 0, 0, 2, 0, 2, 1, -1, 2, 0]
sentimentalized	1.1	1.22066	[3, 0, 2, 0, 1, 3, 2, 0, 0, 0]
sentimentalizes	1.1	1.37477	[3, 0, 1, 0, 1, 4, 2, 0, 0, 0]
sentimentalizing	0.8	0.87178	[1, 1, 1, 1, 2, 0, -1, 1, 0, 2]
sentimentally	1.9	0.9434	[3, 2, 3, 1, 0, 2, 1, 2, 3, 2]
serene	2.0	1.0	[1, 1, 3, 2, 2, 4, 3, 2, 1, 1]
serious	-0.3	0.45826	[0, -1, 0, -1, -1, 0, 0, 0, 0, 0]
seriously	-0.7	1.34536	[-3, -2, 0, 0, -1, -2, 2, -1, 0, 0]
seriousness	-0.2	1.16619	[0, 2, 0, 0, 0, 0, -3, -1, 0, 0]
severe	-1.6	1.8	[-3, -2, -4, 1, -2, -3, 2, -1, -1, -3]
severed	-1.5	0.5	[-1, -1, -2, -1, -2, -1, -1, -2, -2, -2]
severely	-2.0	0.89443	[-2, -1, -1, -2, -2, -1, -3, -2, -4, -2]
severeness	-1.0	1.73205	[0, 0, -1, -4, 1, -2, 0, 1, -1, -4]
severer	-1.6	1.49666	[-2, -3, -2, -1, -2, 2, -4, -1, -1, -2]
severest	-1.5	1.85742	[-4, -1, 2, -2, -2, -3, -4, -1, 1, -1]
sexy	2.4	0.8	[2, 3, 3, 4, 2, 2, 2, 2, 3, 1]
shake	-0.7	0.9	[-2, 0, 0, -2, 1, -1, 0, -1, -1, -1]
shakeable	-0.3	1.00499	[2, -1, 0, 0, -1, -2, 0, 0, -1, 0]
shakedown	-1.2	0.4	[-2, -1, -1, -1, -2, -1, -1, -1, -1, -1]
shakedowns	-1.4	0.8	[-2, -1, -1, -3, -1, -1, 0, -2, -2, -1]
shaken	-0.3	0.9	[-1, -1, 1, -1, 1, 0, -1, 1, -1, -1]
shakeout	-1.3	0.78102	[-1, -2, 0, -1, -1, -2, -2, -2, 0, -2]
shakeouts	-0.8	1.16619	[-2, -1, 0, 0, -1, 2, -2, -1, -2, -1]
shakers	0.3	1.00499	[2, 0, 0, -1, 2, 0, 1, -1, 0, 0]
shakeup	-0.6	0.4899	[-1, 0, -1, -1, -1, 0, 0, 0, -1, -1]
shakeups	-0.5	0.92195	[2, -1, 0, 0, -1, -1, -1, -1, -1, -1]
shakier	-0.9	0.3	[-1, -1, 0, -1, -1, -1, -1, -1, -1, -1]
shakiest	-1.2	0.74833	[-1, 0, 0, -1, -1, -1, -2, -2, -2, -2]
shakily	-0.7	0.45826	[-1, -1, -1, -1, -1, 0, 0, 0, -1, -1]
shakiness	-0.7	1.1	[2, -1, -1, -1, -2, -1, -1, -2, 0, 0]
shaking	-0.7	0.45826	[-1, -1, -1, 0, -1, -1, 0, 0, -1, -1]
shaky	-0.9	0.3	[-1, -1, -1, 0, -1, -1, -1, -1, -1, -1]
shame	-2.1	0.53852	[-2, -2, -2, -3, -3, -1, -2, -2, -2, -2]
shamed	-2.6	0.4899	[-2, -3, -3, -2, -3, -3, -3, -2, -2, -3]
shamefaced	-2.3	0.64031	[-2, -2, -2, -3, -3, -3, -3, -1, -2, -2]
shamefacedly	-1.9	0.3	[-2, -2, -2, -2, -2, -2, -2, -1, -2, -2]
shamefacedness	-2.0	0.89443	[-1, -1, -2, -2, -2, -1, -2, -4, -2, -3]
shamefast	-1.0	0.44721	[-1, -1, 0, -1, -1, -1, -1, -2, -1, -1]
shameful	-2.2	0.6	[-3, -3, -2, -2, -2, -3, -1, -2, -2, -2]
shamefully	-1.9	0.7	[-2, -3, -1, -3, -1, -2, -2, -1, -2, -2]
shamefulness	-2.4	0.4899	[-2, -3, -3, -2, -3, -2, -3, -2, -2, -2]
shamefulnesses	-2.3	0.78102	[-4, -2, -2, -2, -3, -1, -2, -2, -3, -2]
shameless	-1.4	1.0198	[-1, -1, 1, -2, -3, -1, -2, -2, -1, -2]
shamelessly	-1.4	1.0198	[-1, 0, -1, -2, 0, -1, -3, -3, -1, -2]
shamelessness	-1.4	1.0198	[-1, -3, -2, 1, -2, -1, -2, -2, -1, -1]
shamelessnesses	-2.0	1.0	[-2, 0, -2, -4, -2, -1, -2, -3, -2, -2]
shames	-1.7	0.9	[-3, -3, -3, -1, -1, -1, -1, -1, -1, -2]
share	1.2	0.74833	[0, 1, 1, 2, 2, 2, 1, 1, 0, 2]
shared	1.4	0.4899	[2, 2, 2, 1, 2, 1, 1, 1, 1, 1]
shares	1.2	0.87178	[0, 2, 1, 1, 0, 2, 2, 2, 2, 0]
sharing	1.8	0.6	[2, 2, 1, 2, 2, 3, 1, 1, 2, 2]
shattered	-2.1	0.7	[-1, -3, -3, -2, -2, -2, -3, -2, -1, -2]
shit	-2.6	1.0198	[-2, -1, -4, -3, -4, -4, -2, -2, -2, -2]
shitake	-0.3	1.26886	[-4, 0, 0, 0, 0, 0, 0, 0, 1, 0]
shitakes	-1.1	1.7	[0, -4, 0, 0, 0, 0, -3, 0, -4, 0]
shithead	-3.1	0.83066	[-3, -4, -4, -3, -4, -3, -1, -3, -3, -3]
shitheads	-2.6	1.35647	[-2, -3, -4, -3, -3, -3, -2, -4, 1, -3]
shits	-2.1	1.22066	[-3, 0, -3, 0, -2, -4, -2, -2, -2, -3]
shittah	0.1	1.3	[-2, -2, 0, 2, 0, 0, 0, 0, 1, 2]
shitted	-1.7	0.64031	[-2, -1, -2, -1, -2, -2, -3, -1, -2, -1]
shittier	-2.1	0.83066	[-3, -3, -2, -1, -1, -1, -3, -2, -2, -3]
shittiest	-3.4	0.66332	[-3, -4, -3, -4, -4, -4, -3, -3, -4, -2]
shittim	-0.6	1.0198	[-3, 0, 0, 0, 0, -2, 0, 0, -1, 0]
shittimwood	-0.3	0.9	[0, 0, -3, 0, 0, 0, 0, 0, 0, 0]
shitting	-1.8	0.9798	[-1, -2, -2, -1, -3, -1, -1, -2, -4, -1]
shitty	-2.6	0.8	[-3, -4, -2, -3, -3, -2, -3, -1, -2, -3]
shock	-1.6	0.91652	[0, -3, -1, -2, -1, -2, -2, -1, -3, -1]
shockable	-1.0	1.0	[-3, -1, -1, 0, 0, -2, -1, -2, 0, 0]
shocked	-1.3	1.18743	[-2, -1, -1, 0, 0, -2, 0, -2, -1, -4]
shocker	-0.6	1.49666	[-2, -1, 3, -1, 0, -3, -1, 0, -1, 0]
shockers	-1.1	0.9434	[-1, -2, -2, -1, -1, 0, 0, -3, 0, -1]
shocking	-1.7	1.34536	[-3, -3, 0, -1, 0, -3, 0, -1, -3, -3]
shockingly	-0.7	1.48661	[-2, 0, -1, -3, -3, 0, 2, 0, 0, 0]
shockproof	1.3	0.64031	[1, 0, 1, 1, 2, 2, 1, 2, 1, 2]
shocks	-1.6	0.91652	[-3, -1, 0, -1, -2, -2, -1, -3, -2, -1]
shook	-0.4	0.66332	[-1, -2, 0, 0, 0, 0, 0, 0, 0, -1]
shoot	-1.4	1.11355	[-1, -4, -1, -1, -2, 0, -2, 0, -1, -2]
short-sighted	-1.2	0.6	[0, -1, -2, -2, -1, -1, -1, -2, -1, -1]
short-sightedness	-1.1	1.37477	[-1, -2, -2, -2, -3, -1, 0, 2, 0, -2]
shortage	-1.0	1.09545	[-2, -2, -1, -2, -1, -1, 2, -1, -1, -1]
shortages	-0.6	1.0198	[0, -2, -1, -1, 0, -2, 1, 1, -1, -1]
shrew	-0.9	1.04403	[-2, -1, -2, -2, 1, 0, 0, 0, -1, -2]
shy	-1.0	0.0	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
shyer	-0.8	0.6	[-1, -1, -1, -1, 0, -1, 0, 0, -2, -1]
shying	-0.9	0.83066	[-1, -2, -1, 0, -2, 0, 0, 0, -2, -1]
shylock	-2.1	1.13578	[-2, -2, -2, 0, -3, -3, -3, -3, 0, -3]
shylocked	-0.7	1.00499	[0, -3, -1, 0, 0, -2, 0, -1, 0, 0]
shylocking	-1.5	1.11803	[0, -2, -3, 0, -1, -3, -2, 0, -2, -2]
shylocks	-1.4	1.11355	[0, -2, -1, -2, -3, 0, -3, -1, -2, 0]
shyly	-0.7	0.45826	[-1, -1, -1, -1, 0, 0, -1, 0, -1, -1]
shyness	-1.3	1.00499	[-2, 0, -3, 0, -1, -2, -2, -1, -2, 0]
shynesses	-1.2	0.6	[-2, -1, -1, -2, -1, -2, 0, -1, -1, -1]
shyster	-1.6	0.66332	[-2, -1, -2, -3, -2, -1, -1, -1, -1, -2]
shysters	-0.9	0.7	[0, -2, 0, 0, -2, -1, -1, -1, -1, -1]
sick	-2.3	0.78102	[-1, -2, -3, -2, -2, -4, -3, -2, -2, -2]
sicken	-1.9	0.7	[-2, -3, -1, -3, -1, -1, -2, -2, -2, -2]
sickened	-2.5	0.92195	[-3, -2, -4, -3, -1, -1, -3, -3, -2, -3]
sickener	-2.2	0.87178	[-3, -1, -2, -3, -1, -1, -3, -3, -2, -3]
sickeners	-2.2	0.6	[-3, -1, -2, -3, -2, -3, -2, -2, -2, -2]
sickening	-2.4	0.91652	[-3, -4, -1, -3, -2, -2, -1, -3, -2, -3]
sickeningly	-2.1	0.7	[-3, -3, -1, -2, -2, -1, -3, -2, -2, -2]
sickens	-2.0	0.63246	[-2, -1, -2, -2, -3, -2, -1, -2, -3, -2]
sigh	0.1	1.22066	[2, -1, -1, -1, 0, 2, 1, 1, -1, -1]
significance	1.1	1.22066	[0, 0, 0, 1, 3, 2, 3, 2, 0, 0]
significant	0.8	0.9798	[1, 0, 1, 3, 0, 1, 0, 0, 2, 0]
silencing	-0.5	0.67082	[-2, -1, 0, 0, 0, 0, 0, -1, 0, -1]
sillibub	-0.1	0.3	[0, 0, 0, 0, -1, 0, 0, 0, 0, 0]
sillier	1.0	0.7746	[1, 1, 0, 2, 0, 1, 1, 2, 2, 0]
sillies	0.8	0.74833	[2, 1, 0, 1, 0, 1, 0, 2, 1, 0]
silliest	0.8	0.9798	[0, 1, 1, 1, 1, 0, 3, 1, -1, 1]
sillily	-0.1	1.04403	[1, -1, -1, -1, 2, 1, -1, 0, 0, -1]
sillimanite	0.1	0.3	[1, 0, 0, 0, 0, 0, 0, 0, 0, 0]
sillimanites	0.2	0.6	[0, 0, 0, 0, 2, 0, 0, 0, 0, 0]
silliness	-0.9	1.22066	[1, -3, -1, 0, 1, -1, -2, -1, -2, -1]
sillinesses	-1.2	1.46969	[-3, -2, -3, 0, -2, 0, 1, -2, -2, 1]
silly	0.1	1.04403	[1, 1, 0, 0, 0, 2, -1, -2, 0, 0]
sin	-2.6	0.8	[-2, -4, -3, -2, -2, -2, -4, -3, -2, -2]
sincere	1.7	0.78102	[1, 2, 3, 0, 2, 2, 2, 2, 1, 2]
sincerely	2.1	1.04403	[1, 3, 2, 4, 2, 3, 1, 1, 3, 1]
sincereness	1.8	0.74833	[3, 1, 1, 2, 2, 3, 1, 2, 2, 1]
sincerer	2.0	1.0	[1, 3, 3, 2, 3, 2, 1, 3, 0, 2]
sincerest	2.0	1.34164	[0, 3, 1, 1, 4, 2, 1, 4, 1, 3]
sincerities	1.5	0.67082	[2, 1, 1, 1, 1, 1, 3, 2, 2, 1]
sinful	-2.6	0.8	[-4, -3, -3, -2, -1, -2, -3, -2, -3, -3]
singleminded	1.2	0.87178	[1, 2, 0, 0, 3, 2, 1, 1, 1, 1]
sinister	-2.9	1.13578	[-4, -4, -1, -3, -1, -4, -3, -3, -2, -4]
sins	-2.0	1.0	[-2, -1, -2, -3, -1, -4, -3, -2, -1, -1]
skeptic	-0.9	1.13578	[0, 0, -1, -1, -1, 0, -2, -2, 1, -3]
skeptical	-1.3	0.9	[0, -3, -2, -2, -1, -2, 0, -1, -1, -1]
skeptically	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -2, -1, -1, -2]
skepticism	-1.0	0.89443	[0, 0, -1, -1, -1, -1, 0, -1, -3, -2]
skepticisms	-1.2	0.9798	[1, -2, 0, -1, -2, -1, -2, -2, -1, -2]
skeptics	-0.4	0.91652	[-2, 0, 0, -1, -1, 1, -1, 0, 1, -1]
slam	-1.6	1.11355	[-2, -1, -3, -1, -3, -2, -1, -2, 1, -2]
slash	-1.1	1.22066	[-2, -3, -2, 0, -2, 0, -1, -2, 0, 1]
slashed	-0.9	0.83066	[-2, -1, -2, -1, -1, 0, 1, -1, -1, -1]
slashes	-0.8	0.6	[0, -1, -2, -1, 0, 0, -1, -1, -1, -1]
slashing	-1.1	1.86815	[-3, -2, -3, -3, 3, 1, 0, -1, -2, -1]
slavery	-3.8	0.4	[-4, -3, -4, -3, -4, -4, -4, -4, -4, -4]
sleeplessness	-1.6	1.49666	[-3, -4, -2, -1, -2, -2, -2, 2, -1, -1]
slicker	0.4	1.28062	[-2, 1, -1, 2, 0, 0, 2, 2, 0, 0]
slickest	0.3	1.00499	[1, 0, -1, 0, -1, 0, 0, 0, 2, 2]
sluggish	-1.7	0.64031	[-1, -2, -1, -3, -2, -2, -1, -2, -1, -2]
slut	-2.8	0.87178	[-4, -2, -2, -1, -3, -3, -3, -3, -4, -3]
sluts	-2.7	1.48661	[-3, -3, -4, -4, -3, -4, -1, -3, 1, -3]
sluttier	-2.7	1.18743	[-2, -2, -4, -3, -4, -4, -1, -1, -2, -4]
sluttiest	-3.1	0.83066	[-3, -4, -3, -4, -3, -4, -2, -2, -4, -2]
sluttish	-2.2	0.87178	[-2, -3, -3, -2, -2, -4, -2, -1, -1, -2]
sluttishly	-2.1	1.13578	[-2, 0, -2, -3, -1, -2, -3, -1, -3, -4]
sluttishness	-2.5	0.92195	[-2, -1, -3, -4, -2, -1, -3, -3, -3, -3]
sluttishnesses	-2.0	1.09545	[0, -2, -1, -3, -3, -1, -3, -1, -3, -3]
slutty	-2.3	0.9	[-3, -1, -3, -3, -3, -2, -1, -3, -1, -3]
smart	1.7	0.78102	[2, 2, 1, 2, 1, 3, 2, 0, 2, 2]
smartass	-2.1	0.83066	[-1, -1, -2, -3, -2, -1, -2, -3, -3, -3]
smartasses	-1.7	2.05183	[-1, -2, -3, -3, -3, -1, -2, -3, 4, -3]
smarted	0.7	1.41774	[2, 1, -2, 1, 1, 2, 2, 2, -1, -1]
smarten	1.9	0.7	[3, 1, 2, 1, 2, 2, 2, 3, 1, 2]
smartened	1.5	0.67082	[2, 2, 1, 3, 1, 1, 1, 1, 2, 1]
smartening	1.7	0.9	[1, 3, 3, 1, 1, 1, 2, 1, 1, 3]
smartens	1.5	0.5	[2, 2, 1, 1, 1, 1, 2, 2, 2, 1]
smarter	2.0	0.7746	[2, 2, 1, 1, 2, 2, 2, 2, 4, 2]
smartest	3.0	1.0	[4, 3, 4, 3, 2, 2, 4, 3, 1, 4]
smartie	1.3	0.9	[1, 4, 1, 1, 1, 1, 1, 1, 1, 1]
smarties	1.7	0.9	[1, 1, 2, 4, 2, 2, 1, 2, 1, 1]
smarting	-0.7	1.9	[2, 3, -1, -2, 1, -2, -1, -3, -2, -2]
smartly	1.5	0.67082	[2, 1, 1, 1, 1, 2, 1, 3, 1, 2]
smartness	2.0	0.89443	[1, 3, 3, 1, 2, 1, 2, 3, 1, 3]
smartnesses	1.5	0.92195	[1, 0, 1, 1, 1, 2, 1, 2, 3, 3]
smarts	1.6	0.66332	[2, 1, 3, 1, 2, 1, 2, 1, 2, 1]
smartweed	0.2	0.6	[0, 0, 0, 0, 0, 2, 0, 0, 0, 0]
smartweeds	0.1	0.53852	[0, 0, -1, 0, 0, 0, 1, 0, 0, 1]
smarty	1.1	0.53852	[0, 2, 1, 2, 1, 1, 1, 1, 1, 1]
smear	-1.5	1.20416	[-2, -1, -1, -2, -3, 0, 0, -4, -1, -1]
smilax	0.6	0.66332	[0, 2, 1, 0, 0, 0, 1, 1, 1, 0]
smilaxes	0.3	0.78102	[0, 0, 0, 0, 1, 2, 1, -1, 0, 0]
smile	1.5	0.67082	[2, 1, 1, 1, 2, 2, 3, 1, 1, 1]
smiled	2.5	0.80623	[3, 2, 3, 2, 1, 2, 3, 4, 3, 2]
smileless	-1.4	0.4899	[-1, -1, -1, -1, -2, -1, -2, -1, -2, -2]
smiler	1.7	0.78102	[1, 1, 3, 1, 3, 2, 2, 2, 1, 1]
smiles	2.1	1.04403	[2, 4, 2, 1, 3, 1, 1, 3, 1, 3]
smiley	1.7	0.78102	[1, 2, 1, 2, 2, 0, 2, 3, 2, 2]
smileys	1.5	0.92195	[1, 2, 1, 3, 1, 0, 1, 3, 1, 2]
smiling	2.0	1.18322	[2, 1, 1, 1, 2, 3, 4, 1, 4, 1]
smilingly	2.3	0.64031	[3, 2, 3, 2, 3, 1, 2, 2, 2, 3]
smog	-1.2	0.6	[-1, -1, -2, -1, -1, -2, -2, -1, 0, -1]
smother	-1.8	0.87178	[-2, -2, -3, -1, -2, -1, -2, -2, -3, 0]
smothered	-0.9	1.7	[2, -4, 0, -1, -3, -2, 0, -1, 1, -1]
smothering	-1.4	1.56205	[-2, -3, -1, -4, 0, -2, -1, 2, -1, -2]
smothers	-1.9	1.04403	[-1, -1, -3, -1, -2, -3, 0, -2, -3, -3]
smothery	-1.1	0.7	[-2, 0, -2, -1, -2, -1, 0, -1, -1, -1]
smug	0.8	1.16619	[-1, 2, 0, 2, 1, -1, 2, 1, 0, 2]
smugger	-1.0	0.89443	[1, -2, -1, -2, -1, -1, -1, 0, -2, -1]
smuggest	-1.5	1.28452	[-1, -1, -1, 1, -1, -3, -4, -2, -1, -2]
smuggle	-1.6	1.11355	[-2, -1, -1, -1, -2, 0, -1, -1, -3, -4]
smuggled	-1.5	0.92195	[-1, -1, -2, -1, -1, -1, -2, -1, -1, -4]
smuggler	-2.1	1.22066	[-1, -3, -3, -1, -1, -1, -4, -1, -2, -4]
smugglers	-1.4	1.56205	[-2, -3, -4, 1, -2, 0, -2, 1, -1, -2]
smuggles	-1.7	1.00499	[-1, -2, -2, -1, -1, -1, -3, -1, -1, -4]
smuggling	-2.1	0.83066	[-3, -2, -2, -1, -2, -2, -1, -2, -4, -2]
smugly	0.2	1.249	[-1, 2, -1, -1, 1, 1, -1, 1, 2, -1]
smugness	-1.4	1.11355	[-1, -1, -1, -3, -3, -2, 1, -1, -2, -1]
smugnesses	-1.7	0.78102	[-1, -1, -2, -1, -3, -1, -2, -2, -3, -1]
sneaky	-0.9	0.7	[-1, -1, -1, -2, -1, -1, -1, 1, -1, -1]
snob	-2.0	0.63246	[-2, -2, -2, -2, -2, -1, -3, -1, -3, -2]
snobbery	-2.0	0.63246	[-1, -2, -2, -2, -2, -3, -2, -1, -3, -2]
snobbier	-0.7	1.00499	[-1, 2, -1, -1, -1, -1, -1, 0, -2, -1]
snobbiest	-0.5	1.11803	[-2, -2, 1, -1, 1, -1, 0, -1, 1, -1]
snobbily	-1.6	1.11355	[-1, -2, -2, -2, -1, -3, -3, -1, 1, -2]
snobbish	-0.9	1.37477	[-2, -1, 1, -1, 1, -2, -3, -2, 1, -1]
snobbishly	-1.2	1.249	[-2, 1, -2, -1, -2, -3, -1, -2, -1, 1]
snobbishness	-1.1	1.22066	[-1, -2, -2, -1, -3, -1, -1, -1, 2, -1]
snobbishnesses	-1.7	0.78102	[-2, -1, -3, -2, -1, -1, -2, -1, -3, -1]
snobbism	-1.0	1.41421	[-2, -1, 1, -1, 1, -2, -3, -2, 1, -2]
snobbisms	-0.3	1.18743	[1, -1, -1, 0, -1, -1, 1, -2, 2, -1]
snobby	-1.7	1.00499	[-1, -1, -1, -3, -2, -2, 0, -3, -3, -1]
snobs	-1.4	1.0198	[-2, -1, -1, -2, -2, -3, -1, -2, 1, -1]
snub	-1.8	0.4	[-2, -2, -1, -2, -2, -2, -2, -2, -1, -2]
snubbed	-2.0	0.7746	[-2, -2, 0, -2, -3, -2, -2, -2, -3, -2]
snubbing	-0.9	1.13578	[-2, -1, -1, -1, 1, -1, -3, 1, -1, -1]
snubs	-2.1	0.9434	[-2, -1, -1, -1, -3, -2, -4, -3, -2, -2]
sob	-1.0	1.34164	[-1, -2, -2, -2, -1, -1, 2, 1, -2, -2]
sobbed	-1.9	1.3	[-2, -4, 1, -1, -3, -1, -3, -2, -2, -2]
sobbing	-1.6	1.49666	[1, -2, -3, -1, -1, -3, 1, -2, -3, -3]
sobering	-0.8	1.32665	[2, -2, 0, -2, -1, -1, -2, 1, -1, -2]
sobs	-2.5	0.67082	[-3, -3, -2, -2, -3, -3, -3, -1, -2, -3]
sociabilities	1.2	0.9798	[1, 3, 1, 2, 0, 1, 0, 2, 2, 0]
sociability	1.1	0.9434	[0, 1, 1, 1, 0, 2, 1, 0, 2, 3]
sociable	1.9	0.7	[1, 1, 2, 3, 1, 2, 2, 2, 3, 2]
sociableness	1.5	0.5	[1, 1, 1, 2, 2, 2, 1, 2, 1, 2]
sociably	1.6	0.91652	[3, 2, 0, 1, 3, 2, 1, 1, 2, 1]
sok	1.3	1.1	[0, 1, 2, 1, 1, 0, 1, 4, 2, 1]
solemn	-0.3	1.1	[-1, -2, -1, 0, -1, 2, 0, -1, 1, 0]
solemnified	-0.5	0.67082	[0, 0, 0, 0, -1, -1, -1, 0, -2, 0]
solemnifies	-0.5	0.67082	[0, 0, 0, 0, -1, -2, -1, 0, -1, 0]
solemnify	0.3	1.18743	[2, 1, 2, 1, 0, 0, -2, -1, 0, 0]
solemnifying	0.1	1.3	[2, -1, 0, -3, 0, 1, 1, 1, 0, 0]
solemnities	0.3	0.78102	[0, 1, -1, 0, 0, 0, 2, 1, 0, 0]
solemnity	-1.1	0.9434	[0, -2, 0, -2, -2, -1, -2, 0, 0, -2]
solemnization	0.7	1.34536	[-1, -1, 0, 2, 2, 0, 3, 2, 0, 0]
solemnize	0.3	0.78102	[1, -1, 0, 2, 0, 0, 1, 0, 0, 0]
solemnized	-0.7	0.9	[0, 0, 0, -1, -1, -1, -1, 0, 0, -3]
solemnizes	0.6	0.91652	[0, 0, 0, 0, 0, 0, 2, 2, 2, 0]
solemnizing	-0.6	0.8	[0, 0, -1, -1, 0, 0, -2, -2, 0, 0]
solemnly	0.8	0.9798	[0, 2, 0, 1, 1, 2, -1, 2, 1, 0]
solid	0.6	0.8	[0, 2, 1, 2, 0, 1, 0, 0, 0, 0]
solidarity	1.2	0.6	[1, 1, 1, 1, 2, 0, 1, 2, 2, 1]
solution	1.3	0.64031	[1, 2, 1, 2, 1, 1, 2, 2, 0, 1]
solutions	0.7	0.78102	[1, 1, 2, 0, 1, 0, 2, 0, 0, 0]
solve	0.8	0.4	[0, 1, 1, 1, 1, 0, 1, 1, 1, 1]
solved	1.1	0.53852	[1, 2, 1, 0, 1, 1, 1, 2, 1, 1]
solves	1.1	0.7	[2, 0, 1, 0, 1, 2, 1, 2, 1, 1]
solving	1.4	0.8	[0, 3, 1, 2, 1, 1, 2, 1, 2, 1]
somber	-1.8	0.6	[-1, -2, -2, -3, -2, -1, -2, -1, -2, -2]
son-of-a-bitch	-2.7	0.64031	[-3, -3, -3, -1, -2, -3, -3, -3, -3, -3]
soothe	1.5	0.92195	[3, 2, 2, 1, 2, 0, 2, 0, 2, 1]
soothed	0.5	1.43178	[2, -3, 1, 1, 0, 1, 1, 1, 2, -1]
soothing	1.3	0.64031	[2, 1, 2, 1, 1, 1, 2, 0, 2, 1]
sophisticated	2.6	0.91652	[3, 4, 3, 3, 3, 1, 2, 1, 3, 3]
sore	-1.5	0.5	[-2, -2, -2, -1, -1, -1, -2, -1, -1, -2]
sorrow	-2.4	0.8	[-2, -2, -2, -1, -2, -3, -2, -4, -3, -3]
sorrowed	-2.4	0.8	[-1, -2, -3, -3, -2, -3, -2, -4, -2, -2]
sorrower	-2.3	0.78102	[-1, -2, -1, -3, -3, -2, -2, -3, -3, -3]
sorrowful	-2.2	0.6	[-3, -3, -2, -2, -2, -2, -3, -1, -2, -2]
sorrowfully	-2.3	0.64031	[-3, -3, -2, -1, -2, -2, -3, -2, -2, -3]
sorrowfulness	-2.5	0.67082	[-4, -3, -2, -3, -2, -3, -2, -2, -2, -2]
sorrowing	-1.7	1.26886	[-2, -2, 1, -1, -1, -2, -4, -3, -2, -1]
sorrows	-1.6	0.66332	[-1, -1, -2, -1, -2, -2, -1, -2, -1, -3]
sorry	-0.3	1.61555	[-1, 0, -1, -2, -1, -1, 4, 1, -1, -1]
soulmate	2.9	0.83066	[4, 2, 2, 2, 3, 4, 3, 3, 2, 4]
spam	-1.5	1.0247	[-1, -1, -1, -2, 1, -2, -3, -2, -2, -2]
spammer	-2.2	0.6	[-3, -2, -2, -1, -2, -3, -2, -2, -3, -2]
spammers	-1.6	1.11355	[-2, -1, -2, -3, -2, -1, -2, -1, 1, -3]
spamming	-2.1	0.83066	[-3, -3, -1, -3, -2, -1, -2, -2, -1, -3]
spark	0.9	1.04403	[0, 2, 1, 1, 0, 1, 3, -1, 1, 1]
sparkle	1.8	0.74833	[0, 2, 2, 2, 1, 2, 3, 2, 2, 2]
sparkles	1.3	1.18743	[3, 2, 0, 0, 3, 0, 2, 0, 1, 2]
sparkling	1.2	0.4	[1, 1, 1, 2, 1, 1, 1, 2, 1, 1]
special	1.7	0.78102	[3, 1, 3, 2, 2, 1, 1, 2, 1, 1]
speculative	0.4	1.11355	[1, -1, 1, 0, -1, -1, 1, 2, 2, 0]
spirit	0.7	1.00499	[0, 0, 1, 2, 3, 1, 0, 0, 0, 0]
spirited	1.3	1.00499	[1, 1, 0, 1, 1, 3, 2, 3, 0, 1]
spiritless	-1.3	0.64031	[-2, -1, -1, -2, -2, -1, 0, -1, -2, -1]
spite	-2.4	0.8	[-2, -2, -3, -2, -3, -2, -4, -1, -3, -2]
spited	-2.4	0.91652	[-2, -3, -3, -4, -2, -1, -2, -1, -3, -3]
spiteful	-1.9	1.75784	[-2, 3, -2, -3, -2, -3, -2, -2, -4, -2]
spitefully	-2.3	0.78102	[-2, -2, -1, -3, -3, -2, -2, -2, -4, -2]
spitefulness	-1.5	1.74642	[-3, -2, 1, -3, -3, -3, -2, 2, 0, -2]
spitefulnesses	-2.3	0.9	[-1, -4, -2, -3, -3, -2, -2, -3, -1, -2]
spites	-1.4	1.28062	[0, -2, -2, -3, -2, -1, 1, 0, -3, -2]
splendent	2.7	0.78102	[3, 2, 1, 3, 2, 3, 4, 3, 3, 3]
splendid	2.8	0.9798	[4, 2, 3, 4, 2, 2, 3, 1, 3, 4]
splendidly	2.1	1.22066	[1, 4, 1, 3, 3, 4, 1, 2, 1, 1]
splendidness	2.3	0.9	[2, 1, 3, 3, 4, 2, 1, 3, 2, 2]
splendiferous	2.6	1.95959	[4, 4, 3, -3, 3, 3, 4, 2, 3, 3]
splendiferously	1.9	1.3	[2, 4, 4, 3, 0, 1, 1, 1, 2, 1]
splendiferousness	1.7	1.18743	[1, 2, 3, 1, 2, 3, 3, 2, -1, 1]
splendor	3.0	0.63246	[3, 3, 3, 3, 3, 2, 4, 4, 3, 2]
splendorous	2.2	0.87178	[4, 2, 1, 2, 2, 3, 3, 1, 2, 2]
splendors	2.0	0.44721	[2, 2, 2, 2, 3, 2, 2, 2, 1, 2]
splendour	2.2	0.6	[2, 2, 2, 3, 3, 1, 2, 3, 2, 2]
splendours	2.2	1.249	[3, 0, 2, 1, 4, 3, 2, 4, 1, 2]
splendrous	2.2	1.16619	[4, 4, 3, 2, 1, 1, 3, 1, 2, 1]
sprightly	2.0	0.89443	[0, 2, 3, 2, 2, 1, 2, 3, 3, 2]
squelched	-1.0	0.63246	[-1, -1, -1, -1, 0, -2, -1, 0, -1, -2]
stab	-2.8	0.6	[-3, -2, -2, -3, -3, -3, -3, -2, -4, -3]
stabbed	-1.9	1.22066	[-2, -1, -3, -2, -2, 1, -3, -3, -1, -3]
stable	1.2	0.74833	[1, 2, 2, 0, 1, 2, 1, 1, 2, 0]
stabs	-1.9	1.13578	[-3, -3, -4, 0, -1, -2, -2, -2, -1, -1]
stall	-0.8	0.74833	[-1, 0, -1, 0, -1, 0, -2, 0, -2, -1]
stalled	-0.8	0.87178	[-2, -1, 0, -1, -1, 0, 1, -2, -1, -1]
stalling	-0.8	1.4	[-3, -2, -1, -1, 0, 2, 1, -2, -1, -1]
stamina	1.2	0.9798	[1, 0, 3, 0, 1, 2, 2, 1, 2, 0]
stammer	-0.9	0.3	[-1, 0, -1, -1, -1, -1, -1, -1, -1, -1]
stammered	-0.9	0.7	[-2, -1, -1, -1, 1, -1, -1, -1, -1, -1]
stammerer	-1.1	0.3	[-1, -2, -1, -1, -1, -1, -1, -1, -1, -1]
stammerers	-0.8	0.4	[-1, -1, -1, -1, -1, 0, 0, -1, -1, -1]
stammering	-1.0	0.63246	[-2, -1, 0, -1, -1, -2, 0, -1, -1, -1]
stammers	-0.8	0.4	[-1, -1, -1, 0, -1, -1, -1, 0, -1, -1]
stampede	-1.8	1.07703	[-2, -3, -2, -3, 0, 0, -2, -1, -2, -3]
stank	-1.9	1.04403	[-2, -2, -2, -1, -2, -4, -3, 0, -1, -2]
startle	-1.3	0.64031	[-1, -1, -1, -1, -1, -2, -1, -1, -3, -1]
startled	-0.7	0.78102	[-2, -1, -1, 0, 1, -1, -1, 0, -1, -1]
startlement	-0.5	1.20416	[-1, 0, 0, 1, -1, -1, 2, -2, -1, -2]
startlements	0.2	1.32665	[2, 2, 0, 0, -1, 2, 0, -2, -1, 0]
startler	-0.8	0.74833	[-2, -1, -1, 0, 1, -1, -1, -1, -1, -1]
startlers	-0.5	0.80623	[0, 0, 0, 0, -1, -1, -1, 1, -1, -2]
startles	-0.5	1.36015	[-2, 2, -1, 0, 2, -1, -1, -2, -1, -1]
startling	0.3	1.41774	[-2, 2, -1, 0, -1, 2, -1, 1, 1, 2]
startlingly	-0.3	0.9	[-1, -1, 0, -2, 1, 0, 1, 0, -1, 0]
starve	-1.9	2.02237	[2, -3, -4, -1, -3, -4, -1, -2, 1, -4]
starved	-2.6	1.11355	[-3, -4, -1, -3, -3, -4, -1, -1, -3, -3]
starves	-2.3	0.78102	[-3, -2, -2, -3, -2, -3, -1, -1, -3, -3]
starving	-1.8	2.03961	[-2, -2, -4, -4, -3, -3, 2, 2, -2, -2]
steadfast	1.0	1.0	[0, 0, 2, 1, 1, 2, 3, 0, 1, 0]
steal	-2.2	0.6	[-2, -2, -2, -3, -3, -3, -1, -2, -2, -2]
stealable	-1.7	1.00499	[-3, -1, -2, -1, -2, -1, -1, -1, -4, -1]
stealer	-1.7	0.78102	[-2, -2, -1, -2, -3, -1, -2, -2, -2, 0]
stealers	-2.2	0.74833	[-2, -2, -2, -4, -2, -2, -3, -1, -2, -2]
stealing	-2.7	0.9	[-3, -2, -2, -4, -4, -3, -1, -3, -2, -3]
stealings	-1.9	0.9434	[-2, -2, -1, -1, -1, -3, -4, -2, -1, -2]
steals	-2.3	0.64031	[-4, -2, -2, -3, -2, -2, -2, -2, -2, -2]
stealth	-0.3	1.34536	[-2, 1, 1, -1, 0, -3, 0, 1, -1, 1]
stealthier	-0.3	1.00499	[0, 0, -3, 0, 0, 1, 0, -1, 0, 0]
stealthiest	0.4	2.10713	[-1, 2, 1, 1, 3, 4, -1, 0, -2, -3]
stealthily	0.1	1.37477	[2, 0, 0, -1, 1, 0, -3, 2, 0, 0]
stealthiness	0.2	0.74833	[1, 0, 0, -1, 1, 0, -1, 0, 1, 1]
stealths	-0.3	0.78102	[-2, 0, 0, -1, 0, 1, -1, 0, 0, 0]
stealthy	-0.1	0.7	[0, 0, 0, -1, 1, -1, -1, 0, 1, 0]
stench	-2.3	0.64031	[-3, -2, -2, -3, -3, -1, -3, -2, -2, -2]
stenches	-1.5	1.11803	[-2, -2, -1, -3, 0, 0, -3, -2, 0, -2]
stenchful	-2.4	0.91652	[-3, -1, -3, -1, -2, -2, -3, -4, -2, -3]
stenchy	-2.3	1.00499	[-4, -1, -2, -3, -1, -1, -2, -3, -3, -3]
stereotype	-1.3	0.78102	[-1, -1, -2, 0, -2, 0, -2, -1, -2, -2]
stereotyped	-1.2	0.4	[-1, -1, -2, -1, -2, -1, -1, -1, -1, -1]
stifled	-1.4	0.66332	[-1, -1, -1, -1, -3, -1, -2, -2, -1, -1]
stimulate	0.9	0.83066	[1, 0, 1, 1, 1, 2, -1, 2, 1, 1]
stimulated	0.9	0.7	[1, 0, 0, 0, 1, 1, 2, 1, 2, 1]
stimulates	1.0	0.89443	[1, 0, 0, 0, 1, 1, 2, 1, 3, 1]
stimulating	1.9	0.7	[2, 3, 2, 1, 3, 2, 1, 2, 1, 2]
stingy	-1.6	0.8	[-1, 0, -2, -2, -1, -1, -2, -2, -3, -2]
stink	-1.7	0.64031	[-2, -2, -1, -3, -2, -1, -1, -2, -1, -2]
stinkard	-2.3	0.9	[-2, -3, -3, -2, -3, -2, -3, -3, 0, -2]
stinkards	-1.0	1.26491	[-2, -1, 2, -2, -1, -1, 0, -3, -1, -1]
stinkbug	-0.2	0.4	[-1, 0, 0, 0, 0, 0, 0, 0, -1, 0]
stinkbugs	-1.0	1.26491	[0, 0, 0, -4, -1, -2, -1, 0, -2, 0]
stinker	-1.5	0.80623	[-3, -1, -3, -1, -1, -2, -1, -1, -1, -1]
stinkers	-1.2	1.07703	[-3, 1, -1, -2, -2, -1, -1, -2, 0, -1]
stinkhorn	-0.2	1.16619	[-2, -2, 0, 0, -1, 0, 1, 2, 0, 0]
stinkhorns	-0.8	0.9798	[0, -3, 0, -2, 0, 0, -1, -1, -1, 0]
stinkier	-1.5	1.0247	[-2, -1, -2, -2, -1, 1, -2, -1, -3, -2]
stinkiest	-2.1	1.57797	[-1, -2, -3, -4, -2, -1, 1, -4, -1, -4]
stinking	-2.4	0.91652	[-2, -4, -2, -3, -3, -1, -2, -1, -3, -3]
stinkingly	-1.3	1.55242	[-2, -3, -3, -2, -2, 1, -2, 2, -1, -1]
stinko	-1.5	0.80623	[-1, -2, -2, -2, 0, -1, -2, -1, -1, -3]
stinkpot	-2.5	0.92195	[-4, -1, -3, -4, -2, -2, -3, -2, -2, -2]
stinkpots	-0.7	1.26886	[-1, -3, -2, -1, 0, 2, 0, -1, 0, -1]
stinks	-1.0	1.34164	[-2, -2, -1, -2, 2, -1, -2, 1, -1, -2]
stinkweed	-0.4	1.0198	[-2, 0, -1, 0, 2, 0, -1, -1, 0, -1]
stinkwood	-0.1	1.13578	[0, -2, 0, 0, 0, -2, 1, 0, 2, 0]
stinky	-1.5	0.5	[-2, -1, -1, -2, -1, -1, -2, -2, -1, -2]
stolen	-2.2	0.9798	[-3, -2, -2, -1, -3, -2, -3, 0, -3, -3]
stop	-1.2	0.87178	[-1, 0, -2, -2, 0, -1, -2, -2, 0, -2]
stopped	-0.9	0.53852	[-1, -1, -1, -1, -2, -1, -1, -1, 0, 0]
stopping	-0.6	0.66332	[-1, 0, 0, -1, 0, 0, 0, -1, -2, -1]
stops	-0.6	0.8	[-1, -1, 0, 0, -2, 0, -2, 0, 0, 0]
stout	0.7	1.34536	[2, 0, 3, 1, 2, 1, 0, 0, -2, 0]
straight	0.9	1.04403	[2, 0, 1, 0, 0, 0, 1, 2, 3, 0]
strain	-0.2	0.9798	[0, 0, -2, 0, -1, 2, 0, -1, 0, 0]
strained	-1.7	0.78102	[0, -2, -1, -1, -2, -2, -2, -3, -2, -2]
strainer	-0.8	1.249	[2, -2, -1, 0, -1, 0, -2, 0, -2, -2]
strainers	-0.3	0.45826	[0, 0, 0, 0, -1, -1, 0, -1, 0, 0]
straining	-1.3	0.78102	[0, -3, -2, -1, -1, -1, -1, -1, -2, -1]
strains	-1.2	0.4	[-1, -1, -1, -2, -1, -2, -1, -1, -1, -1]
strange	-0.8	0.74833	[0, -1, -1, 0, -2, 0, -1, -2, 0, -1]
strangely	-1.2	0.87178	[0, -1, -2, -2, 0, -3, -1, -1, -1, -1]
strangled	-2.5	1.0247	[-1, -1, -3, -3, -3, -3, -1, -4, -3, -3]
strength	2.2	0.6	[1, 2, 2, 3, 2, 3, 3, 2, 2, 2]
strengthen	1.3	0.64031	[1, 1, 2, 1, 2, 1, 2, 0, 2, 1]
strengthened	1.8	0.4	[2, 2, 2, 2, 1, 2, 2, 1, 2, 2]
strengthener	1.8	0.6	[2, 0, 2, 2, 2, 2, 2, 2, 2, 2]
strengtheners	1.4	0.91652	[1, 2, 1, 2, 3, 0, 2, 0, 2, 1]
strengthening	2.2	0.74833	[3, 3, 1, 1, 2, 2, 2, 2, 3, 3]
strengthens	2.0	0.63246	[3, 2, 1, 2, 2, 3, 2, 1, 2, 2]
strengths	1.7	0.64031	[2, 1, 3, 1, 2, 1, 2, 1, 2, 2]
stress	-1.8	0.6	[-1, -2, -2, -1, -2, -1, -2, -3, -2, -2]
stressed	-1.4	0.4899	[-1, -1, -1, -2, -2, -1, -2, -2, -1, -1]
stresses	-2.0	1.0	[-3, -3, -2, -1, -2, 0, -2, -1, -3, -3]
stressful	-2.3	0.45826	[-2, -2, -2, -2, -3, -3, -2, -3, -2, -2]
stressfully	-2.6	0.66332	[-2, -3, -1, -3, -3, -3, -3, -2, -3, -3]
stressing	-1.5	0.67082	[-1, -2, -1, -1, -3, -1, -2, -2, -1, -1]
stressless	1.6	0.4899	[1, 2, 1, 2, 1, 2, 1, 2, 2, 2]
stresslessness	1.6	0.8	[1, 3, 1, 3, 2, 1, 2, 1, 1, 1]
stressor	-1.8	0.74833	[-2, -1, -1, -2, -3, -2, -1, -2, -3, -1]
stressors	-2.1	0.83066	[-3, -2, -2, -2, -3, -1, -3, -1, -1, -3]
stricken	-2.3	0.9	[0, -2, -2, -2, -2, -3, -3, -3, -3, -3]
strike	-0.5	1.11803	[0, 0, 0, -2, -2, 1, 1, -2, -1, 0]
strikers	-0.6	1.0198	[0, 0, -2, -3, 0, 0, -1, 0, 0, 0]
strikes	-1.5	0.92195	[0, -2, -2, -2, -3, -1, -2, -1, 0, -2]
strong	2.3	0.78102	[3, 2, 3, 3, 1, 1, 3, 2, 2, 3]
strongbox	0.7	0.78102	[2, 1, 0, 0, 0, 1, 2, 1, 0, 0]
strongboxes	0.3	0.64031	[0, 0, 0, 0, 0, 1, 0, 2, 0, 0]
stronger	1.6	0.66332	[2, 1, 1, 1, 2, 3, 2, 1, 2, 1]
strongest	1.9	0.9434	[2, 3, 2, 1, 2, 2, 0, 3, 1, 3]
stronghold	0.5	0.80623	[0, 2, 0, 0, 2, 0, 0, 0, 0, 1]
strongholds	1.0	0.89443	[2, 2, 0, 1, 2, 2, 1, 0, 0, 0]
strongish	1.7	0.78102	[1, 1, 2, 3, 1, 2, 1, 3, 2, 1]
strongly	1.1	0.83066	[0, 0, 2, 2, 0, 1, 2, 2, 1, 1]
strongman	0.7	1.00499	[0, 1, 3, 0, 1, -1, 1, 1, 0, 1]
strongmen	0.5	1.11803	[-1, 0, 0, 3, 0, 1, 0, 2, 0, 0]
strongyl	0.6	1.0198	[0, 1, 0, 0, 0, 0, 0, 2, 3, 0]
strongyles	0.2	1.07703	[0, -2, 1, 1, -1, 1, 0, 0, 2, 0]
strongyloidosis	-0.8	1.66132	[-2, -3, 0, 0, -2, -2, 2, -2, 2, -1]
strongyls	0.1	0.3	[0, 0, 1, 0, 0, 0, 0, 0, 0, 0]
struck	-1.0	0.89443	[-1, -2, -1, 0, -2, -2, -1, -1, 1, -1]
struggle	-1.3	0.45826	[-2, -1, -2, -1, -2, -1, -1, -1, -1, -1]
struggled	-1.4	0.66332	[-2, -2, -1, -1, -1, -1, -2, -2, 0, -2]
struggler	-1.1	0.7	[-1, -1, -1, -1, 0, -1, -2, -2, 0, -2]
strugglers	-1.4	0.4899	[-1, -2, -1, -1, -2, -1, -2, -2, -1, -1]
struggles	-1.5	0.5	[-2, -2, -1, -1, -1, -1, -2, -2, -1, -2]
struggling	-1.8	0.6	[-2, -2, -2, -2, -1, -1, -2, -2, -3, -1]
stubborn	-1.7	1.00499	[0, -1, -2, -2, -2, -4, -1, -2, -1, -2]
stubborner	-1.5	1.20416	[-2, -3, -1, -1, -2, 1, -3, -2, 0, -2]
stubbornest	-0.6	1.62481	[-2, 4, -2, -1, -1, 0, -1, -1, -1, -1]
stubbornly	-1.4	0.4899	[-2, -2, -1, -2, -1, -1, -2, -1, -1, -1]
stubbornness	-1.1	0.53852	[-1, -1, 0, -2, -1, -1, -1, -2, -1, -1]
stubbornnesses	-1.5	0.80623	[-1, -2, -3, -1, -1, -3, -1, -1, -1, -1]
stuck	-1.0	0.44721	[-1, -1, -1, -1, 0, -1, -1, -1, -1, -2]
stunk	-1.6	1.68523	[-2, -2, -3, 2, -2, -3, -3, 1, -1, -3]
stunned	-0.4	1.28062	[-1, -3, -1, 0, -1, -1, 0, 2, 1, 0]
stunning	1.6	1.42829	[0, 0, 3, 2, 2, 4, 3, 0, 2, 0]
stuns	0.1	1.04403	[1, 0, -1, -2, 0, 1, 1, -1, 1, 1]
stupid	-2.4	0.66332	[-2, -3, -3, -2, -3, -3, -2, -1, -2, -3]
stupider	-2.5	0.5	[-3, -2, -3, -3, -2, -2, -3, -2, -3, -2]
stupidest	-2.4	0.66332	[-2, -3, -2, -3, -1, -3, -3, -2, -2, -3]
stupidities	-2.0	0.7746	[-2, -3, -2, -1, -3, -1, -3, -1, -2, -2]
stupidity	-1.9	0.3	[-2, -2, -2, -2, -2, -2, -1, -2, -2, -2]
stupidly	-2.0	0.7746	[-1, -2, -3, -3, -1, -3, -2, -1, -2, -2]
stupidness	-1.7	0.64031	[-3, -1, -2, -1, -2, -1, -2, -2, -1, -2]
stupidnesses	-2.6	0.8	[-2, -2, -2, -4, -4, -3, -3, -2, -2, -2]
stupids	-2.3	0.64031	[-2, -3, -1, -3, -2, -3, -3, -2, -2, -2]
stutter	-1.0	0.0	[-1, -1, -1, -1, -1, -1, -1, -1, -1, -1]
stuttered	-0.9	1.04403	[-1, -1, 2, -1, -1, -1, -2, -2, -1, -1]
stutterer	-1.0	1.18322	[-1, -1, 2, -1, -1, -1, -3, -2, -1, -1]
stutterers	-1.1	0.53852	[-1, -1, -1, 0, -1, -1, -2, -2, -1, -1]
stuttering	-1.3	0.9	[-1, -1, -1, 0, -3, -1, -1, -1, -3, -1]
stutters	-1.0	0.63246	[-1, -1, -1, -2, 0, -2, 0, -1, -1, -1]
suave	2.0	0.44721	[2, 3, 2, 2, 2, 1, 2, 2, 2, 2]
submissive	-1.3	0.9	[-1, 0, -1, -3, -1, 0, -1, -2, -2, -2]
submissively	-1.0	1.18322	[-1, -1, -1, -1, 2, -1, -1, -2, -3, -1]
submissiveness	-0.7	0.78102	[-2, 0, -1, 0, -1, 0, -2, 0, -1, 0]
substantial	0.8	0.6	[2, 0, 1, 1, 0, 0, 1, 1, 1, 1]
subversive	-0.9	1.81384	[-3, 0, -4, -1, -2, -1, 2, -1, 2, -1]
succeed	2.2	0.74833	[2, 3, 2, 2, 2, 1, 2, 4, 2, 2]
succeeded	1.8	0.87178	[2, 2, 1, 2, 2, 0, 2, 3, 3, 1]
succeeder	1.2	1.07703	[1, 0, 0, 2, 2, 0, 0, 3, 2, 2]
succeeders	1.3	0.64031	[2, 0, 2, 1, 2, 1, 1, 1, 1, 2]
succeeding	2.2	1.16619	[2, 3, 0, 3, 4, 3, 1, 1, 2, 3]
succeeds	2.2	0.9798	[4, 1, 4, 1, 2, 2, 2, 2, 2, 2]
success	2.7	0.64031	[4, 3, 2, 2, 3, 2, 3, 3, 2, 3]
successes	2.6	0.66332	[2, 4, 3, 3, 2, 3, 2, 2, 3, 2]
successful	2.8	0.6	[3, 3, 2, 3, 4, 3, 3, 3, 2, 2]
successfully	2.2	0.6	[2, 2, 1, 2, 2, 2, 3, 3, 2, 3]
successfulness	2.7	0.78102	[3, 3, 4, 3, 3, 2, 3, 2, 1, 3]
succession	0.8	0.87178	[2, 0, 1, 2, 0, 2, 1, 0, 0, 0]
successional	0.9	1.04403	[0, 1, 0, 2, 3, 0, 0, 1, 2, 0]
successionally	1.1	1.13578	[2, 2, 0, 0, 0, 0, 2, 3, 0, 2]
successions	0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 1, 0, 0]
successive	1.1	1.13578	[3, 0, 0, 1, 3, 2, 1, 0, 1, 0]
successively	0.9	1.04403	[1, 0, 0, 1, 3, 0, 2, 0, 2, 0]
successiveness	1.0	1.0	[0, 1, 2, 0, 3, 1, 1, 2, 0, 0]
successor	0.9	0.83066	[0, 2, 0, 0, 1, 2, 2, 1, 1, 0]
successors	1.1	1.04403	[2, 0, 1, 2, 0, 1, 3, 0, 2, 0]
suck	-1.9	1.04403	[-1, -1, -1, -2, -1, -1, -3, -4, -3, -2]
sucked	-2.0	0.89443	[-2, -2, -1, -1, -1, -3, -4, -2, -2, -2]
sucker	-2.4	1.2	[-3, -1, -1, -2, -1, -4, -4, -2, -4, -2]
suckered	-2.0	1.0	[-2, -1, -1, -3, -3, -1, -1, -2, -2, -4]
suckering	-2.1	0.7	[-2, -3, -2, -1, -2, -3, -3, -2, -1, -2]
suckers	-2.3	1.00499	[-2, -1, -2, -4, -1, -2, -3, -2, -4, -2]
sucks	-1.5	1.28452	[-1, -2, -2, -1, -3, -4, -1, 1, -1, -1]
sucky	-1.9	0.9434	[-1, -2, -2, -1, -2, -4, -1, -1, -3, -2]
suffer	-2.5	0.67082	[-3, -3, -2, -2, -3, -3, -3, -2, -1, -3]
suffered	-2.2	0.74833	[-3, -2, -1, -3, -2, -3, -1, -3, -2, -2]
sufferer	-2.0	0.63246	[-3, -2, -2, -2, -2, -3, -1, -2, -2, -1]
sufferers	-2.4	0.8	[-1, -3, -3, -2, -1, -2, -3, -3, -3, -3]
suffering	-2.1	0.83066	[-2, -1, -3, -2, -1, -2, -3, -3, -1, -3]
suffers	-2.1	0.7	[-3, -2, -1, -2, -2, -2, -3, -1, -3, -2]
suicidal	-3.5	0.67082	[-4, -4, -4, -4, -3, -4, -3, -3, -2, -4]
suicide	-3.5	0.67082	[-4, -3, -4, -4, -4, -2, -3, -4, -4, -3]
suing	-1.1	1.13578	[1, -1, -1, 0, -1, -3, -1, -1, -3, -1]
sulking	-1.5	0.67082	[-2, -1, -3, -1, -1, -2, -1, -2, -1, -1]
sulky	-0.8	1.77764	[-3, 3, -2, -3, 0, -2, 0, -1, 1, -1]
sullen	-1.7	1.00499	[0, -2, -2, -1, -1, -4, -2, -1, -2, -2]
sunnier	2.3	0.64031	[3, 2, 3, 2, 3, 2, 2, 1, 3, 2]
sunniest	2.4	1.28062	[2, 0, 4, 2, 1, 3, 2, 4, 4, 2]
sunny	1.8	0.87178	[2, 2, 1, 2, 0, 2, 3, 3, 2, 1]
sunshine	2.2	0.6	[3, 1, 2, 2, 2, 2, 2, 3, 3, 2]
sunshiny	1.9	0.7	[2, 2, 3, 1, 2, 2, 1, 3, 1, 2]
super	2.9	0.7	[4, 2, 4, 3, 3, 3, 2, 3, 2, 3]
superb	3.1	0.9434	[3, 4, 2, 4, 3, 1, 3, 3, 4, 4]
superior	2.5	1.11803	[2, 3, 1, 3, 3, 0, 3, 3, 4, 3]
superiorities	0.8	1.6	[-1, 1, 3, -1, -1, 2, 2, 1, 3, -1]
superiority	1.4	1.2	[0, 1, -1, 3, 1, 2, 2, 2, 3, 1]
superiorly	2.2	1.4	[4, 3, 1, 2, 3, -1, 2, 4, 2, 2]
superiors	1.0	1.0	[3, 0, 0, 1, 2, 1, 1, 2, 0, 0]
support	1.7	0.9	[1, 1, 1, 2, 1, 3, 3, 3, 1, 1]
supported	1.3	0.45826	[2, 1, 2, 1, 1, 2, 1, 1, 1, 1]
supporter	1.1	0.3	[1, 1, 1, 1, 1, 2, 1, 1, 1, 1]
supporters	1.9	0.7	[1, 1, 1, 2, 2, 3, 2, 2, 3, 2]
supporting	1.9	0.9434	[3, 2, 1, 1, 3, 3, 1, 1, 3, 1]
supportive	1.2	0.4	[1, 1, 1, 1, 1, 2, 2, 1, 1, 1]
supportiveness	1.5	1.11803	[3, 1, 2, 1, 3, 2, 2, 1, -1, 1]
supports	1.5	0.67082	[2, 1, 2, 0, 2, 2, 2, 1, 1, 2]
supremacies	0.8	1.72047	[3, -2, 3, 0, 0, 2, -1, 3, 0, 0]
supremacist	0.5	2.15639	[3, 2, -3, 1, 2, 2, 2, -2, 1, -3]
supremacists	-1.0	1.89737	[-4, -2, -1, -3, -2, 2, 2, 0, 0, -2]
supremacy	0.2	1.77764	[-1, -2, 0, 3, 2, 0, -3, 0, 1, 2]
suprematists	0.4	1.56205	[2, 1, 0, 0, 2, 2, 2, -2, -1, -2]
supreme	2.6	1.11355	[2, 3, 2, 1, 4, 2, 1, 3, 4, 4]
supremely	2.7	1.00499	[2, 4, 1, 4, 2, 4, 2, 3, 2, 3]
supremeness	2.3	0.64031	[1, 2, 2, 3, 3, 2, 2, 3, 3, 2]
supremer	2.3	1.1	[4, 3, 3, 0, 1, 2, 3, 2, 3, 2]
supremest	2.2	1.98997	[4, 3, 1, 0, 4, 4, -2, 3, 1, 4]
supremo	1.9	1.3	[1, 0, 3, 0, 4, 3, 1, 3, 2, 2]
supremos	1.3	0.78102	[0, 2, 2, 1, 0, 2, 2, 1, 2, 1]
sure	1.3	0.64031	[1, 1, 3, 1, 1, 2, 1, 1, 1, 1]
surefire	1.0	0.7746	[1, 1, 0, 2, 2, 1, 0, 1, 2, 0]
surefooted	1.9	0.83066	[0, 3, 2, 2, 2, 2, 1, 2, 3, 2]
surefootedly	1.6	0.91652	[1, 1, 2, 0, 2, 1, 3, 3, 1, 2]
surefootedness	1.5	1.20416	[2, 1, 4, 1, 0, 3, 2, 1, 1, 0]
surely	1.9	0.7	[2, 2, 2, 1, 1, 1, 3, 2, 3, 2]
sureness	2.0	0.7746	[2, 3, 2, 1, 2, 1, 3, 3, 2, 1]
surer	1.2	1.32665	[1, 1, 3, 1, 3, 2, 1, -2, 1, 1]
surest	1.3	0.78102	[2, 0, 2, 2, 2, 1, 0, 1, 1, 2]
sureties	1.3	0.9	[2, 0, 2, 0, 2, 2, 1, 2, 0, 2]
surety	1.0	0.44721	[1, 1, 1, 1, 1, 0, 1, 1, 2, 1]
suretyship	-0.1	1.51327	[-1, 0, 0, -2, -1, 2, -2, 3, 0, 0]
suretyships	0.4	0.66332	[0, 0, 0, 1, 0, 0, 1, 0, 0, 2]
surprisal	1.5	0.80623	[3, 1, 1, 2, 1, 2, 2, 1, 2, 0]
surprisals	0.7	1.1	[0, 0, 0, 0, 0, 0, 0, 2, 2, 3]
surprise	1.1	1.04403	[0, 2, 0, 2, 3, 0, 1, 1, 2, 0]
surprised	0.9	0.9434	[2, 0, 0, 0, 0, 2, 2, 1, 2, 0]
surpriser	0.6	0.66332	[2, 0, 0, 0, 0, 1, 1, 1, 1, 0]
surprisers	0.3	1.00499	[2, 0, 1, 1, 0, 0, -2, 1, 0, 0]
surprises	0.9	0.7	[2, 0, 0, 0, 1, 1, 2, 1, 1, 1]
surprising	1.1	0.9434	[1, 1, 1, 0, 0, 2, 0, 1, 3, 2]
surprisingly	1.2	0.87178	[1, 0, 2, 0, 0, 2, 2, 1, 2, 2]
survived	2.3	0.78102	[3, 4, 2, 2, 2, 1, 2, 3, 2, 2]
surviving	1.2	0.87178	[1, 3, 1, 0, 2, 0, 1, 1, 1, 2]
survivor	1.5	1.0247	[1, 3, 2, 3, 1, 0, 0, 1, 2, 2]
suspect	-1.2	0.9798	[0, -1, -1, -2, -2, -2, 1, -1, -2, -2]
suspected	-0.9	1.13578	[-1, 0, -1, -2, -1, -1, -2, 2, -2, -1]
suspecting	-0.7	1.34536	[-1, 2, -1, -1, -1, 1, -2, -1, -3, 0]
suspects	-1.4	0.91652	[-2, -2, -2, -1, -2, -1, -2, -2, 1, -1]
suspend	-1.3	0.64031	[0, -2, -1, -2, -1, -1, -2, -1, -2, -1]
suspended	-2.1	0.83066	[-2, -1, -2, -3, -3, -1, -3, -1, -2, -3]
suspicion	-1.6	0.91652	[-2, -2, -1, -2, 1, -2, -2, -2, -2, -2]
suspicions	-1.5	0.67082	[-1, -2, -1, -2, -1, -1, -3, -1, -2, -1]
suspicious	-1.5	0.67082	[-1, -2, -1, -1, -2, -1, -2, -1, -3, -1]
suspiciously	-1.7	0.45826	[-2, -2, -1, -2, -1, -2, -2, -1, -2, -2]
suspiciousness	-1.2	1.46969	[-2, -3, -2, -1, -1, 1, 2, -2, -2, -2]
sux	-1.5	0.92195	[-1, -1, -2, -1, -2, -1, -3, 0, -3, -1]
swear	-0.2	1.53623	[-2, 2, -1, 1, 1, 0, -1, -2, 2, -2]
swearing	-1.0	1.09545	[0, -2, -1, -1, -2, 0, 1, -3, -1, -1]
swears	0.2	1.4	[1, -2, 2, 0, 1, -2, -1, 2, 1, 0]
sweet	2.0	0.63246	[1, 2, 2, 2, 3, 3, 2, 1, 2, 2]
sweet<3	3.0	0.44721	[3, 3, 3, 3, 4, 2, 3, 3, 3, 3]
sweetheart	3.3	1.00499	[4, 1, 3, 4, 4, 4, 2, 3, 4, 4]
sweethearts	2.8	0.87178	[2, 2, 2, 4, 3, 2, 4, 3, 4, 2]
sweetie	2.2	0.6	[2, 3, 3, 2, 1, 3, 2, 2, 2, 2]
sweeties	2.1	0.9434	[1, 2, 2, 3, 2, 1, 2, 4, 3, 1]
sweetly	2.1	0.7	[1, 2, 2, 2, 2, 2, 1, 3, 3, 3]
sweetness	2.2	0.74833	[3, 2, 3, 1, 2, 2, 3, 3, 2, 1]
sweets	2.2	0.6	[2, 1, 3, 2, 2, 2, 3, 2, 3, 2]
swift	0.8	0.6	[1, 0, 1, 1, 0, 2, 0, 1, 1, 1]
swiftly	1.2	0.9798	[0, 3, 1, 0, 1, 1, 1, 3, 1, 1]
swindle	-2.4	1.0198	[-1, -4, -1, -3, -3, -2, -2, -2, -4, -2]
swindles	-1.5	0.92195	[0, -2, -2, -3, -2, -1, -1, 0, -2, -2]
swindling	-2.0	1.0	[-2, -2, -2, -4, -1, 0, -3, -2, -2, -2]
sympathetic	2.3	0.64031	[3, 3, 2, 2, 3, 3, 2, 2, 1, 2]
sympathy	1.5	1.11803	[-1, 2, 3, 2, 1, 1, 1, 2, 1, 3]
talent	1.8	1.07703	[3, 1, 2, 2, 0, 3, 3, 2, 2, 0]
talented	2.3	0.64031	[1, 2, 2, 3, 2, 3, 3, 2, 3, 2]
talentless	-1.6	0.4899	[-2, -2, -2, -1, -2, -1, -1, -1, -2, -2]
talents	2.0	1.18322	[2, 4, 1, 2, 0, 2, 4, 2, 2, 1]
tantrum	-1.8	1.16619	[-3, -2, -2, -3, -1, 0, -1, 0, -3, -3]
tantrums	-1.5	1.36015	[-1, -2, -4, -2, -2, -1, -1, 0, 1, -3]
tard	-2.5	0.92195	[-3, -3, -3, -2, -2, -3, -1, -4, -1, -3]
tears	-0.9	1.13578	[0, -2, -2, -1, -1, -2, -2, 1, 1, -1]
teas	0.3	0.45826	[1, 0, 0, 0, 0, 1, 0, 1, 0, 0]
tease	-1.3	0.9	[0, -1, -1, -2, -1, 0, -3, -2, -1, -2]
teased	-1.2	0.87178	[0, -2, -2, -1, -1, -1, 0, -3, -1, -1]
teasel	-0.1	0.3	[0, -1, 0, 0, 0, 0, 0, 0, 0, 0]
teaseled	-0.8	0.74833	[-1, 0, -1, 0, 0, -1, -2, -1, -2, 0]
teaseler	-0.8	0.87178	[0, 0, -2, 0, 0, -1, -2, -1, -2, 0]
teaselers	-1.2	1.249	[0, -3, 0, 0, 0, -3, -2, -2, -2, 0]
teaseling	-0.4	0.91652	[-1, 0, -3, 0, 0, 0, 0, 0, 0, 0]
teaselled	-0.4	0.91652	[0, -3, 0, 0, 0, 0, -1, 0, 0, 0]
teaselling	-0.2	0.4	[0, 0, 0, 0, -1, 0, -1, 0, 0, 0]
teasels	-0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, -1, 0]
teaser	-1.0	1.18322	[0, -1, -3, -1, -1, -1, 0, -3, -1, 1]
teasers	-0.7	1.1	[-1, -2, -3, -1, 0, 0, -1, 1, 0, 0]
teases	-1.2	0.74833	[-1, -1, 0, -1, -2, -1, -3, -1, -1, -1]
teashops	0.2	0.4	[0, 1, 0, 1, 0, 0, 0, 0, 0, 0]
teasing	-0.3	1.1	[0, 0, 1, 1, -1, 0, 0, 0, -1, -3]
teasingly	-0.4	1.11355	[0, 1, 1, 0, -2, 0, -2, 0, -2, 0]
teaspoon	0.2	0.4	[0, 0, 0, 0, 0, 0, 1, 0, 1, 0]
teaspoonful	0.2	0.4	[0, 0, 0, 1, 0, 0, 1, 0, 0, 0]
teaspoonfuls	0.4	0.8	[2, 0, 0, 0, 0, 0, 0, 0, 2, 0]
teaspoons	0.5	0.80623	[0, 0, 0, 2, 0, 0, 1, 2, 0, 0]
teaspoonsful	0.3	0.9	[2, 0, 0, 0, 0, 2, 0, -1, 0, 0]
temper	-1.8	0.4	[-1, -2, -2, -2, -2, -2, -2, -2, -1, -2]
tempers	-1.3	0.64031	[-1, -1, 0, -1, -1, -2, -2, -2, -1, -2]
tendered	0.5	0.92195	[0, 0, 2, 1, -1, -1, 1, 1, 1, 1]
tenderer	0.6	0.66332	[0, 1, 1, 0, 0, 0, 2, 1, 1, 0]
tenderers	1.2	0.9798	[2, 0, 0, 1, 0, 2, 3, 1, 1, 2]
tenderest	1.4	1.56205	[2, 1, 3, -1, 0, 2, 2, 2, -1, 4]
tenderfeet	-0.4	0.91652	[-1, -1, 0, 0, -1, 0, -1, -2, 1, 1]
tenderfoot	-0.1	0.53852	[-1, 1, 0, 0, 0, 0, 0, -1, 0, 0]
tenderfoots	-0.5	1.11803	[1, -1, 0, 0, 0, -1, -1, 1, -3, -1]
tenderhearted	1.5	1.0247	[2, 2, 1, 2, 3, 1, 1, -1, 2, 2]
tenderheartedly	2.7	0.64031	[2, 2, 2, 3, 3, 3, 3, 4, 3, 2]
tenderheartedness	0.7	1.48661	[1, 3, 2, 2, -1, -1, 1, -2, 1, 1]
tenderheartednesses	2.8	0.74833	[2, 3, 3, 2, 3, 3, 4, 4, 2, 2]
tendering	0.6	0.66332	[0, 0, 1, 1, 1, 0, 0, 1, 2, 0]
tenderization	0.2	0.74833	[-1, 2, 0, 0, 0, 1, 0, 0, 0, 0]
tenderize	0.1	0.53852	[0, 0, 0, 0, 0, -1, 0, 1, 0, 1]
tenderized	0.1	0.53852	[0, 1, 0, 1, 0, 0, -1, 0, 0, 0]
tenderizer	0.4	0.66332	[0, 1, 0, 1, 0, 0, 2, 0, 0, 0]
tenderizes	0.3	0.45826	[0, 1, 0, 1, 0, 0, 1, 0, 0, 0]
tenderizing	0.3	0.45826	[1, 1, 0, 0, 0, 1, 0, 0, 0, 0]
tenderloin	-0.2	0.74833	[0, 0, 0, 0, -2, 0, 1, 0, -1, 0]
tenderloins	0.4	0.66332	[1, 0, 0, 0, 2, 0, 0, 1, 0, 0]
tenderly	1.8	0.74833	[2, 1, 2, 1, 3, 2, 1, 2, 3, 1]
tenderness	1.8	0.4	[2, 2, 1, 2, 2, 2, 2, 1, 2, 2]
tendernesses	0.9	1.44568	[1, 2, 1, 1, 3, -1, -2, 2, 2, 0]
tenderometer	0.2	0.4	[1, 0, 0, 0, 0, 1, 0, 0, 0, 0]
tenderometers	0.2	0.4	[0, 0, 1, 0, 0, 0, 0, 0, 1, 0]
tenders	0.6	0.8	[0, 2, 0, 1, 1, 1, -1, 0, 1, 1]
tense	-1.4	0.4899	[-1, -1, -1, -1, -1, -2, -1, -2, -2, -2]
tensed	-1.0	0.44721	[-1, 0, -1, -1, -1, -1, -2, -1, -1, -1]
tensely	-1.2	0.6	[-1, -2, -2, -2, -1, 0, -1, -1, -1, -1]
tenseness	-1.5	0.67082	[-1, -1, -1, -1, -1, -2, -2, -1, -2, -3]
tenser	-1.5	0.67082	[-2, -2, -1, -2, -1, -1, -3, -1, -1, -1]
tenses	-0.9	1.04403	[-1, -3, 0, 0, 0, -1, 0, -2, -2, 0]
tensest	-1.2	1.07703	[-2, 0, -2, -2, -2, 0, 1, -1, -2, -2]
tensing	-1.0	0.44721	[-1, -2, -1, 0, -1, -1, -1, -1, -1, -1]
tension	-1.3	1.00499	[-2, -1, -1, -1, -1, -2, -2, -3, 1, -1]
tensional	-0.8	0.74833	[-1, 0, -1, -1, -1, -2, 1, -1, -1, -1]
tensioned	-0.4	1.11355	[-2, 0, -1, 2, -1, 0, -1, -1, -1, 1]
tensioner	-1.6	0.8	[-1, -3, -2, -2, -2, 0, -1, -2, -1, -2]
tensioners	-0.9	1.04403	[-1, 0, -2, -2, -1, 1, -2, 0, 0, -2]
tensioning	-1.4	1.0198	[-1, -1, -1, -1, 0, -2, -3, -3, 0, -2]
tensionless	0.6	0.8	[1, 1, 1, -1, 0, 0, 0, 2, 1, 1]
tensions	-1.7	0.78102	[-1, -3, -1, -1, -2, -2, -3, -1, -2, -1]
terrible	-2.1	0.9434	[-1, -3, -2, -1, -3, -1, -2, -2, -4, -2]
terribleness	-1.9	1.81384	[3, -3, -3, -3, -3, -1, -2, -1, -3, -3]
terriblenesses	-2.6	0.4899	[-2, -3, -3, -2, -3, -2, -2, -3, -3, -3]
terribly	-2.6	0.4899	[-3, -3, -3, -2, -2, -3, -3, -2, -2, -3]
terrific	2.1	1.81384	[4, 3, 4, 1, -1, -1, 4, 2, 2, 3]
terrifically	1.7	1.95192	[2, 2, 4, 2, 3, 3, -2, 3, -2, 2]
terrified	-3.0	0.63246	[-2, -3, -3, -3, -4, -3, -4, -2, -3, -3]
terrifies	-2.6	1.0198	[-2, -4, -3, -3, -4, -1, -2, -3, -1, -3]
terrify	-2.3	0.78102	[-3, -3, -2, -2, -1, -4, -2, -2, -2, -2]
terrifying	-2.7	0.78102	[-4, -3, -3, -2, -2, -2, -4, -2, -3, -2]
terror	-2.4	1.2	[-3, -4, -2, -1, 0, -4, -3, -2, -2, -3]
terrorise	-3.1	0.83066	[-2, -3, -4, -4, -3, -3, -2, -4, -2, -4]
terrorised	-3.3	0.64031	[-3, -3, -4, -3, -2, -4, -4, -3, -4, -3]
terrorises	-3.3	0.45826	[-3, -3, -4, -3, -3, -4, -4, -3, -3, -3]
terrorising	-3.0	0.44721	[-3, -3, -3, -3, -2, -3, -4, -3, -3, -3]
terrorism	-3.6	0.4899	[-4, -3, -4, -4, -4, -3, -3, -4, -3, -4]
terrorisms	-3.2	0.6	[-4, -4, -4, -3, -3, -3, -2, -3, -3, -3]
terrorist	-3.7	0.45826	[-4, -3, -4, -4, -4, -3, -4, -4, -3, -4]
terroristic	-3.3	0.78102	[-4, -3, -2, -3, -4, -3, -4, -2, -4, -4]
terrorists	-3.1	0.9434	[-3, -4, -2, -2, -4, -2, -2, -4, -4, -4]
terrorization	-2.7	0.9	[-4, -4, -3, -2, -2, -4, -2, -2, -2, -2]
terrorize	-3.3	0.78102	[-4, -4, -3, -3, -4, -2, -2, -4, -3, -4]
terrorized	-3.1	0.7	[-2, -3, -2, -4, -4, -4, -3, -3, -3, -3]
terrorizes	-3.1	0.53852	[-2, -3, -3, -4, -3, -4, -3, -3, -3, -3]
terrorizing	-3.0	1.0	[-3, -1, -4, -4, -4, -3, -2, -3, -2, -4]
terrorless	0.9	1.04403	[-2, 2, 1, 2, 1, 1, 1, 1, 1, 1]
terrors	-2.6	0.4899	[-2, -3, -3, -3, -2, -2, -3, -2, -3, -3]
thank	1.5	0.92195	[3, 1, 1, 0, 1, 1, 2, 3, 1, 2]
thanked	1.9	1.22066	[1, 3, 1, 1, 1, 1, 4, 4, 2, 1]
thankful	2.7	0.78102	[4, 2, 2, 3, 2, 4, 3, 3, 2, 2]
thankfuller	1.9	0.53852	[2, 2, 1, 2, 2, 2, 1, 2, 3, 2]
thankfullest	2.0	1.0	[3, 1, 1, 4, 1, 2, 2, 2, 3, 1]
thankfully	1.8	0.6	[2, 1, 2, 1, 2, 3, 2, 2, 1, 2]
thankfulness	2.1	1.44568	[3, 3, 4, 2, 2, 1, -1, 1, 2, 4]
thanks	1.9	1.04403	[1, 1, 1, 1, 3, 2, 1, 4, 3, 2]
thief	-2.4	0.66332	[-3, -2, -2, -2, -2, -4, -2, -2, -3, -2]
thieve	-2.2	0.4	[-2, -2, -2, -3, -2, -2, -3, -2, -2, -2]
thieved	-1.4	1.28062	[-1, -2, -3, -1, -2, 2, -2, -1, -2, -2]
thieveries	-2.1	0.53852	[-2, -3, -2, -3, -1, -2, -2, -2, -2, -2]
thievery	-2.0	0.89443	[-2, -2, -2, -1, -1, -3, -1, -2, -4, -2]
thieves	-2.3	0.78102	[-3, -2, -2, -4, -1, -2, -2, -2, -3, -2]
thorny	-1.1	0.83066	[1, -1, -1, -1, -1, -1, -1, -2, -2, -2]
thoughtful	1.6	0.4899	[2, 2, 1, 1, 1, 1, 2, 2, 2, 2]
thoughtfully	1.7	0.64031	[2, 1, 1, 3, 1, 2, 1, 2, 2, 2]
thoughtfulness	1.9	0.53852	[1, 2, 1, 2, 2, 3, 2, 2, 2, 2]
thoughtless	-2.0	0.63246	[-2, -1, -3, -3, -2, -1, -2, -2, -2, -2]
threat	-2.4	0.66332	[-2, -3, -2, -2, -2, -4, -3, -2, -2, -2]
threaten	-1.6	1.56205	[-4, -1, -3, -2, 1, -3, -1, 1, -2, -2]
threatened	-2.0	0.63246	[-2, -2, -3, -2, -2, -1, -2, -3, -1, -2]
threatener	-1.4	1.68523	[-2, -2, -3, -2, 3, 0, -2, -3, -1, -2]
threateners	-1.8	0.74833	[-3, -1, -2, -1, -1, -3, -2, -2, -2, -1]
threatening	-2.4	0.8	[-3, -3, -2, -3, -2, -1, -3, -1, -3, -3]
threateningly	-2.2	0.6	[-2, -2, -2, -2, -2, -2, -2, -2, -4, -2]
threatens	-1.6	1.56205	[-2, -2, -2, 1, -4, -3, -3, -1, 1, -1]
threating	-2.0	0.44721	[-3, -2, -2, -2, -1, -2, -2, -2, -2, -2]
threats	-1.8	0.74833	[-1, -1, -1, -2, -2, -2, -3, -3, -2, -1]
thrill	1.5	1.11803	[2, 2, 2, 0, 1, -1, 2, 3, 2, 2]
thrilled	1.9	1.81384	[3, -1, 3, 3, 1, -2, 3, 3, 3, 3]
thriller	0.4	1.2	[0, 0, 3, 1, 1, -2, 0, 1, 0, 0]
thrillers	0.1	0.83066	[0, 2, 0, 0, 0, -1, 0, -1, 0, 1]
thrilling	2.1	1.04403	[3, 0, 2, 3, 1, 3, 3, 2, 1, 3]
thrillingly	2.0	0.7746	[0, 2, 2, 3, 3, 2, 2, 2, 2, 2]
thrills	1.5	0.92195	[2, 3, 1, 2, 0, 2, 1, 0, 2, 2]
thwarted	-0.1	1.75784	[1, -2, -3, 0, 2, 0, 2, -2, -1, 2]
thwarting	-0.7	0.78102	[0, 0, -1, -1, -1, -2, 1, -1, -1, -1]
thwarts	-0.4	1.28062	[-2, 0, 0, -2, 0, 1, 2, 0, -1, -2]
ticked	-1.8	0.6	[-2, -1, -1, -2, -2, -1, -2, -2, -3, -2]
timid	-1.0	0.44721	[-1, -1, -1, -1, 0, -1, -2, -1, -1, -1]
timider	-1.0	0.44721	[-1, -2, -1, -1, -1, 0, -1, -1, -1, -1]
timidest	-0.9	0.7	[0, -1, 0, -1, 0, -1, -1, -2, -1, -2]
timidities	-0.7	0.64031	[-1, -1, -1, 0, -2, 0, -1, 0, 0, -1]
timidity	-1.3	0.45826	[-1, -2, -1, -1, -1, -1, -2, -2, -1, -1]
timidly	-0.7	0.78102	[0, -1, -1, -1, -1, 1, 0, -1, -2, -1]
timidness	-1.0	0.89443	[-1, -2, -1, 0, -1, -2, 1, -2, -1, -1]
timorous	-0.8	0.9798	[-2, -1, -1, -1, -1, -2, -1, 1, 1, -1]
tired	-1.9	0.7	[-2, -1, -2, -3, -2, -3, -1, -1, -2, -2]
tits	-0.9	0.53852	[-1, -1, -2, -1, -1, -1, 0, -1, 0, -1]
tolerance	1.2	1.53623	[2, 3, 2, 2, -2, 1, 1, 3, -1, 1]
tolerances	0.3	0.9	[-1, 1, 0, 0, 1, 2, 0, -1, 1, 0]
tolerant	1.1	0.53852	[1, 2, 1, 1, 1, 1, 1, 1, 0, 2]
tolerantly	0.4	1.2	[0, 1, 1, 1, 0, -3, 1, 1, 1, 1]
toothless	-1.4	1.0198	[-1, -4, -1, -2, -1, 0, -1, -2, -1, -1]
top	0.8	0.87178	[1, 3, 1, 1, 1, 0, 0, 1, 0, 0]
tops	2.3	1.00499	[3, 3, 3, 0, 2, 2, 3, 3, 1, 3]
torn	-1.0	1.0	[-1, -1, -1, 0, -1, -1, -3, -2, 1, -1]
torture	-2.9	1.51327	[-4, -4, -3, -4, 1, -2, -3, -2, -4, -4]
tortured	-2.6	1.0198	[-4, -1, -2, -2, -4, -3, -2, -2, -4, -2]
torturer	-2.3	1.18743	[-4, -1, -1, -2, -4, -1, -2, -2, -4, -2]
torturers	-3.5	0.67082	[-4, -4, -3, -3, -4, -4, -4, -2, -3, -4]
tortures	-2.5	0.92195	[-2, -3, -2, -4, -1, -3, -2, -4, -2, -2]
torturing	-3.0	0.89443	[-4, -4, -4, -2, -3, -2, -4, -2, -2, -3]
torturous	-2.7	0.78102	[-3, -3, -1, -3, -4, -2, -2, -3, -3, -3]
torturously	-2.2	1.6	[2, -3, -3, -2, -3, -2, -4, -3, -1, -3]
totalitarian	-2.1	1.3	[0, -4, -3, 0, -3, -2, -3, -1, -2, -3]
totalitarianism	-2.7	1.18743	[-2, -3, 0, -4, -2, -4, -4, -2, -3, -3]
tough	-0.5	1.43178	[0, -2, 0, 0, 1, 2, -1, -2, -3, 0]
toughed	0.7	0.64031	[1, 1, 0, 1, 1, 0, 2, 1, 0, 0]
toughen	0.1	1.04403	[0, 0, 1, 0, 1, 0, 2, 0, -2, -1]
toughened	0.1	0.53852	[1, 0, 0, 0, 0, -1, 1, 0, 0, 0]
toughening	0.9	0.83066	[0, 2, 2, 1, 1, 0, 1, 0, 0, 2]
toughens	-0.2	1.16619	[1, -1, 0, 1, 0, -2, -2, 1, -1, 1]
tougher	0.7	1.00499	[1, 1, -1, 0, 2, 0, 2, 2, 0, 0]
toughest	-0.3	1.84662	[2, 1, -1, -3, 0, -2, 2, -2, 2, -2]
toughie	-0.7	0.64031	[-1, 1, 0, -1, -1, -1, -1, -1, -1, -1]
toughies	-0.6	0.66332	[-1, -1, -1, 0, 0, -1, 1, -1, -1, -1]
toughing	-0.5	1.20416	[-1, 0, -2, 0, 0, -2, 2, 0, -2, 0]
toughish	-1.0	1.0	[0, -1, 0, -2, -2, -1, 1, -2, -2, -1]
toughly	-1.1	0.83066	[-1, 0, 0, -1, 0, -1, -2, -2, -2, -2]
toughness	-0.2	1.07703	[0, 0, -1, -2, 0, 1, 1, 1, -2, 0]
toughnesses	0.3	1.18743	[1, 2, -1, 0, 1, 1, -2, 1, -1, 1]
toughs	-0.8	1.16619	[0, 0, -1, 0, -1, -3, -2, 1, 0, -2]
toughy	-0.5	1.11803	[-1, -2, -1, 0, -1, -1, 1, -1, 2, -1]
tout	-0.5	0.67082	[-1, 0, -2, -1, 0, 0, -1, 0, 0, 0]
touted	-0.2	0.9798	[-1, 2, 0, -2, -1, 0, 0, 0, 0, 0]
touting	-0.7	0.64031	[0, 0, -1, -1, -1, -1, 0, -2, 0, -1]
touts	-0.1	0.7	[1, 1, 0, -1, 0, -1, -1, 0, 0, 0]
tragedian	-0.5	0.67082	[-2, 0, -1, 0, 0, -1, 0, -1, 0, 0]
tragedians	-1.0	1.18322	[-1, 0, -2, -1, 0, -3, 0, 0, 0, -3]
tragedienne	-0.4	0.4899	[0, 0, -1, -1, 0, 0, -1, -1, 0, 0]
tragediennes	-1.4	1.28062	[0, -3, 0, -1, -3, 0, -2, -3, 0, -2]
tragedies	-1.9	1.86815	[-3, -4, -2, 2, -3, -4, -2, 1, -2, -2]
tragedy	-3.4	1.0198	[-4, -4, -4, -4, -2, -3, -1, -4, -4, -4]
tragic	-2.0	1.94936	[-1, -3, -4, -3, -3, -4, -2, 1, 2, -3]
tragical	-2.4	1.11355	[-3, -3, -3, -2, -1, -3, -4, -3, 0, -2]
tragically	-2.7	1.48661	[-4, -4, -4, -2, -1, -4, -1, 0, -3, -4]
tragicomedy	0.2	0.9798	[0, -2, 0, 0, 0, 2, 1, 1, 0, 0]
tragicomic	-0.2	0.74833	[0, -1, 1, 0, 0, 0, -2, 0, 0, 0]
tragics	-2.2	0.74833	[-3, -2, -4, -1, -2, -2, -2, -2, -2, -2]
tranquil	0.2	1.77764	[2, 1, -1, 2, -3, 3, -1, 1, -1, -1]
tranquiler	1.9	0.7	[2, 1, 2, 2, 3, 3, 2, 2, 1, 1]
tranquilest	1.6	1.35647	[1, 2, 2, 2, 0, 2, -1, 4, 3, 1]
tranquilities	1.5	1.36015	[3, 2, 4, -1, 1, 0, 2, 2, 1, 1]
tranquility	1.8	1.16619	[3, 2, 1, 2, 3, 4, 1, 1, 1, 0]
tranquilize	0.3	1.00499	[0, -2, 1, -1, 1, 1, 1, 0, 1, 1]
tranquilized	-0.2	1.32665	[-2, 0, 1, 0, 1, 2, 0, -2, -2, 0]
tranquilizer	-0.1	0.9434	[0, 1, -1, 0, -1, 0, -2, 1, 1, 0]
tranquilizers	-0.4	0.8	[0, 0, 0, -2, -1, -1, -1, 1, 0, 0]
tranquilizes	-0.1	0.9434	[-2, 0, 1, 0, 1, 1, 0, -1, -1, 0]
tranquilizing	-0.5	0.67082	[-1, 0, 0, -1, -1, -1, 1, 0, -1, -1]
tranquillest	0.8	1.4	[1, 1, 2, 3, 1, 1, 0, -1, -2, 2]
tranquillities	0.5	1.20416	[-2, 1, 2, 0, 2, 1, 0, -1, 1, 1]
tranquillity	1.8	1.07703	[1, 2, 2, 2, 3, 3, 2, -1, 2, 2]
tranquillized	-0.2	1.07703	[0, 1, 0, 0, -3, 0, 1, 0, -1, 0]
tranquillizer	-0.1	0.7	[0, 1, 0, 0, -1, 0, 1, 0, -1, -1]
tranquillizers	-0.2	0.74833	[0, 0, 0, -2, 1, 0, 0, 0, -1, 0]
tranquillizes	0.1	0.7	[-1, -1, 0, 0, 1, 1, 1, 0, 0, 0]
tranquillizing	0.8	0.87178	[1, 2, 0, 2, 0, 0, 2, 0, 0, 1]
tranquilly	1.2	0.87178	[2, 2, 1, 2, -1, 2, 1, 1, 1, 1]
tranquilness	1.5	1.20416	[3, 3, 1, 2, 2, 2, 0, -1, 1, 2]
trap	-1.3	0.78102	[-1, -1, -2, 0, -1, -1, -2, -1, -1, -3]
trapped	-2.4	0.91652	[-3, -2, -3, -1, -3, -1, -2, -2, -4, -3]
trauma	-1.8	1.249	[-2, -2, -3, -1, -2, 1, -2, -4, -1, -2]
traumas	-2.2	1.6	[-3, -3, -2, -4, 0, -1, -4, -3, 1, -3]
traumata	-1.7	1.34536	[-2, -3, -2, 0, -2, 1, -4, -2, -1, -2]
traumatic	-2.7	1.00499	[-2, -4, -2, -1, -4, -3, -2, -3, -4, -2]
traumatically	-2.8	0.6	[-4, -2, -3, -2, -2, -3, -3, -3, -3, -3]
traumatise	-2.8	0.6	[-4, -3, -3, -2, -2, -3, -2, -3, -3, -3]
traumatised	-2.4	0.91652	[-4, -4, -2, -2, -1, -2, -3, -2, -2, -2]
traumatises	-2.2	0.87178	[-3, -4, -2, -2, -1, -2, -3, -1, -2, -2]
traumatising	-1.9	1.86815	[-3, -3, -3, -1, -3, 1, -4, -2, 2, -3]
traumatism	-2.4	0.4899	[-3, -2, -3, -2, -2, -2, -2, -3, -3, -2]
traumatization	-3.0	1.0	[-4, -4, -4, -2, -2, -2, -4, -4, -2, -2]
traumatizations	-2.2	1.16619	[-3, -2, -4, 0, -1, -2, -4, -2, -2, -2]
traumatize	-2.4	0.66332	[-3, -3, -2, -4, -2, -2, -2, -2, -2, -2]
traumatized	-1.7	1.41774	[-1, -2, -2, -2, 2, -4, -2, -2, -2, -2]
traumatizes	-1.4	1.42829	[-2, -1, -2, -2, 2, -4, -1, -2, -1, -1]
traumatizing	-2.3	1.61555	[-4, -2, -2, -3, -2, -4, -4, 1, 0, -3]
travesty	-2.7	1.48661	[-3, -4, 0, -2, -4, -3, -4, 0, -3, -4]
treason	-1.9	1.75784	[-3, -3, -2, -3, -3, 1, -2, -3, -3, 2]
treasonous	-2.7	1.34536	[-3, -3, -3, -4, -3, -4, -2, 1, -3, -3]
treasurable	2.5	0.67082	[2, 3, 3, 3, 2, 4, 2, 2, 2, 2]
treasure	1.2	1.16619	[3, 1, 3, 2, 0, 2, 1, 0, 0, 0]
treasured	2.6	0.66332	[3, 3, 2, 3, 3, 2, 1, 3, 3, 3]
treasurer	0.5	0.67082	[1, 0, 0, 0, 1, 0, 0, 0, 1, 2]
treasurers	0.4	0.66332	[0, 0, 0, 0, 1, 1, 2, 0, 0, 0]
treasurership	0.4	0.66332	[0, 0, 2, 0, 1, 0, 1, 0, 0, 0]
treasurerships	1.2	0.87178	[2, 0, 2, 0, 2, 1, 2, 2, 1, 0]
treasures	1.8	1.32665	[3, 3, 0, 1, 1, 4, 0, 3, 2, 1]
treasuries	0.9	1.04403	[0, 0, 1, 3, 2, 2, 0, 1, 0, 0]
treasuring	2.1	0.7	[2, 1, 3, 1, 3, 2, 2, 2, 2, 3]
treasury	0.8	1.07703	[2, 3, 0, 0, 0, 0, 0, 1, 2, 0]
treat	1.7	0.78102	[2, 2, 2, 0, 2, 1, 3, 2, 1, 2]
tremble	-1.1	1.3	[-2, 0, -1, -2, -2, -2, 2, 0, -2, -2]
trembled	-1.1	1.22066	[-1, 1, -1, -2, -2, -1, -3, -2, 1, -1]
trembler	-0.6	1.28062	[-2, -2, -1, 0, -1, 2, 1, -2, -1, 0]
tremblers	-1.0	0.63246	[-2, 0, -1, -1, 0, -2, -1, -1, -1, -1]
trembles	-0.1	1.51327	[-1, -2, -1, 0, -1, 2, 2, -2, 2, 0]
trembling	-1.5	0.92195	[-3, 0, -1, -3, -1, -1, -1, -2, -1, -2]
trembly	-1.2	0.87178	[-2, 0, -1, -2, -2, -1, 0, 0, -2, -2]
tremulous	-1.0	1.09545	[-1, -2, -1, -1, -1, -1, 2, -2, -2, -1]
trick	-0.2	1.32665	[-1, 1, -2, -2, -1, 2, 0, 1, 1, -1]
tricked	-0.6	0.91652	[-1, -1, -1, -1, 2, 0, -1, -1, -1, -1]
tricker	-0.9	0.83066	[-2, 0, -1, -1, 1, -2, -1, -1, -1, -1]
trickeries	-1.2	1.46969	[-2, -2, -1, -2, -1, 2, 1, -2, -3, -2]
trickers	-1.4	0.66332	[-1, -3, -1, -2, -2, -1, -1, -1, -1, -1]
trickery	-1.1	1.51327	[1, -1, -1, -2, -3, -2, -3, 2, -1, -1]
trickie	-0.4	1.0198	[1, -1, 0, -1, -1, 1, -2, -1, 1, -1]
trickier	-0.7	0.78102	[-1, -1, -1, 0, 1, -1, -1, -1, 0, -2]
trickiest	-1.2	0.6	[-2, -2, 0, -1, -2, -1, -1, -1, -1, -1]
trickily	-0.8	0.74833	[0, 1, -1, -1, -1, -1, -1, -1, -2, -1]
trickiness	-1.2	0.9798	[-2, -1, -1, -2, -1, -2, 1, 0, -2, -2]
trickinesses	-0.4	1.0198	[0, 2, 0, -2, -1, -1, 0, 0, -1, -1]
tricking	0.1	1.37477	[0, -2, 2, -1, 2, -2, 0, 1, 1, 0]
trickish	-1.0	0.44721	[-1, -2, 0, -1, -1, -1, -1, -1, -1, -1]
trickishly	-0.7	1.55242	[-3, -2, -1, 2, -1, 2, -2, -1, 0, -1]
trickishness	-0.4	1.28062	[-1, 1, -2, -1, -1, -1, 0, -2, 1, 2]
trickled	0.1	0.3	[0, 0, 0, 0, 0, 0, 0, 0, 1, 0]
trickledown	-0.7	0.64031	[0, -1, 0, -1, -2, 0, 0, -1, -1, -1]
trickles	0.2	0.4	[0, 0, 0, 1, 0, 0, 0, 0, 1, 0]
trickling	-0.2	0.9798	[0, 0, -1, -2, -1, 2, 0, 0, 0, 0]
trickly	-0.3	0.45826	[0, 0, -1, 0, 0, 0, 0, -1, 0, -1]
tricks	-0.5	0.67082	[0, 0, -1, -1, 1, -1, -1, 0, -1, -1]
tricksier	-0.5	0.67082	[-1, -1, 1, 0, -1, 0, 0, -1, -1, -1]
tricksiness	-1.0	0.89443	[0, 0, -1, -1, -1, 0, -1, -2, -3, -1]
trickster	-0.9	0.83066	[-1, -2, 0, -1, -2, -1, -1, 1, -1, -1]
tricksters	-1.3	0.9	[-1, -2, -1, -2, 1, -2, -2, -1, -1, -2]
tricksy	-0.8	0.9798	[-1, -1, -2, -1, 2, -1, -1, -1, -1, -1]
tricky	-0.6	0.66332	[0, 0, -1, -1, 1, -1, -1, -1, -1, -1]
trite	-0.8	0.4	[-1, -1, 0, 0, -1, -1, -1, -1, -1, -1]
triumph	2.1	1.04403	[2, 4, 3, 3, 1, 3, 1, 2, 1, 1]
triumphal	2.0	0.63246	[2, 3, 3, 2, 1, 2, 1, 2, 2, 2]
triumphalisms	1.9	0.9434	[2, 1, 2, 2, 2, 0, 2, 2, 4, 2]
triumphalist	0.5	1.20416	[1, 1, 2, 2, -1, -2, 0, 1, 0, 1]
triumphalists	0.9	1.64012	[1, -1, 0, 0, 1, 3, -2, 3, 1, 3]
triumphant	2.4	0.91652	[2, 3, 3, 3, 4, 1, 2, 1, 3, 2]
triumphantly	2.3	1.00499	[3, 3, 3, 3, 1, 0, 3, 2, 2, 3]
triumphed	2.2	1.4	[2, 3, 3, 3, 4, 3, 1, 1, -1, 3]
triumphing	2.3	0.64031	[2, 2, 3, 2, 2, 3, 2, 3, 1, 3]
triumphs	2.0	1.41421	[3, 2, 3, 3, 3, 1, -1, 1, 1, 4]
trivial	-0.1	0.83066	[0, -1, -1, 0, 1, 0, 1, -1, 1, -1]
trivialise	-0.8	1.16619	[-3, -1, -2, 0, 1, -1, -1, 1, -1, -1]
trivialised	-0.8	1.6	[-2, 0, -2, 1, -1, 0, 1, 1, -4, -2]
trivialises	-1.1	0.53852	[-1, -1, -1, 0, -1, -1, -1, -2, -2, -1]
trivialising	-1.4	0.66332	[-2, -1, -2, -1, 0, -1, -2, -1, -2, -2]
trivialities	-1.0	0.63246	[-2, -2, -1, -1, -1, 0, 0, -1, -1, -1]
triviality	-0.5	1.11803	[-1, -1, -2, 0, -1, -1, 2, -1, 1, -1]
trivialization	-0.9	1.04403	[-1, 2, -1, -1, -2, -2, -1, -1, -1, -1]
trivializations	-0.7	1.18743	[-2, -1, -1, -2, 1, -1, -1, -1, 2, -1]
trivialize	-1.1	0.53852	[0, -2, -2, -1, -1, -1, -1, -1, -1, -1]
trivialized	-0.6	0.8	[-1, -1, 0, 0, -1, -1, 1, -1, 0, -2]
trivializes	-1.0	0.7746	[-1, -1, -1, 0, -1, 0, -1, -1, -1, -3]
trivializing	-0.6	1.95959	[-1, -1, -2, -1, -1, 2, -2, -4, 1, 3]
trivially	0.4	1.56205	[-1, -1, -1, -1, 0, 3, 0, 2, 3, 0]
trivium	-0.3	0.64031	[0, 0, -2, 0, 0, 0, 0, -1, 0, 0]
trouble	-1.7	0.64031	[-2, -2, -1, -1, -3, -2, -1, -2, -1, -2]
troubled	-2.0	0.44721	[-2, -2, -2, -1, -2, -3, -2, -2, -2, -2]
troublemaker	-2.0	0.63246	[-3, -2, -3, -2, -2, -1, -1, -2, -2, -2]
troublemakers	-2.2	0.74833	[-3, -3, -3, -1, -3, -1, -2, -2, -2, -2]
troublemaking	-1.8	0.6	[-2, -1, -1, -2, -2, -2, -3, -2, -1, -2]
troubler	-1.4	0.4899	[-1, -1, -2, -1, -1, -1, -2, -2, -2, -1]
troublers	-1.9	0.3	[-2, -2, -2, -2, -1, -2, -2, -2, -2, -2]
troubles	-2.0	0.44721	[-2, -2, -2, -1, -2, -3, -2, -2, -2, -2]
troubleshoot	0.8	0.9798	[0, 0, 0, 2, 2, 2, 2, 0, 0, 0]
troubleshooter	1.0	0.89443	[0, 0, 0, 1, 2, 2, 0, 1, 2, 2]
troubleshooters	0.8	0.87178	[0, 0, 1, 2, 0, 1, 0, 2, 2, 0]
troubleshooting	0.7	1.34536	[0, 2, 1, 1, -1, 2, 0, 2, -2, 2]
troubleshoots	0.5	0.92195	[1, -1, 0, 0, 0, 0, 1, 0, 2, 2]
troublesome	-2.3	0.78102	[-3, -2, -3, -2, -3, -3, -1, -2, -1, -3]
troublesomely	-1.8	0.6	[-3, -1, -2, -2, -1, -2, -2, -2, -2, -1]
troublesomeness	-1.9	0.7	[-2, -1, -2, -3, -2, -3, -1, -2, -1, -2]
troubling	-2.5	0.67082	[-3, -3, -3, -3, -1, -2, -3, -2, -3, -2]
troublous	-2.1	0.53852	[-2, -2, -2, -2, -2, -3, -2, -3, -1, -2]
troublously	-2.1	1.22066	[-2, -3, -3, -3, -2, -1, 1, -2, -3, -3]
trueness	2.1	0.9434	[2, 1, 1, 3, 3, 4, 2, 1, 2, 2]
truer	1.5	0.67082	[1, 2, 1, 2, 1, 1, 2, 1, 3, 1]
truest	1.9	0.83066	[2, 2, 2, 3, 3, 0, 2, 1, 2, 2]
truly	1.9	0.9434	[4, 3, 1, 2, 2, 1, 1, 1, 2, 2]
trust	2.3	1.26886	[0, 4, 3, 3, 4, 1, 2, 2, 1, 3]
trustability	2.1	0.7	[1, 3, 3, 2, 2, 2, 1, 3, 2, 2]
trustable	2.3	0.45826	[2, 2, 3, 2, 2, 3, 3, 2, 2, 2]
trustbuster	-0.5	1.28452	[-3, -1, 0, 0, 0, 0, -2, 0, 2, -1]
trusted	2.1	0.9434	[3, 2, 2, 1, 4, 1, 2, 2, 3, 1]
trustee	1.0	0.89443	[2, 2, 0, 1, 1, 0, 2, 0, 2, 0]
trustees	0.3	0.64031	[0, 1, 0, 0, 2, 0, 0, 0, 0, 0]
trusteeship	0.5	0.67082	[0, 1, 1, 0, 0, 1, 0, 0, 2, 0]
trusteeships	0.6	1.0198	[3, 0, 0, 0, 0, 0, 0, 2, 1, 0]
truster	1.9	1.3	[2, 2, 1, 1, 4, 0, 3, 1, 4, 1]
trustful	2.1	0.9434	[1, 2, 2, 1, 2, 2, 1, 3, 3, 4]
trustfully	1.5	0.67082	[2, 1, 2, 1, 1, 3, 1, 2, 1, 1]
trustfulness	2.1	0.83066	[3, 2, 3, 2, 3, 2, 2, 2, 0, 2]
trustier	1.3	1.1	[1, 1, 1, 2, 0, 0, 3, 0, 3, 2]
trusties	1.0	0.7746	[1, 1, 0, 2, 1, 0, 2, 0, 1, 2]
trustiest	2.2	0.87178	[3, 2, 2, 3, 2, 2, 4, 1, 1, 2]
trustily	1.6	0.91652	[2, 0, 3, 1, 2, 1, 1, 1, 2, 3]
trustiness	1.6	0.91652	[2, 3, 0, 1, 2, 2, 2, 2, 0, 2]
trusting	1.7	1.00499	[3, 0, 1, 2, 3, 0, 2, 2, 2, 2]
trustingly	1.6	0.91652	[3, 1, 2, 3, 2, 2, 0, 1, 1, 1]
trustingness	1.6	1.2	[1, 2, 1, 3, 2, 1, 4, 2, 0, 0]
trustless	-2.3	0.78102	[-2, -4, -3, -2, -2, -3, -2, -2, -1, -2]
trustor	0.4	0.66332	[2, 0, 0, 1, 0, 0, 0, 1, 0, 0]
trustors	1.2	0.87178	[0, 0, 2, 1, 1, 2, 2, 2, 0, 2]
trusts	2.1	0.53852	[2, 2, 2, 2, 1, 2, 2, 3, 2, 3]
trustworthily	2.3	0.9	[3, 1, 2, 2, 2, 1, 3, 4, 3, 2]
trustworthiness	1.8	0.74833	[2, 1, 3, 1, 2, 2, 1, 2, 3, 1]
trustworthy	2.6	0.91652	[3, 2, 3, 4, 2, 4, 2, 3, 1, 2]
trusty	2.2	0.74833	[3, 2, 3, 1, 2, 2, 3, 2, 1, 3]
truth	1.3	1.00499	[2, 1, 0, 1, 1, 0, 3, 3, 1, 1]
truthful	2.0	0.63246	[2, 2, 1, 3, 3, 2, 1, 2, 2, 2]
truthfully	1.9	1.04403	[3, 1, 3, 0, 2, 1, 3, 2, 1, 3]
truthfulness	1.7	1.1	[3, 2, 2, 2, 1, -1, 3, 2, 2, 1]
truths	1.8	0.87178	[0, 1, 1, 3, 2, 2, 3, 2, 2, 2]
tumor	-1.6	1.49666	[-3, -2, -2, -1, -2, 1, -4, 1, -2, -2]
turmoil	-1.5	0.92195	[-1, -1, -3, -3, -2, -2, -1, -1, 0, -1]
twat	-3.4	0.91652	[-3, -4, -4, -4, -3, -1, -4, -4, -3, -4]
ugh	-1.8	0.9798	[-1, -1, -1, -1, -1, -2, -4, -2, -3, -2]
uglier	-2.2	0.87178	[-2, -2, -1, -3, -4, -1, -3, -2, -2, -2]
uglies	-2.0	0.89443	[-2, -2, -1, -3, -4, -1, -2, -2, -1, -2]
ugliest	-2.8	0.74833	[-3, -3, -4, -3, -3, -3, -3, -3, -1, -2]
uglification	-2.2	0.87178	[-3, -1, -2, -2, -1, -2, -4, -3, -2, -2]
uglified	-1.5	0.67082	[-1, -1, -3, -2, -2, -1, -1, -1, -2, -1]
uglifies	-1.8	0.74833	[-1, -1, -3, -2, -3, -1, -2, -2, -2, -1]
uglify	-2.1	0.9434	[-3, -3, -1, -4, -2, -2, -1, -2, -1, -2]
uglifying	-2.2	0.4	[-3, -2, -2, -2, -2, -2, -2, -2, -3, -2]
uglily	-2.1	0.3	[-3, -2, -2, -2, -2, -2, -2, -2, -2, -2]
ugliness	-2.7	0.9	[-4, -2, -3, -2, -4, -2, -4, -2, -2, -2]
uglinesses	-2.5	1.0247	[-3, -3, -2, -1, -1, -3, -2, -2, -4, -4]
ugly	-2.3	0.9	[-3, -2, -1, -2, -4, -1, -3, -2, -2, -3]
unacceptable	-2.0	0.44721	[-2, -2, -1, -2, -2, -2, -2, -3, -2, -2]
unappreciated	-1.7	0.78102	[-1, -3, -2, -1, -1, -1, -2, -1, -2, -3]
unapproved	-1.4	0.4899	[-1, -1, -1, -2, -2, -1, -2, -2, -1, -1]
unattractive	-1.9	0.53852	[-1, -2, -3, -2, -1, -2, -2, -2, -2, -2]
unaware	-0.8	0.4	[-1, -1, -1, 0, -1, -1, 0, -1, -1, -1]
unbelievable	0.8	1.6	[0, 0, 1, -2, 1, 3, 1, 0, 4, 0]
unbelieving	-0.8	0.4	[0, -1, -1, -1, -1, -1, -1, 0, -1, -1]
unbiased	-0.1	1.22066	[-2, -1, 2, 1, 0, -1, -1, 1, 1, -1]
uncertain	-1.2	0.6	[-2, -1, -2, -1, -1, -1, 0, -1, -2, -1]
uncertainly	-1.4	0.4899	[-2, -1, -1, -1, -2, -1, -1, -2, -2, -1]
uncertainness	-1.3	0.45826	[-2, -2, -1, -1, -1, -1, -1, -2, -1, -1]
uncertainties	-1.4	0.66332	[-3, -2, -1, -1, -1, -1, -1, -2, -1, -1]
uncertainty	-1.4	0.4899	[-1, -1, -2, -1, -2, -1, -2, -1, -2, -1]
unclear	-1.0	0.44721	[-2, -1, 0, -1, -1, -1, -1, -1, -1, -1]
uncomfortable	-1.6	0.4899	[-2, -1, -2, -1, -1, -2, -2, -1, -2, -2]
uncomfortably	-1.7	0.64031	[-2, -1, -3, -1, -1, -2, -2, -1, -2, -2]
uncompelling	-0.9	0.7	[0, -2, -1, 0, -1, -1, -1, 0, -1, -2]
unconcerned	-0.9	0.83066	[-2, -2, -1, 1, -1, -1, -1, 0, -1, -1]
unconfirmed	-0.5	0.67082	[0, -1, 0, -1, 0, -1, 0, 0, -2, 0]
uncontrollability	-1.7	0.45826	[-1, -1, -2, -2, -2, -2, -1, -2, -2, -2]
uncontrollable	-1.5	1.11803	[-2, -1, -1, -2, -1, -1, 1, -2, -3, -3]
uncontrollably	-1.5	0.67082	[-2, -1, 0, -2, -1, -1, -2, -2, -2, -2]
uncontrolled	-1.0	0.7746	[-1, 0, -1, -2, -1, 0, -1, -2, -2, 0]
unconvinced	-1.6	0.8	[-2, -3, -1, -2, -1, 0, -2, -2, -1, -2]
uncredited	-1.0	1.09545	[-1, -2, -2, 2, -1, -2, -1, -1, -1, -1]
undecided	-0.9	0.9434	[-1, 0, -1, 0, -1, -1, -2, 0, -3, 0]
underestimate	-1.2	0.4	[-1, -2, -1, -1, -1, -1, -1, -1, -2, -1]
underestimated	-1.1	0.53852	[-1, -1, -1, -2, 0, -1, -2, -1, -1, -1]
underestimates	-1.1	1.64012	[-2, -4, -1, -1, -1, -1, 3, -1, -2, -1]
undermine	-1.2	1.16619	[-2, -2, -1, -1, -1, -1, 2, -2, -2, -2]
undermined	-1.5	0.67082	[-1, -2, -1, -3, -1, -1, -1, -2, -2, -1]
undermines	-1.4	0.4899	[-1, -2, -1, -2, -1, -1, -1, -2, -2, -1]
undermining	-1.5	0.67082	[-1, -3, -1, -2, -2, -1, -1, -1, -2, -1]
undeserving	-1.9	0.3	[-2, -2, -1, -2, -2, -2, -2, -2, -2, -2]
undesirable	-1.9	0.7	[-1, -2, -3, -1, -3, -2, -1, -2, -2, -2]
unease	-1.7	0.64031	[-2, -2, -2, -1, -1, -2, -1, -3, -1, -2]
uneasier	-1.4	0.4899	[-1, -1, -1, -2, -2, -1, -2, -2, -1, -1]
uneasiest	-2.1	0.83066	[-1, -4, -3, -2, -2, -2, -2, -2, -1, -2]
uneasily	-1.4	1.0198	[-2, -2, -1, -1, -2, 1, -3, -1, -2, -1]
uneasiness	-1.6	0.4899	[-2, -2, -1, -2, -1, -1, -2, -2, -1, -2]
uneasinesses	-1.8	0.87178	[-2, -1, -4, -1, -1, -2, -2, -2, -1, -2]
uneasy	-1.6	0.4899	[-1, -2, -2, -1, -1, -2, -2, -1, -2, -2]
unemployment	-1.9	0.7	[-2, -1, -2, -3, -1, -2, -1, -2, -2, -3]
unequal	-1.4	0.66332	[-1, -2, -2, -2, -1, -1, -2, 0, -2, -1]
unequaled	0.5	1.80278	[-2, 3, 0, 3, 3, 0, 0, 0, -2, 0]
unethical	-2.3	0.78102	[-3, -3, -1, -2, -2, -2, -3, -1, -3, -3]
unfair	-2.1	0.83066	[-1, -3, -3, -2, -3, -1, -2, -3, -1, -2]
unfocused	-1.7	0.64031	[-2, -1, -2, -1, -1, -2, -3, -2, -1, -2]
unfortunate	-2.0	0.63246	[-2, -2, -2, -3, -3, -1, -2, -1, -2, -2]
unfortunately	-1.4	0.91652	[-2, -1, -2, -2, 1, -2, -1, -1, -2, -2]
unfortunates	-1.9	0.7	[-2, -3, -1, -1, -2, -2, -2, -1, -3, -2]
unfriendly	-1.5	0.5	[-1, -2, -1, -2, -1, -2, -2, -2, -1, -1]
unfulfilled	-1.8	0.4	[-2, -2, -2, -2, -1, -2, -2, -1, -2, -2]
ungrateful	-2.0	0.0	[-2, -2, -2, -2, -2, -2, -2, -2, -2, -2]
ungratefully	-1.8	0.6	[-2, -2, -1, -2, -2, -1, -3, -1, -2, -2]
ungratefulness	-1.6	0.4899	[-2, -2, -2, -2, -1, -1, -1, -2, -2, -1]
unhappier	-2.4	0.8	[-2, -2, -1, -4, -3, -3, -2, -2, -2, -3]
unhappiest	-2.5	0.80623	[-3, -4, -3, -2, -1, -3, -2, -2, -2, -3]
unhappily	-1.9	0.53852	[-2, -1, -2, -2, -3, -2, -1, -2, -2, -2]
unhappiness	-2.4	0.66332	[-3, -2, -2, -3, -2, -2, -3, -1, -3, -3]
unhappinesses	-2.2	0.87178	[-3, -4, -2, -2, -2, -2, -1, -2, -1, -3]
unhappy	-1.8	0.6	[-2, -2, -1, -3, -2, -2, -2, -1, -2, -1]
unhealthy	-2.4	0.66332	[-1, -2, -3, -3, -2, -3, -3, -2, -2, -3]
unified	1.6	0.66332	[1, 2, 2, 1, 2, 1, 2, 1, 3, 1]
unimportant	-1.3	0.45826	[-1, -1, -2, -1, -1, -2, -1, -1, -2, -1]
unimpressed	-1.4	0.66332	[-1, -1, -1, -2, -1, -2, -1, -1, -3, -1]
unimpressive	-1.4	0.4899	[-1, -2, -2, -1, -1, -2, -2, -1, -1, -1]
unintelligent	-2.0	1.18322	[-1, -2, -3, -1, -1, -4, -1, -1, -4, -2]
uninvolved	-2.2	0.9798	[-2, -1, -3, -2, -1, -3, -1, -2, -4, -3]
uninvolving	-2.0	1.18322	[-4, -1, -3, -2, -1, -4, -1, -1, -2, -1]
united	1.8	0.6	[1, 2, 2, 2, 1, 2, 2, 1, 2, 3]
unjust	-2.3	0.45826	[-3, -3, -2, -2, -2, -2, -3, -2, -2, -2]
unkind	-1.6	0.66332	[-2, -2, -1, -1, -1, -2, -3, -1, -1, -2]
unlovable	-2.7	0.9	[-4, -2, -1, -3, -3, -3, -4, -2, -3, -2]
unloved	-1.9	0.53852	[-1, -2, -2, -1, -2, -2, -3, -2, -2, -2]
unlovelier	-1.9	0.7	[-2, -2, -1, -3, -1, -2, -1, -3, -2, -2]
unloveliest	-1.9	0.83066	[-2, -4, -2, -2, -1, -1, -1, -2, -2, -2]
unloveliness	-2.0	0.89443	[-2, -3, -1, -1, -3, -1, -3, -2, -3, -1]
unlovely	-2.1	0.53852	[-2, -3, -2, -3, -1, -2, -2, -2, -2, -2]
unloving	-2.3	0.45826	[-2, -3, -2, -3, -2, -2, -2, -3, -2, -2]
unmatched	-0.3	2.0025	[0, -1, 2, 3, 0, -1, -3, 0, -4, 1]
unmotivated	-1.4	0.4899	[-2, -2, -1, -1, -1, -2, -2, -1, -1, -1]
unpleasant	-2.1	0.53852	[-2, -2, -3, -3, -2, -2, -2, -2, -1, -2]
unprofessional	-2.3	0.78102	[-2, -1, -3, -3, -2, -1, -3, -2, -3, -3]
unprotected	-1.5	0.67082	[-2, -2, -1, -1, -3, -1, -1, -2, -1, -1]
unresearched	-1.1	0.7	[-2, -1, -1, -1, -1, -2, -2, 0, 0, -1]
unsatisfied	-1.7	0.64031	[-2, -1, -2, -1, -1, -2, -3, -1, -2, -2]
unsavory	-1.9	0.53852	[-2, -1, -2, -2, -2, -3, -1, -2, -2, -2]
unsecured	-1.6	0.4899	[-1, -2, -2, -1, -1, -2, -1, -2, -2, -2]
unsettled	-1.3	0.45826	[-1, -1, -1, -2, -2, -1, -2, -1, -1, -1]
unsophisticated	-1.2	0.87178	[-1, -1, -1, -2, -2, -2, -1, -2, 1, -1]
unstable	-1.5	0.5	[-2, -2, -1, -2, -1, -1, -1, -2, -2, -1]
unstoppable	-0.8	1.77764	[0, -4, 2, 0, 1, -2, -2, 0, -3, 0]
unsuccessful	-1.5	0.5	[-2, -1, -1, -2, -2, -1, -1, -1, -2, -2]
unsuccessfully	-1.7	0.78102	[-2, -2, -1, -1, -1, -2, -3, -3, -1, -1]
unsupported	-1.7	0.78102	[-2, 0, -3, -2, -2, -1, -2, -2, -1, -2]
unsure	-1.0	0.44721	[-1, -1, -1, -1, 0, -2, -1, -1, -1, -1]
unsurely	-1.3	0.78102	[-1, 0, -1, -1, -1, -3, -1, -2, -2, -1]
untarnished	1.6	1.35647	[3, 2, 2, 1, 1, 2, -2, 3, 2, 2]
unwanted	-0.9	1.3	[-1, -2, -2, -1, -2, 1, 2, -1, -2, -1]
unwelcome	-1.7	0.45826	[-2, -2, -2, -1, -1, -2, -2, -1, -2, -2]
unworthy	-2.0	0.44721	[-3, -2, -2, -2, -2, -2, -1, -2, -2, -2]
upset	-1.6	0.4899	[-1, -1, -2, -2, -1, -1, -2, -2, -2, -2]
upsets	-1.5	0.67082	[-2, -3, -1, -1, -1, -1, -2, -2, -1, -1]
upsetter	-1.9	0.7	[-2, -2, -1, -1, -3, -2, -3, -1, -2, -2]
upsetters	-2.0	0.63246	[-3, -3, -1, -2, -2, -2, -2, -2, -1, -2]
upsetting	-2.1	0.53852	[-2, -3, -2, -3, -2, -1, -2, -2, -2, -2]
uptight	-1.6	0.4899	[-2, -1, -2, -1, -1, -2, -2, -1, -2, -2]
uptightness	-1.2	0.4	[-1, -2, -1, -2, -1, -1, -1, -1, -1, -1]
urgent	0.8	1.16619	[3, -1, 0, 1, 1, 0, 0, 0, 2, 2]
useful	1.9	0.83066	[2, 1, 1, 2, 2, 4, 2, 1, 2, 2]
usefully	1.8	0.6	[2, 2, 1, 3, 1, 2, 1, 2, 2, 2]
usefulness	1.2	1.32665	[3, 1, 3, -1, 2, 2, 1, 1, -1, 1]
useless	-1.8	0.4	[-2, -1, -2, -2, -1, -2, -2, -2, -2, -2]
uselessly	-1.5	0.67082	[-2, -3, -1, -1, -1, -1, -2, -2, -1, -1]
uselessness	-1.6	0.8	[-3, -2, -2, -2, -1, -2, 0, -1, -1, -2]
v.v	-2.9	0.9434	[-3, -3, -4, -3, -1, -3, -4, -2, -4, -2]
vague	-0.4	0.8	[0, -1, -1, -1, 0, -1, 1, -1, 1, -1]
vain	-1.8	0.6	[-2, -1, -2, -3, -2, -1, -1, -2, -2, -2]
validate	1.5	0.92195	[1, 2, 1, 1, 1, 3, 1, 3, 0, 2]
validated	0.9	0.83066	[2, 1, 0, 1, 1, 0, 2, 0, 0, 2]
validates	1.4	0.66332	[1, 1, 1, 2, 3, 1, 2, 1, 1, 1]
validating	1.4	0.8	[2, 2, 1, 3, 0, 2, 1, 1, 1, 1]
valuable	2.1	0.83066	[3, 2, 4, 2, 2, 2, 2, 1, 1, 2]
valuableness	1.7	0.78102	[2, 2, 1, 3, 3, 2, 1, 1, 1, 1]
valuables	2.1	0.83066	[4, 1, 2, 2, 3, 2, 1, 2, 2, 2]
valuably	2.3	1.00499	[3, 4, 4, 1, 2, 2, 2, 2, 1, 2]
value	1.4	1.11355	[2, 3, 0, 1, 1, 3, 0, 2, 0, 2]
valued	1.9	0.7	[3, 1, 2, 1, 2, 2, 2, 3, 2, 1]
values	1.7	1.18743	[2, 2, 2, 4, 0, 1, 0, 1, 3, 2]
valuing	1.4	0.91652	[1, 0, 3, 2, 2, 2, 2, 0, 1, 1]
vanity	-0.9	1.7	[-2, -3, -3, 0, -2, -1, 2, 2, -1, -1]
verdict	0.6	0.91652	[0, 0, 0, 0, 0, 0, 0, 2, 2, 2]
verdicts	0.3	1.1	[0, 0, 0, 0, 2, 2, 0, -2, 1, 0]
vested	0.6	1.28062	[2, -2, 1, 3, 1, 0, 0, 1, 0, 0]
vexation	-1.9	1.04403	[0, -2, -3, -3, -2, -2, -2, -3, 0, -2]
vexing	-2.0	0.44721	[-2, -2, -2, -1, -2, -2, -2, -3, -2, -2]
vibrant	2.4	0.8	[2, 3, 1, 1, 3, 3, 3, 2, 3, 3]
vicious	-1.5	1.5	[1, -2, -3, -1, -1, -3, 1, -3, -1, -3]
viciously	-1.3	1.26886	[-2, -3, -1, -2, -2, -1, -1, 2, -1, -2]
viciousness	-2.4	1.35647	[-3, -1, -4, -2, -3, -3, 1, -3, -3, -3]
viciousnesses	-0.6	1.62481	[0, -1, -1, -3, 0, -3, 2, 1, 1, -2]
victim	-1.1	1.92094	[-1, -2, 2, -3, -3, -2, -3, 1, 2, -2]
victimhood	-2.0	0.44721	[-2, -2, -2, -2, -2, -1, -3, -2, -2, -2]
victimhoods	-0.9	1.37477	[-1, 0, -1, 1, -2, -1, -4, 1, -1, -1]
victimise	-1.1	1.92094	[-3, -3, -2, -2, -1, 2, -2, 1, -3, 2]
victimised	-1.5	1.56525	[-2, -2, -2, 1, -3, 2, -2, -3, -2, -2]
victimises	-1.2	2.31517	[-3, -3, -4, 2, -2, -2, 1, 2, 1, -4]
victimising	-2.5	0.67082	[-3, -1, -3, -2, -2, -3, -3, -2, -3, -3]
victimization	-2.3	0.78102	[-1, -3, -3, -2, -3, -1, -3, -2, -3, -2]
victimizations	-1.5	1.85742	[-2, -3, -3, -1, -2, 2, 2, -2, -3, -3]
victimize	-2.5	0.67082	[-3, -2, -4, -2, -2, -2, -2, -3, -3, -2]
victimized	-1.8	1.53623	[-2, -1, -3, -3, -3, 1, 1, -2, -3, -3]
victimizer	-1.8	1.72047	[-3, -2, -3, -3, -2, 2, 1, -2, -3, -3]
victimizers	-1.6	1.68523	[-3, -2, -3, 1, -3, -1, -2, 2, -2, -3]
victimizes	-1.5	1.9105	[-2, -1, -4, -3, -2, 2, 2, -2, -3, -2]
victimizing	-2.6	0.4899	[-2, -3, -3, -3, -2, -3, -2, -3, -2, -3]
victimless	0.6	0.4899	[0, 1, 0, 1, 1, 0, 0, 1, 1, 1]
victimologies	-0.6	1.35647	[-2, 0, -2, -1, 0, 2, 1, 0, -2, -2]
victimologist	-0.5	0.67082	[0, -1, -1, 0, 0, 0, 0, -1, -2, 0]
victimologists	-0.4	0.91652	[0, 1, 0, -2, -2, 0, 0, 0, -1, 0]
victimology	0.3	1.00499	[0, 0, 0, -1, 0, 1, 0, 0, 3, 0]
victims	-1.3	2.05183	[-3, -1, -3, -3, -3, 2, 1, -2, 2, -3]
vigilant	0.7	0.9	[0, 2, 0, 2, 0, -1, 1, 1, 1, 1]
vigor	1.1	1.37477	[0, 3, 2, 1, 2, 2, 0, 1, -2, 2]
vigorish	-0.4	1.2	[0, -3, -1, -1, 0, -1, 0, 0, 2, 0]
vigorishes	0.4	1.56205	[0, 0, 2, 1, 2, 0, -2, -2, 0, 3]
vigoroso	1.5	0.67082	[2, 0, 1, 2, 2, 1, 2, 1, 2, 2]
vigorously	0.5	0.92195	[0, 0, 0, 1, 2, 0, 2, -1, 1, 0]
vigorousness	0.4	1.11355	[0, 3, 0, -1, -1, 0, 0, 1, 1, 1]
vigors	1.0	1.0	[0, 1, 0, 1, 0, 0, 1, 3, 2, 2]
vigour	0.9	0.9434	[0, 2, 2, 2, 1, 1, 0, 1, -1, 1]
vigours	0.4	1.68523	[-4, 1, 1, 1, -1, 1, 2, 2, 0, 1]
vile	-3.1	0.83066	[-4, -2, -4, -4, -2, -3, -3, -3, -2, -4]
villain	-2.6	0.4899	[-3, -2, -2, -3, -2, -2, -3, -3, -3, -3]
villainess	-2.9	0.53852	[-3, -2, -3, -4, -3, -2, -3, -3, -3, -3]
villainesses	-2.0	1.18322	[-2, -3, -2, -2, -2, -3, 1, -3, -1, -3]
villainies	-2.3	1.00499	[-3, -2, -3, -3, -3, -1, -3, -2, -3, 0]
villainous	-2.0	0.63246	[-3, -2, -1, -2, -2, -2, -2, -1, -2, -3]
villainously	-2.9	0.53852	[-3, -3, -3, -3, -3, -4, -2, -3, -2, -3]
villainousness	-2.7	0.9	[-4, -3, -4, -3, -1, -3, -2, -2, -2, -3]
villains	-3.4	0.91652	[-4, -3, -4, -3, -4, -3, -4, -4, -1, -4]
villainy	-2.6	0.4899	[-3, -2, -3, -3, -2, -2, -2, -3, -3, -3]
vindicate	0.3	1.95192	[2, -1, -2, -3, -1, 3, 0, 3, 1, 1]
vindicated	1.8	1.16619	[1, 3, -1, 2, 2, 1, 3, 2, 2, 3]
vindicates	1.6	0.66332	[2, 3, 2, 2, 2, 1, 1, 1, 1, 1]
vindicating	-1.1	1.97231	[-3, -2, 2, -3, -2, 1, 1, 1, -3, -3]
violate	-2.2	0.6	[-3, -3, -2, -3, -2, -2, -1, -2, -2, -2]
violated	-2.4	0.66332	[-3, -3, -3, -3, -2, -3, -1, -2, -2, -2]
violater	-2.6	0.91652	[-3, -3, -4, -4, -2, -3, -2, -2, -2, -1]
violaters	-2.4	0.8	[-1, -3, -1, -2, -3, -3, -3, -2, -3, -3]
violates	-2.3	0.9	[-3, -2, -4, -3, -2, -3, -2, -2, -1, -1]
violating	-2.5	0.92195	[-2, -3, -3, -1, -3, -2, -4, -1, -3, -3]
violation	-2.2	0.9798	[-3, -1, -1, -3, -3, -2, -1, -2, -2, -4]
violations	-2.4	0.66332	[-2, -2, -2, -3, -2, -4, -2, -2, -2, -3]
violative	-2.4	0.66332	[-2, -3, -3, -3, -1, -3, -2, -2, -2, -3]
violator	-2.4	1.0198	[-1, -4, -3, -2, -3, -2, -2, -1, -4, -2]
violators	-1.9	1.51327	[-2, 2, -3, -4, -1, -2, -3, -2, -2, -2]
violence	-3.1	0.53852	[-2, -3, -3, -3, -3, -4, -4, -3, -3, -3]
violent	-2.9	0.53852	[-3, -3, -3, -3, -3, -4, -3, -2, -2, -3]
violently	-2.8	0.74833	[-3, -3, -2, -3, -3, -3, -4, -1, -3, -3]
virtue	1.8	0.74833	[1, 2, 3, 2, 2, 2, 3, 1, 1, 1]
virtueless	-1.4	1.0198	[-2, 0, -2, -3, -1, -3, -1, -1, -1, 0]
virtues	1.5	0.80623	[2, 2, 2, 1, 0, 1, 3, 1, 2, 1]
virtuosa	1.7	1.48661	[0, 4, 2, 3, 2, 3, 0, 2, -1, 2]
virtuosas	1.8	0.87178	[2, 3, 1, 2, 1, 0, 3, 2, 2, 2]
virtuose	1.0	1.41421	[2, 1, 0, 2, 1, -1, 1, 1, -1, 4]
virtuosi	0.9	1.37477	[2, 0, 0, 2, 1, 0, 0, 1, -1, 4]
virtuosic	2.2	1.07703	[2, 2, 4, 1, 0, 3, 3, 2, 2, 3]
virtuosity	2.1	0.83066	[3, 3, 3, 2, 1, 3, 2, 2, 1, 1]
virtuoso	2.0	1.0	[2, 2, 3, 2, 1, 0, 3, 3, 3, 1]
virtuosos	1.8	1.16619	[2, 3, 1, -1, 2, 1, 3, 3, 2, 2]
virtuous	2.4	1.2	[0, 3, 2, 1, 3, 4, 2, 2, 4, 3]
virtuously	1.8	1.16619	[3, 2, 3, 1, 3, 1, -1, 2, 2, 2]
virtuousness	2.0	1.09545	[3, 4, 2, 2, 0, 1, 2, 3, 2, 1]
virulent	-2.7	0.64031	[-3, -2, -4, -2, -3, -3, -2, -3, -2, -3]
vision	1.0	1.0	[0, 0, 0, 2, 1, 3, 2, 1, 1, 0]
visionary	2.4	1.0198	[1, 3, 1, 2, 4, 1, 3, 3, 3, 3]
visioning	1.1	0.9434	[1, 2, 0, 0, 3, 0, 1, 1, 2, 1]
visions	0.9	0.9434	[2, 0, 0, 0, 0, 1, 2, 2, 0, 2]
vital	1.2	1.46969	[-3, 2, 1, 1, 2, 2, 1, 2, 2, 2]
vitalise	1.1	0.9434	[1, 2, 0, 2, 2, 0, 2, 0, 2, 0]
vitalised	0.6	1.49666	[1, -2, 2, 0, 2, 1, -2, 2, 0, 2]
vitalises	1.1	1.3	[1, 2, 2, 0, 2, 2, -2, 2, 0, 2]
vitalising	2.1	0.53852	[2, 2, 3, 2, 3, 2, 2, 1, 2, 2]
vitalism	0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, 2, 0]
vitalist	0.3	0.64031	[0, 0, 0, 0, 0, 0, 1, 0, 2, 0]
vitalists	0.3	1.34536	[2, -3, 1, 0, 0, 0, 1, 0, 2, 0]
vitalities	1.2	0.87178	[2, 1, 3, 1, 1, 0, 2, 1, 0, 1]
vitality	1.3	0.9	[3, 2, 0, 1, 1, 1, 2, 0, 2, 1]
vitalization	1.6	0.91652	[2, 3, 3, 1, 2, 1, 0, 2, 1, 1]
vitalizations	0.8	0.74833	[0, 1, 1, 2, 0, 0, 0, 2, 1, 1]
vitalize	1.6	0.66332	[3, 2, 2, 1, 2, 1, 1, 1, 2, 1]
vitalized	1.5	0.67082	[1, 1, 2, 2, 0, 2, 1, 2, 2, 2]
vitalizes	1.4	0.4899	[2, 1, 1, 2, 1, 2, 1, 2, 1, 1]
vitalizing	1.3	0.9	[3, 1, 0, 0, 1, 1, 2, 2, 2, 1]
vitally	1.1	0.53852	[0, 2, 1, 1, 1, 1, 2, 1, 1, 1]
vitals	1.1	0.7	[1, 0, 1, 2, 2, 0, 2, 1, 1, 1]
vitamin	1.2	0.87178	[3, 1, 0, 0, 1, 2, 1, 2, 1, 1]
vitriolic	-2.1	0.83066	[-2, -2, -2, -4, -3, -2, -1, -1, -2, -2]
vivacious	1.8	0.9798	[0, 1, 3, 3, 3, 2, 2, 2, 1, 1]
vociferous	-0.8	0.9798	[1, -2, -1, -2, -1, -1, 1, -1, -1, -1]
vulnerabilities	-0.6	1.49666	[0, -3, -1, -1, -1, 2, -2, 2, -1, -1]
vulnerability	-0.9	1.75784	[1, -1, -1, -2, 1, -3, -1, 2, -4, -1]
vulnerable	-0.9	1.37477	[-2, -2, 2, -1, -3, -1, 1, -1, -1, -1]
vulnerableness	-1.1	1.04403	[-1, -1, -2, -3, -1, 1, 0, -2, -1, -1]
vulnerably	-1.2	1.46969	[-2, -2, 2, -1, -2, -3, 1, -1, -2, -2]
vulture	-2.0	0.89443	[-2, -3, -1, -2, -1, -1, -1, -3, -3, -3]
vultures	-1.3	1.55242	[-2, -3, -2, 2, -2, -2, -3, -1, -1, 1]
w00t	2.2	1.32665	[3, 2, 3, 2, 0, 4, 0, 4, 2, 2]
walkout	-1.3	0.9	[-1, -2, -2, -1, -1, -2, -1, 1, -2, -2]
walkouts	-0.7	1.00499	[-2, -2, -1, 0, -1, -1, -1, 1, -1, 1]
wanker	-2.5	0.67082	[-2, -3, -3, -2, -3, -3, -2, -1, -3, -3]
want	0.3	1.18743	[0, -2, 0, 1, 2, -1, 2, 1, 0, 0]
war	-2.9	1.13578	[-1, -3, -4, -4, -3, -1, -2, -3, -4, -4]
warfare	-1.2	1.16619	[-2, 0, -1, -2, 0, -3, 1, -2, -2, -1]
warfares	-1.8	0.87178	[-2, -1, -2, -2, -3, -1, -3, 0, -2, -2]
warm	0.9	0.7	[1, 0, 0, 1, 1, 2, 1, 2, 1, 0]
warmblooded	0.2	0.6	[0, 0, 2, 0, 0, 0, 0, 0, 0, 0]
warmed	1.1	0.53852	[2, 0, 1, 1, 1, 2, 1, 1, 1, 1]
warmer	1.2	0.9798	[2, 2, 2, 1, -1, 0, 2, 1, 1, 2]
warmers	1.0	0.44721	[1, 1, 1, 2, 1, 1, 1, 0, 1, 1]
warmest	1.7	1.34536	[3, 2, 1, 2, 3, 2, 2, 2, -2, 2]
warmhearted	1.8	0.6	[3, 2, 2, 2, 2, 1, 2, 1, 1, 2]
warmheartedness	2.7	0.64031	[2, 4, 3, 2, 3, 3, 3, 2, 2, 3]
warming	0.6	0.8	[0, 0, 2, 2, 1, 1, 0, 0, 0, 0]
warmish	1.4	0.66332	[1, 3, 2, 1, 1, 1, 1, 2, 1, 1]
warmly	1.7	0.64031	[2, 1, 2, 1, 2, 1, 2, 1, 2, 3]
warmness	1.5	0.92195	[3, 1, 2, 1, 0, 1, 3, 2, 1, 1]
warmonger	-2.9	1.13578	[-3, 0, -4, -4, -2, -3, -4, -3, -3, -3]
warmongering	-2.5	0.67082	[-2, -3, -3, -1, -2, -3, -3, -2, -3, -3]
warmongers	-2.8	0.87178	[-2, -3, -4, -4, -3, -1, -2, -3, -3, -3]
warmouth	0.4	0.66332	[0, 0, 2, 0, 0, 0, 0, 1, 1, 0]
warmouths	-0.8	1.32665	[-1, -1, -2, -1, -2, -2, 0, 2, 1, -2]
warms	1.1	0.7	[2, 2, 1, 2, 1, 0, 1, 1, 0, 1]
warmth	2.0	0.44721	[2, 2, 2, 2, 1, 2, 2, 3, 2, 2]
warmup	0.4	0.66332	[0, 2, 0, 1, 1, 0, 0, 0, 0, 0]
warmups	0.8	0.9798	[0, 2, 0, 0, 0, 2, 2, 0, 2, 0]
warn	-0.4	1.35647	[0, -1, 0, -2, -1, 2, 1, -2, 1, -2]
warned	-1.1	0.53852	[-1, -1, -2, 0, -1, -1, -1, -2, -1, -1]
warning	-1.4	1.0198	[-2, -1, -1, -1, -2, 0, -4, -1, -1, -1]
warnings	-1.2	0.9798	[-2, -1, 0, -1, -2, 0, 0, -1, -3, -2]
warns	-0.4	1.0198	[1, -1, -1, 1, -1, -1, -2, 1, 0, -1]
warred	-2.4	0.8	[-2, -2, -4, -1, -3, -2, -3, -2, -3, -2]
warring	-1.9	1.04403	[-3, -3, 0, -1, -2, -2, -3, -1, -1, -3]
wars	-2.6	0.8	[-2, -3, -1, -3, -2, -4, -3, -3, -2, -3]
warsaw	-0.1	0.3	[0, -1, 0, 0, 0, 0, 0, 0, 0, 0]
warsaws	-0.2	0.4	[0, 0, 0, -1, 0, 0, 0, 0, -1, 0]
warship	-0.7	0.9	[0, 0, 0, 0, 0, -2, -1, 0, -2, -2]
warships	-0.5	0.80623	[0, -1, 0, 0, -2, 0, 0, -2, 0, 0]
warstle	0.1	0.7	[0, 0, 0, 0, 0, 0, 0, 0, 2, -1]
waste	-1.8	0.9798	[-2, -2, -1, -3, -2, -1, -1, -4, -1, -1]
wasted	-2.2	0.6	[-2, -3, -2, -3, -1, -3, -2, -2, -2, -2]
wasting	-1.7	0.9	[-3, -1, -2, -2, -1, -2, -3, 0, -1, -2]
wavering	-0.6	1.0198	[-1, -1, 0, 0, -1, -1, -1, 2, -1, -2]
weak	-1.9	0.7	[-1, -3, -2, -2, -3, -2, -2, -1, -2, -1]
weaken	-1.8	0.6	[-2, -2, -2, -1, -1, -3, -2, -2, -1, -2]
weakened	-1.3	0.9	[-2, -1, -1, -1, -1, -2, -2, -2, 1, -2]
weakener	-1.6	1.11355	[-2, -1, -1, -1, -2, -2, -3, -3, 1, -2]
weakeners	-1.3	0.45826	[-1, -2, -1, -2, -1, -1, -2, -1, -1, -1]
weakening	-1.3	0.45826	[-2, -1, -1, -1, -1, -1, -1, -2, -2, -1]
weakens	-1.3	0.45826	[-1, -1, -1, -1, -1, -2, -1, -2, -1, -2]
weaker	-1.9	0.83066	[-2, -2, -2, -2, -2, -1, -4, -1, -1, -2]
weakest	-2.3	0.64031	[-2, -4, -2, -3, -2, -2, -2, -2, -2, -2]
weakfish	-0.2	1.07703	[0, -2, 0, 0, 0, 0, -2, 0, 2, 0]
weakfishes	-0.6	0.8	[0, -1, 0, -2, 0, 0, -1, 0, -2, 0]
weakhearted	-1.6	0.8	[-1, -3, -1, -1, -2, -1, -3, -2, -1, -1]
weakish	-1.2	0.4	[-1, -2, -1, -1, -1, -1, -2, -1, -1, -1]
weaklier	-1.5	0.67082	[-1, -2, -1, -3, -2, -1, -1, -2, -1, -1]
weakliest	-2.1	0.83066	[-2, -2, -2, -2, -3, -1, -2, -1, -4, -2]
weakling	-1.3	1.00499	[-1, -2, -1, -3, -2, -2, -1, -1, 1, -1]
weaklings	-1.4	0.66332	[-2, -1, -1, -1, -1, -2, -2, 0, -2, -2]
weakly	-1.8	0.87178	[-2, -2, -2, -2, -4, -1, -1, -1, -1, -2]
weakness	-1.8	0.6	[-2, -2, -2, -1, -1, -2, -1, -3, -2, -2]
weaknesses	-1.5	0.5	[-2, -2, -1, -1, -2, -1, -1, -2, -1, -2]
weakside	-1.1	1.37477	[-3, -2, -3, -1, -2, -1, 1, 1, -1, 0]
wealth	2.2	0.4	[2, 3, 2, 2, 2, 3, 2, 2, 2, 2]
wealthier	2.2	0.6	[3, 2, 1, 3, 2, 2, 2, 3, 2, 2]
wealthiest	2.2	0.9798	[2, 4, 4, 1, 2, 1, 2, 2, 2, 2]
wealthily	2.0	0.89443	[2, 3, 1, 4, 2, 1, 1, 2, 2, 2]
wealthiness	2.4	1.11355	[2, 4, 2, 4, 1, 2, 4, 1, 2, 2]
wealthy	1.5	1.0247	[1, 2, 1, 4, 1, 0, 2, 1, 2, 1]
weapon	-1.2	0.87178	[0, -2, -2, -1, 0, -2, -1, -2, 0, -2]
weaponed	-1.4	0.91652	[-2, -2, -3, -1, -1, 0, 0, -2, -1, -2]
weaponless	0.1	1.13578	[2, -1, 0, 0, -1, 1, -1, 0, 2, -1]
weaponry	-0.9	0.7	[-2, -2, 0, -1, 0, -1, -1, -1, 0, -1]
weapons	-1.9	0.9434	[-2, -1, -2, -2, -1, -3, -3, -3, -2, 0]
weary	-1.1	1.13578	[-2, -1, -2, -3, 0, -1, -1, -2, 0, 1]
weep	-2.7	0.9	[-2, -4, -4, -3, -3, -3, -3, -2, -1, -2]
weeper	-1.9	0.53852	[-2, -2, -2, -3, -1, -1, -2, -2, -2, -2]
weepers	-1.1	1.13578	[-2, -2, -1, -2, -1, 1, -2, 1, -1, -2]
weepie	-0.4	0.91652	[0, 1, -1, 0, -1, -2, 0, -1, -1, 1]
weepier	-1.8	0.87178	[-3, -3, -2, -1, -2, -2, -2, 0, -1, -2]
weepies	-1.6	0.8	[-2, -3, -2, -1, -1, -2, -2, 0, -1, -2]
weepiest	-2.4	0.91652	[-4, -2, -2, -2, -2, -1, -2, -2, -4, -3]
weeping	-1.9	0.9434	[-2, -2, -1, -1, -1, -1, -4, -2, -2, -3]
weepings	-1.9	0.9434	[-2, -2, -3, 0, -1, -2, -2, -3, -1, -3]
weeps	-1.4	1.35647	[-2, -3, -1, -2, -1, -3, 1, -2, 1, -2]
weepy	-1.3	1.55242	[-2, -3, -1, -2, 2, -3, -1, -2, 1, -2]
weird	-0.7	0.64031	[-1, 0, 0, -1, -1, -1, 0, 0, -2, -1]
weirder	-0.5	0.80623	[1, -1, -1, -1, -1, 1, -1, -1, 0, -1]
weirdest	-0.9	1.22066	[-2, 0, -2, -1, -1, -1, -3, 1, 1, -1]
weirdie	-1.3	0.45826	[-1, -2, -1, -2, -1, -1, -2, -1, -1, -1]
weirdies	-1.0	0.63246	[0, -1, -1, -1, -1, 0, -2, -2, -1, -1]
weirdly	-1.2	0.74833	[0, -1, -1, -2, -3, -1, -1, -1, -1, -1]
weirdness	-0.9	1.64012	[-3, -2, -1, -1, 2, -1, 1, -3, 1, -2]
weirdnesses	-0.7	1.00499	[-1, -2, 0, -1, -2, 1, -1, -1, -1, 1]
weirdo	-1.8	0.6	[-2, -2, -2, -2, -2, -2, -1, -1, -3, -1]
weirdoes	-1.3	0.64031	[-2, -1, -2, -1, -1, -2, -1, 0, -1, -2]
weirdos	-1.1	0.9434	[-1, -1, -1, -2, 1, -3, -1, -1, -1, -1]
weirds	-0.6	0.4899	[-1, -1, -1, 0, -1, 0, 0, -1, 0, -1]
weirdy	-0.9	0.83066	[-1, -1, 0, 0, -1, 0, -2, -2, 0, -2]
welcome	2.0	0.63246	[1, 3, 2, 1, 2, 2, 2, 2, 3, 2]
welcomed	1.4	0.4899	[1, 1, 2, 2, 1, 2, 1, 2, 1, 1]
welcomely	1.9	0.53852	[2, 2, 2, 2, 1, 3, 2, 1, 2, 2]
welcomeness	2.0	0.89443	[2, 3, 1, 2, 3, 0, 2, 3, 2, 2]
welcomer	1.4	0.4899	[1, 1, 2, 2, 2, 2, 1, 1, 1, 1]
welcomers	1.9	0.7	[2, 2, 3, 2, 2, 1, 1, 3, 1, 2]
welcomes	1.7	0.78102	[1, 1, 2, 2, 3, 3, 1, 2, 1, 1]
welcoming	1.9	0.7	[2, 2, 1, 1, 2, 2, 2, 3, 3, 1]
well	1.1	1.04403	[0, 0, 2, 0, 2, 0, 1, 1, 3, 2]
welladay	0.3	1.18743	[2, -2, 0, 0, -1, 1, 0, 1, 2, 0]
wellaway	-0.8	1.98997	[3, -2, -3, -3, -1, -2, 1, -2, -1, 2]
wellborn	1.8	0.74833	[2, 1, 2, 1, 2, 2, 1, 3, 1, 3]
welldoer	2.5	0.67082	[2, 2, 2, 3, 2, 3, 4, 3, 2, 2]
welldoers	1.6	0.8	[3, 1, 1, 0, 2, 1, 2, 2, 2, 2]
welled	0.4	0.8	[0, 0, 2, 0, 0, 0, 0, 0, 2, 0]
wellhead	0.1	0.3	[0, 0, 1, 0, 0, 0, 0, 0, 0, 0]
wellheads	0.5	0.92195	[0, 2, 0, 2, -1, 0, 1, 1, 0, 0]
wellhole	-0.1	0.3	[0, 0, 0, -1, 0, 0, 0, 0, 0, 0]
wellies	0.4	0.4899	[0, 1, 0, 0, 0, 1, 0, 0, 1, 1]
welling	1.6	0.8	[2, 0, 1, 2, 2, 1, 3, 2, 2, 1]
wellness	1.9	0.9434	[1, 2, 2, 1, 2, 1, 1, 3, 4, 2]
wells	1.0	1.0	[2, 0, 3, 0, 1, 0, 2, 1, 1, 0]
wellsite	0.5	0.67082	[0, 0, 1, 2, 0, 0, 0, 0, 1, 1]
wellspring	1.5	0.92195	[3, 1, 1, 1, 0, 2, 2, 3, 1, 1]
wellsprings	1.4	0.8	[1, 0, 0, 2, 2, 2, 1, 2, 2, 2]
welly	0.2	0.4	[0, 0, 0, 1, 0, 1, 0, 0, 0, 0]
wept	-2.0	1.09545	[-3, -2, -3, -3, -1, -3, 0, -1, -1, -3]
whimsical	0.3	1.61555	[2, 1, 1, 2, -1, 1, -3, -2, 1, 1]
whine	-1.5	1.11803	[-1, -4, -1, -1, -1, -3, 0, -2, -1, -1]
whined	-0.9	1.04403	[-2, -1, -2, -1, -1, -1, -2, 1, 1, -1]
whiner	-1.2	0.4	[-1, -2, -1, -1, -1, -2, -1, -1, -1, -1]
whiners	-0.6	1.95959	[-2, 0, -2, -2, 4, 1, 1, -2, -2, -2]
whines	-1.8	0.6	[-2, -2, -2, -1, -2, -2, -3, -1, -2, -1]
whiney	-1.3	0.45826	[-1, -2, -1, -1, -1, -2, -2, -1, -1, -1]
whining	-0.9	1.51327	[-3, 0, -1, -1, -3, -1, 1, 2, -2, -1]
whitewash	0.1	0.7	[-1, 0, 1, -1, 0, 0, 0, 1, 1, 0]
whore	-3.3	0.64031	[-4, -4, -3, -2, -3, -4, -3, -3, -4, -3]
whored	-2.8	0.87178	[-2, -3, -4, -2, -2, -3, -4, -4, -2, -2]
whoredom	-2.1	2.02237	[-4, -2, -4, -3, -3, -3, -4, -1, 2, 1]
whoredoms	-2.4	1.11355	[-1, -3, 0, -3, -2, -3, -3, -4, -2, -3]
whorehouse	-1.1	2.11896	[-2, -2, -2, 3, 3, -3, -3, -2, -1, -2]
whorehouses	-1.9	1.92094	[-4, -3, -4, -3, 0, 0, -3, 2, -1, -3]
whoremaster	-1.9	1.22066	[-1, -3, -3, -2, -1, -3, 0, 0, -3, -3]
whoremasters	-1.5	1.85742	[-3, -1, -1, -4, -2, 2, -1, 1, -2, -4]
whoremonger	-2.6	0.91652	[-3, -1, -3, -3, -3, -3, -3, -1, -4, -2]
whoremongers	-2.0	1.78885	[-4, -3, 0, -3, -3, -3, -3, 1, 1, -3]
whores	-3.0	1.0	[-3, -3, -4, -2, -1, -3, -4, -4, -2, -4]
whoreson	-2.2	1.46969	[-2, -3, -4, -4, -3, -1, -1, 1, -3, -2]
whoresons	-2.5	1.20416	[-3, -3, -2, -2, 0, -4, -3, -1, -3, -4]
wicked	-2.4	0.8	[-3, -4, -3, -3, -2, -2, -2, -1, -2, -2]
wickeder	-2.2	1.32665	[-2, -3, -1, -4, 1, -3, -3, -3, -2, -2]
wickedest	-2.9	1.04403	[-3, -1, -3, -3, -3, -3, -1, -4, -4, -4]
wickedly	-2.1	0.83066	[-2, -2, -1, -3, -2, -3, -1, -3, -1, -3]
wickedness	-2.1	0.83066	[-2, -1, -2, -2, -3, -1, -2, -4, -2, -2]
wickednesses	-2.2	1.16619	[-1, -2, -4, -2, -3, -1, -3, -4, -1, -1]
widowed	-2.1	1.22066	[0, -4, -2, -4, -3, -2, -2, -2, -1, -1]
willingness	1.1	0.7	[0, 2, 1, 1, 2, 2, 1, 0, 1, 1]
wimp	-1.4	1.28062	[-2, -3, -1, -2, -1, -2, -2, -1, 2, -2]
wimpier	-1.0	1.18322	[-1, -2, -2, -1, -2, 0, 1, -2, 1, -2]
wimpiest	-0.9	1.22066	[-3, -1, -2, -1, -2, -1, 1, 1, 0, -1]
wimpiness	-1.2	0.9798	[1, -1, -3, -1, -2, -1, -1, -1, -2, -1]
wimpish	-1.6	0.4899	[-2, -1, -2, -1, -2, -2, -1, -2, -2, -1]
wimpishness	-0.2	1.249	[-3, -1, 0, -1, -1, 1, 1, 1, 1, 0]
wimple	-0.2	0.74833	[0, -1, 0, 0, 0, 0, -2, 0, 0, 1]
wimples	-0.3	0.78102	[-2, 0, 0, 0, 0, -1, 0, -1, 1, 0]
wimps	-1.0	1.18322	[-2, -2, -1, -1, 0, -2, -2, 1, -2, 1]
wimpy	-0.9	1.04403	[-2, -1, -1, -1, -1, -2, -1, 1, -2, 1]
win	2.8	0.87178	[3, 2, 4, 3, 2, 4, 3, 1, 3, 3]
winnable	1.8	0.6	[3, 2, 2, 2, 2, 1, 1, 1, 2, 2]
winned	1.8	0.6	[2, 2, 2, 2, 1, 2, 1, 1, 3, 2]
winner	2.8	0.87178	[2, 2, 2, 3, 4, 2, 3, 4, 2, 4]
winners	2.1	1.44568	[3, 3, 2, 3, 3, 2, 2, -2, 3, 2]
winning	2.4	0.4899	[2, 3, 3, 2, 2, 2, 3, 3, 2, 2]
winningly	2.3	1.48661	[1, 3, 4, 3, 3, 1, 2, 3, -1, 4]
winnings	2.5	0.92195	[3, 4, 3, 2, 2, 3, 1, 1, 3, 3]
winnow	-0.3	1.00499	[0, -1, 0, -2, 1, 1, -2, 0, 0, 0]
winnower	-0.1	0.3	[0, 0, 0, 0, 0, -1, 0, 0, 0, 0]
winnowers	-0.2	0.6	[0, 0, 0, 0, 0, 0, 0, 0, -2, 0]
winnowing	-0.1	0.53852	[0, 0, -1, 0, 0, 0, 0, 1, -1, 0]
winnows	-0.2	0.4	[0, 0, -1, -1, 0, 0, 0, 0, 0, 0]
wins	2.7	0.78102	[2, 2, 3, 3, 4, 4, 3, 2, 2, 2]
wisdom	2.4	0.66332	[2, 3, 4, 2, 3, 2, 2, 2, 2, 2]
wise	2.1	0.83066	[2, 3, 1, 4, 2, 2, 1, 2, 2, 2]
wiseacre	-1.2	1.16619	[-2, -1, -2, -2, -2, -1, -2, 1, 1, -2]
wiseacres	-0.1	0.9434	[-2, 1, 0, -1, 0, 1, 1, -1, 0, 0]
wiseass	-1.8	0.6	[-2, -2, -1, -1, -2, -2, -3, -2, -1, -2]
wiseasses	-1.5	1.36015	[-1, -2, 2, -2, -1, -3, -2, -3, -1, -2]
wisecrack	-0.1	1.22066	[-1, -1, -1, -2, 2, 1, -1, 1, 1, 0]
wisecracked	-0.5	0.92195	[1, 1, -1, -1, 0, -1, -2, 0, -1, -1]
wisecracker	-0.1	0.7	[-1, 1, -1, -1, 0, 0, 0, 0, 1, 0]
wisecrackers	0.1	1.04403	[-1, 0, 1, 1, 0, 2, 1, -1, -1, -1]
wisecracking	-0.6	0.4899	[0, 0, -1, 0, -1, 0, -1, -1, -1, -1]
wisecracks	-0.3	1.55242	[1, 2, -1, 2, -3, 0, 0, -2, -1, -1]
wised	1.5	0.67082	[2, 2, 2, 1, 1, 2, 0, 1, 2, 2]
wiseguys	0.3	1.9	[4, -1, 1, -1, 1, 2, 2, -2, -2, -1]
wiselier	0.9	1.3	[1, 2, 2, -2, 0, 1, 3, 1, 0, 1]
wiseliest	1.6	1.49666	[1, 4, 2, 2, 1, 2, 2, 3, -2, 1]
wisely	1.8	0.6	[1, 2, 3, 2, 1, 2, 2, 1, 2, 2]
wiseness	1.9	0.7	[2, 3, 2, 2, 3, 1, 1, 1, 2, 2]
wisenheimer	-1.0	1.18322	[-1, 1, -1, -1, -1, 0, -1, -3, 0, -3]
wisenheimers	-1.4	0.91652	[-3, -3, -2, -1, -1, -1, 0, -1, -1, -1]
wisents	0.4	0.91652	[0, 0, 0, 0, 1, 0, 0, 0, 3, 0]
wiser	1.2	0.87178	[1, 3, 0, 1, 1, 2, 0, 2, 1, 1]
wises	1.3	1.48661	[3, 3, 2, -2, 2, 0, 0, 2, 2, 1]
wisest	2.1	1.51327	[1, 3, 3, 3, 3, 2, 2, 3, -2, 3]
wisewomen	1.3	0.9	[2, 2, 0, 0, 2, 0, 1, 2, 2, 2]
wish	1.7	1.1	[2, 1, 1, 0, 2, 1, 3, 2, 4, 1]
wishes	0.6	0.8	[0, 0, 1, 0, 1, 0, 2, 0, 2, 0]
wishing	0.9	0.7	[2, 1, 1, 0, 0, 0, 1, 1, 2, 1]
witch	-1.5	0.80623	[-1, -2, -2, -1, -3, 0, -2, -2, -1, -1]
withdrawal	0.1	1.57797	[1, -1, 0, -2, -2, 2, -1, 1, 0, 3]
woe	-1.8	0.6	[-3, -2, -2, -2, -1, -1, -2, -1, -2, -2]
woebegone	-2.6	0.66332	[-3, -2, -3, -2, -2, -4, -3, -2, -2, -3]
woebegoneness	-1.1	1.37477	[-3, 0, -1, 1, -1, -4, 0, -1, -1, -1]
woeful	-1.9	0.83066	[-1, -2, -2, -1, -3, -3, -1, -2, -1, -3]
woefully	-1.7	1.48661	[-1, -3, -2, 1, -3, -3, -2, -2, 1, -3]
woefulness	-2.1	0.7	[-3, -2, -2, -1, -2, -3, -3, -1, -2, -2]
woes	-1.9	0.83066	[-2, -2, -2, -1, -2, -3, -3, 0, -2, -2]
woesome	-1.2	1.6	[-2, -3, -2, -1, 0, 3, -2, -2, -1, -2]
won	2.7	0.9	[3, 4, 2, 2, 2, 4, 4, 2, 2, 2]
wonderful	2.7	0.78102	[2, 3, 3, 2, 4, 2, 2, 3, 4, 2]
wonderfully	2.9	0.83066	[1, 3, 3, 4, 3, 2, 3, 3, 4, 3]
wonderfulness	2.9	0.53852	[3, 2, 3, 3, 3, 3, 3, 2, 4, 3]
woo	2.1	1.37477	[4, 2, 1, 3, 2, 2, -1, 2, 2, 4]
woohoo	2.3	1.1	[3, 3, 1, 4, 4, 2, 1, 1, 2, 2]
woot	1.8	1.07703	[2, 0, 2, 2, 2, 2, 0, 4, 2, 2]
worn	-1.2	0.4	[-1, -1, -1, -1, -1, -1, -2, -1, -2, -1]
worried	-1.2	0.74833	[-1, -1, -1, -1, -1, -2, -3, 0, -1, -1]
worriedly	-2.0	0.44721	[-2, -2, -3, -2, -2, -2, -2, -1, -2, -2]
worrier	-1.8	0.6	[-2, -2, -1, -2, -1, -3, -2, -2, -1, -2]
worriers	-1.7	0.45826	[-2, -1, -2, -2, -2, -2, -1, -2, -1, -2]
worries	-1.8	0.6	[-2, -2, -1, -2, -1, -2, -2, -3, -1, -2]
worriment	-1.5	0.67082	[-1, -2, -1, -1, -1, -2, -1, -3, -1, -2]
worriments	-1.9	0.7	[-2, -1, -2, -3, -1, -2, -3, -1, -2, -2]
worrisome	-1.7	0.64031	[-1, -1, -1, -2, -1, -2, -3, -2, -2, -2]
worrisomely	-2.0	0.63246	[-1, -2, -1, -2, -2, -3, -2, -2, -3, -2]
worrisomeness	-1.9	0.53852	[-2, -2, -3, -1, -2, -2, -2, -1, -2, -2]
worrit	-2.1	0.53852	[-2, -2, -1, -2, -2, -3, -3, -2, -2, -2]
worrits	-1.2	0.9798	[-1, -2, -2, -1, 0, 0, -1, -3, 0, -2]
worry	-1.9	0.7	[-2, -3, -1, -3, -1, -2, -1, -2, -2, -2]
worrying	-1.4	0.66332	[-2, -1, -2, -2, -1, 0, -1, -1, -2, -2]
worrywart	-1.8	0.9798	[-2, -2, -2, -1, -1, -1, -1, -3, -1, -4]
worrywarts	-1.5	0.5	[-2, -1, -2, -2, -2, -1, -1, -1, -2, -1]
worse	-2.1	0.83066	[-2, -2, -1, -3, -4, -2, -1, -2, -2, -2]
worsen	-2.3	0.78102	[-4, -3, -1, -2, -2, -2, -2, -3, -2, -2]
worsened	-1.9	1.22066	[-2, -2, -2, -1, -2, -2, -4, 1, -3, -2]
worsening	-2.0	0.44721	[-2, -3, -2, -2, -2, -2, -1, -2, -2, -2]
worsens	-2.1	0.53852	[-2, -2, -2, -2, -1, -2, -2, -3, -3, -2]
worser	-2.0	0.89443	[-2, -2, -4, -1, -2, -2, -2, -3, -1, -1]
worship	1.2	1.07703	[1, 0, 0, 1, 3, 0, 2, 3, 1, 1]
worshiped	2.4	1.0198	[1, 2, 4, 3, 4, 1, 2, 3, 2, 2]
worshiper	1.0	1.0	[0, 0, 2, 3, 0, 2, 1, 1, 1, 0]
worshipers	0.9	0.83066	[0, 0, 0, 2, 1, 1, 1, 2, 2, 0]
worshipful	0.7	1.00499	[1, -1, 3, 1, 1, 1, 0, 0, 0, 1]
worshipfully	1.1	1.3	[0, 0, 0, 1, 3, 0, 3, 3, 1, 0]
worshipfulness	1.6	0.8	[3, 1, 2, 2, 1, 1, 3, 1, 1, 1]
worshiping	1.0	1.18322	[0, 3, 0, 3, 0, 1, 1, 2, 0, 0]
worshipless	-0.6	1.0198	[0, -1, -3, -1, -1, -1, 0, 0, 0, 1]
worshipped	2.7	0.78102	[3, 2, 3, 3, 1, 4, 2, 3, 3, 3]
worshipper	0.6	0.66332	[1, 1, 0, 0, 1, 0, 0, 2, 1, 0]
worshippers	0.8	0.87178	[0, 1, 0, 0, 3, 1, 1, 1, 0, 1]
worshipping	1.6	1.28062	[1, 3, 3, 3, 0, 3, 1, 0, 2, 0]
worships	1.4	1.11355	[2, 0, 1, 3, 2, 1, 0, 3, 2, 0]
worst	-3.1	1.04403	[-4, -4, -3, -1, -3, -4, -2, -2, -4, -4]
worth	0.9	0.9434	[0, 0, 1, 1, 2, 1, 1, 3, 0, 0]
worthless	-1.9	1.13578	[-3, -1, -3, -4, -1, -3, -1, -1, -1, -1]
worthwhile	1.4	0.4899	[1, 1, 1, 2, 1, 1, 2, 1, 2, 2]
worthy	1.9	0.53852	[2, 2, 2, 1, 1, 2, 2, 2, 3, 2]
wow	2.8	0.9798	[2, 3, 2, 4, 4, 3, 3, 2, 1, 4]
wowed	2.6	0.8	[3, 3, 4, 3, 2, 1, 3, 3, 2, 2]
wowing	2.5	0.67082	[2, 2, 3, 3, 2, 3, 4, 2, 2, 2]
wows	2.0	1.61245	[2, 3, 3, 3, 2, 1, -2, 1, 4, 3]
wowser	-1.1	2.02237	[-3, 3, 0, 2, -2, -1, -3, -2, -2, -3]
wowsers	1.0	2.14476	[0, -2, 4, 2, 3, 0, 1, 2, -3, 3]
wrathful	-2.7	0.64031	[-3, -2, -2, -3, -3, -2, -4, -2, -3, -3]
wreck	-1.9	0.7	[-1, -2, -3, -3, -2, -2, -2, -1, -1, -2]
wrong	-2.1	1.04403	[-2, -2, -2, -2, -4, -4, -1, -1, -1, -2]
wronged	-1.9	0.53852	[-2, -2, -2, -2, -2, -1, -3, -2, -2, -1]
x-d	2.6	0.91652	[2, 3, 3, 4, 1, 2, 3, 4, 2, 2]
x-p	1.7	0.45826	[2, 2, 1, 2, 2, 1, 1, 2, 2, 2]
xd	2.8	0.87178	[3, 3, 4, 2, 3, 3, 1, 2, 4, 3]
xp	1.6	0.4899	[2, 2, 2, 1, 1, 1, 2, 2, 1, 2]
yay	2.4	1.0198	[1, 3, 3, 2, 2, 1, 4, 4, 2, 2]
yeah	1.2	0.6	[1, 1, 1, 2, 1, 1, 0, 2, 1, 2]
yearning	0.5	1.0247	[0, 1, 0, 1, 0, 3, 0, 1, -1, 0]
yeees	1.7	1.00499	[1, 3, 1, 2, 1, 1, 4, 2, 1, 1]
yep	1.2	0.4	[1, 1, 1, 1, 1, 1, 2, 2, 1, 1]
yes	1.7	0.78102	[1, 2, 2, 1, 1, 1, 3, 3, 1, 2]
youthful	1.3	0.45826	[1, 2, 1, 2, 1, 1, 1, 1, 2, 1]
yucky	-1.8	0.6	[-2, -1, -1, -2, -2, -1, -2, -2, -3, -2]
yummy	2.4	1.0198	[1, 2, 4, 3, 2, 2, 3, 1, 4, 2]
zealot	-1.9	1.04403	[-2, -3, -1, -2, -1, -3, -4, -1, -1, -1]
zealots	-0.8	1.83303	[-1, -2, -1, -2, -2, 1, -2, 4, -1, -2]
zealous	0.5	1.43178	[2, -1, 2, 1, 0, 0, 3, 0, -2, 0]
{:	1.8	0.9798	[1, 3, 2, 2, 1, 1, 4, 2, 1, 1]
|-0	-1.2	0.74833	[0, -2, -1, -1, -1, -1, -1, -1, -1, -3]
|-:	-0.8	0.74833	[-1, -2, 0, -1, 0, -2, -1, -1, 0, 0]
|-:>	-1.6	0.4899	[-1, -2, -2, -2, -2, -1, -1, -2, -2, -1]
|-o	-1.2	0.9798	[-1, 0, -1, -1, -1, -1, -1, -4, -1, -1]
|:	-0.5	1.68819	[2, -3, -1, 0, -1, -1, -1, -2, -1, 3]
|;-)	2.2	1.32665	[4, 1, 1, 1, 3, 2, 4, 1, 4, 1]
|=	-0.4	1.56205	[2, -2, -1, 0, -1, -1, -1, -2, -1, 3]
|^:	-1.1	0.7	[-2, 0, -1, -1, 0, -1, -1, -2, -2, -1]
|o:	-0.9	0.53852	[-1, 0, -1, -2, -1, 0, -1, -1, -1, -1]
||-:	-2.3	0.45826	[-2, -2, -2, -3, -3, -3, -2, -2, -2, -2]
}:	-2.1	0.83066	[-1, -1, -3, -2, -3, -2, -2, -1, -3, -3]
}:(	-2.0	0.63246	[-3, -1, -2, -1, -3, -2, -2, -2, -2, -2]
}:)	0.4	1.42829	[1, 1, -2, 1, 2, -2, 1, -1, 2, 1]
}:-(	-2.1	0.7	[-2, -1, -2, -2, -2, -4, -2, -2, -2, -2]
}:-)	0.3	1.61555	[1, 1, -2, 1, -1, -3, 2, 2, 1, 1]