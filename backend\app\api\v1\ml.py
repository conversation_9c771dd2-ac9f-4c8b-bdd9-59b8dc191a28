"""
机器学习相关API端点

提供模型训练、预测、推荐等API接口
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
# from app.models.ml import MLModel, MLPrediction, MLTrainingJob, MLRecommendation
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.ml_service import MLService
from app.services.jqdata_service import JQDataService

router = APIRouter()

# 初始化服务
jqdata_service = JQDataService()
ml_service = MLService(jqdata_service)


# =============================================================================
# AI预测功能
# =============================================================================

@router.post("/predict/price", response_model=BaseResponse[dict])
async def predict_stock_price(
    symbol: str,
    days_ahead: int = Query(5, ge=1, le=30, description="预测天数"),
    model_type: str = Query('random_forest', description="模型类型"),
    current_user: User = Depends(get_current_active_user)
):
    """股票价格预测"""
    try:
        result = await ml_service.predict_stock_price(
            symbol=symbol,
            days_ahead=days_ahead,
            model_type=model_type
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            success=True,
            data=result,
            message="价格预测完成"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Price prediction failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="价格预测失败"
        )


@router.post("/intelligent-selection", response_model=BaseResponse[dict])
async def intelligent_stock_selection(
    market: str = Query('all', description="市场类型"),
    top_n: int = Query(20, ge=5, le=50, description="返回股票数量"),
    criteria: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_active_user)
):
    """智能选股"""
    try:
        result = await ml_service.intelligent_stock_selection(
            market=market,
            top_n=top_n,
            criteria=criteria
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            success=True,
            data=result,
            message="智能选股完成"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Intelligent selection failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="智能选股失败"
        )


@router.post("/sentiment-analysis", response_model=BaseResponse[dict])
async def sentiment_analysis(
    symbol: str,
    news_count: int = Query(50, ge=10, le=200, description="分析新闻数量"),
    current_user: User = Depends(get_current_active_user)
):
    """情绪分析"""
    try:
        result = await ml_service.sentiment_analysis(
            symbol=symbol,
            news_count=news_count
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            success=True,
            data=result,
            message="情绪分析完成"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="情绪分析失败"
        )


# =============================================================================
# 模型管理
# =============================================================================

@router.get("/models", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_models(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    model_type: Optional[str] = Query(None, description="模型类型筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户的机器学习模型列表"""
    try:
        # 构建查询条件
        conditions = [MLModel.user_id == current_user.id]
        if model_type:
            conditions.append(MLModel.model_type == model_type)
        
        # 查询总数
        count_query = select(func.count(MLModel.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(MLModel)
            .where(and_(*conditions))
            .order_by(desc(MLModel.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        models = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "model_type": model.model_type,
                "algorithm": model.algorithm,
                "version": model.version,
                "status": model.status,
                "is_active": model.is_active,
                "validation_score": float(model.validation_score) if model.validation_score else None,
                "test_score": float(model.test_score) if model.test_score else None,
                "prediction_count": model.prediction_count,
                "created_at": model.created_at.isoformat(),
                "last_used_at": model.last_used_at.isoformat() if model.last_used_at else None,
            })
        
        return BaseResponse(
            code=200,
            message="获取模型列表成功",
            data=PaginatedResponse(
                items=model_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.post("/models/train", response_model=BaseResponse[dict])
async def train_model(
    model_config: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """训练机器学习模型"""
    try:
        # 验证配置
        required_fields = ["name", "model_type", "algorithm", "start_date", "end_date"]
        for field in required_fields:
            if field not in model_config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少必需字段: {field}"
                )
        
        # 添加后台任务训练模型
        background_tasks.add_task(
            _train_model_task,
            current_user.id,
            model_config,
            db
        )
        
        return BaseResponse(
            code=200,
            message="模型训练任务已提交",
            data={
                "task_submitted": True,
                "model_name": model_config["name"],
                "estimated_time": "5-30分钟"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交模型训练任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交模型训练任务失败"
        )


@router.get("/models/{model_id}", response_model=BaseResponse[dict])
async def get_model_detail(
    model_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取模型详情"""
    try:
        result = await db.execute(
            select(MLModel).where(
                and_(
                    MLModel.id == model_id,
                    MLModel.user_id == current_user.id
                )
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取模型详情成功",
            data={
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "model_type": model.model_type,
                "algorithm": model.algorithm,
                "version": model.version,
                "hyperparameters": model.hyperparameters,
                "feature_config": model.feature_config,
                "training_config": model.training_config,
                "status": model.status,
                "is_active": model.is_active,
                "training_samples": model.training_samples,
                "training_features": model.training_features,
                "performance_metrics": model.performance_metrics,
                "validation_score": float(model.validation_score) if model.validation_score else None,
                "test_score": float(model.test_score) if model.test_score else None,
                "prediction_count": model.prediction_count,
                "created_at": model.created_at.isoformat(),
                "updated_at": model.updated_at.isoformat(),
                "last_used_at": model.last_used_at.isoformat() if model.last_used_at else None,
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型详情失败"
        )


# =============================================================================
# 预测功能
# =============================================================================

@router.post("/predict", response_model=BaseResponse[dict])
async def make_prediction(
    prediction_request: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """进行预测"""
    try:
        model_id = prediction_request.get("model_id")
        symbol = prediction_request.get("symbol")
        prediction_type = prediction_request.get("prediction_type", "price")
        
        if not model_id or not symbol:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少模型ID或股票代码"
            )
        
        # 获取模型
        result = await db.execute(
            select(MLModel).where(
                and_(
                    MLModel.id == model_id,
                    MLModel.user_id == current_user.id,
                    MLModel.status == "trained"
                )
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在或未训练完成"
            )
        
        # 这里应该加载模型并进行预测
        # 目前返回模拟预测结果
        prediction_result = {
            "model_id": model_id,
            "symbol": symbol,
            "prediction_type": prediction_type,
            "predicted_value": 105.50 if prediction_type == "price" else None,
            "predicted_class": "up" if prediction_type == "direction" else None,
            "confidence_score": 0.75,
            "prediction_date": datetime.utcnow().isoformat(),
            "target_date": (datetime.utcnow() + timedelta(days=1)).isoformat(),
        }
        
        # 保存预测结果
        prediction = MLPrediction(
            model_id=model_id,
            user_id=current_user.id,
            prediction_type=prediction_type,
            target_symbol=symbol,
            prediction_date=datetime.utcnow(),
            target_date=datetime.utcnow() + timedelta(days=1),
            predicted_value=prediction_result.get("predicted_value"),
            predicted_class=prediction_result.get("predicted_class"),
            confidence_score=prediction_result["confidence_score"],
            model_version=model.version
        )
        
        db.add(prediction)
        
        # 更新模型使用统计
        model.prediction_count += 1
        model.last_used_at = datetime.utcnow()
        
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="预测完成",
            data=prediction_result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"预测失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="预测失败"
        )


# =============================================================================
# 推荐功能
# =============================================================================

@router.get("/recommendations/strategies", response_model=BaseResponse[List[dict]])
async def get_strategy_recommendations(
    n_recommendations: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取策略推荐"""
    try:
        recommendations = await recommendation_service.recommend_strategies(
            current_user.id, db, n_recommendations
        )
        
        # 保存推荐结果
        for rec in recommendations:
            recommendation = MLRecommendation(
                user_id=current_user.id,
                model_id=1,  # 推荐系统模型ID
                recommendation_type="strategy",
                target_id=str(rec["strategy_id"]),
                target_name=rec["strategy_name"],
                score=rec["score"],
                confidence=rec["confidence"],
                reasons=rec["reasons"],
                expires_at=datetime.utcnow() + timedelta(days=7)
            )
            db.add(recommendation)
        
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="获取策略推荐成功",
            data=recommendations
        )
        
    except Exception as e:
        logger.error(f"获取策略推荐失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取策略推荐失败"
        )


@router.get("/recommendations/stocks", response_model=BaseResponse[List[dict]])
async def get_stock_recommendations(
    n_recommendations: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取股票推荐"""
    try:
        recommendations = await recommendation_service.recommend_stocks(
            current_user.id, db, n_recommendations
        )
        
        return BaseResponse(
            code=200,
            message="获取股票推荐成功",
            data=recommendations
        )
        
    except Exception as e:
        logger.error(f"获取股票推荐失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票推荐失败"
        )


@router.post("/recommendations/{recommendation_id}/feedback", response_model=BaseResponse[dict])
async def submit_recommendation_feedback(
    recommendation_id: int,
    feedback_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """提交推荐反馈"""
    try:
        result = await db.execute(
            select(MLRecommendation).where(
                and_(
                    MLRecommendation.id == recommendation_id,
                    MLRecommendation.user_id == current_user.id
                )
            )
        )
        recommendation = result.scalar_one_or_none()
        
        if not recommendation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="推荐记录不存在"
            )
        
        # 更新反馈信息
        recommendation.user_rating = feedback_data.get("rating")
        recommendation.user_feedback = feedback_data.get("feedback")
        recommendation.is_clicked = feedback_data.get("is_clicked", False)
        recommendation.is_adopted = feedback_data.get("is_adopted", False)
        
        if feedback_data.get("is_clicked"):
            recommendation.click_time = datetime.utcnow()
        
        if feedback_data.get("is_adopted"):
            recommendation.adoption_time = datetime.utcnow()
        
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="反馈提交成功",
            data={"feedback_submitted": True}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交推荐反馈失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交推荐反馈失败"
        )


# =============================================================================
# 特征工程
# =============================================================================

@router.post("/features/extract", response_model=BaseResponse[dict])
async def extract_features(
    extraction_request: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """提取特征"""
    try:
        symbol = extraction_request.get("symbol")
        start_date = extraction_request.get("start_date")
        end_date = extraction_request.get("end_date")
        
        if not all([symbol, start_date, end_date]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少必需参数"
            )
        
        # 添加后台任务提取特征
        background_tasks.add_task(
            _extract_features_task,
            symbol,
            datetime.fromisoformat(start_date),
            datetime.fromisoformat(end_date),
            db
        )
        
        return BaseResponse(
            code=200,
            message="特征提取任务已提交",
            data={
                "task_submitted": True,
                "symbol": symbol,
                "estimated_time": "1-5分钟"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交特征提取任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交特征提取任务失败"
        )


# =============================================================================
# 私有函数
# =============================================================================

async def _train_model_task(user_id: int, model_config: Dict[str, Any], db: AsyncSession):
    """模型训练后台任务"""
    try:
        model = await ml_model_service.train_model(user_id, model_config, db)
        logger.info(f"用户 {user_id} 的模型训练完成: {model.id}")
        
    except Exception as e:
        logger.error(f"模型训练任务失败: {e}")


async def _extract_features_task(
    symbol: str, 
    start_date: datetime, 
    end_date: datetime, 
    db: AsyncSession
):
    """特征提取后台任务"""
    try:
        features = await feature_engineering_service.extract_all_features(
            symbol, start_date, end_date, db
        )
        
        if features:
            await feature_engineering_service.save_features_to_db(features, symbol, db)
            logger.info(f"特征提取完成: {symbol}")
        
    except Exception as e:
        logger.error(f"特征提取任务失败: {e}")
