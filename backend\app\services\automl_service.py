"""
AutoML核心服务

整合特征选择、超参数优化、模型选择等功能，提供完整的AutoML流程
"""

import numpy as np
import pandas as pd
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.logging import logger
from app.models.automl import AutoMLExperiment, AutoMLModel, AutoMLPrediction
from app.services.feature_selection_service import feature_selection_service
from app.services.model_selection_service import model_selection_service
from app.services.jqdata_service import jqdata_service


class DatasetLoader:
    """数据集加载器"""
    
    def __init__(self):
        pass
    
    async def load_dataset(
        self,
        dataset_config: Dict[str, Any],
        db: AsyncSession
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """加载数据集"""
        try:
            dataset_source = dataset_config.get('source', 'jqdata')
            
            if dataset_source == 'jqdata':
                return await self._load_jqdata_dataset(dataset_config, db)
            elif dataset_source == 'upload':
                return await self._load_uploaded_dataset(dataset_config)
            elif dataset_source == 'database':
                return await self._load_database_dataset(dataset_config, db)
            else:
                raise ValueError(f"Unsupported dataset source: {dataset_source}")
                
        except Exception as e:
            logger.error(f"数据集加载失败: {e}")
            raise
    
    async def _load_jqdata_dataset(
        self,
        config: Dict[str, Any],
        db: AsyncSession
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """加载JQData数据集"""
        try:
            symbols = config.get('symbols', ['000001.XSHE'])
            start_date = config.get('start_date', '2020-01-01')
            end_date = config.get('end_date', '2023-12-31')
            fields = config.get('fields', ['close', 'volume', 'high', 'low', 'open'])
            
            # 获取价格数据
            price_data = await jqdata_service.get_price_data(
                symbols, start_date, end_date, fields
            )
            
            if price_data.empty:
                raise ValueError("No price data available")
            
            # 计算技术指标
            technical_data = await jqdata_service.get_technical_indicators(
                symbols[0], start_date, end_date
            )
            
            # 合并数据
            if not technical_data.empty:
                data = pd.concat([price_data, technical_data], axis=1)
            else:
                data = price_data
            
            # 创建目标变量（例如：下一日收益率）
            target_column = config.get('target_column', 'next_return')
            if target_column == 'next_return':
                data['next_return'] = data['close'].pct_change().shift(-1)
                data = data.dropna()
            
            # 分离特征和目标
            target = data[target_column]
            features = data.drop(columns=[target_column])
            
            return features, target
            
        except Exception as e:
            logger.error(f"JQData数据集加载失败: {e}")
            raise
    
    async def _load_uploaded_dataset(
        self,
        config: Dict[str, Any]
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """加载上传的数据集"""
        try:
            file_path = config.get('file_path')
            target_column = config.get('target_column')
            
            if not file_path or not target_column:
                raise ValueError("Missing file_path or target_column")
            
            # 读取文件
            if file_path.endswith('.csv'):
                data = pd.read_csv(file_path)
            elif file_path.endswith('.xlsx'):
                data = pd.read_excel(file_path)
            else:
                raise ValueError("Unsupported file format")
            
            # 分离特征和目标
            target = data[target_column]
            features = data.drop(columns=[target_column])
            
            return features, target
            
        except Exception as e:
            logger.error(f"上传数据集加载失败: {e}")
            raise
    
    async def _load_database_dataset(
        self,
        config: Dict[str, Any],
        db: AsyncSession
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """加载数据库数据集"""
        try:
            # 这里可以实现从数据库加载数据的逻辑
            # 例如从历史交易数据、用户数据等表中加载
            raise NotImplementedError("Database dataset loading not implemented yet")
            
        except Exception as e:
            logger.error(f"数据库数据集加载失败: {e}")
            raise


class ExperimentManager:
    """实验管理器"""
    
    def __init__(self):
        pass
    
    async def create_experiment(
        self,
        user_id: int,
        experiment_config: Dict[str, Any],
        db: AsyncSession
    ) -> AutoMLExperiment:
        """创建AutoML实验"""
        try:
            experiment = AutoMLExperiment(
                user_id=user_id,
                name=experiment_config['name'],
                description=experiment_config.get('description', ''),
                experiment_type=experiment_config['experiment_type'],
                dataset_source=experiment_config['dataset_source'],
                dataset_config=experiment_config['dataset_config'],
                target_column=experiment_config['target_column'],
                feature_columns=experiment_config.get('feature_columns', []),
                problem_type=experiment_config['problem_type'],
                evaluation_metric=experiment_config.get('evaluation_metric', 'accuracy'),
                cross_validation_folds=experiment_config.get('cross_validation_folds', 5),
                test_size=experiment_config.get('test_size', 0.2),
                random_state=experiment_config.get('random_state', 42),
                max_runtime_minutes=experiment_config.get('max_runtime_minutes', 60),
                max_models=experiment_config.get('max_models', 50),
                early_stopping_rounds=experiment_config.get('early_stopping_rounds', 10),
                enable_feature_selection=experiment_config.get('enable_feature_selection', True),
                enable_hyperparameter_tuning=experiment_config.get('enable_hyperparameter_tuning', True),
                enable_ensemble=experiment_config.get('enable_ensemble', True),
                enable_stacking=experiment_config.get('enable_stacking', False),
                included_algorithms=experiment_config.get('included_algorithms', []),
                excluded_algorithms=experiment_config.get('excluded_algorithms', []),
                enable_feature_engineering=experiment_config.get('enable_feature_engineering', True),
                max_feature_interactions=experiment_config.get('max_feature_interactions', 2),
                enable_polynomial_features=experiment_config.get('enable_polynomial_features', False),
                enable_text_features=experiment_config.get('enable_text_features', False),
                status="created"
            )
            
            db.add(experiment)
            await db.commit()
            await db.refresh(experiment)
            
            return experiment
            
        except Exception as e:
            logger.error(f"创建实验失败: {e}")
            raise
    
    async def update_experiment_status(
        self,
        experiment_id: int,
        status: str,
        progress: int = None,
        current_stage: str = None,
        db: AsyncSession = None
    ):
        """更新实验状态"""
        try:
            from sqlalchemy import update
            
            update_data = {'status': status, 'updated_at': datetime.utcnow()}
            
            if progress is not None:
                update_data['progress'] = progress
            
            if current_stage is not None:
                update_data['current_stage'] = current_stage
            
            if status == 'running' and not hasattr(self, '_start_time_set'):
                update_data['started_at'] = datetime.utcnow()
                self._start_time_set = True
            elif status in ['completed', 'failed', 'cancelled']:
                update_data['completed_at'] = datetime.utcnow()
            
            await db.execute(
                update(AutoMLExperiment)
                .where(AutoMLExperiment.id == experiment_id)
                .values(**update_data)
            )
            await db.commit()
            
        except Exception as e:
            logger.error(f"更新实验状态失败: {e}")


class AutoMLService:
    """AutoML核心服务"""
    
    def __init__(self):
        self.dataset_loader = DatasetLoader()
        self.experiment_manager = ExperimentManager()
    
    async def run_automl_experiment(
        self,
        user_id: int,
        experiment_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """运行AutoML实验"""
        try:
            # 创建实验
            experiment = await self.experiment_manager.create_experiment(
                user_id, experiment_config, db
            )
            
            # 异步运行实验
            asyncio.create_task(
                self._run_experiment_async(experiment.id, experiment_config)
            )
            
            return {
                'success': True,
                'experiment_id': experiment.id,
                'message': 'AutoML实验已启动'
            }
            
        except Exception as e:
            logger.error(f"启动AutoML实验失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _run_experiment_async(
        self,
        experiment_id: int,
        experiment_config: Dict[str, Any]
    ):
        """异步运行实验"""
        from app.core.database import get_db

        async with get_db() as db:
            try:
                # 更新状态为运行中
                await self.experiment_manager.update_experiment_status(
                    experiment_id, 'running', 0, 'loading_data', db
                )

                # 1. 加载数据集
                logger.info(f"实验 {experiment_id}: 加载数据集")
                X, y = await self.dataset_loader.load_dataset(
                    experiment_config['dataset_config'], db
                )
            
            await self.experiment_manager.update_experiment_status(
                experiment_id, 'running', 20, 'feature_selection', db
            )
            
            # 2. 特征选择（如果启用）
            if experiment_config.get('enable_feature_selection', True):
                logger.info(f"实验 {experiment_id}: 特征选择")
                
                selection_config = {
                    'enable_univariate': True,
                    'enable_model_based': True,
                    'enable_wrapper': False,  # 包装器方法较慢，默认关闭
                    'enable_feature_engineering': experiment_config.get('enable_feature_engineering', True),
                    'max_final_features': min(50, X.shape[1]),
                    'min_final_features': min(5, X.shape[1] // 4)
                }
                
                feature_result = await feature_selection_service.perform_feature_selection(
                    experiment_id, X, y, experiment_config['problem_type'], selection_config, db
                )
                
                if feature_result['success']:
                    selected_features = feature_result['selected_features']
                    X = X[selected_features]
                    logger.info(f"实验 {experiment_id}: 选择了 {len(selected_features)} 个特征")
            
            await self.experiment_manager.update_experiment_status(
                experiment_id, 'running', 40, 'model_selection', db
            )
            
            # 3. 模型选择和训练
            logger.info(f"实验 {experiment_id}: 模型选择和训练")
            
            selection_config = {
                'test_size': experiment_config.get('test_size', 0.2),
                'random_state': experiment_config.get('random_state', 42),
                'cv': experiment_config.get('cross_validation_folds', 5),
                'enable_hyperparameter_tuning': experiment_config.get('enable_hyperparameter_tuning', True),
                'included_algorithms': experiment_config.get('included_algorithms', []),
                'excluded_algorithms': experiment_config.get('excluded_algorithms', []),
                'max_training_time': 'medium'
            }
            
            model_result = await model_selection_service.select_best_models(
                experiment_id, X, y, experiment_config['problem_type'], selection_config, db
            )
            
            await self.experiment_manager.update_experiment_status(
                experiment_id, 'running', 80, 'ensemble', db
            )
            
            # 4. 模型集成（如果启用）
            if experiment_config.get('enable_ensemble', True) and model_result['success']:
                logger.info(f"实验 {experiment_id}: 模型集成")
                await self._create_ensemble_models(experiment_id, model_result['all_results'], db)
            
            await self.experiment_manager.update_experiment_status(
                experiment_id, 'running', 95, 'finalizing', db
            )
            
            # 5. 完成实验
            await self._finalize_experiment(experiment_id, model_result, db)
            
            await self.experiment_manager.update_experiment_status(
                experiment_id, 'completed', 100, 'completed', db
            )

            logger.info(f"实验 {experiment_id}: 完成")

        except Exception as e:
                logger.error(f"实验 {experiment_id} 运行失败: {e}")
                await self.experiment_manager.update_experiment_status(
                    experiment_id, 'failed', None, f'error: {str(e)}', db
                )
    
    async def _create_ensemble_models(
        self,
        experiment_id: int,
        model_results: List[Dict[str, Any]],
        db: AsyncSession
    ):
        """创建集成模型"""
        try:
            # 这里可以实现模型集成逻辑
            # 例如投票、平均、堆叠等方法
            logger.info(f"实验 {experiment_id}: 创建集成模型（暂未实现）")
            
        except Exception as e:
            logger.error(f"创建集成模型失败: {e}")
    
    async def _finalize_experiment(
        self,
        experiment_id: int,
        model_result: Dict[str, Any],
        db: AsyncSession
    ):
        """完成实验"""
        try:
            from sqlalchemy import update
            
            # 更新实验统计信息
            update_data = {
                'total_models_trained': model_result.get('total_models', 0),
                'updated_at': datetime.utcnow()
            }
            
            if model_result.get('best_model'):
                update_data['best_score'] = model_result['best_model']['score']
                update_data['best_model_id'] = model_result['best_model']['model_id']
            
            await db.execute(
                update(AutoMLExperiment)
                .where(AutoMLExperiment.id == experiment_id)
                .values(**update_data)
            )
            await db.commit()
            
        except Exception as e:
            logger.error(f"完成实验失败: {e}")
    
    async def get_experiment_status(
        self,
        experiment_id: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取实验状态"""
        try:
            result = await db.execute(
                select(AutoMLExperiment).where(AutoMLExperiment.id == experiment_id)
            )
            experiment = result.scalar_one_or_none()
            
            if not experiment:
                return {'success': False, 'error': 'Experiment not found'}
            
            # 获取模型统计
            model_count_result = await db.execute(
                select(func.count(AutoMLModel.id))
                .where(AutoMLModel.experiment_id == experiment_id)
            )
            model_count = model_count_result.scalar()
            
            return {
                'success': True,
                'experiment_id': experiment.id,
                'name': experiment.name,
                'status': experiment.status,
                'progress': experiment.progress,
                'current_stage': experiment.current_stage,
                'total_models_trained': model_count,
                'best_score': float(experiment.best_score) if experiment.best_score else None,
                'created_at': experiment.created_at.isoformat(),
                'started_at': experiment.started_at.isoformat() if experiment.started_at else None,
                'completed_at': experiment.completed_at.isoformat() if experiment.completed_at else None,
                'runtime_minutes': self._calculate_runtime(experiment)
            }
            
        except Exception as e:
            logger.error(f"获取实验状态失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_experiment_results(
        self,
        experiment_id: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取实验结果"""
        try:
            # 获取实验信息
            experiment_result = await db.execute(
                select(AutoMLExperiment).where(AutoMLExperiment.id == experiment_id)
            )
            experiment = experiment_result.scalar_one_or_none()
            
            if not experiment:
                return {'success': False, 'error': 'Experiment not found'}
            
            # 获取所有模型
            models_result = await db.execute(
                select(AutoMLModel)
                .where(AutoMLModel.experiment_id == experiment_id)
                .order_by(desc(AutoMLModel.validation_score))
            )
            models = models_result.scalars().all()
            
            # 构建结果
            model_results = []
            for model in models:
                model_results.append({
                    'id': model.id,
                    'algorithm': model.algorithm,
                    'validation_score': float(model.validation_score) if model.validation_score else 0,
                    'cross_validation_score': float(model.cross_validation_score) if model.cross_validation_score else 0,
                    'training_time': float(model.training_time_seconds) if model.training_time_seconds else 0,
                    'is_best_model': model.is_best_model,
                    'hyperparameters': model.hyperparameters,
                    'performance_metrics': model.performance_metrics,
                    'feature_importance': model.feature_importance
                })
            
            return {
                'success': True,
                'experiment': {
                    'id': experiment.id,
                    'name': experiment.name,
                    'status': experiment.status,
                    'problem_type': experiment.problem_type,
                    'total_models': len(model_results),
                    'best_score': float(experiment.best_score) if experiment.best_score else None
                },
                'models': model_results
            }
            
        except Exception as e:
            logger.error(f"获取实验结果失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _calculate_runtime(self, experiment: AutoMLExperiment) -> Optional[float]:
        """计算运行时间"""
        try:
            if experiment.started_at:
                end_time = experiment.completed_at or datetime.utcnow()
                runtime = (end_time - experiment.started_at).total_seconds() / 60
                return round(runtime, 2)
            return None
        except:
            return None


# 全局AutoML服务实例
automl_service = AutoMLService()
