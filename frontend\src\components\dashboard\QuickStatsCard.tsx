'use client';

/**
 * 快速统计卡片组件
 * 
 * 显示关键指标的统计卡片，支持进度条、趋势指示等
 */

import React from 'react';
import { Card, Statistic, Progress, Typography, Space } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Text } = Typography;

interface QuickStatsCardProps {
  title: string;
  value: number;
  total?: number;
  prefix?: string;
  suffix?: string;
  precision?: number;
  icon?: React.ReactNode;
  color?: string;
  trend?: number; // 趋势值，正数表示上升，负数表示下降
  showProgress?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

export const QuickStatsCard: React.FC<QuickStatsCardProps> = ({
  title,
  value,
  total,
  prefix,
  suffix,
  precision = 0,
  icon,
  color = '#1890ff',
  trend,
  showProgress = false,
  loading = false,
  onClick,
}) => {
  // 计算进度百分比
  const progressPercent = total ? Math.min((value / total) * 100, 100) : 0;
  
  // 趋势颜色
  const getTrendColor = (trendValue?: number) => {
    if (trendValue === undefined) return undefined;
    return trendValue >= 0 ? '#52c41a' : '#ff4d4f';
  };

  // 趋势图标
  const getTrendIcon = (trendValue?: number) => {
    if (trendValue === undefined) return null;
    return trendValue >= 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
  };

  return (
    <motion.div
      whileHover={{ scale: onClick ? 1.02 : 1 }}
      whileTap={{ scale: onClick ? 0.98 : 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <Card
        hoverable={!!onClick}
        onClick={onClick}
        className="h-full shadow-sm hover:shadow-md transition-shadow duration-300"
        bodyStyle={{ padding: '20px' }}
        loading={loading}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* 标题 */}
            <Text type="secondary" className="text-sm font-medium">
              {title}
            </Text>
            
            {/* 主要数值 */}
            <div className="mt-2">
              <Statistic
                value={value}
                prefix={prefix}
                suffix={suffix}
                precision={precision}
                valueStyle={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: color,
                  lineHeight: 1.2,
                }}
              />
            </div>
            
            {/* 趋势指示 */}
            {trend !== undefined && (
              <div className="mt-2">
                <Space size={4}>
                  <span
                    style={{ color: getTrendColor(trend) }}
                    className="text-sm font-medium"
                  >
                    {getTrendIcon(trend)}
                    {Math.abs(trend).toFixed(2)}%
                  </span>
                  <Text type="secondary" className="text-xs">
                    较昨日
                  </Text>
                </Space>
              </div>
            )}
            
            {/* 进度条 */}
            {showProgress && total && (
              <div className="mt-3">
                <Progress
                  percent={progressPercent}
                  strokeColor={color}
                  trailColor="#f0f0f0"
                  strokeWidth={6}
                  showInfo={false}
                  className="mb-1"
                />
                <div className="flex justify-between items-center">
                  <Text type="secondary" className="text-xs">
                    已使用
                  </Text>
                  <Text type="secondary" className="text-xs">
                    {progressPercent.toFixed(1)}%
                  </Text>
                </div>
              </div>
            )}
          </div>
          
          {/* 图标 */}
          {icon && (
            <div
              className="flex items-center justify-center w-12 h-12 rounded-lg"
              style={{
                backgroundColor: `${color}15`,
                color: color,
              }}
            >
              <span className="text-xl">{icon}</span>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
};
