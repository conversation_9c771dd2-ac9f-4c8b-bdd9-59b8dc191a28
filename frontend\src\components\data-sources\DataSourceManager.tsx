'use client';

/**
 * 数据源管理组件
 * 
 * 管理和监控多个数据源的状态和质量
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Progress,
  Statistic,
  Row,
  Col,
  Alert,
  Modal,
  Form,
  Select,
  Switch,
  Typography,
  Tooltip,
  message,
  Badge
} from 'antd';
import {
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  BarChartOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { apiClient } from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface DataSource {
  name: string;
  display_name: string;
  description: string;
  features: string[];
  coverage: string;
  update_frequency: string;
  reliability: string;
  is_primary: boolean;
}

interface DataQualityReport {
  overall_quality: {
    average_quality_score: number;
    average_success_rate: number;
    tested_symbols: number;
  };
  source_status: Record<string, {
    available: boolean;
    last_check: string;
  }>;
  individual_results: Array<{
    symbol: string;
    quality_score: number;
    success_rate: number;
  }>;
}

export const DataSourceManager: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [qualityReport, setQualityReport] = useState<DataQualityReport | null>(null);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [form] = Form.useForm();

  useEffect(() => {
    loadDataSources();
    loadQualityReport();
  }, []);

  const loadDataSources = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get('/api/v1/multi-data-source/sources');
      if (response.data.success) {
        setDataSources(response.data.data.available_sources);
      }
    } catch (error: any) {
      console.error('Load data sources error:', error);
      message.error('加载数据源失败');
    } finally {
      setLoading(false);
    }
  };

  const loadQualityReport = async () => {
    try {
      const response = await apiClient.get('/api/v1/multi-data-source/quality/report');
      if (response.data.success) {
        setQualityReport(response.data.data);
      }
    } catch (error: any) {
      console.error('Load quality report error:', error);
      message.error('加载质量报告失败');
    }
  };

  const getReliabilityColor = (reliability: string) => {
    switch (reliability) {
      case 'high': return 'green';
      case 'medium': return 'orange';
      case 'low': return 'red';
      default: return 'default';
    }
  };

  const getReliabilityIcon = (reliability: string) => {
    switch (reliability) {
      case 'high': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'medium': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'low': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default: return <InfoCircleOutlined />;
    }
  };

  const columns = [
    {
      title: '数据源',
      key: 'source',
      render: (record: DataSource) => (
        <div className="flex items-center space-x-3">
          <DatabaseOutlined className="text-blue-500" />
          <div>
            <div className="flex items-center space-x-2">
              <Text strong>{record.display_name}</Text>
              {record.is_primary && <Tag color="blue">主数据源</Tag>}
            </div>
            <Text type="secondary" className="text-sm">{record.description}</Text>
          </div>
        </div>
      )
    },
    {
      title: '覆盖范围',
      dataIndex: 'coverage',
      key: 'coverage',
      width: 150
    },
    {
      title: '更新频率',
      dataIndex: 'update_frequency',
      key: 'update_frequency',
      width: 100
    },
    {
      title: '可靠性',
      dataIndex: 'reliability',
      key: 'reliability',
      width: 100,
      render: (reliability: string) => (
        <Space>
          {getReliabilityIcon(reliability)}
          <Tag color={getReliabilityColor(reliability)}>
            {reliability === 'high' ? '高' : reliability === 'medium' ? '中' : '低'}
          </Tag>
        </Space>
      )
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (record: DataSource) => {
        const status = qualityReport?.source_status[record.name];
        return (
          <Badge
            status={status?.available ? 'success' : 'error'}
            text={status?.available ? '正常' : '异常'}
          />
        );
      }
    },
    {
      title: '功能特性',
      dataIndex: 'features',
      key: 'features',
      render: (features: string[]) => (
        <div className="space-x-1">
          {features.slice(0, 3).map((feature, index) => (
            <Tag key={index} size="small">{feature}</Tag>
          ))}
          {features.length > 3 && (
            <Tooltip title={features.slice(3).join(', ')}>
              <Tag size="small">+{features.length - 3}</Tag>
            </Tooltip>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (record: DataSource) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => {
              setSelectedSource(record.name);
              setConfigModalVisible(true);
            }}
          >
            配置
          </Button>
          <Button
            type="text"
            size="small"
            icon={<BarChartOutlined />}
            onClick={() => testDataSource(record.name)}
          >
            测试
          </Button>
        </Space>
      )
    }
  ];

  const testDataSource = async (sourceName: string) => {
    try {
      message.loading('正在测试数据源...', 0);
      
      // 测试特定数据源
      const response = await apiClient.get('/api/v1/multi-data-source/stock/000001.XSHE/price', {
        params: { sources: sourceName }
      });
      
      message.destroy();
      
      if (response.data.success) {
        const quality = response.data.data.data_quality;
        message.success(`数据源测试成功！质量评分: ${(quality.quality_score * 100).toFixed(1)}%`);
      } else {
        message.error('数据源测试失败');
      }
    } catch (error: any) {
      message.destroy();
      console.error('Test data source error:', error);
      message.error('数据源测试失败');
    }
  };

  const handleConfigSave = async (values: any) => {
    try {
      // 这里应该调用配置保存API
      message.success('配置保存成功');
      setConfigModalVisible(false);
      form.resetFields();
    } catch (error: any) {
      console.error('Save config error:', error);
      message.error('配置保存失败');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                <DatabaseOutlined />
                数据源管理
              </Space>
            </Title>
            <Text type="secondary" className="text-lg">
              管理和监控多个数据源的状态和质量
            </Text>
          </div>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadQualityReport}>
              刷新状态
            </Button>
            <Button type="primary" icon={<SettingOutlined />}>
              全局配置
            </Button>
          </Space>
        </div>
      </motion.div>

      {/* 数据质量概览 */}
      {qualityReport && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card title="数据质量概览">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Statistic
                  title="平均质量评分"
                  value={qualityReport.overall_quality.average_quality_score * 100}
                  suffix="%"
                  precision={1}
                  valueStyle={{ 
                    color: qualityReport.overall_quality.average_quality_score >= 0.8 ? '#52c41a' : 
                           qualityReport.overall_quality.average_quality_score >= 0.6 ? '#faad14' : '#ff4d4f'
                  }}
                />
                <Progress
                  percent={qualityReport.overall_quality.average_quality_score * 100}
                  showInfo={false}
                  strokeColor={
                    qualityReport.overall_quality.average_quality_score >= 0.8 ? '#52c41a' : 
                    qualityReport.overall_quality.average_quality_score >= 0.6 ? '#faad14' : '#ff4d4f'
                  }
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="平均成功率"
                  value={qualityReport.overall_quality.average_success_rate * 100}
                  suffix="%"
                  precision={1}
                  valueStyle={{ color: '#1890ff' }}
                />
                <Progress
                  percent={qualityReport.overall_quality.average_success_rate * 100}
                  showInfo={false}
                  strokeColor="#1890ff"
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="测试股票数量"
                  value={qualityReport.overall_quality.tested_symbols}
                  suffix="只"
                  valueStyle={{ color: '#faad14' }}
                />
                <div className="mt-2">
                  <Text type="secondary">最近测试时间: 刚刚</Text>
                </div>
              </Col>
            </Row>
          </Card>
        </motion.div>
      )}

      {/* 数据源状态提醒 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Alert
          message="数据源状态良好"
          description="所有数据源运行正常，数据质量符合预期。建议定期检查数据源状态以确保数据准确性。"
          type="success"
          showIcon
          closable
        />
      </motion.div>

      {/* 数据源列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card title="数据源列表">
          <Table
            columns={columns}
            dataSource={dataSources}
            rowKey="name"
            loading={loading}
            pagination={false}
            size="middle"
          />
        </Card>
      </motion.div>

      {/* 配置模态框 */}
      <Modal
        title={`配置数据源: ${selectedSource}`}
        open={configModalVisible}
        onCancel={() => {
          setConfigModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfigSave}
        >
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="选择数据源优先级">
              <Option value="primary">主数据源</Option>
              <Option value="secondary">备用数据源</Option>
              <Option value="disabled">禁用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="timeout"
            label="超时时间(秒)"
            rules={[{ required: true, message: '请输入超时时间' }]}
          >
            <Select placeholder="选择超时时间">
              <Option value={5}>5秒</Option>
              <Option value={10}>10秒</Option>
              <Option value={30}>30秒</Option>
              <Option value={60}>60秒</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="retry_count"
            label="重试次数"
            rules={[{ required: true, message: '请选择重试次数' }]}
          >
            <Select placeholder="选择重试次数">
              <Option value={0}>不重试</Option>
              <Option value={1}>1次</Option>
              <Option value={2}>2次</Option>
              <Option value={3}>3次</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="cache_enabled"
            label="启用缓存"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="quality_threshold"
            label="质量阈值"
            rules={[{ required: true, message: '请选择质量阈值' }]}
          >
            <Select placeholder="选择质量阈值">
              <Option value={0.6}>60%</Option>
              <Option value={0.7}>70%</Option>
              <Option value={0.8}>80%</Option>
              <Option value={0.9}>90%</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
