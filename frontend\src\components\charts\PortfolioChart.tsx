'use client';

/**
 * 投资组合收益曲线图表组件
 * 
 * 显示投资组合的收益表现曲线
 */

import React, { useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import * as echarts from 'echarts';

interface PortfolioChartProps {
  timeRange: string;
  height?: number;
}

interface ChartData {
  dates: string[];
  portfolioValue: number[];
  benchmarkValue: number[];
  cashFlow: number[];
}

export const PortfolioChart: React.FC<PortfolioChartProps> = ({
  timeRange,
  height = 400,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);

  // 生成模拟数据
  const generateMockData = (): ChartData => {
    const now = new Date();
    const days = timeRange === '1D' ? 1 : timeRange === '1W' ? 7 : timeRange === '1M' ? 30 : timeRange === '3M' ? 90 : 365;
    
    const dates: string[] = [];
    const portfolioValue: number[] = [];
    const benchmarkValue: number[] = [];
    const cashFlow: number[] = [];
    
    let basePortfolioValue = 100000;
    let baseBenchmarkValue = 100000;
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      dates.push(date.toISOString().split('T')[0]);
      
      // 模拟投资组合波动
      const portfolioChange = (Math.random() - 0.5) * 0.02; // ±1%
      basePortfolioValue *= (1 + portfolioChange);
      portfolioValue.push(Number(basePortfolioValue.toFixed(2)));
      
      // 模拟基准指数波动（相对稳定）
      const benchmarkChange = (Math.random() - 0.5) * 0.015; // ±0.75%
      baseBenchmarkValue *= (1 + benchmarkChange);
      benchmarkValue.push(Number(baseBenchmarkValue.toFixed(2)));
      
      // 模拟现金流
      cashFlow.push(Math.random() > 0.9 ? (Math.random() - 0.5) * 10000 : 0);
    }
    
    return { dates, portfolioValue, benchmarkValue, cashFlow };
  };

  // 初始化图表
  const initChart = () => {
    if (!chartRef.current) return;

    // 销毁现有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);
    
    const data = generateMockData();
    
    // 计算收益率
    const portfolioReturns = data.portfolioValue.map((value, index) => {
      if (index === 0) return 0;
      return ((value - data.portfolioValue[0]) / data.portfolioValue[0]) * 100;
    });
    
    const benchmarkReturns = data.benchmarkValue.map((value, index) => {
      if (index === 0) return 0;
      return ((value - data.benchmarkValue[0]) / data.benchmarkValue[0]) * 100;
    });

    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E6E8EB',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const date = data.dates[dataIndex];
          const portfolioValue = data.portfolioValue[dataIndex];
          const benchmarkValue = data.benchmarkValue[dataIndex];
          const portfolioReturn = portfolioReturns[dataIndex];
          const benchmarkReturn = benchmarkReturns[dataIndex];
          
          return `
            <div style="padding: 8px;">
              <div style="margin-bottom: 8px; font-weight: bold;">${date}</div>
              <div style="margin-bottom: 4px;">
                <span style="color: #1890ff;">●</span> 
                投资组合: ¥${portfolioValue.toLocaleString()} (${portfolioReturn >= 0 ? '+' : ''}${portfolioReturn.toFixed(2)}%)
              </div>
              <div style="margin-bottom: 4px;">
                <span style="color: #52c41a;">●</span> 
                基准指数: ¥${benchmarkValue.toLocaleString()} (${benchmarkReturn >= 0 ? '+' : ''}${benchmarkReturn.toFixed(2)}%)
              </div>
              <div>
                超额收益: ${(portfolioReturn - benchmarkReturn).toFixed(2)}%
              </div>
            </div>
          `;
        },
      },
      legend: {
        data: ['投资组合', '基准指数'],
        top: 10,
        textStyle: {
          color: '#8392A5',
          fontSize: 12,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '15%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.dates,
        axisLine: { lineStyle: { color: '#8392A5' } },
        axisLabel: { 
          color: '#8392A5',
          fontSize: 12,
          formatter: (value: string) => {
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          },
        },
        axisTick: { show: false },
        splitLine: { show: false },
      },
      yAxis: [
        {
          type: 'value',
          name: '组合价值',
          position: 'left',
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => `¥${(value / 1000).toFixed(0)}K`,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '收益率',
          position: 'right',
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => `${value.toFixed(1)}%`,
          },
          splitLine: { show: false },
        },
      ],
      series: [
        {
          name: '投资组合',
          type: 'line',
          yAxisIndex: 0,
          data: data.portfolioValue,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3,
          },
          itemStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
            ]),
          },
          symbol: 'none',
          emphasis: {
            focus: 'series',
          },
        },
        {
          name: '基准指数',
          type: 'line',
          yAxisIndex: 0,
          data: data.benchmarkValue,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2,
            type: 'dashed',
          },
          itemStyle: {
            color: '#52c41a',
          },
          symbol: 'none',
          emphasis: {
            focus: 'series',
          },
        },
        {
          name: '收益率',
          type: 'line',
          yAxisIndex: 1,
          data: portfolioReturns,
          smooth: true,
          lineStyle: {
            color: '#faad14',
            width: 1,
            opacity: 0.6,
          },
          itemStyle: {
            color: '#faad14',
          },
          symbol: 'none',
          show: false, // 默认隐藏
        },
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };

    chartInstance.current.setOption(option);
    setLoading(false);
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  };

  useEffect(() => {
    initChart();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [timeRange]);

  return (
    <div className="relative">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <Spin size="large" />
        </div>
      )}
      <div
        ref={chartRef}
        style={{ height: `${height}px`, width: '100%' }}
        className="transition-opacity duration-300"
      />
    </div>
  );
};
