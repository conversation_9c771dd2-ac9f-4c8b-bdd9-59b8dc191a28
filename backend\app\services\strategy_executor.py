"""
策略自动执行引擎

负责策略的自动化执行、信号生成、风险控制等功能
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger
from app.models.strategy import Strategy, StrategyExecution
from app.models.portfolio import Portfolio, Position, Transaction
from app.models.user import User
from app.services.jqdata_service import JQDataService
from app.services.risk_service import RiskService
from app.websocket.manager import ConnectionManager


class StrategyExecutor:
    """策略执行器"""
    
    def __init__(
        self,
        db: AsyncSession,
        jqdata_service: JQDataService,
        risk_service: RiskService,
        websocket_manager: ConnectionManager
    ):
        self.db = db
        self.jqdata_service = jqdata_service
        self.risk_service = risk_service
        self.websocket_manager = websocket_manager
        self.running_strategies: Dict[int, bool] = {}
        self.execution_tasks: Dict[int, asyncio.Task] = {}
    
    async def start_strategy(self, strategy_id: int, user_id: int) -> bool:
        """启动策略执行"""
        try:
            # 检查策略是否存在
            result = await self.db.execute(
                select(Strategy).where(
                    Strategy.id == strategy_id,
                    Strategy.user_id == user_id
                )
            )
            strategy = result.scalar_one_or_none()
            
            if not strategy:
                logger.error(f"Strategy {strategy_id} not found for user {user_id}")
                return False
            
            if strategy_id in self.running_strategies:
                logger.warning(f"Strategy {strategy_id} is already running")
                return False
            
            # 标记策略为运行状态
            self.running_strategies[strategy_id] = True
            
            # 更新策略状态
            await self.db.execute(
                update(Strategy)
                .where(Strategy.id == strategy_id)
                .values(status='running', updated_at=datetime.utcnow())
            )
            await self.db.commit()
            
            # 创建执行任务
            task = asyncio.create_task(
                self._execute_strategy_loop(strategy)
            )
            self.execution_tasks[strategy_id] = task
            
            logger.info(f"Strategy {strategy_id} started successfully")
            
            # 发送WebSocket通知
            await self._send_strategy_notification(
                user_id, 
                strategy_id, 
                'started', 
                f"策略 {strategy.name} 已启动"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start strategy {strategy_id}: {e}")
            self.running_strategies.pop(strategy_id, None)
            return False
    
    async def stop_strategy(self, strategy_id: int, user_id: int) -> bool:
        """停止策略执行"""
        try:
            if strategy_id not in self.running_strategies:
                logger.warning(f"Strategy {strategy_id} is not running")
                return False
            
            # 停止执行循环
            self.running_strategies[strategy_id] = False
            
            # 取消执行任务
            if strategy_id in self.execution_tasks:
                task = self.execution_tasks[strategy_id]
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                del self.execution_tasks[strategy_id]
            
            # 更新策略状态
            await self.db.execute(
                update(Strategy)
                .where(Strategy.id == strategy_id)
                .values(status='stopped', updated_at=datetime.utcnow())
            )
            await self.db.commit()
            
            logger.info(f"Strategy {strategy_id} stopped successfully")
            
            # 发送WebSocket通知
            result = await self.db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
            strategy = result.scalar_one_or_none()
            
            if strategy:
                await self._send_strategy_notification(
                    user_id, 
                    strategy_id, 
                    'stopped', 
                    f"策略 {strategy.name} 已停止"
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop strategy {strategy_id}: {e}")
            return False
    
    async def _execute_strategy_loop(self, strategy: Strategy):
        """策略执行循环"""
        strategy_id = strategy.id
        user_id = strategy.user_id
        
        try:
            while self.running_strategies.get(strategy_id, False):
                # 获取策略配置
                config = json.loads(strategy.config) if strategy.config else {}
                
                # 获取市场数据
                market_data = await self._get_market_data(strategy)
                
                # 生成交易信号
                signals = await self._generate_signals(strategy, market_data, config)
                
                # 执行交易信号
                for signal in signals:
                    await self._execute_signal(strategy, signal)
                
                # 更新策略执行记录
                await self._update_execution_record(strategy, signals)
                
                # 等待下一次执行
                interval = config.get('execution_interval', 60)  # 默认60秒
                await asyncio.sleep(interval)
                
        except asyncio.CancelledError:
            logger.info(f"Strategy {strategy_id} execution cancelled")
        except Exception as e:
            logger.error(f"Error in strategy {strategy_id} execution loop: {e}")
            # 策略执行出错，停止策略
            await self.stop_strategy(strategy_id, user_id)
    
    async def _get_market_data(self, strategy: Strategy) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            config = json.loads(strategy.config) if strategy.config else {}
            symbols = config.get('symbols', [])
            
            if not symbols:
                return {}
            
            # 获取实时价格数据
            price_data = {}
            for symbol in symbols:
                try:
                    data = await self.jqdata_service.get_current_price(symbol)
                    price_data[symbol] = data
                except Exception as e:
                    logger.error(f"Failed to get price data for {symbol}: {e}")
            
            return {
                'prices': price_data,
                'timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Failed to get market data for strategy {strategy.id}: {e}")
            return {}
    
    async def _generate_signals(
        self, 
        strategy: Strategy, 
        market_data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成交易信号"""
        signals = []
        
        try:
            strategy_type = config.get('type', 'custom')
            
            if strategy_type == 'ma_crossover':
                signals = await self._ma_crossover_strategy(market_data, config)
            elif strategy_type == 'rsi_strategy':
                signals = await self._rsi_strategy(market_data, config)
            elif strategy_type == 'momentum':
                signals = await self._momentum_strategy(market_data, config)
            else:
                # 自定义策略逻辑
                signals = await self._custom_strategy(strategy, market_data, config)
            
            return signals
            
        except Exception as e:
            logger.error(f"Failed to generate signals for strategy {strategy.id}: {e}")
            return []
    
    async def _ma_crossover_strategy(
        self, 
        market_data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """均线交叉策略"""
        signals = []
        
        try:
            fast_period = config.get('fast_ma', 5)
            slow_period = config.get('slow_ma', 20)
            
            for symbol, price_info in market_data.get('prices', {}).items():
                # 获取历史数据计算均线
                historical_data = await self.jqdata_service.get_price_history(
                    symbol, 
                    count=slow_period + 10
                )
                
                if len(historical_data) < slow_period:
                    continue
                
                # 计算均线
                fast_ma = sum(historical_data[-fast_period:]) / fast_period
                slow_ma = sum(historical_data[-slow_period:]) / slow_period
                prev_fast_ma = sum(historical_data[-fast_period-1:-1]) / fast_period
                prev_slow_ma = sum(historical_data[-slow_period-1:-1]) / slow_period
                
                # 判断交叉信号
                if fast_ma > slow_ma and prev_fast_ma <= prev_slow_ma:
                    # 金叉，买入信号
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': price_info.get('current_price', 0),
                        'quantity': config.get('default_quantity', 100),
                        'confidence': 0.8,
                        'reason': f'MA{fast_period}上穿MA{slow_period}'
                    })
                elif fast_ma < slow_ma and prev_fast_ma >= prev_slow_ma:
                    # 死叉，卖出信号
                    signals.append({
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': price_info.get('current_price', 0),
                        'quantity': config.get('default_quantity', 100),
                        'confidence': 0.8,
                        'reason': f'MA{fast_period}下穿MA{slow_period}'
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error in MA crossover strategy: {e}")
            return []
    
    async def _rsi_strategy(
        self, 
        market_data: Dict[str, Any], 
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """RSI策略"""
        signals = []
        
        try:
            rsi_period = config.get('rsi_period', 14)
            oversold_threshold = config.get('oversold', 30)
            overbought_threshold = config.get('overbought', 70)
            
            for symbol, price_info in market_data.get('prices', {}).items():
                # 获取历史数据计算RSI
                historical_data = await self.jqdata_service.get_price_history(
                    symbol, 
                    count=rsi_period + 10
                )
                
                if len(historical_data) < rsi_period + 1:
                    continue
                
                # 计算RSI
                rsi = await self._calculate_rsi(historical_data, rsi_period)
                
                if rsi < oversold_threshold:
                    # 超卖，买入信号
                    signals.append({
                        'symbol': symbol,
                        'action': 'BUY',
                        'price': price_info.get('current_price', 0),
                        'quantity': config.get('default_quantity', 100),
                        'confidence': 0.7,
                        'reason': f'RSI({rsi:.2f})超卖'
                    })
                elif rsi > overbought_threshold:
                    # 超买，卖出信号
                    signals.append({
                        'symbol': symbol,
                        'action': 'SELL',
                        'price': price_info.get('current_price', 0),
                        'quantity': config.get('default_quantity', 100),
                        'confidence': 0.7,
                        'reason': f'RSI({rsi:.2f})超买'
                    })
            
            return signals
            
        except Exception as e:
            logger.error(f"Error in RSI strategy: {e}")
            return []
    
    async def _calculate_rsi(self, prices: List[float], period: int) -> float:
        """计算RSI指标"""
        if len(prices) < period + 1:
            return 50.0
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        if len(gains) < period:
            return 50.0
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    async def _execute_signal(self, strategy: Strategy, signal: Dict[str, Any]):
        """执行交易信号"""
        try:
            # 风险检查
            risk_check = await self.risk_service.check_trade_risk(
                strategy.user_id,
                signal['symbol'],
                signal['action'],
                signal['quantity'],
                signal['price']
            )
            
            if not risk_check['allowed']:
                logger.warning(f"Trade rejected by risk control: {risk_check['reason']}")
                return
            
            # 发送交易信号通知
            await self._send_trade_signal(strategy.user_id, signal)
            
            # 这里应该连接到实际的交易接口
            # 目前只是记录信号
            logger.info(f"Trade signal: {signal}")
            
        except Exception as e:
            logger.error(f"Failed to execute signal: {e}")
    
    async def _send_trade_signal(self, user_id: int, signal: Dict[str, Any]):
        """发送交易信号通知"""
        try:
            message = {
                'type': 'trade_signal',
                'data': {
                    'strategyId': signal.get('strategy_id'),
                    'symbol': signal['symbol'],
                    'action': signal['action'],
                    'price': signal['price'],
                    'quantity': signal['quantity'],
                    'confidence': signal['confidence'],
                    'reason': signal.get('reason', ''),
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            await self.websocket_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(f"Failed to send trade signal: {e}")
    
    async def _send_strategy_notification(
        self, 
        user_id: int, 
        strategy_id: int, 
        event: str, 
        message: str
    ):
        """发送策略通知"""
        try:
            notification = {
                'type': 'system_notification',
                'data': {
                    'id': f"strategy_{strategy_id}_{event}_{int(datetime.utcnow().timestamp())}",
                    'type': 'info',
                    'title': '策略通知',
                    'message': message,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            await self.websocket_manager.send_to_user(user_id, notification)
            
        except Exception as e:
            logger.error(f"Failed to send strategy notification: {e}")
    
    async def _update_execution_record(
        self, 
        strategy: Strategy, 
        signals: List[Dict[str, Any]]
    ):
        """更新策略执行记录"""
        try:
            execution_record = StrategyExecution(
                strategy_id=strategy.id,
                user_id=strategy.user_id,
                executed_at=datetime.utcnow(),
                signals_count=len(signals),
                execution_data=json.dumps(signals) if signals else None
            )
            
            self.db.add(execution_record)
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"Failed to update execution record: {e}")
    
    async def get_running_strategies(self) -> List[int]:
        """获取正在运行的策略列表"""
        return list(self.running_strategies.keys())
    
    async def is_strategy_running(self, strategy_id: int) -> bool:
        """检查策略是否正在运行"""
        return self.running_strategies.get(strategy_id, False)
