{"name": "@reactflow/node-resizer", "version": "2.2.14", "description": "A helper component for resizing nodes.", "keywords": ["react", "node-based UI", "graph", "diagram", "workflow", "react-flow"], "files": ["dist"], "source": "src/index.tsx", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/umd/index.js"}, "./dist/style.css": "./dist/style.css"}, "sideEffects": ["*.css"], "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/node-resizer"}, "dependencies": {"classcat": "^5.0.4", "d3-drag": "^3.0.0", "d3-selection": "^3.0.0", "zustand": "^4.4.1", "@reactflow/core": "11.11.4"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "devDependencies": {"@types/d3-drag": "^3.0.1", "@types/d3-selection": "^3.0.3", "@types/node": "^18.7.16", "@types/react": ">=17", "@types/react-dom": ">=17", "react": "^18.2.0", "typescript": "^4.9.4", "@reactflow/eslint-config": "0.0.0", "@reactflow/rollup-config": "0.0.0", "@reactflow/tsconfig": "0.0.0"}, "rollup": {"globals": {"zustand": "Zustand", "zustand/shallow": "zustandShallow", "classcat": "cc"}, "name": "ReactFlowNodeResizer"}, "scripts": {"dev": "concurrently \"rollup --config node:@reactflow/rollup-config --watch\" pnpm:css-watch", "build": "rollup --config node:@reactflow/rollup-config --environment NODE_ENV:production && npm run css", "css": "postcss src/*.css --config ../../tooling/postcss-config/postcss.config.js --dir dist", "css-watch": "pnpm css --watch", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "typecheck": "tsc --noEmit"}}