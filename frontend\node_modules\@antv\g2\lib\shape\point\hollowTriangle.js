"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowTriangle = void 0;
const color_1 = require("./color");
/**
 * △
 */
const HollowTriangle = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'triangle' }, options), context);
};
exports.HollowTriangle = HollowTriangle;
exports.HollowTriangle.props = Object.assign({ defaultMarker: 'hollowTriangle' }, color_1.Color.props);
//# sourceMappingURL=hollowTriangle.js.map