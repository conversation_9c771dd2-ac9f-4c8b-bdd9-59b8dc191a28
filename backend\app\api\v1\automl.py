"""
AutoML相关API端点

提供自动化机器学习的API接口，包括实验管理、模型训练、预测等功能
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.automl import AutoMLExperiment, AutoMLModel, AutoMLPrediction
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.automl_service import automl_service

router = APIRouter()


# =============================================================================
# Pydantic模型
# =============================================================================

class ExperimentCreateRequest(BaseModel):
    """创建实验请求"""
    name: str = Field(..., description="实验名称")
    description: Optional[str] = Field(None, description="实验描述")
    experiment_type: str = Field(..., description="实验类型", regex="^(classification|regression|time_series)$")
    problem_type: str = Field(..., description="问题类型", regex="^(binary_classification|multi_classification|regression)$")
    
    # 数据配置
    dataset_source: str = Field(..., description="数据源", regex="^(jqdata|upload|database)$")
    dataset_config: Dict[str, Any] = Field(..., description="数据集配置")
    target_column: str = Field(..., description="目标列")
    feature_columns: Optional[List[str]] = Field(None, description="特征列")
    
    # 实验配置
    evaluation_metric: Optional[str] = Field("accuracy", description="评估指标")
    cross_validation_folds: Optional[int] = Field(5, ge=2, le=10, description="交叉验证折数")
    test_size: Optional[float] = Field(0.2, ge=0.1, le=0.5, description="测试集比例")
    random_state: Optional[int] = Field(42, description="随机种子")
    
    # 时间限制
    max_runtime_minutes: Optional[int] = Field(60, ge=5, le=480, description="最大运行时间（分钟）")
    max_models: Optional[int] = Field(50, ge=5, le=200, description="最大模型数量")
    early_stopping_rounds: Optional[int] = Field(10, ge=5, le=50, description="早停轮数")
    
    # AutoML配置
    enable_feature_selection: Optional[bool] = Field(True, description="启用特征选择")
    enable_hyperparameter_tuning: Optional[bool] = Field(True, description="启用超参数调优")
    enable_ensemble: Optional[bool] = Field(True, description="启用模型集成")
    enable_stacking: Optional[bool] = Field(False, description="启用堆叠")
    
    # 算法选择
    included_algorithms: Optional[List[str]] = Field(None, description="包含的算法")
    excluded_algorithms: Optional[List[str]] = Field(None, description="排除的算法")
    
    # 特征工程配置
    enable_feature_engineering: Optional[bool] = Field(True, description="启用特征工程")
    max_feature_interactions: Optional[int] = Field(2, ge=1, le=5, description="最大特征交互数")
    enable_polynomial_features: Optional[bool] = Field(False, description="启用多项式特征")
    enable_text_features: Optional[bool] = Field(False, description="启用文本特征")


class PredictionRequest(BaseModel):
    """预测请求"""
    model_id: int = Field(..., description="模型ID")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    prediction_name: Optional[str] = Field(None, description="预测名称")


# =============================================================================
# 实验管理
# =============================================================================

@router.post("/experiments", response_model=BaseResponse[dict])
async def create_experiment(
    request: ExperimentCreateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建AutoML实验"""
    try:
        experiment_config = request.dict()
        
        result = await automl_service.run_automl_experiment(
            current_user.id, experiment_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="AutoML实验创建成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建AutoML实验失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建AutoML实验失败"
        )


@router.get("/experiments", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_experiments(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, regex="^(created|running|completed|failed|cancelled)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取实验列表"""
    try:
        # 构建查询条件
        conditions = [AutoMLExperiment.user_id == current_user.id]
        if status:
            conditions.append(AutoMLExperiment.status == status)
        
        # 查询总数
        count_query = select(func.count(AutoMLExperiment.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(AutoMLExperiment)
            .where(and_(*conditions))
            .order_by(desc(AutoMLExperiment.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        experiments = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        experiment_list = []
        for exp in experiments:
            experiment_list.append({
                "id": exp.id,
                "name": exp.name,
                "description": exp.description,
                "experiment_type": exp.experiment_type,
                "problem_type": exp.problem_type,
                "status": exp.status,
                "progress": exp.progress,
                "current_stage": exp.current_stage,
                "total_models_trained": exp.total_models_trained,
                "best_score": float(exp.best_score) if exp.best_score else None,
                "created_at": exp.created_at.isoformat(),
                "started_at": exp.started_at.isoformat() if exp.started_at else None,
                "completed_at": exp.completed_at.isoformat() if exp.completed_at else None
            })
        
        return BaseResponse(
            code=200,
            message="获取实验列表成功",
            data=PaginatedResponse(
                items=experiment_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取实验列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验列表失败"
        )


@router.get("/experiments/{experiment_id}", response_model=BaseResponse[dict])
async def get_experiment(
    experiment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取实验详情"""
    try:
        result = await automl_service.get_experiment_status(experiment_id, db)
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="获取实验详情成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实验详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验详情失败"
        )


@router.get("/experiments/{experiment_id}/results", response_model=BaseResponse[dict])
async def get_experiment_results(
    experiment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取实验结果"""
    try:
        result = await automl_service.get_experiment_results(experiment_id, db)
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="获取实验结果成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实验结果失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取实验结果失败"
        )


@router.delete("/experiments/{experiment_id}", response_model=BaseResponse[dict])
async def delete_experiment(
    experiment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """删除实验"""
    try:
        # 检查实验是否存在且属于当前用户
        result = await db.execute(
            select(AutoMLExperiment).where(
                and_(
                    AutoMLExperiment.id == experiment_id,
                    AutoMLExperiment.user_id == current_user.id
                )
            )
        )
        experiment = result.scalar_one_or_none()
        
        if not experiment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="实验不存在"
            )
        
        # 删除实验（级联删除相关数据）
        await db.delete(experiment)
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="实验删除成功",
            data={"experiment_id": experiment_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除实验失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除实验失败"
        )


# =============================================================================
# 模型管理
# =============================================================================

@router.get("/models", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_models(
    experiment_id: Optional[int] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取模型列表"""
    try:
        # 构建查询条件
        conditions = [AutoMLModel.user_id == current_user.id]
        if experiment_id:
            conditions.append(AutoMLModel.experiment_id == experiment_id)
        
        # 查询总数
        count_query = select(func.count(AutoMLModel.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(AutoMLModel)
            .where(and_(*conditions))
            .order_by(desc(AutoMLModel.validation_score))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        models = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "experiment_id": model.experiment_id,
                "model_name": model.model_name,
                "algorithm": model.algorithm,
                "model_type": model.model_type,
                "validation_score": float(model.validation_score) if model.validation_score else None,
                "cross_validation_score": float(model.cross_validation_score) if model.cross_validation_score else None,
                "training_time_seconds": float(model.training_time_seconds) if model.training_time_seconds else None,
                "model_size_mb": float(model.model_size_mb) if model.model_size_mb else None,
                "is_best_model": model.is_best_model,
                "is_deployed": model.is_deployed,
                "status": model.status,
                "created_at": model.created_at.isoformat(),
                "trained_at": model.trained_at.isoformat() if model.trained_at else None
            })
        
        return BaseResponse(
            code=200,
            message="获取模型列表成功",
            data=PaginatedResponse(
                items=model_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.get("/models/{model_id}", response_model=BaseResponse[dict])
async def get_model(
    model_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取模型详情"""
    try:
        result = await db.execute(
            select(AutoMLModel).where(
                and_(
                    AutoMLModel.id == model_id,
                    AutoMLModel.user_id == current_user.id
                )
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在"
            )
        
        model_data = {
            "id": model.id,
            "experiment_id": model.experiment_id,
            "model_name": model.model_name,
            "algorithm": model.algorithm,
            "model_type": model.model_type,
            "hyperparameters": model.hyperparameters,
            "feature_list": model.feature_list,
            "validation_score": float(model.validation_score) if model.validation_score else None,
            "cross_validation_score": float(model.cross_validation_score) if model.cross_validation_score else None,
            "cross_validation_std": float(model.cross_validation_std) if model.cross_validation_std else None,
            "performance_metrics": model.performance_metrics,
            "feature_importance": model.feature_importance,
            "training_time_seconds": float(model.training_time_seconds) if model.training_time_seconds else None,
            "model_size_mb": float(model.model_size_mb) if model.model_size_mb else None,
            "is_best_model": model.is_best_model,
            "is_deployed": model.is_deployed,
            "status": model.status,
            "model_version": model.model_version,
            "created_at": model.created_at.isoformat(),
            "trained_at": model.trained_at.isoformat() if model.trained_at else None
        }
        
        return BaseResponse(
            code=200,
            message="获取模型详情成功",
            data=model_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型详情失败"
        )


# =============================================================================
# 模型预测
# =============================================================================

@router.post("/predict", response_model=BaseResponse[dict])
async def make_prediction(
    request: PredictionRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """使用模型进行预测"""
    try:
        # 检查模型是否存在且属于当前用户
        result = await db.execute(
            select(AutoMLModel).where(
                and_(
                    AutoMLModel.id == request.model_id,
                    AutoMLModel.user_id == current_user.id
                )
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在"
            )
        
        # 这里应该实现实际的预测逻辑
        # 由于模型存储为二进制数据，需要反序列化并进行预测
        
        # 模拟预测结果
        prediction_result = {
            "model_id": request.model_id,
            "prediction": [0.75],  # 示例预测结果
            "prediction_probability": [0.25, 0.75],  # 示例预测概率
            "confidence": 0.85,
            "prediction_time_ms": 15.2
        }
        
        # 保存预测记录
        prediction_record = AutoMLPrediction(
            model_id=request.model_id,
            user_id=current_user.id,
            prediction_name=request.prediction_name,
            prediction_type="single",
            input_data=request.input_data,
            predictions=prediction_result["prediction"],
            prediction_probabilities=prediction_result.get("prediction_probability"),
            prediction_count=1,
            prediction_time_ms=prediction_result["prediction_time_ms"],
            average_confidence=prediction_result["confidence"],
            status="completed"
        )
        
        db.add(prediction_record)
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="预测完成",
            data={
                "prediction_id": prediction_record.id,
                **prediction_result
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"模型预测失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="模型预测失败"
        )
