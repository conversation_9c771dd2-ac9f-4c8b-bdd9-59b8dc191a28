"""
市场数据相关数据模式

定义股票信息、价格数据等相关的请求和响应模式
"""

from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class StockListRequest(BaseModel):
    """股票列表请求"""
    
    market: str = Field(
        "A", 
        regex="^(A|HK|US)$", 
        description="市场类型: A(A股), HK(港股), US(美股)"
    )
    industry: Optional[str] = Field(None, description="行业筛选")
    sector: Optional[str] = Field(None, description="板块筛选")
    is_active: bool = Field(True, description="是否只返回活跃股票")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(50, ge=1, le=1000, description="每页大小")


class StockResponse(BaseModel):
    """股票响应"""
    
    id: int = Field(..., description="股票ID")
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    display_name: Optional[str] = Field(None, description="显示名称")
    market: str = Field(..., description="市场代码")
    exchange: str = Field(..., description="交易所")
    currency: str = Field(..., description="交易货币")
    industry: Optional[str] = Field(None, description="所属行业")
    sector: Optional[str] = Field(None, description="所属板块")
    concept: Optional[List[str]] = Field(None, description="概念标签")
    list_date: Optional[date] = Field(None, description="上市日期")
    delist_date: Optional[date] = Field(None, description="退市日期")
    is_active: bool = Field(..., description="是否活跃交易")
    is_st: bool = Field(..., description="是否ST股票")
    total_share: Optional[Decimal] = Field(None, description="总股本(万股)")
    float_share: Optional[Decimal] = Field(None, description="流通股本(万股)")
    market_cap: Optional[Decimal] = Field(None, description="总市值(万元)")
    last_update: Optional[datetime] = Field(None, description="最后更新时间")
    data_source: str = Field(..., description="数据来源")
    
    @validator('concept', pre=True)
    def parse_concept(cls, v):
        """解析概念标签"""
        if isinstance(v, str):
            try:
                import json
                return json.loads(v)
            except:
                return v.split(',') if v else []
        return v
    
    class Config:
        from_attributes = True


class PriceDataRequest(BaseModel):
    """价格数据请求"""
    
    symbols: List[str] = Field(..., min_items=1, max_items=100, description="股票代码列表")
    start_date: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$", description="开始日期(YYYY-MM-DD)")
    end_date: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$", description="结束日期(YYYY-MM-DD)")
    frequency: str = Field(
        "daily", 
        regex="^(daily|minute|tick)$", 
        description="数据频率: daily(日线), minute(分钟), tick(逐笔)"
    )
    fields: Optional[List[str]] = Field(
        None, 
        description="返回字段，默认返回所有字段"
    )
    adjust: str = Field(
        "none", 
        regex="^(none|pre|post)$", 
        description="复权类型: none(不复权), pre(前复权), post(后复权)"
    )
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        """验证日期范围"""
        if 'start_date' in values:
            from datetime import datetime
            start = datetime.strptime(values['start_date'], '%Y-%m-%d')
            end = datetime.strptime(v, '%Y-%m-%d')
            
            if end < start:
                raise ValueError('结束日期不能早于开始日期')
            
            # 限制查询范围不超过1年
            if (end - start).days > 365:
                raise ValueError('查询范围不能超过1年')
        
        return v


class PriceDataResponse(BaseModel):
    """价格数据响应"""
    
    symbol: str = Field(..., description="股票代码")
    datetime: str = Field(..., description="时间戳")
    open_price: Optional[Decimal] = Field(None, description="开盘价")
    high_price: Optional[Decimal] = Field(None, description="最高价")
    low_price: Optional[Decimal] = Field(None, description="最低价")
    close_price: Optional[Decimal] = Field(None, description="收盘价")
    volume: Optional[int] = Field(None, description="成交量")
    turnover: Optional[Decimal] = Field(None, description="成交额")
    adj_close: Optional[Decimal] = Field(None, description="复权收盘价")
    change_pct: Optional[Decimal] = Field(None, description="涨跌幅(%)")
    turnover_rate: Optional[Decimal] = Field(None, description="换手率(%)")
    is_trading_day: Optional[bool] = Field(None, description="是否交易日")
    is_suspended: Optional[bool] = Field(None, description="是否停牌")
    
    class Config:
        from_attributes = True


class TickDataRequest(BaseModel):
    """Tick数据请求"""
    
    symbol: str = Field(..., description="股票代码")
    date: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$", description="查询日期(YYYY-MM-DD)")
    start_time: Optional[str] = Field(None, regex=r"^\d{2}:\d{2}:\d{2}$", description="开始时间(HH:MM:SS)")
    end_time: Optional[str] = Field(None, regex=r"^\d{2}:\d{2}:\d{2}$", description="结束时间(HH:MM:SS)")


class TickDataResponse(BaseModel):
    """Tick数据响应"""
    
    symbol: str = Field(..., description="股票代码")
    datetime: str = Field(..., description="时间戳")
    current_price: Optional[Decimal] = Field(None, description="当前价")
    volume: Optional[int] = Field(None, description="成交量")
    turnover: Optional[Decimal] = Field(None, description="成交额")
    bid_price_1: Optional[Decimal] = Field(None, description="买一价")
    bid_volume_1: Optional[int] = Field(None, description="买一量")
    ask_price_1: Optional[Decimal] = Field(None, description="卖一价")
    ask_volume_1: Optional[int] = Field(None, description="卖一量")
    
    class Config:
        from_attributes = True


class MarketOverviewResponse(BaseModel):
    """市场概览响应"""
    
    market: str = Field(..., description="市场代码")
    total_stocks: int = Field(..., description="股票总数")
    trading_stocks: int = Field(..., description="交易中股票数")
    suspended_stocks: int = Field(..., description="停牌股票数")
    up_stocks: int = Field(..., description="上涨股票数")
    down_stocks: int = Field(..., description="下跌股票数")
    flat_stocks: int = Field(..., description="平盘股票数")
    total_volume: Optional[Decimal] = Field(None, description="总成交量")
    total_turnover: Optional[Decimal] = Field(None, description="总成交额")
    avg_change_pct: Optional[Decimal] = Field(None, description="平均涨跌幅")
    last_update: datetime = Field(..., description="最后更新时间")


class IndustryPerformanceResponse(BaseModel):
    """行业表现响应"""
    
    industry: str = Field(..., description="行业名称")
    stock_count: int = Field(..., description="股票数量")
    avg_change_pct: Decimal = Field(..., description="平均涨跌幅")
    total_volume: Decimal = Field(..., description="总成交量")
    total_turnover: Decimal = Field(..., description="总成交额")
    top_gainers: List[dict] = Field(..., description="涨幅榜前5")
    top_losers: List[dict] = Field(..., description="跌幅榜前5")


class StockSearchRequest(BaseModel):
    """股票搜索请求"""
    
    keyword: str = Field(..., min_length=1, max_length=50, description="搜索关键词")
    search_type: str = Field(
        "all", 
        regex="^(all|code|name|pinyin)$", 
        description="搜索类型: all(全部), code(代码), name(名称), pinyin(拼音)"
    )
    market: Optional[str] = Field(None, regex="^(A|HK|US)$", description="市场筛选")
    limit: int = Field(20, ge=1, le=100, description="返回数量限制")


class StockSearchResponse(BaseModel):
    """股票搜索响应"""
    
    symbol: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    market: str = Field(..., description="市场代码")
    industry: Optional[str] = Field(None, description="所属行业")
    match_type: str = Field(..., description="匹配类型")
    match_score: float = Field(..., description="匹配分数")


class DataUpdateRequest(BaseModel):
    """数据更新请求"""
    
    data_type: str = Field(
        ..., 
        regex="^(stocks|daily_prices|minute_prices|tick_data)$", 
        description="数据类型"
    )
    symbols: Optional[List[str]] = Field(None, description="指定股票代码，为空则更新全部")
    date_range: Optional[dict] = Field(None, description="日期范围")
    force_update: bool = Field(False, description="是否强制更新")


class DataUpdateResponse(BaseModel):
    """数据更新响应"""
    
    task_id: str = Field(..., description="任务ID")
    data_type: str = Field(..., description="数据类型")
    status: str = Field(..., description="任务状态")
    total_count: int = Field(..., description="总数量")
    processed_count: int = Field(..., description="已处理数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    start_time: datetime = Field(..., description="开始时间")
    estimated_completion: Optional[datetime] = Field(None, description="预计完成时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class MarketCalendarRequest(BaseModel):
    """交易日历请求"""
    
    start_date: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$", description="开始日期")
    end_date: str = Field(..., regex=r"^\d{4}-\d{2}-\d{2}$", description="结束日期")
    market: str = Field("A", regex="^(A|HK|US)$", description="市场类型")


class MarketCalendarResponse(BaseModel):
    """交易日历响应"""
    
    date: str = Field(..., description="日期")
    is_trading_day: bool = Field(..., description="是否交易日")
    market: str = Field(..., description="市场代码")
    trading_session: Optional[str] = Field(None, description="交易时段")
    holiday_name: Optional[str] = Field(None, description="节假日名称")


class DataQualityReport(BaseModel):
    """数据质量报告"""
    
    data_type: str = Field(..., description="数据类型")
    total_records: int = Field(..., description="总记录数")
    valid_records: int = Field(..., description="有效记录数")
    invalid_records: int = Field(..., description="无效记录数")
    missing_data_rate: float = Field(..., description="缺失数据率")
    duplicate_rate: float = Field(..., description="重复数据率")
    quality_score: float = Field(..., ge=0, le=100, description="质量评分")
    last_check: datetime = Field(..., description="最后检查时间")
    issues: List[dict] = Field(..., description="问题列表")
