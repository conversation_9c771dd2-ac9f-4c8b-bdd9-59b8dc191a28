# =============================================================================
# 智能量化交易平台 - 环境变量配置
# =============================================================================

# 应用基础配置
APP_NAME=智能量化交易平台
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=development
TZ=Asia/Shanghai

# 数据库配置
DATABASE_URL=postgresql://quant_user:quant_password@localhost:5432/quantitative_trading
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=50
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# JWT 安全配置
SECRET_KEY=your-super-secret-key-change-in-production-32-chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 密码加密密钥 (用于JQData密码加密，必须32字符)
ENCRYPTION_KEY=your-encryption-key-32-chars-long-change-this

# CORS 配置
CORS_ORIGINS=["http://localhost:3000","https://yourdomain.com"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET","POST","PUT","DELETE","OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# JQData 配置
JQDATA_DEFAULT_QUOTA=10000
JQDATA_RATE_LIMIT_PER_MINUTE=100
JQDATA_MAX_RETRY_ATTEMPTS=3
JQDATA_RETRY_DELAY=1
JQDATA_TIMEOUT=30

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=["csv","xlsx","json"]

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_KEY_PREFIX=jqdata

# 安全限制配置
RATE_LIMIT_PER_MINUTE=60
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=300
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=false

# 邮件配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=JQData平台

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=********
LOG_BACKUP_COUNT=5
LOG_FORMAT=json

# Celery 配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=Asia/Shanghai
CELERY_ENABLE_UTC=false

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
PERFORMANCE_MONITORING=true

# 数据更新配置
DATA_UPDATE_HOUR=18
DATA_UPDATE_MINUTE=30
WEEKEND_UPDATE_ENABLED=false
HOLIDAY_UPDATE_ENABLED=false

# 回测配置
BACKTEST_MAX_DURATION_DAYS=3650
BACKTEST_DEFAULT_INITIAL_CAPITAL=100000
BACKTEST_MAX_CONCURRENT_JOBS=5
BACKTEST_RESULT_CACHE_HOURS=24

# WebSocket 配置
WEBSOCKET_MAX_CONNECTIONS=1000
WEBSOCKET_HEARTBEAT_INTERVAL=30
WEBSOCKET_MESSAGE_MAX_SIZE=1048576

# 第三方服务配置 (可选)
# Sentry 错误追踪
SENTRY_DSN=

# 云存储配置 (可选)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# 钉钉机器人 (可选)
DINGTALK_WEBHOOK=
DINGTALK_SECRET=

# 企业微信机器人 (可选)
WECHAT_WEBHOOK=

# 开发环境特定配置
DEV_AUTO_RELOAD=true
DEV_DEBUG_SQL=false
DEV_MOCK_JQDATA=false
DEV_SKIP_AUTH=false

# 测试环境配置
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/jqdata_test
TEST_REDIS_URL=redis://localhost:6380/0

# 生产环境配置
PROD_SSL_REQUIRED=true
PROD_SECURE_COOKIES=true
PROD_SESSION_COOKIE_SECURE=true
PROD_CSRF_COOKIE_SECURE=true

# Docker 配置
DOCKER_POSTGRES_VERSION=15-alpine
DOCKER_REDIS_VERSION=7-alpine
DOCKER_PYTHON_VERSION=3.11-slim
DOCKER_NODE_VERSION=18-alpine

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# 性能配置
MAX_WORKERS=4
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# 机器学习配置
ML_MODEL_DIR=./models
ML_TEMP_DIR=./temp
ML_MAX_TRAINING_TIME=3600
ML_DEFAULT_TEST_SIZE=0.2
ML_DEFAULT_RANDOM_STATE=42
ML_N_JOBS=-1

# 深度学习配置
DL_DEVICE=auto
DL_BATCH_SIZE=32
DL_EPOCHS=100
DL_LEARNING_RATE=0.001
DL_EARLY_STOPPING_PATIENCE=10
DL_MODEL_CHECKPOINT_DIR=./checkpoints

# AutoML配置
AUTOML_TIME_LIMIT=3600
AUTOML_MEMORY_LIMIT=8192
AUTOML_N_JOBS=-1
AUTOML_CV_FOLDS=5
AUTOML_METRIC=accuracy

# 知识图谱配置
KG_MAX_ENTITIES=10000
KG_MAX_RELATIONS=50000
KG_EMBEDDING_DIM=128
KG_GNN_HIDDEN_DIM=64

# 模型可解释性配置
EXPLAINABILITY_ENABLED=true
SHAP_MAX_SAMPLES=1000
LIME_NUM_FEATURES=10
LIME_NUM_SAMPLES=5000

# 多模态数据配置
MULTIMODAL_ENABLED=true
TEXT_MODEL=bert-base-chinese
IMAGE_MODEL=resnet50
AUDIO_MODEL=wav2vec2

# 第三方API配置
TUSHARE_TOKEN=your_tushare_token
AKSHARE_ENABLED=true
YFINANCE_ENABLED=true
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
OPENAI_API_KEY=your_openai_api_key

# 功能开关
FEATURE_MACHINE_LEARNING=true
FEATURE_DEEP_LEARNING=true
FEATURE_AUTOML=true
FEATURE_KNOWLEDGE_GRAPH=true
FEATURE_EXPLAINABILITY=true
FEATURE_MULTIMODAL=true
FEATURE_SOCIAL_TRADING=false
FEATURE_OPTIONS_PRICING=true
FEATURE_NEWS_ANALYSIS=true
FEATURE_PORTFOLIO_OPTIMIZATION=true
