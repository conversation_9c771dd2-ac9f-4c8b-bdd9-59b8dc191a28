"""
API限流中间件

提供基于用户、IP、API端点的限流功能
"""

import time
import json
from typing import Dict, Optional, Tuple, Any
from datetime import datetime, timedelta
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import redis
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import logger
from app.models.user import User


class RateLimiter:
    """限流器"""
    
    def __init__(self):
        # 连接Redis
        try:
            self.redis_client = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                decode_responses=True
            )
            # 测试连接
            self.redis_client.ping()
            self.redis_available = True
            logger.info("Redis连接成功，启用分布式限流")
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存限流: {e}")
            self.redis_available = False
            self.memory_store: Dict[str, Dict] = {}
    
    def _get_key(self, identifier: str, window: str) -> str:
        """生成限流键"""
        return f"rate_limit:{identifier}:{window}"
    
    def _get_current_window(self, window_size: int) -> int:
        """获取当前时间窗口"""
        return int(time.time()) // window_size
    
    def check_rate_limit(
        self, 
        identifier: str, 
        limit: int, 
        window_size: int = 60
    ) -> Tuple[bool, Dict[str, Any]]:
        """检查限流状态"""
        try:
            current_window = self._get_current_window(window_size)
            key = self._get_key(identifier, str(current_window))
            
            if self.redis_available:
                return self._check_redis_rate_limit(key, limit, window_size)
            else:
                return self._check_memory_rate_limit(key, limit, window_size)
                
        except Exception as e:
            logger.error(f"检查限流状态失败: {e}")
            # 限流检查失败时，允许请求通过
            return True, {"requests": 0, "limit": limit, "remaining": limit, "reset_time": 0}
    
    def _check_redis_rate_limit(
        self, 
        key: str, 
        limit: int, 
        window_size: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """Redis限流检查"""
        try:
            # 使用Redis管道提高性能
            pipe = self.redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, window_size)
            results = pipe.execute()
            
            current_requests = results[0]
            
            # 计算重置时间
            ttl = self.redis_client.ttl(key)
            reset_time = int(time.time()) + ttl if ttl > 0 else int(time.time()) + window_size
            
            allowed = current_requests <= limit
            remaining = max(0, limit - current_requests)
            
            return allowed, {
                "requests": current_requests,
                "limit": limit,
                "remaining": remaining,
                "reset_time": reset_time,
                "window_size": window_size
            }
            
        except Exception as e:
            logger.error(f"Redis限流检查失败: {e}")
            return True, {"requests": 0, "limit": limit, "remaining": limit, "reset_time": 0}
    
    def _check_memory_rate_limit(
        self, 
        key: str, 
        limit: int, 
        window_size: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """内存限流检查"""
        current_time = time.time()
        
        if key not in self.memory_store:
            self.memory_store[key] = {
                "requests": 1,
                "window_start": current_time
            }
        else:
            # 检查是否需要重置窗口
            if current_time - self.memory_store[key]["window_start"] >= window_size:
                self.memory_store[key] = {
                    "requests": 1,
                    "window_start": current_time
                }
            else:
                self.memory_store[key]["requests"] += 1
        
        current_requests = self.memory_store[key]["requests"]
        window_start = self.memory_store[key]["window_start"]
        
        allowed = current_requests <= limit
        remaining = max(0, limit - current_requests)
        reset_time = int(window_start + window_size)
        
        return allowed, {
            "requests": current_requests,
            "limit": limit,
            "remaining": remaining,
            "reset_time": reset_time,
            "window_size": window_size
        }
    
    def cleanup_expired_keys(self):
        """清理过期的内存键（仅内存模式）"""
        if self.redis_available:
            return
        
        current_time = time.time()
        expired_keys = []
        
        for key, data in self.memory_store.items():
            if current_time - data["window_start"] > 3600:  # 1小时后清理
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.memory_store[key]


class RateLimitConfig:
    """限流配置"""
    
    # 默认限流规则
    DEFAULT_RULES = {
        # 全局限流
        "global": {"limit": 1000, "window": 60},  # 每分钟1000次
        
        # 用户限流
        "user": {"limit": 100, "window": 60},  # 每分钟100次
        
        # IP限流
        "ip": {"limit": 200, "window": 60},  # 每分钟200次
        
        # API端点限流
        "endpoints": {
            "/api/v1/auth/login": {"limit": 5, "window": 60},  # 登录限流
            "/api/v1/auth/register": {"limit": 3, "window": 60},  # 注册限流
            "/api/v1/jqdata/test": {"limit": 10, "window": 60},  # JQData测试限流
            "/api/v1/market/stocks": {"limit": 50, "window": 60},  # 股票数据限流
            "/api/v1/indicators/calculate": {"limit": 30, "window": 60},  # 指标计算限流
        }
    }
    
    # VIP用户限流规则
    VIP_RULES = {
        "user": {"limit": 500, "window": 60},  # VIP用户每分钟500次
        "endpoints": {
            "/api/v1/indicators/calculate": {"limit": 100, "window": 60},  # VIP指标计算限流
        }
    }
    
    @classmethod
    def get_user_limits(cls, user: Optional[User]) -> Dict[str, Any]:
        """获取用户限流规则"""
        if user and getattr(user, 'is_vip', False):
            return {**cls.DEFAULT_RULES, **cls.VIP_RULES}
        return cls.DEFAULT_RULES


class RateLimitMiddleware:
    """限流中间件"""
    
    def __init__(self):
        self.limiter = RateLimiter()
        self.config = RateLimitConfig()
    
    async def __call__(self, request: Request, call_next):
        """中间件处理函数"""
        try:
            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_id = self._get_user_id(request)
            endpoint = request.url.path
            
            # 检查限流
            rate_limit_result = await self._check_all_limits(
                request, client_ip, user_id, endpoint
            )
            
            if not rate_limit_result["allowed"]:
                return self._create_rate_limit_response(rate_limit_result)
            
            # 处理请求
            response = await call_next(request)
            
            # 添加限流头信息
            self._add_rate_limit_headers(response, rate_limit_result)
            
            return response
            
        except Exception as e:
            logger.error(f"限流中间件处理失败: {e}")
            # 中间件失败时，允许请求继续
            return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _get_user_id(self, request: Request) -> Optional[str]:
        """从请求中获取用户ID"""
        try:
            # 从JWT token中获取用户ID
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                # 这里应该解析JWT token获取用户ID
                # 简化处理，实际应该使用JWT库解析
                return "user_from_token"
            return None
        except Exception:
            return None
    
    async def _check_all_limits(
        self, 
        request: Request, 
        client_ip: str, 
        user_id: Optional[str], 
        endpoint: str
    ) -> Dict[str, Any]:
        """检查所有限流规则"""
        
        # 获取用户限流配置
        user = None  # 实际应该从数据库获取用户信息
        limits = self.config.get_user_limits(user)
        
        # 检查IP限流
        ip_allowed, ip_info = self.limiter.check_rate_limit(
            f"ip:{client_ip}",
            limits["ip"]["limit"],
            limits["ip"]["window"]
        )
        
        if not ip_allowed:
            return {
                "allowed": False,
                "type": "ip",
                "info": ip_info,
                "message": "IP请求频率过高，请稍后再试"
            }
        
        # 检查用户限流
        if user_id:
            user_allowed, user_info = self.limiter.check_rate_limit(
                f"user:{user_id}",
                limits["user"]["limit"],
                limits["user"]["window"]
            )
            
            if not user_allowed:
                return {
                    "allowed": False,
                    "type": "user",
                    "info": user_info,
                    "message": "用户请求频率过高，请稍后再试"
                }
        
        # 检查端点限流
        if endpoint in limits["endpoints"]:
            endpoint_config = limits["endpoints"][endpoint]
            endpoint_allowed, endpoint_info = self.limiter.check_rate_limit(
                f"endpoint:{endpoint}:{user_id or client_ip}",
                endpoint_config["limit"],
                endpoint_config["window"]
            )
            
            if not endpoint_allowed:
                return {
                    "allowed": False,
                    "type": "endpoint",
                    "info": endpoint_info,
                    "message": f"接口 {endpoint} 请求频率过高，请稍后再试"
                }
        
        # 检查全局限流
        global_allowed, global_info = self.limiter.check_rate_limit(
            "global",
            limits["global"]["limit"],
            limits["global"]["window"]
        )
        
        if not global_allowed:
            return {
                "allowed": False,
                "type": "global",
                "info": global_info,
                "message": "系统繁忙，请稍后再试"
            }
        
        return {
            "allowed": True,
            "ip_info": ip_info,
            "user_info": user_info if user_id else None,
            "global_info": global_info
        }
    
    def _create_rate_limit_response(self, result: Dict[str, Any]) -> JSONResponse:
        """创建限流响应"""
        info = result["info"]
        
        headers = {
            "X-RateLimit-Limit": str(info["limit"]),
            "X-RateLimit-Remaining": str(info["remaining"]),
            "X-RateLimit-Reset": str(info["reset_time"]),
            "Retry-After": str(info.get("window_size", 60))
        }
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "code": 429,
                "message": result["message"],
                "data": {
                    "type": result["type"],
                    "limit": info["limit"],
                    "remaining": info["remaining"],
                    "reset_time": info["reset_time"]
                }
            },
            headers=headers
        )
    
    def _add_rate_limit_headers(self, response: Response, result: Dict[str, Any]):
        """添加限流头信息"""
        try:
            if "global_info" in result:
                info = result["global_info"]
                response.headers["X-RateLimit-Limit"] = str(info["limit"])
                response.headers["X-RateLimit-Remaining"] = str(info["remaining"])
                response.headers["X-RateLimit-Reset"] = str(info["reset_time"])
        except Exception as e:
            logger.error(f"添加限流头信息失败: {e}")


# 全局限流中间件实例
rate_limit_middleware = RateLimitMiddleware()
