"""add deep learning tables

Revision ID: 007
Revises: 006
Create Date: 2024-12-22 22:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建Transformer模型表
    op.create_table('transformer_models',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('model_type', sa.String(length=50), nullable=True),
        sa.Column('sequence_length', sa.Integer(), nullable=True),
        sa.Column('prediction_length', sa.Integer(), nullable=True),
        sa.Column('d_model', sa.Integer(), nullable=True),
        sa.Column('n_heads', sa.Integer(), nullable=True),
        sa.Column('n_layers', sa.Integer(), nullable=True),
        sa.Column('d_ff', sa.Integer(), nullable=True),
        sa.Column('dropout', sa.Float(), nullable=True),
        sa.Column('batch_size', sa.Integer(), nullable=True),
        sa.Column('learning_rate', sa.Float(), nullable=True),
        sa.Column('epochs', sa.Integer(), nullable=True),
        sa.Column('early_stopping_patience', sa.Integer(), nullable=True),
        sa.Column('target_columns', sa.JSON(), nullable=True),
        sa.Column('feature_columns', sa.JSON(), nullable=True),
        sa.Column('normalization_method', sa.String(length=20), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('training_progress', sa.Integer(), nullable=True),
        sa.Column('train_loss', sa.Float(), nullable=True),
        sa.Column('val_loss', sa.Float(), nullable=True),
        sa.Column('test_mse', sa.Float(), nullable=True),
        sa.Column('test_mae', sa.Float(), nullable=True),
        sa.Column('test_mape', sa.Float(), nullable=True),
        sa.Column('model_path', sa.String(length=500), nullable=True),
        sa.Column('model_size', sa.Integer(), nullable=True),
        sa.Column('checkpoint_path', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transformer_models_id'), 'transformer_models', ['id'], unique=False)
    op.create_index(op.f('ix_transformer_models_user_id'), 'transformer_models', ['user_id'], unique=False)

    # 创建Transformer预测结果表
    op.create_table('transformer_predictions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('prediction_date', sa.DateTime(), nullable=False),
        sa.Column('target_date', sa.DateTime(), nullable=False),
        sa.Column('predicted_values', sa.JSON(), nullable=True),
        sa.Column('confidence_intervals', sa.JSON(), nullable=True),
        sa.Column('attention_weights', sa.JSON(), nullable=True),
        sa.Column('actual_values', sa.JSON(), nullable=True),
        sa.Column('prediction_errors', sa.JSON(), nullable=True),
        sa.Column('input_sequence', sa.JSON(), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('evaluated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['transformer_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transformer_predictions_id'), 'transformer_predictions', ['id'], unique=False)
    op.create_index(op.f('ix_transformer_predictions_model_id'), 'transformer_predictions', ['model_id'], unique=False)
    op.create_index(op.f('ix_transformer_predictions_prediction_date'), 'transformer_predictions', ['prediction_date'], unique=False)
    op.create_index(op.f('ix_transformer_predictions_symbol'), 'transformer_predictions', ['symbol'], unique=False)
    op.create_index(op.f('ix_transformer_predictions_user_id'), 'transformer_predictions', ['user_id'], unique=False)

    # 创建GAN模型表
    op.create_table('gan_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('gan_type', sa.String(length=50), nullable=True),
        sa.Column('generator_architecture', sa.JSON(), nullable=True),
        sa.Column('generator_input_dim', sa.Integer(), nullable=True),
        sa.Column('generator_output_dim', sa.Integer(), nullable=True),
        sa.Column('discriminator_architecture', sa.JSON(), nullable=True),
        sa.Column('discriminator_input_dim', sa.Integer(), nullable=True),
        sa.Column('batch_size', sa.Integer(), nullable=True),
        sa.Column('learning_rate_g', sa.Float(), nullable=True),
        sa.Column('learning_rate_d', sa.Float(), nullable=True),
        sa.Column('beta1', sa.Float(), nullable=True),
        sa.Column('beta2', sa.Float(), nullable=True),
        sa.Column('epochs', sa.Integer(), nullable=True),
        sa.Column('loss_function', sa.String(length=20), nullable=True),
        sa.Column('gradient_penalty_weight', sa.Float(), nullable=True),
        sa.Column('data_columns', sa.JSON(), nullable=True),
        sa.Column('sequence_length', sa.Integer(), nullable=True),
        sa.Column('conditioning_variables', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('training_progress', sa.Integer(), nullable=True),
        sa.Column('generator_loss', sa.Float(), nullable=True),
        sa.Column('discriminator_loss', sa.Float(), nullable=True),
        sa.Column('inception_score', sa.Float(), nullable=True),
        sa.Column('fid_score', sa.Float(), nullable=True),
        sa.Column('generator_path', sa.String(length=500), nullable=True),
        sa.Column('discriminator_path', sa.String(length=500), nullable=True),
        sa.Column('model_size', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gan_models_id'), 'gan_models', ['id'], unique=False)
    op.create_index(op.f('ix_gan_models_user_id'), 'gan_models', ['user_id'], unique=False)

    # 创建GAN生成数据表
    op.create_table('gan_generated_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('generation_date', sa.DateTime(), nullable=False),
        sa.Column('generation_type', sa.String(length=50), nullable=True),
        sa.Column('noise_seed', sa.Integer(), nullable=True),
        sa.Column('conditioning_params', sa.JSON(), nullable=True),
        sa.Column('num_samples', sa.Integer(), nullable=True),
        sa.Column('generated_samples', sa.JSON(), nullable=True),
        sa.Column('quality_metrics', sa.JSON(), nullable=True),
        sa.Column('purpose', sa.String(length=50), nullable=True),
        sa.Column('is_validated', sa.Boolean(), nullable=True),
        sa.Column('validation_score', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['gan_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gan_generated_data_generation_date'), 'gan_generated_data', ['generation_date'], unique=False)
    op.create_index(op.f('ix_gan_generated_data_id'), 'gan_generated_data', ['id'], unique=False)
    op.create_index(op.f('ix_gan_generated_data_model_id'), 'gan_generated_data', ['model_id'], unique=False)
    op.create_index(op.f('ix_gan_generated_data_user_id'), 'gan_generated_data', ['user_id'], unique=False)

    # 创建强化学习智能体表
    op.create_table('rl_agents',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('agent_type', sa.String(length=50), nullable=True),
        sa.Column('environment_type', sa.String(length=50), nullable=True),
        sa.Column('state_space_dim', sa.Integer(), nullable=True),
        sa.Column('action_space_dim', sa.Integer(), nullable=True),
        sa.Column('action_space_type', sa.String(length=20), nullable=True),
        sa.Column('network_architecture', sa.JSON(), nullable=True),
        sa.Column('hidden_layers', sa.JSON(), nullable=True),
        sa.Column('activation_function', sa.String(length=20), nullable=True),
        sa.Column('learning_rate', sa.Float(), nullable=True),
        sa.Column('batch_size', sa.Integer(), nullable=True),
        sa.Column('memory_size', sa.Integer(), nullable=True),
        sa.Column('epsilon_start', sa.Float(), nullable=True),
        sa.Column('epsilon_end', sa.Float(), nullable=True),
        sa.Column('epsilon_decay', sa.Float(), nullable=True),
        sa.Column('gamma', sa.Float(), nullable=True),
        sa.Column('tau', sa.Float(), nullable=True),
        sa.Column('reward_function', sa.JSON(), nullable=True),
        sa.Column('risk_penalty_weight', sa.Float(), nullable=True),
        sa.Column('transaction_cost', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('training_episodes', sa.Integer(), nullable=True),
        sa.Column('total_steps', sa.Integer(), nullable=True),
        sa.Column('average_reward', sa.Float(), nullable=True),
        sa.Column('max_reward', sa.Float(), nullable=True),
        sa.Column('win_rate', sa.Float(), nullable=True),
        sa.Column('sharpe_ratio', sa.Float(), nullable=True),
        sa.Column('max_drawdown', sa.Float(), nullable=True),
        sa.Column('model_path', sa.String(length=500), nullable=True),
        sa.Column('checkpoint_path', sa.String(length=500), nullable=True),
        sa.Column('model_size', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_trained_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rl_agents_id'), 'rl_agents', ['id'], unique=False)
    op.create_index(op.f('ix_rl_agents_user_id'), 'rl_agents', ['user_id'], unique=False)

    # 创建强化学习交易会话表
    op.create_table('rl_trading_sessions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_name', sa.String(length=100), nullable=True),
        sa.Column('session_type', sa.String(length=20), nullable=True),
        sa.Column('initial_capital', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('symbols', sa.JSON(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('final_capital', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('total_return', sa.Float(), nullable=True),
        sa.Column('annual_return', sa.Float(), nullable=True),
        sa.Column('volatility', sa.Float(), nullable=True),
        sa.Column('sharpe_ratio', sa.Float(), nullable=True),
        sa.Column('max_drawdown', sa.Float(), nullable=True),
        sa.Column('total_trades', sa.Integer(), nullable=True),
        sa.Column('winning_trades', sa.Integer(), nullable=True),
        sa.Column('losing_trades', sa.Integer(), nullable=True),
        sa.Column('win_rate', sa.Float(), nullable=True),
        sa.Column('avg_trade_return', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('trade_history', sa.JSON(), nullable=True),
        sa.Column('portfolio_history', sa.JSON(), nullable=True),
        sa.Column('action_history', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['rl_agents.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rl_trading_sessions_agent_id'), 'rl_trading_sessions', ['agent_id'], unique=False)
    op.create_index(op.f('ix_rl_trading_sessions_id'), 'rl_trading_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_rl_trading_sessions_user_id'), 'rl_trading_sessions', ['user_id'], unique=False)

    # 创建强化学习训练回合表
    op.create_table('rl_training_episodes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('agent_id', sa.Integer(), nullable=False),
        sa.Column('episode_number', sa.Integer(), nullable=False),
        sa.Column('episode_steps', sa.Integer(), nullable=True),
        sa.Column('episode_reward', sa.Float(), nullable=True),
        sa.Column('loss', sa.Float(), nullable=True),
        sa.Column('epsilon', sa.Float(), nullable=True),
        sa.Column('learning_rate', sa.Float(), nullable=True),
        sa.Column('portfolio_value', sa.Float(), nullable=True),
        sa.Column('trades_count', sa.Integer(), nullable=True),
        sa.Column('win_rate', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['agent_id'], ['rl_agents.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rl_training_episodes_agent_id'), 'rl_training_episodes', ['agent_id'], unique=False)
    op.create_index(op.f('ix_rl_training_episodes_id'), 'rl_training_episodes', ['id'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_rl_training_episodes_id'), table_name='rl_training_episodes')
    op.drop_index(op.f('ix_rl_training_episodes_agent_id'), table_name='rl_training_episodes')
    op.drop_table('rl_training_episodes')
    
    op.drop_index(op.f('ix_rl_trading_sessions_user_id'), table_name='rl_trading_sessions')
    op.drop_index(op.f('ix_rl_trading_sessions_id'), table_name='rl_trading_sessions')
    op.drop_index(op.f('ix_rl_trading_sessions_agent_id'), table_name='rl_trading_sessions')
    op.drop_table('rl_trading_sessions')
    
    op.drop_index(op.f('ix_rl_agents_user_id'), table_name='rl_agents')
    op.drop_index(op.f('ix_rl_agents_id'), table_name='rl_agents')
    op.drop_table('rl_agents')
    
    op.drop_index(op.f('ix_gan_generated_data_user_id'), table_name='gan_generated_data')
    op.drop_index(op.f('ix_gan_generated_data_model_id'), table_name='gan_generated_data')
    op.drop_index(op.f('ix_gan_generated_data_id'), table_name='gan_generated_data')
    op.drop_index(op.f('ix_gan_generated_data_generation_date'), table_name='gan_generated_data')
    op.drop_table('gan_generated_data')
    
    op.drop_index(op.f('ix_gan_models_user_id'), table_name='gan_models')
    op.drop_index(op.f('ix_gan_models_id'), table_name='gan_models')
    op.drop_table('gan_models')
    
    op.drop_index(op.f('ix_transformer_predictions_user_id'), table_name='transformer_predictions')
    op.drop_index(op.f('ix_transformer_predictions_symbol'), table_name='transformer_predictions')
    op.drop_index(op.f('ix_transformer_predictions_prediction_date'), table_name='transformer_predictions')
    op.drop_index(op.f('ix_transformer_predictions_model_id'), table_name='transformer_predictions')
    op.drop_index(op.f('ix_transformer_predictions_id'), table_name='transformer_predictions')
    op.drop_table('transformer_predictions')
    
    op.drop_index(op.f('ix_transformer_models_user_id'), table_name='transformer_models')
    op.drop_index(op.f('ix_transformer_models_id'), table_name='transformer_models')
    op.drop_table('transformer_models')
