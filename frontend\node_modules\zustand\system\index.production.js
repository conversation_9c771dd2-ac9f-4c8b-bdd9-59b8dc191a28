System.register(["zustand/vanilla","react","use-sync-external-store/shim/with-selector"],function(c){"use strict";var l={create:1,default:1,useStore:1},o,i,f;return{setters:[function(t){o=t.createStore;var n={};for(var r in t)l[r]||(n[r]=t[r]);c(n)},function(t){i=t.default},function(t){f=t.default}],execute:function(){c("useStore",r);const{useDebugValue:t}=i,{useSyncExternalStoreWithSelector:n}=f;function r(e,s=e.getState,a){const u=n(e.subscribe,e.getState,e.getServerState||e.getState,s,a);return t(u),u}const S=e=>{const s=typeof e=="function"?o(e):e,a=(u,v)=>r(s,u,v);return Object.assign(a,s),a},g=c("create",e=>e?S(e):S);var b=c("default",e=>g(e))}}});
