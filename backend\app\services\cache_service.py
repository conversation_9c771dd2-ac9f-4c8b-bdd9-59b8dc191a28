"""
缓存服务模块

提供Redis缓存的统一接口，支持热门数据缓存和个性化数据管理
"""

import json
import pickle
from typing import Any, Dict, List, Optional, Union

from redis.asyncio import Redis

from app.core.config import settings
from app.core.database import get_redis
from app.core.logging import logger


class CacheService:
    """缓存服务类"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
        self.key_prefix = settings.CACHE_KEY_PREFIX
        self.default_ttl = settings.CACHE_TTL
    
    async def _get_redis(self) -> Optional[Redis]:
        """获取Redis连接，如果连接失败返回None"""
        if not self.redis:
            try:
                self.redis = await get_redis()
            except Exception as e:
                logger.warning(f"Redis连接失败，缓存功能将被禁用: {e}")
                return None
        return self.redis
    
    def _make_key(self, key: str) -> str:
        """生成缓存键"""
        return f"{self.key_prefix}:{key}"
    
    # =============================================================================
    # 基础缓存操作
    # =============================================================================
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存数据"""
        try:
            redis = await self._get_redis()
            if redis is None:
                logger.debug(f"Redis不可用，跳过缓存获取: {key}")
                return default

            cache_key = self._make_key(key)

            data = await redis.get(cache_key)
            if data is None:
                return default
            
            # 尝试JSON解析，失败则使用pickle
            try:
                return json.loads(data)
            except (json.JSONDecodeError, TypeError):
                return pickle.loads(data)
                
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        serialize_method: str = "json"
    ) -> bool:
        """设置缓存数据"""
        try:
            redis = await self._get_redis()
            if redis is None:
                logger.debug(f"Redis不可用，跳过缓存设置: {key}")
                return False

            cache_key = self._make_key(key)
            cache_ttl = ttl or self.default_ttl
            
            # 序列化数据
            if serialize_method == "json":
                try:
                    serialized_data = json.dumps(value, ensure_ascii=False, default=str)
                except (TypeError, ValueError):
                    # JSON序列化失败，使用pickle
                    serialized_data = pickle.dumps(value)
            else:
                serialized_data = pickle.dumps(value)
            
            # 设置缓存
            await redis.setex(cache_key, cache_ttl, serialized_data)
            return True
            
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            redis = await self._get_redis()
            cache_key = self._make_key(key)
            
            result = await redis.delete(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            redis = await self._get_redis()
            cache_key = self._make_key(key)
            
            result = await redis.exists(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置缓存过期时间"""
        try:
            redis = await self._get_redis()
            cache_key = self._make_key(key)
            
            result = await redis.expire(cache_key, ttl)
            return result
            
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        try:
            redis = await self._get_redis()
            cache_key = self._make_key(key)
            
            return await redis.ttl(cache_key)
            
        except Exception as e:
            logger.error(f"获取缓存TTL失败 {key}: {e}")
            return -1
    
    # =============================================================================
    # 批量操作
    # =============================================================================
    
    async def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存"""
        try:
            redis = await self._get_redis()
            cache_keys = [self._make_key(key) for key in keys]
            
            values = await redis.mget(cache_keys)
            result = {}
            
            for i, (key, value) in enumerate(zip(keys, values)):
                if value is not None:
                    try:
                        result[key] = json.loads(value)
                    except (json.JSONDecodeError, TypeError):
                        result[key] = pickle.loads(value)
                else:
                    result[key] = None
            
            return result
            
        except Exception as e:
            logger.error(f"批量获取缓存失败: {e}")
            return {key: None for key in keys}
    
    async def mset(self, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存"""
        try:
            redis = await self._get_redis()
            cache_ttl = ttl or self.default_ttl
            
            # 准备数据
            cache_data = {}
            for key, value in data.items():
                cache_key = self._make_key(key)
                try:
                    serialized_value = json.dumps(value, ensure_ascii=False, default=str)
                except (TypeError, ValueError):
                    serialized_value = pickle.dumps(value)
                cache_data[cache_key] = serialized_value
            
            # 批量设置
            await redis.mset(cache_data)
            
            # 设置过期时间
            if cache_ttl > 0:
                for cache_key in cache_data.keys():
                    await redis.expire(cache_key, cache_ttl)
            
            return True
            
        except Exception as e:
            logger.error(f"批量设置缓存失败: {e}")
            return False
    
    # =============================================================================
    # 专用缓存方法
    # =============================================================================
    
    async def cache_stock_list(self, market: str, data: List[Dict]) -> bool:
        """缓存股票列表"""
        key = f"stock_list:{market}"
        return await self.set(key, data, ttl=3600)  # 1小时
    
    async def get_cached_stock_list(self, market: str) -> Optional[List[Dict]]:
        """获取缓存的股票列表"""
        key = f"stock_list:{market}"
        return await self.get(key)
    
    async def cache_price_data(
        self, 
        symbols: List[str], 
        start_date: str, 
        end_date: str, 
        frequency: str,
        data: List[Dict]
    ) -> bool:
        """缓存价格数据"""
        key = f"price_data:{':'.join(sorted(symbols))}:{start_date}:{end_date}:{frequency}"
        ttl = 3600 if frequency == "daily" else 300  # 日线1小时，分钟线5分钟
        return await self.set(key, data, ttl=ttl)
    
    async def get_cached_price_data(
        self, 
        symbols: List[str], 
        start_date: str, 
        end_date: str, 
        frequency: str
    ) -> Optional[List[Dict]]:
        """获取缓存的价格数据"""
        key = f"price_data:{':'.join(sorted(symbols))}:{start_date}:{end_date}:{frequency}"
        return await self.get(key)
    
    async def cache_user_quota(self, user_id: int, quota_info: Dict) -> bool:
        """缓存用户配额信息"""
        key = f"user_quota:{user_id}"
        return await self.set(key, quota_info, ttl=300)  # 5分钟
    
    async def get_cached_user_quota(self, user_id: int) -> Optional[Dict]:
        """获取缓存的用户配额信息"""
        key = f"user_quota:{user_id}"
        return await self.get(key)
    
    # =============================================================================
    # 热门数据管理
    # =============================================================================
    
    async def add_to_hot_stocks(self, symbol: str, score: float = 1.0) -> bool:
        """添加到热门股票列表"""
        try:
            redis = await self._get_redis()
            key = self._make_key("hot_stocks")
            
            await redis.zadd(key, {symbol: score})
            await redis.expire(key, 86400)  # 24小时
            return True
            
        except Exception as e:
            logger.error(f"添加热门股票失败 {symbol}: {e}")
            return False
    
    async def get_hot_stocks(self, limit: int = 100) -> List[str]:
        """获取热门股票列表"""
        try:
            redis = await self._get_redis()
            key = self._make_key("hot_stocks")
            
            # 按分数降序获取
            stocks = await redis.zrevrange(key, 0, limit - 1)
            return [stock.decode() if isinstance(stock, bytes) else stock for stock in stocks]
            
        except Exception as e:
            logger.error(f"获取热门股票失败: {e}")
            return []
    
    # =============================================================================
    # 缓存统计和管理
    # =============================================================================
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            redis = await self._get_redis()
            
            # 获取Redis信息
            info = await redis.info()
            
            # 统计我们的缓存键
            pattern = f"{self.key_prefix}:*"
            keys = await redis.keys(pattern)
            
            return {
                "total_keys": len(keys),
                "memory_used": info.get("used_memory_human", "N/A"),
                "connected_clients": info.get("connected_clients", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0),
                    info.get("keyspace_misses", 0)
                )
            }
            
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    async def clear_cache_pattern(self, pattern: str) -> int:
        """清理匹配模式的缓存"""
        try:
            redis = await self._get_redis()
            full_pattern = f"{self.key_prefix}:{pattern}"
            
            keys = await redis.keys(full_pattern)
            if keys:
                deleted = await redis.delete(*keys)
                logger.info(f"清理缓存 {pattern}: 删除 {deleted} 个键")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error(f"清理缓存失败 {pattern}: {e}")
            return 0
    
    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """计算缓存命中率"""
        total = hits + misses
        if total == 0:
            return 0.0
        return round((hits / total) * 100, 2)
