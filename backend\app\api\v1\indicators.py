"""
技术指标相关API端点

提供技术指标计算、自定义指标管理等API接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import numpy as np

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.schemas.base import BaseResponse
from app.services.indicators_service import indicator_service

router = APIRouter()


@router.post("/calculate", response_model=BaseResponse[dict])
async def calculate_indicator(
    request_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """计算技术指标"""
    try:
        indicator_name = request_data.get("indicator")
        data = request_data.get("data", {})
        parameters = request_data.get("parameters", {})
        
        if not indicator_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少指标名称"
            )
        
        # 转换数据格式
        numpy_data = {}
        for key, values in data.items():
            if isinstance(values, list):
                numpy_data[key] = np.array(values, dtype=float)
            else:
                numpy_data[key] = values
        
        # 计算指标
        result = indicator_service.calculate_indicator(
            indicator_name, numpy_data, parameters
        )
        
        if result["success"]:
            return BaseResponse(
                code=200,
                message="指标计算成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "指标计算失败")
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"计算技术指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="计算技术指标失败"
        )


@router.post("/batch-calculate", response_model=BaseResponse[dict])
async def batch_calculate_indicators(
    request_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """批量计算多个技术指标"""
    try:
        indicators = request_data.get("indicators", [])
        data = request_data.get("data", {})
        
        if not indicators:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少指标配置"
            )
        
        # 转换数据格式
        numpy_data = {}
        for key, values in data.items():
            if isinstance(values, list):
                numpy_data[key] = np.array(values, dtype=float)
            else:
                numpy_data[key] = values
        
        # 批量计算指标
        results = {}
        for indicator_config in indicators:
            indicator_name = indicator_config.get("name")
            parameters = indicator_config.get("parameters", {})
            
            if indicator_name:
                result = indicator_service.calculate_indicator(
                    indicator_name, numpy_data, parameters
                )
                results[indicator_name] = result
        
        return BaseResponse(
            code=200,
            message="批量计算指标成功",
            data={"results": results}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量计算技术指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量计算技术指标失败"
        )


@router.get("/available", response_model=BaseResponse[dict])
async def get_available_indicators(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取可用指标列表"""
    try:
        indicators = indicator_service.get_available_indicators()
        
        return BaseResponse(
            code=200,
            message="获取可用指标列表成功",
            data=indicators
        )
        
    except Exception as e:
        logger.error(f"获取可用指标列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取可用指标列表失败"
        )


@router.post("/analyze", response_model=BaseResponse[dict])
async def analyze_stock_data(
    request_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """股票数据技术分析"""
    try:
        symbol = request_data.get("symbol")
        data = request_data.get("data", {})
        analysis_type = request_data.get("analysis_type", "comprehensive")
        
        if not symbol or not data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少股票代码或数据"
            )
        
        # 转换数据格式
        numpy_data = {}
        for key, values in data.items():
            if isinstance(values, list):
                numpy_data[key] = np.array(values, dtype=float)
            else:
                numpy_data[key] = values
        
        analysis_result = {}
        
        if analysis_type == "comprehensive" or analysis_type == "trend":
            # 趋势分析
            trend_indicators = [
                {"name": "sma", "parameters": {"period": 5}},
                {"name": "sma", "parameters": {"period": 20}},
                {"name": "ema", "parameters": {"period": 12}},
                {"name": "macd", "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9}},
                {"name": "bollinger_bands", "parameters": {"period": 20, "std_dev": 2}},
            ]
            
            trend_results = {}
            for indicator in trend_indicators:
                result = indicator_service.calculate_indicator(
                    indicator["name"], numpy_data, indicator["parameters"]
                )
                if result["success"]:
                    trend_results[f"{indicator['name']}_{indicator['parameters']}"] = result["data"]
            
            analysis_result["trend"] = trend_results
        
        if analysis_type == "comprehensive" or analysis_type == "momentum":
            # 动量分析
            momentum_indicators = [
                {"name": "rsi", "parameters": {"period": 14}},
                {"name": "stochastic", "parameters": {"k_period": 14, "d_period": 3}},
                {"name": "kdj", "parameters": {"period": 9}},
            ]
            
            momentum_results = {}
            for indicator in momentum_indicators:
                result = indicator_service.calculate_indicator(
                    indicator["name"], numpy_data, indicator["parameters"]
                )
                if result["success"]:
                    momentum_results[f"{indicator['name']}_{indicator['parameters']}"] = result["data"]
            
            analysis_result["momentum"] = momentum_results
        
        if analysis_type == "comprehensive" or analysis_type == "volume":
            # 成交量分析
            if "volume" in numpy_data:
                volume_indicators = [
                    {"name": "obv", "parameters": {}},
                    {"name": "vwap", "parameters": {}},
                ]
                
                volume_results = {}
                for indicator in volume_indicators:
                    result = indicator_service.calculate_indicator(
                        indicator["name"], numpy_data, indicator["parameters"]
                    )
                    if result["success"]:
                        volume_results[indicator["name"]] = result["data"]
                
                analysis_result["volume"] = volume_results
        
        # 生成分析总结
        summary = _generate_analysis_summary(numpy_data, analysis_result)
        analysis_result["summary"] = summary
        
        return BaseResponse(
            code=200,
            message="股票技术分析完成",
            data={
                "symbol": symbol,
                "analysis_type": analysis_type,
                "analysis": analysis_result
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"股票技术分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="股票技术分析失败"
        )


@router.post("/signals", response_model=BaseResponse[dict])
async def generate_trading_signals(
    request_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """生成交易信号"""
    try:
        symbol = request_data.get("symbol")
        data = request_data.get("data", {})
        signal_type = request_data.get("signal_type", "basic")
        
        if not symbol or not data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少股票代码或数据"
            )
        
        # 转换数据格式
        numpy_data = {}
        for key, values in data.items():
            if isinstance(values, list):
                numpy_data[key] = np.array(values, dtype=float)
            else:
                numpy_data[key] = values
        
        signals = []
        
        if signal_type == "basic" or signal_type == "ma_cross":
            # 移动平均交叉信号
            ma5_result = indicator_service.calculate_indicator("sma", numpy_data, {"period": 5})
            ma20_result = indicator_service.calculate_indicator("sma", numpy_data, {"period": 20})
            
            if ma5_result["success"] and ma20_result["success"]:
                ma5_data = np.array(ma5_result["data"]["sma"])
                ma20_data = np.array(ma20_result["data"]["sma"])
                
                # 检测金叉死叉
                for i in range(1, len(ma5_data)):
                    if not (np.isnan(ma5_data[i]) or np.isnan(ma20_data[i]) or 
                           np.isnan(ma5_data[i-1]) or np.isnan(ma20_data[i-1])):
                        
                        # 金叉：MA5上穿MA20
                        if ma5_data[i-1] <= ma20_data[i-1] and ma5_data[i] > ma20_data[i]:
                            signals.append({
                                "type": "buy",
                                "signal": "MA金叉",
                                "description": "MA5上穿MA20，买入信号",
                                "index": i,
                                "date": data.get("dates", [])[i] if i < len(data.get("dates", [])) else None,
                                "price": numpy_data["close"][i],
                                "strength": "medium"
                            })
                        
                        # 死叉：MA5下穿MA20
                        elif ma5_data[i-1] >= ma20_data[i-1] and ma5_data[i] < ma20_data[i]:
                            signals.append({
                                "type": "sell",
                                "signal": "MA死叉",
                                "description": "MA5下穿MA20，卖出信号",
                                "index": i,
                                "date": data.get("dates", [])[i] if i < len(data.get("dates", [])) else None,
                                "price": numpy_data["close"][i],
                                "strength": "medium"
                            })
        
        if signal_type == "basic" or signal_type == "rsi":
            # RSI超买超卖信号
            rsi_result = indicator_service.calculate_indicator("rsi", numpy_data, {"period": 14})
            
            if rsi_result["success"]:
                rsi_data = np.array(rsi_result["data"]["rsi"])
                
                for i in range(len(rsi_data)):
                    if not np.isnan(rsi_data[i]):
                        # 超卖信号
                        if rsi_data[i] < 30:
                            signals.append({
                                "type": "buy",
                                "signal": "RSI超卖",
                                "description": f"RSI={rsi_data[i]:.1f}，超卖区域，买入信号",
                                "index": i,
                                "date": data.get("dates", [])[i] if i < len(data.get("dates", [])) else None,
                                "price": numpy_data["close"][i],
                                "strength": "strong" if rsi_data[i] < 20 else "medium"
                            })
                        
                        # 超买信号
                        elif rsi_data[i] > 70:
                            signals.append({
                                "type": "sell",
                                "signal": "RSI超买",
                                "description": f"RSI={rsi_data[i]:.1f}，超买区域，卖出信号",
                                "index": i,
                                "date": data.get("dates", [])[i] if i < len(data.get("dates", [])) else None,
                                "price": numpy_data["close"][i],
                                "strength": "strong" if rsi_data[i] > 80 else "medium"
                            })
        
        # 按时间排序信号
        signals.sort(key=lambda x: x["index"])
        
        return BaseResponse(
            code=200,
            message="交易信号生成成功",
            data={
                "symbol": symbol,
                "signal_type": signal_type,
                "signals": signals[-20:],  # 返回最近20个信号
                "total_signals": len(signals)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成交易信号失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成交易信号失败"
        )


def _generate_analysis_summary(data: Dict[str, np.ndarray], analysis: Dict[str, Any]) -> Dict[str, str]:
    """生成分析总结"""
    summary = {}
    
    try:
        current_price = data["close"][-1]
        
        # 趋势分析总结
        if "trend" in analysis:
            trend_summary = "当前趋势: "
            
            # 检查移动平均线排列
            ma5_key = next((k for k in analysis["trend"].keys() if "sma_{'period': 5}" in k), None)
            ma20_key = next((k for k in analysis["trend"].keys() if "sma_{'period': 20}" in k), None)
            
            if ma5_key and ma20_key:
                ma5_data = analysis["trend"][ma5_key].get("sma", [])
                ma20_data = analysis["trend"][ma20_key].get("sma", [])
                
                if ma5_data and ma20_data:
                    ma5_current = ma5_data[-1] if not np.isnan(ma5_data[-1]) else None
                    ma20_current = ma20_data[-1] if not np.isnan(ma20_data[-1]) else None
                    
                    if ma5_current and ma20_current:
                        if current_price > ma5_current > ma20_current:
                            trend_summary += "多头排列，强势上涨"
                        elif current_price < ma5_current < ma20_current:
                            trend_summary += "空头排列，弱势下跌"
                        else:
                            trend_summary += "震荡整理"
            
            summary["trend"] = trend_summary
        
        # 动量分析总结
        if "momentum" in analysis:
            momentum_summary = "动量状态: "
            
            # 检查RSI
            rsi_key = next((k for k in analysis["momentum"].keys() if "rsi" in k), None)
            if rsi_key:
                rsi_data = analysis["momentum"][rsi_key].get("rsi", [])
                if rsi_data:
                    current_rsi = rsi_data[-1]
                    if not np.isnan(current_rsi):
                        if current_rsi > 70:
                            momentum_summary += f"超买(RSI={current_rsi:.1f})"
                        elif current_rsi < 30:
                            momentum_summary += f"超卖(RSI={current_rsi:.1f})"
                        else:
                            momentum_summary += f"正常(RSI={current_rsi:.1f})"
            
            summary["momentum"] = momentum_summary
        
        # 整体评价
        summary["overall"] = "综合分析: 请结合多个指标进行判断，注意风险控制。"
        
    except Exception as e:
        logger.error(f"生成分析总结失败: {e}")
        summary["error"] = "分析总结生成失败"
    
    return summary
