"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/overview/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/overview/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/overview/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OverviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RiseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nfunction OverviewPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isAuthenticated } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    if (!isAuthenticated) {\n        return null;\n    }\n    const stats = [\n        {\n            title: \"API配额\",\n            value: (user === null || user === void 0 ? void 0 : user.api_quota_used_today) || 0,\n            total: (user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000,\n            suffix: \"/ \".concat((user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, this),\n            color: \"#1890ff\"\n        },\n        {\n            title: \"投资组合价值\",\n            value: 0,\n            prefix: \"\\xa5\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            color: \"#52c41a\"\n        },\n        {\n            title: \"总收益率\",\n            value: 0,\n            suffix: \"%\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 13\n            }, this),\n            color: \"#faad14\"\n        },\n        {\n            title: \"活跃策略\",\n            value: 0,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            color: \"#722ed1\"\n        }\n    ];\n    const quickActions = [\n        {\n            title: \"配置JQData\",\n            description: \"设置API密钥以获取市场数据\",\n            action: ()=>router.push(\"/dashboard/settings/jqdata\"),\n            type: \"primary\"\n        },\n        {\n            title: \"创建策略\",\n            description: \"开始您的第一个量化策略\",\n            action: ()=>router.push(\"/dashboard/strategy/editor\"),\n            type: \"default\"\n        },\n        {\n            title: \"查看市场\",\n            description: \"浏览实时市场数据\",\n            action: ()=>router.push(\"/dashboard/market\"),\n            type: \"default\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            \"数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        className: \"text-lg\",\n                        children: [\n                            \"欢迎回来，\",\n                            user === null || user === void 0 ? void 0 : user.username,\n                            \"！这里是您的量化交易数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请先配置JQData账号以获取实时市场数据和使用量化功能\",\n                type: \"warning\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: \"small\",\n                    type: \"primary\",\n                    onClick: ()=>router.push(\"/dashboard/settings/jqdata\"),\n                    children: \"立即配置\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, void 0),\n                showIcon: true,\n                closable: true,\n                className: \"mb-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-8\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                title: stat.title,\n                                value: stat.value,\n                                precision: stat.precision,\n                                prefix: stat.prefix,\n                                suffix: stat.suffix,\n                                valueStyle: {\n                                    color: stat.color\n                                },\n                                prefix: stat.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"快速操作\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: \"small\",\n                                hoverable: true,\n                                className: \"text-center cursor-pointer\",\n                                onClick: action.action,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 4,\n                                        className: \"!mb-2\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        className: \"block mb-4\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        type: action.type,\n                                        children: \"开始使用\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"最近活动\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    description: \"暂无最近活动\",\n                    image: _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].PRESENTED_IMAGE_SIMPLE,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        type: \"primary\",\n                        onClick: ()=>router.push(\"/dashboard/strategy/editor\"),\n                        children: \"创建第一个策略\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"系统状态\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"后端服务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"正常运行\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"JQData连接\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"未配置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"数据库\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"连接正常\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(OverviewPage, \"yAZ73rp5Tgjs3hq8qIn2wTHK3JI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = OverviewPage;\nvar _c;\n$RefreshReg$(_c, \"OverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL292ZXJ2aWV3L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQVlyQztBQVFhO0FBQ2lCO0FBQ0E7QUFFNUMsTUFBTSxFQUFFZ0IsS0FBSyxFQUFFQyxJQUFJLEVBQUUsR0FBR2Isd0hBQVVBO0FBRW5CLFNBQVNjOztJQUN0QixNQUFNQyxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTSxFQUFFTSxJQUFJLEVBQUVDLGVBQWUsRUFBRSxHQUFHTix5REFBWUE7SUFFOUMsSUFBSSxDQUFDTSxpQkFBaUI7UUFDcEIsT0FBTztJQUNUO0lBRUEsTUFBTUMsUUFBUTtRQUNaO1lBQ0VDLE9BQU87WUFDUEMsT0FBT0osQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNSyxvQkFBb0IsS0FBSTtZQUNyQ0MsT0FBT04sQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNTyxlQUFlLEtBQUk7WUFDaENDLFFBQVEsS0FBbUMsT0FBOUJSLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTU8sZUFBZSxLQUFJO1lBQ3RDRSxvQkFBTSw4REFBQ2pCLDBKQUFXQTs7Ozs7WUFDbEJrQixPQUFPO1FBQ1Q7UUFDQTtZQUNFUCxPQUFPO1lBQ1BDLE9BQU87WUFDUE8sUUFBUTtZQUNSQyxXQUFXO1lBQ1hILG9CQUFNLDhEQUFDbEIsMEpBQWNBOzs7OztZQUNyQm1CLE9BQU87UUFDVDtRQUNBO1lBQ0VQLE9BQU87WUFDUEMsT0FBTztZQUNQSSxRQUFRO1lBQ1JJLFdBQVc7WUFDWEgsb0JBQU0sOERBQUNuQiwwSkFBWUE7Ozs7O1lBQ25Cb0IsT0FBTztRQUNUO1FBQ0E7WUFDRVAsT0FBTztZQUNQQyxPQUFPO1lBQ1BLLG9CQUFNLDhEQUFDaEIsMEpBQWdCQTs7Ozs7WUFDdkJpQixPQUFPO1FBQ1Q7S0FDRDtJQUVELE1BQU1HLGVBQWU7UUFDbkI7WUFDRVYsT0FBTztZQUNQVyxhQUFhO1lBQ2JDLFFBQVEsSUFBTWhCLE9BQU9pQixJQUFJLENBQUM7WUFDMUJDLE1BQU07UUFDUjtRQUNBO1lBQ0VkLE9BQU87WUFDUFcsYUFBYTtZQUNiQyxRQUFRLElBQU1oQixPQUFPaUIsSUFBSSxDQUFDO1lBQzFCQyxNQUFNO1FBQ1I7UUFDQTtZQUNFZCxPQUFPO1lBQ1BXLGFBQWE7WUFDYkMsUUFBUSxJQUFNaEIsT0FBT2lCLElBQUksQ0FBQztZQUMxQkMsTUFBTTtRQUNSO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3ZCO3dCQUFNd0IsT0FBTzt3QkFBR0QsV0FBVTs7MENBQ3pCLDhEQUFDOUIsMEpBQWlCQTtnQ0FBQzhCLFdBQVU7Ozs7Ozs0QkFBUzs7Ozs7OztrQ0FHeEMsOERBQUN0Qjt3QkFBS29CLE1BQUs7d0JBQVlFLFdBQVU7OzRCQUFVOzRCQUNuQ25CLGlCQUFBQSwyQkFBQUEsS0FBTXFCLFFBQVE7NEJBQUM7Ozs7Ozs7Ozs7Ozs7MEJBS3pCLDhEQUFDbEMseUhBQUtBO2dCQUNKbUMsU0FBUTtnQkFDUlIsYUFBWTtnQkFDWkcsTUFBSztnQkFDTEYsc0JBQ0UsOERBQUM3Qix5SEFBTUE7b0JBQ0xxQyxNQUFLO29CQUNMTixNQUFLO29CQUNMTyxTQUFTLElBQU16QixPQUFPaUIsSUFBSSxDQUFDOzhCQUM1Qjs7Ozs7O2dCQUlIUyxRQUFRO2dCQUNSQyxRQUFRO2dCQUNSUCxXQUFVOzs7Ozs7MEJBSVosOERBQUNyQyx5SEFBR0E7Z0JBQUM2QyxRQUFRO29CQUFDO29CQUFJO2lCQUFHO2dCQUFFUixXQUFVOzBCQUM5QmpCLE1BQU0wQixHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ2hCLDhEQUFDL0MseUhBQUdBO3dCQUFDZ0QsSUFBSTt3QkFBSUMsSUFBSTt3QkFBSUMsSUFBSTtrQ0FDdkIsNEVBQUNwRCx5SEFBSUE7c0NBQ0gsNEVBQUNJLHlIQUFTQTtnQ0FDUmtCLE9BQU8wQixLQUFLMUIsS0FBSztnQ0FDakJDLE9BQU95QixLQUFLekIsS0FBSztnQ0FDakJRLFdBQVdpQixLQUFLakIsU0FBUztnQ0FDekJELFFBQVFrQixLQUFLbEIsTUFBTTtnQ0FDbkJILFFBQVFxQixLQUFLckIsTUFBTTtnQ0FDbkIwQixZQUFZO29DQUFFeEIsT0FBT21CLEtBQUtuQixLQUFLO2dDQUFDO2dDQUNoQ0MsUUFBUWtCLEtBQUtwQixJQUFJOzs7Ozs7Ozs7Ozt1QkFUVXFCOzs7Ozs7Ozs7OzBCQWlCckMsOERBQUNqRCx5SEFBSUE7Z0JBQUNzQixPQUFNO2dCQUFPZ0IsV0FBVTswQkFDM0IsNEVBQUNyQyx5SEFBR0E7b0JBQUM2QyxRQUFRO3dCQUFDO3dCQUFJO3FCQUFHOzhCQUNsQmQsYUFBYWUsR0FBRyxDQUFDLENBQUNiLFFBQVFlLHNCQUN6Qiw4REFBQy9DLHlIQUFHQTs0QkFBQ2dELElBQUk7NEJBQUlDLElBQUk7c0NBQ2YsNEVBQUNuRCx5SEFBSUE7Z0NBQ0gwQyxNQUFLO2dDQUNMWSxTQUFTO2dDQUNUaEIsV0FBVTtnQ0FDVkssU0FBU1QsT0FBT0EsTUFBTTs7a0RBRXRCLDhEQUFDbkI7d0NBQU13QixPQUFPO3dDQUFHRCxXQUFVO2tEQUN4QkosT0FBT1osS0FBSzs7Ozs7O2tEQUVmLDhEQUFDTjt3Q0FBS29CLE1BQUs7d0NBQVlFLFdBQVU7a0RBQzlCSixPQUFPRCxXQUFXOzs7Ozs7a0RBRXJCLDhEQUFDNUIseUhBQU1BO3dDQUFDK0IsTUFBTUYsT0FBT0UsSUFBSTtrREFBRTs7Ozs7Ozs7Ozs7OzJCQWJOYTs7Ozs7Ozs7Ozs7Ozs7OzBCQXVCL0IsOERBQUNqRCx5SEFBSUE7Z0JBQUNzQixPQUFNO2dCQUFPZ0IsV0FBVTswQkFDM0IsNEVBQUMvQix5SEFBS0E7b0JBQ0owQixhQUFZO29CQUNac0IsT0FBT2hELHlIQUFLQSxDQUFDaUQsc0JBQXNCOzhCQUVuQyw0RUFBQ25ELHlIQUFNQTt3QkFDTCtCLE1BQUs7d0JBQ0xPLFNBQVMsSUFBTXpCLE9BQU9pQixJQUFJLENBQUM7a0NBQzVCOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU9MLDhEQUFDbkMseUhBQUlBO2dCQUFDc0IsT0FBTTswQkFDViw0RUFBQ3JCLHlIQUFHQTtvQkFBQzZDLFFBQVE7d0JBQUM7d0JBQUk7cUJBQUc7O3NDQUNuQiw4REFBQzVDLHlIQUFHQTs0QkFBQ2dELElBQUk7NEJBQUlDLElBQUk7c0NBQ2YsNEVBQUNkO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQStCOzs7Ozs7a0RBQzlDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBYzs7Ozs7O2tEQUM3Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHM0MsOERBQUNwQyx5SEFBR0E7NEJBQUNnRCxJQUFJOzRCQUFJQyxJQUFJO3NDQUNmLDRFQUFDZDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUE2Qjs7Ozs7O2tEQUM1Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQWM7Ozs7OztrREFDN0IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBRzNDLDhEQUFDcEMseUhBQUdBOzRCQUFDZ0QsSUFBSTs0QkFBSUMsSUFBSTtzQ0FDZiw0RUFBQ2Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBK0I7Ozs7OztrREFDOUMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFjOzs7Ozs7a0RBQzdCLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPckQ7R0F0THdCckI7O1FBQ1BKLHNEQUFTQTtRQUNVQyxxREFBWUE7OztLQUZ4QkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvb3ZlcnZpZXcvcGFnZS50c3g/ZWQxNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgQ2FyZCxcbiAgUm93LFxuICBDb2wsXG4gIFR5cG9ncmFwaHksXG4gIFN0YXRpc3RpYyxcbiAgQnV0dG9uLFxuICBTcGFjZSxcbiAgQWxlcnQsXG4gIEVtcHR5LFxuICBTcGluLFxufSBmcm9tICdhbnRkJztcbmltcG9ydCB7XG4gIERhc2hib2FyZE91dGxpbmVkLFxuICBSaXNlT3V0bGluZWQsXG4gIERvbGxhck91dGxpbmVkLFxuICBBcGlPdXRsaW5lZCxcbiAgQmFyQ2hhcnRPdXRsaW5lZCxcbiAgU2V0dGluZ091dGxpbmVkLFxufSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgdXNlQXV0aFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9hdXRoJztcblxuY29uc3QgeyBUaXRsZSwgVGV4dCB9ID0gVHlwb2dyYXBoeTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gT3ZlcnZpZXdQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyB1c2VyLCBpc0F1dGhlbnRpY2F0ZWQgfSA9IHVzZUF1dGhTdG9yZSgpO1xuXG4gIGlmICghaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICBjb25zdCBzdGF0cyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogJ0FQSemFjeminScsXG4gICAgICB2YWx1ZTogdXNlcj8uYXBpX3F1b3RhX3VzZWRfdG9kYXkgfHwgMCxcbiAgICAgIHRvdGFsOiB1c2VyPy5hcGlfcXVvdGFfZGFpbHkgfHwgMTAwMCxcbiAgICAgIHN1ZmZpeDogYC8gJHt1c2VyPy5hcGlfcXVvdGFfZGFpbHkgfHwgMTAwMH1gLFxuICAgICAgaWNvbjogPEFwaU91dGxpbmVkIC8+LFxuICAgICAgY29sb3I6ICcjMTg5MGZmJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5oqV6LWE57uE5ZCI5Lu35YC8JyxcbiAgICAgIHZhbHVlOiAwLFxuICAgICAgcHJlZml4OiAnwqUnLFxuICAgICAgcHJlY2lzaW9uOiAyLFxuICAgICAgaWNvbjogPERvbGxhck91dGxpbmVkIC8+LFxuICAgICAgY29sb3I6ICcjNTJjNDFhJyxcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5oC75pS255uK546HJyxcbiAgICAgIHZhbHVlOiAwLFxuICAgICAgc3VmZml4OiAnJScsXG4gICAgICBwcmVjaXNpb246IDIsXG4gICAgICBpY29uOiA8UmlzZU91dGxpbmVkIC8+LFxuICAgICAgY29sb3I6ICcjZmFhZDE0JyxcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5rS76LeD562W55WlJyxcbiAgICAgIHZhbHVlOiAwLFxuICAgICAgaWNvbjogPEJhckNoYXJ0T3V0bGluZWQgLz4sXG4gICAgICBjb2xvcjogJyM3MjJlZDEnLFxuICAgIH0sXG4gIF07XG5cbiAgY29uc3QgcXVpY2tBY3Rpb25zID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAn6YWN572uSlFEYXRhJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn6K6+572uQVBJ5a+G6ZKl5Lul6I635Y+W5biC5Zy65pWw5o2uJyxcbiAgICAgIGFjdGlvbjogKCkgPT4gcm91dGVyLnB1c2goJy9kYXNoYm9hcmQvc2V0dGluZ3MvanFkYXRhJyksXG4gICAgICB0eXBlOiAncHJpbWFyeScgYXMgY29uc3QsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+WIm+W7uuetlueVpScsXG4gICAgICBkZXNjcmlwdGlvbjogJ+W8gOWni+aCqOeahOesrOS4gOS4qumHj+WMluetlueVpScsXG4gICAgICBhY3Rpb246ICgpID0+IHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkL3N0cmF0ZWd5L2VkaXRvcicpLFxuICAgICAgdHlwZTogJ2RlZmF1bHQnIGFzIGNvbnN0LFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfmn6XnnIvluILlnLonLFxuICAgICAgZGVzY3JpcHRpb246ICfmtY/op4jlrp7ml7bluILlnLrmlbDmja4nLFxuICAgICAgYWN0aW9uOiAoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9tYXJrZXQnKSxcbiAgICAgIHR5cGU6ICdkZWZhdWx0JyBhcyBjb25zdCxcbiAgICB9LFxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgIHsvKiDpobXpnaLmoIfpopggKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgPFRpdGxlIGxldmVsPXsyfSBjbGFzc05hbWU9XCIhbWItMlwiPlxuICAgICAgICAgIDxEYXNoYm9hcmRPdXRsaW5lZCBjbGFzc05hbWU9XCJtci0zXCIgLz5cbiAgICAgICAgICDmlbDmja7mpoLop4hcbiAgICAgICAgPC9UaXRsZT5cbiAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cInRleHQtbGdcIj5cbiAgICAgICAgICDmrKLov47lm57mnaXvvIx7dXNlcj8udXNlcm5hbWV977yB6L+Z6YeM5piv5oKo55qE6YeP5YyW5Lqk5piT5pWw5o2u5qaC6KeIXG4gICAgICAgIDwvVGV4dD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSlFEYXRh6L+e5o6l54q25oCBICovfVxuICAgICAgPEFsZXJ0XG4gICAgICAgIG1lc3NhZ2U9XCJKUURhdGHmnKrphY3nva5cIlxuICAgICAgICBkZXNjcmlwdGlvbj1cIuivt+WFiOmFjee9rkpRRGF0Yei0puWPt+S7peiOt+WPluWunuaXtuW4guWcuuaVsOaNruWSjOS9v+eUqOmHj+WMluWKn+iDvVwiXG4gICAgICAgIHR5cGU9XCJ3YXJuaW5nXCJcbiAgICAgICAgYWN0aW9uPXtcbiAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCIgXG4gICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9zZXR0aW5ncy9qcWRhdGEnKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDnq4vljbPphY3nva5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgfVxuICAgICAgICBzaG93SWNvblxuICAgICAgICBjbG9zYWJsZVxuICAgICAgICBjbGFzc05hbWU9XCJtYi02XCJcbiAgICAgIC8+XG5cbiAgICAgIHsvKiDnu5/orqHljaHniYcgKi99XG4gICAgICA8Um93IGd1dHRlcj17WzE2LCAxNl19IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAge3N0YXRzLm1hcCgoc3RhdCwgaW5kZXgpID0+IChcbiAgICAgICAgICA8Q29sIHhzPXsyNH0gc209ezEyfSBsZz17Nn0ga2V5PXtpbmRleH0+XG4gICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgPFN0YXRpc3RpY1xuICAgICAgICAgICAgICAgIHRpdGxlPXtzdGF0LnRpdGxlfVxuICAgICAgICAgICAgICAgIHZhbHVlPXtzdGF0LnZhbHVlfVxuICAgICAgICAgICAgICAgIHByZWNpc2lvbj17c3RhdC5wcmVjaXNpb259XG4gICAgICAgICAgICAgICAgcHJlZml4PXtzdGF0LnByZWZpeH1cbiAgICAgICAgICAgICAgICBzdWZmaXg9e3N0YXQuc3VmZml4fVxuICAgICAgICAgICAgICAgIHZhbHVlU3R5bGU9e3sgY29sb3I6IHN0YXQuY29sb3IgfX1cbiAgICAgICAgICAgICAgICBwcmVmaXg9e3N0YXQuaWNvbn1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L0NvbD5cbiAgICAgICAgKSl9XG4gICAgICA8L1Jvdz5cblxuICAgICAgey8qIOW/q+mAn+aTjeS9nCAqL31cbiAgICAgIDxDYXJkIHRpdGxlPVwi5b+r6YCf5pON5L2cXCIgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICA8Um93IGd1dHRlcj17WzE2LCAxNl19PlxuICAgICAgICAgIHtxdWlja0FjdGlvbnMubWFwKChhY3Rpb24sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8Q29sIHhzPXsyNH0gc209ezh9IGtleT17aW5kZXh9PlxuICAgICAgICAgICAgICA8Q2FyZCBcbiAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIiBcbiAgICAgICAgICAgICAgICBob3ZlcmFibGVcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17YWN0aW9uLmFjdGlvbn1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxUaXRsZSBsZXZlbD17NH0gY2xhc3NOYW1lPVwiIW1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIHthY3Rpb24udGl0bGV9XG4gICAgICAgICAgICAgICAgPC9UaXRsZT5cbiAgICAgICAgICAgICAgICA8VGV4dCB0eXBlPVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwiYmxvY2sgbWItNFwiPlxuICAgICAgICAgICAgICAgICAge2FjdGlvbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPXthY3Rpb24udHlwZX0+XG4gICAgICAgICAgICAgICAgICDlvIDlp4vkvb/nlKhcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvUm93PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7Lyog5pyA6L+R5rS75YqoICovfVxuICAgICAgPENhcmQgdGl0bGU9XCLmnIDov5HmtLvliqhcIiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgIDxFbXB0eSBcbiAgICAgICAgICBkZXNjcmlwdGlvbj1cIuaaguaXoOacgOi/kea0u+WKqFwiXG4gICAgICAgICAgaW1hZ2U9e0VtcHR5LlBSRVNFTlRFRF9JTUFHRV9TSU1QTEV9XG4gICAgICAgID5cbiAgICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkL3N0cmF0ZWd5L2VkaXRvcicpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIOWIm+W7uuesrOS4gOS4quetlueVpVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L0VtcHR5PlxuICAgICAgPC9DYXJkPlxuXG4gICAgICB7Lyog57O757uf54q25oCBICovfVxuICAgICAgPENhcmQgdGl0bGU9XCLns7vnu5/nirbmgIFcIj5cbiAgICAgICAgPFJvdyBndXR0ZXI9e1sxNiwgMTZdfT5cbiAgICAgICAgICA8Q29sIHhzPXsyNH0gc209ezh9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTUwMCB0ZXh0LTJ4bCBtYi0yXCI+4pePPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7lkI7nq6/mnI3liqE8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7mraPluLjov5DooYw8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgIDxDb2wgeHM9ezI0fSBzbT17OH0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LTJ4bCBtYi0yXCI+4pePPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5KUURhdGHov57mjqU8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj7mnKrphY3nva48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgIDxDb2wgeHM9ezI0fSBzbT17OH0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNTAwIHRleHQtMnhsIG1iLTJcIj7il488L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPuaVsOaNruW6kzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPui/nuaOpeato+W4uDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9Db2w+XG4gICAgICAgIDwvUm93PlxuICAgICAgPC9DYXJkPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ2FyZCIsIlJvdyIsIkNvbCIsIlR5cG9ncmFwaHkiLCJTdGF0aXN0aWMiLCJCdXR0b24iLCJBbGVydCIsIkVtcHR5IiwiRGFzaGJvYXJkT3V0bGluZWQiLCJSaXNlT3V0bGluZWQiLCJEb2xsYXJPdXRsaW5lZCIsIkFwaU91dGxpbmVkIiwiQmFyQ2hhcnRPdXRsaW5lZCIsInVzZVJvdXRlciIsInVzZUF1dGhTdG9yZSIsIlRpdGxlIiwiVGV4dCIsIk92ZXJ2aWV3UGFnZSIsInJvdXRlciIsInVzZXIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJzdGF0cyIsInRpdGxlIiwidmFsdWUiLCJhcGlfcXVvdGFfdXNlZF90b2RheSIsInRvdGFsIiwiYXBpX3F1b3RhX2RhaWx5Iiwic3VmZml4IiwiaWNvbiIsImNvbG9yIiwicHJlZml4IiwicHJlY2lzaW9uIiwicXVpY2tBY3Rpb25zIiwiZGVzY3JpcHRpb24iLCJhY3Rpb24iLCJwdXNoIiwidHlwZSIsImRpdiIsImNsYXNzTmFtZSIsImxldmVsIiwidXNlcm5hbWUiLCJtZXNzYWdlIiwic2l6ZSIsIm9uQ2xpY2siLCJzaG93SWNvbiIsImNsb3NhYmxlIiwiZ3V0dGVyIiwibWFwIiwic3RhdCIsImluZGV4IiwieHMiLCJzbSIsImxnIiwidmFsdWVTdHlsZSIsImhvdmVyYWJsZSIsImltYWdlIiwiUFJFU0VOVEVEX0lNQUdFX1NJTVBMRSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/overview/page.tsx\n"));

/***/ })

});