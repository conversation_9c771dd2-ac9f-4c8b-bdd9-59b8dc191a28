"""
新闻情感分析服务

提供新闻数据采集、情感分析、实体提取等功能
"""

import re
import jieba
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import requests
import feedparser
from bs4 import BeautifulSoup
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

# 情感分析相关库
try:
    from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
    from snownlp import SnowNLP
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from app.core.logging import logger
from app.models.multimodal import NewsSource, NewsArticle, NewsSentiment, NewsEntityExtraction


class NewsCollector:
    """新闻采集器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def collect_rss_news(self, rss_url: str, max_articles: int = 100) -> List[Dict[str, Any]]:
        """采集RSS新闻"""
        try:
            feed = feedparser.parse(rss_url)
            articles = []
            
            for entry in feed.entries[:max_articles]:
                article = {
                    'title': entry.get('title', ''),
                    'content': self._extract_content(entry.get('link', '')),
                    'summary': entry.get('summary', ''),
                    'author': entry.get('author', ''),
                    'url': entry.get('link', ''),
                    'published_at': self._parse_datetime(entry.get('published', '')),
                    'external_id': entry.get('id', entry.get('link', ''))
                }
                articles.append(article)
            
            return articles
            
        except Exception as e:
            logger.error(f"RSS新闻采集失败: {e}")
            return []
    
    def collect_api_news(self, api_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """通过API采集新闻"""
        try:
            endpoint = api_config['endpoint']
            headers = api_config.get('headers', {})
            params = api_config.get('params', {})
            
            if api_config.get('api_key'):
                headers['Authorization'] = f"Bearer {api_config['api_key']}"
            
            response = self.session.get(endpoint, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            articles = []
            
            # 根据不同API格式解析数据
            if 'articles' in data:
                for item in data['articles']:
                    article = self._parse_api_article(item)
                    articles.append(article)
            
            return articles
            
        except Exception as e:
            logger.error(f"API新闻采集失败: {e}")
            return []
    
    def _extract_content(self, url: str) -> str:
        """提取网页内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取主要内容
            content_selectors = [
                'article', '.article-content', '.content', 
                '.post-content', '.entry-content', 'main'
            ]
            
            content = ""
            for selector in content_selectors:
                element = soup.select_one(selector)
                if element:
                    content = element.get_text(strip=True)
                    break
            
            if not content:
                content = soup.get_text(strip=True)
            
            return content[:5000]  # 限制长度
            
        except Exception as e:
            logger.error(f"内容提取失败: {e}")
            return ""
    
    def _parse_datetime(self, date_str: str) -> Optional[datetime]:
        """解析日期时间"""
        try:
            import dateutil.parser
            return dateutil.parser.parse(date_str)
        except:
            return datetime.utcnow()
    
    def _parse_api_article(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """解析API文章数据"""
        return {
            'title': item.get('title', ''),
            'content': item.get('content', item.get('description', '')),
            'summary': item.get('summary', item.get('description', '')),
            'author': item.get('author', ''),
            'url': item.get('url', item.get('link', '')),
            'published_at': self._parse_datetime(item.get('publishedAt', item.get('pubDate', ''))),
            'external_id': item.get('id', item.get('url', ''))
        }


class SentimentAnalyzer:
    """情感分析器"""
    
    def __init__(self):
        self.models = {}
        self._load_models()
    
    def _load_models(self):
        """加载情感分析模型"""
        try:
            if TRANSFORMERS_AVAILABLE:
                # 加载中文情感分析模型
                self.models['bert_chinese'] = pipeline(
                    "sentiment-analysis",
                    model="uer/roberta-base-finetuned-chinanews-chinese",
                    tokenizer="uer/roberta-base-finetuned-chinanews-chinese"
                )
                
                # 加载英文情感分析模型
                self.models['bert_english'] = pipeline(
                    "sentiment-analysis",
                    model="cardiffnlp/twitter-roberta-base-sentiment-latest"
                )
            
            logger.info("情感分析模型加载完成")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
    
    def analyze_sentiment(self, text: str, language: str = "zh") -> Dict[str, Any]:
        """分析文本情感"""
        try:
            results = {}
            
            # 使用SnowNLP进行中文情感分析
            if language == "zh":
                results.update(self._analyze_with_snownlp(text))
            
            # 使用BERT模型进行情感分析
            if TRANSFORMERS_AVAILABLE:
                if language == "zh" and "bert_chinese" in self.models:
                    bert_result = self._analyze_with_bert(text, "bert_chinese")
                    results.update(bert_result)
                elif language == "en" and "bert_english" in self.models:
                    bert_result = self._analyze_with_bert(text, "bert_english")
                    results.update(bert_result)
            
            # 使用词典方法进行情感分析
            lexicon_result = self._analyze_with_lexicon(text, language)
            results.update(lexicon_result)
            
            # 集成多种方法的结果
            final_result = self._ensemble_results(results)
            
            return final_result
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return {
                'sentiment_label': 'neutral',
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'method': 'fallback'
            }
    
    def _analyze_with_snownlp(self, text: str) -> Dict[str, Any]:
        """使用SnowNLP进行情感分析"""
        try:
            s = SnowNLP(text)
            sentiment_score = s.sentiments  # [0, 1]
            
            # 转换为[-1, 1]范围
            normalized_score = (sentiment_score - 0.5) * 2
            
            if sentiment_score > 0.6:
                label = "positive"
            elif sentiment_score < 0.4:
                label = "negative"
            else:
                label = "neutral"
            
            return {
                'snownlp_score': normalized_score,
                'snownlp_label': label,
                'snownlp_confidence': abs(normalized_score)
            }
            
        except Exception as e:
            logger.error(f"SnowNLP分析失败: {e}")
            return {}
    
    def _analyze_with_bert(self, text: str, model_name: str) -> Dict[str, Any]:
        """使用BERT模型进行情感分析"""
        try:
            model = self.models[model_name]
            
            # 限制文本长度
            text = text[:512]
            
            result = model(text)[0]
            label = result['label'].lower()
            score = result['score']
            
            # 标准化标签
            if label in ['positive', 'pos', 'label_2']:
                sentiment_label = 'positive'
                sentiment_score = score
            elif label in ['negative', 'neg', 'label_0']:
                sentiment_label = 'negative'
                sentiment_score = -score
            else:
                sentiment_label = 'neutral'
                sentiment_score = 0.0
            
            return {
                f'{model_name}_score': sentiment_score,
                f'{model_name}_label': sentiment_label,
                f'{model_name}_confidence': score
            }
            
        except Exception as e:
            logger.error(f"BERT分析失败: {e}")
            return {}
    
    def _analyze_with_lexicon(self, text: str, language: str) -> Dict[str, Any]:
        """使用词典方法进行情感分析"""
        try:
            # 简化的词典方法
            positive_words = {
                'zh': ['好', '棒', '优秀', '上涨', '增长', '利好', '看好', '推荐', '买入'],
                'en': ['good', 'great', 'excellent', 'positive', 'buy', 'bullish', 'up', 'rise']
            }
            
            negative_words = {
                'zh': ['坏', '差', '下跌', '下降', '利空', '看空', '卖出', '风险', '亏损'],
                'en': ['bad', 'poor', 'negative', 'sell', 'bearish', 'down', 'fall', 'risk']
            }
            
            words = positive_words.get(language, positive_words['en'])
            neg_words = negative_words.get(language, negative_words['en'])
            
            # 分词
            if language == 'zh':
                tokens = list(jieba.cut(text.lower()))
            else:
                tokens = text.lower().split()
            
            pos_count = sum(1 for word in tokens if word in words)
            neg_count = sum(1 for word in tokens if word in neg_words)
            
            total_sentiment_words = pos_count + neg_count
            if total_sentiment_words == 0:
                sentiment_score = 0.0
                sentiment_label = 'neutral'
                confidence = 0.0
            else:
                sentiment_score = (pos_count - neg_count) / len(tokens)
                if sentiment_score > 0.01:
                    sentiment_label = 'positive'
                elif sentiment_score < -0.01:
                    sentiment_label = 'negative'
                else:
                    sentiment_label = 'neutral'
                confidence = total_sentiment_words / len(tokens)
            
            return {
                'lexicon_score': sentiment_score,
                'lexicon_label': sentiment_label,
                'lexicon_confidence': confidence
            }
            
        except Exception as e:
            logger.error(f"词典分析失败: {e}")
            return {}
    
    def _ensemble_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """集成多种方法的结果"""
        try:
            scores = []
            confidences = []
            labels = []
            
            # 收集所有方法的结果
            for key, value in results.items():
                if key.endswith('_score') and isinstance(value, (int, float)):
                    scores.append(value)
                elif key.endswith('_confidence') and isinstance(value, (int, float)):
                    confidences.append(value)
                elif key.endswith('_label') and isinstance(value, str):
                    labels.append(value)
            
            if not scores:
                return {
                    'sentiment_label': 'neutral',
                    'sentiment_score': 0.0,
                    'confidence': 0.0,
                    'method': 'ensemble'
                }
            
            # 加权平均
            weights = confidences if confidences else [1.0] * len(scores)
            weighted_score = np.average(scores, weights=weights)
            avg_confidence = np.mean(confidences) if confidences else 0.5
            
            # 确定最终标签
            if weighted_score > 0.1:
                final_label = 'positive'
            elif weighted_score < -0.1:
                final_label = 'negative'
            else:
                final_label = 'neutral'
            
            return {
                'sentiment_label': final_label,
                'sentiment_score': float(weighted_score),
                'confidence': float(avg_confidence),
                'method': 'ensemble',
                'component_results': results
            }
            
        except Exception as e:
            logger.error(f"结果集成失败: {e}")
            return {
                'sentiment_label': 'neutral',
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'method': 'ensemble'
            }


class EntityExtractor:
    """实体提取器"""
    
    def __init__(self):
        self.stock_patterns = self._load_stock_patterns()
        self.company_names = self._load_company_names()
    
    def _load_stock_patterns(self) -> List[str]:
        """加载股票代码模式"""
        return [
            r'\d{6}\.(SH|XSHG)',  # 上海股票
            r'\d{6}\.(SZ|XSHE)',  # 深圳股票
            r'[A-Z]{1,5}',        # 美股代码
        ]
    
    def _load_company_names(self) -> Dict[str, str]:
        """加载公司名称映射"""
        # 这里应该从数据库或文件加载真实的公司名称映射
        return {
            '平安银行': '000001.XSHE',
            '万科A': '000002.XSHE',
            '中国平安': '601318.XSHG',
            '贵州茅台': '600519.XSHG',
            '招商银行': '600036.XSHG',
        }
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """提取实体"""
        try:
            entities = []
            
            # 提取股票代码
            stock_entities = self._extract_stock_codes(text)
            entities.extend(stock_entities)
            
            # 提取公司名称
            company_entities = self._extract_company_names(text)
            entities.extend(company_entities)
            
            # 提取数字和金额
            number_entities = self._extract_numbers(text)
            entities.extend(number_entities)
            
            # 提取时间
            time_entities = self._extract_time_expressions(text)
            entities.extend(time_entities)
            
            return entities
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return []
    
    def _extract_stock_codes(self, text: str) -> List[Dict[str, Any]]:
        """提取股票代码"""
        entities = []
        
        for pattern in self.stock_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    'entity_text': match.group(),
                    'entity_type': 'STOCK',
                    'entity_category': 'stock_code',
                    'start_position': match.start(),
                    'end_position': match.end(),
                    'confidence': 0.9,
                    'normalized_name': match.group(),
                    'entity_id': match.group()
                })
        
        return entities
    
    def _extract_company_names(self, text: str) -> List[Dict[str, Any]]:
        """提取公司名称"""
        entities = []
        
        for company_name, stock_code in self.company_names.items():
            if company_name in text:
                start_pos = text.find(company_name)
                entities.append({
                    'entity_text': company_name,
                    'entity_type': 'ORG',
                    'entity_category': 'company',
                    'start_position': start_pos,
                    'end_position': start_pos + len(company_name),
                    'confidence': 0.8,
                    'normalized_name': company_name,
                    'entity_id': stock_code
                })
        
        return entities
    
    def _extract_numbers(self, text: str) -> List[Dict[str, Any]]:
        """提取数字和金额"""
        entities = []
        
        # 金额模式
        money_patterns = [
            r'(\d+(?:\.\d+)?)\s*(?:万|千万|亿|万亿)?\s*(?:元|美元|港元)',
            r'(\d+(?:\.\d+)?)\s*(?:万|千万|亿|万亿)',
        ]
        
        for pattern in money_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    'entity_text': match.group(),
                    'entity_type': 'MONEY',
                    'entity_category': 'amount',
                    'start_position': match.start(),
                    'end_position': match.end(),
                    'confidence': 0.7,
                    'normalized_name': match.group(1)
                })
        
        return entities
    
    def _extract_time_expressions(self, text: str) -> List[Dict[str, Any]]:
        """提取时间表达式"""
        entities = []
        
        time_patterns = [
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'今天|明天|昨天|本周|下周|本月|下月|今年|明年',
        ]
        
        for pattern in time_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                entities.append({
                    'entity_text': match.group(),
                    'entity_type': 'TIME',
                    'entity_category': 'date',
                    'start_position': match.start(),
                    'end_position': match.end(),
                    'confidence': 0.8,
                    'normalized_name': match.group()
                })
        
        return entities


class NewsSentimentService:
    """新闻情感分析服务"""
    
    def __init__(self):
        self.collector = NewsCollector()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.entity_extractor = EntityExtractor()
    
    async def collect_and_analyze_news(
        self,
        source_id: int,
        db: AsyncSession,
        max_articles: int = 100
    ) -> Dict[str, Any]:
        """采集和分析新闻"""
        try:
            # 获取数据源配置
            result = await db.execute(
                select(NewsSource).where(NewsSource.id == source_id)
            )
            source = result.scalar_one_or_none()
            
            if not source or not source.is_active:
                return {"success": False, "error": "数据源不存在或未激活"}
            
            # 采集新闻
            if source.data_format == "rss":
                articles_data = self.collector.collect_rss_news(
                    source.api_endpoint, max_articles
                )
            else:
                api_config = {
                    'endpoint': source.api_endpoint,
                    'api_key': None,  # 应该从配置中获取
                    'params': {'pageSize': max_articles}
                }
                articles_data = self.collector.collect_api_news(api_config)
            
            processed_count = 0
            
            for article_data in articles_data:
                # 检查文章是否已存在
                existing = await db.execute(
                    select(NewsArticle).where(
                        and_(
                            NewsArticle.source_id == source_id,
                            NewsArticle.external_id == article_data['external_id']
                        )
                    )
                )
                
                if existing.scalar_one_or_none():
                    continue
                
                # 创建新闻文章记录
                article = NewsArticle(
                    source_id=source_id,
                    title=article_data['title'],
                    content=article_data['content'],
                    summary=article_data['summary'],
                    author=article_data['author'],
                    url=article_data['url'],
                    published_at=article_data['published_at'],
                    external_id=article_data['external_id'],
                    word_count=len(article_data['content']),
                    processing_status="processing"
                )
                
                db.add(article)
                await db.flush()  # 获取ID
                
                # 进行情感分析
                await self._analyze_article_sentiment(article, db)
                
                # 进行实体提取
                await self._extract_article_entities(article, db)
                
                # 更新处理状态
                article.processing_status = "completed"
                article.is_processed = True
                
                processed_count += 1
            
            # 更新数据源统计
            source.total_articles += processed_count
            source.last_updated = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "collected_articles": len(articles_data),
                "processed_articles": processed_count,
                "source_name": source.name
            }
            
        except Exception as e:
            logger.error(f"新闻采集和分析失败: {e}")
            await db.rollback()
            return {"success": False, "error": str(e)}
    
    async def _analyze_article_sentiment(self, article: NewsArticle, db: AsyncSession):
        """分析文章情感"""
        try:
            # 分析标题和内容
            title_sentiment = self.sentiment_analyzer.analyze_sentiment(
                article.title, article.language
            )
            
            content_sentiment = self.sentiment_analyzer.analyze_sentiment(
                article.content[:1000], article.language  # 限制长度
            )
            
            # 综合情感分析结果
            combined_score = (title_sentiment['sentiment_score'] * 0.3 + 
                            content_sentiment['sentiment_score'] * 0.7)
            
            combined_confidence = (title_sentiment['confidence'] * 0.3 + 
                                 content_sentiment['confidence'] * 0.7)
            
            if combined_score > 0.1:
                final_label = 'positive'
            elif combined_score < -0.1:
                final_label = 'negative'
            else:
                final_label = 'neutral'
            
            # 保存情感分析结果
            sentiment = NewsSentiment(
                article_id=article.id,
                sentiment_label=final_label,
                sentiment_score=combined_score,
                confidence=combined_confidence,
                emotions={
                    'title_sentiment': title_sentiment,
                    'content_sentiment': content_sentiment
                },
                model_name="ensemble",
                model_version="1.0",
                analysis_method="ensemble"
            )
            
            db.add(sentiment)
            
        except Exception as e:
            logger.error(f"文章情感分析失败: {e}")
    
    async def _extract_article_entities(self, article: NewsArticle, db: AsyncSession):
        """提取文章实体"""
        try:
            # 从标题和内容中提取实体
            title_entities = self.entity_extractor.extract_entities(article.title)
            content_entities = self.entity_extractor.extract_entities(article.content[:2000])
            
            all_entities = title_entities + content_entities
            
            # 去重和过滤
            unique_entities = {}
            for entity in all_entities:
                key = (entity['entity_text'], entity['entity_type'])
                if key not in unique_entities or entity['confidence'] > unique_entities[key]['confidence']:
                    unique_entities[key] = entity
            
            # 保存实体提取结果
            for entity in unique_entities.values():
                entity_record = NewsEntityExtraction(
                    article_id=article.id,
                    entity_text=entity['entity_text'],
                    entity_type=entity['entity_type'],
                    entity_category=entity.get('entity_category', ''),
                    start_position=entity.get('start_position', 0),
                    end_position=entity.get('end_position', 0),
                    confidence=entity['confidence'],
                    normalized_name=entity.get('normalized_name', entity['entity_text']),
                    entity_id=entity.get('entity_id', ''),
                    extraction_model="rule_based",
                    model_version="1.0"
                )
                
                db.add(entity_record)
            
            # 更新文章的相关股票
            stock_entities = [e for e in unique_entities.values() if e['entity_type'] in ['STOCK', 'ORG']]
            if stock_entities:
                related_symbols = [e.get('entity_id', '') for e in stock_entities if e.get('entity_id')]
                article.related_symbols = related_symbols
            
        except Exception as e:
            logger.error(f"文章实体提取失败: {e}")


# 全局新闻情感分析服务实例
news_sentiment_service = NewsSentimentService()
