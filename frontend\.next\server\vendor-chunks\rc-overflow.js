"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-overflow";
exports.ids = ["vendor-chunks/rc-overflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-overflow/es/Item.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-overflow/es/Item.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n\n\n\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\n\n\n\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(!invalidate && prefixCls, className),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalItem);\nItem.displayName = 'Item';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Item);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/Overflow.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-overflow/es/Overflow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\");\n/* harmony import */ var _RawItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RawItem */ \"(ssr)/./node_modules/rc-overflow/es/RawItem.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\n\n\n\n\n\n\n\n\n\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\n\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__.useBatcher)();\n  var _useEffectState = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, null),\n    _useEffectState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, new Map()),\n    _useEffectState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n      key: key,\n      value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n    value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = _RawItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardOverflow);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Overflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/RawItem.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/RawItem.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\n\n\n\n\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext);\n\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(context, _excluded2);\n  var className = props.className,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded3);\n\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Item__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RawItem);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/RawItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/context.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* binding */ OverflowContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OverflowContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEI7QUFDbkIsbUNBQW1DLDBEQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9jb250ZXh0LmpzP2Y1MzQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgT3ZlcmZsb3dDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/channelUpdate.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ channelUpdate)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n\nfunction channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvY2hhbm5lbFVwZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsSUFBSSwwREFBRztBQUNQLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9ob29rcy9jaGFubmVsVXBkYXRlLmpzPzg4MGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJhZiBmcm9tIFwicmMtdXRpbC9lcy9yYWZcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNoYW5uZWxVcGRhdGUoY2FsbGJhY2spIHtcbiAgaWYgKHR5cGVvZiBNZXNzYWdlQ2hhbm5lbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByYWYoY2FsbGJhY2spO1xuICB9IGVsc2Uge1xuICAgIHZhciBjaGFubmVsID0gbmV3IE1lc3NhZ2VDaGFubmVsKCk7XG4gICAgY2hhbm5lbC5wb3J0MS5vbm1lc3NhZ2UgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICB9O1xuICAgIGNoYW5uZWwucG9ydDIucG9zdE1lc3NhZ2UodW5kZWZpbmVkKTtcbiAgfVxufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState),\n/* harmony export */   useBatcher: () => (/* binding */ useBatcher)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _channelUpdate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./channelUpdate */ \"(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\");\n\n\n\n\n\n/**\n * Batcher for record any `useEffectState` need update.\n */\nfunction useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      (0,_channelUpdate__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_batchedUpdates)(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nfunction useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(defaultValue),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-overflow/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Overflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Overflow */ \"(ssr)/./node_modules/rc-overflow/es/Overflow.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Overflow__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qcWRhdGEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanM/YWYzMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgT3ZlcmZsb3cgZnJvbSBcIi4vT3ZlcmZsb3dcIjtcbmV4cG9ydCBkZWZhdWx0IE92ZXJmbG93OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/index.js\n");

/***/ })

};
;