# ============================================================================
# JQData量化数据平台 - Python依赖库
# ============================================================================

# 核心Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
starlette==0.27.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis缓存
redis==5.0.1
aioredis==2.0.1

# JQData SDK
jqdatasdk==1.8.11

# 数据处理和分析
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 技术指标库
TA-Lib==0.4.28

# 机器学习 (可选)
scikit-learn==1.3.2
joblib==1.3.2

# 任务调度和队列
apscheduler==3.10.0
celery[redis]==5.3.4

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 认证与安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# 日志记录
loguru==0.7.2
structlog==23.2.0

# 系统监控
prometheus-client==0.19.0
psutil==5.9.6

# 工具库
python-dateutil==2.8.2
pytz==2023.3
schedule==1.2.0

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# 数据验证
email-validator==2.1.0

# 邮件服务
fastapi-mail==1.4.1

# ============================================================================
# 开发依赖 (可选安装)
# ============================================================================
# 安装命令: pip install -r requirements-dev.txt

# 测试框架
# pytest==7.4.3
# pytest-asyncio==0.21.1
# pytest-cov==4.1.0
# pytest-mock==3.12.0
# factory-boy==3.3.0

# 代码质量工具
# black==23.11.0
# isort==5.12.0
# flake8==6.1.0
# mypy==1.7.1
# bandit==1.7.5
# pre-commit==3.6.0
# autoflake==2.2.1

# 性能分析工具
# py-spy==0.3.14
# memory-profiler==0.61.0

# 开发工具
# ipython==8.17.2
# jupyter==1.0.0

# ============================================================================
# 高级功能依赖 (可选安装)
# ============================================================================

# 深度学习框架 (大型依赖，按需安装)
# tensorflow==2.13.0
# torch==2.0.0
# torchvision==0.15.0

# 高级数据处理
# polars==0.19.19
# statsmodels==0.14.0

# 机器学习扩展
# xgboost==2.0.2
# lightgbm==4.1.0
# catboost==1.2.2
# optuna==3.4.0

# 任务监控
# flower==2.0.1

# ============================================================================
# 安装说明
# ============================================================================
#
# 基础安装:
#   pip install -r requirements.txt
#
# 开发环境安装:
#   pip install -r requirements.txt -r requirements-dev.txt
#
# 完整功能安装:
#   pip install -r requirements.txt -r requirements-full.txt
#
# 注意事项:
# 1. TA-Lib需要先安装C++依赖，Windows用户建议使用预编译包
# 2. 深度学习框架体积较大，建议按需安装
# 3. JQData SDK需要有效的JQData账号才能使用
# ============================================================================
