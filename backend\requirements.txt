# 核心Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
aioredis==2.0.1

# 数据处理
pandas>=2.1.0
numpy>=1.26.0

# 机器学习
scikit-learn>=1.3.0
joblib>=1.3.0
scipy>=1.11.0

# 任务调度
apscheduler>=3.10.0

# HTTP客户端
aiohttp>=3.8.0
httpx>=0.24.0

# 认证与安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 日志
loguru==0.7.2

# 监控
prometheus-client==0.19.0
psutil==5.9.6

# 工具库
python-dateutil==2.8.2
pytz==2023.3

# 文件处理
openpyxl==3.1.2

# 数据验证
email-validator==2.1.0
