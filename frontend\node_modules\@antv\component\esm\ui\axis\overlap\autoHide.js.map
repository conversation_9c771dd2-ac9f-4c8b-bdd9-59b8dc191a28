{"version": 3, "file": "autoHide.js", "sourceRoot": "", "sources": ["../../../../src/ui/axis/overlap/autoHide.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAElE,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAM1C,IAAM,OAAO,GAA2E;IACtF,MAAM,EAAE,UAAC,KAAsB,EAAE,EAAW;YAAT,WAAO,EAAP,GAAG,mBAAG,CAAC,KAAA;QAAO,OAAA,KAAK,CAAC,MAAM,CAAC,UAAC,IAAI,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAtC,CAAsC,CAAC;IAAjE,CAAiE;CACnH,CAAC;AAEF,IAAM,aAAa,GAAG,UAAC,GAAU,IAAK,OAAA,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAnB,CAAmB,CAAC;AAE1D,MAAM,CAAC,OAAO,UAAU,UAAU,CAChC,MAAuB,EACvB,UAA0B,EAC1B,IAA8B,EAC9B,KAAY;IAEZ,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IACpB,IAAA,UAAU,GAAe,UAAU,WAAzB,EAAE,QAAQ,GAAK,UAAU,SAAf,CAAgB;IAE5C,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,IAAI,QAAQ,CAAC;QAAE,OAAO;IAElE,IAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAClC,IAAM,KAAK,GAAG,UAAC,GAAoB,IAAK,OAAA,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,EAA9B,CAA8B,CAAC;IACvE,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAC9B,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAE5B,IAAM,aAAa,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,iBAAK,CAAC,UAAK,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,EAAjB,CAAiB,CAAC,UAAC,CAAC;IAE3E,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QAC/E,IAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1C,IAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACnD,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5C,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAED,IAAI,KAAgC,CAAC;IACrC,IAAI,IAA+B,CAAC;IAEpC,IAAI,UAAU;QAAE,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,CAAC;IAEd,OACE,GAAG,GAAG,MAAM,CAAC,MAAM;QACnB,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,8BAAE,IAAI,UAAK,MAAM,YAAE,KAAK,UAAE,CAAC,gBAAE,KAAK,UAAK,MAAM,SAAC,CAAC,EAAE,IAAI,EAAE,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,CAAC,CAAC,MAAM,EAC/G,CAAC;QACD,0BAA0B;QAC1B,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpC,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,+BAA+B;YAC/B,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;QAC5C,GAAG,EAAE,CAAC;IACR,CAAC;AACH,CAAC"}