"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/overview/page",{

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Looper.js":
/*!*******************************************************!*\
  !*** ./node_modules/antd/es/spin/Indicator/Looper.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Looper; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Progress__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Progress */ \"(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Progress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Looper(props) {\n    const { prefixCls, percent = 0 } = props;\n    const dotClassName = \"\".concat(prefixCls, \"-dot\");\n    const holderClassName = \"\".concat(dotClassName, \"-holder\");\n    const hideClassName = \"\".concat(holderClassName, \"-hidden\");\n    // ===================== Render =====================\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(holderClassName, percent > 0 && hideClassName)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(dotClassName, \"\".concat(prefixCls, \"-dot-spin\"))\n    }, [\n        1,\n        2,\n        3,\n        4\n    ].map((i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"i\", {\n            className: \"\".concat(prefixCls, \"-dot-item\"),\n            key: i\n        })))), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Progress__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        prefixCls: prefixCls,\n        percent: percent\n    }));\n}\n_c = Looper;\nvar _c;\n$RefreshReg$(_c, \"Looper\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Looper.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Progress.js":
/*!*********************************************************!*\
  !*** ./node_modules/antd/es/spin/Indicator/Progress.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(app-pages-browser)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\nconst viewSize = 100;\nconst borderWidth = viewSize / 5;\nconst radius = viewSize / 2 - borderWidth / 2;\nconst circumference = radius * 2 * Math.PI;\nconst position = 50;\nconst CustomCircle = (props)=>{\n    const { dotClassName, style, hasCircleCls } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"circle\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(dotClassName, \"-circle\"), {\n            [\"\".concat(dotClassName, \"-circle-bg\")]: hasCircleCls\n        }),\n        r: radius,\n        cx: position,\n        cy: position,\n        strokeWidth: borderWidth,\n        style: style\n    });\n};\n_c = CustomCircle;\nconst Progress = (param)=>{\n    let { percent, prefixCls } = param;\n    _s();\n    const dotClassName = \"\".concat(prefixCls, \"-dot\");\n    const holderClassName = \"\".concat(dotClassName, \"-holder\");\n    const hideClassName = \"\".concat(holderClassName, \"-hidden\");\n    const [render, setRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    // ==================== Visible =====================\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>{\n        if (percent !== 0) {\n            setRender(true);\n        }\n    }, [\n        percent !== 0\n    ]);\n    // ==================== Progress ====================\n    const safePtg = Math.max(Math.min(percent, 100), 0);\n    // ===================== Render =====================\n    if (!render) {\n        return null;\n    }\n    const circleStyle = {\n        strokeDashoffset: \"\".concat(circumference / 4),\n        strokeDasharray: \"\".concat(circumference * safePtg / 100, \" \").concat(circumference * (100 - safePtg) / 100)\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(holderClassName, \"\".concat(dotClassName, \"-progress\"), safePtg <= 0 && hideClassName)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        viewBox: \"0 0 \".concat(viewSize, \" \").concat(viewSize),\n        role: \"progressbar\",\n        \"aria-valuemin\": 0,\n        \"aria-valuemax\": 100,\n        \"aria-valuenow\": safePtg\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomCircle, {\n        dotClassName: dotClassName,\n        hasCircleCls: true\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomCircle, {\n        dotClassName: dotClassName,\n        style: circleStyle\n    })));\n};\n_s(Progress, \"bg+XEHKYpLPOb77yT6ur/b0WEQw=\");\n_c1 = Progress;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Progress);\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomCircle\");\n$RefreshReg$(_c1, \"Progress\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Progress.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/Indicator/index.js":
/*!******************************************************!*\
  !*** ./node_modules/antd/es/spin/Indicator/index.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Indicator; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../_util/reactNode */ \"(app-pages-browser)/./node_modules/antd/es/_util/reactNode.js\");\n/* harmony import */ var _Looper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Looper */ \"(app-pages-browser)/./node_modules/antd/es/spin/Indicator/Looper.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Indicator(props) {\n    var _a;\n    const { prefixCls, indicator, percent } = props;\n    const dotClassName = \"\".concat(prefixCls, \"-dot\");\n    if (indicator && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(indicator)) {\n        return (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_2__.cloneElement)(indicator, {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_a = indicator.props) === null || _a === void 0 ? void 0 : _a.className, dotClassName),\n            percent\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Looper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        prefixCls: prefixCls,\n        percent: percent\n    });\n}\n_c = Indicator;\nvar _c;\n$RefreshReg$(_c, \"Indicator\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/Indicator/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/index.js":
/*!********************************************!*\
  !*** ./node_modules/antd/es/spin/index.js ***!
  \********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var throttle_debounce__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! throttle-debounce */ \"(app-pages-browser)/./node_modules/throttle-debounce/esm/index.js\");\n/* harmony import */ var _util_warning__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../_util/warning */ \"(app-pages-browser)/./node_modules/antd/es/_util/warning.js\");\n/* harmony import */ var _config_provider_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config-provider/context */ \"(app-pages-browser)/./node_modules/antd/es/config-provider/context.js\");\n/* harmony import */ var _Indicator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Indicator */ \"(app-pages-browser)/./node_modules/antd/es/spin/Indicator/index.js\");\n/* harmony import */ var _style_index__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./style/index */ \"(app-pages-browser)/./node_modules/antd/es/spin/style/index.js\");\n/* harmony import */ var _usePercent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./usePercent */ \"(app-pages-browser)/./node_modules/antd/es/spin/usePercent.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\nconst _SpinSizes = [\n    \"small\",\n    \"default\",\n    \"large\"\n];\n// Render indicator\nlet defaultIndicator;\nfunction shouldDelay(spinning, delay) {\n    return !!spinning && !!delay && !Number.isNaN(Number(delay));\n}\nconst Spin = (props)=>{\n    _s();\n    var _a;\n    const { prefixCls: customizePrefixCls, spinning: customSpinning = true, delay = 0, className, rootClassName, size = \"default\", tip, wrapperClassName, style, children, fullscreen = false, indicator, percent } = props, restProps = __rest(props, [\n        \"prefixCls\",\n        \"spinning\",\n        \"delay\",\n        \"className\",\n        \"rootClassName\",\n        \"size\",\n        \"tip\",\n        \"wrapperClassName\",\n        \"style\",\n        \"children\",\n        \"fullscreen\",\n        \"indicator\",\n        \"percent\"\n    ]);\n    const { getPrefixCls, direction, className: contextClassName, style: contextStyle, indicator: contextIndicator } = (0,_config_provider_context__WEBPACK_IMPORTED_MODULE_3__.useComponentConfig)(\"spin\");\n    const prefixCls = getPrefixCls(\"spin\", customizePrefixCls);\n    const [wrapCSSVar, hashId, cssVarCls] = (0,_style_index__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(prefixCls);\n    const [spinning, setSpinning] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>customSpinning && !shouldDelay(customSpinning, delay));\n    const mergedPercent = (0,_usePercent__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(spinning, percent);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (customSpinning) {\n            const showSpinning = (0,throttle_debounce__WEBPACK_IMPORTED_MODULE_2__.debounce)(delay, ()=>{\n                setSpinning(true);\n            });\n            showSpinning();\n            return ()=>{\n                var _a;\n                (_a = showSpinning === null || showSpinning === void 0 ? void 0 : showSpinning.cancel) === null || _a === void 0 ? void 0 : _a.call(showSpinning);\n            };\n        }\n        setSpinning(false);\n    }, [\n        delay,\n        customSpinning\n    ]);\n    const isNestedPattern = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>typeof children !== \"undefined\" && !fullscreen, [\n        children,\n        fullscreen\n    ]);\n    if (true) {\n        const warning = (0,_util_warning__WEBPACK_IMPORTED_MODULE_6__.devUseWarning)(\"Spin\");\n         true ? warning(!tip || isNestedPattern || fullscreen, \"usage\", \"`tip` only work in nest or fullscreen pattern.\") : 0;\n    }\n    const spinClassName = classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, contextClassName, {\n        [\"\".concat(prefixCls, \"-sm\")]: size === \"small\",\n        [\"\".concat(prefixCls, \"-lg\")]: size === \"large\",\n        [\"\".concat(prefixCls, \"-spinning\")]: spinning,\n        [\"\".concat(prefixCls, \"-show-text\")]: !!tip,\n        [\"\".concat(prefixCls, \"-rtl\")]: direction === \"rtl\"\n    }, className, !fullscreen && rootClassName, hashId, cssVarCls);\n    const containerClassName = classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-container\"), {\n        [\"\".concat(prefixCls, \"-blur\")]: spinning\n    });\n    const mergedIndicator = (_a = indicator !== null && indicator !== void 0 ? indicator : contextIndicator) !== null && _a !== void 0 ? _a : defaultIndicator;\n    const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n    const spinElement = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", Object.assign({}, restProps, {\n        style: mergedStyle,\n        className: spinClassName,\n        \"aria-live\": \"polite\",\n        \"aria-busy\": spinning\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_Indicator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        prefixCls: prefixCls,\n        indicator: mergedIndicator,\n        percent: mergedPercent\n    }), tip && (isNestedPattern || fullscreen) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-text\")\n    }, tip) : null);\n    if (isNestedPattern) {\n        return wrapCSSVar(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", Object.assign({}, restProps, {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-nested-loading\"), wrapperClassName, hashId, cssVarCls)\n        }), spinning && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            key: \"loading\"\n        }, spinElement), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: containerClassName,\n            key: \"container\"\n        }, children)));\n    }\n    if (fullscreen) {\n        return wrapCSSVar(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-fullscreen\"), {\n                [\"\".concat(prefixCls, \"-fullscreen-show\")]: spinning\n            }, rootClassName, hashId, cssVarCls)\n        }, spinElement));\n    }\n    return wrapCSSVar(spinElement);\n};\n_s(Spin, \"RYgDW8SHKJPUgUBJwZLciHxd+fc=\", false, function() {\n    return [\n        _config_provider_context__WEBPACK_IMPORTED_MODULE_3__.useComponentConfig,\n        _style_index__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _usePercent__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = Spin;\nSpin.setDefaultIndicator = (indicator)=>{\n    defaultIndicator = indicator;\n};\nif (true) {\n    Spin.displayName = \"Spin\";\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (Spin);\nvar _c;\n$RefreshReg$(_c, \"Spin\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/style/index.js":
/*!**************************************************!*\
  !*** ./node_modules/antd/es/spin/style/index.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareComponentToken: function() { return /* binding */ prepareComponentToken; }\n/* harmony export */ });\n/* harmony import */ var _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ant-design/cssinjs */ \"(app-pages-browser)/./node_modules/@ant-design/cssinjs/es/index.js\");\n/* harmony import */ var _style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../style */ \"(app-pages-browser)/./node_modules/antd/es/style/index.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/antd/es/theme/util/genStyleUtils.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/@ant-design/cssinjs-utils/es/index.js\");\n\n\n\nconst antSpinMove = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes(\"antSpinMove\", {\n    to: {\n        opacity: 1\n    }\n});\nconst antRotate = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes(\"antRotate\", {\n    to: {\n        transform: \"rotate(405deg)\"\n    }\n});\nconst genSpinStyle = (token)=>{\n    const { componentCls, calc } = token;\n    return {\n        [componentCls]: Object.assign(Object.assign({}, (0,_style__WEBPACK_IMPORTED_MODULE_1__.resetComponent)(token)), {\n            position: \"absolute\",\n            display: \"none\",\n            color: token.colorPrimary,\n            fontSize: 0,\n            textAlign: \"center\",\n            verticalAlign: \"middle\",\n            opacity: 0,\n            transition: \"transform \".concat(token.motionDurationSlow, \" \").concat(token.motionEaseInOutCirc),\n            \"&-spinning\": {\n                position: \"relative\",\n                display: \"inline-block\",\n                opacity: 1\n            },\n            [\"\".concat(componentCls, \"-text\")]: {\n                fontSize: token.fontSize,\n                paddingTop: calc(calc(token.dotSize).sub(token.fontSize)).div(2).add(2).equal()\n            },\n            \"&-fullscreen\": {\n                position: \"fixed\",\n                width: \"100vw\",\n                height: \"100vh\",\n                backgroundColor: token.colorBgMask,\n                zIndex: token.zIndexPopupBase,\n                inset: 0,\n                display: \"flex\",\n                alignItems: \"center\",\n                flexDirection: \"column\",\n                justifyContent: \"center\",\n                opacity: 0,\n                visibility: \"hidden\",\n                transition: \"all \".concat(token.motionDurationMid),\n                \"&-show\": {\n                    opacity: 1,\n                    visibility: \"visible\"\n                },\n                [componentCls]: {\n                    [\"\".concat(componentCls, \"-dot-holder\")]: {\n                        color: token.colorWhite\n                    },\n                    [\"\".concat(componentCls, \"-text\")]: {\n                        color: token.colorTextLightSolid\n                    }\n                }\n            },\n            \"&-nested-loading\": {\n                position: \"relative\",\n                [\"> div > \".concat(componentCls)]: {\n                    position: \"absolute\",\n                    top: 0,\n                    insetInlineStart: 0,\n                    zIndex: 4,\n                    display: \"block\",\n                    width: \"100%\",\n                    height: \"100%\",\n                    maxHeight: token.contentHeight,\n                    [\"\".concat(componentCls, \"-dot\")]: {\n                        position: \"absolute\",\n                        top: \"50%\",\n                        insetInlineStart: \"50%\",\n                        margin: calc(token.dotSize).mul(-1).div(2).equal()\n                    },\n                    [\"\".concat(componentCls, \"-text\")]: {\n                        position: \"absolute\",\n                        top: \"50%\",\n                        width: \"100%\",\n                        textShadow: \"0 1px 2px \".concat(token.colorBgContainer) // FIXME: shadow\n                    },\n                    [\"&\".concat(componentCls, \"-show-text \").concat(componentCls, \"-dot\")]: {\n                        marginTop: calc(token.dotSize).div(2).mul(-1).sub(10).equal()\n                    },\n                    \"&-sm\": {\n                        [\"\".concat(componentCls, \"-dot\")]: {\n                            margin: calc(token.dotSizeSM).mul(-1).div(2).equal()\n                        },\n                        [\"\".concat(componentCls, \"-text\")]: {\n                            paddingTop: calc(calc(token.dotSizeSM).sub(token.fontSize)).div(2).add(2).equal()\n                        },\n                        [\"&\".concat(componentCls, \"-show-text \").concat(componentCls, \"-dot\")]: {\n                            marginTop: calc(token.dotSizeSM).div(2).mul(-1).sub(10).equal()\n                        }\n                    },\n                    \"&-lg\": {\n                        [\"\".concat(componentCls, \"-dot\")]: {\n                            margin: calc(token.dotSizeLG).mul(-1).div(2).equal()\n                        },\n                        [\"\".concat(componentCls, \"-text\")]: {\n                            paddingTop: calc(calc(token.dotSizeLG).sub(token.fontSize)).div(2).add(2).equal()\n                        },\n                        [\"&\".concat(componentCls, \"-show-text \").concat(componentCls, \"-dot\")]: {\n                            marginTop: calc(token.dotSizeLG).div(2).mul(-1).sub(10).equal()\n                        }\n                    }\n                },\n                [\"\".concat(componentCls, \"-container\")]: {\n                    position: \"relative\",\n                    transition: \"opacity \".concat(token.motionDurationSlow),\n                    \"&::after\": {\n                        position: \"absolute\",\n                        top: 0,\n                        insetInlineEnd: 0,\n                        bottom: 0,\n                        insetInlineStart: 0,\n                        zIndex: 10,\n                        width: \"100%\",\n                        height: \"100%\",\n                        background: token.colorBgContainer,\n                        opacity: 0,\n                        transition: \"all \".concat(token.motionDurationSlow),\n                        content: '\"\"',\n                        pointerEvents: \"none\"\n                    }\n                },\n                [\"\".concat(componentCls, \"-blur\")]: {\n                    clear: \"both\",\n                    opacity: 0.5,\n                    userSelect: \"none\",\n                    pointerEvents: \"none\",\n                    \"&::after\": {\n                        opacity: 0.4,\n                        pointerEvents: \"auto\"\n                    }\n                }\n            },\n            // tip\n            // ------------------------------\n            \"&-tip\": {\n                color: token.spinDotDefault\n            },\n            // holder\n            // ------------------------------\n            [\"\".concat(componentCls, \"-dot-holder\")]: {\n                width: \"1em\",\n                height: \"1em\",\n                fontSize: token.dotSize,\n                display: \"inline-block\",\n                transition: \"transform \".concat(token.motionDurationSlow, \" ease, opacity \").concat(token.motionDurationSlow, \" ease\"),\n                transformOrigin: \"50% 50%\",\n                lineHeight: 1,\n                color: token.colorPrimary,\n                \"&-hidden\": {\n                    transform: \"scale(0.3)\",\n                    opacity: 0\n                }\n            },\n            // progress\n            // ------------------------------\n            [\"\".concat(componentCls, \"-dot-progress\")]: {\n                position: \"absolute\",\n                inset: 0\n            },\n            // dots\n            // ------------------------------\n            [\"\".concat(componentCls, \"-dot\")]: {\n                position: \"relative\",\n                display: \"inline-block\",\n                fontSize: token.dotSize,\n                width: \"1em\",\n                height: \"1em\",\n                \"&-item\": {\n                    position: \"absolute\",\n                    display: \"block\",\n                    width: calc(token.dotSize).sub(calc(token.marginXXS).div(2)).div(2).equal(),\n                    height: calc(token.dotSize).sub(calc(token.marginXXS).div(2)).div(2).equal(),\n                    background: \"currentColor\",\n                    borderRadius: \"100%\",\n                    transform: \"scale(0.75)\",\n                    transformOrigin: \"50% 50%\",\n                    opacity: 0.3,\n                    animationName: antSpinMove,\n                    animationDuration: \"1s\",\n                    animationIterationCount: \"infinite\",\n                    animationTimingFunction: \"linear\",\n                    animationDirection: \"alternate\",\n                    \"&:nth-child(1)\": {\n                        top: 0,\n                        insetInlineStart: 0,\n                        animationDelay: \"0s\"\n                    },\n                    \"&:nth-child(2)\": {\n                        top: 0,\n                        insetInlineEnd: 0,\n                        animationDelay: \"0.4s\"\n                    },\n                    \"&:nth-child(3)\": {\n                        insetInlineEnd: 0,\n                        bottom: 0,\n                        animationDelay: \"0.8s\"\n                    },\n                    \"&:nth-child(4)\": {\n                        bottom: 0,\n                        insetInlineStart: 0,\n                        animationDelay: \"1.2s\"\n                    }\n                },\n                \"&-spin\": {\n                    transform: \"rotate(45deg)\",\n                    animationName: antRotate,\n                    animationDuration: \"1.2s\",\n                    animationIterationCount: \"infinite\",\n                    animationTimingFunction: \"linear\"\n                },\n                \"&-circle\": {\n                    strokeLinecap: \"round\",\n                    transition: [\n                        \"stroke-dashoffset\",\n                        \"stroke-dasharray\",\n                        \"stroke\",\n                        \"stroke-width\",\n                        \"opacity\"\n                    ].map((item)=>\"\".concat(item, \" \").concat(token.motionDurationSlow, \" ease\")).join(\",\"),\n                    fillOpacity: 0,\n                    stroke: \"currentcolor\"\n                },\n                \"&-circle-bg\": {\n                    stroke: token.colorFillSecondary\n                }\n            },\n            // small\n            [\"&-sm \".concat(componentCls, \"-dot\")]: {\n                \"&, &-holder\": {\n                    fontSize: token.dotSizeSM\n                }\n            },\n            [\"&-sm \".concat(componentCls, \"-dot-holder\")]: {\n                i: {\n                    width: calc(calc(token.dotSizeSM).sub(calc(token.marginXXS).div(2))).div(2).equal(),\n                    height: calc(calc(token.dotSizeSM).sub(calc(token.marginXXS).div(2))).div(2).equal()\n                }\n            },\n            // large\n            [\"&-lg \".concat(componentCls, \"-dot\")]: {\n                \"&, &-holder\": {\n                    fontSize: token.dotSizeLG\n                }\n            },\n            [\"&-lg \".concat(componentCls, \"-dot-holder\")]: {\n                i: {\n                    width: calc(calc(token.dotSizeLG).sub(token.marginXXS)).div(2).equal(),\n                    height: calc(calc(token.dotSizeLG).sub(token.marginXXS)).div(2).equal()\n                }\n            },\n            [\"&\".concat(componentCls, \"-show-text \").concat(componentCls, \"-text\")]: {\n                display: \"block\"\n            }\n        })\n    };\n};\nconst prepareComponentToken = (token)=>{\n    const { controlHeightLG, controlHeight } = token;\n    return {\n        contentHeight: 400,\n        dotSize: controlHeightLG / 2,\n        dotSizeSM: controlHeightLG * 0.35,\n        dotSizeLG: controlHeight\n    };\n};\n// ============================== Export ==============================\n/* harmony default export */ __webpack_exports__[\"default\"] = ((0,_theme_internal__WEBPACK_IMPORTED_MODULE_2__.genStyleHooks)(\"Spin\", (token)=>{\n    const spinToken = (0,_theme_internal__WEBPACK_IMPORTED_MODULE_3__.mergeToken)(token, {\n        spinDotDefault: token.colorTextDescription\n    });\n    return genSpinStyle(spinToken);\n}, prepareComponentToken));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/style/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/spin/usePercent.js":
/*!*************************************************!*\
  !*** ./node_modules/antd/es/spin/usePercent.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ usePercent; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst AUTO_INTERVAL = 200;\nconst STEP_BUCKETS = [\n    [\n        30,\n        0.05\n    ],\n    [\n        70,\n        0.03\n    ],\n    [\n        96,\n        0.01\n    ]\n];\nfunction usePercent(spinning, percent) {\n    _s();\n    const [mockPercent, setMockPercent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const mockIntervalRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isAuto = percent === \"auto\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isAuto && spinning) {\n            setMockPercent(0);\n            mockIntervalRef.current = setInterval(()=>{\n                setMockPercent((prev)=>{\n                    const restPTG = 100 - prev;\n                    for(let i = 0; i < STEP_BUCKETS.length; i += 1){\n                        const [limit, stepPtg] = STEP_BUCKETS[i];\n                        if (prev <= limit) {\n                            return prev + restPTG * stepPtg;\n                        }\n                    }\n                    return prev;\n                });\n            }, AUTO_INTERVAL);\n        }\n        return ()=>{\n            clearInterval(mockIntervalRef.current);\n        };\n    }, [\n        isAuto,\n        spinning\n    ]);\n    return isAuto ? mockPercent : percent;\n}\n_s(usePercent, \"jhBmjqCBWYW+shQ3ojyud5y3LSI=\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/spin/usePercent.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/overview/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/overview/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OverviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Spin,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RiseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nfunction OverviewPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isAuthenticated } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    // 防止 hydration 错误：在客户端渲染完成前显示加载状态\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    const stats = [\n        {\n            title: \"API配额\",\n            value: (user === null || user === void 0 ? void 0 : user.api_quota_used_today) || 0,\n            total: (user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000,\n            suffix: \"/ \".concat((user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            color: \"#1890ff\"\n        },\n        {\n            title: \"投资组合价值\",\n            value: 0,\n            prefix: \"\\xa5\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            color: \"#52c41a\"\n        },\n        {\n            title: \"总收益率\",\n            value: 0,\n            suffix: \"%\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 73,\n                columnNumber: 13\n            }, this),\n            color: \"#faad14\"\n        },\n        {\n            title: \"活跃策略\",\n            value: 0,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, this),\n            color: \"#722ed1\"\n        }\n    ];\n    const quickActions = [\n        {\n            title: \"配置JQData\",\n            description: \"设置API密钥以获取市场数据\",\n            action: ()=>router.push(\"/dashboard/settings/jqdata\"),\n            type: \"primary\"\n        },\n        {\n            title: \"创建策略\",\n            description: \"开始您的第一个量化策略\",\n            action: ()=>router.push(\"/dashboard/strategy/editor\"),\n            type: \"default\"\n        },\n        {\n            title: \"查看市场\",\n            description: \"浏览实时市场数据\",\n            action: ()=>router.push(\"/dashboard/market\"),\n            type: \"default\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            \"数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        className: \"text-lg\",\n                        children: [\n                            \"欢迎回来，\",\n                            user === null || user === void 0 ? void 0 : user.username,\n                            \"！这里是您的量化交易数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请先配置JQData账号以获取实时市场数据和使用量化功能\",\n                type: \"warning\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: \"small\",\n                    type: \"primary\",\n                    onClick: ()=>router.push(\"/dashboard/settings/jqdata\"),\n                    children: \"立即配置\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, void 0),\n                showIcon: true,\n                closable: true,\n                className: \"mb-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-8\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                title: stat.title,\n                                value: stat.value,\n                                precision: stat.precision,\n                                prefix: stat.prefix,\n                                suffix: stat.suffix,\n                                valueStyle: {\n                                    color: stat.color\n                                },\n                                prefix: stat.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"快速操作\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: \"small\",\n                                hoverable: true,\n                                className: \"text-center cursor-pointer\",\n                                onClick: action.action,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 4,\n                                        className: \"!mb-2\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        className: \"block mb-4\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        type: action.type,\n                                        children: \"开始使用\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"最近活动\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    description: \"暂无最近活动\",\n                    image: _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].PRESENTED_IMAGE_SIMPLE,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        type: \"primary\",\n                        onClick: ()=>router.push(\"/dashboard/strategy/editor\"),\n                        children: \"创建第一个策略\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"系统状态\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"后端服务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"正常运行\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"JQData连接\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"未配置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Spin_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"数据库\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"连接正常\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(OverviewPage, \"rmJgMOLYTRv294XggjZWrG3A/fs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = OverviewPage;\nvar _c;\n$RefreshReg$(_c, \"OverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/overview/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/throttle-debounce/esm/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/throttle-debounce/esm/index.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debounce: function() { return /* binding */ debounce; },\n/* harmony export */   throttle: function() { return /* binding */ throttle; }\n/* harmony export */ });\n/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param {number} delay -                  A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher)\n *                                            are most useful.\n * @param {Function} callback -               A function to be executed after delay milliseconds. The `this` context and all arguments are passed through,\n *                                            as-is, to `callback` when the throttled-function is executed.\n * @param {object} [options] -              An object to configure options.\n * @param {boolean} [options.noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds\n *                                            while the throttled-function is being called. If noTrailing is false or unspecified, callback will be executed\n *                                            one final time after the last throttled-function call. (After the throttled-function has not been called for\n *                                            `delay` milliseconds, the internal counter is reset).\n * @param {boolean} [options.noLeading] -   Optional, defaults to false. If noLeading is false, the first throttled-function call will execute callback\n *                                            immediately. If noLeading is true, the first the callback execution will be skipped. It should be noted that\n *                                            callback will never executed if both noLeading = true and noTrailing = true.\n * @param {boolean} [options.debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is\n *                                            false (at end), schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function} A new, throttled, function.\n */\nfunction throttle (delay, callback, options) {\n  var _ref = options || {},\n    _ref$noTrailing = _ref.noTrailing,\n    noTrailing = _ref$noTrailing === void 0 ? false : _ref$noTrailing,\n    _ref$noLeading = _ref.noLeading,\n    noLeading = _ref$noLeading === void 0 ? false : _ref$noLeading,\n    _ref$debounceMode = _ref.debounceMode,\n    debounceMode = _ref$debounceMode === void 0 ? undefined : _ref$debounceMode;\n  /*\n   * After wrapper has stopped being called, this timeout ensures that\n   * `callback` is executed at the proper times in `throttle` and `end`\n   * debounce modes.\n   */\n  var timeoutID;\n  var cancelled = false;\n\n  // Keep track of the last time `callback` was executed.\n  var lastExec = 0;\n\n  // Function to clear existing timeout\n  function clearExistingTimeout() {\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n  }\n\n  // Function to cancel next exec\n  function cancel(options) {\n    var _ref2 = options || {},\n      _ref2$upcomingOnly = _ref2.upcomingOnly,\n      upcomingOnly = _ref2$upcomingOnly === void 0 ? false : _ref2$upcomingOnly;\n    clearExistingTimeout();\n    cancelled = !upcomingOnly;\n  }\n\n  /*\n   * The `wrapper` function encapsulates all of the throttling / debouncing\n   * functionality and when executed will limit the rate at which `callback`\n   * is executed.\n   */\n  function wrapper() {\n    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {\n      arguments_[_key] = arguments[_key];\n    }\n    var self = this;\n    var elapsed = Date.now() - lastExec;\n    if (cancelled) {\n      return;\n    }\n\n    // Execute `callback` and update the `lastExec` timestamp.\n    function exec() {\n      lastExec = Date.now();\n      callback.apply(self, arguments_);\n    }\n\n    /*\n     * If `debounceMode` is true (at begin) this is used to clear the flag\n     * to allow future `callback` executions.\n     */\n    function clear() {\n      timeoutID = undefined;\n    }\n    if (!noLeading && debounceMode && !timeoutID) {\n      /*\n       * Since `wrapper` is being called for the first time and\n       * `debounceMode` is true (at begin), execute `callback`\n       * and noLeading != true.\n       */\n      exec();\n    }\n    clearExistingTimeout();\n    if (debounceMode === undefined && elapsed > delay) {\n      if (noLeading) {\n        /*\n         * In throttle mode with noLeading, if `delay` time has\n         * been exceeded, update `lastExec` and schedule `callback`\n         * to execute after `delay` ms.\n         */\n        lastExec = Date.now();\n        if (!noTrailing) {\n          timeoutID = setTimeout(debounceMode ? clear : exec, delay);\n        }\n      } else {\n        /*\n         * In throttle mode without noLeading, if `delay` time has been exceeded, execute\n         * `callback`.\n         */\n        exec();\n      }\n    } else if (noTrailing !== true) {\n      /*\n       * In trailing throttle mode, since `delay` time has not been\n       * exceeded, schedule `callback` to execute `delay` ms after most\n       * recent execution.\n       *\n       * If `debounceMode` is true (at begin), schedule `clear` to execute\n       * after `delay` ms.\n       *\n       * If `debounceMode` is false (at end), schedule `callback` to\n       * execute after `delay` ms.\n       */\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n  wrapper.cancel = cancel;\n\n  // Return the wrapper function.\n  return wrapper;\n}\n\n/* eslint-disable no-undefined */\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param {number} delay -               A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param {Function} callback -          A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                        to `callback` when the debounced-function is executed.\n * @param {object} [options] -           An object to configure options.\n * @param {boolean} [options.atBegin] -  Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                        after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                        (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n *\n * @returns {Function} A new, debounced function.\n */\nfunction debounce (delay, callback, options) {\n  var _ref = options || {},\n    _ref$atBegin = _ref.atBegin,\n    atBegin = _ref$atBegin === void 0 ? false : _ref$atBegin;\n  return throttle(delay, callback, {\n    debounceMode: atBegin !== false\n  });\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/throttle-debounce/esm/index.js\n"));

/***/ })

});