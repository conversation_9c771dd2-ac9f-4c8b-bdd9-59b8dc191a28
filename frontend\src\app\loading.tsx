'use client';

/**
 * 全局加载页面
 * 
 * 在页面切换时显示的加载状态
 */

import React from 'react';
import { Spin } from 'antd';
import { motion } from 'framer-motion';

export default function Loading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="text-center"
      >
        <Spin size="large" />
        <div className="mt-4 text-gray-600">
          加载中...
        </div>
      </motion.div>
    </div>
  );
}
