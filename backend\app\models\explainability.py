"""
模型可解释性相关数据模型

定义SHAP、LIME、决策可视化等模型解释功能的数据结构
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class ExplainabilityAnalysis(Base):
    """可解释性分析"""
    
    __tablename__ = "explainability_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    model_id = Column(Integer, nullable=False, index=True)  # 可以关联不同类型的模型
    model_type = Column(String(50), nullable=False)  # ml_model, automl_model, transformer_model, etc.
    
    # 分析基本信息
    analysis_name = Column(String(100), nullable=False)
    description = Column(Text)
    analysis_type = Column(String(50), nullable=False)  # global, local, feature_importance
    
    # 解释方法配置
    explanation_methods = Column(JSON)  # 使用的解释方法列表
    method_configs = Column(JSON)  # 各方法的配置参数
    
    # 数据配置
    dataset_info = Column(JSON)  # 数据集信息
    feature_names = Column(JSON)  # 特征名称列表
    feature_types = Column(JSON)  # 特征类型信息
    target_classes = Column(JSON)  # 目标类别（分类问题）
    
    # 分析范围
    analysis_scope = Column(String(50))  # full_dataset, sample, specific_instances
    sample_size = Column(Integer)  # 样本大小
    instance_ids = Column(JSON)  # 特定实例ID列表
    
    # 执行状态
    status = Column(String(20), default="created")  # created, running, completed, failed
    progress = Column(Integer, default=0)  # 进度百分比
    current_method = Column(String(50))  # 当前执行的方法
    
    # 执行统计
    total_methods = Column(Integer, default=0)
    completed_methods = Column(Integer, default=0)
    failed_methods = Column(Integer, default=0)
    
    # 计算资源
    computation_time_seconds = Column(Float)
    memory_usage_mb = Column(Float)
    cpu_usage_percent = Column(Float)
    
    # 结果摘要
    has_global_explanations = Column(Boolean, default=False)
    has_local_explanations = Column(Boolean, default=False)
    has_feature_importance = Column(Boolean, default=False)
    has_visualizations = Column(Boolean, default=False)
    
    # 质量指标
    explanation_quality_score = Column(Float)  # 解释质量评分
    consistency_score = Column(Float)  # 一致性评分
    stability_score = Column(Float)  # 稳定性评分
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="explainability_analyses")
    shap_explanations = relationship("SHAPExplanation", back_populates="analysis", cascade="all, delete-orphan")
    lime_explanations = relationship("LIMEExplanation", back_populates="analysis", cascade="all, delete-orphan")
    feature_importance_analyses = relationship("FeatureImportanceAnalysis", back_populates="analysis", cascade="all, delete-orphan")
    decision_trees = relationship("DecisionTreeVisualization", back_populates="analysis", cascade="all, delete-orphan")
    explanation_reports = relationship("ExplanationReport", back_populates="analysis", cascade="all, delete-orphan")


class SHAPExplanation(Base):
    """SHAP解释"""
    
    __tablename__ = "shap_explanations"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("explainability_analyses.id"), nullable=False, index=True)
    
    # SHAP配置
    explainer_type = Column(String(50), nullable=False)  # tree, linear, kernel, deep, etc.
    explainer_config = Column(JSON)  # 解释器配置参数
    
    # 解释范围
    explanation_scope = Column(String(50))  # global, local, both
    instance_count = Column(Integer)  # 解释的实例数量
    
    # SHAP值
    shap_values = Column(JSON)  # SHAP值矩阵
    base_values = Column(JSON)  # 基准值
    expected_values = Column(JSON)  # 期望值
    
    # 全局解释
    global_feature_importance = Column(JSON)  # 全局特征重要性
    feature_interactions = Column(JSON)  # 特征交互
    summary_statistics = Column(JSON)  # 汇总统计
    
    # 局部解释
    local_explanations = Column(JSON)  # 局部解释结果
    instance_predictions = Column(JSON)  # 实例预测结果
    explanation_confidence = Column(JSON)  # 解释置信度
    
    # 可视化数据
    summary_plot_data = Column(JSON)  # 汇总图数据
    waterfall_plot_data = Column(JSON)  # 瀑布图数据
    force_plot_data = Column(JSON)  # 力图数据
    dependence_plot_data = Column(JSON)  # 依赖图数据
    
    # 质量指标
    explanation_fidelity = Column(Float)  # 解释保真度
    explanation_stability = Column(Float)  # 解释稳定性
    computation_efficiency = Column(Float)  # 计算效率
    
    # 执行信息
    computation_time_seconds = Column(Float)
    memory_usage_mb = Column(Float)
    status = Column(String(20), default="completed")
    error_message = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    analysis = relationship("ExplainabilityAnalysis", back_populates="shap_explanations")


class LIMEExplanation(Base):
    """LIME解释"""
    
    __tablename__ = "lime_explanations"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("explainability_analyses.id"), nullable=False, index=True)
    
    # LIME配置
    explainer_type = Column(String(50), nullable=False)  # tabular, text, image
    explainer_config = Column(JSON)  # 解释器配置参数
    
    # 解释参数
    num_features = Column(Integer)  # 解释特征数量
    num_samples = Column(Integer)  # 采样数量
    distance_metric = Column(String(50))  # 距离度量
    kernel_width = Column(Float)  # 核宽度
    
    # 实例解释
    instance_explanations = Column(JSON)  # 实例解释结果
    feature_contributions = Column(JSON)  # 特征贡献
    local_model_coefficients = Column(JSON)  # 局部模型系数
    
    # 解释质量
    local_model_r2 = Column(JSON)  # 局部模型R²
    explanation_scores = Column(JSON)  # 解释分数
    feature_selection_frequency = Column(JSON)  # 特征选择频率
    
    # 可视化数据
    explanation_plots = Column(JSON)  # 解释图数据
    feature_importance_plots = Column(JSON)  # 特征重要性图
    
    # 稳定性分析
    stability_analysis = Column(JSON)  # 稳定性分析结果
    perturbation_analysis = Column(JSON)  # 扰动分析结果
    
    # 执行信息
    computation_time_seconds = Column(Float)
    memory_usage_mb = Column(Float)
    status = Column(String(20), default="completed")
    error_message = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    analysis = relationship("ExplainabilityAnalysis", back_populates="lime_explanations")


class FeatureImportanceAnalysis(Base):
    """特征重要性分析"""
    
    __tablename__ = "feature_importance_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("explainability_analyses.id"), nullable=False, index=True)
    
    # 分析方法
    importance_methods = Column(JSON)  # 重要性计算方法
    method_configs = Column(JSON)  # 方法配置
    
    # 特征重要性结果
    feature_importances = Column(JSON)  # 各方法的特征重要性
    aggregated_importance = Column(JSON)  # 聚合重要性
    importance_rankings = Column(JSON)  # 重要性排名
    
    # 重要性统计
    importance_statistics = Column(JSON)  # 重要性统计信息
    correlation_matrix = Column(JSON)  # 特征相关性矩阵
    redundancy_analysis = Column(JSON)  # 冗余性分析
    
    # 特征选择建议
    recommended_features = Column(JSON)  # 推荐特征
    feature_selection_threshold = Column(Float)  # 特征选择阈值
    selection_rationale = Column(JSON)  # 选择理由
    
    # 可视化数据
    importance_plots = Column(JSON)  # 重要性图表数据
    correlation_heatmap = Column(JSON)  # 相关性热力图
    feature_distribution_plots = Column(JSON)  # 特征分布图
    
    # 质量指标
    importance_consistency = Column(Float)  # 重要性一致性
    method_agreement = Column(Float)  # 方法一致性
    statistical_significance = Column(JSON)  # 统计显著性
    
    # 执行信息
    computation_time_seconds = Column(Float)
    status = Column(String(20), default="completed")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    analysis = relationship("ExplainabilityAnalysis", back_populates="feature_importance_analyses")


class DecisionTreeVisualization(Base):
    """决策树可视化"""
    
    __tablename__ = "decision_tree_visualizations"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("explainability_analyses.id"), nullable=False, index=True)
    
    # 决策树信息
    tree_type = Column(String(50))  # original, surrogate, simplified
    tree_depth = Column(Integer)  # 树深度
    node_count = Column(Integer)  # 节点数量
    leaf_count = Column(Integer)  # 叶子节点数量
    
    # 树结构
    tree_structure = Column(JSON)  # 树结构数据
    node_information = Column(JSON)  # 节点信息
    split_conditions = Column(JSON)  # 分割条件
    leaf_predictions = Column(JSON)  # 叶子预测
    
    # 决策路径
    decision_paths = Column(JSON)  # 决策路径
    path_probabilities = Column(JSON)  # 路径概率
    feature_usage = Column(JSON)  # 特征使用情况
    
    # 可视化配置
    visualization_config = Column(JSON)  # 可视化配置
    layout_parameters = Column(JSON)  # 布局参数
    styling_options = Column(JSON)  # 样式选项
    
    # 可视化数据
    tree_plot_data = Column(JSON)  # 树图数据
    interactive_tree_data = Column(JSON)  # 交互式树数据
    decision_boundary_data = Column(JSON)  # 决策边界数据
    
    # 简化版本
    simplified_tree = Column(JSON)  # 简化树结构
    pruning_information = Column(JSON)  # 剪枝信息
    complexity_metrics = Column(JSON)  # 复杂度指标
    
    # 质量指标
    tree_fidelity = Column(Float)  # 树保真度
    interpretability_score = Column(Float)  # 可解释性评分
    accuracy_vs_original = Column(Float)  # 与原模型的准确性对比
    
    # 执行信息
    generation_time_seconds = Column(Float)
    status = Column(String(20), default="completed")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    analysis = relationship("ExplainabilityAnalysis", back_populates="decision_trees")


class ExplanationReport(Base):
    """解释报告"""
    
    __tablename__ = "explanation_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("explainability_analyses.id"), nullable=False, index=True)
    
    # 报告基本信息
    report_name = Column(String(100), nullable=False)
    report_type = Column(String(50))  # summary, detailed, technical, business
    target_audience = Column(String(50))  # technical, business, regulatory
    
    # 报告内容
    executive_summary = Column(Text)  # 执行摘要
    methodology_description = Column(Text)  # 方法论描述
    key_findings = Column(JSON)  # 关键发现
    recommendations = Column(JSON)  # 建议
    
    # 模型概述
    model_overview = Column(JSON)  # 模型概述
    performance_metrics = Column(JSON)  # 性能指标
    data_description = Column(JSON)  # 数据描述
    
    # 解释结果
    global_insights = Column(JSON)  # 全局洞察
    local_explanations_summary = Column(JSON)  # 局部解释摘要
    feature_analysis = Column(JSON)  # 特征分析
    
    # 可视化内容
    included_visualizations = Column(JSON)  # 包含的可视化
    chart_descriptions = Column(JSON)  # 图表描述
    visualization_insights = Column(JSON)  # 可视化洞察
    
    # 质量评估
    explanation_quality_assessment = Column(JSON)  # 解释质量评估
    reliability_analysis = Column(JSON)  # 可靠性分析
    limitations_discussion = Column(Text)  # 局限性讨论
    
    # 合规性信息
    regulatory_compliance = Column(JSON)  # 监管合规性
    audit_trail = Column(JSON)  # 审计跟踪
    documentation_standards = Column(JSON)  # 文档标准
    
    # 报告格式
    report_format = Column(String(20))  # html, pdf, json, markdown
    report_content = Column(Text)  # 报告内容
    report_metadata = Column(JSON)  # 报告元数据
    
    # 生成信息
    generation_config = Column(JSON)  # 生成配置
    template_used = Column(String(100))  # 使用的模板
    generation_time_seconds = Column(Float)
    
    # 状态管理
    status = Column(String(20), default="generated")  # generated, reviewed, approved, published
    review_status = Column(String(20))  # pending, approved, rejected
    reviewer_comments = Column(Text)  # 审核意见
    
    # 版本控制
    version = Column(String(20), default="1.0")
    parent_report_id = Column(Integer, ForeignKey("explanation_reports.id"))
    is_latest_version = Column(Boolean, default=True)
    
    # 访问控制
    access_level = Column(String(20), default="private")  # private, internal, public
    shared_with = Column(JSON)  # 共享对象
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = Column(DateTime)
    
    # 关系
    analysis = relationship("ExplainabilityAnalysis", back_populates="explanation_reports")
    parent_report = relationship("ExplanationReport", remote_side=[id])


class ModelInterpretabilityMetrics(Base):
    """模型可解释性指标"""
    
    __tablename__ = "model_interpretability_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    model_id = Column(Integer, nullable=False, index=True)
    model_type = Column(String(50), nullable=False)
    
    # 可解释性评分
    overall_interpretability_score = Column(Float)  # 总体可解释性评分
    transparency_score = Column(Float)  # 透明度评分
    comprehensibility_score = Column(Float)  # 可理解性评分
    
    # 复杂度指标
    model_complexity = Column(Float)  # 模型复杂度
    feature_complexity = Column(Float)  # 特征复杂度
    interaction_complexity = Column(Float)  # 交互复杂度
    
    # 稳定性指标
    explanation_stability = Column(Float)  # 解释稳定性
    prediction_stability = Column(Float)  # 预测稳定性
    robustness_score = Column(Float)  # 鲁棒性评分
    
    # 一致性指标
    method_consistency = Column(Float)  # 方法一致性
    cross_validation_consistency = Column(Float)  # 交叉验证一致性
    temporal_consistency = Column(Float)  # 时间一致性
    
    # 保真度指标
    local_fidelity = Column(Float)  # 局部保真度
    global_fidelity = Column(Float)  # 全局保真度
    approximation_quality = Column(Float)  # 近似质量
    
    # 效率指标
    explanation_generation_time = Column(Float)  # 解释生成时间
    computational_efficiency = Column(Float)  # 计算效率
    scalability_score = Column(Float)  # 可扩展性评分
    
    # 实用性指标
    actionability_score = Column(Float)  # 可操作性评分
    business_relevance = Column(Float)  # 业务相关性
    user_satisfaction = Column(Float)  # 用户满意度
    
    # 合规性指标
    regulatory_compliance_score = Column(Float)  # 监管合规性评分
    audit_readiness = Column(Float)  # 审计准备度
    documentation_completeness = Column(Float)  # 文档完整性
    
    # 计算详情
    metrics_calculation_method = Column(JSON)  # 指标计算方法
    benchmark_comparisons = Column(JSON)  # 基准对比
    improvement_suggestions = Column(JSON)  # 改进建议
    
    # 时间戳
    calculated_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="interpretability_metrics")
