System.register([],function(s){"use strict";return{execute:function(){const a=e=>{let t;const o=new Set,r=(n,l)=>{const c=typeof n=="function"?n(t):n;if(!Object.is(c,t)){const S=t;t=(l!=null?l:typeof c!="object"||c===null)?c:Object.assign({},t,c),o.forEach(b=>b(t,S))}},i=()=>t,u={setState:r,getState:i,subscribe:n=>(o.add(n),()=>o.delete(n)),destroy:()=>{o.clear()}};return t=e(r,i,u),u},f=s("createStore",e=>e?a(e):a);var d=s("default",e=>f(e))}}});
