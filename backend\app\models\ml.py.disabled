"""
机器学习相关数据模型

定义特征工程、模型管理、预测结果等数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Numeric, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class MLModel(Base):
    """机器学习模型"""
    
    __tablename__ = "ml_models"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模型基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    model_type = Column(String(50), nullable=False)  # recommendation, prediction, classification
    algorithm = Column(String(50), nullable=False)  # lstm, xgboost, collaborative_filtering, etc.
    version = Column(String(20), default="1.0.0")
    
    # 模型配置
    hyperparameters = Column(JSON)  # 超参数配置
    feature_config = Column(JSON)  # 特征配置
    training_config = Column(JSON)  # 训练配置
    
    # 模型文件信息
    model_path = Column(String(500))  # 模型文件路径
    model_size = Column(Integer)  # 模型文件大小
    model_format = Column(String(20))  # pkl, h5, onnx, etc.
    
    # 训练信息
    training_data_start = Column(DateTime)
    training_data_end = Column(DateTime)
    training_samples = Column(Integer)
    training_features = Column(Integer)
    training_duration = Column(Integer)  # 训练耗时（秒）
    
    # 模型性能
    performance_metrics = Column(JSON)  # 性能指标
    validation_score = Column(Numeric(10, 6))  # 验证分数
    test_score = Column(Numeric(10, 6))  # 测试分数
    
    # 状态管理
    status = Column(String(20), default="training")  # training, trained, deployed, deprecated
    is_active = Column(Boolean, default=False)
    is_default = Column(Boolean, default=False)
    
    # 部署信息
    deployed_at = Column(DateTime)
    deployment_config = Column(JSON)
    api_endpoint = Column(String(200))
    
    # 使用统计
    prediction_count = Column(Integer, default=0)
    last_used_at = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用
    predictions = relationship("MLPrediction", back_populates="model", cascade="all, delete-orphan")
    training_jobs = relationship("MLTrainingJob", back_populates="model", cascade="all, delete-orphan")


class MLFeature(Base):
    """机器学习特征"""
    
    __tablename__ = "ml_features"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 特征基本信息
    name = Column(String(100), nullable=False, unique=True, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    category = Column(String(50))  # technical, fundamental, sentiment, macro
    data_type = Column(String(20))  # numeric, categorical, text, datetime
    
    # 特征计算
    calculation_method = Column(Text)  # 计算方法描述
    source_tables = Column(JSON)  # 数据源表
    dependencies = Column(JSON)  # 依赖的其他特征
    update_frequency = Column(String(20))  # daily, hourly, realtime
    
    # 特征统计
    min_value = Column(Numeric(15, 6))
    max_value = Column(Numeric(15, 6))
    mean_value = Column(Numeric(15, 6))
    std_value = Column(Numeric(15, 6))
    null_ratio = Column(Numeric(5, 4))  # 空值比例
    
    # 特征重要性
    importance_score = Column(Numeric(10, 6))  # 特征重要性分数
    correlation_target = Column(Numeric(10, 6))  # 与目标变量的相关性
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_deprecated = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_calculated_at = Column(DateTime)
    
    # 关系
    feature_values = relationship("MLFeatureValue", back_populates="feature", cascade="all, delete-orphan")


class MLFeatureValue(Base):
    """机器学习特征值"""
    
    __tablename__ = "ml_feature_values"
    
    id = Column(Integer, primary_key=True, index=True)
    feature_id = Column(Integer, ForeignKey("ml_features.id"), nullable=False, index=True)
    
    # 特征值信息
    symbol = Column(String(20), nullable=False, index=True)  # 股票代码
    date = Column(DateTime, nullable=False, index=True)  # 日期
    value = Column(Numeric(15, 6))  # 特征值
    
    # 元数据
    data_source = Column(String(50))  # 数据来源
    calculation_version = Column(String(20))  # 计算版本
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    feature = relationship("MLFeature", back_populates="feature_values")
    
    # 复合索引
    __table_args__ = (
        {'mysql_engine': 'InnoDB'},
    )


class MLPrediction(Base):
    """机器学习预测结果"""
    
    __tablename__ = "ml_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("ml_models.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 预测基本信息
    prediction_type = Column(String(50), nullable=False)  # price, direction, volatility, recommendation
    target_symbol = Column(String(20), nullable=False, index=True)  # 目标股票
    prediction_date = Column(DateTime, nullable=False, index=True)  # 预测日期
    target_date = Column(DateTime, nullable=False)  # 目标日期
    
    # 预测结果
    predicted_value = Column(Numeric(15, 6))  # 预测值
    predicted_class = Column(String(20))  # 预测类别（涨/跌/震荡）
    confidence_score = Column(Numeric(5, 4))  # 置信度
    probability_distribution = Column(JSON)  # 概率分布
    
    # 预测区间
    lower_bound = Column(Numeric(15, 6))  # 下界
    upper_bound = Column(Numeric(15, 6))  # 上界
    
    # 实际结果（用于评估）
    actual_value = Column(Numeric(15, 6))  # 实际值
    actual_class = Column(String(20))  # 实际类别
    is_correct = Column(Boolean)  # 预测是否正确
    
    # 预测准确性
    absolute_error = Column(Numeric(15, 6))  # 绝对误差
    relative_error = Column(Numeric(10, 6))  # 相对误差
    
    # 元数据
    input_features = Column(JSON)  # 输入特征
    model_version = Column(String(20))  # 模型版本
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    evaluated_at = Column(DateTime)  # 评估时间
    
    # 关系 - 移除back_populates避免循环引用错误
    model = relationship("MLModel", back_populates="predictions")
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用


class MLTrainingJob(Base):
    """机器学习训练任务"""
    
    __tablename__ = "ml_training_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("ml_models.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 任务基本信息
    job_name = Column(String(100), nullable=False)
    job_type = Column(String(50), default="training")  # training, retraining, tuning
    
    # 训练配置
    training_config = Column(JSON, nullable=False)
    hyperparameters = Column(JSON)
    data_config = Column(JSON)  # 数据配置
    
    # 任务状态
    status = Column(String(20), default="pending")  # pending, running, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 进度百分比
    
    # 执行信息
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    duration = Column(Integer)  # 执行时长（秒）
    
    # 训练结果
    final_metrics = Column(JSON)  # 最终指标
    best_score = Column(Numeric(10, 6))  # 最佳分数
    training_history = Column(JSON)  # 训练历史
    
    # 资源使用
    cpu_usage = Column(Numeric(5, 2))  # CPU使用率
    memory_usage = Column(Numeric(10, 2))  # 内存使用量(MB)
    gpu_usage = Column(Numeric(5, 2))  # GPU使用率
    
    # 错误信息
    error_message = Column(Text)
    error_traceback = Column(Text)
    
    # 输出信息
    output_model_path = Column(String(500))
    logs_path = Column(String(500))
    artifacts_path = Column(String(500))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    model = relationship("MLModel", back_populates="training_jobs")
    user = relationship("User", back_populates="ml_training_jobs")


class MLRecommendation(Base):
    """机器学习推荐结果"""
    
    __tablename__ = "ml_recommendations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    model_id = Column(Integer, ForeignKey("ml_models.id"), nullable=False, index=True)
    
    # 推荐基本信息
    recommendation_type = Column(String(50), nullable=False)  # strategy, stock, portfolio
    target_id = Column(String(50))  # 推荐目标ID（策略ID、股票代码等）
    target_name = Column(String(100))  # 推荐目标名称
    
    # 推荐分数
    score = Column(Numeric(10, 6), nullable=False)  # 推荐分数
    rank = Column(Integer)  # 排名
    confidence = Column(Numeric(5, 4))  # 置信度
    
    # 推荐原因
    reasons = Column(JSON)  # 推荐原因列表
    features_importance = Column(JSON)  # 特征重要性
    similar_items = Column(JSON)  # 相似项目
    
    # 用户反馈
    user_rating = Column(Integer)  # 用户评分 1-5
    user_feedback = Column(Text)  # 用户反馈
    is_clicked = Column(Boolean, default=False)  # 是否点击
    is_adopted = Column(Boolean, default=False)  # 是否采用
    
    # 推荐效果
    click_time = Column(DateTime)  # 点击时间
    adoption_time = Column(DateTime)  # 采用时间
    effectiveness_score = Column(Numeric(10, 6))  # 效果分数
    
    # 元数据
    recommendation_context = Column(JSON)  # 推荐上下文
    algorithm_version = Column(String(20))  # 算法版本
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)  # 过期时间
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用
    model = relationship("MLModel")


class MLExperiment(Base):
    """机器学习实验"""
    
    __tablename__ = "ml_experiments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 实验基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    experiment_type = Column(String(50))  # ab_test, model_comparison, feature_selection
    
    # 实验配置
    config = Column(JSON, nullable=False)
    hypothesis = Column(Text)  # 实验假设
    success_metrics = Column(JSON)  # 成功指标
    
    # 实验状态
    status = Column(String(20), default="draft")  # draft, running, completed, cancelled
    
    # 实验结果
    results = Column(JSON)  # 实验结果
    conclusion = Column(Text)  # 结论
    statistical_significance = Column(Boolean)  # 统计显著性
    
    # 时间管理
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    duration_days = Column(Integer)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="ml_experiments")
