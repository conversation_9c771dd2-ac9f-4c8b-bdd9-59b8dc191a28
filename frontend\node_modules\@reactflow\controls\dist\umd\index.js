!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlowControls={},e.<PERSON>act)}(this,(function(e,t){"use strict";function n(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let o,r=0;r<e.length;r++)""!==(o=n(e[r]))&&(t+=(t&&" ")+o);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}function o(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var i,a,s,l={},c={},u={},d={get exports(){return u},set exports(e){u=e}},h={};function f(){return a||(a=1,function(e){e.exports=function(){if(i)return h;i=1;var e=t,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,a=e.useLayoutEffect,s=e.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!n(e,o)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),i=o({inst:{value:n,getSnapshot:t}}),c=i[0].inst,u=i[1];return a((function(){c.value=n,c.getSnapshot=t,l(c)&&u({inst:c})}),[e,n,t]),r((function(){return l(c)&&u({inst:c}),e((function(){l(c)&&u({inst:c})}))}),[e]),s(n),n};return h.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:c,h}()}(d)),u}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(e){e.exports=function(){if(s)return c;s=1;var e=t,n=f(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=n.useSyncExternalStore,i=e.useRef,a=e.useEffect,l=e.useMemo,u=e.useDebugValue;return c.useSyncExternalStoreWithSelector=function(e,t,n,s,c){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=l((function(){function e(e){if(!a){if(a=!0,r=e,e=s(e),void 0!==c&&h.hasValue){var t=h.value;if(c(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=s(e);return void 0!==c&&c(t,n)?t:(r=e,i=n)}var r,i,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]}),[t,n,s,c]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),u(f),f},c}()}({get exports(){return l},set exports(e){l=e}});var g=r(l);const p=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:m}=g;function y(e,n=e.getState,o){const r=m(e.subscribe,e.getState,e.getServerState||e.getState,n,o);return t.useDebugValue(r),r}const v=(e,t)=>{const n=(e=>e?p(e):p)(e),o=(e,o=t)=>y(n,e,o);return Object.assign(o,n),o};function b(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}var w={value:()=>{}};function x(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new S(o)}function S(e){this._=e}function E(e,t){return e.trim().split(/^|\s+/).map((function(e){var n="",o=e.indexOf(".");if(o>=0&&(n=e.slice(o+1),e=e.slice(0,o)),e&&!t.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:n}}))}function _(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function C(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=w,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}S.prototype=x.prototype={constructor:S,on:function(e,t){var n,o=this._,r=E(e+"",o),i=-1,a=r.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++i<a;)if(n=(e=r[i]).type)o[n]=C(o[n],e.name,t);else if(null==t)for(n in o)o[n]=C(o[n],e.name,null);return this}for(;++i<a;)if((n=(e=r[i]).type)&&(n=_(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new S(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var M="http://www.w3.org/1999/xhtml",N={svg:"http://www.w3.org/2000/svg",xhtml:M,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function k(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),N.hasOwnProperty(t)?{space:N[t],local:e}:e}function P(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===M&&t.documentElement.namespaceURI===M?t.createElement(e):t.createElementNS(n,e)}}function A(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function O(e){var t=k(e);return(t.local?A:P)(t)}function I(){}function D(e){return null==e?I:function(){return this.querySelector(e)}}function z(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}function R(){return[]}function $(e){return null==e?R:function(){return this.querySelectorAll(e)}}function T(e){return function(){return this.matches(e)}}function B(e){return function(t){return t.matches(e)}}var H=Array.prototype.find;function L(){return this.firstElementChild}var V=Array.prototype.filter;function X(){return Array.from(this.children)}function Y(e){return new Array(e.length)}function K(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function Z(e){return function(){return e}}function F(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new K(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function j(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new K(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function W(e){return e.__data__}function q(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function U(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function G(e){return function(){this.removeAttribute(e)}}function Q(e){return function(){this.removeAttributeNS(e.space,e.local)}}function J(e,t){return function(){this.setAttribute(e,t)}}function ee(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function te(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function ne(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function oe(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function re(e){return function(){this.style.removeProperty(e)}}function ie(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ae(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function se(e,t){return e.style.getPropertyValue(t)||oe(e).getComputedStyle(e,null).getPropertyValue(t)}function le(e){return function(){delete this[e]}}function ce(e,t){return function(){this[e]=t}}function ue(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function de(e){return e.trim().split(/^|\s+/)}function he(e){return e.classList||new fe(e)}function fe(e){this._node=e,this._names=de(e.getAttribute("class")||"")}function ge(e,t){for(var n=he(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function pe(e,t){for(var n=he(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function me(e){return function(){ge(this,e)}}function ye(e){return function(){pe(this,e)}}function ve(e,t){return function(){(t.apply(this,arguments)?ge:pe)(this,e)}}function be(){this.textContent=""}function we(e){return function(){this.textContent=e}}function xe(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function Se(){this.innerHTML=""}function Ee(e){return function(){this.innerHTML=e}}function _e(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function Ce(){this.nextSibling&&this.parentNode.appendChild(this)}function Me(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Ne(){return null}function ke(){var e=this.parentNode;e&&e.removeChild(this)}function Pe(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Ae(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Oe(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}function Ie(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function De(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function ze(e,t,n){var o=oe(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function Re(e,t){return function(){return ze(this,e,t)}}function $e(e,t){return function(){return ze(this,e,t.apply(this,arguments))}}K.prototype={constructor:K,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},fe.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var Te=[null];function Be(e,t){this._groups=e,this._parents=t}function He(){return new Be([[document.documentElement]],Te)}function Le(e){return"string"==typeof e?new Be([[document.querySelector(e)]],[document.documentElement]):new Be([[e]],Te)}function Ve(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}Be.prototype=He.prototype={constructor:Be,select:function(e){"function"!=typeof e&&(e=D(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new Be(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return z(e.apply(this,arguments))}}(e):$(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new Be(o,r)},selectChild:function(e){return this.select(null==e?L:function(e){return function(){return H.call(this.children,e)}}("function"==typeof e?e:B(e)))},selectChildren:function(e){return this.selectAll(null==e?X:function(e){return function(){return V.call(this.children,e)}}("function"==typeof e?e:B(e)))},filter:function(e){"function"!=typeof e&&(e=T(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Be(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,W);var n=t?j:F,o=this._parents,r=this._groups;"function"!=typeof e&&(e=Z(e));for(var i=r.length,a=new Array(i),s=new Array(i),l=new Array(i),c=0;c<i;++c){var u=o[c],d=r[c],h=d.length,f=q(e.call(u,u&&u.__data__,c,o)),g=f.length,p=s[c]=new Array(g),m=a[c]=new Array(g),y=l[c]=new Array(h);n(u,d,p,m,y,f,t);for(var v,b,w=0,x=0;w<g;++w)if(v=p[w]){for(w>=x&&(x=w+1);!(b=m[x])&&++x<g;);v._next=b||null}}return(a=new Be(a,o))._enter=s,a._exit=l,a},enter:function(){return new Be(this._enter||this._groups.map(Y),this._parents)},exit:function(){return new Be(this._exit||this._groups.map(Y),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new Be(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=U);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new Be(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=k(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?Q:G:"function"==typeof t?n.local?ne:te:n.local?ee:J)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?re:"function"==typeof t?ae:ie)(e,t,null==n?"":n)):se(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?le:"function"==typeof t?ue:ce)(e,t)):this.node()[e]},classed:function(e,t){var n=de(e+"");if(arguments.length<2){for(var o=he(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?ve:t?me:ye)(n,t))},text:function(e){return arguments.length?this.each(null==e?be:("function"==typeof e?xe:we)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?Se:("function"==typeof e?_e:Ee)(e)):this.node().innerHTML},raise:function(){return this.each(Ce)},lower:function(){return this.each(Me)},append:function(e){var t="function"==typeof e?e:O(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:O(e),o=null==t?Ne:"function"==typeof t?t:D(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(ke)},clone:function(e){return this.select(e?Ae:Pe)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=Oe(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?De:Ie,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?$e:Re)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Xe={passive:!1},Ye={capture:!0,passive:!1};function Ke(e){e.stopImmediatePropagation()}function Ze(e){e.preventDefault(),e.stopImmediatePropagation()}function Fe(e){var t=e.document.documentElement,n=Le(e).on("dragstart.drag",Ze,Ye);"onselectstart"in t?n.on("selectstart.drag",Ze,Ye):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function je(e,t){var n=e.document.documentElement,o=Le(e).on("dragstart.drag",null);t&&(o.on("click.drag",Ze,Ye),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var We=e=>()=>e;function qe(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function Ue(e){return!e.ctrlKey&&!e.button}function Ge(){return this.parentNode}function Qe(e,t){return null==t?{x:e.x,y:e.y}:t}function Je(){return navigator.maxTouchPoints||"ontouchstart"in this}function et(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function tt(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function nt(){}qe.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var ot=.7,rt=1/ot,it="\\s*([+-]?\\d+)\\s*",at="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",st="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",lt=/^#([0-9a-f]{3,8})$/,ct=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),ut=new RegExp(`^rgb\\(${st},${st},${st}\\)$`),dt=new RegExp(`^rgba\\(${it},${it},${it},${at}\\)$`),ht=new RegExp(`^rgba\\(${st},${st},${st},${at}\\)$`),ft=new RegExp(`^hsl\\(${at},${st},${st}\\)$`),gt=new RegExp(`^hsla\\(${at},${st},${st},${at}\\)$`),pt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function mt(){return this.rgb().formatHex()}function yt(){return this.rgb().formatRgb()}function vt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=lt.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?bt(t):3===n?new Et(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?wt(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?wt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ct.exec(e))?new Et(t[1],t[2],t[3],1):(t=ut.exec(e))?new Et(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=dt.exec(e))?wt(t[1],t[2],t[3],t[4]):(t=ht.exec(e))?wt(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=ft.exec(e))?Pt(t[1],t[2]/100,t[3]/100,1):(t=gt.exec(e))?Pt(t[1],t[2]/100,t[3]/100,t[4]):pt.hasOwnProperty(e)?bt(pt[e]):"transparent"===e?new Et(NaN,NaN,NaN,0):null}function bt(e){return new Et(e>>16&255,e>>8&255,255&e,1)}function wt(e,t,n,o){return o<=0&&(e=t=n=NaN),new Et(e,t,n,o)}function xt(e){return e instanceof nt||(e=vt(e)),e?new Et((e=e.rgb()).r,e.g,e.b,e.opacity):new Et}function St(e,t,n,o){return 1===arguments.length?xt(e):new Et(e,t,n,null==o?1:o)}function Et(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function _t(){return`#${kt(this.r)}${kt(this.g)}${kt(this.b)}`}function Ct(){const e=Mt(this.opacity);return`${1===e?"rgb(":"rgba("}${Nt(this.r)}, ${Nt(this.g)}, ${Nt(this.b)}${1===e?")":`, ${e})`}`}function Mt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Nt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function kt(e){return((e=Nt(e))<16?"0":"")+e.toString(16)}function Pt(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ot(e,t,n,o)}function At(e){if(e instanceof Ot)return new Ot(e.h,e.s,e.l,e.opacity);if(e instanceof nt||(e=vt(e)),!e)return new Ot;if(e instanceof Ot)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new Ot(a,s,l,e.opacity)}function Ot(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function It(e){return(e=(e||0)%360)<0?e+360:e}function Dt(e){return Math.max(0,Math.min(1,e||0))}function zt(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}et(nt,vt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:mt,formatHex:mt,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return At(this).formatHsl()},formatRgb:yt,toString:yt}),et(Et,St,tt(nt,{brighter(e){return e=null==e?rt:Math.pow(rt,e),new Et(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?ot:Math.pow(ot,e),new Et(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Et(Nt(this.r),Nt(this.g),Nt(this.b),Mt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:_t,formatHex:_t,formatHex8:function(){return`#${kt(this.r)}${kt(this.g)}${kt(this.b)}${kt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Ct,toString:Ct})),et(Ot,(function(e,t,n,o){return 1===arguments.length?At(e):new Ot(e,t,n,null==o?1:o)}),tt(nt,{brighter(e){return e=null==e?rt:Math.pow(rt,e),new Ot(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?ot:Math.pow(ot,e),new Ot(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Et(zt(e>=240?e-240:e+120,r,o),zt(e,r,o),zt(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new Ot(It(this.h),Dt(this.s),Dt(this.l),Mt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Mt(this.opacity);return`${1===e?"hsl(":"hsla("}${It(this.h)}, ${100*Dt(this.s)}%, ${100*Dt(this.l)}%${1===e?")":`, ${e})`}`}}));var Rt=e=>()=>e;function $t(e){return 1==(e=+e)?Tt:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):Rt(isNaN(t)?n:t)}}function Tt(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Rt(isNaN(e)?t:e)}var Bt=function e(t){var n=$t(t);function o(e,t){var o=n((e=St(e)).r,(t=St(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=Tt(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function Ht(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Lt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Vt=new RegExp(Lt.source,"g");function Xt(e,t){var n,o,r,i=Lt.lastIndex=Vt.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=Lt.exec(e))&&(o=Vt.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:Ht(n,o)})),i=Vt.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}var Yt,Kt=180/Math.PI,Zt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Ft(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Kt,skewX:Math.atan(l)*Kt,scaleX:a,scaleY:s}}function jt(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:Ht(e,r)},{i:l-2,x:Ht(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Ht(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Ht(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Ht(e,n)},{i:s-2,x:Ht(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var Wt=jt((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Zt:Ft(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),qt=jt((function(e){return null==e?Zt:(Yt||(Yt=document.createElementNS("http://www.w3.org/2000/svg","g")),Yt.setAttribute("transform",e),(e=Yt.transform.baseVal.consolidate())?Ft((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Zt)}),", ",")",")");function Ut(e){return((e=Math.exp(e))+1/e)/2}var Gt,Qt,Jt=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),b=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-b)/t,i=function(e){var o,r=e*a,i=Ut(b),u=c/(n*m)*(i*(o=t*r+b,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[s+u*f,l+u*g,c*i/Ut(t*r+b)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),en=0,tn=0,nn=0,on=0,rn=0,an=0,sn="object"==typeof performance&&performance.now?performance:Date,ln="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function cn(){return rn||(ln(un),rn=sn.now()+an)}function un(){rn=0}function dn(){this._call=this._time=this._next=null}function hn(e,t,n){var o=new dn;return o.restart(e,t,n),o}function fn(){rn=(on=sn.now())+an,en=tn=0;try{!function(){cn(),++en;for(var e,t=Gt;t;)(e=rn-t._time)>=0&&t._call.call(void 0,e),t=t._next;--en}()}finally{en=0,function(){var e,t,n=Gt,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:Gt=t);Qt=e,pn(o)}(),rn=0}}function gn(){var e=sn.now(),t=e-on;t>1e3&&(an-=t,on=e)}function pn(e){en||(tn&&(tn=clearTimeout(tn)),e-rn>24?(e<1/0&&(tn=setTimeout(fn,e-sn.now()-an)),nn&&(nn=clearInterval(nn))):(nn||(on=sn.now(),nn=setInterval(gn,1e3)),en=1,ln(fn)))}function mn(e,t,n){var o=new dn;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}dn.prototype=hn.prototype={constructor:dn,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?cn():+n)+(null==t?0:+t),this._next||Qt===this||(Qt?Qt._next=this:Gt=this,Qt=this),this._call=e,this._time=n,pn()},stop:function(){this._call&&(this._call=null,this._time=1/0,pn())}};var yn=x("start","end","cancel","interrupt"),vn=[];function bn(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(1!==n.state)return l();for(c in r)if((h=r[c]).name===n.name){if(3===h.state)return mn(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(mn((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=3,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=hn(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:yn,tween:vn,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function wn(e,t){var n=Sn(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function xn(e,t){var n=Sn(e,t);if(n.state>3)throw new Error("too late; already running");return n}function Sn(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function En(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function _n(e,t){var n,o;return function(){var r=xn(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function Cn(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=xn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function Mn(e,t,n){var o=e._id;return e.each((function(){var e=xn(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return Sn(e,o).value[t]}}function Nn(e,t){var n;return("number"==typeof t?Ht:t instanceof vt?Bt:(n=vt(t))?(t=n,Bt):Xt)(e,t)}function kn(e){return function(){this.removeAttribute(e)}}function Pn(e){return function(){this.removeAttributeNS(e.space,e.local)}}function An(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function On(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function In(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function Dn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function zn(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Rn(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function $n(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Rn(e,r)),n}return r._value=t,r}function Tn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&zn(e,r)),n}return r._value=t,r}function Bn(e,t){return function(){wn(this,e).delay=+t.apply(this,arguments)}}function Hn(e,t){return t=+t,function(){wn(this,e).delay=t}}function Ln(e,t){return function(){xn(this,e).duration=+t.apply(this,arguments)}}function Vn(e,t){return t=+t,function(){xn(this,e).duration=t}}function Xn(e,t){if("function"!=typeof t)throw new Error;return function(){xn(this,e).ease=t}}function Yn(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?wn:xn;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}var Kn=He.prototype.constructor;function Zn(e){return function(){this.style.removeProperty(e)}}function Fn(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function jn(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&Fn(e,i,n)),o}return i._value=t,i}function Wn(e){return function(t){this.textContent=e.call(this,t)}}function qn(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Wn(o)),t}return o._value=e,o}var Un=0;function Gn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function Qn(){return++Un}var Jn=He.prototype;Gn.prototype={constructor:Gn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=D(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,bn(d[h],t,n,h,d,Sn(s,n)));return new Gn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=$(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=Sn(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&bn(h,t,n,p,f,g);i.push(f),a.push(l)}return new Gn(i,a,t,n)},selectChild:Jn.selectChild,selectChildren:Jn.selectChildren,filter:function(e){"function"!=typeof e&&(e=T(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Gn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new Gn(a,this._parents,this._name,this._id)},selection:function(){return new Kn(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=Qn(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=Sn(a,t);bn(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new Gn(o,this._parents,e,n)},call:Jn.call,nodes:Jn.nodes,node:Jn.node,size:Jn.size,empty:Jn.empty,each:Jn.each,on:function(e,t){var n=this._id;return arguments.length<2?Sn(this.node(),n).on.on(e):this.each(Yn(n,e,t))},attr:function(e,t){var n=k(e),o="transform"===n?qt:Nn;return this.attrTween(e,"function"==typeof t?(n.local?Dn:In)(n,o,Mn(this,"attr."+e,t)):null==t?(n.local?Pn:kn)(n):(n.local?On:An)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=k(e);return this.tween(n,(o.local?$n:Tn)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?Wt:Nn;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=se(this,e),a=(this.style.removeProperty(e),se(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,Zn(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=se(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=se(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,Mn(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=xn(this,e),c=l.on,u=null==l.value[a]?i||(i=Zn(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=se(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,jn(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(Mn(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,qn(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=Sn(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?_n:Cn)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Bn:Hn)(t,e)):Sn(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Ln:Vn)(t,e)):Sn(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(Xn(t,e)):Sn(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;xn(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=xn(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:Jn[Symbol.iterator]};var eo={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function to(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}He.prototype.interrupt=function(e){return this.each((function(){En(this,e)}))},He.prototype.transition=function(e){var t,n;e instanceof Gn?(t=e._id,e=e._name):(t=Qn(),(n=eo).time=cn(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&bn(a,e,t,c,s,n||to(a,t));return new Gn(o,this._parents,e,t)};var no=e=>()=>e;function oo(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function ro(e,t,n){this.k=e,this.x=t,this.y=n}ro.prototype={constructor:ro,scale:function(e){return 1===e?this:new ro(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new ro(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var io=new ro(1,0,0);function ao(e){e.stopImmediatePropagation()}function so(e){e.preventDefault(),e.stopImmediatePropagation()}function lo(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function co(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function uo(){return this.__zoom||io}function ho(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function fo(){return navigator.maxTouchPoints||"ontouchstart"in this}function go(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}ro.prototype;const po=t.createContext(null),mo=po.Provider,yo=e=>`Node type "${e}" not found. Using fallback type "default".`,vo=()=>"The React Flow parent container needs a width and a height to render the graph.",bo=()=>"Only child nodes can use a parent extent.",wo=e=>`Marker type "${e}" doesn't exist.`,xo=(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,So=()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",Eo=e=>`Edge type "${e}" not found. Using fallback type "default".`,_o=e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,Co=(()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001")();function Mo(e,n){const o=t.useContext(po);if(null===o)throw new Error(Co);return y(o,e,n)}const No=()=>{const e=t.useContext(po);if(null===e)throw new Error(Co);return t.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy})),[e])},ko=e=>e.userSelectionActive?"none":"all";function Po({position:e,children:o,className:r,style:i,...a}){const s=Mo(ko),l=`${e}`.split("-");return t.createElement("div",{className:n(["react-flow__panel",r,...l]),style:{...i,pointerEvents:s},...a},o)}function Ao({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.createElement(Po,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},t.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var Oo=t.memo((({x:e,y:o,label:r,labelStyle:i={},labelShowBg:a=!0,labelBgStyle:s={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:d,...h})=>{const f=t.useRef(null),[g,p]=t.useState({x:0,y:0,width:0,height:0}),m=n(["react-flow__edge-textwrapper",d]);return t.useEffect((()=>{if(f.current){const e=f.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[r]),void 0!==r&&r?t.createElement("g",{transform:`translate(${e-g.width/2} ${o-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...h},a&&t.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:s,rx:c,ry:c}),t.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:f,style:i},r),u):null}));const Io=e=>({width:e.offsetWidth,height:e.offsetHeight}),Do=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),zo=(e={x:0,y:0},t)=>({x:Do(e.x,t[0][0],t[1][0]),y:Do(e.y,t[0][1],t[1][1])}),Ro=(e,t,n)=>e<t?Do(Math.abs(e-t),1,50)/50:e>n?-Do(Math.abs(e-n),1,50)/50:0,$o=(e,t)=>[20*Ro(e.x,35,t.width-35),20*Ro(e.y,35,t.height-35)],To=e=>e.getRootNode?.()||window?.document,Bo=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Ho=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),Lo=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},Vo=e=>!isNaN(e)&&isFinite(e),Xo=Symbol.for("internals"),Yo=["Enter"," ","Escape"];function Ko(e){const t=((e=>"nativeEvent"in e)(e)?e.nativeEvent:e).composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(t?.nodeName)||t?.hasAttribute("contenteditable")||!!t?.closest(".nokey")}const Zo=e=>"clientX"in e,Fo=(e,t)=>{const n=Zo(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},jo=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,Wo=({id:e,path:n,labelX:o,labelY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g=20})=>t.createElement(t.Fragment,null,t.createElement("path",{id:e,style:d,d:n,fill:"none",className:"react-flow__edge-path",markerEnd:h,markerStart:f}),g&&t.createElement("path",{d:n,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&Vo(o)&&Vo(r)?t.createElement(Oo,{x:o,y:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null);function qo(e,t,n){return void 0===n?n:o=>{const r=t().edges.find((t=>t.id===e));r&&n(o,{...r})}}function Uo({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function Go({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}var Qo,Jo,er,tr,nr,or;function rr({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===or.Left||e===or.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function ir({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top}){const[a,s]=rr({pos:n,x1:e,y1:t,x2:o,y2:r}),[l,c]=rr({pos:i,x1:o,y1:r,x2:e,y2:t}),[u,d,h,f]=Go({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:s,targetControlX:l,targetControlY:c});return[`M${e},${t} C${a},${s} ${l},${c} ${o},${r}`,u,d,h,f]}Wo.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(Qo||(Qo={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(Jo||(Jo={})),function(e){e.Partial="partial",e.Full="full"}(er||(er={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(tr||(tr={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(nr||(nr={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(or||(or={}));const ar=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=or.Bottom,targetPosition:a=or.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})=>{const[y,v,b]=ir({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a});return t.createElement(Wo,{path:y,labelX:v,labelY:b,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})}));ar.displayName="SimpleBezierEdge";const sr={[or.Left]:{x:-1,y:0},[or.Right]:{x:1,y:0},[or.Top]:{x:0,y:-1},[or.Bottom]:{x:0,y:1}},lr=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function cr({source:e,sourcePosition:t=or.Bottom,target:n,targetPosition:o=or.Top,center:r,offset:i}){const a=sr[t],s=sr[o],l={x:e.x+a.x*i,y:e.y+a.y*i},c={x:n.x+s.x*i,y:n.y+s.y*i},u=(({source:e,sourcePosition:t=or.Bottom,target:n})=>t===or.Left||t===or.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1})({source:l,sourcePosition:t,target:c}),d=0!==u.x?"x":"y",h=u[d];let f,g,p=[];const m={x:0,y:0},y={x:0,y:0},[v,b,w,x]=Uo({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]==-1){f=r.x??v,g=r.y??b;const e=[{x:f,y:l.y},{x:f,y:c.y}],t=[{x:l.x,y:g},{x:c.x,y:g}];p=a[d]===h?"x"===d?e:t:"x"===d?t:e}else{const r=[{x:l.x,y:c.y}],u=[{x:c.x,y:l.y}];if(p="x"===d?a.x===h?u:r:a.y===h?r:u,t===o){const t=Math.abs(e[d]-n[d]);if(t<=i){const o=Math.min(i-1,i-t);a[d]===h?m[d]=(l[d]>e[d]?-1:1)*o:y[d]=(c[d]>n[d]?-1:1)*o}}if(t!==o){const e="x"===d?"y":"x",t=a[d]===s[e],n=l[e]>c[e],o=l[e]<c[e];(1===a[d]&&(!t&&n||t&&o)||1!==a[d]&&(!t&&o||t&&n))&&(p="x"===d?r:u)}const v={x:l.x+m.x,y:l.y+m.y},b={x:c.x+y.x,y:c.y+y.y};Math.max(Math.abs(v.x-p[0].x),Math.abs(b.x-p[0].x))>=Math.max(Math.abs(v.y-p[0].y),Math.abs(b.y-p[0].y))?(f=(v.x+b.x)/2,g=p[0].y):(f=p[0].x,g=(v.y+b.y)/2)}return[[e,{x:l.x+m.x,y:l.y+m.y},...p,{x:c.x+y.x,y:c.y+y.y},n],f,g,w,x]}function ur({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top,borderRadius:a=5,centerX:s,centerY:l,offset:c=20}){const[u,d,h,f,g]=cr({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:i,center:{x:s,y:l},offset:c});return[u.reduce(((e,t,n)=>{let o="";return o=n>0&&n<u.length-1?function(e,t,n,o){const r=Math.min(lr(e,t)/2,lr(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(u[n-1],t,u[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),d,h,f,g]}const dr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,sourcePosition:h=or.Bottom,targetPosition:f=or.Top,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,w]=ur({sourceX:e,sourceY:n,sourcePosition:h,targetX:o,targetY:r,targetPosition:f,borderRadius:m?.borderRadius,offset:m?.offset});return t.createElement(Wo,{path:v,labelX:b,labelY:w,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:g,markerStart:p,interactionWidth:y})}));dr.displayName="SmoothStepEdge";const hr=t.memo((e=>t.createElement(dr,{...e,pathOptions:t.useMemo((()=>({borderRadius:0,offset:e.pathOptions?.offset})),[e.pathOptions?.offset])})));hr.displayName="StepEdge";const fr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})=>{const[p,m,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=Uo({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}({sourceX:e,sourceY:n,targetX:o,targetY:r});return t.createElement(Wo,{path:p,labelX:m,labelY:y,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})}));function gr(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function pr({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){switch(e){case or.Left:return[t-gr(t-o,i),n];case or.Right:return[t+gr(o-t,i),n];case or.Top:return[t,n-gr(n-r,i)];case or.Bottom:return[t,n+gr(r-n,i)]}}function mr({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top,curvature:a=.25}){const[s,l]=pr({pos:n,x1:e,y1:t,x2:o,y2:r,c:a}),[c,u]=pr({pos:i,x1:o,y1:r,x2:e,y2:t,c:a}),[d,h,f,g]=Go({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${e},${t} C${s},${l} ${c},${u} ${o},${r}`,d,h,f,g]}fr.displayName="StraightEdge";const yr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=or.Bottom,targetPosition:a=or.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,w]=mr({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a,curvature:m?.curvature});return t.createElement(Wo,{path:v,labelX:b,labelY:w,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:y})}));yr.displayName="BezierEdge";const vr=t.createContext(null),br=vr.Provider;vr.Consumer;const wr=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,xr=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`},Sr=({x:e,y:t},[n,o,r],i,[a,s])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?{x:a*Math.round(l.x/a),y:s*Math.round(l.y/s)}:l},Er=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),_r=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},Cr=(e,t=[0,0])=>{if(0===e.length)return{x:0,y:0,width:0,height:0};return(({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}))(e.reduce(((e,n)=>{const{x:o,y:r}=_r(n,t).positionAbsolute;return i=e,a=Bo({x:o,y:r,width:n.width||0,height:n.height||0}),{x:Math.min(i.x,a.x),y:Math.min(i.y,a.y),x2:Math.max(i.x2,a.x2),y2:Math.max(i.y2,a.y2)};var i,a}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0}))},Mr=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const l={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},c=[];return e.forEach((e=>{const{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;const{positionAbsolute:u}=_r(e,s),d={x:u.x,y:u.y,width:t||0,height:n||0},h=Lo(l,d);(void 0===t||void 0===n||null===t||null===n||i&&h>0||h>=(t||0)*(n||0)||e.dragging)&&c.push(e)})),c},Nr=(e,t)=>{const n=e.map((e=>e.id));return t.filter((e=>n.includes(e.source)||n.includes(e.target)))},kr=(e,t,n,o,r,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),l=Math.min(a,s),c=Do(l,o,r);return{x:t/2-(e.x+e.width/2)*c,y:n/2-(e.y+e.height/2)*c,zoom:c}},Pr=(e,t=0)=>e.transition().duration(t);function Ar(e,t,n,o){return(t[n]||[]).reduce(((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t)),[])}const Or={source:null,target:null,sourceHandle:null,targetHandle:null},Ir=()=>({handleDomNode:null,isValid:!1,connection:Or,endHandle:null});function Dr(e,t,n,o,r,i,a){const s="target"===r,l=a.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...Ir(),handleDomNode:l};if(l){const e=zr(void 0,l),r=l.getAttribute("data-nodeid"),a=l.getAttribute("data-handleid"),u=l.classList.contains("connectable"),d=l.classList.contains("connectableend"),h={source:s?r:n,sourceHandle:s?a:o,target:s?n:r,targetHandle:s?o:a};c.connection=h;u&&d&&(t===Qo.Strict?s&&"source"===e||!s&&"target"===e:r!==n||a!==o)&&(c.endHandle={nodeId:r,handleId:a,type:e},c.isValid=i(h))}return c}function zr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function Rr(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function $r(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Tr({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){const u=To(e.target),{connectionMode:d,domNode:h,autoPanOnConnect:f,connectionRadius:g,onConnectStart:p,panBy:m,getNodes:y,cancelConnection:v}=i();let b,w=0;const{x:x,y:S}=Fo(e),E=u?.elementFromPoint(x,S),_=zr(l,E),C=h?.getBoundingClientRect();if(!C||!_)return;let M,N=Fo(e,C),k=!1,P=null,A=!1,O=null;const I=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce(((e,r)=>{if(r[Xo]){const{handleBounds:i}=r[Xo];let a=[],s=[];i&&(a=Ar(r,i,"source",`${t}-${n}-${o}`),s=Ar(r,i,"target",`${t}-${n}-${o}`)),e.push(...a,...s)}return e}),[])}({nodes:y(),nodeId:n,handleId:t,handleType:_}),D=()=>{if(!f)return;const[e,t]=$o(N,C);m({x:e,y:t}),w=requestAnimationFrame(D)};function z(e){const{transform:o}=i();N=Fo(e,C);const{handle:l,validHandleResult:c}=function(e,t,n,o,r,i){const{x:a,y:s}=Fo(e),l=t.elementsFromPoint(a,s).find((e=>e.classList.contains("react-flow__handle")));if(l){const e=l.getAttribute("data-nodeid");if(e){const t=zr(void 0,l),o=l.getAttribute("data-handleid"),a=i({nodeId:e,id:o,type:t});if(a){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(r.forEach((e=>{const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}})),!c.length)return{handle:null,validHandleResult:Ir()};if(1===c.length)return c[0];const d=c.some((({validHandleResult:e})=>e.isValid)),h=c.some((({handle:e})=>"target"===e.type));return c.find((({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid))||c[0]}(e,u,Sr(N,o,!1,[1,1]),g,I,(e=>Dr(e,d,n,t,r?"target":"source",s,u)));if(b=l,k||(D(),k=!0),O=c.handleDomNode,P=c.connection,A=c.isValid,a({connectionPosition:b&&A?Er({x:b.x,y:b.y},o):N,connectionStatus:$r(!!b,A),connectionEndHandle:c.endHandle}),!b&&!A&&!O)return Rr(M);P.source!==P.target&&O&&(Rr(M),M=O,O.classList.add("connecting","react-flow__handle-connecting"),O.classList.toggle("valid",A),O.classList.toggle("react-flow__handle-valid",A))}function R(e){(b||O)&&P&&A&&o?.(P),i().onConnectEnd?.(e),l&&c?.(e),Rr(M),v(),cancelAnimationFrame(w),k=!1,A=!1,P=null,O=null,u.removeEventListener("mousemove",z),u.removeEventListener("mouseup",R),u.removeEventListener("touchmove",z),u.removeEventListener("touchend",R)}a({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:_,connectionStartHandle:{nodeId:n,handleId:t,type:_},connectionEndHandle:null}),p?.(e,{nodeId:n,handleId:t,handleType:_}),u.addEventListener("mousemove",z),u.addEventListener("mouseup",R),u.addEventListener("touchmove",z),u.addEventListener("touchend",R)}const Br=()=>!0,Hr=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),Lr=t.forwardRef((({type:e="source",position:o=or.Top,isValidConnection:r,isConnectable:i=!0,isConnectableStart:a=!0,isConnectableEnd:s=!0,id:l,onConnect:c,children:u,className:d,onMouseDown:h,onTouchStart:f,...g},p)=>{const m=l||null,y="target"===e,v=No(),w=t.useContext(vr),{connectOnClick:x,noPanClassName:S}=Mo(Hr,b),{connecting:E,clickConnecting:_}=Mo(((e,t,n)=>o=>{const{connectionStartHandle:r,connectionEndHandle:i,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}})(w,m,e),b);w||v.getState().onError?.("010",So());const C=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=v.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=v.getState();t(((e,t)=>{if(!e.source||!e.target)return t;let n;var o;return n="id"in(o=e)&&"source"in o&&"target"in o?{...e}:{...e,id:wr(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:t.concat(n)})(r,e))}n?.(r),c?.(r)},M=e=>{if(!w)return;const t=Zo(e);a&&(t&&0===e.button||!t)&&Tr({event:e,handleId:m,nodeId:w,onConnect:C,isTarget:y,getState:v.getState,setState:v.setState,isValidConnection:r||v.getState().isValidConnection||Br}),t?h?.(e):f?.(e)};return t.createElement("div",{"data-handleid":m,"data-nodeid":w,"data-handlepos":o,"data-id":`${w}-${m}-${e}`,className:n(["react-flow__handle",`react-flow__handle-${o}`,"nodrag",S,d,{source:!y,target:y,connectable:i,connectablestart:a,connectableend:s,connecting:_,connectionindicator:i&&(a&&!E||s&&E)}]),onMouseDown:M,onTouchStart:M,onClick:x?t=>{const{onClickConnectStart:n,onClickConnectEnd:o,connectionClickStartHandle:i,connectionMode:s,isValidConnection:l}=v.getState();if(!w||!i&&!a)return;if(!i)return n?.(t,{nodeId:w,handleId:m,handleType:e}),void v.setState({connectionClickStartHandle:{nodeId:w,type:e,handleId:m}});const c=To(t.target),u=r||l||Br,{connection:d,isValid:h}=Dr({nodeId:w,id:m,type:e},s,i.nodeId,i.handleId||null,i.type,u,c);h&&C(d),o?.(t),v.setState({connectionClickStartHandle:null})}:void 0,ref:p,...g},u)}));Lr.displayName="Handle";var Vr=t.memo(Lr);const Xr=({data:e,isConnectable:n,targetPosition:o=or.Top,sourcePosition:r=or.Bottom})=>t.createElement(t.Fragment,null,t.createElement(Vr,{type:"target",position:o,isConnectable:n}),e?.label,t.createElement(Vr,{type:"source",position:r,isConnectable:n}));Xr.displayName="DefaultNode";var Yr=t.memo(Xr);const Kr=({data:e,isConnectable:n,sourcePosition:o=or.Bottom})=>t.createElement(t.Fragment,null,e?.label,t.createElement(Vr,{type:"source",position:o,isConnectable:n}));Kr.displayName="InputNode";var Zr=t.memo(Kr);const Fr=({data:e,isConnectable:n,targetPosition:o=or.Top})=>t.createElement(t.Fragment,null,t.createElement(Vr,{type:"target",position:o,isConnectable:n}),e?.label);Fr.displayName="OutputNode";var jr=t.memo(Fr);const Wr=()=>null;Wr.displayName="GroupNode";const qr=e=>({selectedNodes:e.getNodes().filter((e=>e.selected)),selectedEdges:e.edges.filter((e=>e.selected)).map((e=>({...e})))}),Ur=e=>e.id;function Gr(e,t){return b(e.selectedNodes.map(Ur),t.selectedNodes.map(Ur))&&b(e.selectedEdges.map(Ur),t.selectedEdges.map(Ur))}const Qr=t.memo((({onSelectionChange:e})=>{const n=No(),{selectedNodes:o,selectedEdges:r}=Mo(qr,Gr);return t.useEffect((()=>{const t={nodes:o,edges:r};e?.(t),n.getState().onSelectionChange.forEach((e=>e(t)))}),[o,r,e]),null}));Qr.displayName="SelectionListener";const Jr=e=>!!e.onSelectionChange;function ei({onSelectionChange:e}){const n=Mo(Jr);return e||n?t.createElement(Qr,{onSelectionChange:e}):null}const ti=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function ni(e,n){t.useEffect((()=>{void 0!==e&&n(e)}),[e])}function oi(e,n,o){t.useEffect((()=>{void 0!==n&&o({[e]:n})}),[n])}const ri=({nodes:e,edges:n,defaultNodes:o,defaultEdges:r,onConnect:i,onConnectStart:a,onConnectEnd:s,onClickConnectStart:l,onClickConnectEnd:c,nodesDraggable:u,nodesConnectable:d,nodesFocusable:h,edgesFocusable:f,edgesUpdatable:g,elevateNodesOnSelect:p,minZoom:m,maxZoom:y,nodeExtent:v,onNodesChange:w,onEdgesChange:x,elementsSelectable:S,connectionMode:E,snapGrid:_,snapToGrid:C,translateExtent:M,connectOnClick:N,defaultEdgeOptions:k,fitView:P,fitViewOptions:A,onNodesDelete:O,onEdgesDelete:I,onNodeDrag:D,onNodeDragStart:z,onNodeDragStop:R,onSelectionDrag:$,onSelectionDragStart:T,onSelectionDragStop:B,noPanClassName:H,nodeOrigin:L,rfId:V,autoPanOnConnect:X,autoPanOnNodeDrag:Y,onError:K,connectionRadius:Z,isValidConnection:F,nodeDragThreshold:j})=>{const{setNodes:W,setEdges:q,setDefaultNodesAndEdges:U,setMinZoom:G,setMaxZoom:Q,setTranslateExtent:J,setNodeExtent:ee,reset:te}=Mo(ti,b),ne=No();return t.useEffect((()=>{const e=r?.map((e=>({...e,...k})));return U(o,e),()=>{te()}}),[]),oi("defaultEdgeOptions",k,ne.setState),oi("connectionMode",E,ne.setState),oi("onConnect",i,ne.setState),oi("onConnectStart",a,ne.setState),oi("onConnectEnd",s,ne.setState),oi("onClickConnectStart",l,ne.setState),oi("onClickConnectEnd",c,ne.setState),oi("nodesDraggable",u,ne.setState),oi("nodesConnectable",d,ne.setState),oi("nodesFocusable",h,ne.setState),oi("edgesFocusable",f,ne.setState),oi("edgesUpdatable",g,ne.setState),oi("elementsSelectable",S,ne.setState),oi("elevateNodesOnSelect",p,ne.setState),oi("snapToGrid",C,ne.setState),oi("snapGrid",_,ne.setState),oi("onNodesChange",w,ne.setState),oi("onEdgesChange",x,ne.setState),oi("connectOnClick",N,ne.setState),oi("fitViewOnInit",P,ne.setState),oi("fitViewOnInitOptions",A,ne.setState),oi("onNodesDelete",O,ne.setState),oi("onEdgesDelete",I,ne.setState),oi("onNodeDrag",D,ne.setState),oi("onNodeDragStart",z,ne.setState),oi("onNodeDragStop",R,ne.setState),oi("onSelectionDrag",$,ne.setState),oi("onSelectionDragStart",T,ne.setState),oi("onSelectionDragStop",B,ne.setState),oi("noPanClassName",H,ne.setState),oi("nodeOrigin",L,ne.setState),oi("rfId",V,ne.setState),oi("autoPanOnConnect",X,ne.setState),oi("autoPanOnNodeDrag",Y,ne.setState),oi("onError",K,ne.setState),oi("connectionRadius",Z,ne.setState),oi("isValidConnection",F,ne.setState),oi("nodeDragThreshold",j,ne.setState),ni(e,W),ni(n,q),ni(m,G),ni(y,Q),ni(M,J),ni(v,ee),null},ii={display:"none"},ai={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},si="react-flow__node-desc",li="react-flow__edge-desc",ci=e=>e.ariaLiveMessage;function ui({rfId:e}){const n=Mo(ci);return t.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:ai},n)}function di({rfId:e,disableKeyboardA11y:n}){return t.createElement(t.Fragment,null,t.createElement("div",{id:`${si}-${e}`,style:ii},"Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),t.createElement("div",{id:`${li}-${e}`,style:ii},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!n&&t.createElement(ui,{rfId:e}))}var hi=(e=null,n={actInsideInputWithModifier:!0})=>{const[o,r]=t.useState(!1),i=t.useRef(!1),a=t.useRef(new Set([])),[s,l]=t.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.split("+"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return t.useEffect((()=>{const t="undefined"!=typeof document?document:null,o=n?.target||t;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Ko(e))return!1;const t=gi(e.code,l);a.current.add(e[t]),fi(s,a.current,!1)&&(e.preventDefault(),r(!0))},t=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Ko(e))return!1;const t=gi(e.code,l);fi(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),r(!1)};return o?.addEventListener("keydown",e),o?.addEventListener("keyup",t),window.addEventListener("blur",c),()=>{o?.removeEventListener("keydown",e),o?.removeEventListener("keyup",t),window.removeEventListener("blur",c)}}}),[e,r]),o};function fi(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function gi(e,t){return t.includes(e)?"code":"key"}function pi(e,t,n,o){const r=e.parentNode||e.parentId;if(!r)return n;const i=t.get(r),a=_r(i,o);return pi(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(i[Xo]?.z??0)>(n.z??0)?i[Xo]?.z??0:n.z??0},o)}function mi(e,t,n){e.forEach((o=>{const r=o.parentNode||o.parentId;if(r&&!e.has(r))throw new Error(`Parent node ${r} not found`);if(r||n?.[o.id]){const{x:r,y:i,z:a}=pi(o,e,{...o.position,z:o[Xo]?.z??0},t);o.positionAbsolute={x:r,y:i},o[Xo].z=a,n?.[o.id]&&(o[Xo].isParent=!0)}}))}function yi(e,t,n,o){const r=new Map,i={},a=o?1e3:0;return e.forEach((e=>{const n=(Vo(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(i[l]=!0);const c=o?.type&&o?.type!==e.type;Object.defineProperty(s,Xo,{enumerable:!1,value:{handleBounds:c?void 0:o?.[Xo]?.handleBounds,z:n}}),r.set(e.id,s)})),mi(r,n,i),r}function vi(e,t={}){const{getNodes:n,width:o,height:r,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){const e=n().filter((e=>{const n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some((t=>t.id===e.id)):n})),c=e.every((e=>e.width&&e.height));if(e.length>0&&c){const n=Cr(e,d),{x:c,y:u,zoom:h}=kr(n,o,r,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=io.translate(c,u).scale(h);return"number"==typeof t.duration&&t.duration>0?s.transform(Pr(l,t.duration),f):s.transform(l,f),!0}}return!1}function bi(e,t){return e.forEach((e=>{const n=t.get(e.id);n&&t.set(n.id,{...n,[Xo]:n[Xo],selected:e.selected})})),new Map(t)}function wi(e,t){return t.map((t=>{const n=e.find((e=>e.id===t.id));return n&&(t.selected=n.selected),t}))}function xi({changedNodes:e,changedEdges:t,get:n,set:o}){const{nodeInternals:r,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&o({nodeInternals:bi(e,r)}),a?.(e)),t?.length&&(c&&o({edges:wi(t,i)}),s?.(t))}const Si=()=>{},Ei={zoomIn:Si,zoomOut:Si,zoomTo:Si,getZoom:()=>1,setViewport:Si,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:Si,fitBounds:Si,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},_i=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection});function Ci(){const e=(()=>{const e=No(),{d3Zoom:n,d3Selection:o}=Mo(_i,b),r=t.useMemo((()=>o&&n?{zoomIn:e=>n.scaleBy(Pr(o,e?.duration),1.2),zoomOut:e=>n.scaleBy(Pr(o,e?.duration),1/1.2),zoomTo:(e,t)=>n.scaleTo(Pr(o,t?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(t,r)=>{const[i,a,s]=e.getState().transform,l=io.translate(t.x??i,t.y??a).scale(t.zoom??s);n.transform(Pr(o,r?.duration),l)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>vi(e.getState,t),setCenter:(t,r,i)=>{const{width:a,height:s,maxZoom:l}=e.getState(),c=void 0!==i?.zoom?i.zoom:l,u=a/2-t*c,d=s/2-r*c,h=io.translate(u,d).scale(c);n.transform(Pr(o,i?.duration),h)},fitBounds:(t,r)=>{const{width:i,height:a,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=kr(t,i,a,s,l,r?.padding??.1),h=io.translate(c,u).scale(d);n.transform(Pr(o,r?.duration),h)},project:t=>{const{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),Sr(t,n,o,r)},screenToFlowPosition:t=>{const{transform:n,snapToGrid:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;const{x:a,y:s}=i.getBoundingClientRect(),l={x:t.x-a,y:t.y-s};return Sr(l,n,o,r)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=Er(t,n);return{x:a.x+r,y:a.y+i}},viewportInitialized:!0}:Ei),[n,o]);return r})(),n=No(),o=t.useCallback((()=>n.getState().getNodes().map((e=>({...e})))),[]),r=t.useCallback((e=>n.getState().nodeInternals.get(e)),[]),i=t.useCallback((()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))}),[]),a=t.useCallback((e=>{const{edges:t=[]}=n.getState();return t.find((t=>t.id===e))}),[]),s=t.useCallback((e=>{const{getNodes:t,setNodes:o,hasDefaultNodes:r,onNodesChange:i}=n.getState(),a=t(),s="function"==typeof e?e(a):e;if(r)o(s);else if(i){i(0===s.length?a.map((e=>({type:"remove",id:e.id}))):s.map((e=>({item:e,type:"reset"}))))}}),[]),l=t.useCallback((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i}=n.getState(),a="function"==typeof e?e(t):e;if(r)o(a);else if(i){i(0===a.length?t.map((e=>({type:"remove",id:e.id}))):a.map((e=>({item:e,type:"reset"}))))}}),[]),c=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:i,onNodesChange:a}=n.getState();if(i){r([...o(),...t])}else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),u=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:i,onEdgesChange:a}=n.getState();if(i)r([...o,...t]);else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),d=t.useCallback((()=>{const{getNodes:e,edges:t=[],transform:o}=n.getState(),[r,i,a]=o;return{nodes:e().map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}}),[]),h=t.useCallback((({nodes:e,edges:t})=>{const{nodeInternals:o,getNodes:r,edges:i,hasDefaultNodes:a,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=n.getState(),h=(e||[]).map((e=>e.id)),f=(t||[]).map((e=>e.id)),g=r().reduce(((e,t)=>{const n=t.parentNode||t.parentId,o=!h.includes(t.id)&&n&&e.find((e=>e.id===n));return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||o)&&e.push(t),e}),[]),p=i.filter((e=>"boolean"!=typeof e.deletable||e.deletable)),m=p.filter((e=>f.includes(e.id)));if(g||m){const e=Nr(g,p),t=[...m,...e],r=t.reduce(((e,t)=>(e.includes(t.id)||e.push(t.id),e)),[]);if((s||a)&&(s&&n.setState({edges:i.filter((e=>!r.includes(e.id)))}),a&&(g.forEach((e=>{o.delete(e.id)})),n.setState({nodeInternals:new Map(o)}))),r.length>0&&(c?.(t),d&&d(r.map((e=>({id:e,type:"remove"}))))),g.length>0&&(l?.(g),u)){u(g.map((e=>({id:e.id,type:"remove"}))))}}}),[]),f=t.useCallback((e=>{const t=Vo((o=e).width)&&Vo(o.height)&&Vo(o.x)&&Vo(o.y);var o;const r=t?null:n.getState().nodeInternals.get(e.id);if(!t&&!r)return[null,null,t];return[t?e:Ho(r),r,t]}),[]),g=t.useCallback(((e,t=!0,o)=>{const[r,i,a]=f(e);return r?(o||n.getState().getNodes()).filter((e=>{if(!(a||e.id!==i.id&&e.positionAbsolute))return!1;const n=Ho(e),o=Lo(n,r);return t&&o>0||o>=r.width*r.height})):[]}),[]),p=t.useCallback(((e,t,n=!0)=>{const[o]=f(e);if(!o)return!1;const r=Lo(o,t);return n&&r>0||r>=o.width*o.height}),[]);return t.useMemo((()=>({...e,getNodes:o,getNode:r,getEdges:i,getEdge:a,setNodes:s,setEdges:l,addNodes:c,addEdges:u,toObject:d,deleteElements:h,getIntersectingNodes:g,isNodeIntersecting:p})),[e,o,r,i,a,s,l,c,u,d,h,g,p])}const Mi={actInsideInputWithModifier:!1};const Ni={position:"absolute",width:"100%",height:"100%",top:0,left:0},ki=e=>({x:e.x,y:e.y,zoom:e.k}),Pi=(e,t)=>e.target.closest(`.${t}`),Ai=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Oi=e=>{const t=e.ctrlKey&&jo()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},Ii=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),Di=({onMove:e,onMoveStart:n,onMoveEnd:o,onPaneContextMenu:r,zoomOnScroll:i=!0,zoomOnPinch:a=!0,panOnScroll:s=!1,panOnScrollSpeed:l=.5,panOnScrollMode:c=Jo.Free,zoomOnDoubleClick:u=!0,elementsSelectable:d,panOnDrag:h=!0,defaultViewport:f,translateExtent:g,minZoom:p,maxZoom:m,zoomActivationKeyCode:y,preventScrolling:v=!0,children:w,noWheelClassName:S,noPanClassName:E})=>{const _=t.useRef(),C=No(),M=t.useRef(!1),N=t.useRef(!1),k=t.useRef(null),P=t.useRef({x:0,y:0,zoom:0}),{d3Zoom:A,d3Selection:O,d3ZoomHandler:I,userSelectionActive:D}=Mo(Ii,b),z=hi(y),R=t.useRef(0),$=t.useRef(!1),T=t.useRef();return function(e){const n=No();t.useEffect((()=>{let t;const o=()=>{if(!e.current)return;const t=Io(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",vo()),n.setState({width:t.width||500,height:t.height||500})};return o(),window.addEventListener("resize",o),e.current&&(t=new ResizeObserver((()=>o())),t.observe(e.current)),()=>{window.removeEventListener("resize",o),t&&e.current&&t.unobserve(e.current)}}),[])}(k),t.useEffect((()=>{if(k.current){const e=k.current.getBoundingClientRect(),t=function(){var e,t,n,o=lo,r=co,i=go,a=ho,s=fo,l=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],u=250,d=Jt,h=x("start","zoom","end"),f=500,g=0,p=10;function m(e){e.property("__zoom",uo).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",C).on("dblclick.zoom",M).filter(s).on("touchstart.zoom",N).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(l[0],Math.min(l[1],t)))===e.k?e:new ro(t,e.x,e.y)}function v(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new ro(e.k,o,r)}function b(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function w(e,t,n,o){e.on("start.zoom",(function(){S(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){S(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,a=S(e,i).event(o),s=r.apply(e,i),l=null==n?b(s):"function"==typeof n?n.apply(e,i):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,h="function"==typeof t?t.apply(e,i):t,f=d(u.invert(l).concat(c/u.k),h.invert(l).concat(c/h.k));return function(e){if(1===e)e=h;else{var t=f(e),n=c/t[2];e=new ro(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function S(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function _(e,...t){if(o.apply(this,arguments)){var n=S(this,t).event(e),r=this.__zoom,s=Math.max(l[0],Math.min(l[1],r.k*Math.pow(2,a.apply(this,arguments)))),u=Ve(e);if(n.wheel)n.mouse[0][0]===u[0]&&n.mouse[0][1]===u[1]||(n.mouse[1]=r.invert(n.mouse[0]=u)),clearTimeout(n.wheel);else{if(r.k===s)return;n.mouse=[u,r.invert(u)],En(this),n.start()}so(e),n.wheel=setTimeout(d,150),n.zoom("mouse",i(v(y(r,s),n.mouse[0],n.mouse[1]),n.extent,c))}function d(){n.wheel=null,n.end()}}function C(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,a=S(this,t,!0).event(e),s=Le(e.view).on("mousemove.zoom",h,!0).on("mouseup.zoom",f,!0),l=Ve(e,r),u=e.clientX,d=e.clientY;Fe(e.view),ao(e),a.mouse=[l,this.__zoom.invert(l)],En(this),a.start()}function h(e){if(so(e),!a.moved){var t=e.clientX-u,n=e.clientY-d;a.moved=t*t+n*n>g}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=Ve(e,r),a.mouse[1]),a.extent,c))}function f(e){s.on("mousemove.zoom mouseup.zoom",null),je(e.view,a.moved),so(e),a.event(e).end()}}function M(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,a=Ve(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(a),l=n.k*(e.shiftKey?.5:2),d=i(v(y(n,l),a,s),r.apply(this,t),c);so(e),u>0?Le(this).transition().duration(u).call(w,d,a,e):Le(this).call(m.transform,d,a,e)}}function N(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=S(this,r,n.changedTouches.length===u).event(n);for(ao(n),a=0;a<u;++a)l=[l=Ve(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),f)),En(this),d.start())}}function k(e,...t){if(this.__zooming){var n,o,r,a,s=S(this,t).event(e),l=e.changedTouches,u=l.length;for(so(e),n=0;n<u;++n)r=Ve(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=y(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(v(o,r,a),s.extent,c))}}function P(e,...o){if(this.__zooming){var r,i,a=S(this,o).event(e),s=e.changedTouches,l=s.length;for(ao(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),f),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Ve(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var c=Le(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return m.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",uo),e!==r?w(e,t,n,o):r.interrupt().each((function(){S(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},m.scaleBy=function(e,t,n,o){m.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},m.scaleTo=function(e,t,n,o){m.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,a=null==n?b(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(a),l="function"==typeof t?t.apply(this,arguments):t;return i(v(y(o,l),a,s),e,c)}),n,o)},m.translateBy=function(e,t,n,o){m.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),c)}),null,o)},m.translateTo=function(e,t,n,o,a){m.transform(e,(function(){var e=r.apply(this,arguments),a=this.__zoom,s=null==o?b(e):"function"==typeof o?o.apply(this,arguments):o;return i(io.translate(s[0],s[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,c)}),o,a)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=Le(this.that).datum();h.call(e,this.that,new oo(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:h}),t)}},m.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:no(+e),m):a},m.filter=function(e){return arguments.length?(o="function"==typeof e?e:no(!!e),m):o},m.touchable=function(e){return arguments.length?(s="function"==typeof e?e:no(!!e),m):s},m.extent=function(e){return arguments.length?(r="function"==typeof e?e:no([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):r},m.scaleExtent=function(e){return arguments.length?(l[0]=+e[0],l[1]=+e[1],m):[l[0],l[1]]},m.translateExtent=function(e){return arguments.length?(c[0][0]=+e[0][0],c[1][0]=+e[1][0],c[0][1]=+e[0][1],c[1][1]=+e[1][1],m):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(u=+e,m):u},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=h.on.apply(h,arguments);return e===h?m:e},m.clickDistance=function(e){return arguments.length?(g=(e=+e)*e,m):Math.sqrt(g)},m.tapDistance=function(e){return arguments.length?(p=+e,m):p},m}().scaleExtent([p,m]).translateExtent(g),n=Le(k.current).call(t),o=io.translate(f.x,f.y).scale(Do(f.zoom,p,m)),r=[[0,0],[e.width,e.height]],i=t.constrain()(o,r,g);t.transform(n,i),t.wheelDelta(Oi),C.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:k.current.closest(".react-flow")})}}),[]),t.useEffect((()=>{O&&A&&(!s||z||D?void 0!==I&&O.on("wheel.zoom",(function(e,t){if(!v&&"wheel"===e.type&&!e.ctrlKey||Pi(e,S))return null;e.preventDefault(),I.call(this,e,t)}),{passive:!1}):O.on("wheel.zoom",(t=>{if(Pi(t,S))return!1;t.preventDefault(),t.stopImmediatePropagation();const r=O.property("__zoom").k||1;if(t.ctrlKey&&a){const e=Ve(t),n=Oi(t),o=r*Math.pow(2,n);return void A.scaleTo(O,o,e,t)}const i=1===t.deltaMode?20:1;let s=c===Jo.Vertical?0:t.deltaX*i,u=c===Jo.Horizontal?0:t.deltaY*i;!jo()&&t.shiftKey&&c!==Jo.Vertical&&(s=t.deltaY*i,u=0),A.translateBy(O,-s/r*l,-u/r*l,{internal:!0});const d=ki(O.property("__zoom")),{onViewportChangeStart:h,onViewportChange:f,onViewportChangeEnd:g}=C.getState();clearTimeout(T.current),$.current||($.current=!0,n?.(t,d),h?.(d)),$.current&&(e?.(t,d),f?.(d),T.current=setTimeout((()=>{o?.(t,d),g?.(d),$.current=!1}),150))}),{passive:!1}))}),[D,s,c,O,A,I,z,a,v,S,n,e,o]),t.useEffect((()=>{A&&A.on("start",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;R.current=e.sourceEvent?.button;const{onViewportChangeStart:t}=C.getState(),o=ki(e.transform);M.current=!0,P.current=o,"mousedown"===e.sourceEvent?.type&&C.setState({paneDragging:!0}),t?.(o),n?.(e.sourceEvent,o)}))}),[A,n]),t.useEffect((()=>{A&&(D&&!M.current?A.on("zoom",null):D||A.on("zoom",(t=>{const{onViewportChange:n}=C.getState();if(C.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),N.current=!(!r||!Ai(h,R.current??0)),(e||n)&&!t.sourceEvent?.internal){const o=ki(t.transform);n?.(o),e?.(t.sourceEvent,o)}})))}),[D,A,e,h,r]),t.useEffect((()=>{A&&A.on("end",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;const{onViewportChangeEnd:t}=C.getState();if(M.current=!1,C.setState({paneDragging:!1}),r&&Ai(h,R.current??0)&&!N.current&&r(e.sourceEvent),N.current=!1,(o||t)&&(n=P.current,i=e.transform,n.x!==i.x||n.y!==i.y||n.zoom!==i.k)){const n=ki(e.transform);P.current=n,clearTimeout(_.current),_.current=setTimeout((()=>{t?.(n),o?.(e.sourceEvent,n)}),s?150:0)}var n,i}))}),[A,s,h,o,r]),t.useEffect((()=>{A&&A.filter((e=>{const t=z||i,n=a&&e.ctrlKey;if((!0===h||Array.isArray(h)&&h.includes(1))&&1===e.button&&"mousedown"===e.type&&(Pi(e,"react-flow__node")||Pi(e,"react-flow__edge")))return!0;if(!(h||t||s||u||a))return!1;if(D)return!1;if(!u&&"dblclick"===e.type)return!1;if(Pi(e,S)&&"wheel"===e.type)return!1;if(Pi(e,E)&&("wheel"!==e.type||s&&"wheel"===e.type&&!z))return!1;if(!a&&e.ctrlKey&&"wheel"===e.type)return!1;if(!t&&!s&&!n&&"wheel"===e.type)return!1;if(!h&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(h)&&!h.includes(e.button)&&"mousedown"===e.type)return!1;const o=Array.isArray(h)&&h.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o}))}),[D,A,i,a,s,u,h,d,z]),t.createElement("div",{className:"react-flow__renderer",ref:k,style:Ni},w)},zi=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Ri(){const{userSelectionActive:e,userSelectionRect:n}=Mo(zi,b);return e&&n?t.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}function $i(e,t){const n=t.parentNode||t.parentId,o=e.find((e=>e.id===n));if(o){const e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style}||{},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){const e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){const e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function Ti(e,t){return function(e,t){if(e.some((e=>"reset"===e.type)))return e.filter((e=>"reset"===e.type)).map((e=>e.item));const n=e.filter((e=>"add"===e.type)).map((e=>e.item));return t.reduce(((t,n)=>{const o=e.filter((e=>e.id===n.id));if(0===o.length)return t.push(n),t;const r={...n};for(const e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&$i(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&$i(t,r);break;case"remove":return t}return t.push(r),t}),n)}(e,t)}const Bi=(e,t)=>({id:e,type:"select",selected:t});function Hi(e,t){return e.reduce(((e,n)=>{const o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(Bi(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(Bi(n.id,!1))),e}),[])}const Li=(e,t)=>n=>{n.target===t.current&&e?.(n)},Vi=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),Xi=t.memo((({isSelecting:e,selectionMode:o=er.Full,panOnDrag:r,onSelectionStart:i,onSelectionEnd:a,onPaneClick:s,onPaneContextMenu:l,onPaneScroll:c,onPaneMouseEnter:u,onPaneMouseMove:d,onPaneMouseLeave:h,children:f})=>{const g=t.useRef(null),p=No(),m=t.useRef(0),y=t.useRef(0),v=t.useRef(),{userSelectionActive:w,elementsSelectable:x,dragging:S}=Mo(Vi,b),E=()=>{p.setState({userSelectionActive:!1,userSelectionRect:null}),m.current=0,y.current=0},_=e=>{s?.(e),p.getState().resetSelectedElements(),p.setState({nodesSelectionActive:!1})},C=c?e=>c(e):void 0,M=x&&(e||w);return t.createElement("div",{className:n(["react-flow__pane",{dragging:S,selection:e}]),onClick:M?void 0:Li(_,g),onContextMenu:Li((e=>{Array.isArray(r)&&r?.includes(2)?e.preventDefault():l?.(e)}),g),onWheel:Li(C,g),onMouseEnter:M?void 0:u,onMouseDown:M?t=>{const{resetSelectedElements:n,domNode:o}=p.getState();if(v.current=o?.getBoundingClientRect(),!x||!e||0!==t.button||t.target!==g.current||!v.current)return;const{x:r,y:a}=Fo(t,v.current);n(),p.setState({userSelectionRect:{width:0,height:0,startX:r,startY:a,x:r,y:a}}),i?.(t)}:void 0,onMouseMove:M?t=>{const{userSelectionRect:n,nodeInternals:r,edges:i,transform:a,onNodesChange:s,onEdgesChange:l,nodeOrigin:c,getNodes:u}=p.getState();if(!e||!v.current||!n)return;p.setState({userSelectionActive:!0,nodesSelectionActive:!1});const d=Fo(t,v.current),h=n.startX??0,f=n.startY??0,g={...n,x:d.x<h?d.x:h,y:d.y<f?d.y:f,width:Math.abs(d.x-h),height:Math.abs(d.y-f)},b=u(),w=Mr(r,g,a,o===er.Partial,!0,c),x=Nr(w,i).map((e=>e.id)),S=w.map((e=>e.id));if(m.current!==S.length){m.current=S.length;const e=Hi(b,S);e.length&&s?.(e)}if(y.current!==x.length){y.current=x.length;const e=Hi(i,x);e.length&&l?.(e)}p.setState({userSelectionRect:g})}:d,onMouseUp:M?e=>{if(0!==e.button)return;const{userSelectionRect:t}=p.getState();!w&&t&&e.target===g.current&&_?.(e),p.setState({nodesSelectionActive:m.current>0}),E(),a?.(e)}:void 0,onMouseLeave:M?e=>{w&&(p.setState({nodesSelectionActive:m.current>0}),a?.(e)),E()}:h,ref:g,style:Ni},f,t.createElement(Ri,null))}));function Yi(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const o=t.get(n);return!!o&&(!!o.selected||Yi(o,t))}function Ki(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)return!1;o=o.parentElement}while(o);return!1}function Zi(e,t,n,o){return Array.from(e.values()).filter((n=>(n.selected||n.id===o)&&(!n.parentNode||n.parentId||!Yi(n,e))&&(n.draggable||t&&void 0===n.draggable))).map((e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:n.x-(e.positionAbsolute?.x??0),y:n.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})))}function Fi(e,t,n,o,r=[0,0],i){const a=function(e,t){return t&&"parent"!==t?[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]:t}(e,e.extent||o);let s=a;const l=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&l&&"parent"!==e.extent){const t=n.get(l),{x:o,y:i}=_r(t,r).positionAbsolute;s=[[e.extent[0][0]+o,e.extent[0][1]+i],[e.extent[1][0]+o,e.extent[1][1]+i]]}}else if(l&&e.width&&e.height){const t=n.get(l),{x:o,y:i}=_r(t,r).positionAbsolute;s=t&&Vo(o)&&Vo(i)&&Vo(t.width)&&Vo(t.height)?[[o+e.width*r[0],i+e.height*r[1]],[o+t.width-e.width+e.width*r[0],i+t.height-e.height+e.height*r[1]]]:s}else i?.("005",bo()),s=a;let c={x:0,y:0};if(l){const e=n.get(l);c=_r(e,r).positionAbsolute}const u=s&&"parent"!==s?zo(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function ji({nodeId:e,dragItems:t,nodeInternals:n}){const o=t.map((e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute})));return[e?o.find((t=>t.id===e)):o[0],o]}Xi.displayName="Pane";const Wi=(e,t,n,o)=>{const r=t.querySelectorAll(e);if(!r||!r.length)return null;const i=Array.from(r),a=t.getBoundingClientRect(),s=a.width*o[0],l=a.height*o[1];return i.map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-s)/n,y:(t.top-a.top-l)/n,...Io(e)}}))};function qi(e,t,n){return void 0===n?n:o=>{const r=t().nodeInternals.get(e);r&&n(o,{...r})}}function Ui({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",_o(e))}function Gi(e){return(t,n,o)=>e?.(t,o)}function Qi({nodeRef:e,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:i,isSelectable:a,selectNodesOnDrag:s}){const l=No(),[c,u]=t.useState(!1),d=t.useRef([]),h=t.useRef({x:null,y:null}),f=t.useRef(0),g=t.useRef(null),p=t.useRef({x:0,y:0}),m=t.useRef(null),y=t.useRef(!1),v=t.useRef(!1),b=t.useRef(!1),w=function(){const e=No(),n=t.useCallback((({sourceEvent:t})=>{const{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,s={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(s.x/o[0]):s.x,ySnapped:r?o[1]*Math.round(s.y/o[1]):s.y,...s}}),[]);return n}();return t.useEffect((()=>{if(e?.current){const t=Le(e.current),c=({x:e,y:t})=>{const{nodeInternals:n,onNodeDrag:o,onSelectionDrag:r,updateNodePositions:a,nodeExtent:s,snapGrid:c,snapToGrid:f,nodeOrigin:g,onError:p}=l.getState();h.current={x:e,y:t};let y=!1,v={x:0,y:0,x2:0,y2:0};if(d.current.length>1&&s){const e=Cr(d.current,g);v=Bo(e)}if(d.current=d.current.map((o=>{const r={x:e-o.distance.x,y:t-o.distance.y};f&&(r.x=c[0]*Math.round(r.x/c[0]),r.y=c[1]*Math.round(r.y/c[1]));const i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];d.current.length>1&&s&&!o.extent&&(i[0][0]=o.positionAbsolute.x-v.x+s[0][0],i[1][0]=o.positionAbsolute.x+(o.width??0)-v.x2+s[1][0],i[0][1]=o.positionAbsolute.y-v.y+s[0][1],i[1][1]=o.positionAbsolute.y+(o.height??0)-v.y2+s[1][1]);const a=Fi(o,r,n,i,g,p);return y=y||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o})),!y)return;a(d.current,!0,!0),u(!0);const b=i?o:Gi(r);if(b&&m.current){const[e,t]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});b(m.current,e,t)}},S=()=>{if(!g.current)return;const[e,t]=$o(p.current,g.current);if(0!==e||0!==t){const{transform:n,panBy:o}=l.getState();h.current.x=(h.current.x??0)-e/n[2],h.current.y=(h.current.y??0)-t/n[2],o({x:e,y:t})&&c(h.current)}f.current=requestAnimationFrame(S)},E=t=>{const{nodeInternals:n,multiSelectionActive:o,nodesDraggable:r,unselectNodesAndEdges:c,onNodeDragStart:u,onSelectionDragStart:f}=l.getState();v.current=!0;const g=i?u:Gi(f);s&&a||o||!i||n.get(i)?.selected||c(),i&&a&&s&&Ui({id:i,store:l,nodeRef:e});const p=w(t);if(h.current=p,d.current=Zi(n,r,p,i),g&&d.current){const[e,o]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});g(t.sourceEvent,e,o)}};if(!n){const n=function(){var e,t,n,o,r=Ue,i=Ge,a=Qe,s=Je,l={},c=x("start","drag","end"),u=0,d=0;function h(e){e.on("mousedown.drag",f).filter(s).on("touchstart.drag",m).on("touchmove.drag",y,Xe).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(a,s){if(!o&&r.call(this,a,s)){var l=b(this,i.call(this,a,s),a,s,"mouse");l&&(Le(a.view).on("mousemove.drag",g,Ye).on("mouseup.drag",p,Ye),Fe(a.view),Ke(a),n=!1,e=a.clientX,t=a.clientY,l("start",a))}}function g(o){if(Ze(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>d}l.mouse("drag",o)}function p(e){Le(e.view).on("mousemove.drag mouseup.drag",null),je(e.view,n),Ze(e),l.mouse("end",e)}function m(e,t){if(r.call(this,e,t)){var n,o,a=e.changedTouches,s=i.call(this,e,t),l=a.length;for(n=0;n<l;++n)(o=b(this,s,e,t,a[n].identifier,a[n]))&&(Ke(e),o("start",e,a[n]))}}function y(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=l[o[t].identifier])&&(Ze(e),n("drag",e,o[t]))}function v(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=l[r[t].identifier])&&(Ke(e),n("end",e,r[t]))}function b(e,t,n,o,r,i){var s,d,f,g=c.copy(),p=Ve(i||n,t);if(null!=(f=a.call(e,new qe("beforestart",{sourceEvent:n,target:h,identifier:r,active:u,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return s=f.x-p[0]||0,d=f.y-p[1]||0,function n(i,a,c){var m,y=p;switch(i){case"start":l[r]=n,m=u++;break;case"end":delete l[r],--u;case"drag":p=Ve(c||a,t),m=u}g.call(i,e,new qe(i,{sourceEvent:a,subject:f,target:h,identifier:r,active:m,x:p[0]+s,y:p[1]+d,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return h.filter=function(e){return arguments.length?(r="function"==typeof e?e:We(!!e),h):r},h.container=function(e){return arguments.length?(i="function"==typeof e?e:We(e),h):i},h.subject=function(e){return arguments.length?(a="function"==typeof e?e:We(e),h):a},h.touchable=function(e){return arguments.length?(s="function"==typeof e?e:We(!!e),h):s},h.on=function(){var e=c.on.apply(c,arguments);return e===c?h:e},h.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,h):Math.sqrt(d)},h}().on("start",(e=>{const{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&E(e),b.current=!1;const o=w(e);h.current=o,g.current=t?.getBoundingClientRect()||null,p.current=Fo(e.sourceEvent,g.current)})).on("drag",(e=>{const t=w(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(b.current=!0),!b.current){if(!y.current&&v.current&&n&&(y.current=!0,S()),!v.current){const n=t.xSnapped-(h?.current?.x??0),r=t.ySnapped-(h?.current?.y??0);Math.sqrt(n*n+r*r)>o&&E(e)}(h.current.x!==t.xSnapped||h.current.y!==t.ySnapped)&&d.current&&v.current&&(m.current=e.sourceEvent,p.current=Fo(e.sourceEvent,g.current),c(t))}})).on("end",(e=>{if(v.current&&!b.current&&(u(!1),y.current=!1,v.current=!1,cancelAnimationFrame(f.current),d.current)){const{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:r}=l.getState(),a=i?o:Gi(r);if(t(d.current,!1,!1),a){const[t,o]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});a(e.sourceEvent,t,o)}}})).filter((t=>{const n=t.target;return!t.button&&(!o||!Ki(n,`.${o}`,e))&&(!r||Ki(n,r,e))}));return t.call(n),()=>{t.on(".drag",null)}}t.on(".drag",null)}}),[e,n,o,r,a,l,i,s,w]),c}function Ji(){const e=No();return t.useCallback((t=>{const{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:i,snapToGrid:a,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=i().filter((e=>e.selected&&(e.draggable||c&&void 0===e.draggable))),d=a?s[0]:5,h=a?s[1]:5,f=t.isShiftPressed?4:1,g=t.x*d*f,p=t.y*h*f;r(u.map((e=>{if(e.positionAbsolute){const t={x:e.positionAbsolute.x+g,y:e.positionAbsolute.y+p};a&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));const{positionAbsolute:r,position:i}=Fi(e,t,n,o,void 0,l);e.position=i,e.positionAbsolute=r}return e})),!0,!1)}),[])}const ea={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var ta=e=>{const o=({id:o,type:r,data:i,xPos:a,yPos:s,xPosOrigin:l,yPosOrigin:c,selected:u,onClick:d,onMouseEnter:h,onMouseMove:f,onMouseLeave:g,onContextMenu:p,onDoubleClick:m,style:y,className:v,isDraggable:b,isSelectable:w,isConnectable:x,isFocusable:S,selectNodesOnDrag:E,sourcePosition:_,targetPosition:C,hidden:M,resizeObserver:N,dragHandle:k,zIndex:P,isParent:A,noDragClassName:O,noPanClassName:I,initialized:D,disableKeyboardA11y:z,ariaLabel:R,rfId:$,hasHandleBounds:T})=>{const B=No(),H=t.useRef(null),L=t.useRef(null),V=t.useRef(_),X=t.useRef(C),Y=t.useRef(r),K=w||b||d||h||f||g,Z=Ji(),F=qi(o,B.getState,h),j=qi(o,B.getState,f),W=qi(o,B.getState,g),q=qi(o,B.getState,p),U=qi(o,B.getState,m);t.useEffect((()=>()=>{L.current&&(N?.unobserve(L.current),L.current=null)}),[]),t.useEffect((()=>{if(H.current&&!M){const e=H.current;D&&T&&L.current===e||(L.current&&N?.unobserve(L.current),N?.observe(e),L.current=e)}}),[M,D,T]),t.useEffect((()=>{const e=Y.current!==r,t=V.current!==_,n=X.current!==C;H.current&&(e||t||n)&&(e&&(Y.current=r),t&&(V.current=_),n&&(X.current=C),B.getState().updateNodeDimensions([{id:o,nodeElement:H.current,forceUpdate:!0}]))}),[o,r,_,C]);const G=Qi({nodeRef:H,disabled:M||!b,noDragClassName:O,handleSelector:k,nodeId:o,isSelectable:w,selectNodesOnDrag:E});return M?null:t.createElement("div",{className:n(["react-flow__node",`react-flow__node-${r}`,{[I]:b},v,{selected:u,selectable:w,parent:A,dragging:G}]),ref:H,style:{zIndex:P,transform:`translate(${l}px,${c}px)`,pointerEvents:K?"all":"none",visibility:D?"visible":"hidden",...y},"data-id":o,"data-testid":`rf__node-${o}`,onMouseEnter:F,onMouseMove:j,onMouseLeave:W,onContextMenu:q,onClick:e=>{const{nodeDragThreshold:t}=B.getState();if(w&&(!E||!b||t>0)&&Ui({id:o,store:B,nodeRef:H}),d){const t=B.getState().nodeInternals.get(o);t&&d(e,{...t})}},onDoubleClick:U,onKeyDown:S?e=>{if(!Ko(e)&&!z)if(Yo.includes(e.key)&&w){const t="Escape"===e.key;Ui({id:o,store:B,unselect:t,nodeRef:H})}else b&&u&&Object.prototype.hasOwnProperty.call(ea,e.key)&&(B.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~a}, y: ${~~s}`}),Z({x:ea[e.key].x,y:ea[e.key].y,isShiftPressed:e.shiftKey}))}:void 0,tabIndex:S?0:void 0,role:S?"button":void 0,"aria-describedby":z?void 0:`${si}-${$}`,"aria-label":R},t.createElement(br,{value:o},t.createElement(e,{id:o,data:i,type:r,xPos:a,yPos:s,selected:u,isConnectable:x,sourcePosition:_,targetPosition:C,dragging:G,dragHandle:k,zIndex:P})))};return o.displayName="NodeWrapper",t.memo(o)};const na=e=>{const t=e.getNodes().filter((e=>e.selected));return{...Cr(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};var oa=t.memo((function({onSelectionContextMenu:e,noPanClassName:o,disableKeyboardA11y:r}){const i=No(),{width:a,height:s,x:l,y:c,transformString:u,userSelectionActive:d}=Mo(na,b),h=Ji(),f=t.useRef(null);if(t.useEffect((()=>{r||f.current?.focus({preventScroll:!0})}),[r]),Qi({nodeRef:f}),d||!a||!s)return null;const g=e?t=>{const n=i.getState().getNodes().filter((e=>e.selected));e(t,n)}:void 0;return t.createElement("div",{className:n(["react-flow__nodesselection","react-flow__container",o]),style:{transform:u}},t.createElement("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:g,tabIndex:r?void 0:-1,onKeyDown:r?void 0:e=>{Object.prototype.hasOwnProperty.call(ea,e.key)&&h({x:ea[e.key].x,y:ea[e.key].y,isShiftPressed:e.shiftKey})},style:{width:a,height:s,top:c,left:l}}))}));const ra=e=>e.nodesSelectionActive,ia=({children:e,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,deleteKeyCode:l,onMove:c,onMoveStart:u,onMoveEnd:d,selectionKeyCode:h,selectionOnDrag:f,selectionMode:g,onSelectionStart:p,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:b,elementsSelectable:w,zoomOnScroll:x,zoomOnPinch:S,panOnScroll:E,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:N,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,preventScrolling:I,onSelectionContextMenu:D,noWheelClassName:z,noPanClassName:R,disableKeyboardA11y:$})=>{const T=Mo(ra),B=hi(h),H=hi(v),L=H||N,V=H||E,X=B||f&&!0!==L;return(({deleteKeyCode:e,multiSelectionKeyCode:n})=>{const o=No(),{deleteElements:r}=Ci(),i=hi(e,Mi),a=hi(n);t.useEffect((()=>{if(i){const{edges:e,getNodes:t}=o.getState(),n=t().filter((e=>e.selected)),i=e.filter((e=>e.selected));r({nodes:n,edges:i}),o.setState({nodesSelectionActive:!1})}}),[i]),t.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])})({deleteKeyCode:l,multiSelectionKeyCode:y}),t.createElement(Di,{onMove:c,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:a,elementsSelectable:w,zoomOnScroll:x,zoomOnPinch:S,panOnScroll:V,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:!B&&L,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,zoomActivationKeyCode:b,preventScrolling:I,noWheelClassName:z,noPanClassName:R},t.createElement(Xi,{onSelectionStart:p,onSelectionEnd:m,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,panOnDrag:L,isSelecting:!!X,selectionMode:g},e,T&&t.createElement(oa,{onSelectionContextMenu:D,noPanClassName:R,disableKeyboardA11y:$})))};ia.displayName="FlowRenderer";var aa=t.memo(ia);function sa(e){return{...{input:ta(e.input||Zr),default:ta(e.default||Yr),output:ta(e.output||jr),group:ta(e.group||Wr)},...Object.keys(e).filter((e=>!["input","default","output","group"].includes(e))).reduce(((t,n)=>(t[n]=ta(e[n]||Yr),t)),{})}}const la=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),ca=e=>{const{nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:i,updateNodeDimensions:a,onError:s}=Mo(la,b),l=(c=e.onlyRenderVisibleElements,Mo(t.useCallback((e=>c?Mr(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes()),[c])));var c;const u=t.useRef(),d=t.useMemo((()=>{if("undefined"==typeof ResizeObserver)return null;const e=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));a(t)}));return u.current=e,e}),[]);return t.useEffect((()=>()=>{u?.current?.disconnect()}),[]),t.createElement("div",{className:"react-flow__nodes",style:Ni},l.map((a=>{let l=a.type||"default";e.nodeTypes[l]||(s?.("003",yo(l)),l="default");const c=e.nodeTypes[l]||e.nodeTypes.default,u=!!(a.draggable||n&&void 0===a.draggable),h=!!(a.selectable||i&&void 0===a.selectable),f=!!(a.connectable||o&&void 0===a.connectable),g=!!(a.focusable||r&&void 0===a.focusable),p=e.nodeExtent?zo(a.positionAbsolute,e.nodeExtent):a.positionAbsolute,m=p?.x??0,y=p?.y??0,v=(({x:e,y:t,width:n,height:o,origin:r})=>n&&o?r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]}:{x:e,y:t})({x:m,y:y,width:a.width??0,height:a.height??0,origin:e.nodeOrigin});return t.createElement(c,{key:a.id,id:a.id,className:a.className,style:a.style,type:l,data:a.data,sourcePosition:a.sourcePosition||or.Bottom,targetPosition:a.targetPosition||or.Top,hidden:a.hidden,xPos:m,yPos:y,xPosOrigin:v.x,yPosOrigin:v.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!a.selected,isDraggable:u,isSelectable:h,isConnectable:f,isFocusable:g,resizeObserver:d,dragHandle:a.dragHandle,zIndex:a[Xo]?.z??0,isParent:!!a[Xo]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!a.width&&!!a.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:a.ariaLabel,hasHandleBounds:!!a[Xo]?.handleBounds})})))};ca.displayName="NodeRenderer";var ua=t.memo(ca);const da=(e,t,n)=>n===or.Left?e-t:n===or.Right?e+t:e,ha=(e,t,n)=>n===or.Top?e-t:n===or.Bottom?e+t:e,fa="react-flow__edgeupdater",ga=({position:e,centerX:o,centerY:r,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c})=>t.createElement("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:n([fa,`${fa}-${c}`]),cx:da(o,i,e),cy:ha(r,i,e),r:i,stroke:"transparent",fill:"transparent"}),pa=()=>!0;var ma=e=>{const o=({id:o,className:r,type:i,data:a,onClick:s,onEdgeDoubleClick:l,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:y,source:v,target:b,sourceX:w,sourceY:x,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,elementsSelectable:M,hidden:N,sourceHandleId:k,targetHandleId:P,onContextMenu:A,onMouseEnter:O,onMouseMove:I,onMouseLeave:D,reconnectRadius:z,onReconnect:R,onReconnectStart:$,onReconnectEnd:T,markerEnd:B,markerStart:H,rfId:L,ariaLabel:V,isFocusable:X,isReconnectable:Y,pathOptions:K,interactionWidth:Z,disableKeyboardA11y:F})=>{const j=t.useRef(null),[W,q]=t.useState(!1),[U,G]=t.useState(!1),Q=No(),J=t.useMemo((()=>`url('#${xr(H,L)}')`),[H,L]),ee=t.useMemo((()=>`url('#${xr(B,L)}')`),[B,L]);if(N)return null;const te=qo(o,Q.getState,l),ne=qo(o,Q.getState,A),oe=qo(o,Q.getState,O),re=qo(o,Q.getState,I),ie=qo(o,Q.getState,D),ae=(e,t)=>{if(0!==e.button)return;const{edges:n,isValidConnection:r}=Q.getState(),i=t?b:v,a=(t?P:k)||null,s=t?"target":"source",l=r||pa,c=t,u=n.find((e=>e.id===o));G(!0),$?.(e,u,s);Tr({event:e,handleId:a,nodeId:i,onConnect:e=>R?.(u,e),isTarget:c,getState:Q.getState,setState:Q.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{G(!1),T?.(e,u,s)}})},se=()=>q(!0),le=()=>q(!1),ce=!M&&!s;return t.createElement("g",{className:n(["react-flow__edge",`react-flow__edge-${i}`,r,{selected:c,animated:u,inactive:ce,updating:W}]),onClick:e=>{const{edges:t,addSelectedEdges:n,unselectNodesAndEdges:r,multiSelectionActive:i}=Q.getState(),a=t.find((e=>e.id===o));a&&(M&&(Q.setState({nodesSelectionActive:!1}),a.selected&&i?(r({nodes:[],edges:[a]}),j.current?.blur()):n([o])),s&&s(e,a))},onDoubleClick:te,onContextMenu:ne,onMouseEnter:oe,onMouseMove:re,onMouseLeave:ie,onKeyDown:X?e=>{if(!F&&Yo.includes(e.key)&&M){const{unselectNodesAndEdges:t,addSelectedEdges:n,edges:r}=Q.getState();"Escape"===e.key?(j.current?.blur(),t({edges:[r.find((e=>e.id===o))]})):n([o])}}:void 0,tabIndex:X?0:void 0,role:X?"button":"img","data-testid":`rf__edge-${o}`,"aria-label":null===V?void 0:V||`Edge from ${v} to ${b}`,"aria-describedby":X?`${li}-${L}`:void 0,ref:j},!U&&t.createElement(e,{id:o,source:v,target:b,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,data:a,style:y,sourceX:w,sourceY:x,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,sourceHandleId:k,targetHandleId:P,markerStart:J,markerEnd:ee,pathOptions:K,interactionWidth:Z}),Y&&t.createElement(t.Fragment,null,("source"===Y||!0===Y)&&t.createElement(ga,{position:_,centerX:w,centerY:x,radius:z,onMouseDown:e=>ae(e,!0),onMouseEnter:se,onMouseOut:le,type:"source"}),("target"===Y||!0===Y)&&t.createElement(ga,{position:C,centerX:S,centerY:E,radius:z,onMouseDown:e=>ae(e,!1),onMouseEnter:se,onMouseOut:le,type:"target"})))};return o.displayName="EdgeWrapper",t.memo(o)};function ya(e){return{...{default:ma(e.default||yr),straight:ma(e.bezier||fr),step:ma(e.step||hr),smoothstep:ma(e.step||dr),simplebezier:ma(e.simplebezier||ar)},...Object.keys(e).filter((e=>!["default","bezier"].includes(e))).reduce(((t,n)=>(t[n]=ma(e[n]||yr),t)),{})}}function va(e,t,n=null){const o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,i=n?.width||t.width,a=n?.height||t.height;switch(e){case or.Top:return{x:o+i/2,y:r};case or.Right:return{x:o+i,y:r+a/2};case or.Bottom:return{x:o+i/2,y:r+a};case or.Left:return{x:o,y:r+a/2}}}function ba(e,t){return e?1!==e.length&&t?t&&e.find((e=>e.id===t))||null:e[0]:null}function wa(e){const t=e?.[Xo]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}const xa=[{level:0,isMaxLevel:!0,edges:[]}];function Sa(e,n,o){return function(e,t,n=!1){let o=-1;const r=e.reduce(((e,r)=>{const i=Vo(r.zIndex);let a=i?r.zIndex:0;if(n){const e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,s=Math.max(n?.[Xo]?.z||0,e?.[Xo]?.z||0,1e3);a=(i?r.zIndex:0)+(o?s:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e}),{}),i=Object.entries(r).map((([e,t])=>{const n=+e;return{edges:t,level:n,isMaxLevel:n===o}}));return 0===i.length?xa:i}(Mo(t.useCallback((t=>e?t.edges.filter((e=>{const o=n.get(e.source),r=n.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:a,height:s,transform:l}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=Bo({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:a/l[2],height:s/l[2]}),d=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),h=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(d*h)>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:t.width,height:t.height,transform:t.transform})})):t.edges),[e,n])),n,o)}const Ea={[nr.Arrow]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[nr.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const _a=({id:e,type:n,color:o,width:r=12.5,height:i=12.5,markerUnits:a="strokeWidth",strokeWidth:s,orient:l="auto-start-reverse"})=>{const c=function(e){const n=No();return t.useMemo((()=>Object.prototype.hasOwnProperty.call(Ea,e)?Ea[e]:(n.getState().onError?.("009",wo(e)),null)),[e])}(n);return c?t.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${i}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:l,refX:"0",refY:"0"},t.createElement(c,{color:o,strokeWidth:s})):null},Ca=({defaultColor:e,rfId:n})=>{const o=Mo(t.useCallback((({defaultColor:e,rfId:t})=>n=>{const o=[];return n.edges.reduce(((n,r)=>([r.markerStart,r.markerEnd].forEach((r=>{if(r&&"object"==typeof r){const i=xr(r,t);o.includes(i)||(n.push({id:i,color:r.color||e,...r}),o.push(i))}})),n)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))})({defaultColor:e,rfId:n}),[e,n]),((e,t)=>!(e.length!==t.length||e.some(((e,n)=>e.id!==t[n].id)))));return t.createElement("defs",null,o.map((e=>t.createElement(_a,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient}))))};Ca.displayName="MarkerDefinitions";var Ma=t.memo(Ca);const Na=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),ka=({defaultMarkerColor:e,onlyRenderVisibleElements:o,elevateEdgesOnSelect:r,rfId:i,edgeTypes:a,noPanClassName:s,onEdgeContextMenu:l,onEdgeMouseEnter:c,onEdgeMouseMove:u,onEdgeMouseLeave:d,onEdgeClick:h,onEdgeDoubleClick:f,onReconnect:g,onReconnectStart:p,onReconnectEnd:m,reconnectRadius:y,children:v,disableKeyboardA11y:w})=>{const{edgesFocusable:x,edgesUpdatable:S,elementsSelectable:E,width:_,height:C,connectionMode:M,nodeInternals:N,onError:k}=Mo(Na,b),P=Sa(o,N,r);return _?t.createElement(t.Fragment,null,P.map((({level:o,edges:r,isMaxLevel:v})=>t.createElement("svg",{key:o,style:{zIndex:o},width:_,height:C,className:"react-flow__edges react-flow__container"},v&&t.createElement(Ma,{defaultColor:e,rfId:i}),t.createElement("g",null,r.map((e=>{const[o,r,v]=wa(N.get(e.source)),[b,_,C]=wa(N.get(e.target));if(!v||!C)return null;let P=e.type||"default";a[P]||(k?.("011",Eo(P)),P="default");const A=a[P]||a.default,O=M===Qo.Strict?_.target:(_.target??[]).concat(_.source??[]),I=ba(r.source,e.sourceHandle),D=ba(O,e.targetHandle),z=I?.position||or.Bottom,R=D?.position||or.Top,$=!!(e.focusable||x&&void 0===e.focusable),T=e.reconnectable||e.updatable,B=void 0!==g&&(T||S&&void 0===T);if(!I||!D)return k?.("008",xo(I,e)),null;const{sourceX:H,sourceY:L,targetX:V,targetY:X}=((e,t,n,o,r,i)=>{const a=va(n,e,t),s=va(i,o,r);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}})(o,I,z,b,D,R);return t.createElement(A,{key:e.id,id:e.id,className:n([e.className,s]),type:P,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:H,sourceY:L,targetX:V,targetY:X,sourcePosition:z,targetPosition:R,elementsSelectable:E,onContextMenu:l,onMouseEnter:c,onMouseMove:u,onMouseLeave:d,onClick:h,onEdgeDoubleClick:f,onReconnect:g,onReconnectStart:p,onReconnectEnd:m,reconnectRadius:y,rfId:i,ariaLabel:e.ariaLabel,isFocusable:$,isReconnectable:B,pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:w})})))))),v):null};ka.displayName="EdgeRenderer";var Pa=t.memo(ka);const Aa=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function Oa({children:e}){const n=Mo(Aa);return t.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:n}},e)}const Ia={[or.Left]:or.Right,[or.Right]:or.Left,[or.Top]:or.Bottom,[or.Bottom]:or.Top},Da=({nodeId:e,handleType:n,style:o,type:r=tr.Bezier,CustomComponent:i,connectionStatus:a})=>{const{fromNode:s,handleId:l,toX:c,toY:u,connectionMode:d}=Mo(t.useCallback((t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode})),[e]),b),h=s?.[Xo]?.handleBounds;let f=h?.[n];if(d===Qo.Loose&&(f=f||h?.["source"===n?"target":"source"]),!s||!f)return null;const g=l?f.find((e=>e.id===l)):f[0],p=g?g.x+g.width/2:(s.width??0)/2,m=g?g.y+g.height/2:s.height??0,y=(s.positionAbsolute?.x??0)+p,v=(s.positionAbsolute?.y??0)+m,w=g?.position,x=w?Ia[w]:null;if(!w||!x)return null;if(i)return t.createElement(i,{connectionLineType:r,connectionLineStyle:o,fromNode:s,fromHandle:g,fromX:y,fromY:v,toX:c,toY:u,fromPosition:w,toPosition:x,connectionStatus:a});let S="";const E={sourceX:y,sourceY:v,sourcePosition:w,targetX:c,targetY:u,targetPosition:x};return r===tr.Bezier?[S]=mr(E):r===tr.Step?[S]=ur({...E,borderRadius:0}):r===tr.SmoothStep?[S]=ur(E):r===tr.SimpleBezier?[S]=ir(E):S=`M${y},${v} ${c},${u}`,t.createElement("path",{d:S,fill:"none",className:"react-flow__connection-path",style:o})};Da.displayName="ConnectionLine";const za=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function Ra({containerStyle:e,style:o,type:r,component:i}){const{nodeId:a,handleType:s,nodesConnectable:l,width:c,height:u,connectionStatus:d}=Mo(za,b);return!!(a&&s&&c&&l)?t.createElement("svg",{style:e,width:c,height:u,className:"react-flow__edges react-flow__connectionline react-flow__container"},t.createElement("g",{className:n(["react-flow__connection",d])},t.createElement(Da,{nodeId:a,handleType:s,style:o,type:r,CustomComponent:i,connectionStatus:d}))):null}function $a(e,n){t.useRef(null),No();return t.useMemo((()=>n(e)),[e])}const Ta=({nodeTypes:e,edgeTypes:n,onMove:o,onMoveStart:r,onMoveEnd:i,onInit:a,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:b,connectionLineComponent:w,connectionLineContainerStyle:x,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,deleteKeyCode:k,onlyRenderVisibleElements:P,elementsSelectable:A,selectNodesOnDrag:O,defaultViewport:I,translateExtent:D,minZoom:z,maxZoom:R,preventScrolling:$,defaultMarkerColor:T,zoomOnScroll:B,zoomOnPinch:H,panOnScroll:L,panOnScrollSpeed:V,panOnScrollMode:X,zoomOnDoubleClick:Y,panOnDrag:K,onPaneClick:Z,onPaneMouseEnter:F,onPaneMouseMove:j,onPaneMouseLeave:W,onPaneScroll:q,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,elevateEdgesOnSelect:le,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})=>{const fe=$a(e,sa),ge=$a(n,ya);return function(e){const n=Ci(),o=t.useRef(!1);t.useEffect((()=>{!o.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),o.current=!0)}),[e,n.viewportInitialized])}(a),t.createElement(aa,{onPaneClick:Z,onPaneMouseEnter:F,onPaneMouseMove:j,onPaneMouseLeave:W,onPaneContextMenu:U,onPaneScroll:q,deleteKeyCode:k,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,elementsSelectable:A,onMove:o,onMoveStart:r,onMoveEnd:i,zoomOnScroll:B,zoomOnPinch:H,zoomOnDoubleClick:Y,panOnScroll:L,panOnScrollSpeed:V,panOnScrollMode:X,panOnDrag:K,defaultViewport:I,translateExtent:D,minZoom:z,maxZoom:R,onSelectionContextMenu:p,preventScrolling:$,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,disableKeyboardA11y:ce},t.createElement(Oa,null,t.createElement(Pa,{edgeTypes:ge,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:P,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,defaultMarkerColor:T,noPanClassName:se,elevateEdgesOnSelect:!!le,disableKeyboardA11y:ce,rfId:he},t.createElement(Ra,{style:b,type:v,component:w,containerStyle:x})),t.createElement("div",{className:"react-flow__edgelabel-renderer"}),t.createElement(ua,{nodeTypes:fe,onNodeClick:s,onNodeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,selectNodesOnDrag:O,onlyRenderVisibleElements:P,noPanClassName:se,noDragClassName:ie,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})))};Ta.displayName="GraphView";var Ba=t.memo(Ta);const Ha=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],La={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Ha,nodeExtent:Ha,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:Qo.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:(e,t)=>{},isValidConnection:void 0},Va=()=>{return e=(e,t)=>({...La,setNodes:n=>{const{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:i}=t();e({nodeInternals:yi(n,o,r,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:o={}}=t();e({edges:n.map((e=>({...o,...e})))})},setDefaultNodesAndEdges:(n,o)=>{const r=void 0!==n,i=void 0!==o,a=r?yi(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?o:[],hasDefaultNodes:r,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:o,nodeInternals:r,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;const d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce(((e,t)=>{const n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[Xo]:{...n[Xo],handleBounds:void 0}});else if(n){const o=Io(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[Xo]:{...n[Xo],handleBounds:{source:Wi(".source",t.nodeElement,h,c),target:Wi(".target",t.nodeElement,h,c)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e}),[]);mi(r,c);const g=a||i&&!a&&vi(t,{initial:!0,...s});e({nodeInternals:new Map(r),fitViewOnInitDone:g}),f?.length>0&&o?.(f)},updateNodePositions:(e,n=!0,o=!1)=>{const{triggerNodeChanges:r}=t();r(e.map((e=>{const t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t})))},triggerNodeChanges:n=>{const{onNodesChange:o,nodeInternals:r,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:l}=t();if(n?.length){if(i){const t=yi(Ti(n,s()),r,a,l);e({nodeInternals:t})}o?.(n)}},addSelectedNodes:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Bi(e,!0))):(a=Hi(i(),n),s=Hi(r,[])),xi({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Bi(e,!0))):(a=Hi(r,n),s=Hi(i(),[])),xi({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{const{edges:r,getNodes:i}=t(),a=o||r;xi({changedNodes:(n||i()).map((e=>(e.selected=!1,Bi(e.id,!1)))),changedEdges:a.map((e=>Bi(e.id,!1))),get:t,set:e})},setMinZoom:n=>{const{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:o}=t();xi({changedNodes:o().filter((e=>e.selected)).map((e=>Bi(e.id,!1))),changedEdges:n.filter((e=>e.selected)).map((e=>Bi(e.id,!1))),get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:o}=t();o.forEach((e=>{e.positionAbsolute=zo(e.position,n)})),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{const{transform:n,width:o,height:r,d3Zoom:i,d3Selection:a,translateExtent:s}=t();if(!i||!a||!e.x&&!e.y)return!1;const l=io.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=[[0,0],[o,r]],u=i?.constrain()(l,c,s);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:La.connectionNodeId,connectionHandleId:La.connectionHandleId,connectionHandleType:La.connectionHandleType,connectionStatus:La.connectionStatus,connectionStartHandle:La.connectionStartHandle,connectionEndHandle:La.connectionEndHandle}),reset:()=>e({...La})}),t=Object.is,e?v(e,t):v;var e,t},Xa=({children:e})=>{const n=t.useRef(null);return n.current||(n.current=Va()),t.createElement(mo,{value:n.current},e)};Xa.displayName="ReactFlowProvider";const Ya=({children:e})=>t.useContext(po)?t.createElement(t.Fragment,null,e):t.createElement(Xa,null,e);Ya.displayName="ReactFlowWrapper";const Ka={input:Zr,default:Yr,output:jr,group:Wr},Za={default:yr,straight:fr,step:hr,smoothstep:dr,simplebezier:ar},Fa=[0,0],ja=[15,15],Wa={x:0,y:0,zoom:1},qa={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},Ua=t.forwardRef((({nodes:e,edges:o,defaultNodes:r,defaultEdges:i,className:a,nodeTypes:s=Ka,edgeTypes:l=Za,onNodeClick:c,onEdgeClick:u,onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,onNodeDragStart:C,onNodeDrag:M,onNodeDragStop:N,onNodesDelete:k,onEdgesDelete:P,onSelectionChange:A,onSelectionDragStart:O,onSelectionDrag:I,onSelectionDragStop:D,onSelectionContextMenu:z,onSelectionStart:R,onSelectionEnd:$,connectionMode:T=Qo.Strict,connectionLineType:B=tr.Bezier,connectionLineStyle:H,connectionLineComponent:L,connectionLineContainerStyle:V,deleteKeyCode:X="Backspace",selectionKeyCode:Y="Shift",selectionOnDrag:K=!1,selectionMode:Z=er.Full,panActivationKeyCode:F="Space",multiSelectionKeyCode:j=(jo()?"Meta":"Control"),zoomActivationKeyCode:W=(jo()?"Meta":"Control"),snapToGrid:q=!1,snapGrid:U=ja,onlyRenderVisibleElements:G=!1,selectNodesOnDrag:Q=!0,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,nodeOrigin:ne=Fa,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,defaultViewport:ae=Wa,minZoom:se=.5,maxZoom:le=2,translateExtent:ce=Ha,preventScrolling:ue=!0,nodeExtent:de,defaultMarkerColor:he="#b1b1b7",zoomOnScroll:fe=!0,zoomOnPinch:ge=!0,panOnScroll:pe=!1,panOnScrollSpeed:me=.5,panOnScrollMode:ye=Jo.Free,zoomOnDoubleClick:ve=!0,panOnDrag:be=!0,onPaneClick:we,onPaneMouseEnter:xe,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,children:Me,onEdgeContextMenu:Ne,onEdgeDoubleClick:ke,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ae,onEdgeMouseLeave:Oe,onEdgeUpdate:Ie,onEdgeUpdateStart:De,onEdgeUpdateEnd:ze,onReconnect:Re,onReconnectStart:$e,onReconnectEnd:Te,reconnectRadius:Be=10,edgeUpdaterRadius:He=10,onNodesChange:Le,onEdgesChange:Ve,noDragClassName:Xe="nodrag",noWheelClassName:Ye="nowheel",noPanClassName:Ke="nopan",fitView:Ze=!1,fitViewOptions:Fe,connectOnClick:je=!0,attributionPosition:We,proOptions:qe,defaultEdgeOptions:Ue,elevateNodesOnSelect:Ge=!0,elevateEdgesOnSelect:Qe=!1,disableKeyboardA11y:Je=!1,autoPanOnConnect:et=!0,autoPanOnNodeDrag:tt=!0,connectionRadius:nt=20,isValidConnection:ot,onError:rt,style:it,id:at,nodeDragThreshold:st,...lt},ct)=>{const ut=at||"1";return t.createElement("div",{...lt,style:{...it,...qa},ref:ct,className:n(["react-flow",a]),"data-testid":"rf__wrapper",id:at},t.createElement(Ya,null,t.createElement(Ba,{onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onNodeClick:c,onEdgeClick:u,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,nodeTypes:s,edgeTypes:l,connectionLineType:B,connectionLineStyle:H,connectionLineComponent:L,connectionLineContainerStyle:V,selectionKeyCode:Y,selectionOnDrag:K,selectionMode:Z,deleteKeyCode:X,multiSelectionKeyCode:j,panActivationKeyCode:F,zoomActivationKeyCode:W,onlyRenderVisibleElements:G,selectNodesOnDrag:Q,defaultViewport:ae,translateExtent:ce,minZoom:se,maxZoom:le,preventScrolling:ue,zoomOnScroll:fe,zoomOnPinch:ge,zoomOnDoubleClick:ve,panOnScroll:pe,panOnScrollSpeed:me,panOnScrollMode:ye,panOnDrag:be,onPaneClick:we,onPaneMouseEnter:xe,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,onSelectionContextMenu:z,onSelectionStart:R,onSelectionEnd:$,onEdgeContextMenu:Ne,onEdgeDoubleClick:ke,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ae,onEdgeMouseLeave:Oe,onReconnect:Re??Ie,onReconnectStart:$e??De,onReconnectEnd:Te??ze,reconnectRadius:Be??He,defaultMarkerColor:he,noDragClassName:Xe,noWheelClassName:Ye,noPanClassName:Ke,elevateEdgesOnSelect:Qe,rfId:ut,disableKeyboardA11y:Je,nodeOrigin:ne,nodeExtent:de}),t.createElement(ri,{nodes:e,edges:o,defaultNodes:r,defaultEdges:i,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,elevateNodesOnSelect:Ge,minZoom:se,maxZoom:le,nodeExtent:de,onNodesChange:Le,onEdgesChange:Ve,snapToGrid:q,snapGrid:U,connectionMode:T,translateExtent:ce,connectOnClick:je,defaultEdgeOptions:Ue,fitView:Ze,fitViewOptions:Fe,onNodesDelete:k,onEdgesDelete:P,onNodeDragStart:C,onNodeDrag:M,onNodeDragStop:N,onSelectionDrag:I,onSelectionDragStart:O,onSelectionDragStop:D,noPanClassName:Ke,nodeOrigin:ne,rfId:ut,autoPanOnConnect:et,autoPanOnNodeDrag:tt,onError:rt,connectionRadius:nt,isValidConnection:ot,nodeDragThreshold:st}),t.createElement(ei,{onSelectionChange:A}),Me,t.createElement(Ao,{proOptions:qe,position:We}),t.createElement(di,{rfId:ut,disableKeyboardA11y:Je})))}));function Ga(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},t.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function Qa(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},t.createElement("path",{d:"M0 0h32v4.2H0z"}))}function Ja(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},t.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function es(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},t.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function ts(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},t.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}Ua.displayName="ReactFlow";const ns=({children:e,className:o,...r})=>t.createElement("button",{type:"button",className:n(["react-flow__controls-button",o]),...r},e);ns.displayName="ControlButton";const os=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),rs=({style:e,showZoom:r=!0,showFitView:i=!0,showInteractive:a=!0,fitViewOptions:s,onZoomIn:l,onZoomOut:c,onFitView:u,onInteractiveChange:d,className:h,children:f,position:g="bottom-left"})=>{const p=No(),[m,y]=t.useState(!1),{isInteractive:v,minZoomReached:b,maxZoomReached:w}=Mo(os,o),{zoomIn:x,zoomOut:S,fitView:E}=Ci();if(t.useEffect((()=>{y(!0)}),[]),!m)return null;return t.createElement(Po,{className:n(["react-flow__controls",h]),position:g,style:e,"data-testid":"rf__controls"},r&&t.createElement(t.Fragment,null,t.createElement(ns,{onClick:()=>{x(),l?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:w},t.createElement(Ga,null)),t.createElement(ns,{onClick:()=>{S(),c?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:b},t.createElement(Qa,null))),i&&t.createElement(ns,{className:"react-flow__controls-fitview",onClick:()=>{E(s),u?.()},title:"fit view","aria-label":"fit view"},t.createElement(Ja,null)),a&&t.createElement(ns,{className:"react-flow__controls-interactive",onClick:()=>{p.setState({nodesDraggable:!v,nodesConnectable:!v,elementsSelectable:!v}),d?.(!v)},title:"toggle interactivity","aria-label":"toggle interactivity"},v?t.createElement(ts,null):t.createElement(es,null)),f)};rs.displayName="Controls";var is=t.memo(rs);e.ControlButton=ns,e.Controls=is}));
