'use client';

/**
 * 策略编辑器页面
 * 
 * 可视化策略构建工具，支持拖拽式策略编辑
 */

import React, { useState, useCallback, useRef } from 'react';
import {
  Card,
  Button,
  Typography,
  Space,
  Drawer,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Modal,
  Tabs,
  Alert,
  Tag,
  Tooltip,
} from 'antd';
import {
  PlayCircleOutlined,
  SaveOutlined,
  SettingOutlined,
  CodeOutlined,
  EyeOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { motion } from 'framer-motion';

import { useAuth } from '@/store/auth';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

// 节点类型定义
interface StrategyNode extends Node {
  type: 'dataSource' | 'indicator' | 'signal' | 'condition' | 'action';
  data: {
    label: string;
    config: Record<string, any>;
    description?: string;
  };
}

// 预定义节点模板
const nodeTemplates = {
  dataSource: [
    {
      id: 'price-data',
      label: '价格数据',
      description: '获取股票价格数据',
      config: { symbols: [], timeframe: 'daily' },
    },
    {
      id: 'volume-data',
      label: '成交量数据',
      description: '获取成交量数据',
      config: { symbols: [], timeframe: 'daily' },
    },
  ],
  indicator: [
    {
      id: 'ma',
      label: '移动平均线',
      description: '计算移动平均线',
      config: { period: 20, type: 'simple' },
    },
    {
      id: 'rsi',
      label: 'RSI指标',
      description: '相对强弱指标',
      config: { period: 14 },
    },
    {
      id: 'macd',
      label: 'MACD指标',
      description: '指数平滑移动平均线',
      config: { fast: 12, slow: 26, signal: 9 },
    },
  ],
  signal: [
    {
      id: 'cross-over',
      label: '金叉信号',
      description: '快线上穿慢线',
      config: { threshold: 0 },
    },
    {
      id: 'cross-under',
      label: '死叉信号',
      description: '快线下穿慢线',
      config: { threshold: 0 },
    },
  ],
  condition: [
    {
      id: 'price-condition',
      label: '价格条件',
      description: '价格满足条件',
      config: { operator: '>', value: 0 },
    },
    {
      id: 'volume-condition',
      label: '成交量条件',
      description: '成交量满足条件',
      config: { operator: '>', value: 0 },
    },
  ],
  action: [
    {
      id: 'buy-action',
      label: '买入操作',
      description: '执行买入操作',
      config: { quantity: 100, orderType: 'market' },
    },
    {
      id: 'sell-action',
      label: '卖出操作',
      description: '执行卖出操作',
      config: { quantity: 100, orderType: 'market' },
    },
  ],
};

export default function StrategyEditorPage() {
  const { user } = useAuth();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<StrategyNode | null>(null);
  const [showNodePanel, setShowNodePanel] = useState(false);
  const [showConfigDrawer, setShowConfigDrawer] = useState(false);
  const [showCodeModal, setShowCodeModal] = useState(false);
  const [strategyName, setStrategyName] = useState('新策略');
  const [strategyDescription, setStrategyDescription] = useState('');
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  // 连接节点
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // 拖拽添加节点
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      const nodeData = JSON.parse(event.dataTransfer.getData('application/nodedata'));

      if (typeof type === 'undefined' || !type || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: StrategyNode = {
        id: `${type}-${Date.now()}`,
        type: type as any,
        position,
        data: {
          label: nodeData.label,
          config: { ...nodeData.config },
          description: nodeData.description,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // 节点点击事件
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node as StrategyNode);
    setShowConfigDrawer(true);
  }, []);

  // 保存策略
  const handleSaveStrategy = async () => {
    try {
      const strategyData = {
        name: strategyName,
        description: strategyDescription,
        nodes: nodes,
        edges: edges,
        config: {
          // 策略配置参数
        },
      };

      // 这里应该调用API保存策略
      // await apiClient.post('/strategy/save', strategyData);
      
      message.success('策略保存成功！');
    } catch (error) {
      message.error('策略保存失败');
    }
  };

  // 运行回测
  const handleRunBacktest = () => {
    if (nodes.length === 0) {
      message.warning('请先添加策略节点');
      return;
    }

    Modal.confirm({
      title: '运行回测',
      content: '确定要运行当前策略的回测吗？',
      onOk: async () => {
        try {
          // 这里应该调用回测API
          message.success('回测任务已提交，请稍后查看结果');
        } catch (error) {
          message.error('回测启动失败');
        }
      },
    });
  };

  // 生成代码
  const generateCode = () => {
    const code = `
# 策略代码 - ${strategyName}
# ${strategyDescription}

import jqdata
import pandas as pd
import numpy as np

def initialize(context):
    """初始化函数"""
    pass

def handle_data(context, data):
    """主要逻辑函数"""
    # 策略逻辑将在这里生成
    pass

def before_trading_start(context):
    """开盘前运行"""
    pass

def after_trading_end(context):
    """收盘后运行"""
    pass
    `;
    
    return code;
  };

  // 节点面板组件
  const NodePanel = () => (
    <div className="w-64 bg-white border-r border-gray-200 p-4 overflow-y-auto">
      <Title level={4} className="!mb-4">策略组件</Title>
      
      {Object.entries(nodeTemplates).map(([category, templates]) => (
        <div key={category} className="mb-6">
          <Text strong className="block mb-2 text-gray-600 uppercase text-xs">
            {category === 'dataSource' && '数据源'}
            {category === 'indicator' && '技术指标'}
            {category === 'signal' && '信号'}
            {category === 'condition' && '条件'}
            {category === 'action' && '操作'}
          </Text>
          
          <div className="space-y-2">
            {templates.map((template) => (
              <motion.div
                key={template.id}
                className="p-3 border border-gray-200 rounded-lg cursor-move hover:border-blue-400 hover:shadow-sm transition-all"
                draggable
                onDragStart={(event) => {
                  event.dataTransfer.setData('application/reactflow', category);
                  event.dataTransfer.setData('application/nodedata', JSON.stringify(template));
                  event.dataTransfer.effectAllowed = 'move';
                }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="font-medium text-sm">{template.label}</div>
                <div className="text-xs text-gray-500 mt-1">{template.description}</div>
              </motion.div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Input
              value={strategyName}
              onChange={(e) => setStrategyName(e.target.value)}
              className="font-medium text-lg border-none p-0 w-48"
              placeholder="策略名称"
            />
            <Tag color="blue">草稿</Tag>
          </div>
          
          <Space>
            <Button
              icon={<CodeOutlined />}
              onClick={() => setShowCodeModal(true)}
            >
              查看代码
            </Button>
            <Button
              icon={<EyeOutlined />}
              onClick={() => setShowNodePanel(!showNodePanel)}
            >
              {showNodePanel ? '隐藏' : '显示'}组件
            </Button>
            <Button
              icon={<PlayCircleOutlined />}
              type="primary"
              onClick={handleRunBacktest}
            >
              运行回测
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveStrategy}
            >
              保存策略
            </Button>
          </Space>
        </div>
      </div>

      {/* 主要编辑区域 */}
      <div className="flex-1 flex">
        {/* 节点面板 */}
        {showNodePanel && <NodePanel />}
        
        {/* 流程图编辑器 */}
        <div className="flex-1 relative" ref={reactFlowWrapper}>
          <ReactFlowProvider>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeClick={onNodeClick}
              fitView
            >
              <Controls />
              <MiniMap />
              <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
            </ReactFlow>
          </ReactFlowProvider>
          
          {/* 空状态提示 */}
          {nodes.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <Text type="secondary" className="text-lg block mb-2">
                  开始构建您的量化策略
                </Text>
                <Text type="secondary">
                  从左侧拖拽组件到画布中，然后连接它们
                </Text>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 节点配置抽屉 */}
      <Drawer
        title={`配置 - ${selectedNode?.data.label}`}
        placement="right"
        onClose={() => setShowConfigDrawer(false)}
        open={showConfigDrawer}
        width={400}
      >
        {selectedNode && (
          <div className="space-y-4">
            <Alert
              message={selectedNode.data.description}
              type="info"
              showIcon
            />
            
            <Form layout="vertical">
              {/* 根据节点类型显示不同的配置项 */}
              {selectedNode.type === 'indicator' && selectedNode.data.config.period && (
                <Form.Item label="周期">
                  <InputNumber
                    value={selectedNode.data.config.period}
                    onChange={(value) => {
                      const updatedNode = {
                        ...selectedNode,
                        data: {
                          ...selectedNode.data,
                          config: {
                            ...selectedNode.data.config,
                            period: value,
                          },
                        },
                      };
                      setNodes((nds) =>
                        nds.map((node) =>
                          node.id === selectedNode.id ? updatedNode : node
                        )
                      );
                    }}
                  />
                </Form.Item>
              )}
              
              {/* 更多配置项... */}
            </Form>
          </div>
        )}
      </Drawer>

      {/* 代码查看模态框 */}
      <Modal
        title="策略代码"
        open={showCodeModal}
        onCancel={() => setShowCodeModal(false)}
        width={800}
        footer={[
          <Button key="copy" icon={<CopyOutlined />}>
            复制代码
          </Button>,
          <Button key="close" onClick={() => setShowCodeModal(false)}>
            关闭
          </Button>,
        ]}
      >
        <pre className="bg-gray-50 p-4 rounded-lg text-sm overflow-auto max-h-96">
          {generateCode()}
        </pre>
      </Modal>
    </div>
  );
}
