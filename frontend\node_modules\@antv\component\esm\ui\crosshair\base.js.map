{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../src/ui/crosshair/base.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAGvC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,MAAM,QAAQ,CAAC;AAC7B,OAAO,EAAE,4BAA4B,EAAE,MAAM,YAAY,CAAC;AAG1D;IAA+E,iCAAY;IAyCzF,uBAAY,OAA6B;QACvC,aAAa;QACb,OAAA,MAAK,YAAC,OAAO,EAAE,4BAA4B,CAAC,SAAC;IAC/C,CAAC;IA3BD,sBAAc,uCAAY;QAH1B;;WAEG;aACH;YACQ,IAAA,KAAA,OAAW,IAAI,CAAC,WAAW,EAAE,IAAA,EAA5B,EAAE,QAAA,EAAE,EAAE,QAAsB,CAAC;YAC9B,IAAA,KAAA,OAAS,IAAI,CAAC,OAAO,IAAA,EAApB,CAAC,QAAA,EAAE,CAAC,QAAgB,CAAC;YAC5B,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1B,CAAC;;;OAAA;IAOD,sBAAY,mCAAQ;aAApB;YACE,IAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAED,sBAAY,yCAAc;aAA1B;YACE,IAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACrD,6BACK,KAAK,KACR,CAAC,EAAE,IAAI,CAAC,aAAa,IACrB;QACJ,CAAC;;;OAAA;IAOM,8BAAM,GAAb,UAAc,UAAmC,EAAE,SAAgB;QACjE,IAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACvF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;aAC1B,sBAAsB,CAAC,eAAe,EAAE,cAAM,OAAA,IAAI,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAA5B,CAA4B,CAAC;aAC3E,MAAM,CAAC,QAAQ,CAAC;aAChB,IAAI,EAAE,CAAC;QACV,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC;QAEpH,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,kCAAU,GAAjB,UAAkB,OAAc;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAnEa,iBAAG,GAAG,gBAAgB,CAAC;IAyEvC,oBAAC;CAAA,AA1ED,CAA+E,SAAS,GA0EvF;SA1EqB,aAAa"}