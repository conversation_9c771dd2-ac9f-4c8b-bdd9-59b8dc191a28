'use client';

/**
 * 股票价格预测组件
 * 
 * 使用机器学习模型预测股票未来价格走势
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Alert,
  Spin,
  Typography,
  Row,
  Col,
  Progress,
  Tag,
  Tooltip,
  message
} from 'antd';
import {
  RobotOutlined,
  LineChartOutlined,
  BulbOutlined,
  InfoCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import * as echarts from 'echarts';
import { motion } from 'framer-motion';

import { apiClient } from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface PredictionResult {
  success: boolean;
  symbol: string;
  predictions: Array<{
    date: string;
    predicted_price: number;
    day_ahead: number;
  }>;
  confidence: number;
  model_type: string;
  prediction_date: string;
  days_ahead: number;
}

interface PricePredictionProps {
  symbol?: string;
  onSymbolChange?: (symbol: string) => void;
}

export const PricePrediction: React.FC<PricePredictionProps> = ({
  symbol: initialSymbol = '000001.XSHE',
  onSymbolChange
}) => {
  const [form] = Form.useForm();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [predictionResult, setPredictionResult] = useState<PredictionResult | null>(null);
  const [symbol, setSymbol] = useState(initialSymbol);
  const [daysAhead, setDaysAhead] = useState(5);
  const [modelType, setModelType] = useState('random_forest');

  // 模型类型选项
  const modelOptions = [
    { value: 'auto_select', label: '自动选择', description: '自动选择最佳模型', category: '智能' },
    { value: 'ensemble', label: '集成模型', description: '多模型投票预测', category: '智能' },
    { value: 'random_forest', label: '随机森林', description: '适合复杂非线性关系', category: '树模型' },
    { value: 'gradient_boosting', label: '梯度提升', description: '高精度预测模型', category: '树模型' },
    { value: 'extra_trees', label: '极端随机树', description: '更快的随机森林变体', category: '树模型' },
    { value: 'ada_boost', label: 'AdaBoost', description: '自适应提升算法', category: '树模型' },
    { value: 'neural_network', label: '神经网络', description: '深度学习模型', category: '神经网络' },
    { value: 'svr', label: '支持向量回归', description: '适合小样本数据', category: '核方法' },
    { value: 'knn', label: 'K近邻', description: '基于相似性的预测', category: '实例学习' },
    { value: 'linear_regression', label: '线性回归', description: '简单快速的基础模型', category: '线性模型' },
    { value: 'ridge', label: '岭回归', description: '带L2正则化的线性回归', category: '线性模型' },
    { value: 'lasso', label: 'Lasso回归', description: '带L1正则化的线性回归', category: '线性模型' },
    { value: 'elastic_net', label: '弹性网络', description: '结合L1和L2正则化', category: '线性模型' }
  ];

  // 初始化图表
  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const handleResize = () => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      };
      
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartInstance.current) {
          chartInstance.current.dispose();
        }
      };
    }
  }, []);

  // 更新图表
  useEffect(() => {
    if (predictionResult && chartInstance.current) {
      updateChart();
    }
  }, [predictionResult]);

  const updateChart = () => {
    if (!predictionResult || !chartInstance.current) return;

    const dates = predictionResult.predictions.map(p => p.date);
    const prices = predictionResult.predictions.map(p => p.predicted_price);
    
    // 生成当前价格点（模拟）
    const currentPrice = prices[0] * 0.98; // 假设当前价格略低于第一个预测价格
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() - 1);
    
    const allDates = [currentDate.toISOString().split('T')[0], ...dates];
    const allPrices = [currentPrice, ...prices];

    const option: echarts.EChartsOption = {
      title: {
        text: `${symbol} 价格预测`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const param = params[0];
          const isCurrentPrice = param.dataIndex === 0;
          const confidence = predictionResult.confidence;
          
          return `
            <div>
              <div><strong>${param.name}</strong></div>
              <div>${isCurrentPrice ? '当前价格' : '预测价格'}: ¥${param.value.toFixed(2)}</div>
              ${!isCurrentPrice ? `<div>置信度: ${(confidence * 100).toFixed(1)}%</div>` : ''}
            </div>
          `;
        }
      },
      xAxis: {
        type: 'category',
        data: allDates,
        axisLabel: {
          formatter: function (value: string) {
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '价格 (¥)',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '价格',
          type: 'line',
          data: allPrices,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3
          },
          itemStyle: {
            color: function (params: any) {
              return params.dataIndex === 0 ? '#52c41a' : '#1890ff';
            }
          },
          markPoint: {
            data: [
              {
                name: '当前价格',
                coord: [allDates[0], allPrices[0]],
                itemStyle: {
                  color: '#52c41a'
                }
              }
            ]
          },
          markLine: {
            data: [
              {
                xAxis: allDates[0],
                lineStyle: {
                  color: '#ff4d4f',
                  type: 'dashed'
                },
                label: {
                  formatter: '预测起点'
                }
              }
            ]
          }
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    };

    chartInstance.current.setOption(option);
  };

  const handlePredict = async (values: any) => {
    setLoading(true);
    
    try {
      const response = await apiClient.post('/api/v1/ml/predict/price', null, {
        params: {
          symbol: values.symbol,
          days_ahead: values.days_ahead,
          model_type: values.model_type
        }
      });

      if (response.data.success) {
        setPredictionResult(response.data.data);
        message.success('预测完成！');
      } else {
        message.error(response.data.message || '预测失败');
      }
    } catch (error: any) {
      console.error('Prediction error:', error);
      message.error(error.response?.data?.detail || '预测失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高';
    if (confidence >= 0.6) return '中';
    return '低';
  };

  return (
    <div className="space-y-6">
      {/* 配置表单 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={
            <Space>
              <RobotOutlined />
              <span>AI价格预测</span>
            </Space>
          }
          extra={
            <Tooltip title="使用机器学习模型预测股票未来价格走势">
              <InfoCircleOutlined />
            </Tooltip>
          }
        >
          <Form
            form={form}
            layout="inline"
            onFinish={handlePredict}
            initialValues={{
              symbol: initialSymbol,
              days_ahead: 5,
              model_type: 'random_forest'
            }}
          >
            <Form.Item
              name="symbol"
              label="股票代码"
              rules={[{ required: true, message: '请输入股票代码' }]}
            >
              <Input
                placeholder="如: 000001.XSHE"
                style={{ width: 150 }}
                onChange={(e) => {
                  setSymbol(e.target.value);
                  onSymbolChange?.(e.target.value);
                }}
              />
            </Form.Item>

            <Form.Item
              name="days_ahead"
              label="预测天数"
              rules={[{ required: true, message: '请选择预测天数' }]}
            >
              <Select
                style={{ width: 100 }}
                onChange={setDaysAhead}
              >
                <Option value={3}>3天</Option>
                <Option value={5}>5天</Option>
                <Option value={7}>7天</Option>
                <Option value={10}>10天</Option>
                <Option value={15}>15天</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="model_type"
              label="模型类型"
              rules={[{ required: true, message: '请选择模型类型' }]}
            >
              <Select
                style={{ width: 180 }}
                onChange={setModelType}
                placeholder="选择预测模型"
              >
                {/* 按类别分组显示模型选项 */}
                <Select.OptGroup label="🤖 智能模型">
                  {modelOptions.filter(option => option.category === '智能').map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tooltip title={option.description}>
                        <div className="flex justify-between items-center">
                          <span>{option.label}</span>
                          {option.value === 'auto_select' && <Tag size="small" color="gold">推荐</Tag>}
                        </div>
                      </Tooltip>
                    </Option>
                  ))}
                </Select.OptGroup>

                <Select.OptGroup label="🌳 树模型">
                  {modelOptions.filter(option => option.category === '树模型').map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tooltip title={option.description}>
                        {option.label}
                      </Tooltip>
                    </Option>
                  ))}
                </Select.OptGroup>

                <Select.OptGroup label="🧠 神经网络">
                  {modelOptions.filter(option => option.category === '神经网络').map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tooltip title={option.description}>
                        <div className="flex justify-between items-center">
                          <span>{option.label}</span>
                          <Tag size="small" color="purple">深度学习</Tag>
                        </div>
                      </Tooltip>
                    </Option>
                  ))}
                </Select.OptGroup>

                <Select.OptGroup label="📐 线性模型">
                  {modelOptions.filter(option => option.category === '线性模型').map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tooltip title={option.description}>
                        {option.label}
                      </Tooltip>
                    </Option>
                  ))}
                </Select.OptGroup>

                <Select.OptGroup label="🔍 其他模型">
                  {modelOptions.filter(option => ['核方法', '实例学习'].includes(option.category)).map(option => (
                    <Option key={option.value} value={option.value}>
                      <Tooltip title={option.description}>
                        {option.label}
                      </Tooltip>
                    </Option>
                  ))}
                </Select.OptGroup>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<BulbOutlined />}
              >
                开始预测
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </motion.div>

      {/* 预测结果 */}
      {predictionResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Row gutter={[16, 16]}>
            {/* 预测概览 */}
            <Col xs={24} lg={8}>
              <Card title="预测概览" size="small">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Text type="secondary">股票代码:</Text>
                    <Text strong>{predictionResult.symbol}</Text>
                  </div>
                  <div className="flex justify-between items-center">
                    <Text type="secondary">预测天数:</Text>
                    <Text>{predictionResult.days_ahead}天</Text>
                  </div>
                  <div className="flex justify-between items-center">
                    <Text type="secondary">模型类型:</Text>
                    <Tag color="blue">
                      {modelOptions.find(m => m.value === predictionResult.model_type)?.label}
                    </Tag>
                  </div>
                  <div className="flex justify-between items-center">
                    <Text type="secondary">预测置信度:</Text>
                    <Space>
                      <Progress
                        percent={predictionResult.confidence * 100}
                        size="small"
                        strokeColor={getConfidenceColor(predictionResult.confidence)}
                        showInfo={false}
                        style={{ width: 60 }}
                      />
                      <Text style={{ color: getConfidenceColor(predictionResult.confidence) }}>
                        {getConfidenceText(predictionResult.confidence)}
                      </Text>
                    </Space>
                  </div>
                </div>
              </Card>
            </Col>

            {/* 预测数据 */}
            <Col xs={24} lg={16}>
              <Card title="预测价格" size="small">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  {predictionResult.predictions.map((pred, index) => (
                    <div key={index} className="text-center p-3 bg-gray-50 rounded">
                      <div className="text-sm text-gray-500 mb-1">
                        {new Date(pred.date).toLocaleDateString()}
                      </div>
                      <div className="text-lg font-bold text-blue-600">
                        ¥{pred.predicted_price.toFixed(2)}
                      </div>
                      <div className="text-xs text-gray-400">
                        第{pred.day_ahead}天
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        </motion.div>
      )}

      {/* 预测图表 */}
      {predictionResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card
            title={
              <Space>
                <LineChartOutlined />
                <span>价格走势预测</span>
              </Space>
            }
            extra={
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => updateChart()}
              >
                刷新图表
              </Button>
            }
          >
            <div
              ref={chartRef}
              style={{ width: '100%', height: '400px' }}
            />
            
            <Alert
              message="预测说明"
              description={`基于${modelOptions.find(m => m.value === predictionResult.model_type)?.label}模型，分析历史价格数据和技术指标生成预测。预测结果仅供参考，投资有风险，决策需谨慎。`}
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </motion.div>
      )}
    </div>
  );
};
