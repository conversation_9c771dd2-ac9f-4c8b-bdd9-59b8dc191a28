"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowTriangleDown = void 0;
const color_1 = require("./color");
/**
 * ▽
 */
const HollowTriangleDown = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'triangle-down' }, options), context);
};
exports.HollowTriangleDown = HollowTriangleDown;
exports.HollowTriangleDown.props = Object.assign({ defaultMarker: 'hollowTriangleDown' }, color_1.Color.props);
//# sourceMappingURL=hollowTriangleDown.js.map