# ============================================================================
# 智能量化交易平台 - 前端服务启动脚本
# ============================================================================

Write-Host "🚀 启动智能量化交易平台前端服务..." -ForegroundColor Green

# 检查Node.js环境
Write-Host "📋 检查Node.js环境..." -ForegroundColor Yellow
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: 未找到Node.js，请先安装Node.js 18+" -ForegroundColor Red
    Write-Host "💡 下载地址: https://nodejs.org/" -ForegroundColor Cyan
    exit 1
}

$nodeVersion = node --version
Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green

# 检查npm
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: 未找到npm" -ForegroundColor Red
    exit 1
}

$npmVersion = npm --version
Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green

# 进入前端目录
Set-Location frontend

# 检查package.json
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误: 未找到package.json文件" -ForegroundColor Red
    exit 1
}

# 检查node_modules
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 安装前端依赖..." -ForegroundColor Yellow
    Write-Host "⏳ 这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow
    
    try {
        npm install
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  npm install失败，尝试使用yarn..." -ForegroundColor Yellow
        if (Get-Command yarn -ErrorAction SilentlyContinue) {
            yarn install
        } else {
            Write-Host "❌ 依赖安装失败，请手动运行: npm install" -ForegroundColor Red
            exit 1
        }
    }
} else {
    Write-Host "✅ 依赖已安装" -ForegroundColor Green
}

# 检查环境变量文件
if (-not (Test-Path ".env.local")) {
    Write-Host "📝 创建环境变量文件..." -ForegroundColor Yellow
    @"
# 智能量化交易平台 - 前端环境变量
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=智能量化交易平台
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development
"@ | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-Host "✅ 环境变量文件创建完成" -ForegroundColor Green
}

# 启动开发服务器
Write-Host "🌟 启动Next.js开发服务器..." -ForegroundColor Green
Write-Host "📍 服务地址: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔄 按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host "💡 确保后端服务已在 http://localhost:8000 运行" -ForegroundColor Yellow
Write-Host ""

try {
    npm run dev
} catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
    Write-Host "💡 请检查依赖是否正确安装" -ForegroundColor Yellow
    Write-Host "💡 尝试删除node_modules文件夹后重新运行" -ForegroundColor Yellow
    exit 1
}

Write-Host "👋 前端服务已停止" -ForegroundColor Yellow
