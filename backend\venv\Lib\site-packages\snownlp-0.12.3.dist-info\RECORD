snownlp-0.12.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
snownlp-0.12.3.dist-info/METADATA,sha256=ul5P1fNFyaEWv4R2EGEY7pDkUgEuvjrOvAlo98hDoD8,483
snownlp-0.12.3.dist-info/RECORD,,
snownlp-0.12.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
snownlp-0.12.3.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
snownlp-0.12.3.dist-info/licenses/LICENSE.md,sha256=FstaM2tONeM2fTSGhfMFIazNDIq_GyCQHW6DIU6HCQ0,1079
snownlp-0.12.3.dist-info/top_level.txt,sha256=Zhp5vIcQfZIfy3XEVi9Y1ba256lc9sIG3THnLmNYeKk,8
snownlp/__init__.py,sha256=K3Ey06eLryXXGuMoMXGffXQ23QY2SkvrtFePwS_n9QE,1945
snownlp/__pycache__/__init__.cpython-312.pyc,,
snownlp/classification/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
snownlp/classification/__pycache__/__init__.cpython-312.pyc,,
snownlp/classification/__pycache__/bayes.cpython-312.pyc,,
snownlp/classification/bayes.py,sha256=MDl0386y2wA549g7hHs98Uv7xKMlMB_wVBa3UoFSWa4,2134
snownlp/normal/__init__.py,sha256=rVKeJTGGXr17XoVlLKDY90otnuP2uSJGKdwUPl1UJHM,1455
snownlp/normal/__pycache__/__init__.cpython-312.pyc,,
snownlp/normal/__pycache__/pinyin.cpython-312.pyc,,
snownlp/normal/__pycache__/zh.cpython-312.pyc,,
snownlp/normal/pinyin.py,sha256=jOWrU5AZ7YaWygBdnv9v98ZXdpQxPhRVy3EKSdh3hcg,615
snownlp/normal/pinyin.txt,sha256=jmXcGVDXGo_2FA07w9c_acSeo49Tip-ZHnUTA5wLNQU,971112
snownlp/normal/stopwords.txt,sha256=_HpVoPuGIs3I8S_001cD_henGpKnRrcJHh7KaSM5dBM,9227
snownlp/normal/zh.py,sha256=U3kWzmEhbAz7OR97CIG2fJoo_dj6JZCX__evmCl9Rrg,63545
snownlp/seg/__init__.py,sha256=-7zobCE5wr2GPBSunbqjF-Gmg0bMNAwekpX70vwnV-w,937
snownlp/seg/__pycache__/__init__.cpython-312.pyc,,
snownlp/seg/__pycache__/seg.cpython-312.pyc,,
snownlp/seg/__pycache__/y09_2047.cpython-312.pyc,,
snownlp/seg/data.txt,sha256=-GEXKmIBgVvm7vYFNlllQX1uswfNDwNyJn_9O8MKFP0,11048911
snownlp/seg/seg.marshal,sha256=S9G4cSXRPJv3x_h99dJQ-925odrtWMci9JRNmIOIcxo,11055603
snownlp/seg/seg.marshal.3,sha256=zN8t9xnKmPKPIZTrbR3zH_cy-NQ0r8SkP2cHhKcHTP0,13086974
snownlp/seg/seg.py,sha256=VXdzQ6ElPN9BXhy71ck5LKQlRqSs_Jg03gJn9LmznjQ,1464
snownlp/seg/y09_2047.py,sha256=aFTpW6g2y2bNuFPrwHQJWDfyp4kLIPdcDdHFybOYSbw,4360
snownlp/sentiment/__init__.py,sha256=4FBeY1hm5SUosZWx5nYl9jAOtwaODH0mvH0zZtsxbdk,1581
snownlp/sentiment/__pycache__/__init__.cpython-312.pyc,,
snownlp/sentiment/neg.txt,sha256=NfqTiPkCKxu-gG-2E1XtSEwwSwApgL8AZMEB9Ra1M5I,3439883
snownlp/sentiment/pos.txt,sha256=cP6FByZtCtqC4M1Lpl1AgjGxQsiwoAIz87fs7Hk8aD0,3981149
snownlp/sentiment/sentiment.marshal,sha256=UA4mH7zxkgdQDRnmUcXnhxaiMChOdaKl0cYyM6ym4LA,301734
snownlp/sentiment/sentiment.marshal.3,sha256=cOP-7vdopufYq98Ay_jM58IL6ISwPaFu_UwO36rd1NE,314206
snownlp/sim/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
snownlp/sim/__pycache__/__init__.cpython-312.pyc,,
snownlp/sim/__pycache__/bm25.cpython-312.pyc,,
snownlp/sim/bm25.py,sha256=SgvqIEFGZonn0qulqbGs_E79qkcCcM7Xe-GMB6zdgCI,1443
snownlp/summary/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
snownlp/summary/__pycache__/__init__.cpython-312.pyc,,
snownlp/summary/__pycache__/textrank.cpython-312.pyc,,
snownlp/summary/__pycache__/words_merge.cpython-312.pyc,,
snownlp/summary/textrank.py,sha256=Kdhgy02lOckvrbDqCIyJ965luM1hYTx7MbTqVMyaWBA,3525
snownlp/summary/words_merge.py,sha256=eKep6nMijy6SpeLL9sZKgun8_IacgzAkZkoWmuJoO9w,1281
snownlp/tag/199801.txt,sha256=mHwrJic62gEYZk4BN-v6ca8QitvNp5FCX3Nx2VLcdYs,10120457
snownlp/tag/__init__.py,sha256=eO9IwJ0IobnIijNG-rvE823Ti5w4lNy64u6dAN8_L5E,820
snownlp/tag/__pycache__/__init__.cpython-312.pyc,,
snownlp/tag/tag.marshal,sha256=bo8GOp0G9rFAIJ9Av67zO6rpEnyxOET15wtVG7W0vkk,1697393
snownlp/tag/tag.marshal.3,sha256=zxGTI6AXx01qAJ9sQ8knP0LJ-l4kabu0BWeG7UTJFbM,1519540
snownlp/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
snownlp/utils/__pycache__/__init__.cpython-312.pyc,,
snownlp/utils/__pycache__/frequency.cpython-312.pyc,,
snownlp/utils/__pycache__/good_turing.cpython-312.pyc,,
snownlp/utils/__pycache__/tnt.cpython-312.pyc,,
snownlp/utils/__pycache__/trie.cpython-312.pyc,,
snownlp/utils/frequency.py,sha256=wl81YJ2JXG2F-lpPSnDTg8EkQCoEMHIucpWpQ1FQy5I,1582
snownlp/utils/good_turing.py,sha256=RpZbBf4cYfPbou4XEIzi8qIm20zdZnrNW9f6TChgerY,1804
snownlp/utils/tnt.py,sha256=E5ui8lcDsLv6ENk9ca3FyCVfOh1i3NTEeb_L840z0yc,5187
snownlp/utils/trie.py,sha256=gNk6s-UA10W9660MNZIDDTU5cV1xYyg7YRXrvOZq3kM,1175
