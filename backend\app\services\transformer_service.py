"""
Transformer时间序列预测服务

实现基于Transformer的时间序列预测模型训练和推理
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
import math
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.logging import logger
from app.models.deep_learning import TransformerModel, TransformerPrediction
from app.services.feature_engineering_service import feature_engineering_service


class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]


class TimeSeriesTransformer(nn.Module):
    """时间序列Transformer模型"""
    
    def __init__(
        self,
        input_dim: int,
        d_model: int = 512,
        n_heads: int = 8,
        n_layers: int = 6,
        d_ff: int = 2048,
        dropout: float = 0.1,
        max_len: int = 5000
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.d_model = d_model
        
        # 输入投影
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, max_len)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_ff,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, n_layers)
        
        # 输出层
        self.output_projection = nn.Linear(d_model, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, src, src_mask=None):
        # 输入投影
        src = self.input_projection(src) * math.sqrt(self.d_model)
        
        # 位置编码
        src = self.pos_encoder(src.transpose(0, 1)).transpose(0, 1)
        
        # Transformer编码
        output = self.transformer_encoder(src, src_mask)
        
        # 输出投影（只取最后一个时间步）
        output = self.output_projection(self.dropout(output[:, -1, :]))
        
        return output


class TimeSeriesDataset(Dataset):
    """时间序列数据集"""
    
    def __init__(self, data: np.ndarray, sequence_length: int, prediction_length: int = 1):
        self.data = data
        self.sequence_length = sequence_length
        self.prediction_length = prediction_length
        
    def __len__(self):
        return len(self.data) - self.sequence_length - self.prediction_length + 1
    
    def __getitem__(self, idx):
        x = self.data[idx:idx + self.sequence_length]
        y = self.data[idx + self.sequence_length:idx + self.sequence_length + self.prediction_length, -1]  # 假设最后一列是目标
        return torch.FloatTensor(x), torch.FloatTensor(y)


class TransformerService:
    """Transformer服务"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models_dir = "transformer_models"
        os.makedirs(self.models_dir, exist_ok=True)
    
    async def create_model(
        self,
        user_id: int,
        model_config: Dict[str, Any],
        db: AsyncSession
    ) -> TransformerModel:
        """创建Transformer模型"""
        try:
            model = TransformerModel(
                user_id=user_id,
                name=model_config["name"],
                description=model_config.get("description", ""),
                model_type=model_config.get("model_type", "transformer"),
                sequence_length=model_config.get("sequence_length", 60),
                prediction_length=model_config.get("prediction_length", 1),
                d_model=model_config.get("d_model", 512),
                n_heads=model_config.get("n_heads", 8),
                n_layers=model_config.get("n_layers", 6),
                d_ff=model_config.get("d_ff", 2048),
                dropout=model_config.get("dropout", 0.1),
                batch_size=model_config.get("batch_size", 32),
                learning_rate=model_config.get("learning_rate", 0.0001),
                epochs=model_config.get("epochs", 100),
                target_columns=model_config.get("target_columns", ["close"]),
                feature_columns=model_config.get("feature_columns", []),
                normalization_method=model_config.get("normalization_method", "minmax")
            )
            
            db.add(model)
            await db.commit()
            await db.refresh(model)
            
            logger.info(f"创建Transformer模型成功: {model.id}")
            return model
            
        except Exception as e:
            logger.error(f"创建Transformer模型失败: {e}")
            await db.rollback()
            raise
    
    async def train_model(
        self,
        model_id: int,
        training_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """训练Transformer模型"""
        try:
            # 获取模型配置
            result = await db.execute(
                select(TransformerModel).where(TransformerModel.id == model_id)
            )
            model_config = result.scalar_one_or_none()
            
            if not model_config:
                raise ValueError(f"模型不存在: {model_id}")
            
            # 更新状态
            model_config.status = "training"
            model_config.training_progress = 0
            await db.commit()
            
            # 准备训练数据
            train_data, val_data, scaler = self._prepare_training_data(
                training_data, model_config
            )
            
            # 创建数据加载器
            train_dataset = TimeSeriesDataset(
                train_data, 
                model_config.sequence_length, 
                model_config.prediction_length
            )
            val_dataset = TimeSeriesDataset(
                val_data, 
                model_config.sequence_length, 
                model_config.prediction_length
            )
            
            train_loader = DataLoader(
                train_dataset, 
                batch_size=model_config.batch_size, 
                shuffle=True
            )
            val_loader = DataLoader(
                val_dataset, 
                batch_size=model_config.batch_size, 
                shuffle=False
            )
            
            # 创建模型
            input_dim = train_data.shape[1]
            transformer_model = TimeSeriesTransformer(
                input_dim=input_dim,
                d_model=model_config.d_model,
                n_heads=model_config.n_heads,
                n_layers=model_config.n_layers,
                d_ff=model_config.d_ff,
                dropout=model_config.dropout
            ).to(self.device)
            
            # 优化器和损失函数
            optimizer = optim.Adam(transformer_model.parameters(), lr=model_config.learning_rate)
            criterion = nn.MSELoss()
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
            
            # 训练循环
            best_val_loss = float('inf')
            patience_counter = 0
            train_losses = []
            val_losses = []
            
            for epoch in range(model_config.epochs):
                # 训练阶段
                transformer_model.train()
                train_loss = 0.0
                
                for batch_x, batch_y in train_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = transformer_model(batch_x)
                    loss = criterion(outputs.squeeze(), batch_y.squeeze())
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                
                train_loss /= len(train_loader)
                train_losses.append(train_loss)
                
                # 验证阶段
                transformer_model.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    for batch_x, batch_y in val_loader:
                        batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                        outputs = transformer_model(batch_x)
                        loss = criterion(outputs.squeeze(), batch_y.squeeze())
                        val_loss += loss.item()
                
                val_loss /= len(val_loader)
                val_losses.append(val_loss)
                
                # 学习率调度
                scheduler.step(val_loss)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    
                    # 保存最佳模型
                    model_path = os.path.join(self.models_dir, f"transformer_{model_id}.pth")
                    torch.save({
                        'model_state_dict': transformer_model.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scaler': scaler,
                        'config': {
                            'input_dim': input_dim,
                            'd_model': model_config.d_model,
                            'n_heads': model_config.n_heads,
                            'n_layers': model_config.n_layers,
                            'd_ff': model_config.d_ff,
                            'dropout': model_config.dropout
                        }
                    }, model_path)
                    
                    model_config.model_path = model_path
                    model_config.model_size = os.path.getsize(model_path)
                else:
                    patience_counter += 1
                
                # 更新进度
                progress = int((epoch + 1) / model_config.epochs * 100)
                model_config.training_progress = progress
                
                if epoch % 10 == 0:  # 每10个epoch更新一次数据库
                    await db.commit()
                
                # 早停
                if patience_counter >= model_config.early_stopping_patience:
                    logger.info(f"早停触发，epoch: {epoch}")
                    break
            
            # 计算测试指标
            test_metrics = self._evaluate_model(transformer_model, val_loader, scaler)
            
            # 更新模型状态
            model_config.status = "trained"
            model_config.training_progress = 100
            model_config.train_loss = train_losses[-1]
            model_config.val_loss = val_losses[-1]
            model_config.test_mse = test_metrics['mse']
            model_config.test_mae = test_metrics['mae']
            model_config.test_mape = test_metrics['mape']
            model_config.trained_at = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "model_id": model_id,
                "train_loss": train_losses[-1],
                "val_loss": val_losses[-1],
                "test_metrics": test_metrics,
                "epochs_trained": epoch + 1
            }
            
        except Exception as e:
            logger.error(f"训练Transformer模型失败: {e}")
            
            # 更新失败状态
            model_config.status = "failed"
            await db.commit()
            
            return {
                "success": False,
                "error": str(e)
            }
    
    def _prepare_training_data(
        self, 
        data: Dict[str, Any], 
        model_config: TransformerModel
    ) -> Tuple[np.ndarray, np.ndarray, Any]:
        """准备训练数据"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 选择特征列
            feature_cols = model_config.feature_columns or df.columns.tolist()
            if model_config.target_columns:
                # 确保目标列在最后
                target_col = model_config.target_columns[0]
                feature_cols = [col for col in feature_cols if col != target_col] + [target_col]
            
            df_features = df[feature_cols]
            
            # 数据归一化
            if model_config.normalization_method == "minmax":
                scaler = MinMaxScaler()
            else:
                scaler = StandardScaler()
            
            scaled_data = scaler.fit_transform(df_features.values)
            
            # 分割训练和验证集
            split_idx = int(len(scaled_data) * 0.8)
            train_data = scaled_data[:split_idx]
            val_data = scaled_data[split_idx:]
            
            return train_data, val_data, scaler
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            raise
    
    def _evaluate_model(
        self, 
        model: nn.Module, 
        data_loader: DataLoader, 
        scaler: Any
    ) -> Dict[str, float]:
        """评估模型性能"""
        try:
            model.eval()
            predictions = []
            actuals = []
            
            with torch.no_grad():
                for batch_x, batch_y in data_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_x)
                    
                    predictions.extend(outputs.cpu().numpy().flatten())
                    actuals.extend(batch_y.cpu().numpy().flatten())
            
            predictions = np.array(predictions)
            actuals = np.array(actuals)
            
            # 反归一化（假设目标列是最后一列）
            if hasattr(scaler, 'scale_'):
                target_scale = scaler.scale_[-1]
                target_min = scaler.min_[-1]
                
                predictions = predictions / target_scale - target_min / target_scale
                actuals = actuals / target_scale - target_min / target_scale
            
            # 计算指标
            mse = mean_squared_error(actuals, predictions)
            mae = mean_absolute_error(actuals, predictions)
            mape = np.mean(np.abs((actuals - predictions) / actuals)) * 100
            
            return {
                'mse': float(mse),
                'mae': float(mae),
                'mape': float(mape)
            }
            
        except Exception as e:
            logger.error(f"评估模型失败: {e}")
            return {'mse': 0.0, 'mae': 0.0, 'mape': 0.0}
    
    async def predict(
        self,
        model_id: int,
        input_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """使用Transformer模型进行预测"""
        try:
            # 获取模型配置
            result = await db.execute(
                select(TransformerModel).where(TransformerModel.id == model_id)
            )
            model_config = result.scalar_one_or_none()
            
            if not model_config or model_config.status != "trained":
                raise ValueError(f"模型不存在或未训练: {model_id}")
            
            # 加载模型
            checkpoint = torch.load(model_config.model_path, map_location=self.device)
            config = checkpoint['config']
            scaler = checkpoint['scaler']
            
            transformer_model = TimeSeriesTransformer(**config).to(self.device)
            transformer_model.load_state_dict(checkpoint['model_state_dict'])
            transformer_model.eval()
            
            # 准备输入数据
            df = pd.DataFrame(input_data)
            feature_cols = model_config.feature_columns or df.columns.tolist()
            if model_config.target_columns:
                target_col = model_config.target_columns[0]
                feature_cols = [col for col in feature_cols if col != target_col] + [target_col]
            
            df_features = df[feature_cols]
            scaled_data = scaler.transform(df_features.values)
            
            # 取最后sequence_length个时间步作为输入
            input_sequence = scaled_data[-model_config.sequence_length:]
            input_tensor = torch.FloatTensor(input_sequence).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                prediction = transformer_model(input_tensor)
                prediction = prediction.cpu().numpy().flatten()
            
            # 反归一化
            if hasattr(scaler, 'scale_'):
                target_scale = scaler.scale_[-1]
                target_min = scaler.min_[-1]
                prediction = prediction / target_scale - target_min / target_scale
            
            # 保存预测结果
            prediction_record = TransformerPrediction(
                model_id=model_id,
                user_id=model_config.user_id,
                symbol=input_data.get('symbol', 'UNKNOWN'),
                prediction_date=datetime.utcnow(),
                target_date=datetime.utcnow() + timedelta(days=model_config.prediction_length),
                predicted_values=prediction.tolist(),
                input_sequence=input_sequence.tolist(),
                model_version="1.0"
            )
            
            db.add(prediction_record)
            await db.commit()
            
            return {
                "success": True,
                "predictions": prediction.tolist(),
                "prediction_id": prediction_record.id
            }
            
        except Exception as e:
            logger.error(f"Transformer预测失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 全局Transformer服务实例
transformer_service = TransformerService()
