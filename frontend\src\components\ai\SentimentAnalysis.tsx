'use client';

/**
 * 情绪分析组件
 * 
 * 分析股票相关新闻和社交媒体的情绪倾向
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Progress,
  Typography,
  Row,
  Col,
  Tag,
  Alert,
  Statistic,
  message,
  Tooltip
} from 'antd';
import {
  HeartOutlined,
  SmileOutlined,
  FrownOutlined,
  MehOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined
} from '@ant-design/icons';
import * as echarts from 'echarts';
import { motion } from 'framer-motion';

import { apiClient } from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface SentimentResult {
  success: boolean;
  symbol: string;
  overall_sentiment: 'positive' | 'negative' | 'neutral';
  sentiment_label: string;
  sentiment_score: number;
  news_count: number;
  sentiment_distribution: {
    positive: number;
    negative: number;
    neutral: number;
  };
  analysis_date: string;
}

interface SentimentAnalysisProps {
  symbol?: string;
  onSymbolChange?: (symbol: string) => void;
}

export const SentimentAnalysis: React.FC<SentimentAnalysisProps> = ({
  symbol: initialSymbol = '000001.XSHE',
  onSymbolChange
}) => {
  const [form] = Form.useForm();
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [sentimentResult, setSentimentResult] = useState<SentimentResult | null>(null);

  // 初始化图表
  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const handleResize = () => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      };
      
      window.addEventListener('resize', handleResize);
      
      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartInstance.current) {
          chartInstance.current.dispose();
        }
      };
    }
  }, []);

  // 更新图表
  useEffect(() => {
    if (sentimentResult && chartInstance.current) {
      updateChart();
    }
  }, [sentimentResult]);

  const updateChart = () => {
    if (!sentimentResult || !chartInstance.current) return;

    const { sentiment_distribution } = sentimentResult;
    
    const option: echarts.EChartsOption = {
      title: {
        text: '情绪分布',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['积极', '消极', '中性']
      },
      series: [
        {
          name: '情绪分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            {
              value: sentiment_distribution.positive,
              name: '积极',
              itemStyle: { color: '#52c41a' }
            },
            {
              value: sentiment_distribution.negative,
              name: '消极',
              itemStyle: { color: '#ff4d4f' }
            },
            {
              value: sentiment_distribution.neutral,
              name: '中性',
              itemStyle: { color: '#faad14' }
            }
          ]
        }
      ]
    };

    chartInstance.current.setOption(option);
  };

  const handleAnalysis = async (values: any) => {
    setLoading(true);
    
    try {
      const response = await apiClient.post('/api/v1/ml/sentiment-analysis', null, {
        params: {
          symbol: values.symbol,
          news_count: values.news_count
        }
      });

      if (response.data.success) {
        setSentimentResult(response.data.data);
        message.success('情绪分析完成！');
      } else {
        message.error(response.data.message || '分析失败');
      }
    } catch (error: any) {
      console.error('Sentiment analysis error:', error);
      message.error(error.response?.data?.detail || '分析失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return <SmileOutlined style={{ color: '#52c41a' }} />;
      case 'negative':
        return <FrownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MehOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive':
        return '#52c41a';
      case 'negative':
        return '#ff4d4f';
      default:
        return '#faad14';
    }
  };

  const getSentimentTrend = (score: number) => {
    if (score > 0.1) {
      return <TrendingUpOutlined style={{ color: '#52c41a' }} />;
    } else if (score < -0.1) {
      return <TrendingDownOutlined style={{ color: '#ff4d4f' }} />;
    }
    return <MehOutlined style={{ color: '#faad14' }} />;
  };

  return (
    <div className="space-y-6">
      {/* 配置表单 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={
            <Space>
              <HeartOutlined />
              <span>情绪分析</span>
            </Space>
          }
          extra={
            <Tooltip title="分析股票相关新闻和社交媒体的情绪倾向">
              <InfoCircleOutlined />
            </Tooltip>
          }
        >
          <Form
            form={form}
            layout="inline"
            onFinish={handleAnalysis}
            initialValues={{
              symbol: initialSymbol,
              news_count: 50
            }}
          >
            <Form.Item
              name="symbol"
              label="股票代码"
              rules={[{ required: true, message: '请输入股票代码' }]}
            >
              <Input
                placeholder="如: 000001.XSHE"
                style={{ width: 150 }}
                onChange={(e) => onSymbolChange?.(e.target.value)}
              />
            </Form.Item>

            <Form.Item
              name="news_count"
              label="分析数量"
              rules={[{ required: true, message: '请选择分析数量' }]}
            >
              <Select style={{ width: 120 }}>
                <Option value={20}>20条新闻</Option>
                <Option value={50}>50条新闻</Option>
                <Option value={100}>100条新闻</Option>
                <Option value={200}>200条新闻</Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<HeartOutlined />}
              >
                开始分析
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </motion.div>

      {/* 分析结果 */}
      {sentimentResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Row gutter={[16, 16]}>
            {/* 总体情绪 */}
            <Col xs={24} lg={12}>
              <Card title="总体情绪" size="small">
                <div className="text-center space-y-4">
                  <div className="text-6xl">
                    {getSentimentIcon(sentimentResult.overall_sentiment)}
                  </div>
                  
                  <div>
                    <Title level={3} style={{ color: getSentimentColor(sentimentResult.overall_sentiment), margin: 0 }}>
                      {sentimentResult.sentiment_label}
                    </Title>
                    <Text type="secondary">整体市场情绪</Text>
                  </div>

                  <div className="flex justify-center items-center space-x-2">
                    <Text>情绪指数:</Text>
                    <Text
                      strong
                      style={{ 
                        color: getSentimentColor(sentimentResult.overall_sentiment),
                        fontSize: '18px'
                      }}
                    >
                      {(sentimentResult.sentiment_score * 100).toFixed(1)}
                    </Text>
                    {getSentimentTrend(sentimentResult.sentiment_score)}
                  </div>

                  <Progress
                    percent={Math.abs(sentimentResult.sentiment_score) * 100}
                    strokeColor={getSentimentColor(sentimentResult.overall_sentiment)}
                    showInfo={false}
                  />
                </div>
              </Card>
            </Col>

            {/* 情绪分布 */}
            <Col xs={24} lg={12}>
              <Card title="情绪分布" size="small">
                <div
                  ref={chartRef}
                  style={{ width: '100%', height: '250px' }}
                />
              </Card>
            </Col>
          </Row>
        </motion.div>
      )}

      {/* 详细统计 */}
      {sentimentResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card
            title="详细统计"
            extra={
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => form.submit()}
                loading={loading}
              >
                刷新分析
              </Button>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="积极情绪"
                  value={sentimentResult.sentiment_distribution.positive}
                  suffix="条"
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<SmileOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="消极情绪"
                  value={sentimentResult.sentiment_distribution.negative}
                  suffix="条"
                  valueStyle={{ color: '#ff4d4f' }}
                  prefix={<FrownOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="中性情绪"
                  value={sentimentResult.sentiment_distribution.neutral}
                  suffix="条"
                  valueStyle={{ color: '#faad14' }}
                  prefix={<MehOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="分析总数"
                  value={sentimentResult.news_count}
                  suffix="条"
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<InfoCircleOutlined />}
                />
              </Col>
            </Row>

            <div className="mt-6 space-y-3">
              <div className="flex justify-between items-center">
                <Text type="secondary">积极比例:</Text>
                <Space>
                  <Progress
                    percent={(sentimentResult.sentiment_distribution.positive / sentimentResult.news_count) * 100}
                    size="small"
                    strokeColor="#52c41a"
                    showInfo={false}
                    style={{ width: 100 }}
                  />
                  <Text>
                    {((sentimentResult.sentiment_distribution.positive / sentimentResult.news_count) * 100).toFixed(1)}%
                  </Text>
                </Space>
              </div>

              <div className="flex justify-between items-center">
                <Text type="secondary">消极比例:</Text>
                <Space>
                  <Progress
                    percent={(sentimentResult.sentiment_distribution.negative / sentimentResult.news_count) * 100}
                    size="small"
                    strokeColor="#ff4d4f"
                    showInfo={false}
                    style={{ width: 100 }}
                  />
                  <Text>
                    {((sentimentResult.sentiment_distribution.negative / sentimentResult.news_count) * 100).toFixed(1)}%
                  </Text>
                </Space>
              </div>

              <div className="flex justify-between items-center">
                <Text type="secondary">中性比例:</Text>
                <Space>
                  <Progress
                    percent={(sentimentResult.sentiment_distribution.neutral / sentimentResult.news_count) * 100}
                    size="small"
                    strokeColor="#faad14"
                    showInfo={false}
                    style={{ width: 100 }}
                  />
                  <Text>
                    {((sentimentResult.sentiment_distribution.neutral / sentimentResult.news_count) * 100).toFixed(1)}%
                  </Text>
                </Space>
              </div>
            </div>

            <Alert
              message="分析说明"
              description={`基于自然语言处理技术，分析了${sentimentResult.news_count}条相关新闻的情绪倾向。情绪分析结果可作为投资决策的参考因素之一，但不应作为唯一依据。`}
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </motion.div>
      )}
    </div>
  );
};
