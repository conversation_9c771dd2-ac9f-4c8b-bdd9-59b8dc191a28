'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Button,
  Space,
  Divider,
  Progress,
  Tag,
  Avatar,
  List,
  Badge,
  Tooltip,
  Spin
} from 'antd';
import {
  DashboardOutlined,
  RiseOutlined,
  FallOutlined,
  WalletOutlined,
  BarChartOutlined,
  RightOutlined,
  StockOutlined,
  FundOutlined,
  LineChartOutlined,
  TrendingUpOutlined,
  BellOutlined,
  EyeOutlined,
  FireOutlined,
  ThunderboltOutlined,
  CrownOutlined
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function DashboardPageContent() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [marketData, setMarketData] = useState({
    totalValue: 1250000,
    todayPnL: 15600,
    todayPnLPercent: 1.26,
    totalPnL: 250000,
    totalPnLPercent: 25.0
  });

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => setLoading(false), 1000);
  }, []);

  const handleQuickAction = (path: string) => {
    try {
      router.push(path);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const quickActions = [
    {
      title: '策略编辑器',
      description: '创建和编辑量化交易策略',
      icon: <BarChartOutlined className="text-2xl text-blue-500" />,
      path: '/dashboard/strategy/editor',
    },
    {
      title: '市场数据',
      description: '查看实时市场行情和数据',
      icon: <LineChartOutlined className="text-2xl text-green-500" />,
      path: '/dashboard/market',
    },
    {
      title: '投资组合',
      description: '管理和分析投资组合',
      icon: <WalletOutlined className="text-2xl text-purple-500" />,
      path: '/dashboard/portfolio',
    },
    {
      title: '系统设置',
      description: '配置JQData和个人偏好',
      icon: <StockOutlined className="text-2xl text-orange-500" />,
      path: '/dashboard/settings',
    },
  ];

  const stats = [
    {
      title: '总资产',
      value: '¥0.00',
      precision: 2,
      valueStyle: { color: '#3f8600' },
      prefix: <FundOutlined />,
    },
    {
      title: '今日收益',
      value: '¥0.00',
      precision: 2,
      valueStyle: { color: '#cf1322' },
      prefix: <RiseOutlined />,
    },
    {
      title: '活跃策略',
      value: 0,
      prefix: <BarChartOutlined />,
    },
    {
      title: '持仓股票',
      value: 0,
      prefix: <StockOutlined />,
    },
  ];

  return (
    <div className="p-6">
      {/* 欢迎区域 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <DashboardOutlined className="mr-3" />
          欢迎回来，{user?.username || '用户'}！
        </Title>
        <Text type="secondary" className="text-lg">
          今天是个适合量化交易的好日子 📈
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-8">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Divider />

      {/* 测试按钮区域 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          测试导航
        </Title>
        <Space wrap>
          <Button type="primary" onClick={() => handleQuickAction('/dashboard/overview')}>
            概览页面
          </Button>
          <Button type="default" onClick={() => handleQuickAction('/dashboard/market')}>
            市场数据
          </Button>
          <Button type="default" onClick={() => handleQuickAction('/dashboard/portfolio')}>
            投资组合
          </Button>
          <Button type="default" onClick={() => handleQuickAction('/dashboard/strategy')}>
            策略中心
          </Button>
          <Button type="default" onClick={() => handleQuickAction('/dashboard/settings')}>
            系统设置
          </Button>
        </Space>
      </div>

      <Divider />

      {/* 快速操作 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          快速操作
        </Title>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
              >
                <div className="text-center">
                  <div className="mb-4">
                    {action.icon}
                  </div>
                  <Title level={4} className="!mb-2">
                    {action.title}
                  </Title>
                  <Text type="secondary" className="text-sm">
                    {action.description}
                  </Text>
                  <div className="mt-4">
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => handleQuickAction(action.path)}
                    >
                      开始使用 <RightOutlined />
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 最近活动 */}
      <Card title="最近活动" className="mb-6">
        <div className="text-center py-8">
          <Text type="secondary">
            暂无最近活动，开始您的量化交易之旅吧！
          </Text>
        </div>
      </Card>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ClientAuthWrapper>
      <DashboardPageContent />
    </ClientAuthWrapper>
  );
}
