#!/usr/bin/env python3
"""
测试导入模块
"""

try:
    print("Testing basic imports...")
    import sys
    print(f"Python version: {sys.version}")
    
    print("Testing pydantic...")
    from pydantic import BaseModel
    print("✓ pydantic imported successfully")
    
    print("Testing pydantic_settings...")
    from pydantic_settings import BaseSettings
    print("✓ pydantic_settings imported successfully")
    
    print("Testing fastapi...")
    from fastapi import FastAPI
    print("✓ fastapi imported successfully")
    
    print("Testing app.core.config...")
    from app.core.config import Settings
    print("✓ Settings class imported successfully")
    
    print("Creating settings instance...")
    settings = Settings()
    print("✓ Settings instance created successfully")
    
    print("Testing app.main...")
    from app.main import app
    print("✓ FastAPI app imported successfully")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"\n❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
