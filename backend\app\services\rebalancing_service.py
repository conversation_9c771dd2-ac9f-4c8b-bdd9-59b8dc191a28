"""
动态再平衡服务

提供投资组合动态再平衡功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.core.database import get_db
from app.models.portfolio import Portfolio, PortfolioHolding
from app.services.jqdata_service import JQDataService


class RebalancingService:
    """动态再平衡服务类"""
    
    def __init__(self, jqdata_service: JQDataService):
        self.jqdata_service = jqdata_service
        
    async def analyze_portfolio_drift(
        self,
        portfolio_id: int,
        target_weights: Dict[str, float],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """分析投资组合偏离度"""
        try:
            # 获取投资组合当前持仓
            holdings = await self._get_portfolio_holdings(portfolio_id, db)
            
            if not holdings:
                return {
                    'success': False,
                    'error': 'No holdings found'
                }
            
            # 计算当前权重
            current_weights = await self._calculate_current_weights(holdings)
            
            # 计算偏离度
            drift_analysis = self._calculate_drift(current_weights, target_weights)
            
            # 计算再平衡建议
            rebalance_suggestions = await self._generate_rebalance_suggestions(
                holdings, current_weights, target_weights
            )
            
            return {
                'success': True,
                'portfolio_id': portfolio_id,
                'current_weights': current_weights,
                'target_weights': target_weights,
                'drift_analysis': drift_analysis,
                'rebalance_suggestions': rebalance_suggestions,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Portfolio drift analysis failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def create_rebalancing_plan(
        self,
        portfolio_id: int,
        target_weights: Dict[str, float],
        rebalance_threshold: float = 0.05,
        min_trade_amount: float = 1000.0,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """创建再平衡计划"""
        try:
            # 分析当前偏离度
            drift_analysis = await self.analyze_portfolio_drift(
                portfolio_id, target_weights, db
            )
            
            if not drift_analysis['success']:
                return drift_analysis
            
            # 检查是否需要再平衡
            max_drift = max(
                abs(drift) for drift in drift_analysis['drift_analysis']['absolute_drifts'].values()
            )
            
            if max_drift < rebalance_threshold:
                return {
                    'success': True,
                    'rebalance_needed': False,
                    'max_drift': max_drift,
                    'threshold': rebalance_threshold,
                    'message': '投资组合偏离度在可接受范围内，无需再平衡'
                }
            
            # 生成交易计划
            trade_plan = await self._generate_trade_plan(
                drift_analysis['rebalance_suggestions'],
                min_trade_amount
            )
            
            # 计算预期成本
            estimated_costs = await self._estimate_rebalancing_costs(trade_plan)
            
            # 风险评估
            risk_assessment = await self._assess_rebalancing_risks(
                portfolio_id, trade_plan, db
            )
            
            return {
                'success': True,
                'rebalance_needed': True,
                'max_drift': max_drift,
                'threshold': rebalance_threshold,
                'trade_plan': trade_plan,
                'estimated_costs': estimated_costs,
                'risk_assessment': risk_assessment,
                'execution_priority': self._prioritize_trades(trade_plan),
                'plan_created_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Rebalancing plan creation failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def execute_rebalancing(
        self,
        portfolio_id: int,
        trade_plan: List[Dict[str, Any]],
        execution_mode: str = 'simulation',
        db: AsyncSession
    ) -> Dict[str, Any]:
        """执行再平衡计划"""
        try:
            execution_results = []
            total_cost = 0.0
            
            for trade in trade_plan:
                if execution_mode == 'simulation':
                    # 模拟执行
                    result = await self._simulate_trade_execution(trade)
                else:
                    # 实际执行（需要接入交易接口）
                    result = await self._execute_actual_trade(trade, db)
                
                execution_results.append(result)
                total_cost += result.get('cost', 0)
            
            # 更新投资组合记录
            if execution_mode != 'simulation':
                await self._update_portfolio_after_rebalancing(
                    portfolio_id, execution_results, db
                )
            
            # 计算执行后的权重
            post_rebalance_weights = await self._calculate_post_rebalance_weights(
                portfolio_id, execution_results, db
            )
            
            return {
                'success': True,
                'execution_mode': execution_mode,
                'execution_results': execution_results,
                'total_cost': total_cost,
                'trades_executed': len([r for r in execution_results if r['success']]),
                'trades_failed': len([r for r in execution_results if not r['success']]),
                'post_rebalance_weights': post_rebalance_weights,
                'executed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Rebalancing execution failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def schedule_automatic_rebalancing(
        self,
        portfolio_id: int,
        target_weights: Dict[str, float],
        schedule_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """设置自动再平衡计划"""
        try:
            # 验证配置
            required_fields = ['frequency', 'threshold', 'min_trade_amount']
            for field in required_fields:
                if field not in schedule_config:
                    return {
                        'success': False,
                        'error': f'Missing required field: {field}'
                    }
            
            # 创建自动再平衡配置
            auto_rebalance_config = {
                'portfolio_id': portfolio_id,
                'target_weights': target_weights,
                'frequency': schedule_config['frequency'],  # daily, weekly, monthly
                'threshold': schedule_config['threshold'],
                'min_trade_amount': schedule_config['min_trade_amount'],
                'enabled': True,
                'created_at': datetime.now().isoformat(),
                'next_check_date': self._calculate_next_check_date(
                    schedule_config['frequency']
                ).isoformat()
            }
            
            # 保存配置到数据库（这里简化处理）
            logger.info(f"Auto rebalancing scheduled for portfolio {portfolio_id}")
            
            return {
                'success': True,
                'config': auto_rebalance_config,
                'message': '自动再平衡计划已设置'
            }
            
        except Exception as e:
            logger.error(f"Auto rebalancing scheduling failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_portfolio_holdings(
        self, 
        portfolio_id: int, 
        db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """获取投资组合持仓"""
        # 这里应该从数据库获取实际持仓数据
        # 为了演示，返回模拟数据
        return [
            {
                'symbol': '000001.XSHE',
                'quantity': 1000,
                'current_price': 12.50,
                'market_value': 12500.0
            },
            {
                'symbol': '600036.XSHG',
                'quantity': 500,
                'current_price': 45.80,
                'market_value': 22900.0
            },
            {
                'symbol': '600519.XSHG',
                'quantity': 100,
                'current_price': 1680.0,
                'market_value': 168000.0
            }
        ]
    
    async def _calculate_current_weights(
        self, 
        holdings: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """计算当前权重"""
        total_value = sum(holding['market_value'] for holding in holdings)
        
        if total_value == 0:
            return {}
        
        return {
            holding['symbol']: holding['market_value'] / total_value
            for holding in holdings
        }
    
    def _calculate_drift(
        self, 
        current_weights: Dict[str, float], 
        target_weights: Dict[str, float]
    ) -> Dict[str, Any]:
        """计算偏离度"""
        absolute_drifts = {}
        relative_drifts = {}
        
        all_symbols = set(current_weights.keys()) | set(target_weights.keys())
        
        for symbol in all_symbols:
            current = current_weights.get(symbol, 0.0)
            target = target_weights.get(symbol, 0.0)
            
            absolute_drift = current - target
            relative_drift = (absolute_drift / target * 100) if target > 0 else 0
            
            absolute_drifts[symbol] = absolute_drift
            relative_drifts[symbol] = relative_drift
        
        return {
            'absolute_drifts': absolute_drifts,
            'relative_drifts': relative_drifts,
            'max_absolute_drift': max(abs(d) for d in absolute_drifts.values()),
            'max_relative_drift': max(abs(d) for d in relative_drifts.values())
        }
    
    async def _generate_rebalance_suggestions(
        self,
        holdings: List[Dict[str, Any]],
        current_weights: Dict[str, float],
        target_weights: Dict[str, float]
    ) -> List[Dict[str, Any]]:
        """生成再平衡建议"""
        suggestions = []
        total_value = sum(holding['market_value'] for holding in holdings)
        
        for symbol, target_weight in target_weights.items():
            current_weight = current_weights.get(symbol, 0.0)
            weight_diff = target_weight - current_weight
            
            if abs(weight_diff) > 0.01:  # 1%阈值
                target_value = total_value * target_weight
                current_value = total_value * current_weight
                value_diff = target_value - current_value
                
                # 获取当前价格
                current_price = next(
                    (h['current_price'] for h in holdings if h['symbol'] == symbol),
                    await self._get_current_price(symbol)
                )
                
                if current_price > 0:
                    quantity_diff = int(value_diff / current_price)
                    
                    suggestions.append({
                        'symbol': symbol,
                        'action': 'buy' if quantity_diff > 0 else 'sell',
                        'quantity': abs(quantity_diff),
                        'current_weight': current_weight,
                        'target_weight': target_weight,
                        'weight_diff': weight_diff,
                        'value_diff': value_diff,
                        'current_price': current_price,
                        'estimated_cost': abs(value_diff)
                    })
        
        return suggestions
    
    async def _generate_trade_plan(
        self,
        suggestions: List[Dict[str, Any]],
        min_trade_amount: float
    ) -> List[Dict[str, Any]]:
        """生成交易计划"""
        trade_plan = []
        
        for suggestion in suggestions:
            if suggestion['estimated_cost'] >= min_trade_amount:
                trade_plan.append({
                    'symbol': suggestion['symbol'],
                    'action': suggestion['action'],
                    'quantity': suggestion['quantity'],
                    'estimated_price': suggestion['current_price'],
                    'estimated_amount': suggestion['estimated_cost'],
                    'priority': self._calculate_trade_priority(suggestion),
                    'risk_level': self._assess_trade_risk(suggestion)
                })
        
        # 按优先级排序
        trade_plan.sort(key=lambda x: x['priority'], reverse=True)
        
        return trade_plan
    
    async def _estimate_rebalancing_costs(
        self, 
        trade_plan: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """估算再平衡成本"""
        total_amount = sum(trade['estimated_amount'] for trade in trade_plan)
        
        # 简化的成本计算
        commission_rate = 0.0003  # 0.03%
        commission = total_amount * commission_rate
        
        # 市场冲击成本（简化）
        market_impact = total_amount * 0.0001  # 0.01%
        
        # 买卖价差成本
        bid_ask_spread = total_amount * 0.0002  # 0.02%
        
        total_cost = commission + market_impact + bid_ask_spread
        
        return {
            'commission': commission,
            'market_impact': market_impact,
            'bid_ask_spread': bid_ask_spread,
            'total_cost': total_cost,
            'cost_ratio': total_cost / total_amount if total_amount > 0 else 0
        }
    
    async def _assess_rebalancing_risks(
        self,
        portfolio_id: int,
        trade_plan: List[Dict[str, Any]],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """评估再平衡风险"""
        risks = {
            'market_risk': 'low',
            'liquidity_risk': 'low',
            'execution_risk': 'low',
            'timing_risk': 'medium',
            'overall_risk': 'low'
        }
        
        # 简化的风险评估
        total_amount = sum(trade['estimated_amount'] for trade in trade_plan)
        
        if total_amount > 100000:  # 大额交易
            risks['market_risk'] = 'medium'
            risks['execution_risk'] = 'medium'
        
        if len(trade_plan) > 10:  # 交易数量多
            risks['execution_risk'] = 'high'
        
        # 计算整体风险
        risk_scores = {'low': 1, 'medium': 2, 'high': 3}
        avg_risk_score = sum(risk_scores[risk] for risk in risks.values() if risk != 'overall_risk') / 4
        
        if avg_risk_score <= 1.5:
            risks['overall_risk'] = 'low'
        elif avg_risk_score <= 2.5:
            risks['overall_risk'] = 'medium'
        else:
            risks['overall_risk'] = 'high'
        
        return risks
    
    def _prioritize_trades(self, trade_plan: List[Dict[str, Any]]) -> List[str]:
        """交易优先级排序"""
        return [
            f"{trade['action'].upper()} {trade['symbol']} - 优先级: {trade['priority']}"
            for trade in sorted(trade_plan, key=lambda x: x['priority'], reverse=True)
        ]
    
    async def _simulate_trade_execution(self, trade: Dict[str, Any]) -> Dict[str, Any]:
        """模拟交易执行"""
        # 模拟执行结果
        success_rate = 0.95  # 95%成功率
        
        import random
        success = random.random() < success_rate
        
        if success:
            # 模拟价格滑点
            slippage = random.uniform(-0.001, 0.001)  # ±0.1%
            actual_price = trade['estimated_price'] * (1 + slippage)
            actual_amount = trade['quantity'] * actual_price
            
            return {
                'success': True,
                'symbol': trade['symbol'],
                'action': trade['action'],
                'quantity': trade['quantity'],
                'executed_price': actual_price,
                'executed_amount': actual_amount,
                'slippage': slippage,
                'cost': actual_amount * 0.0003  # 手续费
            }
        else:
            return {
                'success': False,
                'symbol': trade['symbol'],
                'action': trade['action'],
                'error': '模拟执行失败',
                'cost': 0
            }
    
    async def _execute_actual_trade(
        self, 
        trade: Dict[str, Any], 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """实际交易执行（需要接入交易接口）"""
        # 这里应该接入实际的交易接口
        # 目前返回模拟结果
        return await self._simulate_trade_execution(trade)
    
    async def _get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        try:
            price_data = await self.jqdata_service.get_current_price(symbol)
            return price_data.get('current_price', 0.0)
        except:
            return 0.0
    
    def _calculate_trade_priority(self, suggestion: Dict[str, Any]) -> float:
        """计算交易优先级"""
        # 基于权重偏离度计算优先级
        weight_diff = abs(suggestion['weight_diff'])
        return weight_diff * 100  # 转换为0-100的分数
    
    def _assess_trade_risk(self, suggestion: Dict[str, Any]) -> str:
        """评估单笔交易风险"""
        amount = suggestion['estimated_cost']
        
        if amount < 10000:
            return 'low'
        elif amount < 50000:
            return 'medium'
        else:
            return 'high'
    
    def _calculate_next_check_date(self, frequency: str) -> datetime:
        """计算下次检查日期"""
        now = datetime.now()
        
        if frequency == 'daily':
            return now + timedelta(days=1)
        elif frequency == 'weekly':
            return now + timedelta(weeks=1)
        elif frequency == 'monthly':
            return now + timedelta(days=30)
        else:
            return now + timedelta(days=7)  # 默认一周
    
    async def _calculate_post_rebalance_weights(
        self,
        portfolio_id: int,
        execution_results: List[Dict[str, Any]],
        db: AsyncSession
    ) -> Dict[str, float]:
        """计算再平衡后的权重"""
        # 简化实现，实际应该基于执行结果重新计算
        return {
            '000001.XSHE': 0.20,
            '600036.XSHG': 0.30,
            '600519.XSHG': 0.50
        }
    
    async def _update_portfolio_after_rebalancing(
        self,
        portfolio_id: int,
        execution_results: List[Dict[str, Any]],
        db: AsyncSession
    ):
        """再平衡后更新投资组合记录"""
        # 这里应该更新数据库中的持仓记录
        logger.info(f"Portfolio {portfolio_id} updated after rebalancing")
        pass
