"""
模型可解释性相关API端点

提供SHAP、LIME、决策可视化等模型解释功能的API接口
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
import numpy as np
import pandas as pd
import io

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.explainability import ExplainabilityAnalysis, ModelInterpretabilityMetrics
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.explainability_service import explainability_service

router = APIRouter()


# =============================================================================
# Pydantic模型
# =============================================================================

class ExplainabilityAnalysisRequest(BaseModel):
    """可解释性分析请求"""
    name: str = Field(..., description="分析名称")
    description: Optional[str] = Field(None, description="分析描述")
    model_id: int = Field(..., description="模型ID")
    model_type: str = Field(..., description="模型类型", regex="^(ml_model|automl_model|transformer_model)$")
    
    # 分析配置
    analysis_type: str = Field("global", description="分析类型", regex="^(global|local|feature_importance)$")
    methods: List[str] = Field(["shap", "lime"], description="解释方法")
    scope: str = Field("sample", description="分析范围", regex="^(full_dataset|sample|specific_instances)$")
    sample_size: Optional[int] = Field(1000, ge=10, le=10000, description="样本大小")
    
    # 方法配置
    method_configs: Optional[Dict[str, Any]] = Field(None, description="方法配置")
    
    # 数据配置
    use_uploaded_data: bool = Field(False, description="是否使用上传的数据")
    data_source: Optional[str] = Field(None, description="数据源")


class SHAPExplanationRequest(BaseModel):
    """SHAP解释请求"""
    model_id: int = Field(..., description="模型ID")
    model_type: str = Field(..., description="模型类型")
    explainer_type: str = Field("auto", description="解释器类型")
    max_samples: int = Field(1000, ge=10, le=5000, description="最大样本数")
    explainer_params: Optional[Dict[str, Any]] = Field(None, description="解释器参数")


class LIMEExplanationRequest(BaseModel):
    """LIME解释请求"""
    model_id: int = Field(..., description="模型ID")
    model_type: str = Field(..., description="模型类型")
    explainer_type: str = Field("tabular", description="解释器类型")
    num_features: int = Field(10, ge=1, le=50, description="解释特征数量")
    num_samples: int = Field(5000, ge=100, le=10000, description="采样数量")
    max_instances: int = Field(100, ge=1, le=1000, description="最大实例数")


class DecisionVisualizationRequest(BaseModel):
    """决策可视化请求"""
    model_id: int = Field(..., description="模型ID")
    model_type: str = Field(..., description="模型类型")
    max_depth: int = Field(5, ge=1, le=10, description="最大深度")
    max_instances: int = Field(100, ge=10, le=1000, description="最大实例数")


# =============================================================================
# 可解释性分析管理
# =============================================================================

@router.post("/analyses", response_model=BaseResponse[dict])
async def create_explainability_analysis(
    request: ExplainabilityAnalysisRequest,
    data_file: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建可解释性分析"""
    try:
        # 处理上传的数据
        X, y = None, None
        if request.use_uploaded_data and data_file:
            try:
                content = await data_file.read()
                if data_file.filename.endswith('.csv'):
                    df = pd.read_csv(io.StringIO(content.decode('utf-8')))
                elif data_file.filename.endswith('.xlsx'):
                    df = pd.read_excel(io.BytesIO(content))
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不支持的文件格式，请使用CSV或Excel文件"
                    )
                
                # 假设最后一列是目标变量
                X = df.iloc[:, :-1].values
                y = df.iloc[:, -1].values
                
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"数据文件处理失败: {str(e)}"
                )
        else:
            # 使用模型训练数据（这里需要根据实际情况获取）
            # 暂时使用模拟数据
            np.random.seed(42)
            X = np.random.randn(request.sample_size or 1000, 10)
            y = np.random.randint(0, 2, request.sample_size or 1000)
        
        # 创建分析配置
        analysis_config = {
            'name': request.name,
            'description': request.description,
            'analysis_type': request.analysis_type,
            'methods': request.methods,
            'scope': request.scope,
            'sample_size': request.sample_size,
            'method_configs': request.method_configs or {}
        }
        
        # 创建可解释性分析
        result = await explainability_service.create_explainability_analysis(
            current_user.id, request.model_id, request.model_type, X, y, analysis_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="可解释性分析创建成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建可解释性分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建可解释性分析失败"
        )


@router.get("/analyses", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_explainability_analyses(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, regex="^(created|running|completed|failed)$"),
    model_type: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取可解释性分析列表"""
    try:
        # 构建查询条件
        conditions = [ExplainabilityAnalysis.user_id == current_user.id]
        if status:
            conditions.append(ExplainabilityAnalysis.status == status)
        if model_type:
            conditions.append(ExplainabilityAnalysis.model_type == model_type)
        
        # 查询总数
        count_query = select(func.count(ExplainabilityAnalysis.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(ExplainabilityAnalysis)
            .where(and_(*conditions))
            .order_by(desc(ExplainabilityAnalysis.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        analyses = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        analysis_list = []
        for analysis in analyses:
            analysis_list.append({
                "id": analysis.id,
                "name": analysis.analysis_name,
                "description": analysis.description,
                "model_id": analysis.model_id,
                "model_type": analysis.model_type,
                "analysis_type": analysis.analysis_type,
                "status": analysis.status,
                "progress": analysis.progress,
                "current_method": analysis.current_method,
                "explanation_methods": analysis.explanation_methods,
                "has_global_explanations": analysis.has_global_explanations,
                "has_local_explanations": analysis.has_local_explanations,
                "has_visualizations": analysis.has_visualizations,
                "created_at": analysis.created_at.isoformat(),
                "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None
            })
        
        return BaseResponse(
            code=200,
            message="获取可解释性分析列表成功",
            data=PaginatedResponse(
                items=analysis_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取可解释性分析列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取可解释性分析列表失败"
        )


@router.get("/analyses/{analysis_id}", response_model=BaseResponse[dict])
async def get_explainability_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取可解释性分析详情"""
    try:
        result = await explainability_service.get_analysis_results(analysis_id, db)
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="获取可解释性分析详情成功",
                data=result['results']
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取可解释性分析详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取可解释性分析详情失败"
        )


@router.delete("/analyses/{analysis_id}", response_model=BaseResponse[dict])
async def delete_explainability_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """删除可解释性分析"""
    try:
        # 检查分析是否存在且属于当前用户
        result = await db.execute(
            select(ExplainabilityAnalysis).where(
                and_(
                    ExplainabilityAnalysis.id == analysis_id,
                    ExplainabilityAnalysis.user_id == current_user.id
                )
            )
        )
        analysis = result.scalar_one_or_none()
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="可解释性分析不存在"
            )
        
        # 删除分析（级联删除相关数据）
        await db.delete(analysis)
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="可解释性分析删除成功",
            data={"analysis_id": analysis_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除可解释性分析失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除可解释性分析失败"
        )


# =============================================================================
# 可解释性指标
# =============================================================================

@router.get("/metrics", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_interpretability_metrics(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    model_type: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取可解释性指标列表"""
    try:
        # 构建查询条件
        conditions = [ModelInterpretabilityMetrics.user_id == current_user.id]
        if model_type:
            conditions.append(ModelInterpretabilityMetrics.model_type == model_type)
        
        # 查询总数
        count_query = select(func.count(ModelInterpretabilityMetrics.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(ModelInterpretabilityMetrics)
            .where(and_(*conditions))
            .order_by(desc(ModelInterpretabilityMetrics.calculated_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        metrics = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        metrics_list = []
        for metric in metrics:
            metrics_list.append({
                "id": metric.id,
                "model_id": metric.model_id,
                "model_type": metric.model_type,
                "overall_interpretability_score": float(metric.overall_interpretability_score) if metric.overall_interpretability_score else None,
                "transparency_score": float(metric.transparency_score) if metric.transparency_score else None,
                "comprehensibility_score": float(metric.comprehensibility_score) if metric.comprehensibility_score else None,
                "model_complexity": float(metric.model_complexity) if metric.model_complexity else None,
                "explanation_stability": float(metric.explanation_stability) if metric.explanation_stability else None,
                "method_consistency": float(metric.method_consistency) if metric.method_consistency else None,
                "calculated_at": metric.calculated_at.isoformat()
            })
        
        return BaseResponse(
            code=200,
            message="获取可解释性指标列表成功",
            data=PaginatedResponse(
                items=metrics_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取可解释性指标列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取可解释性指标列表失败"
        )


@router.get("/metrics/{model_id}", response_model=BaseResponse[dict])
async def get_model_interpretability_metrics(
    model_id: int,
    model_type: str = Query(..., regex="^(ml_model|automl_model|transformer_model)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取模型可解释性指标"""
    try:
        result = await db.execute(
            select(ModelInterpretabilityMetrics).where(
                and_(
                    ModelInterpretabilityMetrics.model_id == model_id,
                    ModelInterpretabilityMetrics.model_type == model_type,
                    ModelInterpretabilityMetrics.user_id == current_user.id
                )
            ).order_by(desc(ModelInterpretabilityMetrics.calculated_at))
        )
        metric = result.scalar_one_or_none()
        
        if not metric:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型可解释性指标不存在"
            )
        
        metric_data = {
            "model_id": metric.model_id,
            "model_type": metric.model_type,
            "overall_interpretability_score": float(metric.overall_interpretability_score) if metric.overall_interpretability_score else None,
            "transparency_score": float(metric.transparency_score) if metric.transparency_score else None,
            "comprehensibility_score": float(metric.comprehensibility_score) if metric.comprehensibility_score else None,
            "model_complexity": float(metric.model_complexity) if metric.model_complexity else None,
            "feature_complexity": float(metric.feature_complexity) if metric.feature_complexity else None,
            "explanation_stability": float(metric.explanation_stability) if metric.explanation_stability else None,
            "method_consistency": float(metric.method_consistency) if metric.method_consistency else None,
            "local_fidelity": float(metric.local_fidelity) if metric.local_fidelity else None,
            "global_fidelity": float(metric.global_fidelity) if metric.global_fidelity else None,
            "approximation_quality": float(metric.approximation_quality) if metric.approximation_quality else None,
            "calculated_at": metric.calculated_at.isoformat(),
            "updated_at": metric.updated_at.isoformat()
        }
        
        return BaseResponse(
            code=200,
            message="获取模型可解释性指标成功",
            data=metric_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模型可解释性指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型可解释性指标失败"
        )


# =============================================================================
# 快速解释接口
# =============================================================================

@router.post("/quick-explain/shap", response_model=BaseResponse[dict])
async def quick_shap_explanation(
    request: SHAPExplanationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """快速SHAP解释"""
    try:
        # 创建临时分析
        analysis_config = {
            'name': f"Quick SHAP - Model {request.model_id}",
            'description': "快速SHAP解释",
            'analysis_type': 'global',
            'methods': ['shap'],
            'scope': 'sample',
            'sample_size': request.max_samples,
            'method_configs': {
                'shap': {
                    'explainer_type': request.explainer_type,
                    'explainer_params': request.explainer_params or {},
                    'max_samples': request.max_samples
                }
            }
        }
        
        # 使用模拟数据（实际应用中应该从模型获取训练数据）
        np.random.seed(42)
        X = np.random.randn(request.max_samples, 10)
        y = np.random.randint(0, 2, request.max_samples)
        
        result = await explainability_service.create_explainability_analysis(
            current_user.id, request.model_id, request.model_type, X, y, analysis_config, db
        )
        
        return BaseResponse(
            code=200,
            message="快速SHAP解释已启动",
            data=result
        )
        
    except Exception as e:
        logger.error(f"快速SHAP解释失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="快速SHAP解释失败"
        )


@router.post("/quick-explain/lime", response_model=BaseResponse[dict])
async def quick_lime_explanation(
    request: LIMEExplanationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """快速LIME解释"""
    try:
        # 创建临时分析
        analysis_config = {
            'name': f"Quick LIME - Model {request.model_id}",
            'description': "快速LIME解释",
            'analysis_type': 'local',
            'methods': ['lime'],
            'scope': 'sample',
            'sample_size': request.max_instances,
            'method_configs': {
                'lime': {
                    'explainer_type': request.explainer_type,
                    'num_features': request.num_features,
                    'num_samples': request.num_samples,
                    'max_instances': request.max_instances
                }
            }
        }
        
        # 使用模拟数据
        np.random.seed(42)
        X = np.random.randn(request.max_instances * 2, 10)  # 需要训练数据和解释数据
        y = np.random.randint(0, 2, request.max_instances * 2)
        
        result = await explainability_service.create_explainability_analysis(
            current_user.id, request.model_id, request.model_type, X, y, analysis_config, db
        )
        
        return BaseResponse(
            code=200,
            message="快速LIME解释已启动",
            data=result
        )
        
    except Exception as e:
        logger.error(f"快速LIME解释失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="快速LIME解释失败"
        )
