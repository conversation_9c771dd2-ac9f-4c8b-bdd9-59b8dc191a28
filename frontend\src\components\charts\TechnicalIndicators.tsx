'use client';

/**
 * 技术指标组件
 * 
 * 提供各种技术指标的图表展示，包括MACD、RSI、BOLL等
 */

import React, { useEffect, useRef, useState } from 'react';
import { Card, Select, Space, Switch, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';

import { 
  PriceData, 
  calculateMACD, 
  calculateRSI, 
  calculateBOLL, 
  calculateKDJ,
  calculateSMA,
  calculateEMA
} from '@/utils/indicators';

const { Option } = Select;

interface TechnicalIndicatorsProps {
  data: PriceData[];
  height?: number;
  indicators?: string[];
  onIndicatorChange?: (indicators: string[]) => void;
}

interface IndicatorConfig {
  name: string;
  label: string;
  description: string;
  defaultParams: any;
  color: string[];
}

const INDICATOR_CONFIGS: Record<string, IndicatorConfig> = {
  macd: {
    name: 'MACD',
    label: 'MACD指标',
    description: '指数平滑移动平均线，用于判断趋势变化',
    defaultParams: { fast: 12, slow: 26, signal: 9 },
    color: ['#FF6B6B', '#4ECDC4', '#45B7D1']
  },
  rsi: {
    name: 'RSI',
    label: 'RSI指标',
    description: '相对强弱指标，用于判断超买超卖',
    defaultParams: { period: 14 },
    color: ['#9B59B6']
  },
  boll: {
    name: 'BOLL',
    label: '布林带',
    description: '布林带指标，用于判断价格波动区间',
    defaultParams: { period: 20, multiplier: 2 },
    color: ['#E74C3C', '#3498DB', '#2ECC71']
  },
  kdj: {
    name: 'KDJ',
    label: 'KDJ指标',
    description: '随机指标，用于判断超买超卖和趋势转折',
    defaultParams: { period: 9, m1: 3, m2: 3 },
    color: ['#F39C12', '#8E44AD', '#16A085']
  },
  ma: {
    name: 'MA',
    label: '移动平均线',
    description: '移动平均线，用于判断趋势方向',
    defaultParams: { periods: [5, 10, 20, 60] },
    color: ['#FFD93D', '#6BCF7F', '#4D96FF', '#9B59B6']
  }
};

export const TechnicalIndicators: React.FC<TechnicalIndicatorsProps> = ({
  data,
  height = 300,
  indicators = ['macd'],
  onIndicatorChange
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [selectedIndicators, setSelectedIndicators] = useState<string[]>(indicators);
  const [showGrid, setShowGrid] = useState(true);

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current || !data.length) return;

    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);
    updateChart();

    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, selectedIndicators, showGrid]);

  // 更新图表
  const updateChart = () => {
    if (!chartInstance.current || !data.length) return;

    const dates = data.map(item => item.date);
    const series: any[] = [];
    const yAxes: any[] = [];
    const grids: any[] = [];
    const xAxes: any[] = [];

    let gridIndex = 0;
    const gridHeight = 100 / selectedIndicators.length;

    selectedIndicators.forEach((indicator, index) => {
      const config = INDICATOR_CONFIGS[indicator];
      if (!config) return;

      // 创建网格
      grids.push({
        left: '10%',
        right: '8%',
        top: `${index * gridHeight}%`,
        height: `${gridHeight - 5}%`,
        show: showGrid
      });

      // 创建X轴
      xAxes.push({
        type: 'category',
        data: dates,
        gridIndex: index,
        axisLine: { show: index === selectedIndicators.length - 1 },
        axisTick: { show: index === selectedIndicators.length - 1 },
        axisLabel: { show: index === selectedIndicators.length - 1 },
        splitLine: { show: false }
      });

      // 创建Y轴
      yAxes.push({
        type: 'value',
        gridIndex: index,
        scale: true,
        splitLine: { show: showGrid },
        axisLabel: { fontSize: 10 }
      });

      // 根据指标类型添加系列
      switch (indicator) {
        case 'macd':
          const macdData = calculateMACD(data);
          if (macdData.length > 0) {
            series.push(
              {
                name: 'DIF',
                type: 'line',
                data: macdData.map(d => d.dif),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[0], width: 1 },
                symbol: 'none'
              },
              {
                name: 'DEA',
                type: 'line',
                data: macdData.map(d => d.dea),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[1], width: 1 },
                symbol: 'none'
              },
              {
                name: 'MACD',
                type: 'bar',
                data: macdData.map(d => d.macd),
                xAxisIndex: index,
                yAxisIndex: index,
                itemStyle: {
                  color: (params: any) => params.data >= 0 ? '#FF6B6B' : '#4ECDC4'
                }
              }
            );
          }
          break;

        case 'rsi':
          const rsiData = calculateRSI(data);
          if (rsiData.length > 0) {
            series.push({
              name: 'RSI',
              type: 'line',
              data: rsiData.map(d => d.value),
              xAxisIndex: index,
              yAxisIndex: index,
              lineStyle: { color: config.color[0], width: 2 },
              symbol: 'none'
            });

            // 添加超买超卖线
            series.push(
              {
                name: '超买线',
                type: 'line',
                data: new Array(rsiData.length).fill(70),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: '#E74C3C', type: 'dashed', width: 1 },
                symbol: 'none',
                silent: true
              },
              {
                name: '超卖线',
                type: 'line',
                data: new Array(rsiData.length).fill(30),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: '#2ECC71', type: 'dashed', width: 1 },
                symbol: 'none',
                silent: true
              }
            );
          }
          break;

        case 'kdj':
          const kdjData = calculateKDJ(data);
          if (kdjData.length > 0) {
            series.push(
              {
                name: 'K',
                type: 'line',
                data: kdjData.map(d => d.k),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[0], width: 1 },
                symbol: 'none'
              },
              {
                name: 'D',
                type: 'line',
                data: kdjData.map(d => d.d),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[1], width: 1 },
                symbol: 'none'
              },
              {
                name: 'J',
                type: 'line',
                data: kdjData.map(d => d.j),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[2], width: 1 },
                symbol: 'none'
              }
            );
          }
          break;

        case 'boll':
          const bollData = calculateBOLL(data);
          if (bollData.length > 0) {
            series.push(
              {
                name: '上轨',
                type: 'line',
                data: bollData.map(d => d.upper),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[0], width: 1 },
                symbol: 'none'
              },
              {
                name: '中轨',
                type: 'line',
                data: bollData.map(d => d.middle),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[1], width: 1 },
                symbol: 'none'
              },
              {
                name: '下轨',
                type: 'line',
                data: bollData.map(d => d.lower),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[2], width: 1 },
                symbol: 'none'
              }
            );
          }
          break;

        case 'ma':
          const periods = [5, 10, 20, 60];
          periods.forEach((period, i) => {
            const maData = calculateSMA(data, period);
            if (maData.length > 0) {
              series.push({
                name: `MA${period}`,
                type: 'line',
                data: maData.map(d => d.value),
                xAxisIndex: index,
                yAxisIndex: index,
                lineStyle: { color: config.color[i], width: 1 },
                symbol: 'none'
              });
            }
          });
          break;
      }

      gridIndex++;
    });

    const option: echarts.EChartsOption = {
      animation: false,
      grid: grids,
      xAxis: xAxes,
      yAxis: yAxes,
      series,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          link: { xAxisIndex: 'all' }
        },
        formatter: function (params: any) {
          if (!params || params.length === 0) return '';
          
          const date = params[0].name;
          let content = `<div><strong>${date}</strong></div>`;
          
          params.forEach((param: any) => {
            if (param.seriesName && param.value !== undefined) {
              content += `<div>${param.marker}${param.seriesName}: ${Number(param.value).toFixed(2)}</div>`;
            }
          });
          
          return content;
        }
      },
      legend: {
        show: true,
        top: 0,
        textStyle: { fontSize: 10 }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: xAxes.map((_, i) => i),
          start: 70,
          end: 100
        }
      ]
    };

    chartInstance.current.setOption(option, true);
  };

  const handleIndicatorChange = (values: string[]) => {
    setSelectedIndicators(values);
    onIndicatorChange?.(values);
  };

  return (
    <Card
      title={
        <Space>
          <span>技术指标</span>
          <Select
            mode="multiple"
            value={selectedIndicators}
            onChange={handleIndicatorChange}
            style={{ minWidth: 200 }}
            placeholder="选择技术指标"
          >
            {Object.entries(INDICATOR_CONFIGS).map(([key, config]) => (
              <Option key={key} value={key}>
                <Space>
                  {config.label}
                  <Tooltip title={config.description}>
                    <InfoCircleOutlined style={{ color: '#999' }} />
                  </Tooltip>
                </Space>
              </Option>
            ))}
          </Select>
          <Switch
            checked={showGrid}
            onChange={setShowGrid}
            size="small"
          />
          <span style={{ fontSize: '12px', color: '#999' }}>网格</span>
        </Space>
      }
      size="small"
    >
      <div
        ref={chartRef}
        style={{ width: '100%', height: `${height}px` }}
      />
    </Card>
  );
};
