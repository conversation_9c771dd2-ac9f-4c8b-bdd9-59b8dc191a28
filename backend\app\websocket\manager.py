"""
WebSocket连接管理器

管理WebSocket连接、消息广播、用户订阅等功能
"""

import json
import asyncio
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.models.user import User


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 活跃连接 {connection_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 用户连接映射 {user_id: Set[connection_id]}
        self.user_connections: Dict[int, Set[str]] = {}
        
        # 订阅管理 {symbol: Set[connection_id]}
        self.symbol_subscriptions: Dict[str, Set[str]] = {}
        
        # 连接信息 {connection_id: dict}
        self.connection_info: Dict[str, dict] = {}
    
    async def connect(
        self, 
        websocket: WebSocket, 
        connection_id: str, 
        user_id: Optional[int] = None
    ):
        """建立WebSocket连接"""
        try:
            await websocket.accept()
            
            # 存储连接
            self.active_connections[connection_id] = websocket
            
            # 存储连接信息
            self.connection_info[connection_id] = {
                'user_id': user_id,
                'connected_at': datetime.utcnow(),
                'last_ping': datetime.utcnow(),
                'subscriptions': set(),
            }
            
            # 用户连接映射
            if user_id:
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = set()
                self.user_connections[user_id].add(connection_id)
            
            logger.info(f"WebSocket连接建立: {connection_id}, 用户: {user_id}")
            
            # 发送连接成功消息
            await self.send_personal_message(connection_id, {
                'type': 'connection',
                'status': 'connected',
                'connection_id': connection_id,
                'timestamp': datetime.utcnow().isoformat(),
            })
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        try:
            # 获取连接信息
            conn_info = self.connection_info.get(connection_id, {})
            user_id = conn_info.get('user_id')
            subscriptions = conn_info.get('subscriptions', set())
            
            # 清理订阅
            for symbol in subscriptions:
                if symbol in self.symbol_subscriptions:
                    self.symbol_subscriptions[symbol].discard(connection_id)
                    if not self.symbol_subscriptions[symbol]:
                        del self.symbol_subscriptions[symbol]
            
            # 清理用户连接映射
            if user_id and user_id in self.user_connections:
                self.user_connections[user_id].discard(connection_id)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            # 清理连接
            self.active_connections.pop(connection_id, None)
            self.connection_info.pop(connection_id, None)
            
            logger.info(f"WebSocket连接断开: {connection_id}, 用户: {user_id}")
            
        except Exception as e:
            logger.error(f"WebSocket断开连接失败: {e}")
    
    async def send_personal_message(self, connection_id: str, message: dict):
        """发送个人消息"""
        websocket = self.active_connections.get(connection_id)
        if websocket:
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送个人消息失败: {connection_id}, {e}")
                self.disconnect(connection_id)
    
    async def send_user_message(self, user_id: int, message: dict):
        """发送用户消息（所有连接）"""
        connection_ids = self.user_connections.get(user_id, set())
        for connection_id in connection_ids.copy():
            await self.send_personal_message(connection_id, message)
    
    async def broadcast_message(self, message: dict):
        """广播消息给所有连接"""
        for connection_id in list(self.active_connections.keys()):
            await self.send_personal_message(connection_id, message)
    
    async def broadcast_to_subscribers(self, symbol: str, message: dict):
        """广播消息给订阅者"""
        connection_ids = self.symbol_subscriptions.get(symbol, set())
        for connection_id in connection_ids.copy():
            await self.send_personal_message(connection_id, message)
    
    def subscribe_symbol(self, connection_id: str, symbol: str):
        """订阅股票"""
        if connection_id not in self.active_connections:
            return False
        
        # 添加到订阅列表
        if symbol not in self.symbol_subscriptions:
            self.symbol_subscriptions[symbol] = set()
        self.symbol_subscriptions[symbol].add(connection_id)
        
        # 更新连接信息
        if connection_id in self.connection_info:
            self.connection_info[connection_id]['subscriptions'].add(symbol)
        
        logger.info(f"订阅股票: {connection_id} -> {symbol}")
        return True
    
    def unsubscribe_symbol(self, connection_id: str, symbol: str):
        """取消订阅股票"""
        if symbol in self.symbol_subscriptions:
            self.symbol_subscriptions[symbol].discard(connection_id)
            if not self.symbol_subscriptions[symbol]:
                del self.symbol_subscriptions[symbol]
        
        # 更新连接信息
        if connection_id in self.connection_info:
            self.connection_info[connection_id]['subscriptions'].discard(symbol)
        
        logger.info(f"取消订阅股票: {connection_id} -> {symbol}")
    
    def get_connection_stats(self) -> dict:
        """获取连接统计"""
        return {
            'total_connections': len(self.active_connections),
            'total_users': len(self.user_connections),
            'total_subscriptions': sum(len(subs) for subs in self.symbol_subscriptions.values()),
            'subscribed_symbols': len(self.symbol_subscriptions),
            'connections_by_user': {
                user_id: len(connections) 
                for user_id, connections in self.user_connections.items()
            },
            'popular_symbols': {
                symbol: len(connections)
                for symbol, connections in self.symbol_subscriptions.items()
                if len(connections) > 0
            }
        }
    
    async def handle_ping(self, connection_id: str):
        """处理心跳"""
        if connection_id in self.connection_info:
            self.connection_info[connection_id]['last_ping'] = datetime.utcnow()
            
        await self.send_personal_message(connection_id, {
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat(),
        })
    
    async def cleanup_stale_connections(self):
        """清理过期连接"""
        current_time = datetime.utcnow()
        stale_connections = []
        
        for connection_id, info in self.connection_info.items():
            last_ping = info.get('last_ping', info.get('connected_at'))
            if (current_time - last_ping).total_seconds() > 300:  # 5分钟超时
                stale_connections.append(connection_id)
        
        for connection_id in stale_connections:
            logger.warning(f"清理过期连接: {connection_id}")
            self.disconnect(connection_id)


# 全局连接管理器实例
manager = ConnectionManager()


class MessageHandler:
    """消息处理器"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
    
    async def handle_message(self, connection_id: str, message: dict):
        """处理WebSocket消息"""
        try:
            message_type = message.get('type')
            
            if message_type == 'ping':
                await self.manager.handle_ping(connection_id)
                
            elif message_type == 'subscribe':
                symbol = message.get('symbol')
                if symbol:
                    success = self.manager.subscribe_symbol(connection_id, symbol)
                    await self.manager.send_personal_message(connection_id, {
                        'type': 'subscribe_response',
                        'symbol': symbol,
                        'success': success,
                        'timestamp': datetime.utcnow().isoformat(),
                    })
                    
            elif message_type == 'unsubscribe':
                symbol = message.get('symbol')
                if symbol:
                    self.manager.unsubscribe_symbol(connection_id, symbol)
                    await self.manager.send_personal_message(connection_id, {
                        'type': 'unsubscribe_response',
                        'symbol': symbol,
                        'success': True,
                        'timestamp': datetime.utcnow().isoformat(),
                    })
                    
            elif message_type == 'get_stats':
                stats = self.manager.get_connection_stats()
                await self.manager.send_personal_message(connection_id, {
                    'type': 'stats_response',
                    'data': stats,
                    'timestamp': datetime.utcnow().isoformat(),
                })
                
            else:
                await self.manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': f'未知消息类型: {message_type}',
                    'timestamp': datetime.utcnow().isoformat(),
                })
                
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {e}")
            await self.manager.send_personal_message(connection_id, {
                'type': 'error',
                'message': '消息处理失败',
                'timestamp': datetime.utcnow().isoformat(),
            })


# 全局消息处理器实例
message_handler = MessageHandler(manager)
