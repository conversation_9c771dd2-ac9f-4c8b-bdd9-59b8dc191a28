{"name": "catboost-widget", "version": "1.2.8", "description": "CatBoost widget for plots in Jupyter", "main": "src/index.js", "files": ["src/**/*", "dist/**/*"], "scripts": {"build": "webpack --mode=production && jupyter labextension build .", "clean": "rimraf dist && rimraf nbextension && rimraf labextension", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "gocd-catboost", "license": "ISC", "dependencies": {"@jupyter-widgets/base": "^2 || ^3 || ^4 || ^5 || ^6", "jquery": "^3.6.0", "lodash": "^4.17.21", "plotly.js-dist-min": "^1.58.4"}, "devDependencies": {"@jupyterlab/builder": "^3.0.6", "css-loader": "^5.2.0", "rimraf": "^3.0.2", "style-loader": "^2.0.0", "webpack": "^5.28.0", "webpack-cli": "^4.5.0"}, "jupyterlab": {"extension": "src/labplugin", "outputDir": "labextension", "sharedPackages": {"@jupyter-widgets/base": {"bundled": false, "singleton": true}}, "_build": {"load": "static\\remoteEntry.29e29269f09bf6933a87.js", "extension": "./extension"}}}