"""
金融知识图谱相关数据模型

定义金融实体、关系、图神经网络等知识图谱功能的数据结构
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class FinancialEntity(Base):
    """金融实体"""
    
    __tablename__ = "financial_entities"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 实体基本信息
    entity_id = Column(String(50), nullable=False, unique=True, index=True)  # 实体唯一标识
    entity_name = Column(String(200), nullable=False, index=True)  # 实体名称
    entity_type = Column(String(50), nullable=False, index=True)  # 实体类型
    entity_category = Column(String(50), nullable=True)  # 实体分类
    
    # 实体属性
    properties = Column(JSON)  # 实体属性字典
    metadata = Column(JSON)  # 元数据信息
    
    # 股票相关信息（如果是股票实体）
    stock_code = Column(String(20), nullable=True, index=True)  # 股票代码
    stock_name = Column(String(100), nullable=True)  # 股票名称
    exchange = Column(String(20), nullable=True)  # 交易所
    industry = Column(String(100), nullable=True)  # 行业
    sector = Column(String(100), nullable=True)  # 板块
    market_cap = Column(Float, nullable=True)  # 市值
    
    # 公司相关信息（如果是公司实体）
    company_name = Column(String(200), nullable=True)  # 公司名称
    company_type = Column(String(50), nullable=True)  # 公司类型
    registration_country = Column(String(50), nullable=True)  # 注册国家
    business_scope = Column(Text, nullable=True)  # 经营范围
    
    # 人员相关信息（如果是人员实体）
    person_name = Column(String(100), nullable=True)  # 人员姓名
    position = Column(String(100), nullable=True)  # 职位
    department = Column(String(100), nullable=True)  # 部门
    
    # 事件相关信息（如果是事件实体）
    event_type = Column(String(50), nullable=True)  # 事件类型
    event_date = Column(DateTime, nullable=True)  # 事件日期
    event_description = Column(Text, nullable=True)  # 事件描述
    
    # 图谱信息
    node_features = Column(JSON)  # 节点特征向量
    embedding_vector = Column(JSON)  # 嵌入向量
    centrality_scores = Column(JSON)  # 中心性分数
    community_id = Column(Integer, nullable=True)  # 社区ID
    
    # 数据来源
    data_source = Column(String(100), nullable=True)  # 数据来源
    source_confidence = Column(Float, nullable=True)  # 来源置信度
    last_updated_from_source = Column(DateTime, nullable=True)  # 最后更新时间
    
    # 状态管理
    is_active = Column(Boolean, default=True)  # 是否激活
    is_verified = Column(Boolean, default=False)  # 是否验证
    verification_status = Column(String(20), default="pending")  # 验证状态
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="financial_entities")
    source_relations = relationship("EntityRelation", foreign_keys="EntityRelation.source_entity_id", back_populates="source_entity", cascade="all, delete-orphan")
    target_relations = relationship("EntityRelation", foreign_keys="EntityRelation.target_entity_id", back_populates="target_entity", cascade="all, delete-orphan")
    graph_analyses = relationship("GraphAnalysis", back_populates="entities", secondary="graph_analysis_entities")


class EntityRelation(Base):
    """实体关系"""
    
    __tablename__ = "entity_relations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 关系基本信息
    relation_id = Column(String(100), nullable=False, unique=True, index=True)  # 关系唯一标识
    source_entity_id = Column(Integer, ForeignKey("financial_entities.id"), nullable=False, index=True)  # 源实体
    target_entity_id = Column(Integer, ForeignKey("financial_entities.id"), nullable=False, index=True)  # 目标实体
    relation_type = Column(String(50), nullable=False, index=True)  # 关系类型
    relation_name = Column(String(100), nullable=False)  # 关系名称
    
    # 关系属性
    properties = Column(JSON)  # 关系属性
    weight = Column(Float, default=1.0)  # 关系权重
    confidence = Column(Float, default=1.0)  # 置信度
    strength = Column(Float, default=1.0)  # 关系强度
    
    # 关系方向性
    is_directed = Column(Boolean, default=True)  # 是否有向
    is_symmetric = Column(Boolean, default=False)  # 是否对称
    
    # 时间相关
    start_date = Column(DateTime, nullable=True)  # 关系开始时间
    end_date = Column(DateTime, nullable=True)  # 关系结束时间
    is_temporal = Column(Boolean, default=False)  # 是否时间相关
    
    # 关系验证
    evidence_sources = Column(JSON)  # 证据来源
    verification_score = Column(Float, nullable=True)  # 验证分数
    is_verified = Column(Boolean, default=False)  # 是否验证
    
    # 图谱信息
    edge_features = Column(JSON)  # 边特征
    betweenness_centrality = Column(Float, nullable=True)  # 介数中心性
    closeness_centrality = Column(Float, nullable=True)  # 接近中心性
    
    # 数据来源
    data_source = Column(String(100), nullable=True)  # 数据来源
    extraction_method = Column(String(50), nullable=True)  # 提取方法
    
    # 状态管理
    is_active = Column(Boolean, default=True)  # 是否激活
    status = Column(String(20), default="active")  # 状态
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="entity_relations")
    source_entity = relationship("FinancialEntity", foreign_keys=[source_entity_id], back_populates="source_relations")
    target_entity = relationship("FinancialEntity", foreign_keys=[target_entity_id], back_populates="target_relations")


class KnowledgeGraph(Base):
    """知识图谱"""
    
    __tablename__ = "knowledge_graphs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 图谱基本信息
    graph_name = Column(String(100), nullable=False)  # 图谱名称
    description = Column(Text, nullable=True)  # 图谱描述
    graph_type = Column(String(50), nullable=False)  # 图谱类型
    domain = Column(String(50), nullable=True)  # 领域
    
    # 图谱统计
    entity_count = Column(Integer, default=0)  # 实体数量
    relation_count = Column(Integer, default=0)  # 关系数量
    node_count = Column(Integer, default=0)  # 节点数量
    edge_count = Column(Integer, default=0)  # 边数量
    
    # 图谱结构信息
    graph_schema = Column(JSON)  # 图谱模式
    entity_types = Column(JSON)  # 实体类型列表
    relation_types = Column(JSON)  # 关系类型列表
    
    # 图谱配置
    construction_config = Column(JSON)  # 构建配置
    update_strategy = Column(String(50), default="incremental")  # 更新策略
    quality_threshold = Column(Float, default=0.8)  # 质量阈值
    
    # 图谱质量指标
    completeness_score = Column(Float, nullable=True)  # 完整性评分
    consistency_score = Column(Float, nullable=True)  # 一致性评分
    accuracy_score = Column(Float, nullable=True)  # 准确性评分
    freshness_score = Column(Float, nullable=True)  # 新鲜度评分
    
    # 图谱存储
    graph_data = Column(JSON)  # 图谱数据（小规模）
    graph_file_path = Column(String(500), nullable=True)  # 图谱文件路径（大规模）
    serialization_format = Column(String(20), default="json")  # 序列化格式
    
    # 版本控制
    version = Column(String(20), default="1.0")  # 版本号
    parent_graph_id = Column(Integer, ForeignKey("knowledge_graphs.id"), nullable=True)  # 父图谱ID
    is_latest_version = Column(Boolean, default=True)  # 是否最新版本
    
    # 状态管理
    status = Column(String(20), default="building")  # 状态
    build_progress = Column(Integer, default=0)  # 构建进度
    last_build_time = Column(DateTime, nullable=True)  # 最后构建时间
    
    # 访问控制
    is_public = Column(Boolean, default=False)  # 是否公开
    access_level = Column(String(20), default="private")  # 访问级别
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="knowledge_graphs")
    parent_graph = relationship("KnowledgeGraph", remote_side=[id])
    graph_analyses = relationship("GraphAnalysis", back_populates="knowledge_graph", cascade="all, delete-orphan")
    gnn_models = relationship("GNNModel", back_populates="knowledge_graph", cascade="all, delete-orphan")


class GraphAnalysis(Base):
    """图分析"""
    
    __tablename__ = "graph_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    knowledge_graph_id = Column(Integer, ForeignKey("knowledge_graphs.id"), nullable=False, index=True)
    
    # 分析基本信息
    analysis_name = Column(String(100), nullable=False)  # 分析名称
    analysis_type = Column(String(50), nullable=False)  # 分析类型
    description = Column(Text, nullable=True)  # 分析描述
    
    # 分析配置
    analysis_config = Column(JSON)  # 分析配置
    algorithms_used = Column(JSON)  # 使用的算法
    parameters = Column(JSON)  # 参数设置
    
    # 图统计指标
    graph_metrics = Column(JSON)  # 图指标
    centrality_measures = Column(JSON)  # 中心性度量
    clustering_coefficients = Column(JSON)  # 聚类系数
    path_lengths = Column(JSON)  # 路径长度
    
    # 社区发现
    community_detection_results = Column(JSON)  # 社区发现结果
    community_count = Column(Integer, nullable=True)  # 社区数量
    modularity_score = Column(Float, nullable=True)  # 模块度分数
    
    # 节点重要性
    node_importance_scores = Column(JSON)  # 节点重要性分数
    influential_nodes = Column(JSON)  # 影响力节点
    hub_nodes = Column(JSON)  # 枢纽节点
    
    # 关系分析
    relation_patterns = Column(JSON)  # 关系模式
    frequent_subgraphs = Column(JSON)  # 频繁子图
    anomalous_patterns = Column(JSON)  # 异常模式
    
    # 时间分析（如果适用）
    temporal_evolution = Column(JSON)  # 时间演化
    trend_analysis = Column(JSON)  # 趋势分析
    change_points = Column(JSON)  # 变化点
    
    # 可视化数据
    visualization_data = Column(JSON)  # 可视化数据
    layout_coordinates = Column(JSON)  # 布局坐标
    visual_properties = Column(JSON)  # 视觉属性
    
    # 执行信息
    computation_time_seconds = Column(Float, nullable=True)  # 计算时间
    memory_usage_mb = Column(Float, nullable=True)  # 内存使用
    algorithm_complexity = Column(String(50), nullable=True)  # 算法复杂度
    
    # 结果质量
    analysis_quality_score = Column(Float, nullable=True)  # 分析质量分数
    confidence_level = Column(Float, nullable=True)  # 置信水平
    statistical_significance = Column(JSON)  # 统计显著性
    
    # 状态管理
    status = Column(String(20), default="completed")  # 状态
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="graph_analyses")
    knowledge_graph = relationship("KnowledgeGraph", back_populates="graph_analyses")


# 图分析与实体的多对多关系表
graph_analysis_entities = Column(
    'graph_analysis_entities',
    Column('graph_analysis_id', Integer, ForeignKey('graph_analyses.id'), primary_key=True),
    Column('financial_entity_id', Integer, ForeignKey('financial_entities.id'), primary_key=True)
)


class GNNModel(Base):
    """图神经网络模型"""
    
    __tablename__ = "gnn_models"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    knowledge_graph_id = Column(Integer, ForeignKey("knowledge_graphs.id"), nullable=False, index=True)
    
    # 模型基本信息
    model_name = Column(String(100), nullable=False)  # 模型名称
    model_type = Column(String(50), nullable=False)  # 模型类型
    architecture = Column(String(50), nullable=False)  # 架构类型
    description = Column(Text, nullable=True)  # 模型描述
    
    # 模型架构
    model_config = Column(JSON)  # 模型配置
    layer_config = Column(JSON)  # 层配置
    hyperparameters = Column(JSON)  # 超参数
    
    # 训练配置
    training_config = Column(JSON)  # 训练配置
    optimization_config = Column(JSON)  # 优化配置
    loss_function = Column(String(50), nullable=True)  # 损失函数
    
    # 数据配置
    input_features = Column(JSON)  # 输入特征
    output_features = Column(JSON)  # 输出特征
    feature_dimensions = Column(JSON)  # 特征维度
    
    # 训练统计
    training_epochs = Column(Integer, nullable=True)  # 训练轮数
    training_time_seconds = Column(Float, nullable=True)  # 训练时间
    convergence_epoch = Column(Integer, nullable=True)  # 收敛轮数
    
    # 性能指标
    training_metrics = Column(JSON)  # 训练指标
    validation_metrics = Column(JSON)  # 验证指标
    test_metrics = Column(JSON)  # 测试指标
    
    # 模型评估
    accuracy_score = Column(Float, nullable=True)  # 准确率
    precision_score = Column(Float, nullable=True)  # 精确率
    recall_score = Column(Float, nullable=True)  # 召回率
    f1_score = Column(Float, nullable=True)  # F1分数
    auc_score = Column(Float, nullable=True)  # AUC分数
    
    # 模型存储
    model_binary = Column(LargeBinary, nullable=True)  # 模型二进制数据
    model_file_path = Column(String(500), nullable=True)  # 模型文件路径
    model_size_mb = Column(Float, nullable=True)  # 模型大小
    
    # 模型部署
    is_deployed = Column(Boolean, default=False)  # 是否部署
    deployment_config = Column(JSON)  # 部署配置
    api_endpoint = Column(String(200), nullable=True)  # API端点
    
    # 预测统计
    total_predictions = Column(Integer, default=0)  # 总预测次数
    successful_predictions = Column(Integer, default=0)  # 成功预测次数
    average_prediction_time_ms = Column(Float, nullable=True)  # 平均预测时间
    
    # 模型版本
    version = Column(String(20), default="1.0")  # 版本号
    parent_model_id = Column(Integer, ForeignKey("gnn_models.id"), nullable=True)  # 父模型ID
    is_latest_version = Column(Boolean, default=True)  # 是否最新版本
    
    # 状态管理
    status = Column(String(20), default="training")  # 状态
    training_progress = Column(Integer, default=0)  # 训练进度
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    trained_at = Column(DateTime, nullable=True)
    deployed_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="gnn_models")
    knowledge_graph = relationship("KnowledgeGraph", back_populates="gnn_models")
    parent_model = relationship("GNNModel", remote_side=[id])
    predictions = relationship("GNNPrediction", back_populates="gnn_model", cascade="all, delete-orphan")


class GNNPrediction(Base):
    """GNN预测结果"""
    
    __tablename__ = "gnn_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    gnn_model_id = Column(Integer, ForeignKey("gnn_models.id"), nullable=False, index=True)
    
    # 预测基本信息
    prediction_name = Column(String(100), nullable=True)  # 预测名称
    prediction_type = Column(String(50), nullable=False)  # 预测类型
    task_type = Column(String(50), nullable=False)  # 任务类型
    
    # 输入数据
    input_entities = Column(JSON)  # 输入实体
    input_features = Column(JSON)  # 输入特征
    input_graph_structure = Column(JSON)  # 输入图结构
    
    # 预测结果
    predictions = Column(JSON)  # 预测结果
    prediction_probabilities = Column(JSON)  # 预测概率
    confidence_scores = Column(JSON)  # 置信度分数
    
    # 预测统计
    prediction_count = Column(Integer, nullable=True)  # 预测数量
    prediction_time_ms = Column(Float, nullable=True)  # 预测时间
    average_confidence = Column(Float, nullable=True)  # 平均置信度
    
    # 结果验证
    actual_values = Column(JSON, nullable=True)  # 实际值
    prediction_errors = Column(JSON, nullable=True)  # 预测误差
    accuracy_metrics = Column(JSON, nullable=True)  # 准确性指标
    
    # 解释性信息
    feature_importance = Column(JSON, nullable=True)  # 特征重要性
    attention_weights = Column(JSON, nullable=True)  # 注意力权重
    explanation_data = Column(JSON, nullable=True)  # 解释数据
    
    # 业务上下文
    business_context = Column(JSON, nullable=True)  # 业务上下文
    market_conditions = Column(JSON, nullable=True)  # 市场条件
    external_factors = Column(JSON, nullable=True)  # 外部因素
    
    # 预测质量
    quality_score = Column(Float, nullable=True)  # 质量分数
    reliability_score = Column(Float, nullable=True)  # 可靠性分数
    is_validated = Column(Boolean, default=False)  # 是否验证
    
    # 状态管理
    status = Column(String(20), default="completed")  # 状态
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    prediction_date = Column(DateTime, default=datetime.utcnow)
    validation_date = Column(DateTime, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="gnn_predictions")
    gnn_model = relationship("GNNModel", back_populates="predictions")


class GraphEmbedding(Base):
    """图嵌入"""
    
    __tablename__ = "graph_embeddings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    knowledge_graph_id = Column(Integer, ForeignKey("knowledge_graphs.id"), nullable=False, index=True)
    
    # 嵌入基本信息
    embedding_name = Column(String(100), nullable=False)  # 嵌入名称
    embedding_method = Column(String(50), nullable=False)  # 嵌入方法
    description = Column(Text, nullable=True)  # 描述
    
    # 嵌入配置
    embedding_config = Column(JSON)  # 嵌入配置
    algorithm_parameters = Column(JSON)  # 算法参数
    training_parameters = Column(JSON)  # 训练参数
    
    # 嵌入维度
    embedding_dimension = Column(Integer, nullable=False)  # 嵌入维度
    entity_count = Column(Integer, nullable=True)  # 实体数量
    relation_count = Column(Integer, nullable=True)  # 关系数量
    
    # 嵌入数据
    entity_embeddings = Column(JSON)  # 实体嵌入
    relation_embeddings = Column(JSON, nullable=True)  # 关系嵌入
    embedding_matrix = Column(LargeBinary, nullable=True)  # 嵌入矩阵（二进制）
    
    # 嵌入质量
    embedding_quality_metrics = Column(JSON)  # 嵌入质量指标
    similarity_preservation = Column(Float, nullable=True)  # 相似性保持
    structure_preservation = Column(Float, nullable=True)  # 结构保持
    
    # 训练统计
    training_epochs = Column(Integer, nullable=True)  # 训练轮数
    training_time_seconds = Column(Float, nullable=True)  # 训练时间
    convergence_loss = Column(Float, nullable=True)  # 收敛损失
    
    # 应用统计
    usage_count = Column(Integer, default=0)  # 使用次数
    last_used_at = Column(DateTime, nullable=True)  # 最后使用时间
    
    # 状态管理
    status = Column(String(20), default="completed")  # 状态
    is_active = Column(Boolean, default=True)  # 是否激活
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    trained_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="graph_embeddings")
    knowledge_graph = relationship("KnowledgeGraph")


class EntitySimilarity(Base):
    """实体相似性"""
    
    __tablename__ = "entity_similarities"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 相似性基本信息
    entity1_id = Column(Integer, ForeignKey("financial_entities.id"), nullable=False, index=True)
    entity2_id = Column(Integer, ForeignKey("financial_entities.id"), nullable=False, index=True)
    similarity_type = Column(String(50), nullable=False)  # 相似性类型
    
    # 相似性分数
    similarity_score = Column(Float, nullable=False)  # 相似性分数
    confidence_score = Column(Float, nullable=True)  # 置信度分数
    
    # 相似性计算方法
    calculation_method = Column(String(50), nullable=False)  # 计算方法
    method_parameters = Column(JSON, nullable=True)  # 方法参数
    
    # 相似性详情
    similarity_details = Column(JSON, nullable=True)  # 相似性详情
    feature_contributions = Column(JSON, nullable=True)  # 特征贡献
    
    # 验证信息
    is_verified = Column(Boolean, default=False)  # 是否验证
    verification_source = Column(String(100), nullable=True)  # 验证来源
    
    # 时间戳
    calculated_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="entity_similarities")
    entity1 = relationship("FinancialEntity", foreign_keys=[entity1_id])
    entity2 = relationship("FinancialEntity", foreign_keys=[entity2_id])
