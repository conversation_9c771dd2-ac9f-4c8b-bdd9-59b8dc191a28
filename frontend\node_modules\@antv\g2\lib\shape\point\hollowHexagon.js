"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowHexagon = void 0;
const color_1 = require("./color");
/**
 * ⬡
 */
const HollowHexagon = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'hexagon' }, options), context);
};
exports.HollowHexagon = HollowHexagon;
exports.HollowHexagon.props = Object.assign({ defaultMarker: 'hollowHexagon' }, color_1.Color.props);
//# sourceMappingURL=hollowHexagon.js.map