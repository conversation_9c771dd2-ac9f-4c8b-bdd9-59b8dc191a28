"""add login_type to jqdata_config

Revision ID: add_login_type_jqdata
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_login_type_jqdata'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add login_type column to jqdata_configs table"""
    # Add login_type column with default value 'email'
    op.add_column('jqdata_configs', 
                  sa.Column('login_type', sa.String(20), 
                           nullable=False, 
                           server_default='email',
                           comment='登录类型: email 或 mobile'))


def downgrade() -> None:
    """Remove login_type column from jqdata_configs table"""
    op.drop_column('jqdata_configs', 'login_type')
