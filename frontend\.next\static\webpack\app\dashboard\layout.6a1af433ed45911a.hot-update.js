"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/**\n * API服务层\n * \n * 提供统一的HTTP请求接口，包含认证、错误处理、请求拦截等功能\n */ \n\n// import Cookies from 'js-cookie';\n// 简单的 cookie 工具函数\nconst CookieUtils = {\n    get: (name)=>{\n        var _parts_pop;\n        if (typeof document === \"undefined\") return undefined;\n        const value = \"; \".concat(document.cookie);\n        const parts = value.split(\"; \".concat(name, \"=\"));\n        if (parts.length === 2) return (_parts_pop = parts.pop()) === null || _parts_pop === void 0 ? void 0 : _parts_pop.split(\";\").shift();\n        return undefined;\n    },\n    set: (name, value, options)=>{\n        if (typeof document === \"undefined\") return;\n        let cookieString = \"\".concat(name, \"=\").concat(value);\n        if (options === null || options === void 0 ? void 0 : options.expires) {\n            const date = new Date();\n            date.setTime(date.getTime() + options.expires * 24 * 60 * 60 * 1000);\n            cookieString += \"; expires=\".concat(date.toUTCString());\n        }\n        cookieString += \"; path=/\";\n        document.cookie = cookieString;\n    },\n    remove: (name)=>{\n        if (typeof document === \"undefined\") return;\n        document.cookie = \"\".concat(name, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\");\n    }\n};\n// =============================================================================\n// 常量定义\n// =============================================================================\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_TIMEOUT = 30000; // 30秒超时\nconst TOKEN_KEY = \"access_token\";\nconst REFRESH_TOKEN_KEY = \"refresh_token\";\n// =============================================================================\n// API客户端类\n// =============================================================================\nclass ApiClient {\n    /**\n   * 设置请求和响应拦截器\n   */ setupInterceptors() {\n        // 请求拦截器\n        this.instance.interceptors.request.use((config)=>{\n            // 添加认证token\n            const token = this.getAccessToken();\n            if (token) {\n                config.headers.Authorization = \"Bearer \".concat(token);\n            }\n            // 添加请求ID\n            config.headers[\"X-Request-ID\"] = this.generateRequestId();\n            // 添加时间戳\n            config.headers[\"X-Timestamp\"] = Date.now().toString();\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // 响应拦截器\n        this.instance.interceptors.response.use((response)=>{\n            return response;\n        }, async (error)=>{\n            var _error_response;\n            const originalRequest = error.config;\n            // 处理401未授权错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401 && !originalRequest._retry) {\n                if (this.isRefreshing) {\n                    // 如果正在刷新token，将请求加入队列\n                    return new Promise((resolve, reject)=>{\n                        this.failedQueue.push({\n                            resolve,\n                            reject\n                        });\n                    }).then(()=>{\n                        return this.instance(originalRequest);\n                    }).catch((err)=>{\n                        return Promise.reject(err);\n                    });\n                }\n                originalRequest._retry = true;\n                this.isRefreshing = true;\n                try {\n                    const newTokens = await this.refreshToken();\n                    this.setTokens(newTokens);\n                    this.processQueue(null);\n                    // 重新发送原始请求\n                    return this.instance(originalRequest);\n                } catch (refreshError) {\n                    this.processQueue(refreshError);\n                    this.clearTokens();\n                    this.redirectToLogin();\n                    return Promise.reject(refreshError);\n                } finally{\n                    this.isRefreshing = false;\n                }\n            }\n            // 处理其他错误\n            this.handleError(error);\n            return Promise.reject(error);\n        });\n    }\n    /**\n   * 处理队列中的请求\n   */ processQueue(error) {\n        this.failedQueue.forEach((param)=>{\n            let { resolve, reject } = param;\n            if (error) {\n                reject(error);\n            } else {\n                resolve();\n            }\n        });\n        this.failedQueue = [];\n    }\n    /**\n   * 处理API错误\n   */ handleError(error) {\n        const response = error.response;\n        const status = response === null || response === void 0 ? void 0 : response.status;\n        const data = response === null || response === void 0 ? void 0 : response.data;\n        let errorMessage = \"网络请求失败\";\n        if (status) {\n            switch(status){\n                case 400:\n                    errorMessage = (data === null || data === void 0 ? void 0 : data.message) || \"请求参数错误\";\n                    break;\n                case 401:\n                    errorMessage = \"未授权，请重新登录\";\n                    break;\n                case 403:\n                    errorMessage = \"权限不足\";\n                    break;\n                case 404:\n                    errorMessage = \"请求的资源不存在\";\n                    break;\n                case 429:\n                    errorMessage = \"请求过于频繁，请稍后重试\";\n                    break;\n                case 500:\n                    errorMessage = \"服务器内部错误\";\n                    break;\n                case 502:\n                    errorMessage = \"网关错误\";\n                    break;\n                case 503:\n                    errorMessage = \"服务暂时不可用\";\n                    break;\n                default:\n                    errorMessage = (data === null || data === void 0 ? void 0 : data.message) || \"请求失败 (\".concat(status, \")\");\n            }\n        } else if (error.code === \"ECONNABORTED\") {\n            errorMessage = \"请求超时\";\n        } else if (error.message === \"Network Error\") {\n            errorMessage = \"网络连接失败\";\n        }\n        // 显示错误消息\n        _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_0__[\"default\"].error(errorMessage);\n    }\n    /**\n   * 生成请求ID\n   */ generateRequestId() {\n        return \"req_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n    }\n    /**\n   * 获取访问token\n   */ getAccessToken() {\n        return CookieUtils.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY);\n    }\n    /**\n   * 获取刷新token\n   */ getRefreshToken() {\n        return CookieUtils.get(REFRESH_TOKEN_KEY) || localStorage.getItem(REFRESH_TOKEN_KEY);\n    }\n    /**\n   * 设置tokens\n   */ setTokens(tokens) {\n        const { accessToken, refreshToken } = tokens;\n        // 设置到Cookie（7天过期）\n        CookieUtils.set(TOKEN_KEY, accessToken, {\n            expires: 7\n        });\n        CookieUtils.set(REFRESH_TOKEN_KEY, refreshToken, {\n            expires: 7\n        });\n        // 设置到localStorage作为备份\n        localStorage.setItem(TOKEN_KEY, accessToken);\n        localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);\n    }\n    /**\n   * 清除tokens\n   */ clearTokens() {\n        CookieUtils.remove(TOKEN_KEY);\n        CookieUtils.remove(REFRESH_TOKEN_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    }\n    /**\n   * 刷新token\n   */ async refreshToken() {\n        const refreshToken = this.getRefreshToken();\n        if (!refreshToken) {\n            throw new Error(\"No refresh token available\");\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(API_BASE_URL, \"/api/v1/auth/refresh\"), {\n            refreshToken\n        });\n        return response.data.data;\n    }\n    /**\n   * 重定向到登录页\n   */ redirectToLogin() {\n        if (true) {\n            window.location.href = \"/auth/login\";\n        }\n    }\n    // =============================================================================\n    // 公共方法\n    // =============================================================================\n    /**\n   * GET请求\n   */ async get(url, config) {\n        const response = await this.instance.get(url, config);\n        return response.data;\n    }\n    /**\n   * POST请求\n   */ async post(url, data, config) {\n        const response = await this.instance.post(url, data, config);\n        return response.data;\n    }\n    /**\n   * PUT请求\n   */ async put(url, data, config) {\n        const response = await this.instance.put(url, data, config);\n        return response.data;\n    }\n    /**\n   * DELETE请求\n   */ async delete(url, config) {\n        const response = await this.instance.delete(url, config);\n        return response.data;\n    }\n    /**\n   * PATCH请求\n   */ async patch(url, data, config) {\n        const response = await this.instance.patch(url, data, config);\n        return response.data;\n    }\n    /**\n   * 上传文件\n   */ async upload(url, file, onProgress) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const config = {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        };\n        const response = await this.instance.post(url, formData, config);\n        return response.data;\n    }\n    /**\n   * 下载文件\n   */ async download(url, filename, onProgress) {\n        const config = {\n            responseType: \"blob\",\n            onDownloadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        };\n        const response = await this.instance.get(url, config);\n        // 创建下载链接\n        const blob = new Blob([\n            response.data\n        ]);\n        const downloadUrl = window.URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = downloadUrl;\n        link.download = filename || \"download\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(downloadUrl);\n    }\n    /**\n   * 设置认证tokens\n   */ setAuthTokens(tokens) {\n        this.setTokens(tokens);\n    }\n    /**\n   * 清除认证信息\n   */ clearAuth() {\n        this.clearTokens();\n    }\n    /**\n   * 检查是否已认证\n   */ isAuthenticated() {\n        return !!this.getAccessToken();\n    }\n    /**\n   * 获取原始axios实例\n   */ getInstance() {\n        return this.instance;\n    }\n    constructor(){\n        this.isRefreshing = false;\n        this.failedQueue = [];\n        this.instance = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n            baseURL: API_BASE_URL,\n            timeout: API_TIMEOUT,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n}\n// =============================================================================\n// 导出API客户端实例\n// =============================================================================\nconst apiClient = new ApiClient();\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});