'use client';

/**
 * 数据源管理页面
 * 
 * 管理和监控多个数据源
 */

import React from 'react';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { DataSourceManager } from '@/components/data-sources/DataSourceManager';

function DataSourcesContent() {
  return <DataSourceManager />;
}

export default function DataSourcesPage() {
  return (
    <ClientAuthWrapper>
      <DataSourcesContent />
    </ClientAuthWrapper>
  );
}
