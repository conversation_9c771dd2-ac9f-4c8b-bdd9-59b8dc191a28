"""
基础API测试脚本

测试基本的API连接和响应功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import httpx
import pytest
from fastapi.testclient import TestClient

# 导入应用
from backend.app.main import app

# 创建测试客户端
client = TestClient(app)


class TestBasicAPI:
    """基础API测试类"""
    
    def test_root_endpoint(self):
        """测试根路径"""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert "JQData量化数据平台API" in data["message"]
        assert "data" in data
        assert data["data"]["name"] == "JQData量化平台"
    
    def test_health_check(self):
        """测试健康检查"""
        response = client.get("/health")
        assert response.status_code in [200, 503]  # 可能因为数据库未连接返回503
        
        data = response.json()
        assert "code" in data
        assert "message" in data
        assert "data" in data
        
        if data["code"] == 200:
            assert data["data"]["status"] == "healthy"
        else:
            assert data["data"]["status"] in ["unhealthy", "error"]
    
    def test_app_info(self):
        """测试应用信息"""
        response = client.get("/info")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["name"] == "JQData量化平台"
        assert data["data"]["version"] == "1.0.0"
        assert "environment" in data["data"]
        assert "features" in data["data"]
    
    def test_cors_headers(self):
        """测试CORS头"""
        response = client.options("/")
        # 根据实际CORS配置验证
        assert response.status_code in [200, 405]  # OPTIONS可能不被支持
    
    def test_security_headers(self):
        """测试安全头"""
        response = client.get("/")
        headers = response.headers
        
        # 检查安全头
        assert "X-Content-Type-Options" in headers
        assert headers["X-Content-Type-Options"] == "nosniff"
        assert "X-Frame-Options" in headers
        assert headers["X-Frame-Options"] == "DENY"
        assert "Server" in headers
        assert headers["Server"] == "JQData-Platform"
    
    def test_request_id_header(self):
        """测试请求ID头"""
        response = client.get("/")
        assert "X-Request-ID" in response.headers
        
        request_id = response.headers["X-Request-ID"]
        assert len(request_id) > 0
    
    def test_process_time_header(self):
        """测试处理时间头"""
        response = client.get("/")
        assert "X-Process-Time" in response.headers
        
        process_time = float(response.headers["X-Process-Time"])
        assert process_time >= 0
    
    def test_404_error(self):
        """测试404错误"""
        response = client.get("/nonexistent")
        assert response.status_code == 404
        
        data = response.json()
        assert data["code"] == 404
    
    def test_method_not_allowed(self):
        """测试方法不允许错误"""
        response = client.post("/")
        assert response.status_code == 405
        
        data = response.json()
        assert data["code"] == 405


class TestAPIPerformance:
    """API性能测试类"""
    
    def test_response_time(self):
        """测试响应时间"""
        import time
        
        start_time = time.time()
        response = client.get("/")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 1.0  # 响应时间应小于1秒
        assert response.status_code == 200
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        import threading
        import time
        
        results = []
        
        def make_request():
            response = client.get("/")
            results.append(response.status_code)
        
        # 创建10个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        start_time = time.time()
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        
        # 验证结果
        assert len(results) == 10
        assert all(status == 200 for status in results)
        assert end_time - start_time < 5.0  # 总时间应小于5秒


class TestAPIValidation:
    """API验证测试类"""
    
    def test_json_response_format(self):
        """测试JSON响应格式"""
        response = client.get("/")
        assert response.headers["content-type"] == "application/json"
        
        data = response.json()
        
        # 验证标准响应格式
        required_fields = ["code", "message", "data"]
        for field in required_fields:
            assert field in data
        
        assert isinstance(data["code"], int)
        assert isinstance(data["message"], str)
    
    def test_error_response_format(self):
        """测试错误响应格式"""
        response = client.get("/nonexistent")
        assert response.status_code == 404
        
        data = response.json()
        
        # 验证错误响应格式
        assert "code" in data
        assert "message" in data
        assert data["code"] == 404
        assert isinstance(data["message"], str)


async def test_async_endpoints():
    """测试异步端点"""
    async with httpx.AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert data["code"] == 200


def run_basic_tests():
    """运行基础测试"""
    print("🧪 开始运行基础API测试...")
    
    # 运行同步测试
    test_basic = TestBasicAPI()
    test_performance = TestAPIPerformance()
    test_validation = TestAPIValidation()
    
    try:
        # 基础功能测试
        print("✅ 测试根路径...")
        test_basic.test_root_endpoint()
        
        print("✅ 测试健康检查...")
        test_basic.test_health_check()
        
        print("✅ 测试应用信息...")
        test_basic.test_app_info()
        
        print("✅ 测试安全头...")
        test_basic.test_security_headers()
        
        print("✅ 测试请求ID...")
        test_basic.test_request_id_header()
        
        print("✅ 测试404错误...")
        test_basic.test_404_error()
        
        # 性能测试
        print("✅ 测试响应时间...")
        test_performance.test_response_time()
        
        print("✅ 测试并发请求...")
        test_performance.test_concurrent_requests()
        
        # 验证测试
        print("✅ 测试JSON格式...")
        test_validation.test_json_response_format()
        
        print("✅ 测试错误格式...")
        test_validation.test_error_response_format()
        
        # 异步测试
        print("✅ 测试异步端点...")
        asyncio.run(test_async_endpoints())
        
        print("🎉 所有基础API测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
