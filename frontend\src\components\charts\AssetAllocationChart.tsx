'use client';

/**
 * 资产配置饼图组件
 * 
 * 显示投资组合的资产配置分布
 */

import React, { useEffect, useRef, useState } from 'react';
import { Spin, Typography } from 'antd';
import * as echarts from 'echarts';

const { Text } = Typography;

interface Position {
  id: string;
  symbol: string;
  name: string;
  marketValue: number;
  weight: number;
  sector: string;
}

interface AssetAllocationChartProps {
  data: Position[];
  height?: number;
  showLegend?: boolean;
}

export const AssetAllocationChart: React.FC<AssetAllocationChartProps> = ({
  data,
  height = 300,
  showLegend = true,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);

  // 按行业聚合数据
  const aggregateDataBySector = () => {
    const sectorMap = new Map<string, { value: number; count: number; stocks: string[] }>();
    
    data.forEach(position => {
      const sector = position.sector || '其他';
      if (sectorMap.has(sector)) {
        const existing = sectorMap.get(sector)!;
        existing.value += position.marketValue;
        existing.count += 1;
        existing.stocks.push(position.name);
      } else {
        sectorMap.set(sector, {
          value: position.marketValue,
          count: 1,
          stocks: [position.name],
        });
      }
    });

    return Array.from(sectorMap.entries()).map(([name, data]) => ({
      name,
      value: data.value,
      count: data.count,
      stocks: data.stocks,
    }));
  };

  // 颜色配置
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb',
  ];

  // 初始化图表
  const initChart = () => {
    if (!chartRef.current || data.length === 0) return;

    // 销毁现有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);
    
    const sectorData = aggregateDataBySector();
    const totalValue = sectorData.reduce((sum, item) => sum + item.value, 0);

    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      color: colors,
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E6E8EB',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        formatter: (params: any) => {
          const data = params.data;
          const percentage = ((data.value / totalValue) * 100).toFixed(1);
          
          return `
            <div style="padding: 8px;">
              <div style="margin-bottom: 8px; font-weight: bold;">${data.name}</div>
              <div style="margin-bottom: 4px;">
                市值: ¥${data.value.toLocaleString()}
              </div>
              <div style="margin-bottom: 4px;">
                占比: ${percentage}%
              </div>
              <div style="margin-bottom: 4px;">
                股票数量: ${data.count}只
              </div>
              <div style="font-size: 11px; color: #666;">
                ${data.stocks.join(', ')}
              </div>
            </div>
          `;
        },
      },
      legend: showLegend ? {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
        textStyle: {
          color: '#8392A5',
          fontSize: 12,
        },
        formatter: (name: string) => {
          const item = sectorData.find(d => d.name === name);
          if (item) {
            const percentage = ((item.value / totalValue) * 100).toFixed(1);
            return `${name} ${percentage}%`;
          }
          return name;
        },
      } : undefined,
      series: [
        {
          name: '资产配置',
          type: 'pie',
          radius: showLegend ? ['40%', '70%'] : ['30%', '60%'],
          center: showLegend ? ['65%', '50%'] : ['50%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: !showLegend,
            position: 'outside',
            formatter: '{b}\n{d}%',
            fontSize: 12,
            color: '#333',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          labelLine: {
            show: !showLegend,
          },
          data: sectorData.map(item => ({
            name: item.name,
            value: item.value,
            count: item.count,
            stocks: item.stocks,
          })),
        },
      ],
      animation: true,
      animationType: 'scale',
      animationEasing: 'elasticOut',
      animationDelay: (idx: number) => Math.random() * 200,
    };

    chartInstance.current.setOption(option);
    setLoading(false);
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  };

  useEffect(() => {
    if (data.length > 0) {
      initChart();
    } else {
      setLoading(false);
    }
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, showLegend]);

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center" style={{ height: `${height}px` }}>
        <Text type="secondary">暂无持仓数据</Text>
      </div>
    );
  }

  return (
    <div className="relative">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <Spin size="large" />
        </div>
      )}
      <div
        ref={chartRef}
        style={{ height: `${height}px`, width: '100%' }}
        className="transition-opacity duration-300"
      />
    </div>
  );
};
