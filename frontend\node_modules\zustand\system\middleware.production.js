System.register([],function(b){"use strict";return{execute:function(){b("createJSONStorage",E);const A=b("redux",(d,c)=>(a,o,n)=>(n.dispatch=e=>(a(g=>d(g,e),!1,e),e),n.dispatchFromDevtools=!0,{dispatch:(...e)=>n.dispatch(...e),...c})),I=new Map,w=d=>{const c=I.get(d);return c?Object.fromEntries(Object.entries(c.stores).map(([a,o])=>[a,o.getState()])):{}},T=(d,c,a)=>{if(d===void 0)return{type:"untracked",connection:c.connect(a)};const o=I.get(a.name);if(o)return{type:"tracked",store:d,...o};const n={connection:c.connect(a),stores:{}};return I.set(a.name,n),{type:"tracked",store:d,...n}},P=b("devtools",(d,c={})=>(a,o,n)=>{const{enabled:e,anonymousActionType:g,store:u,...h}=c;let m;try{m=(e!=null?e:!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!m)return d(a,o,n);const{connection:l,...y}=T(u,m,h);let p=!0;n.setState=(r,s,i)=>{const t=a(r,s);if(!p)return t;const f=i===void 0?{type:g||"anonymous"}:typeof i=="string"?{type:i}:i;return u===void 0?(l==null||l.send(f,o()),t):(l==null||l.send({...f,type:`${u}/${f.type}`},{...w(h.name),[u]:n.getState()}),t)};const v=(...r)=>{const s=p;p=!1,a(...r),p=s},S=d(n.setState,o,n);if(y.type==="untracked"?l==null||l.init(S):(y.stores[y.store]=n,l==null||l.init(Object.fromEntries(Object.entries(y.stores).map(([r,s])=>[r,r===y.store?S:s.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){const r=n.dispatch;n.dispatch=(...s)=>{r(...s)}}return l.subscribe(r=>{var s;switch(r.type){case"ACTION":if(typeof r.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return _(r.payload,i=>{if(i.type==="__setState"){if(u===void 0){v(i.state);return}Object.keys(i.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const t=i.state[u];if(t==null)return;JSON.stringify(n.getState())!==JSON.stringify(t)&&v(t);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(i)});case"DISPATCH":switch(r.payload.type){case"RESET":return v(S),u===void 0?l==null?void 0:l.init(n.getState()):l==null?void 0:l.init(w(h.name));case"COMMIT":if(u===void 0){l==null||l.init(n.getState());return}return l==null?void 0:l.init(w(h.name));case"ROLLBACK":return _(r.state,i=>{if(u===void 0){v(i),l==null||l.init(n.getState());return}v(i[u]),l==null||l.init(w(h.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return _(r.state,i=>{if(u===void 0){v(i);return}JSON.stringify(n.getState())!==JSON.stringify(i[u])&&v(i[u])});case"IMPORT_STATE":{const{nextLiftedState:i}=r.payload,t=(s=i.computedStates.slice(-1)[0])==null?void 0:s.state;if(!t)return;v(u===void 0?t:t[u]),l==null||l.send(null,i);return}case"PAUSE_RECORDING":return p=!p}return}}),S}),_=(d,c)=>{let a;try{a=JSON.parse(d)}catch(o){console.error("[zustand devtools middleware] Could not parse the received json",o)}a!==void 0&&c(a)},H=b("subscribeWithSelector",d=>(c,a,o)=>{const n=o.subscribe;return o.subscribe=(e,g,u)=>{let h=e;if(g){const m=(u==null?void 0:u.equalityFn)||Object.is;let l=e(o.getState());h=y=>{const p=e(y);if(!m(l,p)){const v=l;g(l=p,v)}},u!=null&&u.fireImmediately&&g(l,l)}return n(h)},d(c,a,o)}),R=b("combine",(d,c)=>(...a)=>Object.assign({},d,c(...a)));function E(d,c){let a;try{a=d()}catch{return}return{getItem:o=>{var n;const e=u=>u===null?null:JSON.parse(u,c==null?void 0:c.reviver),g=(n=a.getItem(o))!=null?n:null;return g instanceof Promise?g.then(e):e(g)},setItem:(o,n)=>a.setItem(o,JSON.stringify(n,c==null?void 0:c.replacer)),removeItem:o=>a.removeItem(o)}}const O=d=>c=>{try{const a=d(c);return a instanceof Promise?a:{then(o){return O(o)(a)},catch(o){return this}}}catch(a){return{then(o){return this},catch(o){return O(o)(a)}}}},N=(d,c)=>(a,o,n)=>{let e={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:s=>s,version:0,merge:(s,i)=>({...i,...s}),...c},g=!1;const u=new Set,h=new Set;let m;try{m=e.getStorage()}catch{}if(!m)return d((...s)=>{console.warn(`[zustand persist middleware] Unable to update item '${e.name}', the given storage is currently unavailable.`),a(...s)},o,n);const l=O(e.serialize),y=()=>{const s=e.partialize({...o()});let i;const t=l({state:s,version:e.version}).then(f=>m.setItem(e.name,f)).catch(f=>{i=f});if(i)throw i;return t},p=n.setState;n.setState=(s,i)=>{p(s,i),y()};const v=d((...s)=>{a(...s),y()},o,n);let S;const r=()=>{var s;if(!m)return;g=!1,u.forEach(t=>t(o()));const i=((s=e.onRehydrateStorage)==null?void 0:s.call(e,o()))||void 0;return O(m.getItem.bind(m))(e.name).then(t=>{if(t)return e.deserialize(t)}).then(t=>{if(t)if(typeof t.version=="number"&&t.version!==e.version){if(e.migrate)return e.migrate(t.state,t.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return t.state}).then(t=>{var f;return S=e.merge(t,(f=o())!=null?f:v),a(S,!0),y()}).then(()=>{i==null||i(S,void 0),g=!0,h.forEach(t=>t(S))}).catch(t=>{i==null||i(void 0,t)})};return n.persist={setOptions:s=>{e={...e,...s},s.getStorage&&(m=s.getStorage())},clearStorage:()=>{m==null||m.removeItem(e.name)},getOptions:()=>e,rehydrate:()=>r(),hasHydrated:()=>g,onHydrate:s=>(u.add(s),()=>{u.delete(s)}),onFinishHydration:s=>(h.add(s),()=>{h.delete(s)})},r(),S||v},z=(d,c)=>(a,o,n)=>{let e={storage:E(()=>localStorage),partialize:r=>r,version:0,merge:(r,s)=>({...s,...r}),...c},g=!1;const u=new Set,h=new Set;let m=e.storage;if(!m)return d((...r)=>{console.warn(`[zustand persist middleware] Unable to update item '${e.name}', the given storage is currently unavailable.`),a(...r)},o,n);const l=()=>{const r=e.partialize({...o()});return m.setItem(e.name,{state:r,version:e.version})},y=n.setState;n.setState=(r,s)=>{y(r,s),l()};const p=d((...r)=>{a(...r),l()},o,n);let v;const S=()=>{var r,s;if(!m)return;g=!1,u.forEach(t=>{var f;return t((f=o())!=null?f:p)});const i=((s=e.onRehydrateStorage)==null?void 0:s.call(e,(r=o())!=null?r:p))||void 0;return O(m.getItem.bind(m))(e.name).then(t=>{if(t)if(typeof t.version=="number"&&t.version!==e.version){if(e.migrate)return e.migrate(t.state,t.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return t.state}).then(t=>{var f;return v=e.merge(t,(f=o())!=null?f:p),a(v,!0),l()}).then(()=>{i==null||i(v,void 0),v=o(),g=!0,h.forEach(t=>t(v))}).catch(t=>{i==null||i(void 0,t)})};return n.persist={setOptions:r=>{e={...e,...r},r.storage&&(m=r.storage)},clearStorage:()=>{m==null||m.removeItem(e.name)},getOptions:()=>e,rehydrate:()=>S(),hasHydrated:()=>g,onHydrate:r=>(u.add(r),()=>{u.delete(r)}),onFinishHydration:r=>(h.add(r),()=>{h.delete(r)})},e.skipHydration||S(),v||p},$=b("persist",(d,c)=>"getStorage"in c||"serialize"in c||"deserialize"in c?N(d,c):z(d,c))}}});
