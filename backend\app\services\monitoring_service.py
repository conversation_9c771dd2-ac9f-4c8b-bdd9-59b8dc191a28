"""
系统监控服务

提供系统性能监控、健康检查、告警等功能
"""

import psutil
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.core.logging import logger
from app.core.database import get_db_session
from app.models.user import User


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used: int
    memory_total: int
    disk_percent: float
    disk_used: int
    disk_total: int
    network_sent: int
    network_recv: int
    active_connections: int
    load_average: List[float]


@dataclass
class AlertRule:
    """告警规则数据类"""
    name: str
    metric: str
    operator: str  # >, <, >=, <=, ==
    threshold: float
    duration: int  # 持续时间（秒）
    severity: str  # low, medium, high, critical
    enabled: bool = True


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1440  # 保留24小时数据（每分钟一个点）
        self.alert_rules = self._get_default_alert_rules()
        self.active_alerts: Dict[str, Dict] = {}
        self.is_monitoring = False
    
    def _get_default_alert_rules(self) -> List[AlertRule]:
        """获取默认告警规则"""
        return [
            AlertRule("CPU使用率过高", "cpu_percent", ">", 80.0, 300, "high"),
            AlertRule("内存使用率过高", "memory_percent", ">", 85.0, 300, "high"),
            AlertRule("磁盘使用率过高", "disk_percent", ">", 90.0, 600, "critical"),
            AlertRule("CPU使用率极高", "cpu_percent", ">", 95.0, 60, "critical"),
            AlertRule("内存使用率极高", "memory_percent", ">", 95.0, 60, "critical"),
        ]
    
    def collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU指标
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存指标
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used = memory.used
            memory_total = memory.total
            
            # 磁盘指标
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent if disk.total > 0 else 0
            disk_used = disk.used
            disk_total = disk.total
            
            # 网络指标
            network = psutil.net_io_counters()
            network_sent = network.bytes_sent
            network_recv = network.bytes_recv
            
            # 连接数
            active_connections = len(psutil.net_connections())
            
            # 负载平均值
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows系统没有getloadavg
                load_average = [0.0, 0.0, 0.0]
            
            return SystemMetrics(
                timestamp=datetime.utcnow(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used=memory_used,
                memory_total=memory_total,
                disk_percent=disk_percent,
                disk_used=disk_used,
                disk_total=disk_total,
                network_sent=network_sent,
                network_recv=network_recv,
                active_connections=active_connections,
                load_average=load_average
            )
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics(
                timestamp=datetime.utcnow(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used=0,
                memory_total=0,
                disk_percent=0.0,
                disk_used=0,
                disk_total=0,
                network_sent=0,
                network_recv=0,
                active_connections=0,
                load_average=[0.0, 0.0, 0.0]
            )
    
    def add_metrics(self, metrics: SystemMetrics):
        """添加指标到历史记录"""
        self.metrics_history.append(metrics)
        
        # 保持历史记录大小
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
    
    def check_alerts(self, metrics: SystemMetrics):
        """检查告警规则"""
        current_time = datetime.utcnow()
        
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
            
            # 获取指标值
            metric_value = getattr(metrics, rule.metric, 0)
            
            # 检查阈值
            alert_triggered = self._evaluate_condition(
                metric_value, rule.operator, rule.threshold
            )
            
            alert_key = f"{rule.name}_{rule.metric}"
            
            if alert_triggered:
                if alert_key not in self.active_alerts:
                    # 新告警
                    self.active_alerts[alert_key] = {
                        "rule": rule,
                        "first_triggered": current_time,
                        "last_triggered": current_time,
                        "count": 1,
                        "notified": False
                    }
                else:
                    # 更新现有告警
                    self.active_alerts[alert_key]["last_triggered"] = current_time
                    self.active_alerts[alert_key]["count"] += 1
                
                # 检查是否需要发送通知
                alert_info = self.active_alerts[alert_key]
                duration = (current_time - alert_info["first_triggered"]).total_seconds()
                
                if duration >= rule.duration and not alert_info["notified"]:
                    self._send_alert_notification(rule, metric_value, duration)
                    alert_info["notified"] = True
            else:
                # 告警恢复
                if alert_key in self.active_alerts:
                    alert_info = self.active_alerts[alert_key]
                    if alert_info["notified"]:
                        self._send_recovery_notification(rule, metric_value)
                    del self.active_alerts[alert_key]
    
    def _evaluate_condition(self, value: float, operator: str, threshold: float) -> bool:
        """评估告警条件"""
        if operator == ">":
            return value > threshold
        elif operator == "<":
            return value < threshold
        elif operator == ">=":
            return value >= threshold
        elif operator == "<=":
            return value <= threshold
        elif operator == "==":
            return value == threshold
        return False
    
    def _send_alert_notification(self, rule: AlertRule, value: float, duration: float):
        """发送告警通知"""
        logger.warning(
            f"系统告警: {rule.name} - "
            f"当前值: {value:.2f}, 阈值: {rule.threshold}, "
            f"持续时间: {duration:.0f}秒, 严重程度: {rule.severity}"
        )
        
        # 这里可以集成邮件、短信、钉钉等通知方式
        # 目前只记录日志
    
    def _send_recovery_notification(self, rule: AlertRule, value: float):
        """发送恢复通知"""
        logger.info(
            f"系统恢复: {rule.name} - "
            f"当前值: {value:.2f}, 阈值: {rule.threshold}"
        )
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前系统状态"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        latest_metrics = self.metrics_history[-1]
        
        # 判断系统健康状态
        status = "healthy"
        if latest_metrics.cpu_percent > 90 or latest_metrics.memory_percent > 90:
            status = "critical"
        elif latest_metrics.cpu_percent > 80 or latest_metrics.memory_percent > 80:
            status = "warning"
        
        return {
            "status": status,
            "timestamp": latest_metrics.timestamp.isoformat(),
            "cpu_percent": latest_metrics.cpu_percent,
            "memory_percent": latest_metrics.memory_percent,
            "disk_percent": latest_metrics.disk_percent,
            "active_connections": latest_metrics.active_connections,
            "load_average": latest_metrics.load_average,
            "active_alerts": len(self.active_alerts),
            "uptime": self._get_system_uptime()
        }
    
    def _get_system_uptime(self) -> float:
        """获取系统运行时间"""
        try:
            return time.time() - psutil.boot_time()
        except Exception:
            return 0.0
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取指标历史数据"""
        if not self.metrics_history:
            return []
        
        # 计算时间范围
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)
        
        # 筛选数据
        filtered_metrics = [
            {
                "timestamp": m.timestamp.isoformat(),
                "cpu_percent": m.cpu_percent,
                "memory_percent": m.memory_percent,
                "disk_percent": m.disk_percent,
                "active_connections": m.active_connections,
                "network_sent": m.network_sent,
                "network_recv": m.network_recv,
            }
            for m in self.metrics_history
            if m.timestamp >= start_time
        ]
        
        return filtered_metrics


class ApplicationMonitor:
    """应用监控器"""
    
    def __init__(self):
        self.request_count = 0
        self.error_count = 0
        self.response_times: List[float] = []
        self.max_response_times = 1000  # 保留最近1000个响应时间
    
    def record_request(self, response_time: float, status_code: int):
        """记录请求"""
        self.request_count += 1
        
        if status_code >= 400:
            self.error_count += 1
        
        self.response_times.append(response_time)
        if len(self.response_times) > self.max_response_times:
            self.response_times = self.response_times[-self.max_response_times:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取应用统计"""
        if not self.response_times:
            return {
                "request_count": self.request_count,
                "error_count": self.error_count,
                "error_rate": 0.0,
                "avg_response_time": 0.0,
                "p95_response_time": 0.0,
                "p99_response_time": 0.0
            }
        
        import numpy as np
        
        error_rate = (self.error_count / self.request_count * 100) if self.request_count > 0 else 0
        avg_response_time = np.mean(self.response_times)
        p95_response_time = np.percentile(self.response_times, 95)
        p99_response_time = np.percentile(self.response_times, 99)
        
        return {
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": round(error_rate, 2),
            "avg_response_time": round(avg_response_time, 2),
            "p95_response_time": round(p95_response_time, 2),
            "p99_response_time": round(p99_response_time, 2)
        }


class DatabaseMonitor:
    """数据库监控器"""
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计"""
        try:
            async with get_db_session() as db:
                # 查询用户数量
                user_count_result = await db.execute(select(func.count(User.id)))
                user_count = user_count_result.scalar()
                
                # 查询活跃用户数量（最近24小时登录）
                yesterday = datetime.utcnow() - timedelta(days=1)
                active_user_result = await db.execute(
                    select(func.count(User.id)).where(User.last_login >= yesterday)
                )
                active_users = active_user_result.scalar()
                
                return {
                    "total_users": user_count,
                    "active_users_24h": active_users,
                    "connection_status": "healthy"
                }
                
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {
                "total_users": 0,
                "active_users_24h": 0,
                "connection_status": "error",
                "error": str(e)
            }


class MonitoringService:
    """监控服务"""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.app_monitor = ApplicationMonitor()
        self.db_monitor = DatabaseMonitor()
        self.monitoring_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self):
        """启动监控"""
        if self.system_monitor.is_monitoring:
            return
        
        self.system_monitor.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("系统监控服务启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.system_monitor.is_monitoring = False
        
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("系统监控服务停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.system_monitor.is_monitoring:
            try:
                # 收集系统指标
                metrics = self.system_monitor.collect_metrics()
                self.system_monitor.add_metrics(metrics)
                
                # 检查告警
                self.system_monitor.check_alerts(metrics)
                
                # 等待下一次收集
                await asyncio.sleep(60)  # 每分钟收集一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(60)
    
    async def get_health_check(self) -> Dict[str, Any]:
        """健康检查"""
        system_status = self.system_monitor.get_current_status()
        app_stats = self.app_monitor.get_stats()
        db_stats = await self.db_monitor.get_database_stats()
        
        # 综合健康状态
        overall_status = "healthy"
        if (system_status["status"] == "critical" or 
            app_stats["error_rate"] > 10 or 
            db_stats["connection_status"] == "error"):
            overall_status = "critical"
        elif (system_status["status"] == "warning" or 
              app_stats["error_rate"] > 5):
            overall_status = "warning"
        
        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "system": system_status,
            "application": app_stats,
            "database": db_stats,
            "services": {
                "monitoring": self.system_monitor.is_monitoring,
                "websocket": True,  # 实际应该检查WebSocket服务状态
                "redis": True,  # 实际应该检查Redis连接状态
            }
        }
    
    def record_request(self, response_time: float, status_code: int):
        """记录请求（供中间件调用）"""
        self.app_monitor.record_request(response_time, status_code)


# 全局监控服务实例
monitoring_service = MonitoringService()
