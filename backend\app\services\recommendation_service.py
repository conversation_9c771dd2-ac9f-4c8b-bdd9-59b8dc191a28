"""
推荐系统服务

提供策略推荐、股票推荐、个性化推荐等功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
from sklearn.neighbors import NearestNeighbors
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.logging import logger
from app.models.user import User
from app.models.backtest import Strategy, BacktestTask, BacktestResult
from app.models.ml import MLRecommendation, MLModel


class CollaborativeFilteringRecommender:
    """协同过滤推荐器"""
    
    def __init__(self):
        self.user_item_matrix = None
        self.item_similarity_matrix = None
        self.user_similarity_matrix = None
    
    def fit_user_based(self, user_item_matrix: pd.DataFrame):
        """训练基于用户的协同过滤"""
        try:
            self.user_item_matrix = user_item_matrix.fillna(0)
            
            # 计算用户相似度矩阵
            self.user_similarity_matrix = cosine_similarity(self.user_item_matrix)
            self.user_similarity_matrix = pd.DataFrame(
                self.user_similarity_matrix,
                index=self.user_item_matrix.index,
                columns=self.user_item_matrix.index
            )
            
            logger.info("基于用户的协同过滤训练完成")
            
        except Exception as e:
            logger.error(f"训练基于用户的协同过滤失败: {e}")
            raise
    
    def fit_item_based(self, user_item_matrix: pd.DataFrame):
        """训练基于物品的协同过滤"""
        try:
            self.user_item_matrix = user_item_matrix.fillna(0)
            
            # 计算物品相似度矩阵
            self.item_similarity_matrix = cosine_similarity(self.user_item_matrix.T)
            self.item_similarity_matrix = pd.DataFrame(
                self.item_similarity_matrix,
                index=self.user_item_matrix.columns,
                columns=self.user_item_matrix.columns
            )
            
            logger.info("基于物品的协同过滤训练完成")
            
        except Exception as e:
            logger.error(f"训练基于物品的协同过滤失败: {e}")
            raise
    
    def recommend_user_based(self, user_id: int, n_recommendations: int = 10) -> List[Tuple[str, float]]:
        """基于用户的推荐"""
        try:
            if self.user_similarity_matrix is None or user_id not in self.user_similarity_matrix.index:
                return []
            
            # 获取相似用户
            user_similarities = self.user_similarity_matrix.loc[user_id].sort_values(ascending=False)
            similar_users = user_similarities.iloc[1:11]  # 排除自己，取前10个相似用户
            
            # 获取用户已评分的物品
            user_ratings = self.user_item_matrix.loc[user_id]
            rated_items = user_ratings[user_ratings > 0].index.tolist()
            
            # 计算推荐分数
            recommendations = {}
            for similar_user_id, similarity in similar_users.items():
                similar_user_ratings = self.user_item_matrix.loc[similar_user_id]
                
                for item, rating in similar_user_ratings.items():
                    if item not in rated_items and rating > 0:
                        if item not in recommendations:
                            recommendations[item] = 0
                        recommendations[item] += similarity * rating
            
            # 排序并返回前N个推荐
            sorted_recommendations = sorted(recommendations.items(), key=lambda x: x[1], reverse=True)
            return sorted_recommendations[:n_recommendations]
            
        except Exception as e:
            logger.error(f"基于用户的推荐失败: {e}")
            return []
    
    def recommend_item_based(self, user_id: int, n_recommendations: int = 10) -> List[Tuple[str, float]]:
        """基于物品的推荐"""
        try:
            if self.item_similarity_matrix is None or user_id not in self.user_item_matrix.index:
                return []
            
            # 获取用户已评分的物品
            user_ratings = self.user_item_matrix.loc[user_id]
            rated_items = user_ratings[user_ratings > 0]
            
            # 计算推荐分数
            recommendations = {}
            for item, rating in rated_items.items():
                # 获取与该物品相似的物品
                similar_items = self.item_similarity_matrix.loc[item].sort_values(ascending=False)
                
                for similar_item, similarity in similar_items.items():
                    if similar_item not in rated_items.index and similarity > 0:
                        if similar_item not in recommendations:
                            recommendations[similar_item] = 0
                        recommendations[similar_item] += similarity * rating
            
            # 排序并返回前N个推荐
            sorted_recommendations = sorted(recommendations.items(), key=lambda x: x[1], reverse=True)
            return sorted_recommendations[:n_recommendations]
            
        except Exception as e:
            logger.error(f"基于物品的推荐失败: {e}")
            return []


class ContentBasedRecommender:
    """基于内容的推荐器"""
    
    def __init__(self):
        self.item_features = None
        self.feature_weights = None
    
    def fit(self, item_features: pd.DataFrame, user_preferences: Optional[Dict] = None):
        """训练基于内容的推荐器"""
        try:
            self.item_features = item_features.fillna(0)
            
            # 如果有用户偏好，计算特征权重
            if user_preferences:
                self.feature_weights = pd.Series(user_preferences)
            else:
                # 默认等权重
                self.feature_weights = pd.Series(1.0, index=self.item_features.columns)
            
            logger.info("基于内容的推荐器训练完成")
            
        except Exception as e:
            logger.error(f"训练基于内容的推荐器失败: {e}")
            raise
    
    def recommend(self, user_profile: Dict[str, float], n_recommendations: int = 10) -> List[Tuple[str, float]]:
        """基于内容推荐"""
        try:
            if self.item_features is None:
                return []
            
            # 计算用户画像与物品特征的相似度
            user_vector = pd.Series(user_profile).reindex(self.item_features.columns, fill_value=0)
            
            # 应用特征权重
            weighted_features = self.item_features * self.feature_weights
            
            # 计算余弦相似度
            similarities = []
            for item_id, item_features in weighted_features.iterrows():
                similarity = cosine_similarity([user_vector], [item_features])[0][0]
                similarities.append((item_id, similarity))
            
            # 排序并返回前N个推荐
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:n_recommendations]
            
        except Exception as e:
            logger.error(f"基于内容推荐失败: {e}")
            return []


class HybridRecommender:
    """混合推荐器"""
    
    def __init__(self):
        self.cf_recommender = CollaborativeFilteringRecommender()
        self.content_recommender = ContentBasedRecommender()
        self.weights = {"cf": 0.6, "content": 0.4}
    
    def fit(self, user_item_matrix: pd.DataFrame, item_features: pd.DataFrame):
        """训练混合推荐器"""
        try:
            # 训练协同过滤
            self.cf_recommender.fit_item_based(user_item_matrix)
            
            # 训练基于内容的推荐
            self.content_recommender.fit(item_features)
            
            logger.info("混合推荐器训练完成")
            
        except Exception as e:
            logger.error(f"训练混合推荐器失败: {e}")
            raise
    
    def recommend(
        self, 
        user_id: int, 
        user_profile: Dict[str, float],
        n_recommendations: int = 10
    ) -> List[Tuple[str, float]]:
        """混合推荐"""
        try:
            # 获取协同过滤推荐
            cf_recommendations = self.cf_recommender.recommend_item_based(user_id, n_recommendations * 2)
            cf_dict = dict(cf_recommendations)
            
            # 获取基于内容的推荐
            content_recommendations = self.content_recommender.recommend(user_profile, n_recommendations * 2)
            content_dict = dict(content_recommendations)
            
            # 合并推荐结果
            all_items = set(cf_dict.keys()) | set(content_dict.keys())
            hybrid_scores = {}
            
            for item in all_items:
                cf_score = cf_dict.get(item, 0)
                content_score = content_dict.get(item, 0)
                
                # 归一化分数
                cf_score_norm = cf_score / max(cf_dict.values()) if cf_dict else 0
                content_score_norm = content_score / max(content_dict.values()) if content_dict else 0
                
                # 加权合并
                hybrid_score = (self.weights["cf"] * cf_score_norm + 
                               self.weights["content"] * content_score_norm)
                hybrid_scores[item] = hybrid_score
            
            # 排序并返回前N个推荐
            sorted_recommendations = sorted(hybrid_scores.items(), key=lambda x: x[1], reverse=True)
            return sorted_recommendations[:n_recommendations]
            
        except Exception as e:
            logger.error(f"混合推荐失败: {e}")
            return []


class RecommendationService:
    """推荐服务"""
    
    def __init__(self):
        self.hybrid_recommender = HybridRecommender()
        self.strategy_recommender = None
        self.stock_recommender = None
    
    async def recommend_strategies(
        self, 
        user_id: int, 
        db: AsyncSession,
        n_recommendations: int = 10
    ) -> List[Dict[str, Any]]:
        """推荐策略"""
        try:
            # 获取用户画像
            user_profile = await self._get_user_profile(user_id, db)
            
            # 获取策略特征矩阵
            strategy_features = await self._get_strategy_features(db)
            
            # 获取用户-策略评分矩阵
            user_strategy_matrix = await self._get_user_strategy_matrix(db)
            
            if strategy_features.empty or user_strategy_matrix.empty:
                # 如果没有足够数据，返回热门策略
                return await self._get_popular_strategies(db, n_recommendations)
            
            # 训练推荐器
            self.hybrid_recommender.fit(user_strategy_matrix, strategy_features)
            
            # 获取推荐
            recommendations = self.hybrid_recommender.recommend(
                user_id, user_profile, n_recommendations
            )
            
            # 构建推荐结果
            result = []
            for strategy_id, score in recommendations:
                strategy_info = await self._get_strategy_info(int(strategy_id), db)
                if strategy_info:
                    result.append({
                        "strategy_id": int(strategy_id),
                        "strategy_name": strategy_info["name"],
                        "score": float(score),
                        "confidence": min(score, 1.0),
                        "reasons": self._generate_recommendation_reasons(strategy_info, user_profile),
                        "performance": strategy_info.get("performance", {}),
                        "risk_metrics": strategy_info.get("risk_metrics", {})
                    })
            
            return result
            
        except Exception as e:
            logger.error(f"推荐策略失败: {e}")
            return []
    
    async def recommend_stocks(
        self, 
        user_id: int, 
        db: AsyncSession,
        n_recommendations: int = 10
    ) -> List[Dict[str, Any]]:
        """推荐股票"""
        try:
            # 获取用户画像
            user_profile = await self._get_user_profile(user_id, db)
            
            # 基于用户偏好推荐股票
            # 这里应该结合技术指标、基本面数据、市场情绪等
            
            # 目前返回示例推荐
            recommendations = [
                {
                    "symbol": "000001.XSHE",
                    "name": "平安银行",
                    "score": 0.85,
                    "confidence": 0.8,
                    "reasons": ["技术指标良好", "基本面稳健", "符合用户风险偏好"],
                    "predicted_return": 0.12,
                    "risk_level": "medium"
                },
                {
                    "symbol": "000002.XSHE",
                    "name": "万科A",
                    "score": 0.78,
                    "confidence": 0.75,
                    "reasons": ["行业前景良好", "估值合理", "技术面支撑"],
                    "predicted_return": 0.08,
                    "risk_level": "low"
                }
            ]
            
            return recommendations[:n_recommendations]
            
        except Exception as e:
            logger.error(f"推荐股票失败: {e}")
            return []
    
    async def _get_user_profile(self, user_id: int, db: AsyncSession) -> Dict[str, float]:
        """获取用户画像"""
        try:
            # 从用户的历史行为中提取偏好
            # 这里应该分析用户的回测历史、策略偏好等
            
            # 目前返回默认画像
            return {
                "risk_tolerance": 0.6,      # 风险承受能力
                "return_expectation": 0.15,  # 收益期望
                "holding_period": 30,        # 持仓周期偏好（天）
                "sector_preference": 0.5,    # 行业偏好
                "technical_focus": 0.7,      # 技术分析偏好
                "fundamental_focus": 0.3,    # 基本面分析偏好
            }
            
        except Exception as e:
            logger.error(f"获取用户画像失败: {e}")
            return {}
    
    async def _get_strategy_features(self, db: AsyncSession) -> pd.DataFrame:
        """获取策略特征矩阵"""
        try:
            # 查询策略及其回测结果
            query = select(Strategy, BacktestResult).join(
                BacktestResult, Strategy.id == BacktestResult.task_id, isouter=True
            )
            result = await db.execute(query)
            strategies_results = result.all()
            
            features_data = []
            for strategy, backtest_result in strategies_results:
                if backtest_result:
                    features = {
                        "strategy_id": strategy.id,
                        "annual_return": float(backtest_result.annual_return or 0),
                        "max_drawdown": abs(float(backtest_result.max_drawdown or 0)),
                        "sharpe_ratio": float(backtest_result.sharpe_ratio or 0),
                        "win_rate": float(backtest_result.win_rate or 0),
                        "total_trades": int(backtest_result.total_trades or 0),
                        "volatility": float(backtest_result.volatility or 0),
                    }
                else:
                    # 默认特征
                    features = {
                        "strategy_id": strategy.id,
                        "annual_return": 0.0,
                        "max_drawdown": 0.0,
                        "sharpe_ratio": 0.0,
                        "win_rate": 0.0,
                        "total_trades": 0,
                        "volatility": 0.0,
                    }
                
                features_data.append(features)
            
            if features_data:
                df = pd.DataFrame(features_data)
                df.set_index("strategy_id", inplace=True)
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取策略特征矩阵失败: {e}")
            return pd.DataFrame()
    
    async def _get_user_strategy_matrix(self, db: AsyncSession) -> pd.DataFrame:
        """获取用户-策略评分矩阵"""
        try:
            # 这里应该基于用户的回测历史、收藏、评分等构建矩阵
            # 目前返回空矩阵
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取用户-策略矩阵失败: {e}")
            return pd.DataFrame()
    
    async def _get_popular_strategies(self, db: AsyncSession, n: int) -> List[Dict[str, Any]]:
        """获取热门策略"""
        try:
            query = (
                select(Strategy)
                .order_by(desc(Strategy.backtest_count))
                .limit(n)
            )
            result = await db.execute(query)
            strategies = result.scalars().all()
            
            popular_strategies = []
            for strategy in strategies:
                popular_strategies.append({
                    "strategy_id": strategy.id,
                    "strategy_name": strategy.name,
                    "score": 0.7,  # 默认分数
                    "confidence": 0.6,
                    "reasons": ["热门策略", "使用频率高"],
                    "performance": {
                        "backtest_count": strategy.backtest_count or 0
                    }
                })
            
            return popular_strategies
            
        except Exception as e:
            logger.error(f"获取热门策略失败: {e}")
            return []
    
    async def _get_strategy_info(self, strategy_id: int, db: AsyncSession) -> Optional[Dict[str, Any]]:
        """获取策略信息"""
        try:
            result = await db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
            strategy = result.scalar_one_or_none()
            
            if strategy:
                return {
                    "id": strategy.id,
                    "name": strategy.name,
                    "description": strategy.description,
                    "performance": {
                        "avg_return": float(strategy.avg_return or 0),
                        "max_drawdown": float(strategy.max_drawdown or 0),
                        "sharpe_ratio": float(strategy.sharpe_ratio or 0),
                    }
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取策略信息失败: {e}")
            return None
    
    def _generate_recommendation_reasons(
        self, 
        strategy_info: Dict[str, Any], 
        user_profile: Dict[str, float]
    ) -> List[str]:
        """生成推荐理由"""
        reasons = []
        
        try:
            performance = strategy_info.get("performance", {})
            
            # 基于性能指标生成理由
            if performance.get("sharpe_ratio", 0) > 1.0:
                reasons.append("夏普比率优秀")
            
            if performance.get("max_drawdown", 0) < 0.1:
                reasons.append("回撤控制良好")
            
            if performance.get("avg_return", 0) > 0.1:
                reasons.append("历史收益表现良好")
            
            # 基于用户偏好生成理由
            if user_profile.get("risk_tolerance", 0.5) < 0.3 and performance.get("max_drawdown", 0) < 0.05:
                reasons.append("符合您的低风险偏好")
            
            if user_profile.get("return_expectation", 0.1) < performance.get("avg_return", 0):
                reasons.append("预期收益符合您的要求")
            
            if not reasons:
                reasons.append("综合评分较高")
            
            return reasons
            
        except Exception as e:
            logger.error(f"生成推荐理由失败: {e}")
            return ["推荐算法匹配"]


# 全局推荐服务实例
recommendation_service = RecommendationService()
