"""
知识图谱相关API端点

提供图构建、分析、GNN训练等知识图谱功能的API接口
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.knowledge_graph import (
    KnowledgeGraph, FinancialEntity, EntityRelation, GraphAnalysis, GNNModel
)
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.knowledge_graph_service import knowledge_graph_service

router = APIRouter()


# =============================================================================
# Pydantic模型
# =============================================================================

class KnowledgeGraphRequest(BaseModel):
    """知识图谱创建请求"""
    name: str = Field(..., description="图谱名称")
    description: Optional[str] = Field(None, description="图谱描述")
    graph_type: str = Field("financial", description="图谱类型")
    domain: str = Field("finance", description="领域")
    
    # 数据源配置
    include_stocks: bool = Field(True, description="包含股票数据")
    include_companies: bool = Field(False, description="包含公司数据")
    include_relations: bool = Field(True, description="包含关系数据")
    
    # 实体提取配置
    entity_extraction: Optional[Dict[str, Any]] = Field(None, description="实体提取配置")
    relation_extraction: Optional[Dict[str, Any]] = Field(None, description="关系提取配置")
    
    # 构建配置
    quality_threshold: float = Field(0.8, ge=0.0, le=1.0, description="质量阈值")
    update_strategy: str = Field("incremental", description="更新策略")


class GraphAnalysisRequest(BaseModel):
    """图分析请求"""
    name: str = Field(..., description="分析名称")
    analysis_type: str = Field("comprehensive", description="分析类型")
    description: Optional[str] = Field(None, description="分析描述")
    
    # 分析配置
    calculate_basic_metrics: bool = Field(True, description="计算基本指标")
    calculate_centrality: bool = Field(True, description="计算中心性")
    detect_communities: bool = Field(True, description="社区发现")
    analyze_paths: bool = Field(False, description="路径分析")
    
    # 社区发现配置
    community_method: str = Field("louvain", description="社区发现方法")
    
    # 路径分析配置
    source_nodes: Optional[List[int]] = Field(None, description="源节点")
    target_nodes: Optional[List[int]] = Field(None, description="目标节点")


class GNNTrainingRequest(BaseModel):
    """GNN训练请求"""
    model_name: str = Field(..., description="模型名称")
    description: Optional[str] = Field(None, description="模型描述")
    
    # 模型配置
    architecture: str = Field("gcn", description="模型架构", regex="^(gcn|gat|sage)$")
    task_type: str = Field("node_classification", description="任务类型")
    target_column: Optional[str] = Field(None, description="目标列")
    
    # 架构配置
    hidden_dim: int = Field(64, ge=16, le=512, description="隐藏层维度")
    num_layers: int = Field(2, ge=1, le=10, description="层数")
    dropout: float = Field(0.5, ge=0.0, le=1.0, description="Dropout率")
    heads: int = Field(8, ge=1, le=16, description="注意力头数（GAT）")
    
    # 训练配置
    learning_rate: float = Field(0.01, ge=0.0001, le=0.1, description="学习率")
    epochs: int = Field(200, ge=10, le=1000, description="训练轮数")
    train_ratio: float = Field(0.6, ge=0.1, le=0.9, description="训练集比例")
    val_ratio: float = Field(0.2, ge=0.1, le=0.5, description="验证集比例")
    patience: int = Field(20, ge=5, le=100, description="早停耐心")


class EntitySimilarityRequest(BaseModel):
    """实体相似性请求"""
    entity_id: int = Field(..., description="目标实体ID")
    similarity_method: str = Field("cosine", description="相似性方法")
    top_k: int = Field(10, ge=1, le=100, description="返回前K个相似实体")


class GNNPredictionRequest(BaseModel):
    """GNN预测请求"""
    model_id: int = Field(..., description="模型ID")
    entities: List[int] = Field(..., description="预测实体ID列表")
    prediction_type: str = Field("node_classification", description="预测类型")


# =============================================================================
# 知识图谱管理
# =============================================================================

@router.post("/graphs", response_model=BaseResponse[dict])
async def create_knowledge_graph(
    request: KnowledgeGraphRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建知识图谱"""
    try:
        # 构建图谱配置
        graph_config = {
            'name': request.name,
            'description': request.description,
            'type': request.graph_type,
            'domain': request.domain,
            'include_stocks': request.include_stocks,
            'include_companies': request.include_companies,
            'include_relations': request.include_relations,
            'quality_threshold': request.quality_threshold,
            'update_strategy': request.update_strategy,
            'entity_extraction': request.entity_extraction or {
                'stock': {'data_source': 'jqdata', 'confidence': 0.9},
                'company': {'data_source': 'manual', 'confidence': 0.8}
            },
            'relation_extraction': request.relation_extraction or {
                'correlation': {'correlation_threshold': 0.5, 'confidence': 0.8},
                'partnership': {'confidence': 0.6},
                'competition': {'confidence': 0.6}
            }
        }
        
        # 创建知识图谱
        result = await knowledge_graph_service.create_knowledge_graph(
            current_user.id, graph_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="知识图谱创建成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建知识图谱失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建知识图谱失败"
        )


@router.get("/graphs", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_knowledge_graphs(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    graph_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取知识图谱列表"""
    try:
        # 构建查询条件
        conditions = [KnowledgeGraph.user_id == current_user.id]
        if graph_type:
            conditions.append(KnowledgeGraph.graph_type == graph_type)
        if status:
            conditions.append(KnowledgeGraph.status == status)
        
        # 查询总数
        count_query = select(func.count(KnowledgeGraph.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(KnowledgeGraph)
            .where(and_(*conditions))
            .order_by(desc(KnowledgeGraph.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        graphs = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        graph_list = []
        for graph in graphs:
            graph_list.append({
                "id": graph.id,
                "name": graph.graph_name,
                "description": graph.description,
                "graph_type": graph.graph_type,
                "domain": graph.domain,
                "status": graph.status,
                "entity_count": graph.entity_count,
                "relation_count": graph.relation_count,
                "completeness_score": float(graph.completeness_score) if graph.completeness_score else None,
                "consistency_score": float(graph.consistency_score) if graph.consistency_score else None,
                "accuracy_score": float(graph.accuracy_score) if graph.accuracy_score else None,
                "created_at": graph.created_at.isoformat(),
                "last_build_time": graph.last_build_time.isoformat() if graph.last_build_time else None
            })
        
        return BaseResponse(
            code=200,
            message="获取知识图谱列表成功",
            data=PaginatedResponse(
                items=graph_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取知识图谱列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识图谱列表失败"
        )


@router.get("/graphs/{graph_id}", response_model=BaseResponse[dict])
async def get_knowledge_graph(
    graph_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取知识图谱详情"""
    try:
        result = await knowledge_graph_service.get_graph_statistics(
            current_user.id, graph_id, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="获取知识图谱详情成功",
                data=result['statistics']
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识图谱详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取知识图谱详情失败"
        )


@router.delete("/graphs/{graph_id}", response_model=BaseResponse[dict])
async def delete_knowledge_graph(
    graph_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """删除知识图谱"""
    try:
        # 检查图谱是否存在且属于当前用户
        result = await db.execute(
            select(KnowledgeGraph).where(
                and_(
                    KnowledgeGraph.id == graph_id,
                    KnowledgeGraph.user_id == current_user.id
                )
            )
        )
        knowledge_graph = result.scalar_one_or_none()
        
        if not knowledge_graph:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识图谱不存在"
            )
        
        # 删除图谱（级联删除相关数据）
        await db.delete(knowledge_graph)
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="知识图谱删除成功",
            data={"graph_id": graph_id}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除知识图谱失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除知识图谱失败"
        )


# =============================================================================
# 图分析
# =============================================================================

@router.post("/graphs/{graph_id}/analyze", response_model=BaseResponse[dict])
async def analyze_knowledge_graph(
    graph_id: int,
    request: GraphAnalysisRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """分析知识图谱"""
    try:
        # 构建分析配置
        analysis_config = {
            'name': request.name,
            'type': request.analysis_type,
            'description': request.description,
            'calculate_basic_metrics': request.calculate_basic_metrics,
            'calculate_centrality': request.calculate_centrality,
            'detect_communities': request.detect_communities,
            'analyze_paths': request.analyze_paths,
            'community_method': request.community_method,
            'source_nodes': request.source_nodes or [],
            'target_nodes': request.target_nodes or []
        }
        
        # 执行分析
        result = await knowledge_graph_service.analyze_knowledge_graph(
            current_user.id, graph_id, analysis_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="知识图谱分析成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"知识图谱分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="知识图谱分析失败"
        )


@router.get("/graphs/{graph_id}/analyses", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_graph_analyses(
    graph_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取图分析列表"""
    try:
        # 查询总数
        count_query = select(func.count(GraphAnalysis.id)).where(
            and_(
                GraphAnalysis.knowledge_graph_id == graph_id,
                GraphAnalysis.user_id == current_user.id
            )
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(GraphAnalysis)
            .where(
                and_(
                    GraphAnalysis.knowledge_graph_id == graph_id,
                    GraphAnalysis.user_id == current_user.id
                )
            )
            .order_by(desc(GraphAnalysis.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        analyses = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        analysis_list = []
        for analysis in analyses:
            analysis_list.append({
                "id": analysis.id,
                "name": analysis.analysis_name,
                "analysis_type": analysis.analysis_type,
                "description": analysis.description,
                "status": analysis.status,
                "algorithms_used": analysis.algorithms_used,
                "computation_time_seconds": float(analysis.computation_time_seconds) if analysis.computation_time_seconds else None,
                "created_at": analysis.created_at.isoformat(),
                "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None
            })
        
        return BaseResponse(
            code=200,
            message="获取图分析列表成功",
            data=PaginatedResponse(
                items=analysis_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取图分析列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取图分析列表失败"
        )


# =============================================================================
# GNN模型
# =============================================================================

@router.post("/graphs/{graph_id}/gnn/train", response_model=BaseResponse[dict])
async def train_gnn_model(
    graph_id: int,
    request: GNNTrainingRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """训练GNN模型"""
    try:
        # 构建模型配置
        model_config = {
            'name': request.model_name,
            'description': request.description,
            'architecture': request.architecture,
            'task_type': request.task_type,
            'target_column': request.target_column,
            'layer_config': {
                'hidden_dim': request.hidden_dim,
                'num_layers': request.num_layers,
                'dropout': request.dropout,
                'heads': request.heads
            }
        }
        
        # 构建训练配置
        training_config = {
            'learning_rate': request.learning_rate,
            'epochs': request.epochs,
            'train_ratio': request.train_ratio,
            'val_ratio': request.val_ratio,
            'patience': request.patience
        }
        
        # 训练模型
        result = await knowledge_graph_service.train_gnn_on_graph(
            current_user.id, graph_id, model_config, training_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="GNN模型训练成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"GNN模型训练失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="GNN模型训练失败"
        )


@router.get("/gnn/models", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_gnn_models(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    graph_id: Optional[int] = Query(None),
    architecture: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取GNN模型列表"""
    try:
        # 构建查询条件
        conditions = [GNNModel.user_id == current_user.id]
        if graph_id:
            conditions.append(GNNModel.knowledge_graph_id == graph_id)
        if architecture:
            conditions.append(GNNModel.architecture == architecture)
        
        # 查询总数
        count_query = select(func.count(GNNModel.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(GNNModel)
            .where(and_(*conditions))
            .order_by(desc(GNNModel.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        models = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "name": model.model_name,
                "architecture": model.architecture,
                "model_type": model.model_type,
                "description": model.description,
                "status": model.status,
                "knowledge_graph_id": model.knowledge_graph_id,
                "training_epochs": model.training_epochs,
                "test_metrics": model.test_metrics,
                "is_deployed": model.is_deployed,
                "created_at": model.created_at.isoformat(),
                "trained_at": model.trained_at.isoformat() if model.trained_at else None
            })
        
        return BaseResponse(
            code=200,
            message="获取GNN模型列表成功",
            data=PaginatedResponse(
                items=model_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取GNN模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取GNN模型列表失败"
        )


# =============================================================================
# 实体相似性
# =============================================================================

@router.post("/entities/similarity", response_model=BaseResponse[dict])
async def find_similar_entities(
    request: EntitySimilarityRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """寻找相似实体"""
    try:
        similarity_config = {
            'method': request.similarity_method,
            'top_k': request.top_k
        }
        
        result = await knowledge_graph_service.find_similar_entities(
            current_user.id, request.entity_id, similarity_config, db
        )
        
        if result['success']:
            return BaseResponse(
                code=200,
                message="相似实体查找成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"相似实体查找失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="相似实体查找失败"
        )
