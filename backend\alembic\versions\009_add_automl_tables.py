"""add automl tables

Revision ID: 009
Revises: 008
Create Date: 2024-12-22 23:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建AutoML实验表
    op.create_table('automl_experiments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('experiment_type', sa.String(length=50), nullable=False),
        sa.Column('dataset_source', sa.String(length=100), nullable=True),
        sa.Column('dataset_config', sa.J<PERSON>(), nullable=True),
        sa.Column('target_column', sa.String(length=100), nullable=False),
        sa.Column('feature_columns', sa.JSON(), nullable=True),
        sa.Column('problem_type', sa.String(length=50), nullable=True),
        sa.Column('evaluation_metric', sa.String(length=50), nullable=True),
        sa.Column('cross_validation_folds', sa.Integer(), nullable=True),
        sa.Column('test_size', sa.Float(), nullable=True),
        sa.Column('random_state', sa.Integer(), nullable=True),
        sa.Column('max_runtime_minutes', sa.Integer(), nullable=True),
        sa.Column('max_models', sa.Integer(), nullable=True),
        sa.Column('early_stopping_rounds', sa.Integer(), nullable=True),
        sa.Column('enable_feature_selection', sa.Boolean(), nullable=True),
        sa.Column('enable_hyperparameter_tuning', sa.Boolean(), nullable=True),
        sa.Column('enable_ensemble', sa.Boolean(), nullable=True),
        sa.Column('enable_stacking', sa.Boolean(), nullable=True),
        sa.Column('included_algorithms', sa.JSON(), nullable=True),
        sa.Column('excluded_algorithms', sa.JSON(), nullable=True),
        sa.Column('enable_feature_engineering', sa.Boolean(), nullable=True),
        sa.Column('max_feature_interactions', sa.Integer(), nullable=True),
        sa.Column('enable_polynomial_features', sa.Boolean(), nullable=True),
        sa.Column('enable_text_features', sa.Boolean(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('current_stage', sa.String(length=50), nullable=True),
        sa.Column('total_models_trained', sa.Integer(), nullable=True),
        sa.Column('best_score', sa.Float(), nullable=True),
        sa.Column('best_model_id', sa.Integer(), nullable=True),
        sa.Column('cpu_usage_hours', sa.Float(), nullable=True),
        sa.Column('memory_usage_gb', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_automl_experiments_id'), 'automl_experiments', ['id'], unique=False)
    op.create_index(op.f('ix_automl_experiments_user_id'), 'automl_experiments', ['user_id'], unique=False)

    # 创建AutoML模型表
    op.create_table('automl_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('experiment_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('model_name', sa.String(length=100), nullable=False),
        sa.Column('algorithm', sa.String(length=50), nullable=False),
        sa.Column('model_type', sa.String(length=50), nullable=True),
        sa.Column('hyperparameters', sa.JSON(), nullable=True),
        sa.Column('feature_list', sa.JSON(), nullable=True),
        sa.Column('preprocessing_steps', sa.JSON(), nullable=True),
        sa.Column('training_time_seconds', sa.Float(), nullable=True),
        sa.Column('training_samples', sa.Integer(), nullable=True),
        sa.Column('validation_samples', sa.Integer(), nullable=True),
        sa.Column('train_score', sa.Float(), nullable=True),
        sa.Column('validation_score', sa.Float(), nullable=True),
        sa.Column('test_score', sa.Float(), nullable=True),
        sa.Column('cross_validation_score', sa.Float(), nullable=True),
        sa.Column('cross_validation_std', sa.Float(), nullable=True),
        sa.Column('performance_metrics', sa.JSON(), nullable=True),
        sa.Column('feature_importance', sa.JSON(), nullable=True),
        sa.Column('confusion_matrix', sa.JSON(), nullable=True),
        sa.Column('model_size_mb', sa.Float(), nullable=True),
        sa.Column('prediction_time_ms', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('model_path', sa.String(length=500), nullable=True),
        sa.Column('model_binary', sa.LargeBinary(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_best_model', sa.Boolean(), nullable=True),
        sa.Column('is_deployed', sa.Boolean(), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('parent_model_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['experiment_id'], ['automl_experiments.id'], ),
        sa.ForeignKeyConstraint(['parent_model_id'], ['automl_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_automl_models_experiment_id'), 'automl_models', ['experiment_id'], unique=False)
    op.create_index(op.f('ix_automl_models_id'), 'automl_models', ['id'], unique=False)
    op.create_index(op.f('ix_automl_models_user_id'), 'automl_models', ['user_id'], unique=False)

    # 创建特征选择表
    op.create_table('feature_selections',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('experiment_id', sa.Integer(), nullable=False),
        sa.Column('selection_method', sa.String(length=50), nullable=False),
        sa.Column('selection_config', sa.JSON(), nullable=True),
        sa.Column('original_features', sa.JSON(), nullable=True),
        sa.Column('original_feature_count', sa.Integer(), nullable=True),
        sa.Column('selected_features', sa.JSON(), nullable=True),
        sa.Column('selected_feature_count', sa.Integer(), nullable=True),
        sa.Column('feature_scores', sa.JSON(), nullable=True),
        sa.Column('feature_rankings', sa.JSON(), nullable=True),
        sa.Column('baseline_score', sa.Float(), nullable=True),
        sa.Column('selected_score', sa.Float(), nullable=True),
        sa.Column('score_improvement', sa.Float(), nullable=True),
        sa.Column('selection_time_seconds', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('engineered_features', sa.JSON(), nullable=True),
        sa.Column('feature_interactions', sa.JSON(), nullable=True),
        sa.Column('polynomial_features', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['experiment_id'], ['automl_experiments.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_feature_selections_experiment_id'), 'feature_selections', ['experiment_id'], unique=False)
    op.create_index(op.f('ix_feature_selections_id'), 'feature_selections', ['id'], unique=False)

    # 创建超参数调优表
    op.create_table('hyperparameter_tunings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('experiment_id', sa.Integer(), nullable=False),
        sa.Column('algorithm', sa.String(length=50), nullable=False),
        sa.Column('tuning_method', sa.String(length=50), nullable=False),
        sa.Column('tuning_config', sa.JSON(), nullable=True),
        sa.Column('search_space', sa.JSON(), nullable=True),
        sa.Column('parameter_bounds', sa.JSON(), nullable=True),
        sa.Column('total_trials', sa.Integer(), nullable=True),
        sa.Column('completed_trials', sa.Integer(), nullable=True),
        sa.Column('failed_trials', sa.Integer(), nullable=True),
        sa.Column('best_parameters', sa.JSON(), nullable=True),
        sa.Column('best_score', sa.Float(), nullable=True),
        sa.Column('best_trial_number', sa.Integer(), nullable=True),
        sa.Column('trial_history', sa.JSON(), nullable=True),
        sa.Column('convergence_history', sa.JSON(), nullable=True),
        sa.Column('parameter_importance', sa.JSON(), nullable=True),
        sa.Column('tuning_time_seconds', sa.Float(), nullable=True),
        sa.Column('average_trial_time', sa.Float(), nullable=True),
        sa.Column('best_trial_time', sa.Float(), nullable=True),
        sa.Column('early_stopping_triggered', sa.Boolean(), nullable=True),
        sa.Column('early_stopping_round', sa.Integer(), nullable=True),
        sa.Column('patience_counter', sa.Integer(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['experiment_id'], ['automl_experiments.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_hyperparameter_tunings_experiment_id'), 'hyperparameter_tunings', ['experiment_id'], unique=False)
    op.create_index(op.f('ix_hyperparameter_tunings_id'), 'hyperparameter_tunings', ['id'], unique=False)

    # 创建AutoML预测表
    op.create_table('automl_predictions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('prediction_name', sa.String(length=100), nullable=True),
        sa.Column('prediction_type', sa.String(length=50), nullable=True),
        sa.Column('input_data', sa.JSON(), nullable=True),
        sa.Column('input_features', sa.JSON(), nullable=True),
        sa.Column('data_source', sa.String(length=100), nullable=True),
        sa.Column('predictions', sa.JSON(), nullable=True),
        sa.Column('prediction_probabilities', sa.JSON(), nullable=True),
        sa.Column('confidence_intervals', sa.JSON(), nullable=True),
        sa.Column('prediction_count', sa.Integer(), nullable=True),
        sa.Column('prediction_time_ms', sa.Float(), nullable=True),
        sa.Column('average_confidence', sa.Float(), nullable=True),
        sa.Column('actual_values', sa.JSON(), nullable=True),
        sa.Column('prediction_errors', sa.JSON(), nullable=True),
        sa.Column('accuracy_metrics', sa.JSON(), nullable=True),
        sa.Column('market_conditions', sa.JSON(), nullable=True),
        sa.Column('external_factors', sa.JSON(), nullable=True),
        sa.Column('prediction_context', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_validated', sa.Boolean(), nullable=True),
        sa.Column('validation_score', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('prediction_date', sa.DateTime(), nullable=True),
        sa.Column('validation_date', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['automl_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_automl_predictions_id'), 'automl_predictions', ['id'], unique=False)
    op.create_index(op.f('ix_automl_predictions_model_id'), 'automl_predictions', ['model_id'], unique=False)
    op.create_index(op.f('ix_automl_predictions_user_id'), 'automl_predictions', ['user_id'], unique=False)

    # 创建模型集成表
    op.create_table('model_ensembles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('experiment_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('ensemble_name', sa.String(length=100), nullable=False),
        sa.Column('ensemble_method', sa.String(length=50), nullable=False),
        sa.Column('ensemble_config', sa.JSON(), nullable=True),
        sa.Column('base_model_ids', sa.JSON(), nullable=True),
        sa.Column('model_weights', sa.JSON(), nullable=True),
        sa.Column('model_selection_criteria', sa.String(length=50), nullable=True),
        sa.Column('voting_strategy', sa.String(length=20), nullable=True),
        sa.Column('aggregation_method', sa.String(length=20), nullable=True),
        sa.Column('stacking_meta_learner', sa.String(length=50), nullable=True),
        sa.Column('ensemble_score', sa.Float(), nullable=True),
        sa.Column('individual_scores', sa.JSON(), nullable=True),
        sa.Column('score_improvement', sa.Float(), nullable=True),
        sa.Column('model_diversity', sa.Float(), nullable=True),
        sa.Column('correlation_matrix', sa.JSON(), nullable=True),
        sa.Column('disagreement_measure', sa.Float(), nullable=True),
        sa.Column('training_time_seconds', sa.Float(), nullable=True),
        sa.Column('ensemble_size_mb', sa.Float(), nullable=True),
        sa.Column('prediction_time_ms', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_deployed', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['experiment_id'], ['automl_experiments.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_ensembles_experiment_id'), 'model_ensembles', ['experiment_id'], unique=False)
    op.create_index(op.f('ix_model_ensembles_id'), 'model_ensembles', ['id'], unique=False)
    op.create_index(op.f('ix_model_ensembles_user_id'), 'model_ensembles', ['user_id'], unique=False)

    # 创建AutoML流水线表
    op.create_table('automl_pipelines',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('pipeline_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('pipeline_type', sa.String(length=50), nullable=True),
        sa.Column('pipeline_config', sa.JSON(), nullable=True),
        sa.Column('execution_schedule', sa.String(length=100), nullable=True),
        sa.Column('pipeline_steps', sa.JSON(), nullable=True),
        sa.Column('step_dependencies', sa.JSON(), nullable=True),
        sa.Column('input_sources', sa.JSON(), nullable=True),
        sa.Column('output_destinations', sa.JSON(), nullable=True),
        sa.Column('data_validation_rules', sa.JSON(), nullable=True),
        sa.Column('model_registry', sa.JSON(), nullable=True),
        sa.Column('model_versioning', sa.JSON(), nullable=True),
        sa.Column('deployment_config', sa.JSON(), nullable=True),
        sa.Column('monitoring_config', sa.JSON(), nullable=True),
        sa.Column('alert_rules', sa.JSON(), nullable=True),
        sa.Column('performance_thresholds', sa.JSON(), nullable=True),
        sa.Column('total_executions', sa.Integer(), nullable=True),
        sa.Column('successful_executions', sa.Integer(), nullable=True),
        sa.Column('failed_executions', sa.Integer(), nullable=True),
        sa.Column('last_execution_status', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_scheduled', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('last_executed_at', sa.DateTime(), nullable=True),
        sa.Column('next_execution_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_automl_pipelines_id'), 'automl_pipelines', ['id'], unique=False)
    op.create_index(op.f('ix_automl_pipelines_user_id'), 'automl_pipelines', ['user_id'], unique=False)

    # 创建流水线执行表
    op.create_table('pipeline_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('pipeline_id', sa.Integer(), nullable=False),
        sa.Column('execution_id', sa.String(length=100), nullable=False),
        sa.Column('trigger_type', sa.String(length=50), nullable=True),
        sa.Column('trigger_user_id', sa.Integer(), nullable=True),
        sa.Column('execution_config', sa.JSON(), nullable=True),
        sa.Column('runtime_parameters', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('current_step', sa.String(length=100), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('step_results', sa.JSON(), nullable=True),
        sa.Column('output_artifacts', sa.JSON(), nullable=True),
        sa.Column('execution_logs', sa.Text(), nullable=True),
        sa.Column('execution_time_seconds', sa.Float(), nullable=True),
        sa.Column('cpu_usage', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_stack_trace', sa.Text(), nullable=True),
        sa.Column('failed_step', sa.String(length=100), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['pipeline_id'], ['automl_pipelines.id'], ),
        sa.ForeignKeyConstraint(['trigger_user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('execution_id')
    )
    op.create_index(op.f('ix_pipeline_executions_id'), 'pipeline_executions', ['id'], unique=False)
    op.create_index(op.f('ix_pipeline_executions_pipeline_id'), 'pipeline_executions', ['pipeline_id'], unique=False)

    # 添加外键约束
    op.create_foreign_key(None, 'automl_experiments', 'automl_models', ['best_model_id'], ['id'])


def downgrade() -> None:
    # 删除外键约束
    op.drop_constraint(None, 'automl_experiments', type_='foreignkey')
    
    # 删除表
    op.drop_index(op.f('ix_pipeline_executions_pipeline_id'), table_name='pipeline_executions')
    op.drop_index(op.f('ix_pipeline_executions_id'), table_name='pipeline_executions')
    op.drop_table('pipeline_executions')
    
    op.drop_index(op.f('ix_automl_pipelines_user_id'), table_name='automl_pipelines')
    op.drop_index(op.f('ix_automl_pipelines_id'), table_name='automl_pipelines')
    op.drop_table('automl_pipelines')
    
    op.drop_index(op.f('ix_model_ensembles_user_id'), table_name='model_ensembles')
    op.drop_index(op.f('ix_model_ensembles_id'), table_name='model_ensembles')
    op.drop_index(op.f('ix_model_ensembles_experiment_id'), table_name='model_ensembles')
    op.drop_table('model_ensembles')
    
    op.drop_index(op.f('ix_automl_predictions_user_id'), table_name='automl_predictions')
    op.drop_index(op.f('ix_automl_predictions_model_id'), table_name='automl_predictions')
    op.drop_index(op.f('ix_automl_predictions_id'), table_name='automl_predictions')
    op.drop_table('automl_predictions')
    
    op.drop_index(op.f('ix_hyperparameter_tunings_id'), table_name='hyperparameter_tunings')
    op.drop_index(op.f('ix_hyperparameter_tunings_experiment_id'), table_name='hyperparameter_tunings')
    op.drop_table('hyperparameter_tunings')
    
    op.drop_index(op.f('ix_feature_selections_id'), table_name='feature_selections')
    op.drop_index(op.f('ix_feature_selections_experiment_id'), table_name='feature_selections')
    op.drop_table('feature_selections')
    
    op.drop_index(op.f('ix_automl_models_user_id'), table_name='automl_models')
    op.drop_index(op.f('ix_automl_models_id'), table_name='automl_models')
    op.drop_index(op.f('ix_automl_models_experiment_id'), table_name='automl_models')
    op.drop_table('automl_models')
    
    op.drop_index(op.f('ix_automl_experiments_user_id'), table_name='automl_experiments')
    op.drop_index(op.f('ix_automl_experiments_id'), table_name='automl_experiments')
    op.drop_table('automl_experiments')
