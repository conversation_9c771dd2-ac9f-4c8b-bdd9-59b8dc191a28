"""
系统相关数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import Base


class SystemLog(Base):
    """系统日志模型"""
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False, index=True)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    logger_name = Column(String(100), nullable=False, index=True)
    message = Column(Text, nullable=False)
    
    # 上下文信息
    module = Column(String(100))  # 模块名
    function = Column(String(100))  # 函数名
    line_number = Column(Integer)  # 行号
    
    # 用户信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    session_id = Column(String(100))  # 会话ID
    request_id = Column(String(100))  # 请求ID
    
    # 额外数据
    extra_data = Column(JSON)  # 额外的上下文数据
    stack_trace = Column(Text)  # 堆栈跟踪（错误时）
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # 关系 - 暂时注释掉，等用户模型中的关系启用后再启用
    # user = relationship("User", back_populates="system_logs")


class ApiUsage(Base):
    """API使用统计模型"""
    __tablename__ = "api_usage"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # API信息
    endpoint = Column(String(200), nullable=False, index=True)
    method = Column(String(10), nullable=False)  # GET, POST, PUT, DELETE
    status_code = Column(Integer, nullable=False, index=True)
    
    # 性能指标
    response_time_ms = Column(Float, nullable=False)  # 响应时间（毫秒）
    request_size_bytes = Column(Integer, default=0)  # 请求大小
    response_size_bytes = Column(Integer, default=0)  # 响应大小
    
    # 客户端信息
    ip_address = Column(String(45))  # IPv4/IPv6地址
    user_agent = Column(String(500))  # 用户代理
    referer = Column(String(500))  # 来源页面
    
    # 请求详情
    query_params = Column(JSON)  # 查询参数
    request_headers = Column(JSON)  # 请求头
    response_headers = Column(JSON)  # 响应头
    
    # 错误信息
    error_message = Column(Text)  # 错误消息
    error_type = Column(String(100))  # 错误类型
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # 关系 - 暂时注释掉，等用户模型中的关系启用后再启用
    # user = relationship("User", back_populates="api_usage")


class CacheRecord(Base):
    """缓存记录模型"""
    __tablename__ = "cache_records"

    id = Column(Integer, primary_key=True, index=True)
    cache_key = Column(String(500), nullable=False, unique=True, index=True)
    cache_type = Column(String(50), nullable=False, index=True)  # redis, memory, file
    
    # 缓存内容
    data_size_bytes = Column(Integer, default=0)  # 数据大小
    data_type = Column(String(50))  # 数据类型
    compression = Column(String(20))  # 压缩方式
    
    # 访问统计
    hit_count = Column(Integer, default=0)  # 命中次数
    miss_count = Column(Integer, default=0)  # 未命中次数
    last_accessed_at = Column(DateTime)  # 最后访问时间
    
    # 生命周期
    ttl_seconds = Column(Integer)  # 生存时间（秒）
    expires_at = Column(DateTime)  # 过期时间
    is_expired = Column(Boolean, default=False, index=True)
    
    # 元数据
    tags = Column(JSON)  # 标签列表
    extra_metadata = Column(JSON)  # 额外元数据
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TaskQueue(Base):
    """任务队列模型"""
    __tablename__ = "task_queue"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), nullable=False, unique=True, index=True)
    task_name = Column(String(100), nullable=False, index=True)
    
    # 任务信息
    status = Column(String(20), default="pending", index=True)  # pending, running, completed, failed, cancelled
    priority = Column(Integer, default=0, index=True)  # 优先级，数字越大优先级越高
    
    # 任务参数
    args = Column(JSON)  # 位置参数
    kwargs = Column(JSON)  # 关键字参数
    
    # 执行信息
    worker_id = Column(String(100))  # 执行的工作进程ID
    started_at = Column(DateTime)  # 开始执行时间
    completed_at = Column(DateTime)  # 完成时间
    
    # 结果和错误
    result = Column(JSON)  # 执行结果
    error_message = Column(Text)  # 错误消息
    traceback = Column(Text)  # 错误堆栈
    
    # 重试机制
    max_retries = Column(Integer, default=3)  # 最大重试次数
    retry_count = Column(Integer, default=0)  # 当前重试次数
    retry_delay_seconds = Column(Integer, default=60)  # 重试延迟
    next_retry_at = Column(DateTime)  # 下次重试时间
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SystemMetrics(Base):
    """系统指标模型"""
    __tablename__ = "system_metrics"

    id = Column(Integer, primary_key=True, index=True)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_type = Column(String(20), nullable=False)  # counter, gauge, histogram
    
    # 指标值
    value = Column(Float, nullable=False)
    unit = Column(String(20))  # 单位
    
    # 标签和维度
    labels = Column(JSON)  # 标签键值对
    dimensions = Column(JSON)  # 维度信息
    
    # 聚合信息
    aggregation_period = Column(String(20))  # 聚合周期：minute, hour, day
    sample_count = Column(Integer, default=1)  # 样本数量
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
