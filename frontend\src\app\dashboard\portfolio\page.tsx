'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Button, 
  Space, 
  Statistic, 
  Table, 
  Tag, 
  Progress, 
  Spin,
  Empty,
  Tooltip,
  Badge,
  Divider
} from 'antd';
import { 
  WalletOutlined,
  PieChartOutlined,
  RiseOutlined,
  FundOutlined,
  RightOutlined,
  PlusOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  FallOutlined,
  StockOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

interface Position {
  code: string;
  name: string;
  shares: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  pnl: number;
  pnlPercent: number;
  weight: number;
}

export default function PortfolioPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [positions, setPositions] = useState<Position[]>([]);

  // 模拟投资组合数据
  const [portfolioData] = useState({
    totalValue: 1250000,
    todayPnL: 15600,
    todayPnLPercent: 1.26,
    totalPnL: 250000,
    totalPnLPercent: 25.0,
    cash: 150000,
    positionCount: 12
  });

  // 获取持仓数据
  useEffect(() => {
    const fetchPositions = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/v1/portfolio/positions', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          if (result.code === 200 && result.data?.items) {
            const positionsData = result.data.items.map((item: any) => ({
              code: item.code,
              name: item.name,
              shares: item.shares,
              avgPrice: item.avg_cost,
              currentPrice: item.current_price,
              marketValue: item.market_value,
              pnl: item.pnl,
              pnlPercent: item.pnl_percent,
              weight: item.weight
            }));
            setPositions(positionsData);
          }
        }
      } catch (error) {
        console.error('获取持仓数据失败:', error);
        // 如果API失败，使用模拟数据
        setPositions([
          {
            code: '000001.XSHE',
            name: '平安银行',
            shares: 10000,
            avgPrice: 11.50,
            currentPrice: 12.45,
            marketValue: 124500,
            pnl: 9500,
            pnlPercent: 8.26,
            weight: 9.96
          },
          {
            code: '600036.XSHG',
            name: '招商银行',
            shares: 5000,
            avgPrice: 32.80,
            currentPrice: 35.67,
            marketValue: 178350,
            pnl: 14350,
            pnlPercent: 8.75,
            weight: 14.27
          },
          {
            code: '600519.XSHG',
            name: '贵州茅台',
            shares: 100,
            avgPrice: 1720.00,
            currentPrice: 1678.90,
            marketValue: 167890,
            pnl: -4110,
            pnlPercent: -2.39,
            weight: 13.43
          },
          {
            code: '000858.XSHE',
            name: '五粮液',
            shares: 1000,
            avgPrice: 142.30,
            currentPrice: 145.67,
            marketValue: 145670,
            pnl: 3370,
            pnlPercent: 2.37,
            weight: 11.65
          },
          {
            code: '000002.XSHE',
            name: '万科A',
            shares: 15000,
            avgPrice: 9.20,
            currentPrice: 8.76,
            marketValue: 131400,
            pnl: -6600,
            pnlPercent: -4.78,
            weight: 10.51
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchPositions();
  }, []);

  const portfolioStats = [
    {
      title: '总资产',
      value: portfolioData.totalValue,
      precision: 2,
      prefix: '¥',
      valueStyle: { color: '#1890ff', fontSize: '24px', fontWeight: 'bold' },
      icon: <FundOutlined />
    },
    {
      title: '今日收益',
      value: portfolioData.todayPnL,
      precision: 2,
      prefix: '¥',
      suffix: ` (${portfolioData.todayPnLPercent > 0 ? '+' : ''}${portfolioData.todayPnLPercent}%)`,
      valueStyle: { 
        color: portfolioData.todayPnL >= 0 ? '#3f8600' : '#cf1322',
        fontSize: '24px',
        fontWeight: 'bold'
      },
      icon: portfolioData.todayPnL >= 0 ? <RiseOutlined /> : <FallOutlined />
    },
    {
      title: '总收益率',
      value: portfolioData.totalPnLPercent,
      precision: 2,
      suffix: '%',
      valueStyle: { 
        color: portfolioData.totalPnLPercent >= 0 ? '#3f8600' : '#cf1322',
        fontSize: '24px',
        fontWeight: 'bold'
      },
      icon: <BarChartOutlined />
    },
    {
      title: '持仓股票',
      value: portfolioData.positionCount,
      suffix: '只',
      valueStyle: { color: '#722ed1', fontSize: '24px', fontWeight: 'bold' },
      icon: <PieChartOutlined />
    }
  ];

  const positionColumns = [
    {
      title: '股票代码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (code: string) => (
        <Text strong className="font-mono">{code}</Text>
      )
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (name: string, record: Position) => (
        <div>
          <Text strong>{name}</Text>
          <br />
          <Text type="secondary" className="text-sm">{record.shares}股</Text>
        </div>
      )
    },
    {
      title: '成本价',
      dataIndex: 'avgPrice',
      key: 'avgPrice',
      width: 100,
      align: 'right' as const,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      width: 100,
      align: 'right' as const,
      render: (price: number) => (
        <Text strong>¥{price.toFixed(2)}</Text>
      )
    },
    {
      title: '市值',
      dataIndex: 'marketValue',
      key: 'marketValue',
      width: 120,
      align: 'right' as const,
      render: (value: number) => (
        <Text strong>¥{value.toLocaleString()}</Text>
      )
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      width: 120,
      align: 'right' as const,
      render: (pnl: number, record: Position) => (
        <div className="text-right">
          <div className={`font-bold ${pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {pnl >= 0 ? '+' : ''}¥{pnl.toLocaleString()}
          </div>
          <div className={`text-sm ${record.pnlPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {record.pnlPercent >= 0 ? '+' : ''}{record.pnlPercent.toFixed(2)}%
          </div>
        </div>
      )
    },
    {
      title: '占比',
      dataIndex: 'weight',
      key: 'weight',
      width: 100,
      align: 'center' as const,
      render: (weight: number) => (
        <div>
          <Progress 
            percent={weight} 
            size="small" 
            showInfo={false}
            strokeColor={weight > 15 ? '#ff4d4f' : weight > 10 ? '#faad14' : '#52c41a'}
          />
          <Text className="text-sm">{weight.toFixed(1)}%</Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      align: 'center' as const,
      render: (_, record: Position) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => router.push(`/dashboard/market/stocks?code=${record.code}`)}
            />
          </Tooltip>
          <Tooltip title="编辑持仓">
            <Button 
              type="text" 
              size="small" 
              icon={<EditOutlined />}
            />
          </Tooltip>
          <Tooltip title="卖出">
            <Button 
              type="text" 
              size="small" 
              icon={<DeleteOutlined />}
              danger
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  const portfolioModules = [
    {
      title: '持仓概览',
      description: '查看当前持仓详情和收益情况',
      icon: <PieChartOutlined className="text-3xl text-blue-500" />,
      path: '/dashboard/portfolio/overview',
      features: ['持仓分布', '收益分析', '风险评估', '资产配置']
    },
    {
      title: '交易记录',
      description: '查看历史交易记录和分析',
      icon: <BarChartOutlined className="text-3xl text-green-500" />,
      path: '/dashboard/portfolio/performance',
      features: ['交易历史', '收益曲线', '回撤分析', '夏普比率']
    },
    {
      title: '持仓管理',
      description: '管理股票持仓和调整配置',
      icon: <StockOutlined className="text-3xl text-purple-500" />,
      path: '/dashboard/portfolio/positions',
      features: ['买入卖出', '仓位调整', '止盈止损', '自动调仓']
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <WalletOutlined className="text-3xl text-blue-600" />
          <div>
            <Title level={2} className="!mb-0">投资组合</Title>
            <Text type="secondary">管理和分析您的投资组合表现</Text>
          </div>
        </div>
        <Button type="primary" icon={<PlusOutlined />}>
          添加持仓
        </Button>
      </div>

      {/* 核心统计数据 */}
      <Row gutter={[16, 16]}>
        {portfolioStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card className="text-center hover:shadow-lg transition-shadow">
              <Statistic
                title={<span className="text-gray-600">{stat.title}</span>}
                value={stat.value}
                precision={stat.precision}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={stat.valueStyle}
              />
              <div className="mt-2 text-2xl text-gray-400">
                {stat.icon}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 持仓列表 */}
      <Card 
        title={
          <div className="flex items-center justify-between">
            <span>
              <StockOutlined className="mr-2" />
              持仓明细
              <Badge count={positions.length} className="ml-2" />
            </span>
            <Space>
              <Text type="secondary">总市值: ¥{positions.reduce((sum, pos) => sum + pos.marketValue, 0).toLocaleString()}</Text>
            </Space>
          </div>
        }
      >
        {positions.length > 0 ? (
          <Table
            columns={positionColumns}
            dataSource={positions}
            rowKey="code"
            pagination={false}
            scroll={{ x: 800 }}
            className="portfolio-table"
          />
        ) : (
          <Empty 
            description="暂无持仓数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>

      {/* 功能模块 */}
      <Card title="投资组合管理">
        <Row gutter={[16, 16]}>
          {portfolioModules.map((module, index) => (
            <Col xs={24} md={8} key={index}>
              <Card
                hoverable
                className="h-full"
                onClick={() => router.push(module.path)}
              >
                <div className="text-center">
                  <div className="mb-4">{module.icon}</div>
                  <Title level={4} className="!mb-2">{module.title}</Title>
                  <Text type="secondary" className="block mb-4">
                    {module.description}
                  </Text>
                  <div className="flex flex-wrap gap-1 justify-center">
                    {module.features.map((feature, idx) => (
                      <Tag key={idx} color="blue">{feature}</Tag>
                    ))}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  );
}
