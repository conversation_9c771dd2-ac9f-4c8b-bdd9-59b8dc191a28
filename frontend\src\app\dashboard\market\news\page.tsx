'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  DatePicker, 
  Button, 
  Table, 
  Tag, 
  Space, 
  Statistic, 
  Row, 
  Col, 
  Alert,
  Spin,
  message,
  Typography,
  Timeline,
  Progress
} from 'antd';
import {
  BellOutlined,
  RiseOutlined,
  FallOutlined,
  Bar<PERSON>hartOutlined,
  CalendarOutlined,
  SearchOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Title, Text, Paragraph } = Typography;

interface NewsItem {
  date: string;
  title: string;
  content: string;
  sentiment_score?: number;
  positive_count?: number;
  negative_count?: number;
  neutral_count?: number;
}

interface SentimentAnalysis {
  overall_sentiment: number;
  sentiment_trend: 'positive' | 'negative' | 'neutral';
  daily_sentiment: Array<{
    date: string;
    sentiment_score: number;
    positive_count: number;
    negative_count: number;
    neutral_count: number;
  }>;
  total_news: number;
  analysis_date: string;
}

export default function NewsAnalysisPage() {
  const [loading, setLoading] = useState(false);
  const [newsData, setNewsData] = useState<NewsItem[]>([]);
  const [sentimentData, setSentimentData] = useState<SentimentAnalysis | null>(null);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs()
  ]);

  // 获取新闻数据
  const fetchNewsData = async () => {
    setLoading(true);
    try {
      const [startDate, endDate] = dateRange;
      const response = await fetch(
        `/api/v1/market/cctv-news?start_date=${startDate.format('YYYY-MM-DD')}&end_date=${endDate.format('YYYY-MM-DD')}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      
      if (response.ok) {
        const result = await response.json();
        setNewsData(result.data.news || []);
        message.success(`获取到 ${result.data.count} 条新闻数据`);
      } else {
        message.error('获取新闻数据失败');
      }
    } catch (error) {
      console.error('获取新闻数据失败:', error);
      message.error('获取新闻数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 分析新闻情绪
  const analyzeSentiment = async () => {
    setLoading(true);
    try {
      const [startDate, endDate] = dateRange;
      const response = await fetch(
        `/api/v1/market/news-sentiment?start_date=${startDate.format('YYYY-MM-DD')}&end_date=${endDate.format('YYYY-MM-DD')}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );
      
      if (response.ok) {
        const result = await response.json();
        setSentimentData(result.data);
        message.success('新闻情绪分析完成');
      } else {
        message.error('新闻情绪分析失败');
      }
    } catch (error) {
      console.error('新闻情绪分析失败:', error);
      message.error('新闻情绪分析失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取情绪标签颜色
  const getSentimentColor = (trend: string) => {
    switch (trend) {
      case 'positive': return 'success';
      case 'negative': return 'error';
      default: return 'default';
    }
  };

  // 获取情绪图标
  const getSentimentIcon = (trend: string) => {
    switch (trend) {
      case 'positive': return <RiseOutlined />;
      case 'negative': return <FallOutlined />;
      default: return <BarChartOutlined />;
    }
  };

  // 新闻表格列定义
  const newsColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => dayjs(date).format('MM-DD')
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (title: string) => (
        <Text strong style={{ fontSize: '14px' }}>{title}</Text>
      )
    },
    {
      title: '内容摘要',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content: string) => (
        <Text type="secondary">
          {content ? content.substring(0, 100) + '...' : '暂无内容'}
        </Text>
      )
    }
  ];

  useEffect(() => {
    fetchNewsData();
  }, []);

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <BellOutlined className="text-3xl text-blue-600" />
          <div>
            <Title level={2} className="!mb-0">CCTV新闻联播分析</Title>
            <Text type="secondary">基于新闻联播内容进行市场情绪分析</Text>
          </div>
        </div>
      </div>

      {/* 操作区域 */}
      <Card>
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <CalendarOutlined className="text-gray-500" />
              <Text>日期范围:</Text>
            </Space>
          </Col>
          <Col>
            <RangePicker
              value={dateRange}
              onChange={(dates) => dates && setDateRange(dates)}
              format="YYYY-MM-DD"
              allowClear={false}
            />
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={fetchNewsData}
                loading={loading}
              >
                获取新闻
              </Button>
              <Button
                type="default"
                icon={<BarChartOutlined />}
                onClick={analyzeSentiment}
                loading={loading}
                disabled={newsData.length === 0}
              >
                情绪分析
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 情绪分析结果 */}
      {sentimentData && (
        <Row gutter={16}>
          <Col span={6}>
            <Card>
              <Statistic
                title="整体情绪"
                value={sentimentData.overall_sentiment}
                precision={3}
                valueStyle={{ 
                  color: sentimentData.sentiment_trend === 'positive' ? '#3f8600' : 
                         sentimentData.sentiment_trend === 'negative' ? '#cf1322' : '#666'
                }}
                prefix={getSentimentIcon(sentimentData.sentiment_trend)}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="情绪趋势"
                value={sentimentData.sentiment_trend}
                formatter={(value) => (
                  <Tag color={getSentimentColor(value as string)}>
                    {value === 'positive' ? '积极' : 
                     value === 'negative' ? '消极' : '中性'}
                  </Tag>
                )}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="新闻总数"
                value={sentimentData.total_news}
                suffix="条"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="分析时间"
                value={dayjs(sentimentData.analysis_date).format('MM-DD HH:mm')}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 每日情绪趋势 */}
      {sentimentData && sentimentData.daily_sentiment.length > 0 && (
        <Card title="每日情绪趋势">
          <Timeline>
            {sentimentData.daily_sentiment.map((item, index) => (
              <Timeline.Item
                key={index}
                color={item.sentiment_score > 0.1 ? 'green' : 
                       item.sentiment_score < -0.1 ? 'red' : 'blue'}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <Text strong>{dayjs(item.date).format('MM月DD日')}</Text>
                    <div className="mt-1">
                      <Text type="secondary">情绪得分: </Text>
                      <Text 
                        style={{ 
                          color: item.sentiment_score > 0 ? '#3f8600' : 
                                 item.sentiment_score < 0 ? '#cf1322' : '#666'
                        }}
                      >
                        {item.sentiment_score.toFixed(3)}
                      </Text>
                    </div>
                  </div>
                  <div className="text-right">
                    <div><Text type="success">积极: {item.positive_count}</Text></div>
                    <div><Text type="danger">消极: {item.negative_count}</Text></div>
                    <div><Text type="secondary">中性: {item.neutral_count}</Text></div>
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </Card>
      )}

      {/* 新闻列表 */}
      <Card 
        title={`新闻列表 (${newsData.length}条)`}
        extra={
          newsData.length > 0 && (
            <Text type="secondary">
              {dateRange[0].format('YYYY-MM-DD')} 至 {dateRange[1].format('YYYY-MM-DD')}
            </Text>
          )
        }
      >
        <Spin spinning={loading}>
          {newsData.length > 0 ? (
            <Table
              columns={newsColumns}
              dataSource={newsData}
              rowKey={(record, index) => `${record.date}-${index}`}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条新闻`
              }}
            />
          ) : (
            <div className="text-center py-8">
              <BellOutlined className="text-6xl text-gray-300 mb-4" />
              <br />
              <Text type="secondary">暂无新闻数据，请选择日期范围后点击"获取新闻"</Text>
            </div>
          )}
        </Spin>
      </Card>

      {/* 使用说明 */}
      <Alert
        message="功能说明"
        description={
          <div>
            <Paragraph>
              • <strong>新闻数据</strong>: 获取CCTV新闻联播的文本内容，这是重要的市场情绪指标
            </Paragraph>
            <Paragraph>
              • <strong>情绪分析</strong>: 通过关键词分析新闻内容的情绪倾向，帮助判断市场氛围
            </Paragraph>
            <Paragraph>
              • <strong>量化应用</strong>: 情绪数据可用于量化策略，作为市场情绪因子参考
            </Paragraph>
          </div>
        }
        type="info"
        showIcon
      />
    </div>
  );
}
