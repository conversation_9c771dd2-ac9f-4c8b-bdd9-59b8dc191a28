{"version": 3, "file": "bounds.js", "sourceRoot": "", "sources": ["../../../../src/ui/axis/utils/bounds.ts"], "names": [], "mappings": ";;;AAmFA,8BA8CC;;AA9HD,sCAAkE;AAElE;IASE,gBAAY,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;QACxD,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC3B,CAAC;IAED,sBAAW,wBAAI;aAAf;YACE,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAW,uBAAG;aAAd;YACE,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAW,yBAAK;aAAhB;YACE,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAW,0BAAM;aAAjB;YACE,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAW,yBAAK;aAAhB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAClF,CAAC;;;OAAA;IAED,sBAAW,0BAAM;aAAjB;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAClF,CAAC;;;OAAA;IAED,8BAAa,GAAb,UAAc,MAAc,EAAE,CAAS,EAAE,CAAS;QAC1C,IAAA,KAAqB,IAAI,EAAvB,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAS,CAAC;QAChC,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC7B,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACjC,IAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACjC,IAAM,MAAM,GAAY;YACtB,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;YACpD,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;YACpD,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;YACpD,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;SACrD,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,oBAAG,GAAH,UAAI,EAAU,EAAE,EAAU,EAAE,EAAU,EAAE,EAAU;QAChD,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACf,CAAC;QACD,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACf,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wBAAO,GAAP,UAAQ,GAAoE;QAC1E,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;IAC3E,CAAC;IACH,aAAC;AAAD,CAAC,AAzED,IAyEC;AAzEY,wBAAM;AA2EnB;;GAEG;AACH,SAAgB,SAAS,CAAC,IAAwB,EAAE,MAAmB;IACrE,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IACzC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IACvB,iBAAiB;IACX,IAAA,KAGF,IAAI,CAAC,SAAS,EAAE,EAFlB,KAAA,yBAAW,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA,EACV,KAAA,yBAAoB,EAAd,KAAK,QAAA,EAAE,MAAM,QACD,CAAC;IACf,IAAA,KAA0B,IAAI,CAAC,OAAO,EAAE,EAA/B,CAAC,WAAA,EAAU,CAAC,YAAmB,CAAC;IAE/C,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,IAAM,IAAI,GAAG,IAAA,aAAM,EAAC,IAAI,CAAC,CAAC;IAE1B,IAAI,IAAI,EAAE,CAAC;QACT,+BAA+B;QAC/B,MAAM,IAAI,GAAG,CAAC;QACd,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;QAC/B,IAAM,GAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QAElC,uBAAuB;QACvB,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC;YACxC,OAAO,GAAG,KAAK,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,8BAA8B;QAChC,CAAC;QAED,qBAAqB;QACrB,IAAI,GAAC,KAAK,QAAQ,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,GAAC,KAAK,QAAQ,EAAE,CAAC;YAC1B,OAAO,GAAG,MAAM,CAAC;QACnB,CAAC;IACH,CAAC;IAEK,IAAA,KAAA,eAA+B,IAAA,sBAAe,EAAC,MAAM,CAAC,IAAA,EAArD,UAAK,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,UAAK,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,UAAK,EAAL,CAAC,mBAAG,CAAC,KAAA,EAAE,UAAK,EAAL,CAAC,mBAAG,CAAC,KAA2B,CAAC;IAC7D,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACrF,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAE3B,OAAO,MAAM,CAAC,aAAa,CAAC,IAAA,eAAQ,EAAC,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC"}