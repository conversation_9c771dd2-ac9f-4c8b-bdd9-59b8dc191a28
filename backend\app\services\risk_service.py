"""
风险管理服务

提供VaR计算、风险预警、止损策略等风险管理功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, desc
from scipy import stats
from scipy.optimize import minimize

from app.core.logging import logger
from app.models.risk import RiskProfile, RiskMetrics, RiskAlert, StopLossOrder, RiskScenario
from app.models.user import User
from app.services.jqdata_service import JQDataService


class RiskCalculationService:
    """风险计算服务"""
    
    def __init__(self):
        self.jqdata_service = JQDataService()
    
    def calculate_var(
        self, 
        returns: np.ndarray, 
        confidence_level: float = 0.95,
        method: str = "historical"
    ) -> Dict[str, float]:
        """计算VaR（风险价值）"""
        try:
            if len(returns) == 0:
                return {"var": 0.0, "cvar": 0.0}
            
            if method == "historical":
                # 历史模拟法
                var = np.percentile(returns, (1 - confidence_level) * 100)
                # 条件VaR（期望损失）
                cvar = returns[returns <= var].mean() if len(returns[returns <= var]) > 0 else var
                
            elif method == "parametric":
                # 参数法（假设正态分布）
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                z_score = stats.norm.ppf(1 - confidence_level)
                var = mean_return + z_score * std_return
                cvar = mean_return - std_return * stats.norm.pdf(z_score) / (1 - confidence_level)
                
            elif method == "monte_carlo":
                # 蒙特卡洛模拟法
                mean_return = np.mean(returns)
                std_return = np.std(returns)
                simulated_returns = np.random.normal(mean_return, std_return, 10000)
                var = np.percentile(simulated_returns, (1 - confidence_level) * 100)
                cvar = simulated_returns[simulated_returns <= var].mean()
                
            else:
                raise ValueError(f"不支持的VaR计算方法: {method}")
            
            return {
                "var": float(var),
                "cvar": float(cvar),
                "method": method,
                "confidence_level": confidence_level
            }
            
        except Exception as e:
            logger.error(f"VaR计算失败: {e}")
            return {"var": 0.0, "cvar": 0.0}
    
    def calculate_portfolio_volatility(
        self, 
        weights: np.ndarray, 
        cov_matrix: np.ndarray
    ) -> float:
        """计算投资组合波动率"""
        try:
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
            return float(np.sqrt(portfolio_variance))
        except Exception as e:
            logger.error(f"投资组合波动率计算失败: {e}")
            return 0.0
    
    def calculate_beta(
        self, 
        asset_returns: np.ndarray, 
        market_returns: np.ndarray
    ) -> float:
        """计算贝塔系数"""
        try:
            if len(asset_returns) != len(market_returns) or len(asset_returns) == 0:
                return 1.0
            
            covariance = np.cov(asset_returns, market_returns)[0, 1]
            market_variance = np.var(market_returns)
            
            if market_variance == 0:
                return 1.0
                
            return float(covariance / market_variance)
        except Exception as e:
            logger.error(f"贝塔系数计算失败: {e}")
            return 1.0
    
    def calculate_correlation_matrix(self, returns_matrix: np.ndarray) -> np.ndarray:
        """计算相关性矩阵"""
        try:
            return np.corrcoef(returns_matrix.T)
        except Exception as e:
            logger.error(f"相关性矩阵计算失败: {e}")
            return np.eye(returns_matrix.shape[1])
    
    def calculate_max_drawdown(self, portfolio_values: np.ndarray) -> Dict[str, Any]:
        """计算最大回撤"""
        try:
            if len(portfolio_values) == 0:
                return {"max_drawdown": 0.0, "current_drawdown": 0.0, "duration": 0}
            
            # 计算累计最高点
            peak = np.maximum.accumulate(portfolio_values)
            
            # 计算回撤
            drawdown = (portfolio_values - peak) / peak
            
            # 最大回撤
            max_drawdown = np.min(drawdown)
            
            # 当前回撤
            current_drawdown = drawdown[-1]
            
            # 回撤持续时间
            duration = 0
            for i in range(len(drawdown) - 1, -1, -1):
                if drawdown[i] < 0:
                    duration += 1
                else:
                    break
            
            return {
                "max_drawdown": float(max_drawdown),
                "current_drawdown": float(current_drawdown),
                "duration": duration,
                "drawdown_series": drawdown.tolist()
            }
            
        except Exception as e:
            logger.error(f"最大回撤计算失败: {e}")
            return {"max_drawdown": 0.0, "current_drawdown": 0.0, "duration": 0}
    
    def calculate_concentration_metrics(self, weights: np.ndarray) -> Dict[str, float]:
        """计算集中度指标"""
        try:
            if len(weights) == 0:
                return {"hhi": 0.0, "top5_concentration": 0.0}
            
            # HHI指数（赫芬达尔-赫希曼指数）
            hhi = np.sum(weights ** 2)
            
            # 前5大持仓集中度
            sorted_weights = np.sort(weights)[::-1]
            top5_concentration = np.sum(sorted_weights[:min(5, len(sorted_weights))])
            
            return {
                "hhi": float(hhi),
                "top5_concentration": float(top5_concentration),
                "effective_positions": float(1 / hhi) if hhi > 0 else 0.0
            }
            
        except Exception as e:
            logger.error(f"集中度指标计算失败: {e}")
            return {"hhi": 0.0, "top5_concentration": 0.0}
    
    def calculate_diversification_ratio(
        self, 
        weights: np.ndarray, 
        volatilities: np.ndarray, 
        portfolio_volatility: float
    ) -> float:
        """计算分散化比率"""
        try:
            if portfolio_volatility == 0:
                return 1.0
            
            weighted_avg_volatility = np.dot(weights, volatilities)
            return float(weighted_avg_volatility / portfolio_volatility)
            
        except Exception as e:
            logger.error(f"分散化比率计算失败: {e}")
            return 1.0


class RiskMonitoringService:
    """风险监控服务"""
    
    def __init__(self):
        self.risk_calc = RiskCalculationService()
    
    async def calculate_portfolio_risk_metrics(
        self, 
        user_id: int, 
        db: AsyncSession,
        portfolio_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算投资组合风险指标"""
        try:
            # 获取用户风险档案
            risk_profile = await self._get_user_risk_profile(user_id, db)
            
            # 提取投资组合数据
            positions = portfolio_data.get("positions", [])
            if not positions:
                return {}
            
            # 计算权重
            total_value = sum(pos.get("market_value", 0) for pos in positions)
            weights = np.array([pos.get("market_value", 0) / total_value for pos in positions])
            
            # 获取历史收益率数据（这里应该从JQData获取）
            symbols = [pos.get("symbol") for pos in positions]
            returns_data = await self._get_returns_data(symbols, db)
            
            if returns_data.empty:
                logger.warning("无法获取收益率数据，使用默认风险指标")
                return self._get_default_risk_metrics()
            
            # 计算各种风险指标
            portfolio_returns = np.dot(returns_data.values, weights)
            
            # VaR计算
            var_results = self.risk_calc.calculate_var(
                portfolio_returns, 
                float(risk_profile.var_confidence_level) if risk_profile else 0.95,
                risk_profile.var_method if risk_profile else "historical"
            )
            
            # 波动率计算
            cov_matrix = np.cov(returns_data.values.T)
            portfolio_vol = self.risk_calc.calculate_portfolio_volatility(weights, cov_matrix)
            
            # 贝塔计算（假设有市场指数数据）
            market_returns = np.random.normal(0, 0.02, len(portfolio_returns))  # 模拟市场收益
            portfolio_beta = self.risk_calc.calculate_beta(portfolio_returns, market_returns)
            
            # 集中度指标
            concentration = self.risk_calc.calculate_concentration_metrics(weights)
            
            # 组装结果
            risk_metrics = {
                "var_1day": var_results["var"] * np.sqrt(1),
                "var_5day": var_results["var"] * np.sqrt(5),
                "var_10day": var_results["var"] * np.sqrt(10),
                "cvar_1day": var_results["cvar"],
                "portfolio_volatility": portfolio_vol,
                "market_beta": portfolio_beta,
                "concentration_hhi": concentration["hhi"],
                "top5_concentration": concentration["top5_concentration"],
                "calculation_date": datetime.utcnow(),
            }
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"计算投资组合风险指标失败: {e}")
            return self._get_default_risk_metrics()
    
    async def check_risk_alerts(
        self, 
        user_id: int, 
        db: AsyncSession,
        risk_metrics: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """检查风险预警"""
        alerts = []
        
        try:
            # 获取用户风险档案
            risk_profile = await self._get_user_risk_profile(user_id, db)
            if not risk_profile:
                return alerts
            
            # 检查VaR突破
            var_1day = abs(risk_metrics.get("var_1day", 0))
            if var_1day > 0.05:  # 5%的VaR阈值
                alerts.append({
                    "alert_type": "var_breach",
                    "severity": "high" if var_1day > 0.1 else "medium",
                    "title": "VaR风险预警",
                    "message": f"1日VaR为{var_1day:.2%}，超过风险阈值",
                    "current_value": var_1day,
                    "threshold_value": 0.05
                })
            
            # 检查集中度风险
            hhi = risk_metrics.get("concentration_hhi", 0)
            if hhi > 0.25:  # HHI > 0.25表示高集中度
                alerts.append({
                    "alert_type": "concentration_risk",
                    "severity": "medium",
                    "title": "投资组合集中度预警",
                    "message": f"投资组合集中度过高，HHI指数为{hhi:.3f}",
                    "current_value": hhi,
                    "threshold_value": 0.25
                })
            
            # 检查波动率异常
            portfolio_vol = risk_metrics.get("portfolio_volatility", 0)
            if portfolio_vol > 0.3:  # 年化波动率30%
                alerts.append({
                    "alert_type": "high_volatility",
                    "severity": "medium",
                    "title": "高波动率预警",
                    "message": f"投资组合波动率为{portfolio_vol:.2%}，风险较高",
                    "current_value": portfolio_vol,
                    "threshold_value": 0.3
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"风险预警检查失败: {e}")
            return alerts
    
    async def _get_user_risk_profile(self, user_id: int, db: AsyncSession) -> Optional[RiskProfile]:
        """获取用户风险档案"""
        result = await db.execute(
            select(RiskProfile).where(RiskProfile.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_returns_data(self, symbols: List[str], db: AsyncSession) -> pd.DataFrame:
        """获取收益率数据"""
        try:
            # 这里应该从JQData获取真实的历史价格数据
            # 目前返回模拟数据
            logger.warning("使用模拟收益率数据，实际应从JQData获取")
            
            # 生成模拟收益率数据
            np.random.seed(42)
            n_days = 252  # 一年交易日
            returns_data = {}
            
            for symbol in symbols:
                returns_data[symbol] = np.random.normal(0.001, 0.02, n_days)  # 日收益率
            
            return pd.DataFrame(returns_data)
            
        except Exception as e:
            logger.error(f"获取收益率数据失败: {e}")
            return pd.DataFrame()
    
    def _get_default_risk_metrics(self) -> Dict[str, Any]:
        """获取默认风险指标"""
        return {
            "var_1day": 0.0,
            "var_5day": 0.0,
            "var_10day": 0.0,
            "cvar_1day": 0.0,
            "portfolio_volatility": 0.0,
            "market_beta": 1.0,
            "concentration_hhi": 0.0,
            "top5_concentration": 0.0,
            "calculation_date": datetime.utcnow(),
        }


class StopLossService:
    """止损服务"""
    
    async def create_stop_loss_order(
        self,
        user_id: int,
        db: AsyncSession,
        order_data: Dict[str, Any]
    ) -> StopLossOrder:
        """创建止损订单"""
        try:
            stop_loss_order = StopLossOrder(
                user_id=user_id,
                symbol=order_data["symbol"],
                order_type=order_data.get("order_type", "stop_loss"),
                quantity=order_data["quantity"],
                trigger_price=Decimal(str(order_data["trigger_price"])),
                stop_price=Decimal(str(order_data["stop_price"])),
                original_price=Decimal(str(order_data.get("original_price", 0))),
                trailing_amount=Decimal(str(order_data.get("trailing_amount", 0))),
                trailing_percentage=Decimal(str(order_data.get("trailing_percentage", 0))),
                max_loss_amount=Decimal(str(order_data.get("max_loss_amount", 0))),
                expires_at=order_data.get("expires_at")
            )
            
            db.add(stop_loss_order)
            await db.commit()
            await db.refresh(stop_loss_order)
            
            logger.info(f"创建止损订单成功: {stop_loss_order.id}")
            return stop_loss_order
            
        except Exception as e:
            logger.error(f"创建止损订单失败: {e}")
            raise
    
    async def check_stop_loss_triggers(
        self,
        db: AsyncSession,
        current_prices: Dict[str, float]
    ) -> List[StopLossOrder]:
        """检查止损触发"""
        triggered_orders = []
        
        try:
            # 获取所有活跃的止损订单
            result = await db.execute(
                select(StopLossOrder).where(StopLossOrder.status == "active")
            )
            active_orders = result.scalars().all()
            
            for order in active_orders:
                current_price = current_prices.get(order.symbol)
                if current_price is None:
                    continue
                
                should_trigger = False
                
                if order.order_type == "stop_loss":
                    # 普通止损
                    should_trigger = current_price <= float(order.trigger_price)
                    
                elif order.order_type == "trailing_stop":
                    # 追踪止损
                    if current_price > float(order.highest_price or 0):
                        # 更新最高价
                        order.highest_price = Decimal(str(current_price))
                        
                        # 重新计算触发价格
                        if order.trailing_percentage:
                            new_trigger = current_price * (1 - float(order.trailing_percentage))
                        else:
                            new_trigger = current_price - float(order.trailing_amount or 0)
                        
                        order.trigger_price = Decimal(str(new_trigger))
                    
                    should_trigger = current_price <= float(order.trigger_price)
                
                if should_trigger:
                    order.status = "triggered"
                    order.triggered_at = datetime.utcnow()
                    triggered_orders.append(order)
            
            await db.commit()
            return triggered_orders
            
        except Exception as e:
            logger.error(f"检查止损触发失败: {e}")
            return []


# 全局风险管理服务实例
risk_monitoring_service = RiskMonitoringService()
stop_loss_service = StopLossService()
