"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HV = void 0;
const d3_shape_1 = require("@antv/vendor/d3-shape");
const curve_1 = require("./curve");
const HV = (options, context) => {
    return (0, curve_1.Curve)(Object.assign({ curve: d3_shape_1.curveStepAfter }, options), context);
};
exports.HV = HV;
exports.HV.props = Object.assign(Object.assign({}, curve_1.Curve.props), { defaultMarker: 'hv' });
//# sourceMappingURL=hv.js.map