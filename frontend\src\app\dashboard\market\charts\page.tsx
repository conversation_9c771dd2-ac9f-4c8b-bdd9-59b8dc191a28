'use client';

/**
 * 图表分析页面
 * 
 * 提供专业的股票图表分析工具，包括K线图、技术指标等
 */

import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Select,
  Space,
  Button,
  Typography,
  Tabs,
  Input,
  AutoComplete,
  message,
  Spin,
} from 'antd';
import {
  SearchOutlined,
  StarOutlined,
  StarFilled,
  FullscreenOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { CandlestickChart } from '@/components/charts/CandlestickChart';
import { MarketOverviewChart } from '@/components/charts/MarketOverviewChart';

const { Title, Text } = Typography;
const { Option } = AutoComplete;
const { TabPane } = Tabs;

interface StockOption {
  value: string;
  label: string;
  symbol: string;
}

export default function ChartsPage() {
  const [selectedSymbol, setSelectedSymbol] = useState('000001.XSHE');
  const [selectedStock, setSelectedStock] = useState({
    symbol: '000001.XSHE',
    name: '平安银行',
  });
  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<StockOption[]>([]);
  const [favorites, setFavorites] = useState<string[]>(['000001.XSHE', '600036.XSHG']);
  const [loading, setLoading] = useState(false);

  // 模拟股票搜索数据
  const mockStocks: StockOption[] = [
    { value: '000001.XSHE', label: '平安银行', symbol: '000001' },
    { value: '000002.XSHE', label: '万科A', symbol: '000002' },
    { value: '600036.XSHG', label: '招商银行', symbol: '600036' },
    { value: '600519.XSHG', label: '贵州茅台', symbol: '600519' },
    { value: '000858.XSHE', label: '五粮液', symbol: '000858' },
    { value: '002415.XSHE', label: '海康威视', symbol: '002415' },
    { value: '300059.XSHE', label: '东方财富', symbol: '300059' },
    { value: '600887.XSHG', label: '伊利股份', symbol: '600887' },
  ];

  // 搜索股票
  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (value) {
      const filtered = mockStocks.filter(
        stock =>
          stock.label.includes(value) ||
          stock.symbol.includes(value) ||
          stock.value.includes(value)
      );
      setSearchOptions(filtered);
    } else {
      setSearchOptions([]);
    }
  };

  // 选择股票
  const handleStockSelect = (value: string) => {
    const stock = mockStocks.find(s => s.value === value);
    if (stock) {
      setSelectedSymbol(value);
      setSelectedStock({
        symbol: value,
        name: stock.label,
      });
      setSearchValue('');
      setSearchOptions([]);
    }
  };

  // 切换收藏
  const toggleFavorite = (symbol: string) => {
    setFavorites(prev => {
      if (prev.includes(symbol)) {
        return prev.filter(s => s !== symbol);
      } else {
        return [...prev, symbol];
      }
    });
    message.success(
      favorites.includes(symbol) ? '已取消收藏' : '已添加到收藏'
    );
  };

  // 分享图表
  const handleShare = () => {
    navigator.clipboard.writeText(window.location.href);
    message.success('链接已复制到剪贴板');
  };

  // 全屏显示
  const handleFullscreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen();
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和工具栏 */}
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div>
          <Title level={2} className="!mb-2">
            图表分析
          </Title>
          <Text type="secondary">
            专业的股票图表分析工具
          </Text>
        </div>

        <Space wrap>
          <Button
            icon={<ShareAltOutlined />}
            onClick={handleShare}
          >
            分享
          </Button>
          <Button
            icon={<FullscreenOutlined />}
            onClick={handleFullscreen}
          >
            全屏
          </Button>
        </Space>
      </div>

      {/* 股票搜索和选择 */}
      <Card>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center space-x-4">
            <AutoComplete
              value={searchValue}
              options={searchOptions.map(option => ({
                value: option.value,
                label: (
                  <div className="flex items-center justify-between">
                    <span>{option.label}</span>
                    <Text type="secondary" className="text-xs">
                      {option.symbol}
                    </Text>
                  </div>
                ),
              }))}
              onSearch={handleSearch}
              onSelect={handleStockSelect}
              placeholder="搜索股票代码或名称"
              style={{ width: 300 }}
              allowClear
            >
              <Input
                prefix={<SearchOutlined />}
                placeholder="搜索股票代码或名称"
              />
            </AutoComplete>

            <div className="flex items-center space-x-2">
              <Text strong className="text-lg">
                {selectedStock.name}
              </Text>
              <Text type="secondary">
                {selectedStock.symbol}
              </Text>
              <Button
                type="text"
                size="small"
                icon={
                  favorites.includes(selectedSymbol) ? (
                    <StarFilled className="text-yellow-500" />
                  ) : (
                    <StarOutlined />
                  )
                }
                onClick={() => toggleFavorite(selectedSymbol)}
              />
            </div>
          </div>

          {/* 收藏股票快速选择 */}
          <div className="flex items-center space-x-2">
            <Text type="secondary" className="text-sm">
              收藏:
            </Text>
            <Space>
              {favorites.map(symbol => {
                const stock = mockStocks.find(s => s.value === symbol);
                return stock ? (
                  <Button
                    key={symbol}
                    type={selectedSymbol === symbol ? 'primary' : 'default'}
                    size="small"
                    onClick={() => handleStockSelect(symbol)}
                  >
                    {stock.label}
                  </Button>
                ) : null;
              })}
            </Space>
          </div>
        </div>
      </Card>

      {/* 图表区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Tabs defaultActiveKey="kline" className="bg-white rounded-lg">
          <TabPane tab="K线图" key="kline">
            <Card bodyStyle={{ padding: '24px' }}>
              <CandlestickChart
                symbol={selectedSymbol}
                height={600}
                onSymbolChange={handleStockSelect}
                showTechnicalIndicators={true}
              />
            </Card>
          </TabPane>

          <TabPane tab="分时图" key="timeshare">
            <Card bodyStyle={{ padding: '24px' }}>
              <MarketOverviewChart height={600} />
            </Card>
          </TabPane>

          <TabPane tab="技术指标" key="indicators">
            <Card bodyStyle={{ padding: '24px' }}>
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <Text type="secondary" className="text-lg">
                    技术指标功能开发中...
                  </Text>
                  <div className="mt-4">
                    <Text type="secondary">
                      即将支持MACD、RSI、KDJ等常用技术指标
                    </Text>
                  </div>
                </div>
              </div>
            </Card>
          </TabPane>

          <TabPane tab="对比分析" key="compare">
            <Card bodyStyle={{ padding: '24px' }}>
              <div className="flex items-center justify-center h-96">
                <div className="text-center">
                  <Text type="secondary" className="text-lg">
                    对比分析功能开发中...
                  </Text>
                  <div className="mt-4">
                    <Text type="secondary">
                      即将支持多股票对比、行业对比等功能
                    </Text>
                  </div>
                </div>
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </motion.div>

      {/* 相关信息面板 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={8}>
          <Card title="股票信息" size="small">
            <div className="space-y-3">
              <div className="flex justify-between">
                <Text type="secondary">股票代码:</Text>
                <Text>{selectedStock.symbol}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">股票名称:</Text>
                <Text>{selectedStock.name}</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">所属市场:</Text>
                <Text>深圳证券交易所</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">所属行业:</Text>
                <Text>银行业</Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="实时数据" size="small">
            <div className="space-y-3">
              <div className="flex justify-between">
                <Text type="secondary">最新价:</Text>
                <Text strong className="text-red-500">¥12.45</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">涨跌额:</Text>
                <Text className="text-red-500">+0.23</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">涨跌幅:</Text>
                <Text className="text-red-500">+1.88%</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">成交量:</Text>
                <Text>1.25亿</Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="技术指标" size="small">
            <div className="space-y-3">
              <div className="flex justify-between">
                <Text type="secondary">MA5:</Text>
                <Text>¥12.38</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">MA10:</Text>
                <Text>¥12.25</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">MA20:</Text>
                <Text>¥12.15</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">RSI:</Text>
                <Text>65.2</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
