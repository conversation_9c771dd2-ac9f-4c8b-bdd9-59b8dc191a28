# Authors: <AUTHORS>
#          + All contributors to <https://github.com/smarie/python-makefun>
#
# License: 3-clause BSD, <https://github.com/smarie/python-makefun/blob/master/LICENSE>
from .main import create_function, with_signature, remove_signature_parameters, add_signature_parameters, \
    wraps, create_wrapper, partial, with_partial, compile_fun, UndefinedSymbolError, UnsupportedForCompilation, \
    SourceUnavailable

try:
    # -- Distribution mode: import from _version.py generated by setuptools_scm during release
    from ._version import version as __version__
except ImportError:
    # -- Source mode: use setuptools_scm to get the current version from src using git
    from setuptools_scm import get_version as _gv
    from os import path as _path
    __version__ = _gv(_path.join(_path.dirname(__file__), _path.pardir, _path.pardir))

__all__ = [
    '__version__',
    # submodules
    'main',
    # symbols
    'create_function', 'with_signature',
    'remove_signature_parameters', 'add_signature_parameters',
    'wraps', 'create_wrapper', 'partial', 'with_partial',
    # pseudo compilation
    'compile_fun', 'UndefinedSymbolError', 'UnsupportedForCompilation', 'SourceUnavailable'
]
