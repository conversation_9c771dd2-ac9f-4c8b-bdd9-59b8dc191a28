'use client';

/**
 * 高级图表组件
 * 
 * 支持多种技术指标、自定义指标和交互式分析
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Select, InputNumber, Drawer, Space, Divider, Checkbox, message } from 'antd';
import { SettingOutlined, FullscreenOutlined, DownloadOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';

const { Option } = Select;

interface AdvancedChartProps {
  symbol: string;
  data: {
    dates: string[];
    open: number[];
    high: number[];
    low: number[];
    close: number[];
    volume: number[];
  };
  height?: number;
  showToolbar?: boolean;
}

interface IndicatorConfig {
  name: string;
  type: string;
  parameters: Record<string, any>;
  visible: boolean;
  color?: string;
}

export const AdvancedChart: React.FC<AdvancedChartProps> = ({
  symbol,
  data,
  height = 600,
  showToolbar = true,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [chartType, setChartType] = useState<'candlestick' | 'line' | 'area'>('candlestick');
  const [indicators, setIndicators] = useState<IndicatorConfig[]>([
    { name: 'MA5', type: 'sma', parameters: { period: 5 }, visible: true, color: '#FF6B6B' },
    { name: 'MA20', type: 'sma', parameters: { period: 20 }, visible: true, color: '#4ECDC4' },
    { name: 'MACD', type: 'macd', parameters: { fast: 12, slow: 26, signal: 9 }, visible: false },
    { name: 'RSI', type: 'rsi', parameters: { period: 14 }, visible: false },
    { name: 'BOLL', type: 'bollinger_bands', parameters: { period: 20, std: 2 }, visible: false },
  ]);

  // 可用指标列表
  const availableIndicators = {
    trend: [
      { key: 'sma', name: '简单移动平均', params: ['period'] },
      { key: 'ema', name: '指数移动平均', params: ['period'] },
      { key: 'bollinger_bands', name: '布林带', params: ['period', 'std'] },
    ],
    momentum: [
      { key: 'rsi', name: 'RSI', params: ['period'] },
      { key: 'macd', name: 'MACD', params: ['fast', 'slow', 'signal'] },
      { key: 'kdj', name: 'KDJ', params: ['period'] },
    ],
    volume: [
      { key: 'obv', name: 'OBV', params: [] },
      { key: 'vwap', name: 'VWAP', params: [] },
    ],
  };

  // 计算技术指标
  const calculateIndicator = useCallback(async (config: IndicatorConfig) => {
    try {
      const response = await fetch('/api/v1/indicators/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          indicator: config.type,
          data: {
            close: data.close,
            high: data.high,
            low: data.low,
            volume: data.volume,
          },
          parameters: config.parameters,
        }),
      });

      const result = await response.json();
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || '指标计算失败');
      }
    } catch (error) {
      console.error(`计算指标 ${config.name} 失败:`, error);
      return null;
    }
  }, [data]);

  // 初始化图表
  const initChart = useCallback(async () => {
    if (!chartRef.current || !data.dates.length) return;

    setLoading(true);

    // 销毁现有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);

    // 准备K线数据
    const klineData = data.dates.map((date, index) => [
      data.open[index],
      data.close[index],
      data.low[index],
      data.high[index],
    ]);

    // 计算可见指标
    const indicatorData: Record<string, any> = {};
    for (const config of indicators.filter(ind => ind.visible)) {
      const result = await calculateIndicator(config);
      if (result) {
        indicatorData[config.name] = { ...result, config };
      }
    }

    // 构建图表配置
    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      animation: false,
      legend: {
        data: ['K线', ...indicators.filter(ind => ind.visible).map(ind => ind.name)],
        top: 10,
        textStyle: { color: '#8392A5', fontSize: 12 },
      },
      grid: [
        {
          id: 'main',
          left: '10%',
          right: '8%',
          top: '15%',
          height: '50%',
        },
        {
          id: 'volume',
          left: '10%',
          right: '8%',
          top: '70%',
          height: '15%',
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: data.dates,
          gridIndex: 0,
          axisLine: { onZero: false },
          splitLine: { show: false },
          axisLabel: { show: false },
        },
        {
          type: 'category',
          data: data.dates,
          gridIndex: 1,
          axisLine: { onZero: false },
          splitLine: { show: false },
          axisLabel: {
            formatter: (value: string) => {
              const date = new Date(value);
              return `${date.getMonth() + 1}/${date.getDate()}`;
            },
          },
        },
      ],
      yAxis: [
        {
          scale: true,
          gridIndex: 0,
          splitArea: { show: true },
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 80,
          end: 100,
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '90%',
          start: 80,
          end: 100,
        },
      ],
      series: [],
    };

    // 添加主图表系列
    if (chartType === 'candlestick') {
      (option.series as any[]).push({
        name: 'K线',
        type: 'candlestick',
        data: klineData,
        xAxisIndex: 0,
        yAxisIndex: 0,
        itemStyle: {
          color: '#ef232a',
          color0: '#14b143',
          borderColor: '#ef232a',
          borderColor0: '#14b143',
        },
      });
    } else if (chartType === 'line') {
      (option.series as any[]).push({
        name: '收盘价',
        type: 'line',
        data: data.close,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        lineStyle: { width: 2 },
        symbol: 'none',
      });
    } else if (chartType === 'area') {
      (option.series as any[]).push({
        name: '收盘价',
        type: 'line',
        data: data.close,
        xAxisIndex: 0,
        yAxisIndex: 0,
        smooth: true,
        lineStyle: { width: 2 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
          ]),
        },
        symbol: 'none',
      });
    }

    // 添加成交量
    (option.series as any[]).push({
      name: '成交量',
      type: 'bar',
      data: data.volume,
      xAxisIndex: 1,
      yAxisIndex: 1,
      itemStyle: {
        color: (params: any) => {
          const index = params.dataIndex;
          return data.close[index] >= data.open[index] ? '#ef232a' : '#14b143';
        },
      },
    });

    // 添加技术指标
    Object.entries(indicatorData).forEach(([name, result]) => {
      const config = result.config;
      
      if (config.type === 'sma' || config.type === 'ema') {
        (option.series as any[]).push({
          name,
          type: 'line',
          data: result[config.type],
          xAxisIndex: 0,
          yAxisIndex: 0,
          smooth: true,
          lineStyle: { color: config.color, width: 1 },
          symbol: 'none',
        });
      } else if (config.type === 'bollinger_bands') {
        ['upper_band', 'middle_band', 'lower_band'].forEach((band, index) => {
          (option.series as any[]).push({
            name: `${name}_${band}`,
            type: 'line',
            data: result[band],
            xAxisIndex: 0,
            yAxisIndex: 0,
            smooth: true,
            lineStyle: { 
              color: config.color, 
              width: index === 1 ? 2 : 1,
              type: index === 1 ? 'solid' : 'dashed',
            },
            symbol: 'none',
            showInLegend: index === 1,
          });
        });
      }
      // 可以继续添加其他指标的绘制逻辑
    });

    chartInstance.current.setOption(option);
    setLoading(false);
  }, [data, chartType, indicators, calculateIndicator]);

  // 处理窗口大小变化
  const handleResize = useCallback(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, []);

  // 添加指标
  const addIndicator = (type: string, name: string) => {
    const newIndicator: IndicatorConfig = {
      name: `${name}_${Date.now()}`,
      type,
      parameters: getDefaultParameters(type),
      visible: true,
      color: getRandomColor(),
    };
    setIndicators(prev => [...prev, newIndicator]);
  };

  // 获取默认参数
  const getDefaultParameters = (type: string): Record<string, any> => {
    const defaults: Record<string, any> = {
      sma: { period: 20 },
      ema: { period: 20 },
      rsi: { period: 14 },
      macd: { fast: 12, slow: 26, signal: 9 },
      bollinger_bands: { period: 20, std: 2 },
      kdj: { period: 9 },
    };
    return defaults[type] || {};
  };

  // 获取随机颜色
  const getRandomColor = (): string => {
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // 切换指标可见性
  const toggleIndicator = (index: number) => {
    setIndicators(prev => prev.map((ind, i) => 
      i === index ? { ...ind, visible: !ind.visible } : ind
    ));
  };

  // 删除指标
  const removeIndicator = (index: number) => {
    setIndicators(prev => prev.filter((_, i) => i !== index));
  };

  // 导出图表
  const exportChart = () => {
    if (chartInstance.current) {
      const url = chartInstance.current.getDataURL({
        type: 'png',
        pixelRatio: 2,
        backgroundColor: '#fff',
      });
      const link = document.createElement('a');
      link.download = `${symbol}_chart.png`;
      link.href = url;
      link.click();
      message.success('图表导出成功');
    }
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (chartRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        chartRef.current.requestFullscreen();
      }
    }
  };

  useEffect(() => {
    initChart();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [initChart, handleResize]);

  return (
    <div className="relative">
      {/* 工具栏 */}
      {showToolbar && (
        <div className="absolute top-2 right-2 z-10 flex space-x-2">
          <Select
            value={chartType}
            onChange={setChartType}
            size="small"
            style={{ width: 100 }}
          >
            <Option value="candlestick">K线图</Option>
            <Option value="line">线图</Option>
            <Option value="area">面积图</Option>
          </Select>
          
          <Button
            size="small"
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            指标
          </Button>
          
          <Button
            size="small"
            icon={<DownloadOutlined />}
            onClick={exportChart}
          />
          
          <Button
            size="small"
            icon={<FullscreenOutlined />}
            onClick={toggleFullscreen}
          />
        </div>
      )}

      {/* 图表容器 */}
      <div
        ref={chartRef}
        style={{ height: `${height}px`, width: '100%' }}
        className={loading ? 'opacity-50' : ''}
      />

      {/* 指标设置抽屉 */}
      <Drawer
        title="技术指标设置"
        placement="right"
        width={400}
        open={settingsVisible}
        onClose={() => setSettingsVisible(false)}
      >
        <div className="space-y-4">
          {/* 当前指标 */}
          <div>
            <h4 className="font-medium mb-2">当前指标</h4>
            {indicators.map((indicator, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded mb-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={indicator.visible}
                    onChange={() => toggleIndicator(index)}
                  />
                  <span>{indicator.name}</span>
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: indicator.color }}
                  />
                </div>
                <Button
                  size="small"
                  danger
                  onClick={() => removeIndicator(index)}
                >
                  删除
                </Button>
              </div>
            ))}
          </div>

          <Divider />

          {/* 添加指标 */}
          <div>
            <h4 className="font-medium mb-2">添加指标</h4>
            
            {Object.entries(availableIndicators).map(([category, indicators]) => (
              <div key={category} className="mb-4">
                <h5 className="text-sm font-medium text-gray-600 mb-2">
                  {category === 'trend' ? '趋势指标' : 
                   category === 'momentum' ? '动量指标' : '成交量指标'}
                </h5>
                <div className="grid grid-cols-2 gap-2">
                  {indicators.map(indicator => (
                    <Button
                      key={indicator.key}
                      size="small"
                      onClick={() => addIndicator(indicator.key, indicator.name)}
                    >
                      {indicator.name}
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </Drawer>
    </div>
  );
};
