'use client';

/**
 * 金融专用图表组件
 * 
 * 专门为金融数据设计的图表组件
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Typography, Space, Tag, Statistic, Row, Col } from 'antd';
import { 
  RiseOutlined, 
  FallOutlined, 
  TrendingUpOutlined,
  TrendingDownOutlined 
} from '@ant-design/icons';

const { Title, Text } = Typography;

// 技术指标图表组件
export const TechnicalIndicatorChart: React.FC<{
  priceData: any[];
  indicators: {
    ma5?: number[];
    ma10?: number[];
    ma20?: number[];
    rsi?: number[];
    macd?: {
      dif: number[];
      dea: number[];
      histogram: number[];
    };
  };
  title?: string;
  height?: number;
}> = ({ 
  priceData, 
  indicators, 
  title = "技术指标图", 
  height = 600 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const dates = priceData.map(item => item.date);
      const prices = priceData.map(item => [item.open, item.close, item.low, item.high]);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['K线', 'MA5', 'MA10', 'MA20', 'RSI', 'MACD'],
          top: 30
        },
        grid: [
          {
            left: '10%',
            right: '8%',
            top: '15%',
            height: '40%'
          },
          {
            left: '10%',
            right: '8%',
            top: '60%',
            height: '15%'
          },
          {
            left: '10%',
            right: '8%',
            top: '80%',
            height: '15%'
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: dates,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: 'dataMin',
            max: 'dataMax'
          },
          {
            type: 'category',
            gridIndex: 1,
            data: dates,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: { show: false }
          },
          {
            type: 'category',
            gridIndex: 2,
            data: dates,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: { show: false }
          }
        ],
        yAxis: [
          {
            scale: true,
            splitArea: { show: true }
          },
          {
            scale: true,
            gridIndex: 1,
            splitNumber: 2,
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
          },
          {
            scale: true,
            gridIndex: 2,
            splitNumber: 2,
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1, 2],
            start: 80,
            end: 100
          },
          {
            show: true,
            xAxisIndex: [0, 1, 2],
            type: 'slider',
            top: '96%',
            start: 80,
            end: 100
          }
        ],
        series: [
          // K线
          {
            name: 'K线',
            type: 'candlestick',
            data: prices,
            itemStyle: {
              color: '#ef232a',
              color0: '#14b143',
              borderColor: '#ef232a',
              borderColor0: '#14b143'
            }
          },
          // 移动平均线
          ...(indicators.ma5 ? [{
            name: 'MA5',
            type: 'line',
            data: indicators.ma5,
            smooth: true,
            lineStyle: { width: 1, color: '#1890ff' }
          }] : []),
          ...(indicators.ma10 ? [{
            name: 'MA10',
            type: 'line',
            data: indicators.ma10,
            smooth: true,
            lineStyle: { width: 1, color: '#52c41a' }
          }] : []),
          ...(indicators.ma20 ? [{
            name: 'MA20',
            type: 'line',
            data: indicators.ma20,
            smooth: true,
            lineStyle: { width: 1, color: '#faad14' }
          }] : []),
          // RSI
          ...(indicators.rsi ? [{
            name: 'RSI',
            type: 'line',
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: indicators.rsi,
            smooth: true,
            lineStyle: { color: '#722ed1' }
          }] : []),
          // MACD
          ...(indicators.macd ? [
            {
              name: 'DIF',
              type: 'line',
              xAxisIndex: 2,
              yAxisIndex: 2,
              data: indicators.macd.dif,
              smooth: true,
              lineStyle: { color: '#1890ff' }
            },
            {
              name: 'DEA',
              type: 'line',
              xAxisIndex: 2,
              yAxisIndex: 2,
              data: indicators.macd.dea,
              smooth: true,
              lineStyle: { color: '#52c41a' }
            },
            {
              name: 'MACD',
              type: 'bar',
              xAxisIndex: 2,
              yAxisIndex: 2,
              data: indicators.macd.histogram,
              itemStyle: {
                color: function(params: any) {
                  return params.value >= 0 ? '#ef232a' : '#14b143';
                }
              }
            }
          ] : [])
        ]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [priceData, indicators, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 收益率分布图
export const ReturnDistributionChart: React.FC<{
  returns: number[];
  title?: string;
  height?: number;
}> = ({ 
  returns, 
  title = "收益率分布", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      // 计算直方图数据
      const bins = 20;
      const min = Math.min(...returns);
      const max = Math.max(...returns);
      const binWidth = (max - min) / bins;
      
      const histogram = new Array(bins).fill(0);
      const binLabels = [];
      
      for (let i = 0; i < bins; i++) {
        const binStart = min + i * binWidth;
        const binEnd = min + (i + 1) * binWidth;
        binLabels.push(`${binStart.toFixed(2)}%`);
        
        returns.forEach(ret => {
          if (ret >= binStart && ret < binEnd) {
            histogram[i]++;
          }
        });
      }
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params: any) {
            const data = params[0];
            return `收益率区间: ${data.name}<br/>频次: ${data.value}`;
          }
        },
        xAxis: {
          type: 'category',
          data: binLabels,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '频次'
        },
        series: [{
          name: '收益率分布',
          type: 'bar',
          data: histogram,
          itemStyle: {
            color: function(params: any) {
              const binStart = min + params.dataIndex * binWidth;
              return binStart >= 0 ? '#52c41a' : '#ff4d4f';
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [returns, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 相关性矩阵图
export const CorrelationMatrixChart: React.FC<{
  correlationMatrix: number[][];
  labels: string[];
  title?: string;
  height?: number;
}> = ({ 
  correlationMatrix, 
  labels, 
  title = "相关性矩阵", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      // 转换数据格式
      const data: Array<[number, number, number]> = [];
      for (let i = 0; i < correlationMatrix.length; i++) {
        for (let j = 0; j < correlationMatrix[i].length; j++) {
          data.push([j, i, correlationMatrix[i][j]]);
        }
      }
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          position: 'top',
          formatter: function (params: any) {
            return `${labels[params.value[1]]} vs ${labels[params.value[0]]}<br/>相关系数: ${params.value[2].toFixed(3)}`;
          }
        },
        grid: {
          height: '50%',
          top: '10%'
        },
        xAxis: {
          type: 'category',
          data: labels,
          splitArea: { show: true },
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'category',
          data: labels,
          splitArea: { show: true }
        },
        visualMap: {
          min: -1,
          max: 1,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '15%',
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          }
        },
        series: [{
          name: '相关性',
          type: 'heatmap',
          data: data,
          label: {
            show: true,
            formatter: function (params: any) {
              return params.value[2].toFixed(2);
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [correlationMatrix, labels, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 风险收益散点图
export const RiskReturnScatterChart: React.FC<{
  data: Array<{
    name: string;
    risk: number;
    return: number;
    size?: number;
  }>;
  title?: string;
  height?: number;
}> = ({ 
  data, 
  title = "风险收益散点图", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params: any) {
            const data = params.data;
            return `${data.name}<br/>风险: ${data.value[0].toFixed(2)}%<br/>收益: ${data.value[1].toFixed(2)}%`;
          }
        },
        xAxis: {
          type: 'value',
          name: '风险 (%)',
          nameLocation: 'middle',
          nameGap: 30,
          scale: true
        },
        yAxis: {
          type: 'value',
          name: '收益 (%)',
          nameLocation: 'middle',
          nameGap: 40,
          scale: true
        },
        series: [{
          name: '资产',
          type: 'scatter',
          data: data.map(item => ({
            name: item.name,
            value: [item.risk, item.return],
            symbolSize: item.size || 20,
            itemStyle: {
              color: item.return >= 0 ? '#52c41a' : '#ff4d4f'
            }
          })),
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 资产配置饼图（增强版）
export const EnhancedPieChart: React.FC<{
  data: Array<{
    name: string;
    value: number;
    return?: number;
    weight?: number;
  }>;
  title?: string;
  height?: number;
  showStats?: boolean;
}> = ({ 
  data, 
  title = "资产配置", 
  height = 400,
  showStats = true 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  const totalValue = data.reduce((sum, item) => sum + item.value, 0);
  const avgReturn = data.reduce((sum, item) => sum + (item.return || 0) * (item.value / totalValue), 0);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function (params: any) {
            const data = params.data;
            return `${data.name}<br/>价值: ¥${data.value.toLocaleString()}<br/>占比: ${params.percent}%${data.return ? `<br/>收益率: ${data.return.toFixed(2)}%` : ''}`;
          }
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.map(item => item.name)
        },
        series: [{
          name: '资产配置',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data.map(item => ({
            name: item.name,
            value: item.value,
            return: item.return,
            itemStyle: {
              color: item.return && item.return >= 0 ? 
                `rgba(82, 196, 26, ${0.6 + (item.return / 100) * 0.4})` : 
                `rgba(255, 77, 79, ${0.6 + Math.abs(item.return || 0) / 100 * 0.4})`
            }
          }))
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div className="space-y-4">
      <div 
        ref={chartRef} 
        style={{ width: '100%', height: `${height}px` }}
      />
      
      {showStats && (
        <Row gutter={[16, 16]}>
          <Col xs={8}>
            <Statistic
              title="总价值"
              value={totalValue}
              prefix="¥"
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Col>
          <Col xs={8}>
            <Statistic
              title="平均收益率"
              value={avgReturn}
              suffix="%"
              precision={2}
              valueStyle={{ color: avgReturn >= 0 ? '#52c41a' : '#ff4d4f' }}
              prefix={avgReturn >= 0 ? <RiseOutlined /> : <FallOutlined />}
            />
          </Col>
          <Col xs={8}>
            <Statistic
              title="资产数量"
              value={data.length}
              suffix="项"
            />
          </Col>
        </Row>
      )}
    </div>
  );
};
