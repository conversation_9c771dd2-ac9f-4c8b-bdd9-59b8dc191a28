"""add multimodal tables

Revision ID: 008
Revises: 007
Create Date: 2024-12-22 23:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '008'
down_revision = '007'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建新闻数据源表
    op.create_table('news_sources',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('source_type', sa.String(length=50), nullable=True),
        sa.Column('api_endpoint', sa.String(length=500), nullable=True),
        sa.Column('api_key_required', sa.<PERSON>(), nullable=True),
        sa.Column('api_rate_limit', sa.Integer(), nullable=True),
        sa.Column('supported_languages', sa.JSON(), nullable=True),
        sa.Column('supported_categories', sa.JSON(), nullable=True),
        sa.Column('data_format', sa.String(length=20), nullable=True),
        sa.Column('reliability_score', sa.Float(), nullable=True),
        sa.Column('update_frequency', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_premium', sa.Boolean(), nullable=True),
        sa.Column('total_articles', sa.Integer(), nullable=True),
        sa.Column('last_updated', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_news_sources_id'), 'news_sources', ['id'], unique=False)

    # 创建新闻文章表
    op.create_table('news_articles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('author', sa.String(length=200), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=True),
        sa.Column('language', sa.String(length=10), nullable=True),
        sa.Column('url', sa.String(length=1000), nullable=True),
        sa.Column('external_id', sa.String(length=200), nullable=True),
        sa.Column('published_at', sa.DateTime(), nullable=False),
        sa.Column('crawled_at', sa.DateTime(), nullable=True),
        sa.Column('related_symbols', sa.JSON(), nullable=True),
        sa.Column('related_keywords', sa.JSON(), nullable=True),
        sa.Column('relevance_score', sa.Float(), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('readability_score', sa.Float(), nullable=True),
        sa.Column('credibility_score', sa.Float(), nullable=True),
        sa.Column('view_count', sa.Integer(), nullable=True),
        sa.Column('share_count', sa.Integer(), nullable=True),
        sa.Column('comment_count', sa.Integer(), nullable=True),
        sa.Column('is_processed', sa.Boolean(), nullable=True),
        sa.Column('processing_status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_id'], ['news_sources.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_news_articles_id'), 'news_articles', ['id'], unique=False)
    op.create_index(op.f('ix_news_articles_published_at'), 'news_articles', ['published_at'], unique=False)
    op.create_index(op.f('ix_news_articles_source_id'), 'news_articles', ['source_id'], unique=False)

    # 创建新闻情感分析表
    op.create_table('news_sentiments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('article_id', sa.Integer(), nullable=False),
        sa.Column('sentiment_label', sa.String(length=20), nullable=False),
        sa.Column('sentiment_score', sa.Float(), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=False),
        sa.Column('emotions', sa.JSON(), nullable=True),
        sa.Column('emotion_scores', sa.JSON(), nullable=True),
        sa.Column('topic_sentiments', sa.JSON(), nullable=True),
        sa.Column('aspect_sentiments', sa.JSON(), nullable=True),
        sa.Column('model_name', sa.String(length=100), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('analysis_method', sa.String(length=50), nullable=True),
        sa.Column('analysis_quality', sa.Float(), nullable=True),
        sa.Column('text_complexity', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['article_id'], ['news_articles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_news_sentiments_article_id'), 'news_sentiments', ['article_id'], unique=False)
    op.create_index(op.f('ix_news_sentiments_id'), 'news_sentiments', ['id'], unique=False)

    # 创建新闻实体提取表
    op.create_table('news_entity_extractions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('article_id', sa.Integer(), nullable=False),
        sa.Column('entity_text', sa.String(length=200), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('entity_category', sa.String(length=50), nullable=True),
        sa.Column('start_position', sa.Integer(), nullable=True),
        sa.Column('end_position', sa.Integer(), nullable=True),
        sa.Column('context', sa.String(length=500), nullable=True),
        sa.Column('confidence', sa.Float(), nullable=False),
        sa.Column('relevance_score', sa.Float(), nullable=True),
        sa.Column('importance_score', sa.Float(), nullable=True),
        sa.Column('normalized_name', sa.String(length=200), nullable=True),
        sa.Column('entity_id', sa.String(length=100), nullable=True),
        sa.Column('related_entities', sa.JSON(), nullable=True),
        sa.Column('entity_attributes', sa.JSON(), nullable=True),
        sa.Column('extraction_model', sa.String(length=100), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['article_id'], ['news_articles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_news_entity_extractions_article_id'), 'news_entity_extractions', ['article_id'], unique=False)
    op.create_index(op.f('ix_news_entity_extractions_id'), 'news_entity_extractions', ['id'], unique=False)

    # 创建社交媒体数据源表
    op.create_table('social_media_sources',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('platform', sa.String(length=50), nullable=False),
        sa.Column('platform_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('api_endpoint', sa.String(length=500), nullable=True),
        sa.Column('api_version', sa.String(length=20), nullable=True),
        sa.Column('authentication_type', sa.String(length=50), nullable=True),
        sa.Column('rate_limit_per_hour', sa.Integer(), nullable=True),
        sa.Column('supported_data_types', sa.JSON(), nullable=True),
        sa.Column('max_history_days', sa.Integer(), nullable=True),
        sa.Column('real_time_support', sa.Boolean(), nullable=True),
        sa.Column('data_quality_score', sa.Float(), nullable=True),
        sa.Column('spam_filter_enabled', sa.Boolean(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_premium', sa.Boolean(), nullable=True),
        sa.Column('total_posts', sa.Integer(), nullable=True),
        sa.Column('active_users', sa.Integer(), nullable=True),
        sa.Column('last_sync', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_social_media_sources_id'), 'social_media_sources', ['id'], unique=False)

    # 创建社交媒体帖子表
    op.create_table('social_media_posts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_id', sa.Integer(), nullable=False),
        sa.Column('post_id', sa.String(length=200), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('content_type', sa.String(length=20), nullable=True),
        sa.Column('language', sa.String(length=10), nullable=True),
        sa.Column('user_id', sa.String(length=200), nullable=True),
        sa.Column('username', sa.String(length=200), nullable=True),
        sa.Column('user_followers', sa.Integer(), nullable=True),
        sa.Column('user_verified', sa.Boolean(), nullable=True),
        sa.Column('user_influence_score', sa.Float(), nullable=True),
        sa.Column('posted_at', sa.DateTime(), nullable=False),
        sa.Column('collected_at', sa.DateTime(), nullable=True),
        sa.Column('like_count', sa.Integer(), nullable=True),
        sa.Column('share_count', sa.Integer(), nullable=True),
        sa.Column('comment_count', sa.Integer(), nullable=True),
        sa.Column('view_count', sa.Integer(), nullable=True),
        sa.Column('is_repost', sa.Boolean(), nullable=True),
        sa.Column('original_post_id', sa.String(length=200), nullable=True),
        sa.Column('repost_chain_length', sa.Integer(), nullable=True),
        sa.Column('viral_score', sa.Float(), nullable=True),
        sa.Column('related_symbols', sa.JSON(), nullable=True),
        sa.Column('related_topics', sa.JSON(), nullable=True),
        sa.Column('hashtags', sa.JSON(), nullable=True),
        sa.Column('mentions', sa.JSON(), nullable=True),
        sa.Column('spam_score', sa.Float(), nullable=True),
        sa.Column('credibility_score', sa.Float(), nullable=True),
        sa.Column('influence_score', sa.Float(), nullable=True),
        sa.Column('is_processed', sa.Boolean(), nullable=True),
        sa.Column('processing_status', sa.String(length=20), nullable=True),
        sa.Column('location', sa.String(length=200), nullable=True),
        sa.Column('coordinates', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_id'], ['social_media_sources.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_social_media_posts_id'), 'social_media_posts', ['id'], unique=False)
    op.create_index(op.f('ix_social_media_posts_posted_at'), 'social_media_posts', ['posted_at'], unique=False)
    op.create_index(op.f('ix_social_media_posts_source_id'), 'social_media_posts', ['source_id'], unique=False)

    # 创建社交媒体情感分析表
    op.create_table('social_sentiments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('post_id', sa.Integer(), nullable=False),
        sa.Column('sentiment_label', sa.String(length=20), nullable=False),
        sa.Column('sentiment_score', sa.Float(), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=False),
        sa.Column('emotions', sa.JSON(), nullable=True),
        sa.Column('emotion_intensities', sa.JSON(), nullable=True),
        sa.Column('topic_sentiments', sa.JSON(), nullable=True),
        sa.Column('stock_sentiments', sa.JSON(), nullable=True),
        sa.Column('weighted_sentiment', sa.Float(), nullable=True),
        sa.Column('viral_adjusted_sentiment', sa.Float(), nullable=True),
        sa.Column('analysis_model', sa.String(length=100), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('analysis_timestamp', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['post_id'], ['social_media_posts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_social_sentiments_id'), 'social_sentiments', ['id'], unique=False)
    op.create_index(op.f('ix_social_sentiments_post_id'), 'social_sentiments', ['post_id'], unique=False)

    # 创建另类数据源表
    op.create_table('alternative_data_sources',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('data_category', sa.String(length=50), nullable=True),
        sa.Column('provider', sa.String(length=100), nullable=True),
        sa.Column('provider_contact', sa.String(length=200), nullable=True),
        sa.Column('data_license', sa.String(length=100), nullable=True),
        sa.Column('api_endpoint', sa.String(length=500), nullable=True),
        sa.Column('data_format', sa.String(length=20), nullable=True),
        sa.Column('authentication_method', sa.String(length=50), nullable=True),
        sa.Column('update_frequency', sa.String(length=50), nullable=True),
        sa.Column('geographic_coverage', sa.JSON(), nullable=True),
        sa.Column('temporal_coverage', sa.JSON(), nullable=True),
        sa.Column('data_resolution', sa.String(length=50), nullable=True),
        sa.Column('data_latency', sa.String(length=50), nullable=True),
        sa.Column('data_quality_score', sa.Float(), nullable=True),
        sa.Column('cost_per_request', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('monthly_cost', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_premium', sa.Boolean(), nullable=True),
        sa.Column('requires_approval', sa.Boolean(), nullable=True),
        sa.Column('total_requests', sa.Integer(), nullable=True),
        sa.Column('successful_requests', sa.Integer(), nullable=True),
        sa.Column('last_request', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_alternative_data_sources_id'), 'alternative_data_sources', ['id'], unique=False)

    # 创建另类数据表
    op.create_table('alternative_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('source_id', sa.Integer(), nullable=False),
        sa.Column('data_type', sa.String(length=50), nullable=False),
        sa.Column('data_subtype', sa.String(length=50), nullable=True),
        sa.Column('data_identifier', sa.String(length=200), nullable=True),
        sa.Column('raw_data', sa.JSON(), nullable=True),
        sa.Column('processed_data', sa.JSON(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('location', sa.String(length=200), nullable=True),
        sa.Column('coordinates', sa.JSON(), nullable=True),
        sa.Column('geographic_scope', sa.String(length=100), nullable=True),
        sa.Column('related_symbols', sa.JSON(), nullable=True),
        sa.Column('related_sectors', sa.JSON(), nullable=True),
        sa.Column('impact_score', sa.Float(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('completeness', sa.Float(), nullable=True),
        sa.Column('accuracy', sa.Float(), nullable=True),
        sa.Column('timeliness', sa.Float(), nullable=True),
        sa.Column('processing_status', sa.String(length=20), nullable=True),
        sa.Column('validation_status', sa.String(length=20), nullable=True),
        sa.Column('access_count', sa.Integer(), nullable=True),
        sa.Column('last_accessed', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_id'], ['alternative_data_sources.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_alternative_data_id'), 'alternative_data', ['id'], unique=False)
    op.create_index(op.f('ix_alternative_data_source_id'), 'alternative_data', ['source_id'], unique=False)
    op.create_index(op.f('ix_alternative_data_timestamp'), 'alternative_data', ['timestamp'], unique=False)

    # 创建多模态信号表
    op.create_table('multimodal_signals',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('signal_name', sa.String(length=100), nullable=False),
        sa.Column('signal_type', sa.String(length=50), nullable=False),
        sa.Column('signal_category', sa.String(length=50), nullable=True),
        sa.Column('target_symbol', sa.String(length=20), nullable=False),
        sa.Column('target_sector', sa.String(length=50), nullable=True),
        sa.Column('target_market', sa.String(length=20), nullable=True),
        sa.Column('signal_value', sa.Float(), nullable=False),
        sa.Column('signal_strength', sa.Float(), nullable=False),
        sa.Column('confidence', sa.Float(), nullable=False),
        sa.Column('signal_timestamp', sa.DateTime(), nullable=False),
        sa.Column('valid_from', sa.DateTime(), nullable=True),
        sa.Column('valid_until', sa.DateTime(), nullable=True),
        sa.Column('data_sources', sa.JSON(), nullable=True),
        sa.Column('source_weights', sa.JSON(), nullable=True),
        sa.Column('calculation_method', sa.String(length=100), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('feature_importance', sa.JSON(), nullable=True),
        sa.Column('historical_accuracy', sa.Float(), nullable=True),
        sa.Column('hit_rate', sa.Float(), nullable=True),
        sa.Column('false_positive_rate', sa.Float(), nullable=True),
        sa.Column('market_impact', sa.Float(), nullable=True),
        sa.Column('price_correlation', sa.Float(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_validated', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_multimodal_signals_id'), 'multimodal_signals', ['id'], unique=False)
    op.create_index(op.f('ix_multimodal_signals_signal_timestamp'), 'multimodal_signals', ['signal_timestamp'], unique=False)
    op.create_index(op.f('ix_multimodal_signals_target_symbol'), 'multimodal_signals', ['target_symbol'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_multimodal_signals_target_symbol'), table_name='multimodal_signals')
    op.drop_index(op.f('ix_multimodal_signals_signal_timestamp'), table_name='multimodal_signals')
    op.drop_index(op.f('ix_multimodal_signals_id'), table_name='multimodal_signals')
    op.drop_table('multimodal_signals')
    
    op.drop_index(op.f('ix_alternative_data_timestamp'), table_name='alternative_data')
    op.drop_index(op.f('ix_alternative_data_source_id'), table_name='alternative_data')
    op.drop_index(op.f('ix_alternative_data_id'), table_name='alternative_data')
    op.drop_table('alternative_data')
    
    op.drop_index(op.f('ix_alternative_data_sources_id'), table_name='alternative_data_sources')
    op.drop_table('alternative_data_sources')
    
    op.drop_index(op.f('ix_social_sentiments_post_id'), table_name='social_sentiments')
    op.drop_index(op.f('ix_social_sentiments_id'), table_name='social_sentiments')
    op.drop_table('social_sentiments')
    
    op.drop_index(op.f('ix_social_media_posts_source_id'), table_name='social_media_posts')
    op.drop_index(op.f('ix_social_media_posts_posted_at'), table_name='social_media_posts')
    op.drop_index(op.f('ix_social_media_posts_id'), table_name='social_media_posts')
    op.drop_table('social_media_posts')
    
    op.drop_index(op.f('ix_social_media_sources_id'), table_name='social_media_sources')
    op.drop_table('social_media_sources')
    
    op.drop_index(op.f('ix_news_entity_extractions_id'), table_name='news_entity_extractions')
    op.drop_index(op.f('ix_news_entity_extractions_article_id'), table_name='news_entity_extractions')
    op.drop_table('news_entity_extractions')
    
    op.drop_index(op.f('ix_news_sentiments_id'), table_name='news_sentiments')
    op.drop_index(op.f('ix_news_sentiments_article_id'), table_name='news_sentiments')
    op.drop_table('news_sentiments')
    
    op.drop_index(op.f('ix_news_articles_source_id'), table_name='news_articles')
    op.drop_index(op.f('ix_news_articles_published_at'), table_name='news_articles')
    op.drop_index(op.f('ix_news_articles_id'), table_name='news_articles')
    op.drop_table('news_articles')
    
    op.drop_index(op.f('ix_news_sources_id'), table_name='news_sources')
    op.drop_table('news_sources')
