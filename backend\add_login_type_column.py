#!/usr/bin/env python3
"""
手动添加login_type字段到jqdata_configs表
"""

import sqlite3
import os

def add_login_type_column():
    """添加login_type字段到jqdata_configs表"""
    db_path = "quantitative_trading.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(jqdata_configs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'login_type' in columns:
            print("login_type字段已存在")
        else:
            # 添加login_type字段
            cursor.execute("""
                ALTER TABLE jqdata_configs 
                ADD COLUMN login_type VARCHAR(20) DEFAULT 'email'
            """)
            print("成功添加login_type字段")
        
        # 提交更改
        conn.commit()
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(jqdata_configs)")
        columns = cursor.fetchall()
        print("\n当前jqdata_configs表结构:")
        for column in columns:
            print(f"  {column[1]} ({column[2]})")
        
    except Exception as e:
        print(f"添加字段失败: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_login_type_column()
