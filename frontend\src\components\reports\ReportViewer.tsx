'use client';

/**
 * 报告查看器组件
 * 
 * 显示投资组合、策略、市场分析报告
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  Button,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Table,
  Divider,
  Timeline,
  message
} from 'antd';
import {
  FileTextOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  PrinterOutlined,
  ReloadOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import * as echarts from 'echarts';

import { apiClient } from '@/services/api';
import { EnhancedPieChart } from '@/components/charts/FinancialCharts';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface ReportData {
  report_info: {
    title: string;
    type: string;
    generated_at: string;
    period?: string;
  };
  executive_summary?: any;
  performance_analysis?: any;
  holdings_analysis?: any;
  risk_analysis?: any;
  market_outlook?: any;
  recommendations?: any;
  strategy_overview?: any;
  backtest_results?: any;
  market_overview?: any;
  sector_analysis?: any;
}

interface ReportViewerProps {
  reportType: 'portfolio' | 'strategy' | 'market';
  reportId: number;
  onClose?: () => void;
}

export const ReportViewer: React.FC<ReportViewerProps> = ({
  reportType,
  reportId,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [activeTab, setActiveTab] = useState('summary');

  useEffect(() => {
    loadReport();
  }, [reportType, reportId]);

  const loadReport = async () => {
    setLoading(true);
    try {
      let endpoint = '';
      switch (reportType) {
        case 'portfolio':
          endpoint = `/api/v1/reports/display/portfolio/${reportId}`;
          break;
        case 'strategy':
          endpoint = `/api/v1/reports/display/strategy/${reportId}`;
          break;
        case 'market':
          endpoint = `/api/v1/reports/display/market`;
          break;
      }

      const response = await apiClient.post(endpoint);
      if (response.data.success) {
        setReportData(response.data.data.report_data);
      } else {
        message.error('报告加载失败');
      }
    } catch (error: any) {
      console.error('Report loading error:', error);
      message.error('报告加载失败');
    } finally {
      setLoading(false);
    }
  };

  const renderExecutiveSummary = () => {
    if (!reportData?.executive_summary) return null;

    const { key_highlights, performance_summary, key_insights } = reportData.executive_summary;

    return (
      <div className="space-y-6">
        {/* 关键亮点 */}
        <Card title="关键亮点" size="small">
          <Row gutter={[16, 16]}>
            {key_highlights?.map((highlight: string, index: number) => (
              <Col xs={24} sm={12} key={index}>
                <div className="flex items-center space-x-2">
                  <TrophyOutlined className="text-yellow-500" />
                  <Text>{highlight}</Text>
                </div>
              </Col>
            ))}
          </Row>
        </Card>

        {/* 绩效摘要 */}
        {performance_summary && (
          <Card title="绩效摘要" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="总收益率"
                  value={performance_summary.total_return}
                  suffix="%"
                  valueStyle={{ color: performance_summary.total_return >= 0 ? '#52c41a' : '#ff4d4f' }}
                  prefix={performance_summary.total_return >= 0 ? <RiseOutlined /> : <FallOutlined />}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="基准收益率"
                  value={performance_summary.benchmark_return}
                  suffix="%"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="超额收益"
                  value={performance_summary.excess_return}
                  suffix="%"
                  valueStyle={{ color: performance_summary.excess_return >= 0 ? '#52c41a' : '#ff4d4f' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="波动率"
                  value={performance_summary.volatility}
                  suffix="%"
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* 关键洞察 */}
        <Card title="关键洞察" size="small">
          <div className="space-y-2">
            {key_insights?.map((insight: string, index: number) => (
              <div key={index} className="flex items-start space-x-2">
                <InfoCircleOutlined className="text-blue-500 mt-1" />
                <Text>{insight}</Text>
              </div>
            ))}
          </div>
        </Card>
      </div>
    );
  };

  const renderPerformanceAnalysis = () => {
    if (!reportData?.performance_analysis) return null;

    const { return_analysis, risk_metrics, benchmark_comparison } = reportData.performance_analysis;

    return (
      <div className="space-y-6">
        {/* 收益分析 */}
        {return_analysis && (
          <Card title="收益分析" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <div className="space-y-4">
                  <Statistic
                    title="年化收益率"
                    value={return_analysis.annualized_return}
                    suffix="%"
                    precision={2}
                  />
                  <div>
                    <Text type="secondary">月度收益率趋势</Text>
                    <div className="mt-2">
                      {return_analysis.monthly_returns?.slice(0, 6).map((item: any, index: number) => (
                        <div key={index} className="flex justify-between items-center py-1">
                          <Text>{item.month}</Text>
                          <Space>
                            <Text style={{ color: item.return >= 0 ? '#52c41a' : '#ff4d4f' }}>
                              {item.return > 0 ? '+' : ''}{item.return}%
                            </Text>
                            <Text type="secondary">({item.benchmark}%)</Text>
                          </Space>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Col>
              <Col xs={24} lg={12}>
                <div className="space-y-4">
                  <Text strong>累计收益率对比</Text>
                  <div className="h-64">
                    <EnhancedPieChart
                      data={[
                        { name: '股票收益', value: 180000, return: 12.5 },
                        { name: '债券收益', value: 45000, return: 4.2 },
                        { name: '现金收益', value: 8000, return: 2.1 },
                        { name: '其他收益', value: 15000, return: 8.7 }
                      ]}
                      title="收益构成分析"
                      height={240}
                      showStats={false}
                    />
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        )}

        {/* 风险指标 */}
        {risk_metrics && (
          <Card title="风险指标" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="最大回撤"
                  value={Math.abs(risk_metrics.max_drawdown)}
                  suffix="%"
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="夏普比率"
                  value={risk_metrics.sharpe_ratio}
                  precision={2}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="索提诺比率"
                  value={risk_metrics.sortino_ratio}
                  precision={2}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={8} md={6}>
                <Statistic
                  title="VaR (95%)"
                  value={Math.abs(risk_metrics.var_95)}
                  suffix="%"
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
            </Row>
          </Card>
        )}

        {/* 基准比较 */}
        {benchmark_comparison && (
          <Card title="基准比较" size="small">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Text>基准指数: {benchmark_comparison.benchmark}</Text>
                <Tag color="blue">跟踪误差: {benchmark_comparison.tracking_error}%</Tag>
              </div>
              <Row gutter={[16, 16]}>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="组合收益"
                    value={benchmark_comparison.portfolio_return}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="基准收益"
                    value={benchmark_comparison.benchmark_return}
                    suffix="%"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="信息比率"
                    value={benchmark_comparison.information_ratio}
                    precision={2}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="上行捕获"
                    value={benchmark_comparison.up_capture}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
              </Row>
            </div>
          </Card>
        )}
      </div>
    );
  };

  const renderHoldingsAnalysis = () => {
    if (!reportData?.holdings_analysis) return null;

    const { asset_allocation, top_holdings, concentration_risk } = reportData.holdings_analysis;

    return (
      <div className="space-y-6">
        {/* 资产配置 */}
        {asset_allocation && (
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="行业配置" size="small">
                <div className="space-y-3">
                  {asset_allocation.by_sector?.map((item: any, index: number) => (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-1">
                        <Text>{item.sector}</Text>
                        <Text strong>{item.weight}%</Text>
                      </div>
                      <Progress
                        percent={item.weight}
                        showInfo={false}
                        strokeColor={index % 2 === 0 ? '#1890ff' : '#52c41a'}
                      />
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="市值分布" size="small">
                <div className="space-y-3">
                  {asset_allocation.by_market_cap?.map((item: any, index: number) => (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-1">
                        <Text>{item.category}</Text>
                        <Text strong>{item.weight}%</Text>
                      </div>
                      <Progress
                        percent={item.weight}
                        showInfo={false}
                        strokeColor={['#faad14', '#fa8c16', '#ff7a45'][index]}
                      />
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>
        )}

        {/* 重仓股票 */}
        {top_holdings && (
          <Card title="重仓股票" size="small">
            <Table
              dataSource={top_holdings}
              rowKey="symbol"
              size="small"
              pagination={false}
              columns={[
                {
                  title: '股票代码',
                  dataIndex: 'symbol',
                  key: 'symbol',
                  width: 120
                },
                {
                  title: '股票名称',
                  dataIndex: 'name',
                  key: 'name'
                },
                {
                  title: '持仓数量',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  render: (value: number) => value.toLocaleString()
                },
                {
                  title: '成本价',
                  dataIndex: 'cost_price',
                  key: 'cost_price',
                  render: (value: number) => `¥${value.toFixed(2)}`
                },
                {
                  title: '现价',
                  dataIndex: 'current_price',
                  key: 'current_price',
                  render: (value: number) => `¥${value.toFixed(2)}`
                },
                {
                  title: '市值',
                  dataIndex: 'market_value',
                  key: 'market_value',
                  render: (value: number) => `¥${value.toLocaleString()}`
                },
                {
                  title: '收益率',
                  dataIndex: 'return_rate',
                  key: 'return_rate',
                  render: (value: number) => (
                    <Text style={{ color: value >= 0 ? '#52c41a' : '#ff4d4f' }}>
                      {value > 0 ? '+' : ''}{value.toFixed(2)}%
                    </Text>
                  )
                }
              ]}
            />
          </Card>
        )}

        {/* 集中度风险 */}
        {concentration_risk && (
          <Card title="集中度风险" size="small">
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={6}>
                <Statistic
                  title="前5大持仓占比"
                  value={concentration_risk.top_5_weight}
                  suffix="%"
                  valueStyle={{ 
                    color: concentration_risk.top_5_weight > 60 ? '#ff4d4f' : '#52c41a' 
                  }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="前10大持仓占比"
                  value={concentration_risk.top_10_weight}
                  suffix="%"
                  valueStyle={{ 
                    color: concentration_risk.top_10_weight > 80 ? '#ff4d4f' : '#52c41a' 
                  }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <Statistic
                  title="赫芬达尔指数"
                  value={concentration_risk.herfindahl_index}
                  precision={3}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col xs={12} sm={6}>
                <div>
                  <Text type="secondary">风险等级</Text>
                  <div className="mt-1">
                    <Tag color={
                      concentration_risk.risk_level === '低' ? 'green' :
                      concentration_risk.risk_level === '中等' ? 'orange' : 'red'
                    }>
                      {concentration_risk.risk_level}
                    </Tag>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!reportData) {
    return (
      <Alert
        message="报告加载失败"
        description="无法加载报告数据，请重试"
        type="error"
        showIcon
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* 报告头部 */}
      <Card>
        <div className="flex justify-between items-start">
          <div>
            <Title level={3} className="!mb-2">
              <Space>
                <FileTextOutlined />
                {reportData.report_info.title}
              </Space>
            </Title>
            <div className="space-x-4">
              <Text type="secondary">
                生成时间: {new Date(reportData.report_info.generated_at).toLocaleString()}
              </Text>
              {reportData.report_info.period && (
                <Text type="secondary">
                  分析期间: {reportData.report_info.period}
                </Text>
              )}
            </div>
          </div>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={loadReport}>
              刷新
            </Button>
            <Button icon={<ShareAltOutlined />}>
              分享
            </Button>
            <Button icon={<PrinterOutlined />}>
              打印
            </Button>
            <Button type="primary" icon={<DownloadOutlined />}>
              导出
            </Button>
          </Space>
        </div>
      </Card>

      {/* 报告内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="执行摘要" key="summary">
            {renderExecutiveSummary()}
          </TabPane>
          <TabPane tab="绩效分析" key="performance">
            {renderPerformanceAnalysis()}
          </TabPane>
          <TabPane tab="持仓分析" key="holdings">
            {renderHoldingsAnalysis()}
          </TabPane>
          <TabPane tab="风险分析" key="risk">
            <div className="text-center py-12">
              <Text type="secondary">风险分析内容开发中...</Text>
            </div>
          </TabPane>
          <TabPane tab="投资建议" key="recommendations">
            <div className="text-center py-12">
              <Text type="secondary">投资建议内容开发中...</Text>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </motion.div>
  );
};
