!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("react"),require("zustand/traditional")):"function"==typeof define&&define.amd?define(["react","zustand/traditional"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).zustandContext=t(e.React,e.traditional)}(this,(function(e,t){"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},r.apply(this,arguments)}var n=e.createElement,o=e.createContext,i=e.useContext,u=e.useMemo,a=e.useRef;return function(){var e=o(void 0);return{Provider:function(t){var r=t.createStore,o=t.children,i=a();return i.current||(i.current=r()),n(e.Provider,{value:i.current},o)},useStore:function(r,n){var o=i(e);if(!o)throw new Error("Seems like you have not used zustand provider as an ancestor.");return t.useStoreWithEqualityFn(o,r,n)},useStoreApi:function(){var t=i(e);if(!t)throw new Error("Seems like you have not used zustand provider as an ancestor.");return u((function(){return r({},t)}),[t])}}}}));
