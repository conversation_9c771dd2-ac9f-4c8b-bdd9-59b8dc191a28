"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/overview/page",{

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RiseOutlined.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/RiseOutlined.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n// This icon file is generated automatically.\nvar RiseOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M917 211.1l-199.2 24c-6.6.8-9.4 8.9-4.7 13.6l59.3 59.3-226 226-101.8-101.7c-6.3-6.3-16.4-6.2-22.6 0L100.3 754.1a8.03 8.03 0 000 11.3l45 45.2c3.1 3.1 8.2 3.1 11.3 0L433.3 534 535 635.7c6.3 6.2 16.4 6.2 22.6 0L829 364.5l59.3 59.3a8.01 8.01 0 0013.6-4.7l24-199.2c.7-5.1-3.7-9.5-8.9-8.8z\" } }] }, \"name\": \"rise\", \"theme\": \"outlined\" };\n/* harmony default export */ __webpack_exports__[\"default\"] = (RiseOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1Jpc2VPdXRsaW5lZC5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7QUFDQSxxQkFBcUIsVUFBVSx5QkFBeUIsa0RBQWtELGlCQUFpQiwwQkFBMEIsc1NBQXNTLEdBQUc7QUFDOWIsK0RBQWUsWUFBWSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1Jpc2VPdXRsaW5lZC5qcz83Mjg5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFJpc2VPdXRsaW5lZCA9IHsgXCJpY29uXCI6IHsgXCJ0YWdcIjogXCJzdmdcIiwgXCJhdHRyc1wiOiB7IFwidmlld0JveFwiOiBcIjY0IDY0IDg5NiA4OTZcIiwgXCJmb2N1c2FibGVcIjogXCJmYWxzZVwiIH0sIFwiY2hpbGRyZW5cIjogW3sgXCJ0YWdcIjogXCJwYXRoXCIsIFwiYXR0cnNcIjogeyBcImRcIjogXCJNOTE3IDIxMS4xbC0xOTkuMiAyNGMtNi42LjgtOS40IDguOS00LjcgMTMuNmw1OS4zIDU5LjMtMjI2IDIyNi0xMDEuOC0xMDEuN2MtNi4zLTYuMy0xNi40LTYuMi0yMi42IDBMMTAwLjMgNzU0LjFhOC4wMyA4LjAzIDAgMDAwIDExLjNsNDUgNDUuMmMzLjEgMy4xIDguMiAzLjEgMTEuMyAwTDQzMy4zIDUzNCA1MzUgNjM1LjdjNi4zIDYuMiAxNi40IDYuMiAyMi42IDBMODI5IDM2NC41bDU5LjMgNTkuM2E4LjAxIDguMDEgMCAwMDEzLjYtNC43bDI0LTE5OS4yYy43LTUuMS0zLjctOS41LTguOS04Ljh6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJyaXNlXCIsIFwidGhlbWVcIjogXCJvdXRsaW5lZFwiIH07XG5leHBvcnQgZGVmYXVsdCBSaXNlT3V0bGluZWQ7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RiseOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RiseOutlined.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/RiseOutlined.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(app-pages-browser)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_RiseOutlined__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/RiseOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RiseOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\n\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nvar RiseOutlined = function RiseOutlined(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_RiseOutlined__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    }));\n};\n_c = RiseOutlined;\n/**![rise](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxNyAyMTEuMWwtMTk5LjIgMjRjLTYuNi44LTkuNCA4LjktNC43IDEzLjZsNTkuMyA1OS4zLTIyNiAyMjYtMTAxLjgtMTAxLjdjLTYuMy02LjMtMTYuNC02LjItMjIuNiAwTDEwMC4zIDc1NC4xYTguMDMgOC4wMyAwIDAwMCAxMS4zbDQ1IDQ1LjJjMy4xIDMuMSA4LjIgMy4xIDExLjMgMEw0MzMuMyA1MzQgNTM1IDYzNS43YzYuMyA2LjIgMTYuNCA2LjIgMjIuNiAwTDgyOSAzNjQuNWw1OS4zIDU5LjNhOC4wMSA4LjAxIDAgMDAxMy42LTQuN2wyNC0xOTkuMmMuNy01LjEtMy43LTkuNS04LjktOC44eiIgLz48L3N2Zz4=) */ var RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(RiseOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = \"RiseOutlined\";\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"RiseOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy9lcy9pY29ucy9SaXNlT3V0bGluZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBEO0FBQzFELG9DQUFvQztBQUNwQywyQkFBMkI7QUFFSTtBQUN5QztBQUMxQjtBQUM5QyxJQUFJSSxlQUFlLFNBQVNBLGFBQWFDLEtBQUssRUFBRUMsR0FBRztJQUNqRCxPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUNFLDREQUFRQSxFQUFFSCw4RUFBUUEsQ0FBQyxDQUFDLEdBQUdLLE9BQU87UUFDcEVDLEtBQUtBO1FBQ0xFLE1BQU1OLGlGQUFlQTtJQUN2QjtBQUNGO0tBTElFO0FBT0osMmxCQUEybEIsR0FDM2xCLElBQUlLLFVBQVUsV0FBVyxHQUFFUiw2Q0FBZ0IsQ0FBQ0c7O0FBQzVDLElBQUlPLElBQXlCLEVBQWM7SUFDekNGLFFBQVFHLFdBQVcsR0FBRztBQUN4QjtBQUNBLCtEQUFlSCxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy9lcy9pY29ucy9SaXNlT3V0bGluZWQuanM/N2Q5MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbi8vIEdFTkVSQVRFIEJZIC4vc2NyaXB0cy9nZW5lcmF0ZS50c1xuLy8gRE9OIE5PVCBFRElUIElUIE1BTlVBTExZXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSaXNlT3V0bGluZWRTdmcgZnJvbSBcIkBhbnQtZGVzaWduL2ljb25zLXN2Zy9lcy9hc24vUmlzZU91dGxpbmVkXCI7XG5pbXBvcnQgQW50ZEljb24gZnJvbSBcIi4uL2NvbXBvbmVudHMvQW50ZEljb25cIjtcbnZhciBSaXNlT3V0bGluZWQgPSBmdW5jdGlvbiBSaXNlT3V0bGluZWQocHJvcHMsIHJlZikge1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoQW50ZEljb24sIF9leHRlbmRzKHt9LCBwcm9wcywge1xuICAgIHJlZjogcmVmLFxuICAgIGljb246IFJpc2VPdXRsaW5lZFN2Z1xuICB9KSk7XG59O1xuXG4vKiohW3Jpc2VdKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU5UQWlJR2hsYVdkb2REMGlOVEFpSUdacGJHdzlJaU5qWVdOaFkyRWlJSFpwWlhkQ2IzZzlJalkwSURZMElEZzVOaUE0T1RZaUlHWnZZM1Z6WVdKc1pUMGlabUZzYzJVaUlIaHRiRzV6UFNKb2RIUndPaTh2ZDNkM0xuY3pMbTl5Wnk4eU1EQXdMM04yWnlJK1BIQmhkR2dnWkQwaVRUa3hOeUF5TVRFdU1Xd3RNVGs1TGpJZ01qUmpMVFl1Tmk0NExUa3VOQ0E0TGprdE5DNDNJREV6TGpac05Ua3VNeUExT1M0ekxUSXlOaUF5TWpZdE1UQXhMamd0TVRBeExqZGpMVFl1TXkwMkxqTXRNVFl1TkMwMkxqSXRNakl1TmlBd1RERXdNQzR6SURjMU5DNHhZVGd1TURNZ09DNHdNeUF3SURBd01DQXhNUzR6YkRRMUlEUTFMakpqTXk0eElETXVNU0E0TGpJZ015NHhJREV4TGpNZ01FdzBNek11TXlBMU16UWdOVE0xSURZek5TNDNZell1TXlBMkxqSWdNVFl1TkNBMkxqSWdNakl1TmlBd1REZ3lPU0F6TmpRdU5XdzFPUzR6SURVNUxqTmhPQzR3TVNBNExqQXhJREFnTURBeE15NDJMVFF1TjJ3eU5DMHhPVGt1TW1NdU55MDFMakV0TXk0M0xUa3VOUzA0TGprdE9DNDRlaUlnTHo0OEwzTjJaejQ9KSAqL1xudmFyIFJlZkljb24gPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZihSaXNlT3V0bGluZWQpO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgUmVmSWNvbi5kaXNwbGF5TmFtZSA9ICdSaXNlT3V0bGluZWQnO1xufVxuZXhwb3J0IGRlZmF1bHQgUmVmSWNvbjsiXSwibmFtZXMiOlsiX2V4dGVuZHMiLCJSZWFjdCIsIlJpc2VPdXRsaW5lZFN2ZyIsIkFudGRJY29uIiwiUmlzZU91dGxpbmVkIiwicHJvcHMiLCJyZWYiLCJjcmVhdGVFbGVtZW50IiwiaWNvbiIsIlJlZkljb24iLCJmb3J3YXJkUmVmIiwicHJvY2VzcyIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RiseOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/overview/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/overview/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OverviewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Empty,Row,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/empty/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DollarOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RiseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,DashboardOutlined,DollarOutlined,RiseOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nfunction OverviewPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isAuthenticated } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore)();\n    if (!isAuthenticated) {\n        return null;\n    }\n    const stats = [\n        {\n            title: \"API配额\",\n            value: (user === null || user === void 0 ? void 0 : user.api_quota_used_today) || 0,\n            total: (user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000,\n            suffix: \"/ \".concat((user === null || user === void 0 ? void 0 : user.api_quota_daily) || 1000),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            color: \"#1890ff\"\n        },\n        {\n            title: \"投资组合价值\",\n            value: 0,\n            prefix: \"\\xa5\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, this),\n            color: \"#52c41a\"\n        },\n        {\n            title: \"总收益率\",\n            value: 0,\n            suffix: \"%\",\n            precision: 2,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, this),\n            color: \"#faad14\"\n        },\n        {\n            title: \"活跃策略\",\n            value: 0,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, this),\n            color: \"#722ed1\"\n        }\n    ];\n    const quickActions = [\n        {\n            title: \"配置JQData\",\n            description: \"设置API密钥以获取市场数据\",\n            action: ()=>router.push(\"/dashboard/settings/jqdata\"),\n            type: \"primary\"\n        },\n        {\n            title: \"创建策略\",\n            description: \"开始您的第一个量化策略\",\n            action: ()=>router.push(\"/dashboard/strategy/editor\"),\n            type: \"default\"\n        },\n        {\n            title: \"查看市场\",\n            description: \"浏览实时市场数据\",\n            action: ()=>router.push(\"/dashboard/market\"),\n            type: \"default\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_DashboardOutlined_DollarOutlined_RiseOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"mr-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            \"数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        className: \"text-lg\",\n                        children: [\n                            \"欢迎回来，\",\n                            user === null || user === void 0 ? void 0 : user.username,\n                            \"！这里是您的量化交易数据概览\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请先配置JQData账号以获取实时市场数据和使用量化功能\",\n                type: \"warning\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: \"small\",\n                    type: \"primary\",\n                    onClick: ()=>router.push(\"/dashboard/settings/jqdata\"),\n                    children: \"立即配置\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, void 0),\n                showIcon: true,\n                closable: true,\n                className: \"mb-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                className: \"mb-8\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        xs: 24,\n                        sm: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                title: stat.title,\n                                value: stat.value,\n                                precision: stat.precision,\n                                prefix: stat.prefix,\n                                suffix: stat.suffix,\n                                valueStyle: {\n                                    color: stat.color\n                                },\n                                prefix: stat.icon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"快速操作\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: quickActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: \"small\",\n                                hoverable: true,\n                                className: \"text-center cursor-pointer\",\n                                onClick: action.action,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 4,\n                                        className: \"!mb-2\",\n                                        children: action.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        className: \"block mb-4\",\n                                        children: action.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        type: action.type,\n                                        children: \"开始使用\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"最近活动\",\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    description: \"暂无最近活动\",\n                    image: _barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"].PRESENTED_IMAGE_SIMPLE,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        type: \"primary\",\n                        onClick: ()=>router.push(\"/dashboard/strategy/editor\"),\n                        children: \"创建第一个策略\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                title: \"系统状态\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    gutter: [\n                        16,\n                        16\n                    ],\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"后端服务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"正常运行\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"JQData连接\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"未配置\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Empty_Row_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            xs: 24,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-green-500 text-2xl mb-2\",\n                                        children: \"●\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium\",\n                                        children: \"数据库\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"连接正常\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\overview\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(OverviewPage, \"yAZ73rp5Tgjs3hq8qIn2wTHK3JI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthStore\n    ];\n});\n_c = OverviewPage;\nvar _c;\n$RefreshReg$(_c, \"OverviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/overview/page.tsx\n"));

/***/ })

});