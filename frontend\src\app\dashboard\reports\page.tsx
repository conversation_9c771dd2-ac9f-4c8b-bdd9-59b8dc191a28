'use client';

/**
 * 报告管理页面
 * 
 * 管理和查看各类投资分析报告
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Select,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  message,
  Tooltip
} from 'antd';
import {
  FileTextOutlined,
  PlusOutlined,
  EyeOutlined,
  DownloadOutlined,
  DeleteOutlined,
  Bar<PERSON>hartOutlined,
  TrophyOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { ReportViewer } from '@/components/reports/ReportViewer';
import { apiClient } from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface Report {
  id: number;
  title: string;
  type: 'portfolio' | 'strategy' | 'market';
  template: string;
  generated_at: string;
  status: string;
  file_size: string;
  page_count: number;
}

function ReportsContent() {
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState<Report[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [viewerVisible, setViewerVisible] = useState(false);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    setLoading(true);
    try {
      const response = await apiClient.get('/api/v1/reports/history');
      if (response.data.success) {
        setReports(response.data.data.reports);
      }
    } catch (error: any) {
      console.error('Load reports error:', error);
      message.error('加载报告列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateReport = async (values: any) => {
    try {
      let endpoint = '';
      const params: any = {};

      switch (values.type) {
        case 'portfolio':
          endpoint = `/api/v1/reports/display/portfolio/${values.portfolio_id || 1}`;
          params.report_type = values.template;
          break;
        case 'strategy':
          endpoint = `/api/v1/reports/display/strategy/${values.strategy_id || 1}`;
          break;
        case 'market':
          endpoint = '/api/v1/reports/display/market';
          params.market_type = values.market_type || 'overall';
          break;
      }

      const response = await apiClient.post(endpoint, null, { params });
      
      if (response.data.success) {
        message.success('报告生成成功！');
        setCreateModalVisible(false);
        form.resetFields();
        loadReports();
      } else {
        message.error('报告生成失败');
      }
    } catch (error: any) {
      console.error('Create report error:', error);
      message.error('报告生成失败');
    }
  };

  const handleViewReport = (report: Report) => {
    setSelectedReport(report);
    setViewerVisible(true);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'portfolio': return <TrophyOutlined style={{ color: '#52c41a' }} />;
      case 'strategy': return <BarChartOutlined style={{ color: '#1890ff' }} />;
      case 'market': return <LineChartOutlined style={{ color: '#faad14' }} />;
      default: return <FileTextOutlined />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'portfolio': return '投资组合';
      case 'strategy': return '策略回测';
      case 'market': return '市场分析';
      default: return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'generating': return 'processing';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'generating': return '生成中';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  const columns = [
    {
      title: '报告标题',
      key: 'title',
      render: (record: Report) => (
        <div className="flex items-center space-x-2">
          {getTypeIcon(record.type)}
          <div>
            <div className="font-medium">{record.title}</div>
            <Text type="secondary" className="text-sm">
              模板: {record.template}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color="blue">{getTypeLabel(type)}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '生成时间',
      dataIndex: 'generated_at',
      key: 'generated_at',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100
    },
    {
      title: '页数',
      dataIndex: 'page_count',
      key: 'page_count',
      width: 80,
      render: (count: number) => `${count}页`
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: Report) => (
        <Space>
          <Tooltip title="查看报告">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewReport(record)}
            />
          </Tooltip>
          <Tooltip title="下载报告">
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => message.info('下载功能开发中')}
            />
          </Tooltip>
          <Tooltip title="删除报告">
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => message.info('删除功能开发中')}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                <FileTextOutlined />
                报告管理
              </Space>
            </Title>
            <Text type="secondary" className="text-lg">
              生成和管理投资分析报告
            </Text>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            生成报告
          </Button>
        </div>
      </motion.div>

      {/* 统计概览 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总报告数"
                value={reports.length}
                suffix="份"
                valueStyle={{ color: '#1890ff' }}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="本月生成"
                value={reports.filter(r => {
                  const reportDate = new Date(r.generated_at);
                  const now = new Date();
                  return reportDate.getMonth() === now.getMonth() && 
                         reportDate.getFullYear() === now.getFullYear();
                }).length}
                suffix="份"
                valueStyle={{ color: '#52c41a' }}
                prefix={<PlusOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="投资组合报告"
                value={reports.filter(r => r.type === 'portfolio').length}
                suffix="份"
                valueStyle={{ color: '#faad14' }}
                prefix={<TrophyOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="策略报告"
                value={reports.filter(r => r.type === 'strategy').length}
                suffix="份"
                valueStyle={{ color: '#fa8c16' }}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>

      {/* 功能说明 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Alert
          message="报告功能说明"
          description="支持生成投资组合分析报告、策略回测报告和市场分析报告。报告内容包括绩效分析、风险评估、持仓分析等多个维度，帮助您全面了解投资状况。"
          type="info"
          showIcon
          closable
        />
      </motion.div>

      {/* 报告列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card title="报告列表">
          <Table
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 份报告`
            }}
          />
        </Card>
      </motion.div>

      {/* 生成报告模态框 */}
      <Modal
        title="生成新报告"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateReport}
        >
          <Form.Item
            name="type"
            label="报告类型"
            rules={[{ required: true, message: '请选择报告类型' }]}
          >
            <Select placeholder="选择报告类型">
              <Option value="portfolio">投资组合报告</Option>
              <Option value="strategy">策略回测报告</Option>
              <Option value="market">市场分析报告</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="template"
            label="报告模板"
            rules={[{ required: true, message: '请选择报告模板' }]}
          >
            <Select placeholder="选择报告模板">
              <Option value="comprehensive">综合分析报告</Option>
              <Option value="performance">绩效分析报告</Option>
              <Option value="risk">风险评估报告</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="portfolio_id"
            label="投资组合ID"
            tooltip="仅投资组合报告需要"
          >
            <Select placeholder="选择投资组合">
              <Option value={1}>主投资组合</Option>
              <Option value={2}>测试组合</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="strategy_id"
            label="策略ID"
            tooltip="仅策略报告需要"
          >
            <Select placeholder="选择策略">
              <Option value={1}>均线交叉策略</Option>
              <Option value={2}>动量策略</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 报告查看器 */}
      {selectedReport && (
        <Modal
          title="报告查看"
          open={viewerVisible}
          onCancel={() => {
            setViewerVisible(false);
            setSelectedReport(null);
          }}
          footer={null}
          width="90%"
          style={{ top: 20 }}
        >
          <ReportViewer
            reportType={selectedReport.type}
            reportId={selectedReport.id}
            onClose={() => {
              setViewerVisible(false);
              setSelectedReport(null);
            }}
          />
        </Modal>
      )}
    </div>
  );
}

export default function ReportsPage() {
  return (
    <ClientAuthWrapper>
      <ReportsContent />
    </ClientAuthWrapper>
  );
}
