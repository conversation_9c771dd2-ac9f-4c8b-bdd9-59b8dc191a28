"""
AutoML自动化机器学习相关数据模型

定义自动特征选择、超参数优化、模型选择等AutoML功能的数据结构
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class AutoMLExperiment(Base):
    """AutoML实验"""
    
    __tablename__ = "automl_experiments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 实验基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    experiment_type = Column(String(50), nullable=False)  # classification, regression, time_series
    
    # 数据配置
    dataset_source = Column(String(100))  # jqdata, upload, database
    dataset_config = Column(JSON)  # 数据集配置
    target_column = Column(String(100), nullable=False)
    feature_columns = Column(JSON)  # 特征列列表
    
    # 实验配置
    problem_type = Column(String(50))  # binary_classification, multi_classification, regression
    evaluation_metric = Column(String(50))  # accuracy, f1, roc_auc, mse, mae, etc.
    cross_validation_folds = Column(Integer, default=5)
    test_size = Column(Float, default=0.2)
    random_state = Column(Integer, default=42)
    
    # 时间限制
    max_runtime_minutes = Column(Integer, default=60)  # 最大运行时间
    max_models = Column(Integer, default=50)  # 最大模型数量
    early_stopping_rounds = Column(Integer, default=10)
    
    # AutoML配置
    enable_feature_selection = Column(Boolean, default=True)
    enable_hyperparameter_tuning = Column(Boolean, default=True)
    enable_ensemble = Column(Boolean, default=True)
    enable_stacking = Column(Boolean, default=False)
    
    # 算法选择
    included_algorithms = Column(JSON)  # 包含的算法列表
    excluded_algorithms = Column(JSON)  # 排除的算法列表
    
    # 特征工程配置
    enable_feature_engineering = Column(Boolean, default=True)
    max_feature_interactions = Column(Integer, default=2)
    enable_polynomial_features = Column(Boolean, default=False)
    enable_text_features = Column(Boolean, default=False)
    
    # 状态管理
    status = Column(String(20), default="created")  # created, running, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 进度百分比
    current_stage = Column(String(50))  # 当前阶段
    
    # 结果统计
    total_models_trained = Column(Integer, default=0)
    best_score = Column(Float)
    best_model_id = Column(Integer, ForeignKey("automl_models.id"))
    
    # 资源使用
    cpu_usage_hours = Column(Float, default=0.0)
    memory_usage_gb = Column(Float, default=0.0)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="automl_experiments")
    models = relationship("AutoMLModel", back_populates="experiment", cascade="all, delete-orphan")
    feature_selections = relationship("FeatureSelection", back_populates="experiment", cascade="all, delete-orphan")
    hyperparameter_tunings = relationship("HyperparameterTuning", back_populates="experiment", cascade="all, delete-orphan")


class AutoMLModel(Base):
    """AutoML模型"""
    
    __tablename__ = "automl_models"
    
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey("automl_experiments.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模型基本信息
    model_name = Column(String(100), nullable=False)
    algorithm = Column(String(50), nullable=False)  # random_forest, xgboost, lightgbm, etc.
    model_type = Column(String(50))  # base_model, ensemble, stacked
    
    # 模型配置
    hyperparameters = Column(JSON)  # 超参数配置
    feature_list = Column(JSON)  # 使用的特征列表
    preprocessing_steps = Column(JSON)  # 预处理步骤
    
    # 训练信息
    training_time_seconds = Column(Float)
    training_samples = Column(Integer)
    validation_samples = Column(Integer)
    
    # 性能指标
    train_score = Column(Float)
    validation_score = Column(Float)
    test_score = Column(Float)
    cross_validation_score = Column(Float)
    cross_validation_std = Column(Float)
    
    # 详细指标
    performance_metrics = Column(JSON)  # 详细性能指标
    feature_importance = Column(JSON)  # 特征重要性
    confusion_matrix = Column(JSON)  # 混淆矩阵（分类问题）
    
    # 模型复杂度
    model_size_mb = Column(Float)
    prediction_time_ms = Column(Float)  # 单次预测时间
    memory_usage_mb = Column(Float)
    
    # 模型文件
    model_path = Column(String(500))  # 模型文件路径
    model_binary = Column(LargeBinary)  # 模型二进制数据（小模型）
    
    # 状态管理
    status = Column(String(20), default="training")  # training, completed, failed
    is_best_model = Column(Boolean, default=False)
    is_deployed = Column(Boolean, default=False)
    
    # 版本控制
    model_version = Column(String(20), default="1.0")
    parent_model_id = Column(Integer, ForeignKey("automl_models.id"))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    trained_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    experiment = relationship("AutoMLExperiment", back_populates="models")
    user = relationship("User", back_populates="automl_models")
    predictions = relationship("AutoMLPrediction", back_populates="model", cascade="all, delete-orphan")


class FeatureSelection(Base):
    """特征选择记录"""
    
    __tablename__ = "feature_selections"
    
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey("automl_experiments.id"), nullable=False, index=True)
    
    # 特征选择基本信息
    selection_method = Column(String(50), nullable=False)  # univariate, rfe, lasso, tree_based, etc.
    selection_config = Column(JSON)  # 选择方法配置
    
    # 原始特征
    original_features = Column(JSON)  # 原始特征列表
    original_feature_count = Column(Integer)
    
    # 选择结果
    selected_features = Column(JSON)  # 选择的特征列表
    selected_feature_count = Column(Integer)
    feature_scores = Column(JSON)  # 特征评分
    feature_rankings = Column(JSON)  # 特征排名
    
    # 性能影响
    baseline_score = Column(Float)  # 使用所有特征的基线分数
    selected_score = Column(Float)  # 使用选择特征的分数
    score_improvement = Column(Float)  # 分数提升
    
    # 计算统计
    selection_time_seconds = Column(Float)
    memory_usage_mb = Column(Float)
    
    # 特征工程
    engineered_features = Column(JSON)  # 工程化特征
    feature_interactions = Column(JSON)  # 特征交互
    polynomial_features = Column(JSON)  # 多项式特征
    
    # 状态管理
    status = Column(String(20), default="running")  # running, completed, failed
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # 关系
    experiment = relationship("AutoMLExperiment", back_populates="feature_selections")


class HyperparameterTuning(Base):
    """超参数调优记录"""
    
    __tablename__ = "hyperparameter_tunings"
    
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey("automl_experiments.id"), nullable=False, index=True)
    
    # 调优基本信息
    algorithm = Column(String(50), nullable=False)
    tuning_method = Column(String(50), nullable=False)  # grid_search, random_search, bayesian, optuna
    tuning_config = Column(JSON)  # 调优配置
    
    # 搜索空间
    search_space = Column(JSON)  # 超参数搜索空间
    parameter_bounds = Column(JSON)  # 参数边界
    
    # 调优过程
    total_trials = Column(Integer)
    completed_trials = Column(Integer, default=0)
    failed_trials = Column(Integer, default=0)
    
    # 最佳结果
    best_parameters = Column(JSON)  # 最佳超参数
    best_score = Column(Float)  # 最佳分数
    best_trial_number = Column(Integer)
    
    # 调优历史
    trial_history = Column(JSON)  # 试验历史
    convergence_history = Column(JSON)  # 收敛历史
    parameter_importance = Column(JSON)  # 参数重要性
    
    # 性能统计
    tuning_time_seconds = Column(Float)
    average_trial_time = Column(Float)
    best_trial_time = Column(Float)
    
    # 早停信息
    early_stopping_triggered = Column(Boolean, default=False)
    early_stopping_round = Column(Integer)
    patience_counter = Column(Integer, default=0)
    
    # 状态管理
    status = Column(String(20), default="running")  # running, completed, failed, cancelled
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 关系
    experiment = relationship("AutoMLExperiment", back_populates="hyperparameter_tunings")


class AutoMLPrediction(Base):
    """AutoML预测记录"""
    
    __tablename__ = "automl_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("automl_models.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 预测基本信息
    prediction_name = Column(String(100))
    prediction_type = Column(String(50))  # single, batch, streaming
    
    # 输入数据
    input_data = Column(JSON)  # 输入数据
    input_features = Column(JSON)  # 输入特征
    data_source = Column(String(100))  # 数据来源
    
    # 预测结果
    predictions = Column(JSON)  # 预测结果
    prediction_probabilities = Column(JSON)  # 预测概率（分类问题）
    confidence_intervals = Column(JSON)  # 置信区间（回归问题）
    
    # 预测统计
    prediction_count = Column(Integer)
    prediction_time_ms = Column(Float)
    average_confidence = Column(Float)
    
    # 实际结果（如果有）
    actual_values = Column(JSON)  # 实际值
    prediction_errors = Column(JSON)  # 预测误差
    accuracy_metrics = Column(JSON)  # 准确性指标
    
    # 预测上下文
    market_conditions = Column(JSON)  # 市场条件
    external_factors = Column(JSON)  # 外部因素
    prediction_context = Column(JSON)  # 预测上下文
    
    # 状态管理
    status = Column(String(20), default="completed")  # completed, failed, pending_validation
    is_validated = Column(Boolean, default=False)
    validation_score = Column(Float)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    prediction_date = Column(DateTime, default=datetime.utcnow)
    validation_date = Column(DateTime)
    
    # 关系
    model = relationship("AutoMLModel", back_populates="predictions")
    user = relationship("User", back_populates="automl_predictions")


class ModelEnsemble(Base):
    """模型集成"""
    
    __tablename__ = "model_ensembles"
    
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey("automl_experiments.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 集成基本信息
    ensemble_name = Column(String(100), nullable=False)
    ensemble_method = Column(String(50), nullable=False)  # voting, bagging, stacking, blending
    ensemble_config = Column(JSON)  # 集成配置
    
    # 基模型
    base_model_ids = Column(JSON)  # 基模型ID列表
    model_weights = Column(JSON)  # 模型权重
    model_selection_criteria = Column(String(50))  # 模型选择标准
    
    # 集成策略
    voting_strategy = Column(String(20))  # hard, soft (for classification)
    aggregation_method = Column(String(20))  # mean, median, weighted (for regression)
    stacking_meta_learner = Column(String(50))  # 堆叠元学习器
    
    # 性能指标
    ensemble_score = Column(Float)
    individual_scores = Column(JSON)  # 个体模型分数
    score_improvement = Column(Float)  # 相对于最佳单模型的提升
    
    # 多样性指标
    model_diversity = Column(Float)  # 模型多样性
    correlation_matrix = Column(JSON)  # 模型相关性矩阵
    disagreement_measure = Column(Float)  # 分歧度量
    
    # 计算统计
    training_time_seconds = Column(Float)
    ensemble_size_mb = Column(Float)
    prediction_time_ms = Column(Float)
    
    # 状态管理
    status = Column(String(20), default="training")  # training, completed, failed
    is_deployed = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    trained_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    experiment = relationship("AutoMLExperiment")
    user = relationship("User", back_populates="model_ensembles")


class AutoMLPipeline(Base):
    """AutoML流水线"""
    
    __tablename__ = "automl_pipelines"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 流水线基本信息
    pipeline_name = Column(String(100), nullable=False)
    description = Column(Text)
    pipeline_type = Column(String(50))  # training, inference, retraining
    
    # 流水线配置
    pipeline_config = Column(JSON)  # 流水线配置
    execution_schedule = Column(String(100))  # 执行计划（cron表达式）
    
    # 流水线步骤
    pipeline_steps = Column(JSON)  # 流水线步骤定义
    step_dependencies = Column(JSON)  # 步骤依赖关系
    
    # 数据流
    input_sources = Column(JSON)  # 输入数据源
    output_destinations = Column(JSON)  # 输出目标
    data_validation_rules = Column(JSON)  # 数据验证规则
    
    # 模型管理
    model_registry = Column(JSON)  # 模型注册表
    model_versioning = Column(JSON)  # 模型版本管理
    deployment_config = Column(JSON)  # 部署配置
    
    # 监控配置
    monitoring_config = Column(JSON)  # 监控配置
    alert_rules = Column(JSON)  # 告警规则
    performance_thresholds = Column(JSON)  # 性能阈值
    
    # 执行统计
    total_executions = Column(Integer, default=0)
    successful_executions = Column(Integer, default=0)
    failed_executions = Column(Integer, default=0)
    last_execution_status = Column(String(20))
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_scheduled = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    last_executed_at = Column(DateTime)
    next_execution_at = Column(DateTime)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="automl_pipelines")
    executions = relationship("PipelineExecution", back_populates="pipeline", cascade="all, delete-orphan")


class PipelineExecution(Base):
    """流水线执行记录"""
    
    __tablename__ = "pipeline_executions"
    
    id = Column(Integer, primary_key=True, index=True)
    pipeline_id = Column(Integer, ForeignKey("automl_pipelines.id"), nullable=False, index=True)
    
    # 执行基本信息
    execution_id = Column(String(100), nullable=False, unique=True)
    trigger_type = Column(String(50))  # manual, scheduled, event
    trigger_user_id = Column(Integer, ForeignKey("users.id"))
    
    # 执行配置
    execution_config = Column(JSON)  # 执行时配置
    runtime_parameters = Column(JSON)  # 运行时参数
    
    # 执行状态
    status = Column(String(20), default="running")  # running, completed, failed, cancelled
    current_step = Column(String(100))  # 当前步骤
    progress = Column(Integer, default=0)  # 进度百分比
    
    # 执行结果
    step_results = Column(JSON)  # 各步骤结果
    output_artifacts = Column(JSON)  # 输出产物
    execution_logs = Column(Text)  # 执行日志
    
    # 性能统计
    execution_time_seconds = Column(Float)
    cpu_usage = Column(Float)
    memory_usage_mb = Column(Float)
    
    # 错误信息
    error_message = Column(Text)
    error_stack_trace = Column(Text)
    failed_step = Column(String(100))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 关系
    pipeline = relationship("AutoMLPipeline", back_populates="executions")
    trigger_user = relationship("User")
