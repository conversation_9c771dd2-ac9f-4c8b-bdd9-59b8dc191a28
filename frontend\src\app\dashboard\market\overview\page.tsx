'use client';

import React from 'react';
import { Card, Row, Col, Typography, Statistic, Empty, Button } from 'antd';
import {
  BarChartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function MarketOverviewContent() {
  const router = useRouter();

  const marketStats = [
    {
      title: '上证指数',
      value: 3234.56,
      precision: 2,
      valueStyle: { color: '#52c41a' },
      prefix: <RiseOutlined />,
      suffix: '+1.23%'
    },
    {
      title: '深证成指',
      value: 12345.67,
      precision: 2,
      valueStyle: { color: '#f5222d' },
      prefix: <FallOutlined />,
      suffix: '-0.45%'
    },
    {
      title: '创业板指',
      value: 2567.89,
      precision: 2,
      valueStyle: { color: '#52c41a' },
      prefix: <RiseOutlined />,
      suffix: '+2.10%'
    },
    {
      title: '沪深300',
      value: 4123.45,
      precision: 2,
      valueStyle: { color: '#52c41a' },
      prefix: <RiseOutlined />,
      suffix: '+0.78%'
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <BarChartOutlined className="mr-3" />
          市场概览
        </Title>
        <Text type="secondary" className="text-lg">
          实时市场指数和整体行情概况
        </Text>
      </div>

      {/* 市场指数 */}
      <Row gutter={[16, 16]} className="mb-8">
        {marketStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
                suffix={stat.suffix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 市场热点 */}
      <Card title="市场热点" className="mb-6">
        <Empty 
          description="暂无市场热点数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary">
            配置数据源
          </Button>
        </Empty>
      </Card>

      {/* 行业板块 */}
      <Card title="行业板块">
        <Empty 
          description="暂无行业板块数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary"
            onClick={() => router.push('/dashboard/settings/jqdata')}
          >
            配置JQData
          </Button>
        </Empty>
      </Card>
    </div>
  );
}

export default function MarketOverviewPage() {
  return (
    <ClientAuthWrapper>
      <MarketOverviewContent />
    </ClientAuthWrapper>
  );
}
