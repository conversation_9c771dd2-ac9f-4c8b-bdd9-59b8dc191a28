'use client';

/**
 * 404 页面
 * 
 * 当用户访问不存在的页面时显示
 */

import React from 'react';
import { Button, Result } from 'antd';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div>
        <Result
          status="404"
          title="404"
          subTitle="抱歉，您访问的页面不存在。"
          extra={
            <div className="space-x-4">
              <Button type="primary" onClick={() => router.push('/dashboard')}>
                返回首页
              </Button>
              <Button onClick={() => router.back()}>
                返回上页
              </Button>
            </div>
          }
        />
      </div>
    </div>
  );
}
