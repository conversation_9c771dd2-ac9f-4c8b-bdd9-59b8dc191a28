"""add explainability tables

Revision ID: 010
Revises: 009
Create Date: 2024-12-23 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建可解释性分析表
    op.create_table('explainability_analyses',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('model_type', sa.String(length=50), nullable=False),
        sa.Column('analysis_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('analysis_type', sa.String(length=50), nullable=False),
        sa.Column('explanation_methods', sa.JSON(), nullable=True),
        sa.Column('method_configs', sa.JSON(), nullable=True),
        sa.Column('dataset_info', sa.JSON(), nullable=True),
        sa.Column('feature_names', sa.JSON(), nullable=True),
        sa.Column('feature_types', sa.JSON(), nullable=True),
        sa.Column('target_classes', sa.JSON(), nullable=True),
        sa.Column('analysis_scope', sa.String(length=50), nullable=True),
        sa.Column('sample_size', sa.Integer(), nullable=True),
        sa.Column('instance_ids', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('current_method', sa.String(length=50), nullable=True),
        sa.Column('total_methods', sa.Integer(), nullable=True),
        sa.Column('completed_methods', sa.Integer(), nullable=True),
        sa.Column('failed_methods', sa.Integer(), nullable=True),
        sa.Column('computation_time_seconds', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('cpu_usage_percent', sa.Float(), nullable=True),
        sa.Column('has_global_explanations', sa.Boolean(), nullable=True),
        sa.Column('has_local_explanations', sa.Boolean(), nullable=True),
        sa.Column('has_feature_importance', sa.Boolean(), nullable=True),
        sa.Column('has_visualizations', sa.Boolean(), nullable=True),
        sa.Column('explanation_quality_score', sa.Float(), nullable=True),
        sa.Column('consistency_score', sa.Float(), nullable=True),
        sa.Column('stability_score', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_explainability_analyses_id'), 'explainability_analyses', ['id'], unique=False)
    op.create_index(op.f('ix_explainability_analyses_user_id'), 'explainability_analyses', ['user_id'], unique=False)
    op.create_index(op.f('ix_explainability_analyses_model_id'), 'explainability_analyses', ['model_id'], unique=False)

    # 创建SHAP解释表
    op.create_table('shap_explanations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('explainer_type', sa.String(length=50), nullable=False),
        sa.Column('explainer_config', sa.JSON(), nullable=True),
        sa.Column('explanation_scope', sa.String(length=50), nullable=True),
        sa.Column('instance_count', sa.Integer(), nullable=True),
        sa.Column('shap_values', sa.JSON(), nullable=True),
        sa.Column('base_values', sa.JSON(), nullable=True),
        sa.Column('expected_values', sa.JSON(), nullable=True),
        sa.Column('global_feature_importance', sa.JSON(), nullable=True),
        sa.Column('feature_interactions', sa.JSON(), nullable=True),
        sa.Column('summary_statistics', sa.JSON(), nullable=True),
        sa.Column('local_explanations', sa.JSON(), nullable=True),
        sa.Column('instance_predictions', sa.JSON(), nullable=True),
        sa.Column('explanation_confidence', sa.JSON(), nullable=True),
        sa.Column('summary_plot_data', sa.JSON(), nullable=True),
        sa.Column('waterfall_plot_data', sa.JSON(), nullable=True),
        sa.Column('force_plot_data', sa.JSON(), nullable=True),
        sa.Column('dependence_plot_data', sa.JSON(), nullable=True),
        sa.Column('explanation_fidelity', sa.Float(), nullable=True),
        sa.Column('explanation_stability', sa.Float(), nullable=True),
        sa.Column('computation_efficiency', sa.Float(), nullable=True),
        sa.Column('computation_time_seconds', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['explainability_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_shap_explanations_id'), 'shap_explanations', ['id'], unique=False)
    op.create_index(op.f('ix_shap_explanations_analysis_id'), 'shap_explanations', ['analysis_id'], unique=False)

    # 创建LIME解释表
    op.create_table('lime_explanations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('explainer_type', sa.String(length=50), nullable=False),
        sa.Column('explainer_config', sa.JSON(), nullable=True),
        sa.Column('num_features', sa.Integer(), nullable=True),
        sa.Column('num_samples', sa.Integer(), nullable=True),
        sa.Column('distance_metric', sa.String(length=50), nullable=True),
        sa.Column('kernel_width', sa.Float(), nullable=True),
        sa.Column('instance_explanations', sa.JSON(), nullable=True),
        sa.Column('feature_contributions', sa.JSON(), nullable=True),
        sa.Column('local_model_coefficients', sa.JSON(), nullable=True),
        sa.Column('local_model_r2', sa.JSON(), nullable=True),
        sa.Column('explanation_scores', sa.JSON(), nullable=True),
        sa.Column('feature_selection_frequency', sa.JSON(), nullable=True),
        sa.Column('explanation_plots', sa.JSON(), nullable=True),
        sa.Column('feature_importance_plots', sa.JSON(), nullable=True),
        sa.Column('stability_analysis', sa.JSON(), nullable=True),
        sa.Column('perturbation_analysis', sa.JSON(), nullable=True),
        sa.Column('computation_time_seconds', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['explainability_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_lime_explanations_id'), 'lime_explanations', ['id'], unique=False)
    op.create_index(op.f('ix_lime_explanations_analysis_id'), 'lime_explanations', ['analysis_id'], unique=False)

    # 创建特征重要性分析表
    op.create_table('feature_importance_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('importance_methods', sa.JSON(), nullable=True),
        sa.Column('method_configs', sa.JSON(), nullable=True),
        sa.Column('feature_importances', sa.JSON(), nullable=True),
        sa.Column('aggregated_importance', sa.JSON(), nullable=True),
        sa.Column('importance_rankings', sa.JSON(), nullable=True),
        sa.Column('importance_statistics', sa.JSON(), nullable=True),
        sa.Column('correlation_matrix', sa.JSON(), nullable=True),
        sa.Column('redundancy_analysis', sa.JSON(), nullable=True),
        sa.Column('recommended_features', sa.JSON(), nullable=True),
        sa.Column('feature_selection_threshold', sa.Float(), nullable=True),
        sa.Column('selection_rationale', sa.JSON(), nullable=True),
        sa.Column('importance_plots', sa.JSON(), nullable=True),
        sa.Column('correlation_heatmap', sa.JSON(), nullable=True),
        sa.Column('feature_distribution_plots', sa.JSON(), nullable=True),
        sa.Column('importance_consistency', sa.Float(), nullable=True),
        sa.Column('method_agreement', sa.Float(), nullable=True),
        sa.Column('statistical_significance', sa.JSON(), nullable=True),
        sa.Column('computation_time_seconds', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['explainability_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_feature_importance_analyses_id'), 'feature_importance_analyses', ['id'], unique=False)
    op.create_index(op.f('ix_feature_importance_analyses_analysis_id'), 'feature_importance_analyses', ['analysis_id'], unique=False)

    # 创建决策树可视化表
    op.create_table('decision_tree_visualizations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('tree_type', sa.String(length=50), nullable=True),
        sa.Column('tree_depth', sa.Integer(), nullable=True),
        sa.Column('node_count', sa.Integer(), nullable=True),
        sa.Column('leaf_count', sa.Integer(), nullable=True),
        sa.Column('tree_structure', sa.JSON(), nullable=True),
        sa.Column('node_information', sa.JSON(), nullable=True),
        sa.Column('split_conditions', sa.JSON(), nullable=True),
        sa.Column('leaf_predictions', sa.JSON(), nullable=True),
        sa.Column('decision_paths', sa.JSON(), nullable=True),
        sa.Column('path_probabilities', sa.JSON(), nullable=True),
        sa.Column('feature_usage', sa.JSON(), nullable=True),
        sa.Column('visualization_config', sa.JSON(), nullable=True),
        sa.Column('layout_parameters', sa.JSON(), nullable=True),
        sa.Column('styling_options', sa.JSON(), nullable=True),
        sa.Column('tree_plot_data', sa.JSON(), nullable=True),
        sa.Column('interactive_tree_data', sa.JSON(), nullable=True),
        sa.Column('decision_boundary_data', sa.JSON(), nullable=True),
        sa.Column('simplified_tree', sa.JSON(), nullable=True),
        sa.Column('pruning_information', sa.JSON(), nullable=True),
        sa.Column('complexity_metrics', sa.JSON(), nullable=True),
        sa.Column('tree_fidelity', sa.Float(), nullable=True),
        sa.Column('interpretability_score', sa.Float(), nullable=True),
        sa.Column('accuracy_vs_original', sa.Float(), nullable=True),
        sa.Column('generation_time_seconds', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['explainability_analyses.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_decision_tree_visualizations_id'), 'decision_tree_visualizations', ['id'], unique=False)
    op.create_index(op.f('ix_decision_tree_visualizations_analysis_id'), 'decision_tree_visualizations', ['analysis_id'], unique=False)

    # 创建解释报告表
    op.create_table('explanation_reports',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('analysis_id', sa.Integer(), nullable=False),
        sa.Column('report_name', sa.String(length=100), nullable=False),
        sa.Column('report_type', sa.String(length=50), nullable=True),
        sa.Column('target_audience', sa.String(length=50), nullable=True),
        sa.Column('executive_summary', sa.Text(), nullable=True),
        sa.Column('methodology_description', sa.Text(), nullable=True),
        sa.Column('key_findings', sa.JSON(), nullable=True),
        sa.Column('recommendations', sa.JSON(), nullable=True),
        sa.Column('model_overview', sa.JSON(), nullable=True),
        sa.Column('performance_metrics', sa.JSON(), nullable=True),
        sa.Column('data_description', sa.JSON(), nullable=True),
        sa.Column('global_insights', sa.JSON(), nullable=True),
        sa.Column('local_explanations_summary', sa.JSON(), nullable=True),
        sa.Column('feature_analysis', sa.JSON(), nullable=True),
        sa.Column('included_visualizations', sa.JSON(), nullable=True),
        sa.Column('chart_descriptions', sa.JSON(), nullable=True),
        sa.Column('visualization_insights', sa.JSON(), nullable=True),
        sa.Column('explanation_quality_assessment', sa.JSON(), nullable=True),
        sa.Column('reliability_analysis', sa.JSON(), nullable=True),
        sa.Column('limitations_discussion', sa.Text(), nullable=True),
        sa.Column('regulatory_compliance', sa.JSON(), nullable=True),
        sa.Column('audit_trail', sa.JSON(), nullable=True),
        sa.Column('documentation_standards', sa.JSON(), nullable=True),
        sa.Column('report_format', sa.String(length=20), nullable=True),
        sa.Column('report_content', sa.Text(), nullable=True),
        sa.Column('report_metadata', sa.JSON(), nullable=True),
        sa.Column('generation_config', sa.JSON(), nullable=True),
        sa.Column('template_used', sa.String(length=100), nullable=True),
        sa.Column('generation_time_seconds', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('review_status', sa.String(length=20), nullable=True),
        sa.Column('reviewer_comments', sa.Text(), nullable=True),
        sa.Column('version', sa.String(length=20), nullable=True),
        sa.Column('parent_report_id', sa.Integer(), nullable=True),
        sa.Column('is_latest_version', sa.Boolean(), nullable=True),
        sa.Column('access_level', sa.String(length=20), nullable=True),
        sa.Column('shared_with', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('published_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['analysis_id'], ['explainability_analyses.id'], ),
        sa.ForeignKeyConstraint(['parent_report_id'], ['explanation_reports.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_explanation_reports_id'), 'explanation_reports', ['id'], unique=False)
    op.create_index(op.f('ix_explanation_reports_analysis_id'), 'explanation_reports', ['analysis_id'], unique=False)

    # 创建模型可解释性指标表
    op.create_table('model_interpretability_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('model_type', sa.String(length=50), nullable=False),
        sa.Column('overall_interpretability_score', sa.Float(), nullable=True),
        sa.Column('transparency_score', sa.Float(), nullable=True),
        sa.Column('comprehensibility_score', sa.Float(), nullable=True),
        sa.Column('model_complexity', sa.Float(), nullable=True),
        sa.Column('feature_complexity', sa.Float(), nullable=True),
        sa.Column('interaction_complexity', sa.Float(), nullable=True),
        sa.Column('explanation_stability', sa.Float(), nullable=True),
        sa.Column('prediction_stability', sa.Float(), nullable=True),
        sa.Column('robustness_score', sa.Float(), nullable=True),
        sa.Column('method_consistency', sa.Float(), nullable=True),
        sa.Column('cross_validation_consistency', sa.Float(), nullable=True),
        sa.Column('temporal_consistency', sa.Float(), nullable=True),
        sa.Column('local_fidelity', sa.Float(), nullable=True),
        sa.Column('global_fidelity', sa.Float(), nullable=True),
        sa.Column('approximation_quality', sa.Float(), nullable=True),
        sa.Column('explanation_generation_time', sa.Float(), nullable=True),
        sa.Column('computational_efficiency', sa.Float(), nullable=True),
        sa.Column('scalability_score', sa.Float(), nullable=True),
        sa.Column('actionability_score', sa.Float(), nullable=True),
        sa.Column('business_relevance', sa.Float(), nullable=True),
        sa.Column('user_satisfaction', sa.Float(), nullable=True),
        sa.Column('regulatory_compliance_score', sa.Float(), nullable=True),
        sa.Column('audit_readiness', sa.Float(), nullable=True),
        sa.Column('documentation_completeness', sa.Float(), nullable=True),
        sa.Column('metrics_calculation_method', sa.JSON(), nullable=True),
        sa.Column('benchmark_comparisons', sa.JSON(), nullable=True),
        sa.Column('improvement_suggestions', sa.JSON(), nullable=True),
        sa.Column('calculated_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_model_interpretability_metrics_id'), 'model_interpretability_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_model_interpretability_metrics_user_id'), 'model_interpretability_metrics', ['user_id'], unique=False)
    op.create_index(op.f('ix_model_interpretability_metrics_model_id'), 'model_interpretability_metrics', ['model_id'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_model_interpretability_metrics_model_id'), table_name='model_interpretability_metrics')
    op.drop_index(op.f('ix_model_interpretability_metrics_user_id'), table_name='model_interpretability_metrics')
    op.drop_index(op.f('ix_model_interpretability_metrics_id'), table_name='model_interpretability_metrics')
    op.drop_table('model_interpretability_metrics')
    
    op.drop_index(op.f('ix_explanation_reports_analysis_id'), table_name='explanation_reports')
    op.drop_index(op.f('ix_explanation_reports_id'), table_name='explanation_reports')
    op.drop_table('explanation_reports')
    
    op.drop_index(op.f('ix_decision_tree_visualizations_analysis_id'), table_name='decision_tree_visualizations')
    op.drop_index(op.f('ix_decision_tree_visualizations_id'), table_name='decision_tree_visualizations')
    op.drop_table('decision_tree_visualizations')
    
    op.drop_index(op.f('ix_feature_importance_analyses_analysis_id'), table_name='feature_importance_analyses')
    op.drop_index(op.f('ix_feature_importance_analyses_id'), table_name='feature_importance_analyses')
    op.drop_table('feature_importance_analyses')
    
    op.drop_index(op.f('ix_lime_explanations_analysis_id'), table_name='lime_explanations')
    op.drop_index(op.f('ix_lime_explanations_id'), table_name='lime_explanations')
    op.drop_table('lime_explanations')
    
    op.drop_index(op.f('ix_shap_explanations_analysis_id'), table_name='shap_explanations')
    op.drop_index(op.f('ix_shap_explanations_id'), table_name='shap_explanations')
    op.drop_table('shap_explanations')
    
    op.drop_index(op.f('ix_explainability_analyses_model_id'), table_name='explainability_analyses')
    op.drop_index(op.f('ix_explainability_analyses_user_id'), table_name='explainability_analyses')
    op.drop_index(op.f('ix_explainability_analyses_id'), table_name='explainability_analyses')
    op.drop_table('explainability_analyses')
