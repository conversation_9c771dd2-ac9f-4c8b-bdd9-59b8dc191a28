# ============================================================================
# JQData量化数据平台 - Python依赖库
# ============================================================================
#
# 这是项目根目录的依赖文件，主要用于快速部署
# 详细的依赖管理请参考 backend/requirements.txt
# ============================================================================

# 核心Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
starlette==0.27.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis缓存
redis==5.0.1
aioredis==2.0.1

# JQData SDK
jqdatasdk==1.8.11

# 数据处理和分析
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4

# 技术指标库
TA-Lib==0.4.28

# 认证与安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.7

# 配置管理
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 日志记录
loguru==0.7.2

# 工具库
python-dateutil==2.8.2
pytz==2023.3

# 文件处理
openpyxl==3.1.2

# 数据验证
email-validator==2.1.0

# ============================================================================
# 安装说明
# ============================================================================
#
# 快速安装核心依赖:
#   pip install -r requirements.txt
#
# 完整后端开发环境:
#   cd backend
#   pip install -r requirements.txt
#   pip install -r requirements-dev.txt  # 开发工具
#   pip install -r requirements-full.txt # 完整功能
#
# 前端开发环境:
#   cd frontend
#   npm install
#   npm run dev
#
# 注意事项:
# 1. 建议使用Python 3.11+
# 2. TA-Lib需要预编译包或C++编译环境
# 3. JQData SDK需要有效账号
# 4. 详细依赖管理请参考backend目录下的requirements文件
# ============================================================================
