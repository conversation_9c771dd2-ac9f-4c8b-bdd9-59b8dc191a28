# ============================================================================
# 量化交易平台 - Python依赖库
# ============================================================================

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
starlette==0.27.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.1
psycopg2-binary==2.9.9

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# 数据处理和分析
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
openpyxl==3.1.2
xlsxwriter==3.1.9

# 机器学习
scikit-learn==1.3.2
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2
optuna==3.4.0
hyperopt==0.2.7
joblib==1.3.2

# 深度学习
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1
transformers==4.36.2
datasets==2.15.0
accelerate==0.25.0
tokenizers==0.15.0

# 图神经网络
torch-geometric==2.4.0
torch-scatter==2.1.2
torch-sparse==0.6.18
torch-cluster==1.6.3
torch-spline-conv==1.2.2
networkx==3.2.1
igraph==0.11.3

# 模型可解释性
shap==0.43.0
lime==*******
eli5==0.13.0

# 时间序列
statsmodels==0.14.0
arch==6.2.0
pmdarima==2.0.4
prophet==1.1.5
tsfresh==0.20.1

# 金融数据
yfinance==0.2.28
akshare==1.12.3
tushare==1.2.89
ccxt==4.1.49
alpha-vantage==2.3.1

# 技术指标
talib-binary==0.4.26
ta==0.10.2
finta==1.3

# 可视化
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
bokeh==3.3.2

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# 异步任务
celery==5.3.4
redis==5.0.1
kombu==5.3.4

# 缓存
python-redis==4.6.0
aiocache==0.12.2

# 配置管理
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# 日志
loguru==0.7.2
structlog==23.2.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 文档
mkdocs==1.5.3
mkdocs-material==9.4.8

# 监控和性能
prometheus-client==0.19.0
psutil==5.9.6
memory-profiler==0.61.0

# 工具库
click==8.1.7
rich==13.7.0
typer==0.9.0
tqdm==4.66.1
python-dateutil==2.8.2
pytz==2023.3.post1
arrow==1.3.0

# 数据验证
cerberus==1.3.5
marshmallow==3.20.1

# 文件处理
pillow==10.1.0
python-magic==0.4.27

# 网络和通信
websockets==12.0
socketio==5.10.0
python-socketio==5.10.0

# 数学和统计
sympy==1.12
patsy==0.5.4

# 并发和异步
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# 序列化
orjson==3.9.10
msgpack==1.0.7

# 环境和部署
gunicorn==21.2.0
docker==6.1.3

# 开发工具
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6
