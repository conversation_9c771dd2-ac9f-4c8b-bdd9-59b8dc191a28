'use client';

/**
 * 偏好设置页面
 *
 * 用户个性化设置，包括主题、语言、通知等偏好配置
 */

import React from 'react';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { PersonalizationSettings } from '@/components/settings/PersonalizationSettings';
function PreferencesContent() {
  return <PersonalizationSettings />;
}
export default function PreferencesPage() {
  return (
    <ClientAuthWrapper>
      <PreferencesContent />
    </ClientAuthWrapper>
  );
}


