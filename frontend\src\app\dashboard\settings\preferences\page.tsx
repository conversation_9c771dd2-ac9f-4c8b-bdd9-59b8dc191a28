'use client';

/**
 * 偏好设置页面
 * 
 * 用户个性化设置，包括主题、语言、通知等偏好配置
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Select,
  Button,
  Typography,
  Row,
  Col,
  Divider,
  Space,
  message,
  Radio,
  Slider,
  InputNumber,
  Alert,
  Tag,
} from 'antd';
import {
  BulbOutlined,
  GlobalOutlined,
  BellOutlined,
  LineChartOutlined,
  SaveOutlined,
  ReloadOutlined,
  MoonOutlined,
  SunOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { useAuth } from '@/store/auth';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  defaultChartType: string;
  defaultTimeframe: string;
  autoRefresh: boolean;
  refreshInterval: number;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    priceAlert: boolean;
    newsAlert: boolean;
    systemAlert: boolean;
  };
  trading: {
    confirmOrders: boolean;
    showRiskWarning: boolean;
    defaultOrderType: string;
    maxOrderAmount: number;
  };
  display: {
    showVolume: boolean;
    showMA: boolean;
    chartHeight: number;
    tablePageSize: number;
    compactMode: boolean;
  };
}

export default function PreferencesPage() {
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferences>({
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    numberFormat: 'zh-CN',
    defaultChartType: 'candlestick',
    defaultTimeframe: 'daily',
    autoRefresh: true,
    refreshInterval: 30,
    notifications: {
      email: true,
      push: true,
      sms: false,
      priceAlert: true,
      newsAlert: false,
      systemAlert: true,
    },
    trading: {
      confirmOrders: true,
      showRiskWarning: true,
      defaultOrderType: 'limit',
      maxOrderAmount: 100000,
    },
    display: {
      showVolume: true,
      showMA: true,
      chartHeight: 400,
      tablePageSize: 50,
      compactMode: false,
    },
  });

  // 加载用户偏好设置
  const loadPreferences = async () => {
    try {
      // 这里应该从API加载用户偏好设置
      // const response = await apiClient.get('/user/preferences');
      // setPreferences(response.data);
      
      // 暂时使用本地存储
      const saved = localStorage.getItem('user_preferences');
      if (saved) {
        const parsedPreferences = JSON.parse(saved);
        setPreferences(parsedPreferences);
        form.setFieldsValue(parsedPreferences);
      } else {
        form.setFieldsValue(preferences);
      }
    } catch (error) {
      message.error('加载偏好设置失败');
    }
  };

  // 保存偏好设置
  const handleSave = async (values: UserPreferences) => {
    try {
      setLoading(true);
      
      // 这里应该调用API保存偏好设置
      // await apiClient.put('/user/preferences', values);
      
      // 暂时保存到本地存储
      localStorage.setItem('user_preferences', JSON.stringify(values));
      setPreferences(values);
      
      message.success('偏好设置保存成功！');
    } catch (error: any) {
      message.error(error.message || '保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置为默认设置
  const handleReset = () => {
    const defaultPreferences: UserPreferences = {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      numberFormat: 'zh-CN',
      defaultChartType: 'candlestick',
      defaultTimeframe: 'daily',
      autoRefresh: true,
      refreshInterval: 30,
      notifications: {
        email: true,
        push: true,
        sms: false,
        priceAlert: true,
        newsAlert: false,
        systemAlert: true,
      },
      trading: {
        confirmOrders: true,
        showRiskWarning: true,
        defaultOrderType: 'limit',
        maxOrderAmount: 100000,
      },
      display: {
        showVolume: true,
        showMA: true,
        chartHeight: 400,
        tablePageSize: 50,
        compactMode: false,
      },
    };
    
    form.setFieldsValue(defaultPreferences);
    setPreferences(defaultPreferences);
    message.success('已重置为默认设置');
  };

  useEffect(() => {
    loadPreferences();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="!mb-2">
          偏好设置
        </Title>
        <Text type="secondary">
          个性化您的使用体验
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={preferences}
      >
        <Row gutter={[24, 24]}>
          {/* 外观设置 */}
          <Col xs={24} lg={12}>
            <Card title={
              <Space>
                <BulbOutlined />
                外观设置
              </Space>
            }>
              <Form.Item name="theme" label="主题模式">
                <Radio.Group>
                  <Radio.Button value="light">
                    <Space>
                      <SunOutlined />
                      浅色
                    </Space>
                  </Radio.Button>
                  <Radio.Button value="dark">
                    <Space>
                      <MoonOutlined />
                      深色
                    </Space>
                  </Radio.Button>
                  <Radio.Button value="auto">自动</Radio.Button>
                </Radio.Group>
              </Form.Item>

              <Form.Item name="language" label="语言">
                <Select>
                  <Option value="zh-CN">简体中文</Option>
                  <Option value="en-US">English</Option>
                </Select>
              </Form.Item>

              <Form.Item name="timezone" label="时区">
                <Select>
                  <Option value="Asia/Shanghai">北京时间 (UTC+8)</Option>
                  <Option value="America/New_York">纽约时间 (UTC-5)</Option>
                  <Option value="Europe/London">伦敦时间 (UTC+0)</Option>
                  <Option value="Asia/Tokyo">东京时间 (UTC+9)</Option>
                </Select>
              </Form.Item>

              <Form.Item name="dateFormat" label="日期格式">
                <Select>
                  <Option value="YYYY-MM-DD">2024-12-22</Option>
                  <Option value="MM/DD/YYYY">12/22/2024</Option>
                  <Option value="DD/MM/YYYY">22/12/2024</Option>
                  <Option value="YYYY年MM月DD日">2024年12月22日</Option>
                </Select>
              </Form.Item>
            </Card>
          </Col>

          {/* 图表设置 */}
          <Col xs={24} lg={12}>
            <Card title={
              <Space>
                <LineChartOutlined />
                图表设置
              </Space>
            }>
              <Form.Item name="defaultChartType" label="默认图表类型">
                <Select>
                  <Option value="candlestick">K线图</Option>
                  <Option value="line">折线图</Option>
                  <Option value="area">面积图</Option>
                </Select>
              </Form.Item>

              <Form.Item name="defaultTimeframe" label="默认时间周期">
                <Select>
                  <Option value="1m">1分钟</Option>
                  <Option value="5m">5分钟</Option>
                  <Option value="15m">15分钟</Option>
                  <Option value="30m">30分钟</Option>
                  <Option value="1h">1小时</Option>
                  <Option value="daily">日线</Option>
                  <Option value="weekly">周线</Option>
                </Select>
              </Form.Item>

              <Form.Item name={['display', 'chartHeight']} label="图表高度">
                <Slider
                  min={300}
                  max={800}
                  step={50}
                  marks={{
                    300: '300px',
                    400: '400px',
                    500: '500px',
                    600: '600px',
                    800: '800px',
                  }}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={['display', 'showVolume']} valuePropName="checked">
                    <Switch checkedChildren="显示成交量" unCheckedChildren="隐藏成交量" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['display', 'showMA']} valuePropName="checked">
                    <Switch checkedChildren="显示均线" unCheckedChildren="隐藏均线" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 通知设置 */}
          <Col xs={24} lg={12}>
            <Card title={
              <Space>
                <BellOutlined />
                通知设置
              </Space>
            }>
              <Form.Item name={['notifications', 'email']} valuePropName="checked">
                <Switch checkedChildren="邮件通知" unCheckedChildren="关闭邮件" />
              </Form.Item>

              <Form.Item name={['notifications', 'push']} valuePropName="checked">
                <Switch checkedChildren="推送通知" unCheckedChildren="关闭推送" />
              </Form.Item>

              <Form.Item name={['notifications', 'sms']} valuePropName="checked">
                <Switch checkedChildren="短信通知" unCheckedChildren="关闭短信" />
              </Form.Item>

              <Divider />

              <Form.Item name={['notifications', 'priceAlert']} valuePropName="checked">
                <Switch checkedChildren="价格提醒" unCheckedChildren="关闭价格提醒" />
              </Form.Item>

              <Form.Item name={['notifications', 'newsAlert']} valuePropName="checked">
                <Switch checkedChildren="新闻提醒" unCheckedChildren="关闭新闻提醒" />
              </Form.Item>

              <Form.Item name={['notifications', 'systemAlert']} valuePropName="checked">
                <Switch checkedChildren="系统通知" unCheckedChildren="关闭系统通知" />
              </Form.Item>
            </Card>
          </Col>

          {/* 数据刷新设置 */}
          <Col xs={24} lg={12}>
            <Card title="数据刷新">
              <Form.Item name="autoRefresh" valuePropName="checked">
                <Switch checkedChildren="自动刷新" unCheckedChildren="手动刷新" />
              </Form.Item>

              <Form.Item name="refreshInterval" label="刷新间隔（秒）">
                <InputNumber
                  min={5}
                  max={300}
                  step={5}
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item name={['display', 'tablePageSize']} label="表格每页显示">
                <Select>
                  <Option value={20}>20条</Option>
                  <Option value={50}>50条</Option>
                  <Option value={100}>100条</Option>
                  <Option value={200}>200条</Option>
                </Select>
              </Form.Item>

              <Form.Item name={['display', 'compactMode']} valuePropName="checked">
                <Switch checkedChildren="紧凑模式" unCheckedChildren="标准模式" />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* 操作按钮 */}
        <Card>
          <div className="flex justify-between items-center">
            <Alert
              message="设置说明"
              description="修改设置后需要保存才能生效，部分设置可能需要刷新页面"
              type="info"
              showIcon
              className="flex-1 mr-4"
            />
            
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
              >
                重置默认
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存设置
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
}
