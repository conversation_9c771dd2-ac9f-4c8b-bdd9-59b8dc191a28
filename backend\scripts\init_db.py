#!/usr/bin/env python3
"""
数据库初始化脚本

创建数据库、运行迁移、初始化基础数据
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from alembic import command
from alembic.config import Config
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import settings
from app.core.logging import logger


async def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 检查是否为 SQLite 数据库
        if "sqlite" in settings.DATABASE_URL.lower():
            logger.info("使用 SQLite 数据库，无需创建数据库")
            return

        # PostgreSQL 数据库创建逻辑
        if "postgresql" in settings.DATABASE_URL.lower():
            # 从 URL 中提取数据库名
            import re
            match = re.search(r'/([^/?]+)(?:\?|$)', settings.DATABASE_URL)
            if not match:
                raise ValueError("无法从数据库URL中提取数据库名")

            database_name = match.group(1)
            base_url = settings.DATABASE_URL.rsplit('/', 1)[0] + '/postgres'

            # 连接到默认数据库
            engine = create_async_engine(base_url, isolation_level="AUTOCOMMIT")

            async with engine.connect() as conn:
                # 检查数据库是否存在
                result = await conn.execute(
                    text(f"SELECT 1 FROM pg_database WHERE datname = '{database_name}'")
                )

                if not result.fetchone():
                    # 创建数据库
                    await conn.execute(
                        text(f"CREATE DATABASE {database_name}")
                    )
                    logger.info(f"数据库 {database_name} 创建成功")
                else:
                    logger.info(f"数据库 {database_name} 已存在")

            await engine.dispose()
        else:
            logger.info("未知数据库类型，跳过数据库创建")

    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        raise


async def create_tables():
    """创建数据库表"""
    try:
        from app.core.database import async_engine
        from app.models.base import Base

        logger.info("开始创建数据库表...")

        # 创建所有表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("数据库表创建完成")

    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise


async def create_initial_data():
    """创建初始数据"""
    try:
        from app.core.database import AsyncSessionLocal
        from app.models.user import User
        from sqlalchemy import select
        from passlib.context import CryptContext

        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

        async with AsyncSessionLocal() as session:
            # 检查是否已有管理员用户
            result = await session.execute(
                select(User).where(User.is_superuser == True)
            )
            admin_user = result.scalar_one_or_none()
            
            if not admin_user:
                # 创建默认管理员用户
                admin_user = User(
                    email="<EMAIL>",
                    username="admin",
                    hashed_password=pwd_context.hash("admin123456"),
                    full_name="系统管理员",
                    is_active=True,
                    is_superuser=True,
                    is_verified=True,
                    subscription_type="enterprise",
                    api_quota_daily=100000,
                )
                
                session.add(admin_user)
                await session.commit()
                
                logger.info("默认管理员用户创建成功")
                logger.info("邮箱: <EMAIL>")
                logger.info("密码: admin123456")
            else:
                logger.info("管理员用户已存在")
                
    except Exception as e:
        logger.error(f"创建初始数据失败: {e}")
        raise


async def init_database():
    """初始化数据库"""
    try:
        logger.info("🚀 开始初始化数据库...")

        # 1. 创建数据库
        await create_database_if_not_exists()

        # 2. 创建表
        await create_tables()

        # 3. 创建初始数据
        await create_initial_data()

        logger.info("🎉 数据库初始化完成！")

    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)


def create_migration(message: str):
    """创建新的迁移文件"""
    try:
        alembic_cfg = Config(str(project_root / "alembic.ini"))
        alembic_cfg.set_main_option("sqlalchemy.url", settings.database_url_sync)
        
        logger.info(f"创建迁移: {message}")
        command.revision(alembic_cfg, message=message, autogenerate=True)
        logger.info("迁移文件创建成功")
        
    except Exception as e:
        logger.error(f"创建迁移失败: {e}")
        raise


def show_migration_history():
    """显示迁移历史"""
    try:
        alembic_cfg = Config(str(project_root / "alembic.ini"))
        alembic_cfg.set_main_option("sqlalchemy.url", settings.database_url_sync)
        
        command.history(alembic_cfg)
        
    except Exception as e:
        logger.error(f"显示迁移历史失败: {e}")
        raise


def show_current_revision():
    """显示当前版本"""
    try:
        alembic_cfg = Config(str(project_root / "alembic.ini"))
        alembic_cfg.set_main_option("sqlalchemy.url", settings.database_url_sync)
        
        command.current(alembic_cfg)
        
    except Exception as e:
        logger.error(f"显示当前版本失败: {e}")
        raise


async def reset_database():
    """重置数据库"""
    try:
        logger.warning("⚠️  即将重置数据库，所有数据将被删除！")
        
        # 确认操作
        if input("确认重置数据库？(yes/no): ").lower() != 'yes':
            logger.info("操作已取消")
            return
        
        # 删除所有表
        from app.core.database import get_async_engine
        from app.models.base import Base
        
        engine = get_async_engine()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("所有表已删除")
        
        # 重新初始化
        await init_database()
        
    except Exception as e:
        logger.error(f"重置数据库失败: {e}")
        raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库管理工具")
    parser.add_argument(
        "command",
        choices=["init", "migrate", "history", "current", "reset"],
        help="执行的命令"
    )
    parser.add_argument(
        "-m", "--message",
        help="迁移消息（仅用于migrate命令）"
    )
    
    args = parser.parse_args()
    
    if args.command == "init":
        asyncio.run(init_database())
    elif args.command == "migrate":
        if not args.message:
            logger.error("创建迁移需要提供消息，使用 -m 参数")
            sys.exit(1)
        create_migration(args.message)
    elif args.command == "history":
        show_migration_history()
    elif args.command == "current":
        show_current_revision()
    elif args.command == "reset":
        asyncio.run(reset_database())


if __name__ == "__main__":
    main()
