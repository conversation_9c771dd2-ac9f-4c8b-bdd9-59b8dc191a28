"""add machine learning tables

Revision ID: 006
Revises: 005
Create Date: 2024-12-22 20:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建机器学习模型表
    op.create_table('ml_models',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('model_type', sa.String(length=50), nullable=False),
        sa.Column('algorithm', sa.String(length=50), nullable=False),
        sa.Column('version', sa.String(length=20), nullable=True),
        sa.Column('hyperparameters', sa.J<PERSON>(), nullable=True),
        sa.Column('feature_config', sa.JSON(), nullable=True),
        sa.Column('training_config', sa.JSON(), nullable=True),
        sa.Column('model_path', sa.String(length=500), nullable=True),
        sa.Column('model_size', sa.Integer(), nullable=True),
        sa.Column('model_format', sa.String(length=20), nullable=True),
        sa.Column('training_data_start', sa.DateTime(), nullable=True),
        sa.Column('training_data_end', sa.DateTime(), nullable=True),
        sa.Column('training_samples', sa.Integer(), nullable=True),
        sa.Column('training_features', sa.Integer(), nullable=True),
        sa.Column('training_duration', sa.Integer(), nullable=True),
        sa.Column('performance_metrics', sa.JSON(), nullable=True),
        sa.Column('validation_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('test_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('deployed_at', sa.DateTime(), nullable=True),
        sa.Column('deployment_config', sa.JSON(), nullable=True),
        sa.Column('api_endpoint', sa.String(length=200), nullable=True),
        sa.Column('prediction_count', sa.Integer(), nullable=True),
        sa.Column('last_used_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_models_id'), 'ml_models', ['id'], unique=False)
    op.create_index(op.f('ix_ml_models_user_id'), 'ml_models', ['user_id'], unique=False)

    # 创建机器学习特征表
    op.create_table('ml_features',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('data_type', sa.String(length=20), nullable=True),
        sa.Column('calculation_method', sa.Text(), nullable=True),
        sa.Column('source_tables', sa.JSON(), nullable=True),
        sa.Column('dependencies', sa.JSON(), nullable=True),
        sa.Column('update_frequency', sa.String(length=20), nullable=True),
        sa.Column('min_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('max_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('mean_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('std_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('null_ratio', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('importance_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('correlation_target', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_deprecated', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('last_calculated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_ml_features_id'), 'ml_features', ['id'], unique=False)
    op.create_index(op.f('ix_ml_features_name'), 'ml_features', ['name'], unique=True)

    # 创建机器学习特征值表
    op.create_table('ml_feature_values',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('feature_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('date', sa.DateTime(), nullable=False),
        sa.Column('value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('data_source', sa.String(length=50), nullable=True),
        sa.Column('calculation_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['feature_id'], ['ml_features.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_feature_values_date'), 'ml_feature_values', ['date'], unique=False)
    op.create_index(op.f('ix_ml_feature_values_feature_id'), 'ml_feature_values', ['feature_id'], unique=False)
    op.create_index(op.f('ix_ml_feature_values_id'), 'ml_feature_values', ['id'], unique=False)
    op.create_index(op.f('ix_ml_feature_values_symbol'), 'ml_feature_values', ['symbol'], unique=False)

    # 创建机器学习预测结果表
    op.create_table('ml_predictions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('prediction_type', sa.String(length=50), nullable=False),
        sa.Column('target_symbol', sa.String(length=20), nullable=False),
        sa.Column('prediction_date', sa.DateTime(), nullable=False),
        sa.Column('target_date', sa.DateTime(), nullable=False),
        sa.Column('predicted_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('predicted_class', sa.String(length=20), nullable=True),
        sa.Column('confidence_score', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('probability_distribution', sa.JSON(), nullable=True),
        sa.Column('lower_bound', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('upper_bound', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('actual_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('actual_class', sa.String(length=20), nullable=True),
        sa.Column('is_correct', sa.Boolean(), nullable=True),
        sa.Column('absolute_error', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('relative_error', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('input_features', sa.JSON(), nullable=True),
        sa.Column('model_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('evaluated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['ml_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_predictions_id'), 'ml_predictions', ['id'], unique=False)
    op.create_index(op.f('ix_ml_predictions_model_id'), 'ml_predictions', ['model_id'], unique=False)
    op.create_index(op.f('ix_ml_predictions_prediction_date'), 'ml_predictions', ['prediction_date'], unique=False)
    op.create_index(op.f('ix_ml_predictions_target_symbol'), 'ml_predictions', ['target_symbol'], unique=False)
    op.create_index(op.f('ix_ml_predictions_user_id'), 'ml_predictions', ['user_id'], unique=False)

    # 创建机器学习训练任务表
    op.create_table('ml_training_jobs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('job_name', sa.String(length=100), nullable=False),
        sa.Column('job_type', sa.String(length=50), nullable=True),
        sa.Column('training_config', sa.JSON(), nullable=False),
        sa.Column('hyperparameters', sa.JSON(), nullable=True),
        sa.Column('data_config', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('duration', sa.Integer(), nullable=True),
        sa.Column('final_metrics', sa.JSON(), nullable=True),
        sa.Column('best_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('training_history', sa.JSON(), nullable=True),
        sa.Column('cpu_usage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('memory_usage', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('gpu_usage', sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_traceback', sa.Text(), nullable=True),
        sa.Column('output_model_path', sa.String(length=500), nullable=True),
        sa.Column('logs_path', sa.String(length=500), nullable=True),
        sa.Column('artifacts_path', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['ml_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_training_jobs_id'), 'ml_training_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_ml_training_jobs_model_id'), 'ml_training_jobs', ['model_id'], unique=False)
    op.create_index(op.f('ix_ml_training_jobs_user_id'), 'ml_training_jobs', ['user_id'], unique=False)

    # 创建机器学习推荐结果表
    op.create_table('ml_recommendations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('model_id', sa.Integer(), nullable=False),
        sa.Column('recommendation_type', sa.String(length=50), nullable=False),
        sa.Column('target_id', sa.String(length=50), nullable=True),
        sa.Column('target_name', sa.String(length=100), nullable=True),
        sa.Column('score', sa.Numeric(precision=10, scale=6), nullable=False),
        sa.Column('rank', sa.Integer(), nullable=True),
        sa.Column('confidence', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('reasons', sa.JSON(), nullable=True),
        sa.Column('features_importance', sa.JSON(), nullable=True),
        sa.Column('similar_items', sa.JSON(), nullable=True),
        sa.Column('user_rating', sa.Integer(), nullable=True),
        sa.Column('user_feedback', sa.Text(), nullable=True),
        sa.Column('is_clicked', sa.Boolean(), nullable=True),
        sa.Column('is_adopted', sa.Boolean(), nullable=True),
        sa.Column('click_time', sa.DateTime(), nullable=True),
        sa.Column('adoption_time', sa.DateTime(), nullable=True),
        sa.Column('effectiveness_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('recommendation_context', sa.JSON(), nullable=True),
        sa.Column('algorithm_version', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['model_id'], ['ml_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_recommendations_id'), 'ml_recommendations', ['id'], unique=False)
    op.create_index(op.f('ix_ml_recommendations_model_id'), 'ml_recommendations', ['model_id'], unique=False)
    op.create_index(op.f('ix_ml_recommendations_user_id'), 'ml_recommendations', ['user_id'], unique=False)

    # 创建机器学习实验表
    op.create_table('ml_experiments',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('experiment_type', sa.String(length=50), nullable=True),
        sa.Column('config', sa.JSON(), nullable=False),
        sa.Column('hypothesis', sa.Text(), nullable=True),
        sa.Column('success_metrics', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('results', sa.JSON(), nullable=True),
        sa.Column('conclusion', sa.Text(), nullable=True),
        sa.Column('statistical_significance', sa.Boolean(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('duration_days', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ml_experiments_id'), 'ml_experiments', ['id'], unique=False)
    op.create_index(op.f('ix_ml_experiments_user_id'), 'ml_experiments', ['user_id'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_ml_experiments_user_id'), table_name='ml_experiments')
    op.drop_index(op.f('ix_ml_experiments_id'), table_name='ml_experiments')
    op.drop_table('ml_experiments')
    
    op.drop_index(op.f('ix_ml_recommendations_user_id'), table_name='ml_recommendations')
    op.drop_index(op.f('ix_ml_recommendations_model_id'), table_name='ml_recommendations')
    op.drop_index(op.f('ix_ml_recommendations_id'), table_name='ml_recommendations')
    op.drop_table('ml_recommendations')
    
    op.drop_index(op.f('ix_ml_training_jobs_user_id'), table_name='ml_training_jobs')
    op.drop_index(op.f('ix_ml_training_jobs_model_id'), table_name='ml_training_jobs')
    op.drop_index(op.f('ix_ml_training_jobs_id'), table_name='ml_training_jobs')
    op.drop_table('ml_training_jobs')
    
    op.drop_index(op.f('ix_ml_predictions_user_id'), table_name='ml_predictions')
    op.drop_index(op.f('ix_ml_predictions_target_symbol'), table_name='ml_predictions')
    op.drop_index(op.f('ix_ml_predictions_prediction_date'), table_name='ml_predictions')
    op.drop_index(op.f('ix_ml_predictions_model_id'), table_name='ml_predictions')
    op.drop_index(op.f('ix_ml_predictions_id'), table_name='ml_predictions')
    op.drop_table('ml_predictions')
    
    op.drop_index(op.f('ix_ml_feature_values_symbol'), table_name='ml_feature_values')
    op.drop_index(op.f('ix_ml_feature_values_id'), table_name='ml_feature_values')
    op.drop_index(op.f('ix_ml_feature_values_feature_id'), table_name='ml_feature_values')
    op.drop_index(op.f('ix_ml_feature_values_date'), table_name='ml_feature_values')
    op.drop_table('ml_feature_values')
    
    op.drop_index(op.f('ix_ml_features_name'), table_name='ml_features')
    op.drop_index(op.f('ix_ml_features_id'), table_name='ml_features')
    op.drop_table('ml_features')
    
    op.drop_index(op.f('ix_ml_models_user_id'), table_name='ml_models')
    op.drop_index(op.f('ix_ml_models_id'), table_name='ml_models')
    op.drop_table('ml_models')
