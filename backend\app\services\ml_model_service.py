"""
机器学习模型服务

提供模型训练、预测、管理等功能
"""

import os
import pickle
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json

# 机器学习库
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, accuracy_score, precision_score, recall_score, f1_score
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger

# 机器学习库导入（可选）
XGBOOST_AVAILABLE = False
LIGHTGBM_AVAILABLE = False
CATBOOST_AVAILABLE = False

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
    logger.info("XGBoost已加载")
except ImportError:
    logger.warning("XGBoost未安装，XGBoost功能不可用")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
    logger.info("LightGBM已加载")
except ImportError:
    logger.warning("LightGBM未安装，LightGBM功能不可用")

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    logger.info("CatBoost已加载")
except ImportError:
    logger.warning("CatBoost未安装，CatBoost功能不可用")

# 深度学习库
TENSORFLOW_AVAILABLE = False
PYTORCH_AVAILABLE = False

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
    logger.info("TensorFlow已加载，深度学习功能可用")
except ImportError:
    logger.warning("TensorFlow未安装，TensorFlow功能不可用")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    PYTORCH_AVAILABLE = True
    logger.info("PyTorch已加载，深度学习功能可用")
except ImportError:
    logger.warning("PyTorch未安装，PyTorch功能不可用")

if not TENSORFLOW_AVAILABLE and not PYTORCH_AVAILABLE:
    logger.warning("未安装深度学习库，将使用传统机器学习方法")
from app.models.ml import MLModel, MLTrainingJob, MLPrediction
from app.services.feature_engineering_service import feature_engineering_service


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self):
        self.models_dir = Path("ml_models")
        self.models_dir.mkdir(exist_ok=True)
    
    def train_price_prediction_model(
        self, 
        features: pd.DataFrame, 
        target: pd.Series,
        model_type: str = "xgboost",
        hyperparameters: Optional[Dict] = None
    ) -> Tuple[Any, Dict[str, float]]:
        """训练价格预测模型"""
        try:
            # 数据预处理
            X = features.fillna(0)  # 填充缺失值
            y = target.fillna(0)
            
            # 时间序列分割
            tscv = TimeSeriesSplit(n_splits=5)
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, shuffle=False
            )
            
            # 选择模型
            if model_type == "xgboost":
                model = self._train_xgboost_regressor(X_train, y_train, hyperparameters)
            elif model_type == "lightgbm":
                model = self._train_lightgbm_regressor(X_train, y_train, hyperparameters)
            elif model_type == "random_forest":
                model = self._train_random_forest_regressor(X_train, y_train, hyperparameters)
            elif model_type == "lstm":
                model = self._train_lstm_model(X_train, y_train, hyperparameters)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            # 模型评估
            y_pred = model.predict(X_test)
            metrics = self._calculate_regression_metrics(y_test, y_pred)
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"训练价格预测模型失败: {e}")
            raise
    
    def train_direction_prediction_model(
        self,
        features: pd.DataFrame,
        target: pd.Series,
        model_type: str = "xgboost",
        hyperparameters: Optional[Dict] = None
    ) -> Tuple[Any, Dict[str, float]]:
        """训练方向预测模型"""
        try:
            # 数据预处理
            X = features.fillna(0)
            y = target.fillna(0)

            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, shuffle=False, stratify=None
            )

            # 选择模型
            if model_type == "xgboost":
                model = self._train_xgboost_classifier(X_train, y_train, hyperparameters)
            elif model_type == "lightgbm":
                model = self._train_lightgbm_classifier(X_train, y_train, hyperparameters)
            elif model_type == "random_forest":
                model = self._train_random_forest_classifier(X_train, y_train, hyperparameters)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")

            # 模型评估
            y_pred = model.predict(X_test)
            metrics = self._calculate_classification_metrics(y_test, y_pred)

            return model, metrics

        except Exception as e:
            logger.error(f"训练方向预测模型失败: {e}")
            raise

    def train_profitability_model(
        self,
        features: pd.DataFrame,
        target: pd.Series,
        model_type: str = "xgboost",
        hyperparameters: Optional[Dict] = None
    ) -> Tuple[Any, Dict[str, float]]:
        """训练盈利能力模型 - 专门优化盈利能力"""
        try:
            # 数据预处理
            X = features.fillna(0)
            y = target.fillna(0)

            # 特征选择：优先选择盈利相关特征
            profitability_features = [col for col in X.columns if any(keyword in col.lower()
                for keyword in ['profitability', 'trend_strength', 'momentum_persistence',
                               'volatility_adjusted', 'breakout', 'relative_strength',
                               'upside_downside', 'max_drawdown'])]

            if profitability_features:
                logger.info(f"使用 {len(profitability_features)} 个盈利特征进行训练")
                X_selected = X[profitability_features + [col for col in X.columns
                    if col not in profitability_features][:20]]  # 加上其他重要特征
            else:
                X_selected = X

            # 时间序列分割（避免未来信息泄露）
            X_train, X_test, y_train, y_test = train_test_split(
                X_selected, y, test_size=0.2, shuffle=False
            )

            # 使用专门优化的超参数
            if model_type == "xgboost":
                # 针对盈利能力优化的XGBoost参数
                profit_params = {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.05,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'reg_alpha': 0.1,
                    'reg_lambda': 0.1,
                    'random_state': 42,
                    'objective': 'reg:squarederror',
                    'eval_metric': 'rmse'
                }
                if hyperparameters:
                    profit_params.update(hyperparameters)

                model = xgb.XGBRegressor(**profit_params)
                model.fit(X_train, y_train,
                         eval_set=[(X_test, y_test)],
                         early_stopping_rounds=20,
                         verbose=False)

            elif model_type == "lightgbm":
                profit_params = {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.05,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'reg_alpha': 0.1,
                    'reg_lambda': 0.1,
                    'random_state': 42,
                    'objective': 'regression',
                    'metric': 'rmse'
                }
                if hyperparameters:
                    profit_params.update(hyperparameters)

                model = lgb.LGBMRegressor(**profit_params)
                model.fit(X_train, y_train,
                         eval_set=[(X_test, y_test)],
                         early_stopping_rounds=20,
                         verbose=False)
            else:
                # 回退到标准训练
                return self.train_price_prediction_model(features, target, model_type, hyperparameters)

            # 模型评估
            y_pred = model.predict(X_test)
            metrics = self._calculate_profitability_metrics(y_test, y_pred)

            # 特征重要性分析
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(X_selected.columns, model.feature_importances_))
                top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
                logger.info(f"Top 10 重要特征: {top_features}")
                metrics['feature_importance'] = feature_importance

            return model, metrics

        except Exception as e:
            logger.error(f"训练盈利能力模型失败: {e}")
            raise
    
    def _train_xgboost_regressor(self, X_train, y_train, hyperparameters):
        """训练XGBoost回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = xgb.XGBRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_xgboost_classifier(self, X_train, y_train, hyperparameters):
        """训练XGBoost分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = xgb.XGBClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lightgbm_regressor(self, X_train, y_train, hyperparameters):
        """训练LightGBM回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42,
            'verbose': -1
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = lgb.LGBMRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lightgbm_classifier(self, X_train, y_train, hyperparameters):
        """训练LightGBM分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42,
            'verbose': -1
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = lgb.LGBMClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_random_forest_regressor(self, X_train, y_train, hyperparameters):
        """训练随机森林回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = RandomForestRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_random_forest_classifier(self, X_train, y_train, hyperparameters):
        """训练随机森林分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = RandomForestClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lstm_model(self, X_train, y_train, hyperparameters):
        """训练LSTM模型"""
        if not TENSORFLOW_AVAILABLE:
            logger.warning("TensorFlow未安装，使用随机森林模型替代LSTM")
            # 使用随机森林作为替代
            from sklearn.ensemble import RandomForestRegressor
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X_train, y_train)
            return model
        
        # 重塑数据为LSTM格式 (samples, timesteps, features)
        timesteps = hyperparameters.get('timesteps', 10) if hyperparameters else 10
        X_lstm = self._reshape_for_lstm(X_train, timesteps)
        y_lstm = y_train[timesteps-1:]
        
        # 构建LSTM模型
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(timesteps, X_train.shape[1])),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # 训练模型
        model.fit(X_lstm, y_lstm, epochs=50, batch_size=32, verbose=0)
        
        return model
    
    def _reshape_for_lstm(self, data, timesteps):
        """重塑数据为LSTM格式"""
        X_lstm = []
        for i in range(timesteps, len(data)):
            X_lstm.append(data[i-timesteps:i])
        return np.array(X_lstm)
    
    def _calculate_regression_metrics(self, y_true, y_pred):
        """计算回归指标"""
        return {
            'mse': float(mean_squared_error(y_true, y_pred)),
            'rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
            'mae': float(mean_absolute_error(y_true, y_pred)),
            'r2': float(1 - (np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)))
        }
    
    def _calculate_classification_metrics(self, y_true, y_pred):
        """计算分类指标"""
        return {
            'accuracy': float(accuracy_score(y_true, y_pred)),
            'precision': float(precision_score(y_true, y_pred, average='weighted', zero_division=0)),
            'recall': float(recall_score(y_true, y_pred, average='weighted', zero_division=0)),
            'f1': float(f1_score(y_true, y_pred, average='weighted', zero_division=0))
        }

    def _calculate_profitability_metrics(self, y_true, y_pred):
        """计算盈利能力指标 - 专门评估模型的盈利潜力"""
        try:
            # 基础回归指标
            mse = float(mean_squared_error(y_true, y_pred))
            rmse = float(np.sqrt(mse))
            mae = float(mean_absolute_error(y_true, y_pred))
            r2 = float(1 - (np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)))

            # 盈利能力指标
            # 1. 方向准确率（预测涨跌方向的准确性）
            direction_accuracy = float(np.mean(np.sign(y_true) == np.sign(y_pred)))

            # 2. 盈利交易比例（预测为正收益且实际为正的比例）
            profitable_predictions = np.sum((y_pred > 0) & (y_true > 0))
            total_positive_predictions = np.sum(y_pred > 0)
            profit_precision = float(profitable_predictions / total_positive_predictions) if total_positive_predictions > 0 else 0.0

            # 3. 捕获盈利机会的能力（实际盈利中被预测到的比例）
            actual_profits = np.sum(y_true > 0)
            profit_recall = float(profitable_predictions / actual_profits) if actual_profits > 0 else 0.0

            # 4. 平均预测收益（如果按预测进行交易的平均收益）
            avg_predicted_return = float(np.mean(y_pred))

            # 5. 预测收益的夏普比率（风险调整收益）
            pred_sharpe = float(np.mean(y_pred) / np.std(y_pred)) if np.std(y_pred) > 0 else 0.0

            # 6. 最大预测收益和最大预测损失
            max_predicted_gain = float(np.max(y_pred))
            max_predicted_loss = float(np.min(y_pred))

            # 7. 预测收益分布的偏度（正偏度表示更多正收益）
            pred_skewness = float(self._calculate_skewness(y_pred))

            # 8. 信息比率（超额收益/跟踪误差）
            excess_return = np.mean(y_pred) - np.mean(y_true)
            tracking_error = np.std(y_pred - y_true)
            information_ratio = float(excess_return / tracking_error) if tracking_error > 0 else 0.0

            # 9. 胜率（预测正确的交易占总交易的比例）
            win_rate = float(np.mean(np.abs(y_true - y_pred) < np.std(y_true) * 0.5))

            # 10. 盈亏比（平均盈利/平均亏损）
            profits = y_pred[y_pred > 0]
            losses = y_pred[y_pred < 0]
            profit_loss_ratio = float(np.mean(profits) / abs(np.mean(losses))) if len(losses) > 0 and np.mean(losses) != 0 else 1.0

            return {
                # 基础指标
                'mse': mse,
                'rmse': rmse,
                'mae': mae,
                'r2': r2,

                # 盈利能力指标
                'direction_accuracy': direction_accuracy,
                'profit_precision': profit_precision,
                'profit_recall': profit_recall,
                'avg_predicted_return': avg_predicted_return,
                'predicted_sharpe_ratio': pred_sharpe,
                'max_predicted_gain': max_predicted_gain,
                'max_predicted_loss': max_predicted_loss,
                'prediction_skewness': pred_skewness,
                'information_ratio': information_ratio,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio,

                # 综合盈利评分（0-100）
                'profitability_score': self._calculate_profitability_score(
                    direction_accuracy, profit_precision, profit_recall, pred_sharpe, win_rate
                )
            }

        except Exception as e:
            logger.error(f"计算盈利能力指标失败: {e}")
            return self._calculate_regression_metrics(y_true, y_pred)

    def _calculate_skewness(self, data):
        """计算偏度"""
        try:
            mean = np.mean(data)
            std = np.std(data)
            if std == 0:
                return 0.0
            return np.mean(((data - mean) / std) ** 3)
        except:
            return 0.0

    def _calculate_profitability_score(self, direction_acc, profit_prec, profit_rec, sharpe, win_rate):
        """计算综合盈利评分（0-100）"""
        try:
            # 各指标权重
            weights = {
                'direction_accuracy': 0.25,
                'profit_precision': 0.25,
                'profit_recall': 0.20,
                'sharpe_ratio': 0.20,
                'win_rate': 0.10
            }

            # 标准化到0-1范围
            normalized_scores = {
                'direction_accuracy': max(0, min(1, direction_acc)),
                'profit_precision': max(0, min(1, profit_prec)),
                'profit_recall': max(0, min(1, profit_rec)),
                'sharpe_ratio': max(0, min(1, (sharpe + 2) / 4)),  # 夏普比率-2到2映射到0-1
                'win_rate': max(0, min(1, win_rate))
            }

            # 加权平均
            score = sum(weights[key] * normalized_scores[key] for key in weights.keys())
            return float(score * 100)  # 转换为0-100分

        except:
            return 50.0  # 默认中等评分
    
    def save_model(self, model, model_path: str, model_format: str = "pkl"):
        """保存模型"""
        try:
            if model_format == "pkl":
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
            elif model_format == "joblib":
                joblib.dump(model, model_path)
            elif model_format == "h5" and TENSORFLOW_AVAILABLE:
                model.save(model_path)
            else:
                raise ValueError(f"不支持的模型格式: {model_format}")
            
            logger.info(f"模型保存成功: {model_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def load_model(self, model_path: str, model_format: str = "pkl"):
        """加载模型"""
        try:
            if model_format == "pkl":
                with open(model_path, 'rb') as f:
                    model = pickle.load(f)
            elif model_format == "joblib":
                model = joblib.load(model_path)
            elif model_format == "h5" and TENSORFLOW_AVAILABLE:
                model = load_model(model_path)
            else:
                raise ValueError(f"不支持的模型格式: {model_format}")
            
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise


class ModelPredictor:
    """模型预测器"""
    
    def __init__(self):
        self.trainer = ModelTrainer()
    
    def predict_price(
        self, 
        model, 
        features: pd.DataFrame,
        model_type: str = "xgboost"
    ) -> np.ndarray:
        """价格预测"""
        try:
            X = features.fillna(0)
            
            if model_type == "lstm" and TENSORFLOW_AVAILABLE:
                # LSTM需要特殊的数据格式
                timesteps = 10  # 应该从模型配置中获取
                if len(X) >= timesteps:
                    X_lstm = self.trainer._reshape_for_lstm(X, timesteps)
                    predictions = model.predict(X_lstm[-1:])
                    return predictions.flatten()
                else:
                    return np.array([0.0])
            else:
                # 对于非LSTM模型或LSTM替代模型，使用标准预测
                predictions = model.predict(X)
                return predictions
                
        except Exception as e:
            logger.error(f"价格预测失败: {e}")
            return np.array([0.0])
    
    def predict_direction(
        self, 
        model, 
        features: pd.DataFrame
    ) -> Tuple[np.ndarray, np.ndarray]:
        """方向预测"""
        try:
            X = features.fillna(0)
            
            # 预测类别
            predictions = model.predict(X)
            
            # 预测概率
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(X)
            else:
                probabilities = np.ones((len(predictions), 1))
            
            return predictions, probabilities
            
        except Exception as e:
            logger.error(f"方向预测失败: {e}")
            return np.array([0]), np.array([[1.0]])


class MLModelService:
    """机器学习模型服务"""
    
    def __init__(self):
        self.trainer = ModelTrainer()
        self.predictor = ModelPredictor()
    
    async def train_model(
        self,
        user_id: int,
        model_config: Dict[str, Any],
        db: AsyncSession
    ) -> MLModel:
        """训练模型"""
        try:
            # 创建训练任务
            training_job = MLTrainingJob(
                user_id=user_id,
                job_name=model_config["name"],
                job_type="training",
                training_config=model_config,
                status="running"
            )
            db.add(training_job)
            await db.commit()
            await db.refresh(training_job)
            
            # 准备训练数据
            symbols = model_config.get("symbols", ["000001.XSHE"])
            start_date = datetime.fromisoformat(model_config["start_date"])
            end_date = datetime.fromisoformat(model_config["end_date"])
            
            # 提取特征
            all_features = {}
            for symbol in symbols:
                features = await feature_engineering_service.extract_all_features(
                    symbol, start_date, end_date, db
                )
                all_features[symbol] = features
            
            # 构建训练数据集
            X, y = self._prepare_training_data(all_features, model_config)
            
            # 训练模型
            model_type = model_config.get("algorithm", "xgboost")
            hyperparameters = model_config.get("hyperparameters", {})
            
            if model_config["model_type"] == "prediction":
                trained_model, metrics = self.trainer.train_price_prediction_model(
                    X, y, model_type, hyperparameters
                )
            else:
                trained_model, metrics = self.trainer.train_direction_prediction_model(
                    X, y, model_type, hyperparameters
                )
            
            # 保存模型
            model_filename = f"model_{user_id}_{int(datetime.now().timestamp())}.pkl"
            model_path = self.trainer.models_dir / model_filename
            self.trainer.save_model(trained_model, str(model_path))
            
            # 创建模型记录
            ml_model = MLModel(
                user_id=user_id,
                name=model_config["name"],
                description=model_config.get("description", ""),
                model_type=model_config["model_type"],
                algorithm=model_type,
                hyperparameters=hyperparameters,
                model_path=str(model_path),
                model_size=model_path.stat().st_size,
                model_format="pkl",
                training_samples=len(X),
                training_features=len(X.columns) if hasattr(X, 'columns') else X.shape[1],
                performance_metrics=metrics,
                validation_score=metrics.get("r2", metrics.get("accuracy", 0)),
                status="trained"
            )
            
            db.add(ml_model)
            training_job.model = ml_model
            training_job.status = "completed"
            training_job.final_metrics = metrics
            
            await db.commit()
            await db.refresh(ml_model)
            
            logger.info(f"模型训练完成: {ml_model.id}")
            return ml_model
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            training_job.status = "failed"
            training_job.error_message = str(e)
            await db.commit()
            raise
    
    def _prepare_training_data(
        self, 
        features_dict: Dict[str, Dict], 
        model_config: Dict
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练数据"""
        try:
            all_data = []
            
            for symbol, features in features_dict.items():
                if not features or 'dates' not in features:
                    continue
                
                dates = features['dates']
                
                # 构建特征DataFrame
                feature_data = {}
                for feature_name, values in features.items():
                    if feature_name != 'dates' and isinstance(values, (list, np.ndarray)):
                        feature_data[feature_name] = values
                
                if not feature_data:
                    continue
                
                df = pd.DataFrame(feature_data, index=dates)
                df['symbol'] = symbol
                
                # 构建目标变量
                if model_config["model_type"] == "prediction":
                    # 价格预测：预测未来N天的收益率
                    target_days = model_config.get("target_days", 1)
                    df['target'] = df['close_price'].pct_change(target_days).shift(-target_days)
                else:
                    # 方向预测：预测涨跌方向
                    target_days = model_config.get("target_days", 1)
                    future_return = df['close_price'].pct_change(target_days).shift(-target_days)
                    df['target'] = (future_return > 0).astype(int)
                
                all_data.append(df)
            
            if not all_data:
                raise ValueError("没有可用的训练数据")
            
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 移除包含NaN的行
            combined_df = combined_df.dropna()
            
            # 分离特征和目标
            feature_columns = [col for col in combined_df.columns if col not in ['target', 'symbol', 'dates']]
            X = combined_df[feature_columns]
            y = combined_df['target']
            
            return X, y
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            raise


# 全局机器学习模型服务实例
ml_model_service = MLModelService()
