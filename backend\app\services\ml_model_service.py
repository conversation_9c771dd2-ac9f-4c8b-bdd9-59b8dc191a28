"""
机器学习模型服务

提供模型训练、预测、管理等功能
"""

import os
import pickle
import joblib
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json

# 机器学习库
from sklearn.model_selection import train_test_split, GridSearchCV, TimeSeriesSplit
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, accuracy_score, precision_score, recall_score, f1_score
import xgboost as xgb
import lightgbm as lgb

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger

# 深度学习库
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow未安装，深度学习功能不可用")
from app.models.ml import MLModel, MLTrainingJob, MLPrediction
from app.services.feature_engineering_service import feature_engineering_service


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self):
        self.models_dir = Path("ml_models")
        self.models_dir.mkdir(exist_ok=True)
    
    def train_price_prediction_model(
        self, 
        features: pd.DataFrame, 
        target: pd.Series,
        model_type: str = "xgboost",
        hyperparameters: Optional[Dict] = None
    ) -> Tuple[Any, Dict[str, float]]:
        """训练价格预测模型"""
        try:
            # 数据预处理
            X = features.fillna(0)  # 填充缺失值
            y = target.fillna(0)
            
            # 时间序列分割
            tscv = TimeSeriesSplit(n_splits=5)
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, shuffle=False
            )
            
            # 选择模型
            if model_type == "xgboost":
                model = self._train_xgboost_regressor(X_train, y_train, hyperparameters)
            elif model_type == "lightgbm":
                model = self._train_lightgbm_regressor(X_train, y_train, hyperparameters)
            elif model_type == "random_forest":
                model = self._train_random_forest_regressor(X_train, y_train, hyperparameters)
            elif model_type == "lstm" and TENSORFLOW_AVAILABLE:
                model = self._train_lstm_model(X_train, y_train, hyperparameters)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            # 模型评估
            y_pred = model.predict(X_test)
            metrics = self._calculate_regression_metrics(y_test, y_pred)
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"训练价格预测模型失败: {e}")
            raise
    
    def train_direction_prediction_model(
        self, 
        features: pd.DataFrame, 
        target: pd.Series,
        model_type: str = "xgboost",
        hyperparameters: Optional[Dict] = None
    ) -> Tuple[Any, Dict[str, float]]:
        """训练方向预测模型"""
        try:
            # 数据预处理
            X = features.fillna(0)
            y = target.fillna(0)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, shuffle=False, stratify=None
            )
            
            # 选择模型
            if model_type == "xgboost":
                model = self._train_xgboost_classifier(X_train, y_train, hyperparameters)
            elif model_type == "lightgbm":
                model = self._train_lightgbm_classifier(X_train, y_train, hyperparameters)
            elif model_type == "random_forest":
                model = self._train_random_forest_classifier(X_train, y_train, hyperparameters)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
            
            # 模型评估
            y_pred = model.predict(X_test)
            metrics = self._calculate_classification_metrics(y_test, y_pred)
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"训练方向预测模型失败: {e}")
            raise
    
    def _train_xgboost_regressor(self, X_train, y_train, hyperparameters):
        """训练XGBoost回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = xgb.XGBRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_xgboost_classifier(self, X_train, y_train, hyperparameters):
        """训练XGBoost分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = xgb.XGBClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lightgbm_regressor(self, X_train, y_train, hyperparameters):
        """训练LightGBM回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42,
            'verbose': -1
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = lgb.LGBMRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lightgbm_classifier(self, X_train, y_train, hyperparameters):
        """训练LightGBM分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 6,
            'learning_rate': 0.1,
            'random_state': 42,
            'verbose': -1
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = lgb.LGBMClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_random_forest_regressor(self, X_train, y_train, hyperparameters):
        """训练随机森林回归模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = RandomForestRegressor(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_random_forest_classifier(self, X_train, y_train, hyperparameters):
        """训练随机森林分类模型"""
        params = {
            'n_estimators': 100,
            'max_depth': 10,
            'random_state': 42
        }
        if hyperparameters:
            params.update(hyperparameters)
        
        model = RandomForestClassifier(**params)
        model.fit(X_train, y_train)
        return model
    
    def _train_lstm_model(self, X_train, y_train, hyperparameters):
        """训练LSTM模型"""
        if not TENSORFLOW_AVAILABLE:
            raise ImportError("TensorFlow未安装，无法训练LSTM模型")
        
        # 重塑数据为LSTM格式 (samples, timesteps, features)
        timesteps = hyperparameters.get('timesteps', 10) if hyperparameters else 10
        X_lstm = self._reshape_for_lstm(X_train, timesteps)
        y_lstm = y_train[timesteps-1:]
        
        # 构建LSTM模型
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(timesteps, X_train.shape[1])),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
        
        # 训练模型
        model.fit(X_lstm, y_lstm, epochs=50, batch_size=32, verbose=0)
        
        return model
    
    def _reshape_for_lstm(self, data, timesteps):
        """重塑数据为LSTM格式"""
        X_lstm = []
        for i in range(timesteps, len(data)):
            X_lstm.append(data[i-timesteps:i])
        return np.array(X_lstm)
    
    def _calculate_regression_metrics(self, y_true, y_pred):
        """计算回归指标"""
        return {
            'mse': float(mean_squared_error(y_true, y_pred)),
            'rmse': float(np.sqrt(mean_squared_error(y_true, y_pred))),
            'mae': float(mean_absolute_error(y_true, y_pred)),
            'r2': float(1 - (np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)))
        }
    
    def _calculate_classification_metrics(self, y_true, y_pred):
        """计算分类指标"""
        return {
            'accuracy': float(accuracy_score(y_true, y_pred)),
            'precision': float(precision_score(y_true, y_pred, average='weighted', zero_division=0)),
            'recall': float(recall_score(y_true, y_pred, average='weighted', zero_division=0)),
            'f1': float(f1_score(y_true, y_pred, average='weighted', zero_division=0))
        }
    
    def save_model(self, model, model_path: str, model_format: str = "pkl"):
        """保存模型"""
        try:
            if model_format == "pkl":
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
            elif model_format == "joblib":
                joblib.dump(model, model_path)
            elif model_format == "h5" and TENSORFLOW_AVAILABLE:
                model.save(model_path)
            else:
                raise ValueError(f"不支持的模型格式: {model_format}")
            
            logger.info(f"模型保存成功: {model_path}")
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            raise
    
    def load_model(self, model_path: str, model_format: str = "pkl"):
        """加载模型"""
        try:
            if model_format == "pkl":
                with open(model_path, 'rb') as f:
                    model = pickle.load(f)
            elif model_format == "joblib":
                model = joblib.load(model_path)
            elif model_format == "h5" and TENSORFLOW_AVAILABLE:
                model = load_model(model_path)
            else:
                raise ValueError(f"不支持的模型格式: {model_format}")
            
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise


class ModelPredictor:
    """模型预测器"""
    
    def __init__(self):
        self.trainer = ModelTrainer()
    
    def predict_price(
        self, 
        model, 
        features: pd.DataFrame,
        model_type: str = "xgboost"
    ) -> np.ndarray:
        """价格预测"""
        try:
            X = features.fillna(0)
            
            if model_type == "lstm" and TENSORFLOW_AVAILABLE:
                # LSTM需要特殊的数据格式
                timesteps = 10  # 应该从模型配置中获取
                if len(X) >= timesteps:
                    X_lstm = self.trainer._reshape_for_lstm(X, timesteps)
                    predictions = model.predict(X_lstm[-1:])
                    return predictions.flatten()
                else:
                    return np.array([0.0])
            else:
                predictions = model.predict(X)
                return predictions
                
        except Exception as e:
            logger.error(f"价格预测失败: {e}")
            return np.array([0.0])
    
    def predict_direction(
        self, 
        model, 
        features: pd.DataFrame
    ) -> Tuple[np.ndarray, np.ndarray]:
        """方向预测"""
        try:
            X = features.fillna(0)
            
            # 预测类别
            predictions = model.predict(X)
            
            # 预测概率
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(X)
            else:
                probabilities = np.ones((len(predictions), 1))
            
            return predictions, probabilities
            
        except Exception as e:
            logger.error(f"方向预测失败: {e}")
            return np.array([0]), np.array([[1.0]])


class MLModelService:
    """机器学习模型服务"""
    
    def __init__(self):
        self.trainer = ModelTrainer()
        self.predictor = ModelPredictor()
    
    async def train_model(
        self,
        user_id: int,
        model_config: Dict[str, Any],
        db: AsyncSession
    ) -> MLModel:
        """训练模型"""
        try:
            # 创建训练任务
            training_job = MLTrainingJob(
                user_id=user_id,
                job_name=model_config["name"],
                job_type="training",
                training_config=model_config,
                status="running"
            )
            db.add(training_job)
            await db.commit()
            await db.refresh(training_job)
            
            # 准备训练数据
            symbols = model_config.get("symbols", ["000001.XSHE"])
            start_date = datetime.fromisoformat(model_config["start_date"])
            end_date = datetime.fromisoformat(model_config["end_date"])
            
            # 提取特征
            all_features = {}
            for symbol in symbols:
                features = await feature_engineering_service.extract_all_features(
                    symbol, start_date, end_date, db
                )
                all_features[symbol] = features
            
            # 构建训练数据集
            X, y = self._prepare_training_data(all_features, model_config)
            
            # 训练模型
            model_type = model_config.get("algorithm", "xgboost")
            hyperparameters = model_config.get("hyperparameters", {})
            
            if model_config["model_type"] == "prediction":
                trained_model, metrics = self.trainer.train_price_prediction_model(
                    X, y, model_type, hyperparameters
                )
            else:
                trained_model, metrics = self.trainer.train_direction_prediction_model(
                    X, y, model_type, hyperparameters
                )
            
            # 保存模型
            model_filename = f"model_{user_id}_{int(datetime.now().timestamp())}.pkl"
            model_path = self.trainer.models_dir / model_filename
            self.trainer.save_model(trained_model, str(model_path))
            
            # 创建模型记录
            ml_model = MLModel(
                user_id=user_id,
                name=model_config["name"],
                description=model_config.get("description", ""),
                model_type=model_config["model_type"],
                algorithm=model_type,
                hyperparameters=hyperparameters,
                model_path=str(model_path),
                model_size=model_path.stat().st_size,
                model_format="pkl",
                training_samples=len(X),
                training_features=len(X.columns) if hasattr(X, 'columns') else X.shape[1],
                performance_metrics=metrics,
                validation_score=metrics.get("r2", metrics.get("accuracy", 0)),
                status="trained"
            )
            
            db.add(ml_model)
            training_job.model = ml_model
            training_job.status = "completed"
            training_job.final_metrics = metrics
            
            await db.commit()
            await db.refresh(ml_model)
            
            logger.info(f"模型训练完成: {ml_model.id}")
            return ml_model
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            training_job.status = "failed"
            training_job.error_message = str(e)
            await db.commit()
            raise
    
    def _prepare_training_data(
        self, 
        features_dict: Dict[str, Dict], 
        model_config: Dict
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """准备训练数据"""
        try:
            all_data = []
            
            for symbol, features in features_dict.items():
                if not features or 'dates' not in features:
                    continue
                
                dates = features['dates']
                
                # 构建特征DataFrame
                feature_data = {}
                for feature_name, values in features.items():
                    if feature_name != 'dates' and isinstance(values, (list, np.ndarray)):
                        feature_data[feature_name] = values
                
                if not feature_data:
                    continue
                
                df = pd.DataFrame(feature_data, index=dates)
                df['symbol'] = symbol
                
                # 构建目标变量
                if model_config["model_type"] == "prediction":
                    # 价格预测：预测未来N天的收益率
                    target_days = model_config.get("target_days", 1)
                    df['target'] = df['close_price'].pct_change(target_days).shift(-target_days)
                else:
                    # 方向预测：预测涨跌方向
                    target_days = model_config.get("target_days", 1)
                    future_return = df['close_price'].pct_change(target_days).shift(-target_days)
                    df['target'] = (future_return > 0).astype(int)
                
                all_data.append(df)
            
            if not all_data:
                raise ValueError("没有可用的训练数据")
            
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 移除包含NaN的行
            combined_df = combined_df.dropna()
            
            # 分离特征和目标
            feature_columns = [col for col in combined_df.columns if col not in ['target', 'symbol', 'dates']]
            X = combined_df[feature_columns]
            y = combined_df['target']
            
            return X, y
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            raise


# 全局机器学习模型服务实例
ml_model_service = MLModelService()
