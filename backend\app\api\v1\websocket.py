"""
WebSocket API端点

提供实时数据推送的WebSocket连接
"""

import json
import uuid
from typing import Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.api.v1.auth import get_current_user_websocket
from app.websocket.manager import manager, message_handler

router = APIRouter()


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    WebSocket连接端点
    
    支持实时数据推送，包括：
    - 股票价格更新
    - 市场数据推送
    - 系统通知
    - 用户消息
    """
    connection_id = str(uuid.uuid4())
    user = None
    
    try:
        # 验证用户身份（可选）
        if token:
            try:
                user = await get_current_user_websocket(token, db)
                logger.info(f"WebSocket认证用户: {user.id} ({user.email})")
            except Exception as e:
                logger.warning(f"WebSocket认证失败: {e}")
        
        # 建立连接
        await manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user.id if user else None
        )
        
        # 消息循环
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await message_handler.handle_message(connection_id, message)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端断开连接: {connection_id}")
                break
                
            except json.JSONDecodeError:
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': '无效的JSON格式',
                })
                
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await manager.send_personal_message(connection_id, {
                    'type': 'error',
                    'message': '消息处理失败',
                })
                
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
        
    finally:
        # 清理连接
        manager.disconnect(connection_id)


@router.websocket("/ws/market")
async def market_websocket_endpoint(
    websocket: WebSocket,
    symbols: str = Query(..., description="订阅的股票代码，逗号分隔"),
    token: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    市场数据WebSocket端点
    
    专门用于股票市场数据推送
    """
    connection_id = str(uuid.uuid4())
    user = None
    
    try:
        # 验证用户身份
        if token:
            try:
                user = await get_current_user_websocket(token, db)
            except Exception as e:
                logger.warning(f"市场WebSocket认证失败: {e}")
        
        # 建立连接
        await manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user.id if user else None
        )
        
        # 自动订阅指定股票
        symbol_list = [s.strip() for s in symbols.split(',') if s.strip()]
        for symbol in symbol_list:
            manager.subscribe_symbol(connection_id, symbol)
        
        # 发送订阅确认
        await manager.send_personal_message(connection_id, {
            'type': 'market_subscription',
            'symbols': symbol_list,
            'message': f'已订阅 {len(symbol_list)} 只股票的实时数据',
        })
        
        # 保持连接
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await message_handler.handle_message(connection_id, message)
                
            except WebSocketDisconnect:
                break
                
            except Exception as e:
                logger.error(f"市场WebSocket错误: {e}")
                break
                
    except Exception as e:
        logger.error(f"市场WebSocket连接错误: {e}")
        
    finally:
        manager.disconnect(connection_id)


@router.websocket("/ws/portfolio")
async def portfolio_websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(..., description="认证token"),
    db: AsyncSession = Depends(get_db)
):
    """
    投资组合WebSocket端点
    
    推送用户投资组合相关的实时数据
    """
    connection_id = str(uuid.uuid4())
    
    try:
        # 验证用户身份（必需）
        user = await get_current_user_websocket(token, db)
        
        # 建立连接
        await manager.connect(
            websocket=websocket,
            connection_id=connection_id,
            user_id=user.id
        )
        
        # 发送欢迎消息
        await manager.send_personal_message(connection_id, {
            'type': 'portfolio_connected',
            'message': f'欢迎 {user.full_name or user.username}，投资组合实时数据已连接',
        })
        
        # 消息循环
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await message_handler.handle_message(connection_id, message)
                
            except WebSocketDisconnect:
                break
                
            except Exception as e:
                logger.error(f"投资组合WebSocket错误: {e}")
                break
                
    except Exception as e:
        logger.error(f"投资组合WebSocket连接错误: {e}")
        # 发送认证失败消息
        try:
            await websocket.send_text(json.dumps({
                'type': 'auth_error',
                'message': '认证失败，请重新登录',
            }))
        except:
            pass
        
    finally:
        manager.disconnect(connection_id)
