{"version": 3, "file": "autoRotate.js", "sourceRoot": "", "sources": ["../../../../src/ui/axis/overlap/autoRotate.ts"], "names": [], "mappings": ";;AAUA,+BA0BC;;AAlCD,sCAA0C;AAQ1C,SAAwB,YAAY,CAClC,MAAuB,EACvB,UAA4B,EAC5B,IAAoB,EACpB,KAAY;;IAEJ,IAAA,KAAmE,UAAU,eAAjD,EAA5B,cAAc,mBAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,KAAA,EAAE,MAAM,GAA+B,UAAU,OAAzC,EAAE,KAA6B,UAAU,kBAAf,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,CAAgB;IAEtF,IAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,mBAAmB,EAAE,EAA3B,CAA2B,CAAC,CAAC;IAEzE,IAAM,YAAY,GAAG,cAAM,OAAA,IAAA,gBAAS,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAA1C,CAA0C,CAAC;IAEtE,IAAM,eAAe,GAAG,UAAC,KAAgC;QACvD,OAAA,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAC1B,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3D,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC,CAAC;IAHF,CAGE,CAAC;;QAEL,KAAoB,IAAA,mBAAA,iBAAA,cAAc,CAAA,8CAAA,0EAAE,CAAC;YAAhC,IAAM,KAAK,2BAAA;YACd,eAAe,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,YAAY,EAAE;gBAAE,OAAO;QAC7B,CAAC;;;;;;;;;IAED,IAAI,iBAAiB,EAAE,CAAC;QACtB,eAAe,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;AACH,CAAC"}