2025-08-26 12:37:14.326 | ERROR    | app.core.database:init_db:210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:37:14.338 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:39:30.641 | ERROR    | app.core.database:init_db:210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:39:30.645 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:41:28.087 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:41:28.088 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:43:08.945 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:43:48.680 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-26 12:44:19.030 | ERROR    | app.core.database:check_database_health:235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-08-26 12:44:23.104 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:44:23.110 | ERROR    | app.core.database:check_redis_health:246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'
2025-08-26 12:46:21.729 | ERROR    | app.core.database:check_database_health:235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-08-26 12:46:25.785 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:46:25.786 | ERROR    | app.core.database:check_redis_health:246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'
2025-08-26 12:47:12.716 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:47:54.891 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 15:00:14.240 | ERROR    | __main__:create_database_if_not_exists:51 | 创建数据库失败: 'Settings' object has no attribute 'DATABASE_NAME'
2025-08-26 15:00:14.242 | ERROR    | __main__:init_database:134 | ❌ 数据库初始化失败: 'Settings' object has no attribute 'DATABASE_NAME'
2025-08-26 15:01:55.235 | ERROR    | __main__:run_migrations:84 | 数据库迁移失败: asyncio.run() cannot be called from a running event loop
2025-08-26 15:01:55.239 | ERROR    | __main__:init_database:150 | ❌ 数据库初始化失败: asyncio.run() cannot be called from a running event loop
2025-08-26 15:03:15.107 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: cannot import name 'get_async_session' from 'app.core.database' (C:\Users\<USER>\Desktop\JQData\backend\app\core\database.py)
2025-08-26 15:03:15.107 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: cannot import name 'get_async_session' from 'app.core.database' (C:\Users\<USER>\Desktop\JQData\backend\app\core\database.py)
2025-08-26 15:04:14.716 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:04:14.717 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:04.152 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:04.152 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:57.211 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:57.213 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:10:51.668 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:10:51.708 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:11:47.173 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: Mapper 'Mapper[User(users)]' has no property 'system_logs'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:11:47.173 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: Mapper 'Mapper[User(users)]' has no property 'system_logs'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:18:24.675 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:21:28.440 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: reverse_property 'strategies' on relationship BacktestStrategy.user references relationship User.strategies, which does not reference mapper Mapper[BacktestStrategy(backtest_strategies)]
2025-08-26 15:21:28.441 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:21:28.443 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:24:58.594 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:25:33.376 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: reverse_property 'strategy' on relationship BacktestStrategy.backtests references relationship BacktestTask.strategy, which does not reference mapper Mapper[BacktestStrategy(backtest_strategies)]
2025-08-26 15:25:33.378 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:25:33.378 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:27:43.047 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:28:14.394 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'backtest_templates'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:28:14.395 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:28:14.400 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:29:58.735 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:30:24.362 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'risk_profile'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:30:24.363 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:30:24.364 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:35:06.800 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:35:34.795 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'backtest_strategies'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:35:34.800 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:35:34.800 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:40:17.294 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:40:41.300 | ERROR    | app.api.v1.auth:test_login:483 | 测试登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:40:41.301 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:40:41.301 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:29.886 | ERROR    | app.api.v1.auth:test_login:483 | 测试登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:29.887 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:41:29.889 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:42.685 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:43:01.099 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:43:21.510 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:43:21.511 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - 邮箱或密码错误
2025-08-26 15:53:06.907 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:07.389 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:30.511 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:30.967 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:36.746 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:17:21.087 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:38:39.511 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:39:43.326 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
