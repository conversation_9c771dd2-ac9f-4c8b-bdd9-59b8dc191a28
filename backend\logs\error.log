2025-08-26 12:37:14.326 | ERROR    | app.core.database:init_db:210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:37:14.338 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:39:30.641 | ERROR    | app.core.database:init_db:210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:39:30.645 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。
2025-08-26 12:41:28.087 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:41:28.088 | ERROR    | app.main:lifespan:42 | ❌ 应用启动失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:43:08.945 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:43:48.680 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-26 12:44:19.030 | ERROR    | app.core.database:check_database_health:235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-08-26 12:44:23.104 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:44:23.110 | ERROR    | app.core.database:check_redis_health:246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'
2025-08-26 12:46:21.729 | ERROR    | app.core.database:check_database_health:235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-08-26 12:46:25.785 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:46:25.786 | ERROR    | app.core.database:check_redis_health:246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'
2025-08-26 12:47:12.716 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 12:47:54.891 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-26 15:00:14.240 | ERROR    | __main__:create_database_if_not_exists:51 | 创建数据库失败: 'Settings' object has no attribute 'DATABASE_NAME'
2025-08-26 15:00:14.242 | ERROR    | __main__:init_database:134 | ❌ 数据库初始化失败: 'Settings' object has no attribute 'DATABASE_NAME'
2025-08-26 15:01:55.235 | ERROR    | __main__:run_migrations:84 | 数据库迁移失败: asyncio.run() cannot be called from a running event loop
2025-08-26 15:01:55.239 | ERROR    | __main__:init_database:150 | ❌ 数据库初始化失败: asyncio.run() cannot be called from a running event loop
2025-08-26 15:03:15.107 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: cannot import name 'get_async_session' from 'app.core.database' (C:\Users\<USER>\Desktop\JQData\backend\app\core\database.py)
2025-08-26 15:03:15.107 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: cannot import name 'get_async_session' from 'app.core.database' (C:\Users\<USER>\Desktop\JQData\backend\app\core\database.py)
2025-08-26 15:04:14.716 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:04:14.717 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:04.152 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:04.152 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:57.211 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:05:57.213 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:10:51.668 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:10:51.708 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.
2025-08-26 15:11:47.173 | ERROR    | __main__:create_initial_data:131 | 创建初始数据失败: Mapper 'Mapper[User(users)]' has no property 'system_logs'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:11:47.173 | ERROR    | __main__:init_database:152 | ❌ 数据库初始化失败: Mapper 'Mapper[User(users)]' has no property 'system_logs'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:18:24.675 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:21:28.440 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: reverse_property 'strategies' on relationship BacktestStrategy.user references relationship User.strategies, which does not reference mapper Mapper[BacktestStrategy(backtest_strategies)]
2025-08-26 15:21:28.441 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:21:28.443 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:24:58.594 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:25:33.376 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: reverse_property 'strategy' on relationship BacktestStrategy.backtests references relationship BacktestTask.strategy, which does not reference mapper Mapper[BacktestStrategy(backtest_strategies)]
2025-08-26 15:25:33.378 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:25:33.378 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:27:43.047 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:28:14.394 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'backtest_templates'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:28:14.395 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:28:14.400 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:29:58.735 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:30:24.362 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'risk_profile'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:30:24.363 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:30:24.364 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:35:06.800 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:35:34.795 | ERROR    | app.api.v1.auth:login:254 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'backtest_strategies'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 15:35:34.800 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:35:34.800 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 15:40:17.294 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:40:41.300 | ERROR    | app.api.v1.auth:test_login:483 | 测试登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:40:41.301 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:40:41.301 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:29.886 | ERROR    | app.api.v1.auth:test_login:483 | 测试登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:29.887 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:41:29.889 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败: 'str' object has no attribute 'isoformat'
2025-08-26 15:41:42.685 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:43:01.099 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 15:43:21.510 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 15:43:21.511 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - 邮箱或密码错误
2025-08-26 15:53:06.907 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:07.389 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:30.511 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:30.967 | ERROR    | app.services.news_sentiment_service:_load_models:184 | 模型加载失败: name 'torch' is not defined
2025-08-26 15:53:36.746 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:17:21.087 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:38:39.511 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:39:43.326 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 16:43:24.202 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-26 16:43:41.848 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-26 16:59:10.139 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'backtest_templates'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 16:59:10.141 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 16:59:10.142 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 16:59:57.039 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[BacktestTemplate(backtest_templates)]'. Original exception was: Mapper 'Mapper[User(users)]' has no property 'backtest_templates'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 16:59:57.043 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 16:59:57.044 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:03:45.284 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:05:36.012 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'risk_profile'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 17:05:36.014 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:05:36.016 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:10:01.873 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:11:24.640 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'stop_loss_orders'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 17:11:24.641 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:11:24.641 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:11:40.385 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[StopLossOrder(stop_loss_orders)]'. Original exception was: Mapper 'Mapper[User(users)]' has no property 'stop_loss_orders'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 17:11:40.387 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:11:40.389 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:15:24.373 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:16:01.042 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: Mapper 'Mapper[User(users)]' has no property 'report_templates'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-08-26 17:16:01.045 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:16:01.046 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:20:25.488 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:21:05.613 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: 1 validation error for UserResponse
updated_at
  Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<app.models.user.User obj...t at 0x000001C5C7DC1E20>, input_type=User]
    For further information visit https://errors.pydantic.dev/2.5/v/get_attribute_error
2025-08-26 17:21:05.614 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:21:05.616 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:34:54.155 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:36:54.056 | ERROR    | app.api.v1.auth:login:271 | 用户登录失败: 1 validation error for UserResponse
updated_at
  Error extracting attribute: MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s) [type=get_attribute_error, input_value=<app.models.user.User obj...t at 0x000002A76FBDA3C0>, input_type=User]
    For further information visit https://errors.pydantic.dev/2.5/v/get_attribute_error
2025-08-26 17:36:54.056 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:36:54.057 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:37:06.135 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:38:14.049 | ERROR    | app.api.v1.auth:login:291 | 用户登录失败: greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place? (Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-08-26 17:38:14.050 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-26 17:38:14.050 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 500 - 登录失败，请稍后重试
2025-08-26 17:38:21.510 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:39:52.925 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-26 17:42:20.103 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 09:56:38.515 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 09:58:09.475 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 10:55:57.511 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 10:55:57.515 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-27 10:55:57.757 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg2NTIwNywidHlwZSI6InJlZnJlc2gifQ.Ej7b_34f5WGS0t1GsObhblnYXzZlOmST1PcEVwTSf-4'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 10:55:57.759 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg2NTIwNywidHlwZSI6InJlZnJlc2gifQ.Ej7b_34f5WGS0t1GsObhblnYXzZlOmST1PcEVwTSf-4'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 10:58:19.566 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 10:58:19.572 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 11:38:43.993 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 11:38:44.001 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:19:23.222 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:19:23.229 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:33:06.233 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:33:06.239 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:36:41.319 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:36:41.352 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 13:46:51.855 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 13:50:51.431 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 13:52:06.710 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 13:52:06.712 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-27 13:53:05.110 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 13:53:05.111 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-27 13:56:56.668 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 13:59:18.241 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 14:25:51.878 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 14:26:01.267 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 14:31:06.291 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 14:59:23.494 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error connecting to localhost:6379. Multiple exceptions: [Errno 10061] Connect call failed ('::1', 6379, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 6379).
2025-08-27 15:08:42.638 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-27 15:10:21.943 | ERROR    | app.core.database:connect:164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.
2025-08-27 15:57:29.791 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 15:57:29.792 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - 邮箱或密码错误
2025-08-27 15:57:29.810 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg4NjIzNCwidHlwZSI6InJlZnJlc2gifQ.h4CGCOUuo76msakcIP_XqINTYo0gMQO6wZjX_hwUXhk'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 15:57:29.812 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg4NjIzNCwidHlwZSI6InJlZnJlc2gifQ.h4CGCOUuo76msakcIP_XqINTYo0gMQO6wZjX_hwUXhk'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 15:57:52.552 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 15:57:52.603 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 15:58:05.356 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 15:58:18.834 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 15:59:33.279 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 15:59:33.280 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 15:59:33.282 | ERROR    | app.middleware.logging:dispatch:79 | 请求异常
2025-08-27 15:59:33.283 | ERROR    | app.main:general_exception_handler:145 | 未处理的异常: TypeError: Object of type FormData is not JSON serializable
2025-08-27 16:40:39.958 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 16:40:39.962 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 16:40:59.073 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 16:41:05.908 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 16:49:37.033 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 16:49:37.035 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-27 16:49:37.044 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-27 16:49:37.044 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-27 16:49:37.099 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg2ODE5NSwidHlwZSI6InJlZnJlc2gifQ.7s9yK_CMr_8m5uXWTv4YsuX1tR9X8TW0gJAfxhaU1i0'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 16:49:37.103 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njg2ODE5NSwidHlwZSI6InJlZnJlc2gifQ.7s9yK_CMr_8m5uXWTv4YsuX1tR9X8TW0gJAfxhaU1i0'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-27 17:00:24.484 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:00:24.518 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.132 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.143 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.516 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.521 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.526 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:06:18.531 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - Not Found
2025-08-27 17:08:16.681 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:08:16.712 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:10:46.092 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:10:46.097 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:12:25.265 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:12:25.547 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:14.173 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:14.183 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:19.362 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:19.406 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:24.903 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:24.908 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:36.025 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:36.115 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:46.279 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:14:46.287 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:15:07.957 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:15:07.965 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:15:14.806 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-27 17:15:14.815 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:45:41.396 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 15:45:41.397 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 15:45:41.413 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 15:45:41.414 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 15:45:41.433 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njk3MTg4NywidHlwZSI6InJlZnJlc2gifQ.q2JBdbNaYmiTol4n2LukFLLQx_aPAWa-Ugr1UuiF3lQ'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-28 15:45:41.434 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'refresh_token'), 'msg': 'Field required', 'input': {'refreshToken': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjEsImV4cCI6MTc1Njk3MTg4NywidHlwZSI6InJlZnJlc2gifQ.q2JBdbNaYmiTol4n2LukFLLQx_aPAWa-Ugr1UuiF3lQ'}, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-28 15:45:49.419 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:45:49.490 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:45:54.995 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:45:55.110 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:54:32.149 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 15:54:32.150 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 15:59:53.901 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:59:53.912 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:59:59.683 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 15:59:59.705 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:00:10.278 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:00:10.293 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:01:08.997 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:01:09.003 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:01:25.920 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-28 16:01:25.921 | ERROR    | app.main:validation_exception_handler:126 | 请求验证失败: [{'type': 'missing', 'loc': ('body', 'username'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}, {'type': 'missing', 'loc': ('body', 'password'), 'msg': 'Field required', 'input': None, 'url': 'https://errors.pydantic.dev/2.5/v/missing'}]
2025-08-28 16:01:25.923 | ERROR    | app.middleware.logging:dispatch:79 | 请求异常
2025-08-28 16:01:25.924 | ERROR    | app.main:general_exception_handler:145 | 未处理的异常: TypeError: Object of type FormData is not JSON serializable
2025-08-28 16:03:10.587 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:03:10.588 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:05:25.985 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:05:25.987 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:14:13.804 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:14:13.806 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:14:54.833 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:14:54.834 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:18:18.752 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:18:18.754 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:20:17.754 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:20:17.755 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:21:20.042 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:21:20.045 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:25:07.178 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:25:07.179 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Could not validate credentials
2025-08-28 16:28:26.921 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:28:26.923 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 16:44:43.176 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:44:43.186 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:44:49.087 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:44:49.093 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:48:55.199 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:48:55.205 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:49:03.151 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:49:03.195 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:49:09.827 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:49:09.849 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 16:51:41.685 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 16:51:41.690 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:07:30.593 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 17:07:30.601 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 17:07:34.806 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 401 - Not authenticated
2025-08-28 17:07:51.461 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:07:51.462 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:07:51.477 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:07:51.478 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:09:55.896 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:09:55.897 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 400 - JQData账号验证失败: 用户不存在或密码错误;
如果未开通调用权限，请打开以下链接提交申请：https://www.joinquant.com/default/index/sdk
2025-08-28 17:15:04.950 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:15:04.951 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:15:04.968 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:15:04.969 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:15:22.773 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:15:22.774 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:15:22.790 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:15:22.791 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:16:08.071 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:16:08.072 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:16:08.103 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:16:08.105 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:21:26.836 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:21:26.836 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:21:26.851 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:21:26.852 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:22:05.874 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:22:05.875 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:22:05.904 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:22:05.905 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:22:52.192 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:22:52.192 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:24:15.963 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:24:15.965 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:24:16.000 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:24:16.001 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-28 17:24:30.444 | ERROR    | app.core.database:get_db:106 | 数据库会话错误: 
2025-08-28 17:24:30.447 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 400 - JQData账号验证失败: 用户不存在或密码错误;
如果未开通调用权限，请打开以下链接提交申请：https://www.joinquant.com/default/index/sdk
2025-08-29 12:17:39.509 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
2025-08-29 12:18:04.247 | ERROR    | app.main:http_exception_handler:110 | HTTP异常: 404 - JQData配置不存在，请先配置
