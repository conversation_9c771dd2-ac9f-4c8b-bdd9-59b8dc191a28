"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Hollow = void 0;
const color_1 = require("./color");
/**
 * A hollow path.
 */
const Hollow = (options, context) => {
    return (0, color_1.Color)(Object.assign({ fill: 'none', colorAttribute: 'stroke' }, options), context);
};
exports.Hollow = Hollow;
exports.Hollow.props = {
    defaultMarker: 'hvh',
    defaultEnterAnimation: 'fadeIn',
    defaultUpdateAnimation: 'morphing',
    defaultExitAnimation: 'fadeOut',
};
//# sourceMappingURL=hollow.js.map