"""
认证相关数据模式

定义用户认证、注册、登录等相关的请求和响应模式
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, validator


class LoginRequest(BaseModel):
    """登录请求"""
    
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=6, max_length=128, description="密码")
    remember: bool = Field(False, description="记住登录状态")


class RegisterRequest(BaseModel):
    """注册请求"""
    
    email: EmailStr = Field(..., description="邮箱地址")
    username: str = Field(
        ..., 
        min_length=3, 
        max_length=50, 
        regex="^[a-zA-Z0-9_-]+$",
        description="用户名"
    )
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    confirm_password: str = Field(..., description="确认密码")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    phone: Optional[str] = Field(
        None, 
        regex="^1[3-9]\\d{9}$",
        description="手机号码"
    )
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不一致')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        """密码强度验证"""
        if len(v) < 8:
            raise ValueError('密码长度至少8位')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('密码必须包含大写字母、小写字母和数字')
        
        return v


class TokenRefreshRequest(BaseModel):
    """Token刷新请求"""
    
    refresh_token: str = Field(..., description="刷新Token")


class PasswordChangeRequest(BaseModel):
    """密码修改请求"""
    
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的密码不一致')
        return v


class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    
    email: EmailStr = Field(..., description="邮箱地址")


class PasswordResetConfirmRequest(BaseModel):
    """密码重置确认请求"""
    
    token: str = Field(..., description="重置Token")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('两次输入的密码不一致')
        return v


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求"""
    
    token: str = Field(..., description="验证Token")


class UserResponse(BaseModel):
    """用户响应"""
    
    id: int = Field(..., description="用户ID")
    email: str = Field(..., description="邮箱地址")
    username: str = Field(..., description="用户名")
    full_name: Optional[str] = Field(None, description="真实姓名")
    phone: Optional[str] = Field(None, description="手机号码")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    subscription_type: str = Field(..., description="订阅类型")
    subscription_expires_at: Optional[datetime] = Field(None, description="订阅到期时间")
    api_quota_daily: int = Field(..., description="每日API配额")
    api_quota_used_today: int = Field(..., description="今日已使用配额")
    last_quota_reset: datetime = Field(..., description="配额最后重置时间")
    is_active: bool = Field(..., description="是否活跃")
    is_verified: bool = Field(..., description="是否已验证邮箱")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class UserProfileResponse(BaseModel):
    """用户资料响应"""
    
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, description="真实姓名")
    phone: Optional[str] = Field(None, description="手机号码")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    subscription_type: str = Field(..., description="订阅类型")
    subscription_expires_at: Optional[datetime] = Field(None, description="订阅到期时间")
    
    class Config:
        from_attributes = True


class UserProfileUpdateRequest(BaseModel):
    """用户资料更新请求"""
    
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    phone: Optional[str] = Field(
        None, 
        regex="^1[3-9]\\d{9}$",
        description="手机号码"
    )
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")


class LoginResponse(BaseModel):
    """登录响应"""
    
    access_token: str = Field(..., description="访问Token")
    refresh_token: str = Field(..., description="刷新Token")
    token_type: str = Field("bearer", description="Token类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: UserResponse = Field(..., description="用户信息")


class TokenResponse(BaseModel):
    """Token响应"""
    
    access_token: str = Field(..., description="访问Token")
    token_type: str = Field("bearer", description="Token类型")
    expires_in: int = Field(..., description="过期时间(秒)")


class UserStatsResponse(BaseModel):
    """用户统计响应"""
    
    total_api_calls: int = Field(..., description="总API调用次数")
    api_calls_today: int = Field(..., description="今日API调用次数")
    quota_usage_rate: float = Field(..., description="配额使用率")
    strategies_count: int = Field(..., description="策略数量")
    backtests_count: int = Field(..., description="回测数量")
    last_active_at: Optional[datetime] = Field(None, description="最后活跃时间")


class SessionResponse(BaseModel):
    """会话响应"""
    
    id: int = Field(..., description="会话ID")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    created_at: datetime = Field(..., description="创建时间")
    expires_at: datetime = Field(..., description="过期时间")
    is_current: bool = Field(..., description="是否当前会话")
    
    class Config:
        from_attributes = True


class TwoFactorSetupRequest(BaseModel):
    """双因子认证设置请求"""
    
    password: str = Field(..., description="当前密码")


class TwoFactorSetupResponse(BaseModel):
    """双因子认证设置响应"""
    
    secret: str = Field(..., description="密钥")
    qr_code_url: str = Field(..., description="二维码URL")
    backup_codes: list[str] = Field(..., description="备用代码")


class TwoFactorVerifyRequest(BaseModel):
    """双因子认证验证请求"""
    
    code: str = Field(..., regex="^\\d{6}$", description="验证码")


class TwoFactorDisableRequest(BaseModel):
    """双因子认证禁用请求"""
    
    password: str = Field(..., description="当前密码")
    code: str = Field(..., regex="^\\d{6}$", description="验证码")
