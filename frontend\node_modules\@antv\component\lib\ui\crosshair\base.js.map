{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../../src/ui/crosshair/base.ts"], "names": [], "mappings": ";;;;AAAA,mCAAuC;AAGvC,mCAAmD;AACnD,8BAA6B;AAC7B,uCAA0D;AAG1D;IAA+E,yCAAY;IAyCzF,uBAAY,OAA6B;QACvC,aAAa;QACb,OAAA,MAAK,YAAC,OAAO,EAAE,uCAA4B,CAAC,SAAC;IAC/C,CAAC;IA3BD,sBAAc,uCAAY;QAH1B;;WAEG;aACH;YACQ,IAAA,KAAA,eAAW,IAAI,CAAC,WAAW,EAAE,IAAA,EAA5B,EAAE,QAAA,EAAE,EAAE,QAAsB,CAAC;YAC9B,IAAA,KAAA,eAAS,IAAI,CAAC,OAAO,IAAA,EAApB,CAAC,QAAA,EAAE,CAAC,QAAgB,CAAC;YAC5B,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1B,CAAC;;;OAAA;IAOD,sBAAY,mCAAQ;aAApB;YACE,IAAM,KAAK,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;;;OAAA;IAED,sBAAY,yCAAc;aAA1B;YACE,IAAM,KAAK,GAAG,IAAA,oBAAa,EAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACrD,6CACK,KAAK,KACR,CAAC,EAAE,IAAI,CAAC,aAAa,IACrB;QACJ,CAAC;;;OAAA;IAOM,8BAAM,GAAb,UAAc,UAAmC,EAAE,SAAgB;QACjE,IAAM,KAAK,GAAG,IAAA,aAAM,EAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACvF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAA,aAAM,EAAC,KAAK,CAAC;aAC1B,sBAAsB,CAAC,eAAe,EAAE,cAAM,OAAA,IAAI,SAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAA5B,CAA4B,CAAC;aAC3E,MAAM,CAAC,QAAQ,CAAC;aAChB,IAAI,EAAE,CAAC;QACV,IAAI,CAAC,cAAc,GAAG,IAAA,aAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC;QAEpH,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACI,kCAAU,GAAjB,UAAkB,OAAc;QAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAnEa,iBAAG,GAAG,gBAAgB,CAAC;IAyEvC,oBAAC;CAAA,AA1ED,CAA+E,gBAAS,GA0EvF;AA1EqB,sCAAa"}