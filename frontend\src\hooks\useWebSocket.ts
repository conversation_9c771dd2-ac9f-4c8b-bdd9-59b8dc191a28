/**
 * WebSocket Hook
 * 
 * 提供WebSocket连接管理和实时数据订阅功能
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { message } from 'antd';

import { useAuth } from '@/store/auth';

interface WebSocketMessage {
  type: string;
  data?: any;
  symbol?: string;
  message?: string;
  timestamp?: string;
}

interface UseWebSocketOptions {
  url: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
}

interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
}

export const useWebSocket = (options: UseWebSocketOptions) => {
  const {
    url,
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    onMessage,
    onConnect,
    onDisconnect,
    onError,
  } = options;

  const { tokens } = useAuth();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastMessage: null,
  });

  // 构建WebSocket URL
  const buildUrl = useCallback(() => {
    const wsUrl = new URL(url, window.location.origin);
    wsUrl.protocol = wsUrl.protocol === 'https:' ? 'wss:' : 'ws:';
    
    if (tokens?.accessToken) {
      wsUrl.searchParams.set('token', tokens.accessToken);
    }
    
    return wsUrl.toString();
  }, [url, tokens?.accessToken]);

  // 发送消息
  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // 订阅股票
  const subscribe = useCallback((symbol: string) => {
    return sendMessage({
      type: 'subscribe',
      symbol,
    });
  }, [sendMessage]);

  // 取消订阅股票
  const unsubscribe = useCallback((symbol: string) => {
    return sendMessage({
      type: 'unsubscribe',
      symbol,
    });
  }, [sendMessage]);

  // 发送心跳
  const sendPing = useCallback(() => {
    return sendMessage({
      type: 'ping',
    });
  }, [sendMessage]);

  // 连接WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const wsUrl = buildUrl();
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null,
        }));
        
        reconnectCountRef.current = 0;
        
        // 启动心跳
        pingIntervalRef.current = setInterval(() => {
          sendPing();
        }, 30000); // 30秒心跳
        
        onConnect?.();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          setState(prev => ({
            ...prev,
            lastMessage: message,
          }));
          
          onMessage?.(message);
          
          // 处理特殊消息类型
          if (message.type === 'error') {
            console.error('WebSocket错误:', message.message);
          }
          
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
        }));
        
        // 清理心跳
        if (pingIntervalRef.current) {
          clearInterval(pingIntervalRef.current);
          pingIntervalRef.current = null;
        }
        
        onDisconnect?.();
        
        // 自动重连
        if (reconnectCountRef.current < reconnectAttempts && !event.wasClean) {
          reconnectCountRef.current++;
          console.log(`WebSocket连接断开，${reconnectInterval}ms后尝试第${reconnectCountRef.current}次重连`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          setState(prev => ({
            ...prev,
            error: '连接失败，已达到最大重连次数',
          }));
          message.error('WebSocket连接失败，请刷新页面重试');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setState(prev => ({
          ...prev,
          error: 'WebSocket连接错误',
          isConnecting: false,
        }));
        
        onError?.(error);
      };

    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      setState(prev => ({
        ...prev,
        error: '创建WebSocket连接失败',
        isConnecting: false,
      }));
    }
  }, [buildUrl, reconnectAttempts, reconnectInterval, onConnect, onMessage, onDisconnect, onError, sendPing]);

  // 断开连接
  const disconnect = useCallback(() => {
    // 清理重连定时器
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    // 清理心跳定时器
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
    
    // 关闭WebSocket连接
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setState({
      isConnected: false,
      isConnecting: false,
      error: null,
      lastMessage: null,
    });
  }, []);

  // 重连
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      reconnectCountRef.current = 0;
      connect();
    }, 100);
  }, [disconnect, connect]);

  // 自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // 监听认证状态变化
  useEffect(() => {
    if (tokens?.accessToken && state.isConnected) {
      // Token变化时重连
      reconnect();
    }
  }, [tokens?.accessToken, reconnect, state.isConnected]);

  return {
    ...state,
    connect,
    disconnect,
    reconnect,
    sendMessage,
    subscribe,
    unsubscribe,
  };
};

// 市场数据WebSocket Hook
export const useMarketWebSocket = (symbols: string[] = []) => {
  const [priceData, setPriceData] = useState<Record<string, any>>({});
  const [marketStatus, setMarketStatus] = useState<string>('unknown');

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'price_update':
        if (message.symbol && message.data) {
          setPriceData(prev => ({
            ...prev,
            [message.symbol!]: message.data,
          }));
        }
        break;
        
      case 'system_status':
        if (message.data?.market_status) {
          setMarketStatus(message.data.market_status);
        }
        break;
        
      case 'market_subscription':
        console.log('市场数据订阅成功:', message.message);
        break;
        
      default:
        break;
    }
  }, []);

  const ws = useWebSocket({
    url: `/api/v1/ws/market?symbols=${symbols.join(',')}`,
    onMessage: handleMessage,
    onConnect: () => {
      console.log('市场数据WebSocket连接成功');
    },
    onDisconnect: () => {
      console.log('市场数据WebSocket连接断开');
    },
  });

  return {
    ...ws,
    priceData,
    marketStatus,
  };
};

// 投资组合WebSocket Hook
export const usePortfolioWebSocket = () => {
  const [portfolioData, setPortfolioData] = useState<any>(null);
  const [notifications, setNotifications] = useState<any[]>([]);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'portfolio_update':
        setPortfolioData(message.data);
        break;
        
      case 'notification':
        setNotifications(prev => [message.data, ...prev.slice(0, 99)]); // 保留最近100条
        break;
        
      case 'price_alert':
        message.success(`价格提醒: ${message.symbol} ${message.data?.message}`);
        break;
        
      default:
        break;
    }
  }, []);

  const ws = useWebSocket({
    url: '/api/v1/ws/portfolio',
    onMessage: handleMessage,
    onConnect: () => {
      console.log('投资组合WebSocket连接成功');
    },
  });

  return {
    ...ws,
    portfolioData,
    notifications,
  };
};
