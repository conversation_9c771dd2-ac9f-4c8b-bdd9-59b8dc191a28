"""
图分析服务

提供图结构分析、中心性计算、社区发现、路径分析等功能
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import warnings
warnings.filterwarnings('ignore')

# 尝试导入图分析库
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

try:
    import igraph as ig
    IGRAPH_AVAILABLE = True
except ImportError:
    IGRAPH_AVAILABLE = False

try:
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.metrics import silhouette_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from app.core.logging import logger
from app.models.knowledge_graph import (
    KnowledgeGraph, FinancialEntity, EntityRelation, GraphAnalysis
)


class GraphMetricsCalculator:
    """图指标计算器"""
    
    def __init__(self):
        pass
    
    def calculate_basic_metrics(self, graph_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算基本图指标"""
        try:
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            metrics = {
                'node_count': len(nodes),
                'edge_count': len(edges),
                'density': 0.0,
                'average_degree': 0.0,
                'clustering_coefficient': 0.0,
                'diameter': 0,
                'average_path_length': 0.0
            }
            
            if not NETWORKX_AVAILABLE:
                logger.warning("NetworkX不可用，返回基本指标")
                return metrics
            
            # 构建NetworkX图
            G = self._build_networkx_graph(graph_data)
            
            if G.number_of_nodes() == 0:
                return metrics
            
            # 计算密度
            metrics['density'] = nx.density(G)
            
            # 计算平均度
            degrees = [d for n, d in G.degree()]
            metrics['average_degree'] = np.mean(degrees) if degrees else 0.0
            
            # 计算聚类系数
            if G.number_of_nodes() > 2:
                metrics['clustering_coefficient'] = nx.average_clustering(G)
            
            # 计算连通性指标
            if nx.is_connected(G):
                metrics['diameter'] = nx.diameter(G)
                metrics['average_path_length'] = nx.average_shortest_path_length(G)
            else:
                # 对于非连通图，计算最大连通分量的指标
                largest_cc = max(nx.connected_components(G), key=len)
                subgraph = G.subgraph(largest_cc)
                if subgraph.number_of_nodes() > 1:
                    metrics['diameter'] = nx.diameter(subgraph)
                    metrics['average_path_length'] = nx.average_shortest_path_length(subgraph)
            
            return metrics
            
        except Exception as e:
            logger.error(f"基本图指标计算失败: {e}")
            return {
                'node_count': 0,
                'edge_count': 0,
                'density': 0.0,
                'average_degree': 0.0,
                'clustering_coefficient': 0.0,
                'diameter': 0,
                'average_path_length': 0.0
            }
    
    def calculate_centrality_measures(self, graph_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算中心性度量"""
        try:
            if not NETWORKX_AVAILABLE:
                logger.warning("NetworkX不可用，无法计算中心性")
                return {}
            
            G = self._build_networkx_graph(graph_data)
            
            if G.number_of_nodes() == 0:
                return {}
            
            centrality_measures = {}
            
            # 度中心性
            degree_centrality = nx.degree_centrality(G)
            centrality_measures['degree_centrality'] = degree_centrality
            
            # 介数中心性
            if G.number_of_nodes() > 2:
                betweenness_centrality = nx.betweenness_centrality(G)
                centrality_measures['betweenness_centrality'] = betweenness_centrality
            
            # 接近中心性
            if nx.is_connected(G):
                closeness_centrality = nx.closeness_centrality(G)
                centrality_measures['closeness_centrality'] = closeness_centrality
            
            # 特征向量中心性
            try:
                eigenvector_centrality = nx.eigenvector_centrality(G, max_iter=1000)
                centrality_measures['eigenvector_centrality'] = eigenvector_centrality
            except:
                logger.warning("特征向量中心性计算失败")
            
            # PageRank
            pagerank = nx.pagerank(G)
            centrality_measures['pagerank'] = pagerank
            
            return centrality_measures
            
        except Exception as e:
            logger.error(f"中心性度量计算失败: {e}")
            return {}
    
    def _build_networkx_graph(self, graph_data: Dict[str, Any]) -> nx.Graph:
        """构建NetworkX图"""
        try:
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            # 检查是否有有向边
            has_directed = any(edge.get('directed', True) for edge in edges)
            
            if has_directed:
                G = nx.DiGraph()
            else:
                G = nx.Graph()
            
            # 添加节点
            for node in nodes:
                G.add_node(
                    node['id'],
                    name=node.get('name', ''),
                    type=node.get('type', ''),
                    **node.get('properties', {})
                )
            
            # 添加边
            for edge in edges:
                G.add_edge(
                    edge['source'],
                    edge['target'],
                    weight=edge.get('weight', 1.0),
                    type=edge.get('type', ''),
                    **edge.get('properties', {})
                )
            
            return G
            
        except Exception as e:
            logger.error(f"NetworkX图构建失败: {e}")
            return nx.Graph()


class CommunityDetector:
    """社区发现器"""
    
    def __init__(self):
        pass
    
    def detect_communities(
        self,
        graph_data: Dict[str, Any],
        method: str = 'louvain',
        **kwargs
    ) -> Dict[str, Any]:
        """社区发现"""
        try:
            if not NETWORKX_AVAILABLE:
                logger.warning("NetworkX不可用，无法进行社区发现")
                return {}
            
            G = self._build_networkx_graph(graph_data)
            
            if G.number_of_nodes() < 2:
                return {'communities': [], 'modularity': 0.0}
            
            if method == 'louvain':
                return self._louvain_community_detection(G, **kwargs)
            elif method == 'greedy_modularity':
                return self._greedy_modularity_communities(G, **kwargs)
            elif method == 'label_propagation':
                return self._label_propagation_communities(G, **kwargs)
            elif method == 'spectral':
                return self._spectral_clustering_communities(G, **kwargs)
            else:
                logger.warning(f"未知的社区发现方法: {method}")
                return {}
                
        except Exception as e:
            logger.error(f"社区发现失败: {e}")
            return {}
    
    def _louvain_community_detection(self, G: nx.Graph, **kwargs) -> Dict[str, Any]:
        """Louvain社区发现"""
        try:
            # 尝试导入community库
            try:
                import community as community_louvain
            except ImportError:
                logger.warning("python-louvain库不可用")
                return self._greedy_modularity_communities(G, **kwargs)
            
            # 转换为无向图（如果需要）
            if G.is_directed():
                G_undirected = G.to_undirected()
            else:
                G_undirected = G
            
            # 执行Louvain算法
            partition = community_louvain.best_partition(G_undirected)
            
            # 计算模块度
            modularity = community_louvain.modularity(partition, G_undirected)
            
            # 组织社区结果
            communities = {}
            for node, community_id in partition.items():
                if community_id not in communities:
                    communities[community_id] = []
                communities[community_id].append(node)
            
            community_list = list(communities.values())
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'community_count': len(community_list),
                'partition': partition,
                'method': 'louvain'
            }
            
        except Exception as e:
            logger.error(f"Louvain社区发现失败: {e}")
            return self._greedy_modularity_communities(G, **kwargs)
    
    def _greedy_modularity_communities(self, G: nx.Graph, **kwargs) -> Dict[str, Any]:
        """贪心模块度社区发现"""
        try:
            # 转换为无向图
            if G.is_directed():
                G_undirected = G.to_undirected()
            else:
                G_undirected = G
            
            # 使用NetworkX的贪心模块度算法
            communities = nx.community.greedy_modularity_communities(G_undirected)
            
            # 计算模块度
            modularity = nx.community.modularity(G_undirected, communities)
            
            # 转换为列表格式
            community_list = [list(community) for community in communities]
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'community_count': len(community_list),
                'method': 'greedy_modularity'
            }
            
        except Exception as e:
            logger.error(f"贪心模块度社区发现失败: {e}")
            return {}
    
    def _label_propagation_communities(self, G: nx.Graph, **kwargs) -> Dict[str, Any]:
        """标签传播社区发现"""
        try:
            # 转换为无向图
            if G.is_directed():
                G_undirected = G.to_undirected()
            else:
                G_undirected = G
            
            # 使用标签传播算法
            communities = nx.community.label_propagation_communities(G_undirected)
            
            # 计算模块度
            modularity = nx.community.modularity(G_undirected, communities)
            
            # 转换为列表格式
            community_list = [list(community) for community in communities]
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'community_count': len(community_list),
                'method': 'label_propagation'
            }
            
        except Exception as e:
            logger.error(f"标签传播社区发现失败: {e}")
            return {}
    
    def _spectral_clustering_communities(self, G: nx.Graph, **kwargs) -> Dict[str, Any]:
        """谱聚类社区发现"""
        try:
            if not SKLEARN_AVAILABLE:
                logger.warning("scikit-learn不可用，无法进行谱聚类")
                return {}
            
            # 获取邻接矩阵
            adj_matrix = nx.adjacency_matrix(G).toarray()
            
            if adj_matrix.shape[0] < 2:
                return {}
            
            # 确定聚类数量
            n_clusters = kwargs.get('n_clusters', min(10, max(2, adj_matrix.shape[0] // 10)))
            
            # 执行谱聚类
            from sklearn.cluster import SpectralClustering
            
            spectral = SpectralClustering(
                n_clusters=n_clusters,
                affinity='precomputed',
                random_state=42
            )
            
            labels = spectral.fit_predict(adj_matrix)
            
            # 组织社区结果
            communities = {}
            nodes = list(G.nodes())
            
            for i, label in enumerate(labels):
                if label not in communities:
                    communities[label] = []
                communities[label].append(nodes[i])
            
            community_list = list(communities.values())
            
            # 计算模块度
            communities_set = [set(community) for community in community_list]
            modularity = nx.community.modularity(G, communities_set)
            
            return {
                'communities': community_list,
                'modularity': modularity,
                'community_count': len(community_list),
                'method': 'spectral_clustering'
            }
            
        except Exception as e:
            logger.error(f"谱聚类社区发现失败: {e}")
            return {}
    
    def _build_networkx_graph(self, graph_data: Dict[str, Any]) -> nx.Graph:
        """构建NetworkX图"""
        try:
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            G = nx.Graph()  # 社区发现通常使用无向图
            
            # 添加节点
            for node in nodes:
                G.add_node(node['id'])
            
            # 添加边
            for edge in edges:
                G.add_edge(
                    edge['source'],
                    edge['target'],
                    weight=edge.get('weight', 1.0)
                )
            
            return G
            
        except Exception as e:
            logger.error(f"NetworkX图构建失败: {e}")
            return nx.Graph()


class PathAnalyzer:
    """路径分析器"""
    
    def __init__(self):
        pass
    
    def find_shortest_paths(
        self,
        graph_data: Dict[str, Any],
        source_nodes: List[int],
        target_nodes: List[int],
        max_paths: int = 10
    ) -> Dict[str, Any]:
        """寻找最短路径"""
        try:
            if not NETWORKX_AVAILABLE:
                logger.warning("NetworkX不可用，无法进行路径分析")
                return {}
            
            G = self._build_networkx_graph(graph_data)
            
            paths_result = {
                'paths': [],
                'path_lengths': [],
                'average_path_length': 0.0
            }
            
            all_paths = []
            
            for source in source_nodes:
                for target in target_nodes:
                    if source != target and G.has_node(source) and G.has_node(target):
                        try:
                            if nx.has_path(G, source, target):
                                path = nx.shortest_path(G, source, target, weight='weight')
                                path_length = nx.shortest_path_length(G, source, target, weight='weight')
                                
                                all_paths.append({
                                    'source': source,
                                    'target': target,
                                    'path': path,
                                    'length': path_length,
                                    'hop_count': len(path) - 1
                                })
                        except nx.NetworkXNoPath:
                            continue
            
            # 按路径长度排序并限制数量
            all_paths.sort(key=lambda x: x['length'])
            paths_result['paths'] = all_paths[:max_paths]
            
            if all_paths:
                paths_result['path_lengths'] = [p['length'] for p in all_paths]
                paths_result['average_path_length'] = np.mean(paths_result['path_lengths'])
            
            return paths_result
            
        except Exception as e:
            logger.error(f"最短路径分析失败: {e}")
            return {}
    
    def find_influential_paths(
        self,
        graph_data: Dict[str, Any],
        centrality_measures: Dict[str, Any],
        top_k: int = 5
    ) -> Dict[str, Any]:
        """寻找影响力路径"""
        try:
            if not NETWORKX_AVAILABLE:
                return {}
            
            G = self._build_networkx_graph(graph_data)
            
            # 获取高中心性节点
            pagerank = centrality_measures.get('pagerank', {})
            if not pagerank:
                return {}
            
            # 选择前k个高中心性节点
            top_nodes = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:top_k]
            top_node_ids = [node_id for node_id, _ in top_nodes]
            
            # 寻找这些节点之间的路径
            influential_paths = []
            
            for i, source in enumerate(top_node_ids):
                for target in top_node_ids[i+1:]:
                    if nx.has_path(G, source, target):
                        path = nx.shortest_path(G, source, target, weight='weight')
                        path_length = nx.shortest_path_length(G, source, target, weight='weight')
                        
                        # 计算路径影响力（基于路径上节点的中心性）
                        path_influence = sum(pagerank.get(node, 0) for node in path)
                        
                        influential_paths.append({
                            'source': source,
                            'target': target,
                            'path': path,
                            'length': path_length,
                            'influence_score': path_influence,
                            'hop_count': len(path) - 1
                        })
            
            # 按影响力排序
            influential_paths.sort(key=lambda x: x['influence_score'], reverse=True)
            
            return {
                'influential_paths': influential_paths[:10],
                'top_nodes': top_node_ids,
                'average_influence': np.mean([p['influence_score'] for p in influential_paths]) if influential_paths else 0
            }
            
        except Exception as e:
            logger.error(f"影响力路径分析失败: {e}")
            return {}
    
    def _build_networkx_graph(self, graph_data: Dict[str, Any]) -> nx.Graph:
        """构建NetworkX图"""
        try:
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            # 检查是否有有向边
            has_directed = any(edge.get('directed', True) for edge in edges)
            
            if has_directed:
                G = nx.DiGraph()
            else:
                G = nx.Graph()
            
            # 添加节点
            for node in nodes:
                G.add_node(node['id'])
            
            # 添加边
            for edge in edges:
                G.add_edge(
                    edge['source'],
                    edge['target'],
                    weight=edge.get('weight', 1.0)
                )
            
            return G
            
        except Exception as e:
            logger.error(f"NetworkX图构建失败: {e}")
            return nx.Graph()


class GraphAnalysisService:
    """图分析服务"""
    
    def __init__(self):
        self.metrics_calculator = GraphMetricsCalculator()
        self.community_detector = CommunityDetector()
        self.path_analyzer = PathAnalyzer()
    
    async def analyze_graph(
        self,
        user_id: int,
        knowledge_graph_id: int,
        analysis_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """分析图"""
        try:
            # 获取知识图谱
            result = await db.execute(
                select(KnowledgeGraph).where(
                    and_(
                        KnowledgeGraph.id == knowledge_graph_id,
                        KnowledgeGraph.user_id == user_id
                    )
                )
            )
            knowledge_graph = result.scalar_one_or_none()
            
            if not knowledge_graph:
                return {'success': False, 'error': 'Knowledge graph not found'}
            
            graph_data = knowledge_graph.graph_data
            if not graph_data:
                return {'success': False, 'error': 'Graph data not available'}
            
            # 执行各种分析
            analysis_results = {}
            
            # 基本图指标
            if analysis_config.get('calculate_basic_metrics', True):
                basic_metrics = self.metrics_calculator.calculate_basic_metrics(graph_data)
                analysis_results['graph_metrics'] = basic_metrics
            
            # 中心性度量
            if analysis_config.get('calculate_centrality', True):
                centrality_measures = self.metrics_calculator.calculate_centrality_measures(graph_data)
                analysis_results['centrality_measures'] = centrality_measures
                
                # 识别重要节点
                if centrality_measures.get('pagerank'):
                    pagerank = centrality_measures['pagerank']
                    top_nodes = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:10]
                    analysis_results['influential_nodes'] = [
                        {'node_id': node_id, 'score': score} for node_id, score in top_nodes
                    ]
            
            # 社区发现
            if analysis_config.get('detect_communities', True):
                community_method = analysis_config.get('community_method', 'louvain')
                community_results = self.community_detector.detect_communities(
                    graph_data, method=community_method
                )
                analysis_results['community_detection_results'] = community_results
            
            # 路径分析
            if analysis_config.get('analyze_paths', False):
                source_nodes = analysis_config.get('source_nodes', [])
                target_nodes = analysis_config.get('target_nodes', [])
                
                if source_nodes and target_nodes:
                    path_results = self.path_analyzer.find_shortest_paths(
                        graph_data, source_nodes, target_nodes
                    )
                    analysis_results['path_analysis'] = path_results
                
                # 影响力路径分析
                if 'centrality_measures' in analysis_results:
                    influential_paths = self.path_analyzer.find_influential_paths(
                        graph_data, analysis_results['centrality_measures']
                    )
                    analysis_results['influential_paths'] = influential_paths
            
            # 保存分析结果
            graph_analysis = GraphAnalysis(
                user_id=user_id,
                knowledge_graph_id=knowledge_graph_id,
                analysis_name=analysis_config.get('name', f'Analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}'),
                analysis_type=analysis_config.get('type', 'comprehensive'),
                description=analysis_config.get('description', ''),
                analysis_config=analysis_config,
                algorithms_used=list(analysis_config.keys()),
                graph_metrics=analysis_results.get('graph_metrics', {}),
                centrality_measures=analysis_results.get('centrality_measures', {}),
                community_detection_results=analysis_results.get('community_detection_results', {}),
                node_importance_scores=analysis_results.get('influential_nodes', []),
                status='completed',
                completed_at=datetime.utcnow()
            )
            
            db.add(graph_analysis)
            await db.commit()
            
            return {
                'success': True,
                'analysis_id': graph_analysis.id,
                'results': analysis_results
            }
            
        except Exception as e:
            logger.error(f"图分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# 全局图分析服务实例
graph_analysis_service = GraphAnalysisService()
