'use client';

/**
 * 高级功能展示页面
 * 
 * 展示所有新增的高级功能
 */

import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Alert,
  Divider,
  message
} from 'antd';
import {
  RobotOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  ExperimentOutlined,
  DashboardOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { Combined3DCharts } from '@/components/charts/Advanced3DCharts';
import { ChartConfigurator } from '@/components/charts/ChartConfigurator';
import { PersonalizationSettings } from '@/components/settings/PersonalizationSettings';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

function AdvancedContent() {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);

  // 模拟数据
  const performanceStats = {
    totalModels: 12,
    activeModels: 8,
    avgAccuracy: 87.5,
    totalPredictions: 1250,
    successfulRebalances: 15,
    costSavings: 12500
  };

  const modelPerformance = [
    { name: 'LSTM-平安银行', accuracy: 89.2, status: 'active' },
    { name: 'CNN-招商银行', accuracy: 85.7, status: 'active' },
    { name: 'Transformer-贵州茅台', accuracy: 92.1, status: 'active' },
    { name: 'LSTM-五粮液', accuracy: 83.4, status: 'training' }
  ];

  const rebalanceHistory = [
    { date: '2024-08-25', type: '自动', trades: 3, cost: 125.50, drift: 0.08 },
    { date: '2024-08-20', type: '手动', trades: 2, cost: 89.20, drift: 0.06 },
    { date: '2024-08-15', type: '自动', trades: 4, cost: 156.80, drift: 0.09 }
  ];

  // 3D图表数据
  const scatter3DData = [
    { name: '平安银行', value: [15.2, 8.5, 0.56], category: 'positive' },
    { name: '招商银行', value: [12.8, 12.3, 0.96], category: 'positive' },
    { name: '贵州茅台', value: [18.5, 15.7, 0.85], category: 'positive' },
    { name: '五粮液', value: [20.1, 11.2, 0.56], category: 'positive' },
    { name: '比亚迪', value: [25.3, 22.8, 0.90], category: 'positive' }
  ];

  const handleTrainModel = async () => {
    setLoading(true);
    try {
      // 模拟训练过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('模型训练已启动！');
    } catch (error) {
      message.error('训练启动失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRebalance = async () => {
    setLoading(true);
    try {
      // 模拟再平衡过程
      await new Promise(resolve => setTimeout(resolve, 1500));
      message.success('再平衡计划已执行！');
    } catch (error) {
      message.error('再平衡执行失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                <ThunderboltOutlined />
                高级功能中心
              </Space>
            </Title>
            <Text type="secondary" className="text-lg">
              探索AI驱动的投资分析与智能化投资组合管理
            </Text>
          </div>
          <Space>
            <Button icon={<ExperimentOutlined />} type="dashed">
              实验室功能
            </Button>
            <Button icon={<SettingOutlined />} type="primary">
              功能设置
            </Button>
          </Space>
        </div>
      </motion.div>

      {/* 功能概览 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Alert
          message="🚀 新功能上线"
          description="我们为您带来了全新的AI深度学习模型、3D可视化图表、动态再平衡系统和个性化设置功能。立即体验智能化投资管理的强大能力！"
          type="info"
          showIcon
          closable
        />
      </motion.div>

      {/* 主要功能标签页 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Space>
                <DashboardOutlined />
                功能概览
              </Space>
            }
            key="overview"
          >
            <Row gutter={[24, 24]}>
              {/* 性能统计 */}
              <Col xs={24} lg={12}>
                <Card title="系统性能统计" extra={<Tag color="green">实时</Tag>}>
                  <Row gutter={[16, 16]}>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="AI模型总数"
                        value={performanceStats.totalModels}
                        prefix={<RobotOutlined />}
                      />
                    </Col>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="活跃模型"
                        value={performanceStats.activeModels}
                        valueStyle={{ color: '#3f8600' }}
                      />
                    </Col>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="平均准确率"
                        value={performanceStats.avgAccuracy}
                        suffix="%"
                        precision={1}
                      />
                    </Col>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="预测次数"
                        value={performanceStats.totalPredictions}
                      />
                    </Col>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="再平衡次数"
                        value={performanceStats.successfulRebalances}
                      />
                    </Col>
                    <Col xs={12} md={8}>
                      <Statistic
                        title="节省成本"
                        value={performanceStats.costSavings}
                        prefix="¥"
                        valueStyle={{ color: '#cf1322' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* 模型性能 */}
              <Col xs={24} lg={12}>
                <Card title="模型性能排行" extra={<Button size="small">查看全部</Button>}>
                  <div className="space-y-4">
                    {modelPerformance.map((model, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                            <Text strong>{index + 1}</Text>
                          </div>
                          <div>
                            <Text strong>{model.name}</Text>
                            <br />
                            <Tag color={model.status === 'active' ? 'green' : 'orange'}>
                              {model.status === 'active' ? '运行中' : '训练中'}
                            </Tag>
                          </div>
                        </div>
                        <div className="text-right">
                          <Text strong>{model.accuracy}%</Text>
                          <br />
                          <Progress
                            percent={model.accuracy}
                            size="small"
                            showInfo={false}
                            strokeColor={model.accuracy > 85 ? '#52c41a' : '#faad14'}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>

              {/* 快速操作 */}
              <Col xs={24}>
                <Card title="快速操作">
                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={12} md={6}>
                      <Button
                        type="primary"
                        size="large"
                        icon={<RobotOutlined />}
                        loading={loading}
                        onClick={handleTrainModel}
                        block
                      >
                        训练新模型
                      </Button>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Button
                        size="large"
                        icon={<ReloadOutlined />}
                        loading={loading}
                        onClick={handleRebalance}
                        block
                      >
                        执行再平衡
                      </Button>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Button
                        size="large"
                        icon={<BarChartOutlined />}
                        onClick={() => setActiveTab('3d-charts')}
                        block
                      >
                        3D图表分析
                      </Button>
                    </Col>
                    <Col xs={24} sm={12} md={6}>
                      <Button
                        size="large"
                        icon={<SettingOutlined />}
                        onClick={() => setActiveTab('personalization')}
                        block
                      >
                        个性化设置
                      </Button>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <RobotOutlined />
                深度学习
              </Space>
            }
            key="deep-learning"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={16}>
                <Card title="模型训练中心" extra={<Tag color="blue">AI驱动</Tag>}>
                  <div className="space-y-4">
                    <Alert
                      message="深度学习模型"
                      description="支持LSTM、CNN、Transformer等多种深度学习模型，用于股价预测和趋势分析。"
                      type="info"
                      showIcon
                    />
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card size="small" title="LSTM模型">
                        <Text type="secondary">长短期记忆网络</Text>
                        <br />
                        <Text>适用于时间序列预测</Text>
                        <Divider />
                        <Button type="primary" size="small" block>
                          开始训练
                        </Button>
                      </Card>
                      
                      <Card size="small" title="CNN模型">
                        <Text type="secondary">卷积神经网络</Text>
                        <br />
                        <Text>适用于模式识别</Text>
                        <Divider />
                        <Button type="primary" size="small" block>
                          开始训练
                        </Button>
                      </Card>
                      
                      <Card size="small" title="Transformer">
                        <Text type="secondary">注意力机制模型</Text>
                        <br />
                        <Text>适用于复杂序列分析</Text>
                        <Divider />
                        <Button type="primary" size="small" block>
                          开始训练
                        </Button>
                      </Card>
                    </div>
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Card title="训练状态">
                  <div className="space-y-3">
                    {modelPerformance.map((model, index) => (
                      <div key={index} className="p-3 border rounded">
                        <div className="flex justify-between items-center mb-2">
                          <Text strong>{model.name}</Text>
                          <Tag color={model.status === 'active' ? 'green' : 'orange'}>
                            {model.status === 'active' ? '完成' : '训练中'}
                          </Tag>
                        </div>
                        <Progress
                          percent={model.status === 'active' ? 100 : 65}
                          size="small"
                          status={model.status === 'active' ? 'success' : 'active'}
                        />
                        <Text type="secondary" className="text-sm">
                          准确率: {model.accuracy}%
                        </Text>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                3D图表
              </Space>
            }
            key="3d-charts"
          >
            <Card title="3D可视化图表" extra={<Tag color="purple">立体展示</Tag>}>
              <Alert
                message="3D图表功能"
                description="提供3D散点图、柱状图、曲面图等多种立体可视化方式，让数据分析更加直观。"
                type="success"
                showIcon
                className="mb-4"
              />
              <Combined3DCharts
                scatterData={scatter3DData}
                barData={[]}
                surfaceData={[]}
                geoData={[]}
              />
            </Card>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <ReloadOutlined />
                动态再平衡
              </Space>
            }
            key="rebalancing"
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={16}>
                <Card title="再平衡历史" extra={<Tag color="cyan">智能优化</Tag>}>
                  <div className="space-y-3">
                    {rebalanceHistory.map((record, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <Text strong>{record.date}</Text>
                            <Tag color={record.type === '自动' ? 'green' : 'blue'} className="ml-2">
                              {record.type}再平衡
                            </Tag>
                            <br />
                            <Text type="secondary">
                              执行交易: {record.trades}笔 | 成本: ¥{record.cost} | 偏离度: {record.drift}%
                            </Text>
                          </div>
                          <TrophyOutlined className="text-yellow-500" />
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
              
              <Col xs={24} lg={8}>
                <Card title="再平衡设置">
                  <div className="space-y-4">
                    <div>
                      <Text strong>偏离阈值</Text>
                      <br />
                      <Progress percent={50} size="small" />
                      <Text type="secondary">5%</Text>
                    </div>
                    
                    <div>
                      <Text strong>最小交易金额</Text>
                      <br />
                      <Text>¥1,000</Text>
                    </div>
                    
                    <div>
                      <Text strong>自动执行</Text>
                      <br />
                      <Tag color="green">已启用</Tag>
                    </div>
                    
                    <Button type="primary" block>
                      修改设置
                    </Button>
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                个性化设置
              </Space>
            }
            key="personalization"
          >
            <PersonalizationSettings />
          </TabPane>

          <TabPane
            tab={
              <Space>
                <BarChartOutlined />
                图表配置
              </Space>
            }
            key="chart-config"
          >
            <ChartConfigurator
              onConfigChange={(config) => console.log('Config changed:', config)}
              onSave={(config) => {
                console.log('Config saved:', config);
                message.success('图表配置已保存');
              }}
              onPreview={(config) => {
                console.log('Config preview:', config);
                message.info('图表预览已更新');
              }}
            />
          </TabPane>
        </Tabs>
      </motion.div>
    </div>
  );
}

export default function AdvancedPage() {
  return (
    <ClientAuthWrapper>
      <AdvancedContent />
    </ClientAuthWrapper>
  );
}
