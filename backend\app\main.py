"""
FastAPI主应用程序

JQData量化数据平台的核心应用入口
"""

from contextlib import asynccontextmanager
from typing import Any, Dict

from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.core.config import settings
from app.core.database import close_db, init_db, redis_manager
from app.core.logging import logger
from app.middleware.security import SecurityMiddleware
from app.middleware.logging import LoggingMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 JQData量化平台启动中...")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("✅ 数据库初始化完成")
        
        # 连接Redis
        await redis_manager.connect()
        logger.info("✅ Redis连接成功")
        
        logger.info("🎉 JQData量化平台启动完成")
        
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("🛑 JQData量化平台关闭中...")
    
    try:
        # 关闭数据库连接
        await close_db()
        logger.info("✅ 数据库连接已关闭")
        
        # 断开Redis连接
        await redis_manager.disconnect()
        logger.info("✅ Redis连接已断开")
        
        logger.info("👋 JQData量化平台已关闭")
        
    except Exception as e:
        logger.error(f"❌ 应用关闭失败: {e}")


# 创建FastAPI应用
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="企业级量化数据获取、处理和分析平台",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan,
)

# =============================================================================
# 中间件配置
# =============================================================================

# CORS中间件
if settings.CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )

# 受信任主机中间件
if not settings.DEBUG:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # 生产环境应该配置具体的主机名
    )

# 安全中间件
app.add_middleware(SecurityMiddleware)

# 日志中间件
app.add_middleware(LoggingMiddleware)

# =============================================================================
# 异常处理器
# =============================================================================

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "data": None,
            "timestamp": int(request.state.start_time * 1000) if hasattr(request.state, 'start_time') else None
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理器"""
    logger.error(f"请求验证失败: {exc.errors()}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "code": 422,
            "message": "请求参数验证失败",
            "data": {
                "errors": exc.errors(),
                "body": exc.body
            },
            "timestamp": int(request.state.start_time * 1000) if hasattr(request.state, 'start_time') else None
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__}: {str(exc)}", exc_info=True)
    
    # 生产环境不暴露详细错误信息
    if settings.DEBUG:
        error_detail = str(exc)
    else:
        error_detail = "服务器内部错误"
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": 500,
            "message": error_detail,
            "data": None,
            "timestamp": int(request.state.start_time * 1000) if hasattr(request.state, 'start_time') else None
        }
    )


# =============================================================================
# 基础路由
# =============================================================================

@app.get("/", tags=["基础"])
async def root() -> Dict[str, Any]:
    """根路径"""
    return {
        "code": 200,
        "message": "JQData量化数据平台API",
        "data": {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "docs_url": "/docs" if settings.DEBUG else None
        },
        "timestamp": None
    }


@app.get("/health", tags=["基础"])
async def health_check() -> Dict[str, Any]:
    """健康检查"""
    from app.core.database import check_database_health, check_redis_health
    
    try:
        # 检查数据库
        db_healthy = await check_database_health()
        
        # 检查Redis
        redis_healthy = await check_redis_health()
        
        # 整体健康状态
        healthy = db_healthy and redis_healthy
        
        return {
            "code": 200 if healthy else 503,
            "message": "服务健康" if healthy else "服务异常",
            "data": {
                "status": "healthy" if healthy else "unhealthy",
                "database": "ok" if db_healthy else "error",
                "redis": "ok" if redis_healthy else "error",
                "timestamp": None
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "code": 503,
            "message": "健康检查失败",
            "data": {
                "status": "error",
                "error": str(e)
            },
            "timestamp": None
        }


@app.get("/info", tags=["基础"])
async def app_info() -> Dict[str, Any]:
    """应用信息"""
    return {
        "code": 200,
        "message": "应用信息",
        "data": {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "environment": settings.ENVIRONMENT,
            "debug": settings.DEBUG,
            "features": {
                "social_trading": settings.FEATURE_SOCIAL_TRADING,
                "ml_predictions": settings.FEATURE_ML_PREDICTIONS,
                "options_pricing": settings.FEATURE_OPTIONS_PRICING,
                "news_analysis": settings.FEATURE_NEWS_ANALYSIS,
                "portfolio_optimization": settings.FEATURE_PORTFOLIO_OPTIMIZATION,
            }
        },
        "timestamp": None
    }


# =============================================================================
# API路由注册
# =============================================================================

# 注册API路由
from app.api.v1.auth import router as auth_router
from app.api.v1.jqdata import router as jqdata_router
from app.api.v1.market import router as market_router

app.include_router(auth_router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(jqdata_router, prefix="/api/v1/jqdata", tags=["JQData配置"])
app.include_router(market_router, prefix="/api/v1/market", tags=["市场数据"])

# =============================================================================
# 启动配置
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
    )
