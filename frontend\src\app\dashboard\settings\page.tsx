'use client';

import { Card, Row, Col, Typography, Button, Space, Divider } from 'antd';
import { 
  SettingOutlined,
  UserOutlined,
  ApiOutlined,
  BgColorsOutlined,
  RightOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

export default function SettingsPage() {
  const router = useRouter();

  const settingsModules = [
    {
      title: '个人资料',
      description: '管理您的个人信息和账户设置',
      icon: <UserOutlined className="text-3xl text-blue-500" />,
      path: '/dashboard/settings/profile',
      features: ['基本信息', '密码修改', '头像设置', '联系方式']
    },
    {
      title: 'JQData配置',
      description: '配置聚宽数据API密钥和相关设置',
      icon: <ApiOutlined className="text-3xl text-green-500" />,
      path: '/dashboard/settings/jqdata',
      features: ['API密钥', '数据权限', '使用配额', '连接测试']
    },
    {
      title: '系统偏好',
      description: '自定义界面主题和系统行为设置',
      icon: <BgColorsOutlined className="text-3xl text-purple-500" />,
      path: '/dashboard/settings/preferences',
      features: ['主题设置', '语言选择', '通知设置', '显示偏好']
    }
  ];

  const quickSettings = [
    {
      title: '数据源状态',
      status: '未配置',
      statusColor: 'orange',
      action: '配置JQData',
      path: '/dashboard/settings/jqdata'
    },
    {
      title: '账户类型',
      status: '企业版',
      statusColor: 'green',
      action: '查看详情',
      path: '/dashboard/settings/profile'
    },
    {
      title: '主题模式',
      status: '浅色模式',
      statusColor: 'blue',
      action: '切换主题',
      path: '/dashboard/settings/preferences'
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <SettingOutlined className="mr-3" />
          系统设置
        </Title>
        <Text type="secondary" className="text-lg">
          管理您的账户、API配置和系统偏好设置
        </Text>
      </div>

      {/* 快速状态 */}
      <Card title="快速状态" className="mb-6">
        <Row gutter={[16, 16]}>
          {quickSettings.map((setting, index) => (
            <Col xs={24} sm={8} key={index}>
              <div className="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                <Text type="secondary" className="block mb-2">{setting.title}</Text>
                <div className="mb-3">
                  <span className={`px-3 py-1 rounded-full text-sm bg-${setting.statusColor}-100 text-${setting.statusColor}-600`}>
                    {setting.status}
                  </span>
                </div>
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => router.push(setting.path)}
                >
                  {setting.action}
                </Button>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      <Divider />

      {/* 设置模块 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          设置模块
        </Title>
        <Row gutter={[24, 24]}>
          {settingsModules.map((module, index) => (
            <Col xs={24} lg={8} key={index}>
              <Card 
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => router.push(module.path)}
              >
                <div className="text-center">
                  <div className="mb-4">
                    {module.icon}
                  </div>
                  <Title level={4} className="!mb-2">
                    {module.title}
                  </Title>
                  <Text type="secondary" className="block mb-4">
                    {module.description}
                  </Text>
                  <div className="mb-4">
                    <Space wrap>
                      {module.features.map((feature, idx) => (
                        <span 
                          key={idx}
                          className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs"
                        >
                          {feature}
                        </span>
                      ))}
                    </Space>
                  </div>
                  <Button type="link" className="p-0">
                    进入设置 <RightOutlined />
                  </Button>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 安全提示 */}
      <Card className="bg-yellow-50 border-yellow-200">
        <div className="flex items-start space-x-3">
          <SafetyCertificateOutlined className="text-yellow-600 text-xl mt-1" />
          <div>
            <Title level={4} className="!mb-2 text-yellow-800">
              安全提示
            </Title>
            <Text className="text-yellow-700">
              请妥善保管您的API密钥和账户信息。建议定期更新密码，并启用双因素认证以确保账户安全。
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
}
