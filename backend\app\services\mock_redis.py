"""
内存模拟Redis服务

在开发环境中提供Redis功能的内存模拟实现，无需安装真实Redis服务
"""

import asyncio
import json
import pickle
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from app.core.logging import logger


class MockRedis:
    """内存模拟Redis客户端"""
    
    def __init__(self):
        self._data: Dict[str, Any] = {}
        self._expires: Dict[str, float] = {}
        self._connected = False
        logger.info("MockRedis初始化完成 - 使用内存模拟Redis功能")
    
    async def connect(self):
        """模拟连接"""
        self._connected = True
        logger.info("MockRedis连接成功")
    
    async def disconnect(self):
        """模拟断开连接"""
        self._connected = False
        self._data.clear()
        self._expires.clear()
        logger.info("MockRedis连接已断开")
    
    def _is_expired(self, key: str) -> bool:
        """检查键是否过期"""
        if key not in self._expires:
            return False
        
        if time.time() > self._expires[key]:
            # 删除过期的键
            self._data.pop(key, None)
            self._expires.pop(key, None)
            return True
        
        return False
    
    async def ping(self) -> bool:
        """健康检查"""
        return self._connected
    
    async def get(self, key: str) -> Optional[bytes]:
        """获取值"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        if self._is_expired(key):
            return None
        
        value = self._data.get(key)
        if value is None:
            return None
        
        # 返回bytes格式，模拟真实Redis
        if isinstance(value, str):
            return value.encode('utf-8')
        elif isinstance(value, bytes):
            return value
        else:
            return str(value).encode('utf-8')
    
    async def set(
        self, 
        key: str, 
        value: Union[str, bytes], 
        ex: Optional[int] = None,
        px: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """设置值"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        # 检查nx和xx条件
        exists = key in self._data and not self._is_expired(key)
        
        if nx and exists:  # nx: 只在键不存在时设置
            return False
        
        if xx and not exists:  # xx: 只在键存在时设置
            return False
        
        # 设置值
        if isinstance(value, bytes):
            self._data[key] = value.decode('utf-8')
        else:
            self._data[key] = str(value)
        
        # 设置过期时间
        if ex is not None:  # 秒
            self._expires[key] = time.time() + ex
        elif px is not None:  # 毫秒
            self._expires[key] = time.time() + (px / 1000)
        else:
            # 移除过期时间
            self._expires.pop(key, None)
        
        return True
    
    async def delete(self, *keys: str) -> int:
        """删除键"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        deleted_count = 0
        for key in keys:
            if key in self._data:
                self._data.pop(key, None)
                self._expires.pop(key, None)
                deleted_count += 1
        
        return deleted_count
    
    async def exists(self, *keys: str) -> int:
        """检查键是否存在"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        count = 0
        for key in keys:
            if key in self._data and not self._is_expired(key):
                count += 1
        
        return count
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置过期时间"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        if key not in self._data or self._is_expired(key):
            return False
        
        self._expires[key] = time.time() + seconds
        return True
    
    async def ttl(self, key: str) -> int:
        """获取剩余生存时间"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        if key not in self._data:
            return -2  # 键不存在
        
        if self._is_expired(key):
            return -2  # 键不存在
        
        if key not in self._expires:
            return -1  # 键存在但没有设置过期时间
        
        remaining = self._expires[key] - time.time()
        return max(0, int(remaining))
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        # 清理过期键
        expired_keys = []
        for key in list(self._data.keys()):
            if self._is_expired(key):
                expired_keys.append(key)
        
        # 简单的模式匹配（只支持*通配符）
        if pattern == "*":
            return list(self._data.keys())
        
        # 简单的前缀匹配
        if pattern.endswith("*"):
            prefix = pattern[:-1]
            return [key for key in self._data.keys() if key.startswith(prefix)]
        
        # 精确匹配
        return [key for key in self._data.keys() if key == pattern]
    
    async def flushdb(self) -> bool:
        """清空当前数据库"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        self._data.clear()
        self._expires.clear()
        logger.info("MockRedis数据库已清空")
        return True
    
    async def info(self, section: Optional[str] = None) -> str:
        """获取服务器信息"""
        if not self._connected:
            raise ConnectionError("MockRedis未连接")
        
        info_data = {
            "redis_version": "mock-1.0.0",
            "connected_clients": "1",
            "used_memory": str(len(str(self._data))),
            "total_keys": str(len(self._data)),
            "expired_keys": str(len(self._expires)),
        }
        
        return "\r\n".join([f"{k}:{v}" for k, v in info_data.items()])
    
    # 支持上下文管理器
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()


class MockRedisPool:
    """模拟Redis连接池"""
    
    def __init__(self, **kwargs):
        self._redis = MockRedis()
        logger.info("MockRedis连接池初始化完成")
    
    async def get_connection(self) -> MockRedis:
        """获取连接"""
        if not self._redis._connected:
            await self._redis.connect()
        return self._redis
    
    async def release(self, connection: MockRedis):
        """释放连接（模拟）"""
        pass
    
    async def disconnect(self):
        """断开所有连接"""
        await self._redis.disconnect()


# 全局模拟Redis实例
mock_redis_instance = None


async def get_mock_redis() -> MockRedis:
    """获取全局模拟Redis实例"""
    global mock_redis_instance
    
    if mock_redis_instance is None:
        mock_redis_instance = MockRedis()
        await mock_redis_instance.connect()
    
    return mock_redis_instance


async def close_mock_redis():
    """关闭全局模拟Redis实例"""
    global mock_redis_instance
    
    if mock_redis_instance is not None:
        await mock_redis_instance.disconnect()
        mock_redis_instance = None
