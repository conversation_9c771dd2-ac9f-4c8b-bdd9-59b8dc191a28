"""
市场数据模型

包含股票基本信息、价格数据、成交数据等
"""

from datetime import date, datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import (
    BigInteger, Boolean, Date, DateTime, ForeignKey, Index, Integer, 
    Numeric, String, Text, UniqueConstraint, func
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class Stock(BaseModel):
    """股票基本信息模型"""
    
    __tablename__ = "stocks"
    
    # 基本信息
    symbol: Mapped[str] = mapped_column(
        String(20),
        unique=True,
        index=True,
        nullable=False,
        comment="股票代码 (如: 000001.XSHE)"
    )
    
    name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="股票名称"
    )
    
    display_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="显示名称"
    )
    
    # 市场信息
    market: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        index=True,
        comment="市场代码: XSHE(深交所), XSHG(上交所), XHKG(港交所), XNAS(纳斯达克)"
    )
    
    exchange: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="交易所名称"
    )
    
    currency: Mapped[str] = mapped_column(
        String(10),
        default="CNY",
        nullable=False,
        comment="交易货币"
    )
    
    # 分类信息
    industry: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        index=True,
        comment="所属行业"
    )
    
    sector: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="所属板块"
    )
    
    concept: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="概念标签(JSON格式)"
    )
    
    # 上市信息
    list_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        comment="上市日期"
    )
    
    delist_date: Mapped[Optional[date]] = mapped_column(
        Date,
        nullable=True,
        comment="退市日期"
    )
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="是否活跃交易"
    )
    
    is_st: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否ST股票"
    )
    
    # 基本面数据
    total_share: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="总股本(万股)"
    )
    
    float_share: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="流通股本(万股)"
    )
    
    market_cap: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(20, 2),
        nullable=True,
        comment="总市值(万元)"
    )
    
    # 数据更新信息
    last_update: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后更新时间"
    )
    
    data_source: Mapped[str] = mapped_column(
        String(50),
        default="jqdata",
        nullable=False,
        comment="数据来源"
    )
    
    # 关系
    daily_prices = relationship("DailyPrice", back_populates="stock", cascade="all, delete-orphan")
    minute_prices = relationship("MinutePrice", back_populates="stock", cascade="all, delete-orphan")
    tick_data = relationship("TickData", back_populates="stock", cascade="all, delete-orphan")


class DailyPrice(BaseModel):
    """日线价格数据模型"""
    
    __tablename__ = "daily_prices"
    
    stock_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("stocks.id", ondelete="CASCADE"),
        nullable=False,
        comment="股票ID"
    )
    
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    trade_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        index=True,
        comment="交易日期"
    )
    
    # OHLCV数据
    open_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="开盘价"
    )
    
    high_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="最高价"
    )
    
    low_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="最低价"
    )
    
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="收盘价"
    )
    
    volume: Mapped[Optional[BigInteger]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="成交量(股)"
    )
    
    turnover: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(15, 2),
        nullable=True,
        comment="成交额(元)"
    )
    
    # 复权价格
    adj_close: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="后复权收盘价"
    )
    
    # 技术指标(预计算)
    change_pct: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(8, 4),
        nullable=True,
        comment="涨跌幅(%)"
    )
    
    turnover_rate: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(8, 4),
        nullable=True,
        comment="换手率(%)"
    )
    
    # 数据质量标记
    is_trading_day: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否交易日"
    )
    
    is_suspended: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否停牌"
    )
    
    # 关系
    stock = relationship("Stock", back_populates="daily_prices")
    
    # 索引
    __table_args__ = (
        UniqueConstraint('symbol', 'trade_date', name='uk_daily_prices_symbol_date'),
        Index('idx_daily_prices_date', 'trade_date'),
        Index('idx_daily_prices_symbol', 'symbol'),
        Index('idx_daily_prices_symbol_date', 'symbol', 'trade_date'),
    )


class MinutePrice(BaseModel):
    """分钟级价格数据模型"""
    
    __tablename__ = "minute_prices"
    
    stock_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("stocks.id", ondelete="CASCADE"),
        nullable=False,
        comment="股票ID"
    )
    
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    datetime: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        comment="时间戳"
    )
    
    # OHLCV数据
    open_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="开盘价"
    )
    
    high_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="最高价"
    )
    
    low_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="最低价"
    )
    
    close_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="收盘价"
    )
    
    volume: Mapped[Optional[BigInteger]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="成交量(股)"
    )
    
    turnover: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(15, 2),
        nullable=True,
        comment="成交额(元)"
    )
    
    # 关系
    stock = relationship("Stock", back_populates="minute_prices")
    
    # 索引和分区
    __table_args__ = (
        UniqueConstraint('symbol', 'datetime', name='uk_minute_prices_symbol_datetime'),
        Index('idx_minute_prices_datetime', 'datetime'),
        Index('idx_minute_prices_symbol', 'symbol'),
        Index('idx_minute_prices_symbol_datetime', 'symbol', 'datetime'),
    )


class TickData(BaseModel):
    """Tick级数据模型"""
    
    __tablename__ = "tick_data"
    
    stock_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("stocks.id", ondelete="CASCADE"),
        nullable=False,
        comment="股票ID"
    )
    
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        index=True,
        comment="股票代码"
    )
    
    datetime: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        comment="时间戳"
    )
    
    # Tick数据
    current_price: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(10, 3),
        nullable=True,
        comment="当前价"
    )
    
    volume: Mapped[Optional[BigInteger]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="成交量"
    )
    
    turnover: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(15, 2),
        nullable=True,
        comment="成交额"
    )
    
    # 买卖盘数据
    bid_price_1: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="买一价")
    bid_volume_1: Mapped[Optional[BigInteger]] = mapped_column(BigInteger, comment="买一量")
    ask_price_1: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3), comment="卖一价")
    ask_volume_1: Mapped[Optional[BigInteger]] = mapped_column(BigInteger, comment="卖一量")
    
    # 关系
    stock = relationship("Stock", back_populates="tick_data")
    
    # 索引
    __table_args__ = (
        Index('idx_tick_data_datetime', 'datetime'),
        Index('idx_tick_data_symbol', 'symbol'),
        Index('idx_tick_data_symbol_datetime', 'symbol', 'datetime'),
    )
