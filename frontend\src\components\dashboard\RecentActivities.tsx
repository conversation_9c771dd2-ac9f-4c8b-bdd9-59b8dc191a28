'use client';

/**
 * 最近活动组件
 * 
 * 显示用户最近的操作活动、系统通知等
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Avatar,
  Typography,
  Tag,
  Space,
  Button,
  Empty,
  Spin,
  Tooltip,
} from 'antd';
import {
  BellOutlined,
  TradingOutlined,
  SettingOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

const { Text } = Typography;

interface Activity {
  id: string;
  type: 'trade' | 'system' | 'config' | 'analysis' | 'notification';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
  icon?: React.ReactNode;
  data?: any;
}

export const RecentActivities: React.FC = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);

  // 模拟活动数据
  useEffect(() => {
    const loadActivities = async () => {
      setLoading(true);
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockActivities: Activity[] = [
        {
          id: '1',
          type: 'trade',
          title: '买入订单执行成功',
          description: '买入 平安银行(000001) 1000股，成交价格 ¥12.45',
          timestamp: dayjs().subtract(10, 'minute').toISOString(),
          status: 'success',
          icon: <TradingOutlined />,
          data: { symbol: '000001.XSHE', quantity: 1000, price: 12.45 },
        },
        {
          id: '2',
          type: 'system',
          title: 'JQData配额提醒',
          description: '今日API配额已使用80%，请注意合理使用',
          timestamp: dayjs().subtract(1, 'hour').toISOString(),
          status: 'warning',
          icon: <ExclamationCircleOutlined />,
        },
        {
          id: '3',
          type: 'analysis',
          title: '策略回测完成',
          description: '均线策略回测完成，年化收益率15.6%',
          timestamp: dayjs().subtract(2, 'hour').toISOString(),
          status: 'success',
          icon: <LineChartOutlined />,
          data: { strategyName: '均线策略', annualReturn: 15.6 },
        },
        {
          id: '4',
          type: 'config',
          title: 'JQData配置更新',
          description: '成功更新JQData账号配置',
          timestamp: dayjs().subtract(3, 'hour').toISOString(),
          status: 'info',
          icon: <SettingOutlined />,
        },
        {
          id: '5',
          type: 'system',
          title: '数据同步失败',
          description: '股票列表同步失败，请检查网络连接',
          timestamp: dayjs().subtract(4, 'hour').toISOString(),
          status: 'error',
          icon: <CloseCircleOutlined />,
        },
        {
          id: '6',
          type: 'notification',
          title: '市场提醒',
          description: '沪深300指数上涨2.1%，创近期新高',
          timestamp: dayjs().subtract(5, 'hour').toISOString(),
          status: 'info',
          icon: <BellOutlined />,
        },
        {
          id: '7',
          type: 'trade',
          title: '卖出订单执行成功',
          description: '卖出 万科A(000002) 500股，成交价格 ¥18.76',
          timestamp: dayjs().subtract(1, 'day').toISOString(),
          status: 'success',
          icon: <TradingOutlined />,
          data: { symbol: '000002.XSHE', quantity: -500, price: 18.76 },
        },
      ];
      
      setActivities(mockActivities);
      setLoading(false);
    };

    loadActivities();
  }, []);

  // 获取状态颜色
  const getStatusColor = (status: Activity['status']) => {
    switch (status) {
      case 'success':
        return '#52c41a';
      case 'warning':
        return '#faad14';
      case 'error':
        return '#ff4d4f';
      case 'info':
      default:
        return '#1890ff';
    }
  };

  // 获取状态标签
  const getStatusTag = (status: Activity['status']) => {
    switch (status) {
      case 'success':
        return <Tag color="success">成功</Tag>;
      case 'warning':
        return <Tag color="warning">警告</Tag>;
      case 'error':
        return <Tag color="error">错误</Tag>;
      case 'info':
      default:
        return <Tag color="processing">信息</Tag>;
    }
  };

  // 获取类型图标
  const getTypeIcon = (activity: Activity) => {
    if (activity.icon) {
      return activity.icon;
    }
    
    switch (activity.type) {
      case 'trade':
        return <TradingOutlined />;
      case 'system':
        return <SettingOutlined />;
      case 'config':
        return <SettingOutlined />;
      case 'analysis':
        return <LineChartOutlined />;
      case 'notification':
        return <BellOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  if (loading) {
    return (
      <Card title="最近活动" className="h-full">
        <div className="flex items-center justify-center h-64">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Card
      title="最近活动"
      extra={
        <Button type="link" size="small">
          查看全部
        </Button>
      }
      className="h-full"
      bodyStyle={{ padding: 0 }}
    >
      {activities.length === 0 ? (
        <div className="p-6">
          <Empty description="暂无活动记录" />
        </div>
      ) : (
        <List
          dataSource={activities}
          renderItem={(activity, index) => (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <List.Item
                className="px-6 py-4 hover:bg-gray-50 transition-colors cursor-pointer"
                actions={[
                  <Tooltip title={dayjs(activity.timestamp).format('YYYY-MM-DD HH:mm:ss')}>
                    <Text type="secondary" className="text-xs">
                      {dayjs(activity.timestamp).fromNow()}
                    </Text>
                  </Tooltip>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      icon={getTypeIcon(activity)}
                      style={{
                        backgroundColor: `${getStatusColor(activity.status)}15`,
                        color: getStatusColor(activity.status),
                      }}
                    />
                  }
                  title={
                    <div className="flex items-center justify-between">
                      <Text strong className="text-sm">
                        {activity.title}
                      </Text>
                      {getStatusTag(activity.status)}
                    </div>
                  }
                  description={
                    <Text type="secondary" className="text-xs">
                      {activity.description}
                    </Text>
                  }
                />
              </List.Item>
            </motion.div>
          )}
        />
      )}
    </Card>
  );
};
