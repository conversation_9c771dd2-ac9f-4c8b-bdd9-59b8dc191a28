# ============================================================================
# JQData量化数据平台 - 开发依赖
# ============================================================================

# 基础依赖
-r requirements.txt

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
factory-boy==3.3.0

# 代码质量工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
pre-commit==3.6.0
autoflake==2.2.1

# 性能分析工具
py-spy==0.3.14
memory-profiler==0.61.0

# 开发工具
ipython==8.17.2
jupyter==1.0.0

# ============================================================================
# 安装说明
# ============================================================================
# 
# 开发环境安装:
#   pip install -r requirements-dev.txt
#
# 这将自动安装基础依赖和开发工具
# ============================================================================
