{"version": 3, "file": "autoWrap.js", "sourceRoot": "", "sources": ["../../../../src/ui/axis/overlap/autoWrap.ts"], "names": [], "mappings": ";;AAoBA,6BAwCC;;AA5DD,sCAAiD;AAEjD,uCAAkD;AAElD,sCAA0C;AAQ1C,SAAS,iBAAiB,CAAC,IAAoB;IACrC,IAAA,IAAI,GAAqB,IAAI,KAAzB,EAAE,cAAc,GAAK,IAAI,eAAT,CAAU;IACtC,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAA,uBAAgB,EAAC,IAAsC,CAAC,EAAE,CAAC;QAClF,OAAO,cAAc,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1D,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAwB,UAAU,CAChC,MAAc,EACd,UAA0B,EAC1B,IAAoB,EACpB,KAAY,EACZ,IAAmB;;IAEX,IAAA,KAAkE,UAAU,SAAhE,EAAZ,QAAQ,mBAAG,CAAC,KAAA,EAAE,KAAoD,UAAU,kBAAtC,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,EAAE,KAA0B,UAAU,OAAf,EAArB,MAAM,mBAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAA,CAAgB;IACrF,IAAM,aAAa,GAAG,IAAA,uBAAgB,EAAC,MAAA,UAAU,CAAC,aAAa,mCAAI,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/E,IAAM,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAA3B,CAA2B,CAAC,CAAC;IAExE,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,OAAR,IAAI,2CAAQ,YAAY,UAAC,CAAC;IAE3C,IAAM,YAAY,GAAG,cAAM,OAAA,IAAA,gBAAS,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAA1C,CAA0C,CAAC;IAEtE,IAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7C,IAAM,aAAa,GAAG,UAAC,KAA4B;QACjD,OAAA,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YAC1B,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7D,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC3D,CAAC,CAAC;IAHF,CAGE,CAAC;IAEL,IAAI,QAAQ,GAAG,QAAQ;QAAE,OAAO;IAEhC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAA,uBAAgB,EAAC,IAAsC,CAAC,EAAE,CAAC;QACvF,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxB,IAAI,YAAY,EAAE,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,IAAI,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC;YACtD,aAAa,CAAC,KAAK,CAAC,CAAC;YACrB,IAAI,YAAY,EAAE;gBAAE,OAAO;QAC7B,CAAC;IACH,CAAC;IAED,IAAI,iBAAiB,EAAE,CAAC;QACtB,aAAa,CAAC,YAAY,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC"}