"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapIt = wrapIt;
var text_1 = require("./text");
function wrapIt(node, wordWrapWidth, maxLines, textBaseline) {
    if (maxLines === void 0) { maxLines = 2; }
    if (textBaseline === void 0) { textBaseline = 'top'; }
    (0, text_1.applyToText)(node, { wordWrap: true, wordWrapWidth: wordWrapWidth, maxLines: maxLines, textBaseline: textBaseline });
}
//# sourceMappingURL=wrap.js.map