var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
import React, { forwardRef } from 'react';
import { BaseChart } from '../components/base';
import useConfig from '../hooks/useConfig';
import scale from './scale';
import { flow } from '../util';
export function makeChartComp(chartType) {
    var configKey = chartType.charAt(0).toLowerCase() + chartType.slice(1);
    return forwardRef(function (props, ref) {
        var config = useConfig();
        var flowProps = flow([scale])(props);
        if (!config || !config[configKey]) {
            return React.createElement(Base<PERSON>hart, __assign({}, flowProps, { chartType: chartType, ref: ref }));
        }
        return React.createElement(BaseChart, __assign({}, config.common, config[configKey], flowProps, { chartType: chartType, ref: ref }));
    });
}
