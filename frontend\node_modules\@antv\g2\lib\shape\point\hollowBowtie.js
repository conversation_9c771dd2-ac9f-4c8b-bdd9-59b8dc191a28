"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowBowtie = void 0;
const color_1 = require("./color");
/**
 * ▷◁
 */
const HollowBowtie = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'bowtie' }, options), context);
};
exports.HollowBowtie = HollowBowtie;
exports.HollowBowtie.props = Object.assign({ defaultMarker: 'hollowBowtie' }, color_1.Color.props);
//# sourceMappingURL=hollowBowtie.js.map