"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useAuthActions: function() { return /* binding */ useAuthActions; },\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * 认证状态管理\n * \n * 使用Zustand管理用户认证状态，包括登录、登出、token管理等\n */ \n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // 初始状态\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        loadingState: \"idle\",\n        // 登录\n        login: async (loginData)=>{\n            try {\n                console.log(\"\\uD83D\\uDD10 Auth Store: 开始登录流程...\", loginData);\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                // 使用表单数据格式，因为后端使用 OAuth2PasswordRequestForm\n                const formData = new FormData();\n                formData.append(\"username\", loginData.email);\n                formData.append(\"password\", loginData.password);\n                console.log(\"\\uD83D\\uDCE1 Auth Store: 发送API请求...\");\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(\"/api/v1/auth/login\", formData, {\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\"\n                    }\n                });\n                console.log(\"\\uD83D\\uDCE5 Auth Store: 收到响应:\", response.data);\n                // 处理axios直接响应\n                const responseData = response.data;\n                if (responseData.code === 200 && responseData.data) {\n                    const { access_token, refresh_token, token_type, expires_in, user_info } = responseData.data;\n                    console.log(\"✅ Auth Store: 解析用户数据成功:\", user_info);\n                    const tokens = {\n                        accessToken: access_token,\n                        refreshToken: refresh_token || access_token,\n                        tokenType: token_type,\n                        expiresIn: expires_in\n                    };\n                    // 设置认证信息\n                    console.log(\"\\uD83D\\uDD11 Auth Store: 设置认证令牌...\");\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n                    console.log(\"\\uD83D\\uDCBE Auth Store: 更新状态...\");\n                    set({\n                        user: user_info,\n                        tokens,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    // 设置cookie以便中间件检查认证状态\n                    console.log(\"\\uD83C\\uDF6A Auth Store: 设置认证cookie...\");\n                    if (typeof document !== \"undefined\") {\n                        document.cookie = \"auth-storage=\".concat(JSON.stringify({\n                            state: {\n                                isAuthenticated: true,\n                                tokens,\n                                user: user_info\n                            }\n                        }), \"; path=/; max-age=86400\"); // 24小时过期\n                    }\n                    console.log(\"\\uD83C\\uDF89 Auth Store: 登录流程完成！\");\n                } else {\n                    console.error(\"❌ Auth Store: 响应格式错误:\", responseData);\n                    throw new Error(responseData.message || \"登录失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\",\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"登录失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 注册\n        register: async (registerData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/register\", registerData);\n                if (response.code === 200) {\n                    set({\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"注册成功！请登录您的账号\");\n                } else {\n                    throw new Error(response.message || \"注册失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"注册失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 登出\n        logout: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                // 调用登出API\n                await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/logout\");\n            } catch (error) {\n                console.error(\"登出API调用失败:\", error);\n            } finally{\n                // 无论API调用是否成功，都清理本地状态\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n                set({\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    loadingState: \"idle\"\n                });\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已安全登出\");\n            }\n        },\n        // 刷新Token\n        refreshToken: async ()=>{\n            try {\n                const { tokens } = get();\n                if (!(tokens === null || tokens === void 0 ? void 0 : tokens.refreshToken)) {\n                    throw new Error(\"没有刷新Token\");\n                }\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/refresh\", {\n                    refresh_token: tokens.refreshToken\n                });\n                if (response.code === 200 && response.data) {\n                    const newTokens = {\n                        accessToken: response.data.access_token,\n                        refreshToken: tokens.refreshToken,\n                        tokenType: response.data.token_type,\n                        expiresIn: response.data.expires_in\n                    };\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(newTokens);\n                    set({\n                        tokens: newTokens\n                    });\n                } else {\n                    throw new Error(\"Token刷新失败\");\n                }\n            } catch (error) {\n                console.error(\"Token刷新失败:\", error);\n                // Token刷新失败，清理认证状态\n                get().clearAuth();\n                throw error;\n            }\n        },\n        // 获取当前用户信息\n        getCurrentUser: async ()=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/auth/me\");\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                } else {\n                    throw new Error(\"获取用户信息失败\");\n                }\n            } catch (error) {\n                var _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                // 如果是401错误，清理认证状态\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                    get().clearAuth();\n                }\n                throw error;\n            }\n        },\n        // 更新用户资料\n        updateProfile: async (profileData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/api/v1/auth/profile\", profileData);\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"资料更新成功\");\n                } else {\n                    throw new Error(response.message || \"更新失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"更新失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 设置用户\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        // 设置Token\n        setTokens: (tokens)=>{\n            set({\n                tokens\n            });\n            if (tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n            } else {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            }\n        },\n        // 设置加载状态\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        // 设置加载状态\n        setLoadingState: (loadingState)=>{\n            set({\n                loadingState\n            });\n        },\n        // 清理认证状态\n        clearAuth: ()=>{\n            _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                isLoading: false,\n                loadingState: \"idle\"\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            // 恢复状态后，设置API客户端的认证信息\n            if (state === null || state === void 0 ? void 0 : state.tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(state.tokens);\n            }\n        }\n}));\n// 导出选择器函数\nconst useAuth = ()=>{\n    const { user, isAuthenticated, isLoading } = useAuthStore();\n    return {\n        user,\n        isAuthenticated,\n        isLoading\n    };\n};\nconst useAuthActions = ()=>{\n    const { login, register, logout, refreshToken, getCurrentUser, updateProfile } = useAuthStore();\n    return {\n        login,\n        register,\n        logout,\n        refreshToken,\n        getCurrentUser,\n        updateProfile\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});