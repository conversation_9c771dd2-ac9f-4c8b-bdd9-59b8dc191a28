'use client';

/**
 * 股票详情页面
 * 
 * 显示单只股票的详细信息，包括实时价格、K线图、财务数据等
 */

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Button,
  Tag,
  Tabs,
  Table,
  Progress,
  Spin,
  message,
  Tooltip,
  Alert
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  StarOutlined,
  StarFilled,
  ShareAltOutlined,
  LineChartOutlined,
  BarChartOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { CandlestickChart } from '@/components/charts/CandlestickChart';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface StockDetail {
  symbol: string;
  name: string;
  currentPrice: number;
  change: number;
  changePercent: number;
  open: number;
  high: number;
  low: number;
  volume: number;
  turnover: number;
  marketCap: number;
  pe: number;
  pb: number;
  eps: number;
  roe: number;
  industry: string;
  concept: string[];
}

export default function StockDetailPage() {
  const params = useParams();
  const router = useRouter();
  const symbol = params.symbol as string;
  
  const [loading, setLoading] = useState(true);
  const [stockDetail, setStockDetail] = useState<StockDetail | null>(null);
  const [isWatched, setIsWatched] = useState(false);
  const [activeTab, setActiveTab] = useState('chart');

  // 模拟股票详情数据
  const mockStockDetail: StockDetail = {
    symbol: symbol || '000001.XSHE',
    name: '平安银行',
    currentPrice: 12.45,
    change: 0.23,
    changePercent: 1.88,
    open: 12.20,
    high: 12.58,
    low: 12.15,
    volume: 45678900,
    turnover: 568900000,
    marketCap: 241500000000,
    pe: 5.2,
    pb: 0.68,
    eps: 2.39,
    roe: 13.2,
    industry: '银行',
    concept: ['金融科技', '数字货币', '区块链', '移动支付']
  };

  // 财务数据
  const financialData = [
    { key: '1', indicator: '总资产', value: '4.87万亿', unit: '元', change: '+8.5%' },
    { key: '2', indicator: '净资产', value: '3542亿', unit: '元', change: '+12.3%' },
    { key: '3', indicator: '营业收入', value: '1658亿', unit: '元', change: '+15.2%' },
    { key: '4', indicator: '净利润', value: '374亿', unit: '元', change: '+18.7%' },
    { key: '5', indicator: '资产负债率', value: '92.73', unit: '%', change: '-0.5%' },
    { key: '6', indicator: '不良贷款率', value: '1.02', unit: '%', change: '-0.12%' }
  ];

  // 股东信息
  const shareholderData = [
    { key: '1', name: '中国平安保险(集团)股份有限公司', shares: '59.33%', type: '控股股东' },
    { key: '2', name: '香港中央结算有限公司', shares: '8.92%', type: '境外法人' },
    { key: '3', name: '全国社保基金一零三组合', shares: '1.45%', type: '基金' },
    { key: '4', name: '中央汇金资产管理有限责任公司', shares: '1.23%', type: '国有法人' },
    { key: '5', name: '全国社保基金四一三组合', shares: '0.98%', type: '基金' }
  ];

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setStockDetail(mockStockDetail);
      setLoading(false);
    }, 1000);
  }, [symbol]);

  const handleAddToWatchlist = () => {
    setIsWatched(!isWatched);
    message.success(isWatched ? '已从自选股移除' : '已添加到自选股');
  };

  const formatNumber = (num: number, unit: string = '') => {
    if (num >= 100000000) {
      return `${(num / 100000000).toFixed(2)}亿${unit}`;
    } else if (num >= 10000) {
      return `${(num / 10000).toFixed(2)}万${unit}`;
    }
    return `${num.toFixed(2)}${unit}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" tip="正在加载股票详情..." />
      </div>
    );
  }

  if (!stockDetail) {
    return (
      <Alert
        message="股票不存在"
        description="未找到该股票的详细信息"
        type="error"
        showIcon
      />
    );
  }

  const isPositive = stockDetail.change >= 0;

  return (
    <div className="space-y-6">
      {/* 股票基本信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card>
          <Row gutter={[24, 16]} align="middle">
            <Col xs={24} md={12}>
              <Space direction="vertical" size="small">
                <div className="flex items-center space-x-3">
                  <Title level={2} className="!mb-0">
                    {stockDetail.name}
                  </Title>
                  <Text type="secondary" className="text-lg">
                    {stockDetail.symbol}
                  </Text>
                  <Tag color="blue">{stockDetail.industry}</Tag>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Text className="text-3xl font-bold" style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
                    ¥{stockDetail.currentPrice.toFixed(2)}
                  </Text>
                  <Space>
                    <Text style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
                      {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                      {Math.abs(stockDetail.change).toFixed(2)}
                    </Text>
                    <Text style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
                      ({isPositive ? '+' : ''}{stockDetail.changePercent.toFixed(2)}%)
                    </Text>
                  </Space>
                </div>

                <Space wrap>
                  {stockDetail.concept.map(concept => (
                    <Tag key={concept} color="processing">{concept}</Tag>
                  ))}
                </Space>
              </Space>
            </Col>

            <Col xs={24} md={12}>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Statistic
                    title="今开"
                    value={stockDetail.open}
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="最高"
                    value={stockDetail.high}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#ff4d4f' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="最低"
                    value={stockDetail.low}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="成交量"
                    value={formatNumber(stockDetail.volume, '股')}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="成交额"
                    value={formatNumber(stockDetail.turnover, '元')}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="市值"
                    value={formatNumber(stockDetail.marketCap, '元')}
                  />
                </Col>
              </Row>
            </Col>
          </Row>

          <div className="mt-4 flex justify-between items-center">
            <Space>
              <Button
                type={isWatched ? "default" : "primary"}
                icon={isWatched ? <StarFilled /> : <StarOutlined />}
                onClick={handleAddToWatchlist}
              >
                {isWatched ? '已关注' : '加自选'}
              </Button>
              <Button icon={<ShareAltOutlined />}>分享</Button>
              <Button icon={<LineChartOutlined />} onClick={() => router.push(`/dashboard/market/charts?symbol=${symbol}`)}>
                图表分析
              </Button>
            </Space>

            <Space>
              <Text type="secondary">市盈率: {stockDetail.pe}</Text>
              <Text type="secondary">市净率: {stockDetail.pb}</Text>
              <Text type="secondary">ROE: {stockDetail.roe}%</Text>
            </Space>
          </div>
        </Card>
      </motion.div>

      {/* 详细信息标签页 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="K线图" key="chart">
              <CandlestickChart
                symbol={stockDetail.symbol}
                height={500}
              />
            </TabPane>

            <TabPane tab="财务数据" key="financial">
              <Table
                dataSource={financialData}
                pagination={false}
                size="middle"
                columns={[
                  {
                    title: '财务指标',
                    dataIndex: 'indicator',
                    key: 'indicator',
                    width: 150
                  },
                  {
                    title: '数值',
                    dataIndex: 'value',
                    key: 'value',
                    render: (value, record) => (
                      <Text strong>{value}</Text>
                    )
                  },
                  {
                    title: '同比变化',
                    dataIndex: 'change',
                    key: 'change',
                    render: (change) => {
                      const isPositive = change.startsWith('+');
                      return (
                        <Text style={{ color: isPositive ? '#52c41a' : '#ff4d4f' }}>
                          {change}
                        </Text>
                      );
                    }
                  }
                ]}
              />
            </TabPane>

            <TabPane tab="股东信息" key="shareholders">
              <Table
                dataSource={shareholderData}
                pagination={false}
                size="middle"
                columns={[
                  {
                    title: '股东名称',
                    dataIndex: 'name',
                    key: 'name'
                  },
                  {
                    title: '持股比例',
                    dataIndex: 'shares',
                    key: 'shares',
                    render: (shares) => <Text strong>{shares}</Text>
                  },
                  {
                    title: '股东类型',
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => <Tag>{type}</Tag>
                  }
                ]}
              />
            </TabPane>
          </Tabs>
        </Card>
      </motion.div>
    </div>
  );
}
