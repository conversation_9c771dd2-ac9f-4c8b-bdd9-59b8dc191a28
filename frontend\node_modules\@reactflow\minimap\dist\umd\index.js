!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlowMinimap={},e.<PERSON>act)}(this,(function(e,t){"use strict";function n(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let o,r=0;r<e.length;r++)""!==(o=n(e[r]))&&(t+=(t&&" ")+o);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}function o(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}var r={value:()=>{}};function i(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new a(o)}function a(e){this._=e}function s(e,t){return e.trim().split(/^|\s+/).map((function(e){var n="",o=e.indexOf(".");if(o>=0&&(n=e.slice(o+1),e=e.slice(0,o)),e&&!t.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:n}}))}function l(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function c(e,t,n){for(var o=0,i=e.length;o<i;++o)if(e[o].name===t){e[o]=r,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}a.prototype=i.prototype={constructor:a,on:function(e,t){var n,o=this._,r=s(e+"",o),i=-1,a=r.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++i<a;)if(n=(e=r[i]).type)o[n]=c(o[n],e.name,t);else if(null==t)for(n in o)o[n]=c(o[n],e.name,null);return this}for(;++i<a;)if((n=(e=r[i]).type)&&(n=l(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new a(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var u="http://www.w3.org/1999/xhtml",d={svg:"http://www.w3.org/2000/svg",xhtml:u,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function h(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),d.hasOwnProperty(t)?{space:d[t],local:e}:e}function f(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===u&&t.documentElement.namespaceURI===u?t.createElement(e):t.createElementNS(n,e)}}function g(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function p(e){var t=h(e);return(t.local?g:f)(t)}function m(){}function y(e){return null==e?m:function(){return this.querySelector(e)}}function v(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}function b(){return[]}function w(e){return null==e?b:function(){return this.querySelectorAll(e)}}function x(e){return function(){return this.matches(e)}}function S(e){return function(t){return t.matches(e)}}var E=Array.prototype.find;function _(){return this.firstElementChild}var C=Array.prototype.filter;function M(){return Array.from(this.children)}function N(e){return new Array(e.length)}function k(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function P(e){return function(){return e}}function A(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new k(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function O(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new k(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function I(e){return e.__data__}function D(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function R(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function z(e){return function(){this.removeAttribute(e)}}function $(e){return function(){this.removeAttributeNS(e.space,e.local)}}function T(e,t){return function(){this.setAttribute(e,t)}}function B(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function L(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function Y(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function X(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function H(e){return function(){this.style.removeProperty(e)}}function V(e,t,n){return function(){this.style.setProperty(e,t,n)}}function K(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function F(e,t){return e.style.getPropertyValue(t)||X(e).getComputedStyle(e,null).getPropertyValue(t)}function Z(e){return function(){delete this[e]}}function W(e,t){return function(){this[e]=t}}function j(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function q(e){return e.trim().split(/^|\s+/)}function U(e){return e.classList||new G(e)}function G(e){this._node=e,this._names=q(e.getAttribute("class")||"")}function Q(e,t){for(var n=U(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function J(e,t){for(var n=U(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function ee(e){return function(){Q(this,e)}}function te(e){return function(){J(this,e)}}function ne(e,t){return function(){(t.apply(this,arguments)?Q:J)(this,e)}}function oe(){this.textContent=""}function re(e){return function(){this.textContent=e}}function ie(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function ae(){this.innerHTML=""}function se(e){return function(){this.innerHTML=e}}function le(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function ce(){this.nextSibling&&this.parentNode.appendChild(this)}function ue(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function de(){return null}function he(){var e=this.parentNode;e&&e.removeChild(this)}function fe(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ge(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function pe(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}function me(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function ye(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function ve(e,t,n){var o=X(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function be(e,t){return function(){return ve(this,e,t)}}function we(e,t){return function(){return ve(this,e,t.apply(this,arguments))}}k.prototype={constructor:k,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},G.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var xe=[null];function Se(e,t){this._groups=e,this._parents=t}function Ee(){return new Se([[document.documentElement]],xe)}function _e(e){return"string"==typeof e?new Se([[document.querySelector(e)]],[document.documentElement]):new Se([[e]],xe)}function Ce(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}Se.prototype=Ee.prototype={constructor:Se,select:function(e){"function"!=typeof e&&(e=y(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new Se(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return v(e.apply(this,arguments))}}(e):w(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new Se(o,r)},selectChild:function(e){return this.select(null==e?_:function(e){return function(){return E.call(this.children,e)}}("function"==typeof e?e:S(e)))},selectChildren:function(e){return this.selectAll(null==e?M:function(e){return function(){return C.call(this.children,e)}}("function"==typeof e?e:S(e)))},filter:function(e){"function"!=typeof e&&(e=x(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Se(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,I);var n=t?O:A,o=this._parents,r=this._groups;"function"!=typeof e&&(e=P(e));for(var i=r.length,a=new Array(i),s=new Array(i),l=new Array(i),c=0;c<i;++c){var u=o[c],d=r[c],h=d.length,f=D(e.call(u,u&&u.__data__,c,o)),g=f.length,p=s[c]=new Array(g),m=a[c]=new Array(g),y=l[c]=new Array(h);n(u,d,p,m,y,f,t);for(var v,b,w=0,x=0;w<g;++w)if(v=p[w]){for(w>=x&&(x=w+1);!(b=m[x])&&++x<g;);v._next=b||null}}return(a=new Se(a,o))._enter=s,a._exit=l,a},enter:function(){return new Se(this._enter||this._groups.map(N),this._parents)},exit:function(){return new Se(this._exit||this._groups.map(N),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new Se(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=R);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new Se(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=h(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?$:z:"function"==typeof t?n.local?Y:L:n.local?B:T)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?H:"function"==typeof t?K:V)(e,t,null==n?"":n)):F(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?Z:"function"==typeof t?j:W)(e,t)):this.node()[e]},classed:function(e,t){var n=q(e+"");if(arguments.length<2){for(var o=U(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?ne:t?ee:te)(n,t))},text:function(e){return arguments.length?this.each(null==e?oe:("function"==typeof e?ie:re)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?ae:("function"==typeof e?le:se)(e)):this.node().innerHTML},raise:function(){return this.each(ce)},lower:function(){return this.each(ue)},append:function(e){var t="function"==typeof e?e:p(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:p(e),o=null==t?de:"function"==typeof t?t:y(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(he)},clone:function(e){return this.select(e?ge:fe)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=pe(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?ye:me,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?we:be)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Me={passive:!1},Ne={capture:!0,passive:!1};function ke(e){e.stopImmediatePropagation()}function Pe(e){e.preventDefault(),e.stopImmediatePropagation()}function Ae(e){var t=e.document.documentElement,n=_e(e).on("dragstart.drag",Pe,Ne);"onselectstart"in t?n.on("selectstart.drag",Pe,Ne):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Oe(e,t){var n=e.document.documentElement,o=_e(e).on("dragstart.drag",null);t&&(o.on("click.drag",Pe,Ne),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var Ie=e=>()=>e;function De(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function Re(e){return!e.ctrlKey&&!e.button}function ze(){return this.parentNode}function $e(e,t){return null==t?{x:e.x,y:e.y}:t}function Te(){return navigator.maxTouchPoints||"ontouchstart"in this}function Be(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Le(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function Ye(){}De.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var Xe=.7,He=1/Xe,Ve="\\s*([+-]?\\d+)\\s*",Ke="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Fe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Ze=/^#([0-9a-f]{3,8})$/,We=new RegExp(`^rgb\\(${Ve},${Ve},${Ve}\\)$`),je=new RegExp(`^rgb\\(${Fe},${Fe},${Fe}\\)$`),qe=new RegExp(`^rgba\\(${Ve},${Ve},${Ve},${Ke}\\)$`),Ue=new RegExp(`^rgba\\(${Fe},${Fe},${Fe},${Ke}\\)$`),Ge=new RegExp(`^hsl\\(${Ke},${Fe},${Fe}\\)$`),Qe=new RegExp(`^hsla\\(${Ke},${Fe},${Fe},${Ke}\\)$`),Je={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function et(){return this.rgb().formatHex()}function tt(){return this.rgb().formatRgb()}function nt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Ze.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?ot(t):3===n?new st(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?rt(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?rt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=We.exec(e))?new st(t[1],t[2],t[3],1):(t=je.exec(e))?new st(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=qe.exec(e))?rt(t[1],t[2],t[3],t[4]):(t=Ue.exec(e))?rt(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Ge.exec(e))?ft(t[1],t[2]/100,t[3]/100,1):(t=Qe.exec(e))?ft(t[1],t[2]/100,t[3]/100,t[4]):Je.hasOwnProperty(e)?ot(Je[e]):"transparent"===e?new st(NaN,NaN,NaN,0):null}function ot(e){return new st(e>>16&255,e>>8&255,255&e,1)}function rt(e,t,n,o){return o<=0&&(e=t=n=NaN),new st(e,t,n,o)}function it(e){return e instanceof Ye||(e=nt(e)),e?new st((e=e.rgb()).r,e.g,e.b,e.opacity):new st}function at(e,t,n,o){return 1===arguments.length?it(e):new st(e,t,n,null==o?1:o)}function st(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function lt(){return`#${ht(this.r)}${ht(this.g)}${ht(this.b)}`}function ct(){const e=ut(this.opacity);return`${1===e?"rgb(":"rgba("}${dt(this.r)}, ${dt(this.g)}, ${dt(this.b)}${1===e?")":`, ${e})`}`}function ut(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function dt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ht(e){return((e=dt(e))<16?"0":"")+e.toString(16)}function ft(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new pt(e,t,n,o)}function gt(e){if(e instanceof pt)return new pt(e.h,e.s,e.l,e.opacity);if(e instanceof Ye||(e=nt(e)),!e)return new pt;if(e instanceof pt)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new pt(a,s,l,e.opacity)}function pt(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function mt(e){return(e=(e||0)%360)<0?e+360:e}function yt(e){return Math.max(0,Math.min(1,e||0))}function vt(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}Be(Ye,nt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:et,formatHex:et,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return gt(this).formatHsl()},formatRgb:tt,toString:tt}),Be(st,at,Le(Ye,{brighter(e){return e=null==e?He:Math.pow(He,e),new st(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?Xe:Math.pow(Xe,e),new st(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new st(dt(this.r),dt(this.g),dt(this.b),ut(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:lt,formatHex:lt,formatHex8:function(){return`#${ht(this.r)}${ht(this.g)}${ht(this.b)}${ht(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:ct,toString:ct})),Be(pt,(function(e,t,n,o){return 1===arguments.length?gt(e):new pt(e,t,n,null==o?1:o)}),Le(Ye,{brighter(e){return e=null==e?He:Math.pow(He,e),new pt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?Xe:Math.pow(Xe,e),new pt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new st(vt(e>=240?e-240:e+120,r,o),vt(e,r,o),vt(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new pt(mt(this.h),yt(this.s),yt(this.l),ut(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ut(this.opacity);return`${1===e?"hsl(":"hsla("}${mt(this.h)}, ${100*yt(this.s)}%, ${100*yt(this.l)}%${1===e?")":`, ${e})`}`}}));var bt=e=>()=>e;function wt(e){return 1==(e=+e)?xt:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):bt(isNaN(t)?n:t)}}function xt(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):bt(isNaN(e)?t:e)}var St=function e(t){var n=wt(t);function o(e,t){var o=n((e=at(e)).r,(t=at(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=xt(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function Et(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var _t=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ct=new RegExp(_t.source,"g");function Mt(e,t){var n,o,r,i=_t.lastIndex=Ct.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=_t.exec(e))&&(o=Ct.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:Et(n,o)})),i=Ct.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}var Nt,kt=180/Math.PI,Pt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function At(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*kt,skewX:Math.atan(l)*kt,scaleX:a,scaleY:s}}function Ot(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:Et(e,r)},{i:l-2,x:Et(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Et(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Et(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Et(e,n)},{i:s-2,x:Et(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var It=Ot((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Pt:At(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),Dt=Ot((function(e){return null==e?Pt:(Nt||(Nt=document.createElementNS("http://www.w3.org/2000/svg","g")),Nt.setAttribute("transform",e),(e=Nt.transform.baseVal.consolidate())?At((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Pt)}),", ",")",")");function Rt(e){return((e=Math.exp(e))+1/e)/2}var zt,$t,Tt=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),b=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-b)/t,i=function(e){var o,r=e*a,i=Rt(b),u=c/(n*m)*(i*(o=t*r+b,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[s+u*f,l+u*g,c*i/Rt(t*r+b)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),Bt=0,Lt=0,Yt=0,Xt=0,Ht=0,Vt=0,Kt="object"==typeof performance&&performance.now?performance:Date,Ft="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Zt(){return Ht||(Ft(Wt),Ht=Kt.now()+Vt)}function Wt(){Ht=0}function jt(){this._call=this._time=this._next=null}function qt(e,t,n){var o=new jt;return o.restart(e,t,n),o}function Ut(){Ht=(Xt=Kt.now())+Vt,Bt=Lt=0;try{!function(){Zt(),++Bt;for(var e,t=zt;t;)(e=Ht-t._time)>=0&&t._call.call(void 0,e),t=t._next;--Bt}()}finally{Bt=0,function(){var e,t,n=zt,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:zt=t);$t=e,Qt(o)}(),Ht=0}}function Gt(){var e=Kt.now(),t=e-Xt;t>1e3&&(Vt-=t,Xt=e)}function Qt(e){Bt||(Lt&&(Lt=clearTimeout(Lt)),e-Ht>24?(e<1/0&&(Lt=setTimeout(Ut,e-Kt.now()-Vt)),Yt&&(Yt=clearInterval(Yt))):(Yt||(Xt=Kt.now(),Yt=setInterval(Gt,1e3)),Bt=1,Ft(Ut)))}function Jt(e,t,n){var o=new jt;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}jt.prototype=qt.prototype={constructor:jt,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?Zt():+n)+(null==t?0:+t),this._next||$t===this||($t?$t._next=this:zt=this,$t=this),this._call=e,this._time=n,Qt()},stop:function(){this._call&&(this._call=null,this._time=1/0,Qt())}};var en=i("start","end","cancel","interrupt"),tn=[];function nn(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(1!==n.state)return l();for(c in r)if((h=r[c]).name===n.name){if(3===h.state)return Jt(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(Jt((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=3,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=qt(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:en,tween:tn,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function on(e,t){var n=an(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function rn(e,t){var n=an(e,t);if(n.state>3)throw new Error("too late; already running");return n}function an(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function sn(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function ln(e,t){var n,o;return function(){var r=rn(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function cn(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=rn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function un(e,t,n){var o=e._id;return e.each((function(){var e=rn(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return an(e,o).value[t]}}function dn(e,t){var n;return("number"==typeof t?Et:t instanceof nt?St:(n=nt(t))?(t=n,St):Mt)(e,t)}function hn(e){return function(){this.removeAttribute(e)}}function fn(e){return function(){this.removeAttributeNS(e.space,e.local)}}function gn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function pn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function mn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function yn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function vn(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function bn(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function wn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&bn(e,r)),n}return r._value=t,r}function xn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&vn(e,r)),n}return r._value=t,r}function Sn(e,t){return function(){on(this,e).delay=+t.apply(this,arguments)}}function En(e,t){return t=+t,function(){on(this,e).delay=t}}function _n(e,t){return function(){rn(this,e).duration=+t.apply(this,arguments)}}function Cn(e,t){return t=+t,function(){rn(this,e).duration=t}}function Mn(e,t){if("function"!=typeof t)throw new Error;return function(){rn(this,e).ease=t}}function Nn(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?on:rn;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}var kn=Ee.prototype.constructor;function Pn(e){return function(){this.style.removeProperty(e)}}function An(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function On(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&An(e,i,n)),o}return i._value=t,i}function In(e){return function(t){this.textContent=e.call(this,t)}}function Dn(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&In(o)),t}return o._value=e,o}var Rn=0;function zn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function $n(){return++Rn}var Tn=Ee.prototype;zn.prototype={constructor:zn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=y(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,nn(d[h],t,n,h,d,an(s,n)));return new zn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=w(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=an(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&nn(h,t,n,p,f,g);i.push(f),a.push(l)}return new zn(i,a,t,n)},selectChild:Tn.selectChild,selectChildren:Tn.selectChildren,filter:function(e){"function"!=typeof e&&(e=x(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new zn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new zn(a,this._parents,this._name,this._id)},selection:function(){return new kn(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=$n(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=an(a,t);nn(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new zn(o,this._parents,e,n)},call:Tn.call,nodes:Tn.nodes,node:Tn.node,size:Tn.size,empty:Tn.empty,each:Tn.each,on:function(e,t){var n=this._id;return arguments.length<2?an(this.node(),n).on.on(e):this.each(Nn(n,e,t))},attr:function(e,t){var n=h(e),o="transform"===n?Dt:dn;return this.attrTween(e,"function"==typeof t?(n.local?yn:mn)(n,o,un(this,"attr."+e,t)):null==t?(n.local?fn:hn)(n):(n.local?pn:gn)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=h(e);return this.tween(n,(o.local?wn:xn)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?It:dn;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=F(this,e),a=(this.style.removeProperty(e),F(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,Pn(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=F(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=F(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,un(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=rn(this,e),c=l.on,u=null==l.value[a]?i||(i=Pn(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=F(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,On(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(un(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,Dn(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=an(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?ln:cn)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Sn:En)(t,e)):an(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?_n:Cn)(t,e)):an(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(Mn(t,e)):an(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;rn(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=rn(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:Tn[Symbol.iterator]};var Bn={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function Ln(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}Ee.prototype.interrupt=function(e){return this.each((function(){sn(this,e)}))},Ee.prototype.transition=function(e){var t,n;e instanceof zn?(t=e._id,e=e._name):(t=$n(),(n=Bn).time=Zt(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&nn(a,e,t,c,s,n||Ln(a,t));return new zn(o,this._parents,e,t)};var Yn=e=>()=>e;function Xn(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function Hn(e,t,n){this.k=e,this.x=t,this.y=n}Hn.prototype={constructor:Hn,scale:function(e){return 1===e?this:new Hn(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new Hn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Vn=new Hn(1,0,0);function Kn(e){e.stopImmediatePropagation()}function Fn(e){e.preventDefault(),e.stopImmediatePropagation()}function Zn(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function Wn(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function jn(){return this.__zoom||Vn}function qn(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function Un(){return navigator.maxTouchPoints||"ontouchstart"in this}function Gn(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function Qn(){var e,t,n,o=Zn,r=Wn,a=Gn,s=qn,l=Un,c=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],d=250,h=Tt,f=i("start","zoom","end"),g=500,p=0,m=10;function y(e){e.property("__zoom",jn).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",C).on("dblclick.zoom",M).filter(l).on("touchstart.zoom",N).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function v(e,t){return(t=Math.max(c[0],Math.min(c[1],t)))===e.k?e:new Hn(t,e.x,e.y)}function b(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new Hn(e.k,o,r)}function w(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function x(e,t,n,o){e.on("start.zoom",(function(){S(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){S(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,a=S(e,i).event(o),s=r.apply(e,i),l=null==n?w(s):"function"==typeof n?n.apply(e,i):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,d="function"==typeof t?t.apply(e,i):t,f=h(u.invert(l).concat(c/u.k),d.invert(l).concat(c/d.k));return function(e){if(1===e)e=d;else{var t=f(e),n=c/t[2];e=new Hn(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function S(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function _(e,...t){if(o.apply(this,arguments)){var n=S(this,t).event(e),r=this.__zoom,i=Math.max(c[0],Math.min(c[1],r.k*Math.pow(2,s.apply(this,arguments)))),l=Ce(e);if(n.wheel)n.mouse[0][0]===l[0]&&n.mouse[0][1]===l[1]||(n.mouse[1]=r.invert(n.mouse[0]=l)),clearTimeout(n.wheel);else{if(r.k===i)return;n.mouse=[l,r.invert(l)],sn(this),n.start()}Fn(e),n.wheel=setTimeout(d,150),n.zoom("mouse",a(b(v(r,i),n.mouse[0],n.mouse[1]),n.extent,u))}function d(){n.wheel=null,n.end()}}function C(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,i=S(this,t,!0).event(e),s=_e(e.view).on("mousemove.zoom",h,!0).on("mouseup.zoom",f,!0),l=Ce(e,r),c=e.clientX,d=e.clientY;Ae(e.view),Kn(e),i.mouse=[l,this.__zoom.invert(l)],sn(this),i.start()}function h(e){if(Fn(e),!i.moved){var t=e.clientX-c,n=e.clientY-d;i.moved=t*t+n*n>p}i.event(e).zoom("mouse",a(b(i.that.__zoom,i.mouse[0]=Ce(e,r),i.mouse[1]),i.extent,u))}function f(e){s.on("mousemove.zoom mouseup.zoom",null),Oe(e.view,i.moved),Fn(e),i.event(e).end()}}function M(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,i=Ce(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(i),l=n.k*(e.shiftKey?.5:2),c=a(b(v(n,l),i,s),r.apply(this,t),u);Fn(e),d>0?_e(this).transition().duration(d).call(x,c,i,e):_e(this).call(y.transform,c,i,e)}}function N(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=S(this,r,n.changedTouches.length===u).event(n);for(Kn(n),a=0;a<u;++a)l=[l=Ce(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),g)),sn(this),d.start())}}function k(e,...t){if(this.__zooming){var n,o,r,i,s=S(this,t).event(e),l=e.changedTouches,c=l.length;for(Fn(e),n=0;n<c;++n)r=Ce(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=v(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],i=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],i=s.touch0[1]}s.zoom("touch",a(b(o,r,i),s.extent,u))}}function P(e,...o){if(this.__zooming){var r,i,a=S(this,o).event(e),s=e.changedTouches,l=s.length;for(Kn(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),g),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Ce(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<m)){var c=_e(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return y.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",jn),e!==r?x(e,t,n,o):r.interrupt().each((function(){S(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},y.scaleBy=function(e,t,n,o){y.scaleTo(e,(function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n}),n,o)},y.scaleTo=function(e,t,n,o){y.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,i=null==n?w(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(i),l="function"==typeof t?t.apply(this,arguments):t;return a(b(v(o,l),i,s),e,u)}),n,o)},y.translateBy=function(e,t,n,o){y.transform(e,(function(){return a(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),u)}),null,o)},y.translateTo=function(e,t,n,o,i){y.transform(e,(function(){var e=r.apply(this,arguments),i=this.__zoom,s=null==o?w(e):"function"==typeof o?o.apply(this,arguments):o;return a(Vn.translate(s[0],s[1]).scale(i.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),o,i)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=_e(this.that).datum();f.call(e,this.that,new Xn(e,{sourceEvent:this.sourceEvent,target:y,type:e,transform:this.that.__zoom,dispatch:f}),t)}},y.wheelDelta=function(e){return arguments.length?(s="function"==typeof e?e:Yn(+e),y):s},y.filter=function(e){return arguments.length?(o="function"==typeof e?e:Yn(!!e),y):o},y.touchable=function(e){return arguments.length?(l="function"==typeof e?e:Yn(!!e),y):l},y.extent=function(e){return arguments.length?(r="function"==typeof e?e:Yn([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),y):r},y.scaleExtent=function(e){return arguments.length?(c[0]=+e[0],c[1]=+e[1],y):[c[0],c[1]]},y.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],y):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},y.constrain=function(e){return arguments.length?(a=e,y):a},y.duration=function(e){return arguments.length?(d=+e,y):d},y.interpolate=function(e){return arguments.length?(h=e,y):h},y.on=function(){var e=f.on.apply(f,arguments);return e===f?y:e},y.clickDistance=function(e){return arguments.length?(p=(e=+e)*e,y):Math.sqrt(p)},y.tapDistance=function(e){return arguments.length?(m=+e,y):m},y}function Jn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}Hn.prototype;var eo,to,no,oo={},ro={},io={},ao={get exports(){return io},set exports(e){io=e}},so={};function lo(){return to||(to=1,function(e){e.exports=function(){if(eo)return so;eo=1;var e=t,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,i=e.useLayoutEffect,a=e.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!n(e,o)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),l=o({inst:{value:n,getSnapshot:t}}),c=l[0].inst,u=l[1];return i((function(){c.value=n,c.getSnapshot=t,s(c)&&u({inst:c})}),[e,n,t]),r((function(){return s(c)&&u({inst:c}),e((function(){s(c)&&u({inst:c})}))}),[e]),a(n),n};return so.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:l,so}()}(ao)),io}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(e){e.exports=function(){if(no)return ro;no=1;var e=t,n=lo(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=n.useSyncExternalStore,i=e.useRef,a=e.useEffect,s=e.useMemo,l=e.useDebugValue;return ro.useSyncExternalStoreWithSelector=function(e,t,n,c,u){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=s((function(){function e(e){if(!a){if(a=!0,r=e,e=c(e),void 0!==u&&h.hasValue){var t=h.value;if(u(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=c(e);return void 0!==u&&u(t,n)?t:(r=e,i=n)}var r,i,a=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,n,c,u]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),l(f),f},ro}()}({get exports(){return oo},set exports(e){oo=e}});var co=Jn(oo);const uo=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:ho}=co;function fo(e,n=e.getState,o){const r=ho(e.subscribe,e.getState,e.getServerState||e.getState,n,o);return t.useDebugValue(r),r}const go=(e,t)=>{const n=(e=>e?uo(e):uo)(e),o=(e,o=t)=>fo(n,e,o);return Object.assign(o,n),o},po=t.createContext(null),mo=po.Provider,yo=e=>`Node type "${e}" not found. Using fallback type "default".`,vo=()=>"The React Flow parent container needs a width and a height to render the graph.",bo=()=>"Only child nodes can use a parent extent.",wo=e=>`Marker type "${e}" doesn't exist.`,xo=(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,So=()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",Eo=e=>`Edge type "${e}" not found. Using fallback type "default".`,_o=e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,Co=(()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001")();function Mo(e,n){const o=t.useContext(po);if(null===o)throw new Error(Co);return fo(o,e,n)}const No=()=>{const e=t.useContext(po);if(null===e)throw new Error(Co);return t.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy})),[e])},ko=e=>e.userSelectionActive?"none":"all";function Po({position:e,children:o,className:r,style:i,...a}){const s=Mo(ko),l=`${e}`.split("-");return t.createElement("div",{className:n(["react-flow__panel",r,...l]),style:{...i,pointerEvents:s},...a},o)}function Ao({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.createElement(Po,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},t.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var Oo=t.memo((({x:e,y:o,label:r,labelStyle:i={},labelShowBg:a=!0,labelBgStyle:s={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:d,...h})=>{const f=t.useRef(null),[g,p]=t.useState({x:0,y:0,width:0,height:0}),m=n(["react-flow__edge-textwrapper",d]);return t.useEffect((()=>{if(f.current){const e=f.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[r]),void 0!==r&&r?t.createElement("g",{transform:`translate(${e-g.width/2} ${o-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...h},a&&t.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:s,rx:c,ry:c}),t.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:f,style:i},r),u):null}));const Io=e=>({width:e.offsetWidth,height:e.offsetHeight}),Do=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),Ro=(e={x:0,y:0},t)=>({x:Do(e.x,t[0][0],t[1][0]),y:Do(e.y,t[0][1],t[1][1])}),zo=(e,t,n)=>e<t?Do(Math.abs(e-t),1,50)/50:e>n?-Do(Math.abs(e-n),1,50)/50:0,$o=(e,t)=>[20*zo(e.x,35,t.width-35),20*zo(e.y,35,t.height-35)],To=e=>e.getRootNode?.()||window?.document,Bo=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Lo=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Yo=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),Xo=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),Ho=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},Vo=e=>!isNaN(e)&&isFinite(e),Ko=Symbol.for("internals"),Fo=["Enter"," ","Escape"];function Zo(e){const t=((e=>"nativeEvent"in e)(e)?e.nativeEvent:e).composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(t?.nodeName)||t?.hasAttribute("contenteditable")||!!t?.closest(".nokey")}const Wo=e=>"clientX"in e,jo=(e,t)=>{const n=Wo(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},qo=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,Uo=({id:e,path:n,labelX:o,labelY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g=20})=>t.createElement(t.Fragment,null,t.createElement("path",{id:e,style:d,d:n,fill:"none",className:"react-flow__edge-path",markerEnd:h,markerStart:f}),g&&t.createElement("path",{d:n,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&Vo(o)&&Vo(r)?t.createElement(Oo,{x:o,y:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null);function Go(e,t,n){return void 0===n?n:o=>{const r=t().edges.find((t=>t.id===e));r&&n(o,{...r})}}function Qo({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function Jo({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}var er,tr,nr,or,rr,ir;function ar({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===ir.Left||e===ir.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function sr({sourceX:e,sourceY:t,sourcePosition:n=ir.Bottom,targetX:o,targetY:r,targetPosition:i=ir.Top}){const[a,s]=ar({pos:n,x1:e,y1:t,x2:o,y2:r}),[l,c]=ar({pos:i,x1:o,y1:r,x2:e,y2:t}),[u,d,h,f]=Jo({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:s,targetControlX:l,targetControlY:c});return[`M${e},${t} C${a},${s} ${l},${c} ${o},${r}`,u,d,h,f]}Uo.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(er||(er={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(tr||(tr={})),function(e){e.Partial="partial",e.Full="full"}(nr||(nr={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(or||(or={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(rr||(rr={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(ir||(ir={}));const lr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=ir.Bottom,targetPosition:a=ir.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})=>{const[y,v,b]=sr({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a});return t.createElement(Uo,{path:y,labelX:v,labelY:b,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})}));lr.displayName="SimpleBezierEdge";const cr={[ir.Left]:{x:-1,y:0},[ir.Right]:{x:1,y:0},[ir.Top]:{x:0,y:-1},[ir.Bottom]:{x:0,y:1}},ur=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function dr({source:e,sourcePosition:t=ir.Bottom,target:n,targetPosition:o=ir.Top,center:r,offset:i}){const a=cr[t],s=cr[o],l={x:e.x+a.x*i,y:e.y+a.y*i},c={x:n.x+s.x*i,y:n.y+s.y*i},u=(({source:e,sourcePosition:t=ir.Bottom,target:n})=>t===ir.Left||t===ir.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1})({source:l,sourcePosition:t,target:c}),d=0!==u.x?"x":"y",h=u[d];let f,g,p=[];const m={x:0,y:0},y={x:0,y:0},[v,b,w,x]=Qo({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]==-1){f=r.x??v,g=r.y??b;const e=[{x:f,y:l.y},{x:f,y:c.y}],t=[{x:l.x,y:g},{x:c.x,y:g}];p=a[d]===h?"x"===d?e:t:"x"===d?t:e}else{const r=[{x:l.x,y:c.y}],u=[{x:c.x,y:l.y}];if(p="x"===d?a.x===h?u:r:a.y===h?r:u,t===o){const t=Math.abs(e[d]-n[d]);if(t<=i){const o=Math.min(i-1,i-t);a[d]===h?m[d]=(l[d]>e[d]?-1:1)*o:y[d]=(c[d]>n[d]?-1:1)*o}}if(t!==o){const e="x"===d?"y":"x",t=a[d]===s[e],n=l[e]>c[e],o=l[e]<c[e];(1===a[d]&&(!t&&n||t&&o)||1!==a[d]&&(!t&&o||t&&n))&&(p="x"===d?r:u)}const v={x:l.x+m.x,y:l.y+m.y},b={x:c.x+y.x,y:c.y+y.y};Math.max(Math.abs(v.x-p[0].x),Math.abs(b.x-p[0].x))>=Math.max(Math.abs(v.y-p[0].y),Math.abs(b.y-p[0].y))?(f=(v.x+b.x)/2,g=p[0].y):(f=p[0].x,g=(v.y+b.y)/2)}return[[e,{x:l.x+m.x,y:l.y+m.y},...p,{x:c.x+y.x,y:c.y+y.y},n],f,g,w,x]}function hr({sourceX:e,sourceY:t,sourcePosition:n=ir.Bottom,targetX:o,targetY:r,targetPosition:i=ir.Top,borderRadius:a=5,centerX:s,centerY:l,offset:c=20}){const[u,d,h,f,g]=dr({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:i,center:{x:s,y:l},offset:c});return[u.reduce(((e,t,n)=>{let o="";return o=n>0&&n<u.length-1?function(e,t,n,o){const r=Math.min(ur(e,t)/2,ur(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(u[n-1],t,u[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),d,h,f,g]}const fr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,sourcePosition:h=ir.Bottom,targetPosition:f=ir.Top,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,w]=hr({sourceX:e,sourceY:n,sourcePosition:h,targetX:o,targetY:r,targetPosition:f,borderRadius:m?.borderRadius,offset:m?.offset});return t.createElement(Uo,{path:v,labelX:b,labelY:w,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:g,markerStart:p,interactionWidth:y})}));fr.displayName="SmoothStepEdge";const gr=t.memo((e=>t.createElement(fr,{...e,pathOptions:t.useMemo((()=>({borderRadius:0,offset:e.pathOptions?.offset})),[e.pathOptions?.offset])})));gr.displayName="StepEdge";const pr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})=>{const[p,m,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=Qo({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}({sourceX:e,sourceY:n,targetX:o,targetY:r});return t.createElement(Uo,{path:p,labelX:m,labelY:y,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})}));function mr(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function yr({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){switch(e){case ir.Left:return[t-mr(t-o,i),n];case ir.Right:return[t+mr(o-t,i),n];case ir.Top:return[t,n-mr(n-r,i)];case ir.Bottom:return[t,n+mr(r-n,i)]}}function vr({sourceX:e,sourceY:t,sourcePosition:n=ir.Bottom,targetX:o,targetY:r,targetPosition:i=ir.Top,curvature:a=.25}){const[s,l]=yr({pos:n,x1:e,y1:t,x2:o,y2:r,c:a}),[c,u]=yr({pos:i,x1:o,y1:r,x2:e,y2:t,c:a}),[d,h,f,g]=Jo({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${e},${t} C${s},${l} ${c},${u} ${o},${r}`,d,h,f,g]}pr.displayName="StraightEdge";const br=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=ir.Bottom,targetPosition:a=ir.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,w]=vr({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a,curvature:m?.curvature});return t.createElement(Uo,{path:v,labelX:b,labelY:w,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:y})}));br.displayName="BezierEdge";const wr=t.createContext(null),xr=wr.Provider;wr.Consumer;const Sr=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,Er=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`},_r=({x:e,y:t},[n,o,r],i,[a,s])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?{x:a*Math.round(l.x/a),y:s*Math.round(l.y/s)}:l},Cr=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),Mr=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},Nr=(e,t=[0,0])=>{if(0===e.length)return{x:0,y:0,width:0,height:0};const n=e.reduce(((e,n)=>{const{x:o,y:r}=Mr(n,t).positionAbsolute;return Bo(e,Lo({x:o,y:r,width:n.width||0,height:n.height||0}))}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Yo(n)},kr=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const l={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},c=[];return e.forEach((e=>{const{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;const{positionAbsolute:u}=Mr(e,s),d={x:u.x,y:u.y,width:t||0,height:n||0},h=Ho(l,d);(void 0===t||void 0===n||null===t||null===n||i&&h>0||h>=(t||0)*(n||0)||e.dragging)&&c.push(e)})),c},Pr=(e,t)=>{const n=e.map((e=>e.id));return t.filter((e=>n.includes(e.source)||n.includes(e.target)))},Ar=(e,t,n,o,r,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),l=Math.min(a,s),c=Do(l,o,r);return{x:t/2-(e.x+e.width/2)*c,y:n/2-(e.y+e.height/2)*c,zoom:c}},Or=(e,t=0)=>e.transition().duration(t);function Ir(e,t,n,o){return(t[n]||[]).reduce(((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t)),[])}const Dr={source:null,target:null,sourceHandle:null,targetHandle:null},Rr=()=>({handleDomNode:null,isValid:!1,connection:Dr,endHandle:null});function zr(e,t,n,o,r,i,a){const s="target"===r,l=a.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...Rr(),handleDomNode:l};if(l){const e=$r(void 0,l),r=l.getAttribute("data-nodeid"),a=l.getAttribute("data-handleid"),u=l.classList.contains("connectable"),d=l.classList.contains("connectableend"),h={source:s?r:n,sourceHandle:s?a:o,target:s?n:r,targetHandle:s?o:a};c.connection=h;u&&d&&(t===er.Strict?s&&"source"===e||!s&&"target"===e:r!==n||a!==o)&&(c.endHandle={nodeId:r,handleId:a,type:e},c.isValid=i(h))}return c}function $r(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function Tr(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function Br(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Lr({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){const u=To(e.target),{connectionMode:d,domNode:h,autoPanOnConnect:f,connectionRadius:g,onConnectStart:p,panBy:m,getNodes:y,cancelConnection:v}=i();let b,w=0;const{x:x,y:S}=jo(e),E=u?.elementFromPoint(x,S),_=$r(l,E),C=h?.getBoundingClientRect();if(!C||!_)return;let M,N=jo(e,C),k=!1,P=null,A=!1,O=null;const I=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce(((e,r)=>{if(r[Ko]){const{handleBounds:i}=r[Ko];let a=[],s=[];i&&(a=Ir(r,i,"source",`${t}-${n}-${o}`),s=Ir(r,i,"target",`${t}-${n}-${o}`)),e.push(...a,...s)}return e}),[])}({nodes:y(),nodeId:n,handleId:t,handleType:_}),D=()=>{if(!f)return;const[e,t]=$o(N,C);m({x:e,y:t}),w=requestAnimationFrame(D)};function R(e){const{transform:o}=i();N=jo(e,C);const{handle:l,validHandleResult:c}=function(e,t,n,o,r,i){const{x:a,y:s}=jo(e),l=t.elementsFromPoint(a,s).find((e=>e.classList.contains("react-flow__handle")));if(l){const e=l.getAttribute("data-nodeid");if(e){const t=$r(void 0,l),o=l.getAttribute("data-handleid"),a=i({nodeId:e,id:o,type:t});if(a){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(r.forEach((e=>{const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}})),!c.length)return{handle:null,validHandleResult:Rr()};if(1===c.length)return c[0];const d=c.some((({validHandleResult:e})=>e.isValid)),h=c.some((({handle:e})=>"target"===e.type));return c.find((({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid))||c[0]}(e,u,_r(N,o,!1,[1,1]),g,I,(e=>zr(e,d,n,t,r?"target":"source",s,u)));if(b=l,k||(D(),k=!0),O=c.handleDomNode,P=c.connection,A=c.isValid,a({connectionPosition:b&&A?Cr({x:b.x,y:b.y},o):N,connectionStatus:Br(!!b,A),connectionEndHandle:c.endHandle}),!b&&!A&&!O)return Tr(M);P.source!==P.target&&O&&(Tr(M),M=O,O.classList.add("connecting","react-flow__handle-connecting"),O.classList.toggle("valid",A),O.classList.toggle("react-flow__handle-valid",A))}function z(e){(b||O)&&P&&A&&o?.(P),i().onConnectEnd?.(e),l&&c?.(e),Tr(M),v(),cancelAnimationFrame(w),k=!1,A=!1,P=null,O=null,u.removeEventListener("mousemove",R),u.removeEventListener("mouseup",z),u.removeEventListener("touchmove",R),u.removeEventListener("touchend",z)}a({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:_,connectionStartHandle:{nodeId:n,handleId:t,type:_},connectionEndHandle:null}),p?.(e,{nodeId:n,handleId:t,handleType:_}),u.addEventListener("mousemove",R),u.addEventListener("mouseup",z),u.addEventListener("touchmove",R),u.addEventListener("touchend",z)}const Yr=()=>!0,Xr=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),Hr=t.forwardRef((({type:e="source",position:r=ir.Top,isValidConnection:i,isConnectable:a=!0,isConnectableStart:s=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:d,className:h,onMouseDown:f,onTouchStart:g,...p},m)=>{const y=c||null,v="target"===e,b=No(),w=t.useContext(wr),{connectOnClick:x,noPanClassName:S}=Mo(Xr,o),{connecting:E,clickConnecting:_}=Mo(((e,t,n)=>o=>{const{connectionStartHandle:r,connectionEndHandle:i,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}})(w,y,e),o);w||b.getState().onError?.("010",So());const C=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=b.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=b.getState();t(((e,t)=>{if(!e.source||!e.target)return t;let n;var o;return n="id"in(o=e)&&"source"in o&&"target"in o?{...e}:{...e,id:Sr(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:t.concat(n)})(r,e))}n?.(r),u?.(r)},M=e=>{if(!w)return;const t=Wo(e);s&&(t&&0===e.button||!t)&&Lr({event:e,handleId:y,nodeId:w,onConnect:C,isTarget:v,getState:b.getState,setState:b.setState,isValidConnection:i||b.getState().isValidConnection||Yr}),t?f?.(e):g?.(e)};return t.createElement("div",{"data-handleid":y,"data-nodeid":w,"data-handlepos":r,"data-id":`${w}-${y}-${e}`,className:n(["react-flow__handle",`react-flow__handle-${r}`,"nodrag",S,h,{source:!v,target:v,connectable:a,connectablestart:s,connectableend:l,connecting:_,connectionindicator:a&&(s&&!E||l&&E)}]),onMouseDown:M,onTouchStart:M,onClick:x?t=>{const{onClickConnectStart:n,onClickConnectEnd:o,connectionClickStartHandle:r,connectionMode:a,isValidConnection:l}=b.getState();if(!w||!r&&!s)return;if(!r)return n?.(t,{nodeId:w,handleId:y,handleType:e}),void b.setState({connectionClickStartHandle:{nodeId:w,type:e,handleId:y}});const c=To(t.target),u=i||l||Yr,{connection:d,isValid:h}=zr({nodeId:w,id:y,type:e},a,r.nodeId,r.handleId||null,r.type,u,c);h&&C(d),o?.(t),b.setState({connectionClickStartHandle:null})}:void 0,ref:m,...p},d)}));Hr.displayName="Handle";var Vr=t.memo(Hr);const Kr=({data:e,isConnectable:n,targetPosition:o=ir.Top,sourcePosition:r=ir.Bottom})=>t.createElement(t.Fragment,null,t.createElement(Vr,{type:"target",position:o,isConnectable:n}),e?.label,t.createElement(Vr,{type:"source",position:r,isConnectable:n}));Kr.displayName="DefaultNode";var Fr=t.memo(Kr);const Zr=({data:e,isConnectable:n,sourcePosition:o=ir.Bottom})=>t.createElement(t.Fragment,null,e?.label,t.createElement(Vr,{type:"source",position:o,isConnectable:n}));Zr.displayName="InputNode";var Wr=t.memo(Zr);const jr=({data:e,isConnectable:n,targetPosition:o=ir.Top})=>t.createElement(t.Fragment,null,t.createElement(Vr,{type:"target",position:o,isConnectable:n}),e?.label);jr.displayName="OutputNode";var qr=t.memo(jr);const Ur=()=>null;Ur.displayName="GroupNode";const Gr=e=>({selectedNodes:e.getNodes().filter((e=>e.selected)),selectedEdges:e.edges.filter((e=>e.selected)).map((e=>({...e})))}),Qr=e=>e.id;function Jr(e,t){return o(e.selectedNodes.map(Qr),t.selectedNodes.map(Qr))&&o(e.selectedEdges.map(Qr),t.selectedEdges.map(Qr))}const ei=t.memo((({onSelectionChange:e})=>{const n=No(),{selectedNodes:o,selectedEdges:r}=Mo(Gr,Jr);return t.useEffect((()=>{const t={nodes:o,edges:r};e?.(t),n.getState().onSelectionChange.forEach((e=>e(t)))}),[o,r,e]),null}));ei.displayName="SelectionListener";const ti=e=>!!e.onSelectionChange;function ni({onSelectionChange:e}){const n=Mo(ti);return e||n?t.createElement(ei,{onSelectionChange:e}):null}const oi=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function ri(e,n){t.useEffect((()=>{void 0!==e&&n(e)}),[e])}function ii(e,n,o){t.useEffect((()=>{void 0!==n&&o({[e]:n})}),[n])}const ai=({nodes:e,edges:n,defaultNodes:r,defaultEdges:i,onConnect:a,onConnectStart:s,onConnectEnd:l,onClickConnectStart:c,onClickConnectEnd:u,nodesDraggable:d,nodesConnectable:h,nodesFocusable:f,edgesFocusable:g,edgesUpdatable:p,elevateNodesOnSelect:m,minZoom:y,maxZoom:v,nodeExtent:b,onNodesChange:w,onEdgesChange:x,elementsSelectable:S,connectionMode:E,snapGrid:_,snapToGrid:C,translateExtent:M,connectOnClick:N,defaultEdgeOptions:k,fitView:P,fitViewOptions:A,onNodesDelete:O,onEdgesDelete:I,onNodeDrag:D,onNodeDragStart:R,onNodeDragStop:z,onSelectionDrag:$,onSelectionDragStart:T,onSelectionDragStop:B,noPanClassName:L,nodeOrigin:Y,rfId:X,autoPanOnConnect:H,autoPanOnNodeDrag:V,onError:K,connectionRadius:F,isValidConnection:Z,nodeDragThreshold:W})=>{const{setNodes:j,setEdges:q,setDefaultNodesAndEdges:U,setMinZoom:G,setMaxZoom:Q,setTranslateExtent:J,setNodeExtent:ee,reset:te}=Mo(oi,o),ne=No();return t.useEffect((()=>{const e=i?.map((e=>({...e,...k})));return U(r,e),()=>{te()}}),[]),ii("defaultEdgeOptions",k,ne.setState),ii("connectionMode",E,ne.setState),ii("onConnect",a,ne.setState),ii("onConnectStart",s,ne.setState),ii("onConnectEnd",l,ne.setState),ii("onClickConnectStart",c,ne.setState),ii("onClickConnectEnd",u,ne.setState),ii("nodesDraggable",d,ne.setState),ii("nodesConnectable",h,ne.setState),ii("nodesFocusable",f,ne.setState),ii("edgesFocusable",g,ne.setState),ii("edgesUpdatable",p,ne.setState),ii("elementsSelectable",S,ne.setState),ii("elevateNodesOnSelect",m,ne.setState),ii("snapToGrid",C,ne.setState),ii("snapGrid",_,ne.setState),ii("onNodesChange",w,ne.setState),ii("onEdgesChange",x,ne.setState),ii("connectOnClick",N,ne.setState),ii("fitViewOnInit",P,ne.setState),ii("fitViewOnInitOptions",A,ne.setState),ii("onNodesDelete",O,ne.setState),ii("onEdgesDelete",I,ne.setState),ii("onNodeDrag",D,ne.setState),ii("onNodeDragStart",R,ne.setState),ii("onNodeDragStop",z,ne.setState),ii("onSelectionDrag",$,ne.setState),ii("onSelectionDragStart",T,ne.setState),ii("onSelectionDragStop",B,ne.setState),ii("noPanClassName",L,ne.setState),ii("nodeOrigin",Y,ne.setState),ii("rfId",X,ne.setState),ii("autoPanOnConnect",H,ne.setState),ii("autoPanOnNodeDrag",V,ne.setState),ii("onError",K,ne.setState),ii("connectionRadius",F,ne.setState),ii("isValidConnection",Z,ne.setState),ii("nodeDragThreshold",W,ne.setState),ri(e,j),ri(n,q),ri(y,G),ri(v,Q),ri(M,J),ri(b,ee),null},si={display:"none"},li={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},ci="react-flow__node-desc",ui="react-flow__edge-desc",di=e=>e.ariaLiveMessage;function hi({rfId:e}){const n=Mo(di);return t.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:li},n)}function fi({rfId:e,disableKeyboardA11y:n}){return t.createElement(t.Fragment,null,t.createElement("div",{id:`${ci}-${e}`,style:si},"Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),t.createElement("div",{id:`${ui}-${e}`,style:si},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!n&&t.createElement(hi,{rfId:e}))}var gi=(e=null,n={actInsideInputWithModifier:!0})=>{const[o,r]=t.useState(!1),i=t.useRef(!1),a=t.useRef(new Set([])),[s,l]=t.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.split("+"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return t.useEffect((()=>{const t="undefined"!=typeof document?document:null,o=n?.target||t;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Zo(e))return!1;const t=mi(e.code,l);a.current.add(e[t]),pi(s,a.current,!1)&&(e.preventDefault(),r(!0))},t=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Zo(e))return!1;const t=mi(e.code,l);pi(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),r(!1)};return o?.addEventListener("keydown",e),o?.addEventListener("keyup",t),window.addEventListener("blur",c),()=>{o?.removeEventListener("keydown",e),o?.removeEventListener("keyup",t),window.removeEventListener("blur",c)}}}),[e,r]),o};function pi(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function mi(e,t){return t.includes(e)?"code":"key"}function yi(e,t,n,o){const r=e.parentNode||e.parentId;if(!r)return n;const i=t.get(r),a=Mr(i,o);return yi(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(i[Ko]?.z??0)>(n.z??0)?i[Ko]?.z??0:n.z??0},o)}function vi(e,t,n){e.forEach((o=>{const r=o.parentNode||o.parentId;if(r&&!e.has(r))throw new Error(`Parent node ${r} not found`);if(r||n?.[o.id]){const{x:r,y:i,z:a}=yi(o,e,{...o.position,z:o[Ko]?.z??0},t);o.positionAbsolute={x:r,y:i},o[Ko].z=a,n?.[o.id]&&(o[Ko].isParent=!0)}}))}function bi(e,t,n,o){const r=new Map,i={},a=o?1e3:0;return e.forEach((e=>{const n=(Vo(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(i[l]=!0);const c=o?.type&&o?.type!==e.type;Object.defineProperty(s,Ko,{enumerable:!1,value:{handleBounds:c?void 0:o?.[Ko]?.handleBounds,z:n}}),r.set(e.id,s)})),vi(r,n,i),r}function wi(e,t={}){const{getNodes:n,width:o,height:r,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){const e=n().filter((e=>{const n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some((t=>t.id===e.id)):n})),c=e.every((e=>e.width&&e.height));if(e.length>0&&c){const n=Nr(e,d),{x:c,y:u,zoom:h}=Ar(n,o,r,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=Vn.translate(c,u).scale(h);return"number"==typeof t.duration&&t.duration>0?s.transform(Or(l,t.duration),f):s.transform(l,f),!0}}return!1}function xi(e,t){return e.forEach((e=>{const n=t.get(e.id);n&&t.set(n.id,{...n,[Ko]:n[Ko],selected:e.selected})})),new Map(t)}function Si(e,t){return t.map((t=>{const n=e.find((e=>e.id===t.id));return n&&(t.selected=n.selected),t}))}function Ei({changedNodes:e,changedEdges:t,get:n,set:o}){const{nodeInternals:r,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&o({nodeInternals:xi(e,r)}),a?.(e)),t?.length&&(c&&o({edges:Si(t,i)}),s?.(t))}const _i=()=>{},Ci={zoomIn:_i,zoomOut:_i,zoomTo:_i,getZoom:()=>1,setViewport:_i,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:_i,fitBounds:_i,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},Mi=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection});function Ni(){const e=(()=>{const e=No(),{d3Zoom:n,d3Selection:r}=Mo(Mi,o),i=t.useMemo((()=>r&&n?{zoomIn:e=>n.scaleBy(Or(r,e?.duration),1.2),zoomOut:e=>n.scaleBy(Or(r,e?.duration),1/1.2),zoomTo:(e,t)=>n.scaleTo(Or(r,t?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(t,o)=>{const[i,a,s]=e.getState().transform,l=Vn.translate(t.x??i,t.y??a).scale(t.zoom??s);n.transform(Or(r,o?.duration),l)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>wi(e.getState,t),setCenter:(t,o,i)=>{const{width:a,height:s,maxZoom:l}=e.getState(),c=void 0!==i?.zoom?i.zoom:l,u=a/2-t*c,d=s/2-o*c,h=Vn.translate(u,d).scale(c);n.transform(Or(r,i?.duration),h)},fitBounds:(t,o)=>{const{width:i,height:a,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=Ar(t,i,a,s,l,o?.padding??.1),h=Vn.translate(c,u).scale(d);n.transform(Or(r,o?.duration),h)},project:t=>{const{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),_r(t,n,o,r)},screenToFlowPosition:t=>{const{transform:n,snapToGrid:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;const{x:a,y:s}=i.getBoundingClientRect(),l={x:t.x-a,y:t.y-s};return _r(l,n,o,r)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=Cr(t,n);return{x:a.x+r,y:a.y+i}},viewportInitialized:!0}:Ci),[n,r]);return i})(),n=No(),r=t.useCallback((()=>n.getState().getNodes().map((e=>({...e})))),[]),i=t.useCallback((e=>n.getState().nodeInternals.get(e)),[]),a=t.useCallback((()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))}),[]),s=t.useCallback((e=>{const{edges:t=[]}=n.getState();return t.find((t=>t.id===e))}),[]),l=t.useCallback((e=>{const{getNodes:t,setNodes:o,hasDefaultNodes:r,onNodesChange:i}=n.getState(),a=t(),s="function"==typeof e?e(a):e;if(r)o(s);else if(i){i(0===s.length?a.map((e=>({type:"remove",id:e.id}))):s.map((e=>({item:e,type:"reset"}))))}}),[]),c=t.useCallback((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i}=n.getState(),a="function"==typeof e?e(t):e;if(r)o(a);else if(i){i(0===a.length?t.map((e=>({type:"remove",id:e.id}))):a.map((e=>({item:e,type:"reset"}))))}}),[]),u=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:i,onNodesChange:a}=n.getState();if(i){r([...o(),...t])}else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),d=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:i,onEdgesChange:a}=n.getState();if(i)r([...o,...t]);else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),h=t.useCallback((()=>{const{getNodes:e,edges:t=[],transform:o}=n.getState(),[r,i,a]=o;return{nodes:e().map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}}),[]),f=t.useCallback((({nodes:e,edges:t})=>{const{nodeInternals:o,getNodes:r,edges:i,hasDefaultNodes:a,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=n.getState(),h=(e||[]).map((e=>e.id)),f=(t||[]).map((e=>e.id)),g=r().reduce(((e,t)=>{const n=t.parentNode||t.parentId,o=!h.includes(t.id)&&n&&e.find((e=>e.id===n));return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||o)&&e.push(t),e}),[]),p=i.filter((e=>"boolean"!=typeof e.deletable||e.deletable)),m=p.filter((e=>f.includes(e.id)));if(g||m){const e=Pr(g,p),t=[...m,...e],r=t.reduce(((e,t)=>(e.includes(t.id)||e.push(t.id),e)),[]);if((s||a)&&(s&&n.setState({edges:i.filter((e=>!r.includes(e.id)))}),a&&(g.forEach((e=>{o.delete(e.id)})),n.setState({nodeInternals:new Map(o)}))),r.length>0&&(c?.(t),d&&d(r.map((e=>({id:e,type:"remove"}))))),g.length>0&&(l?.(g),u)){u(g.map((e=>({id:e.id,type:"remove"}))))}}}),[]),g=t.useCallback((e=>{const t=Vo((o=e).width)&&Vo(o.height)&&Vo(o.x)&&Vo(o.y);var o;const r=t?null:n.getState().nodeInternals.get(e.id);if(!t&&!r)return[null,null,t];return[t?e:Xo(r),r,t]}),[]),p=t.useCallback(((e,t=!0,o)=>{const[r,i,a]=g(e);return r?(o||n.getState().getNodes()).filter((e=>{if(!(a||e.id!==i.id&&e.positionAbsolute))return!1;const n=Xo(e),o=Ho(n,r);return t&&o>0||o>=r.width*r.height})):[]}),[]),m=t.useCallback(((e,t,n=!0)=>{const[o]=g(e);if(!o)return!1;const r=Ho(o,t);return n&&r>0||r>=o.width*o.height}),[]);return t.useMemo((()=>({...e,getNodes:r,getNode:i,getEdges:a,getEdge:s,setNodes:l,setEdges:c,addNodes:u,addEdges:d,toObject:h,deleteElements:f,getIntersectingNodes:p,isNodeIntersecting:m})),[e,r,i,a,s,l,c,u,d,h,f,p,m])}const ki={actInsideInputWithModifier:!1};const Pi={position:"absolute",width:"100%",height:"100%",top:0,left:0},Ai=e=>({x:e.x,y:e.y,zoom:e.k}),Oi=(e,t)=>e.target.closest(`.${t}`),Ii=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Di=e=>{const t=e.ctrlKey&&qo()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},Ri=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),zi=({onMove:e,onMoveStart:n,onMoveEnd:r,onPaneContextMenu:i,zoomOnScroll:a=!0,zoomOnPinch:s=!0,panOnScroll:l=!1,panOnScrollSpeed:c=.5,panOnScrollMode:u=tr.Free,zoomOnDoubleClick:d=!0,elementsSelectable:h,panOnDrag:f=!0,defaultViewport:g,translateExtent:p,minZoom:m,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:b=!0,children:w,noWheelClassName:x,noPanClassName:S})=>{const E=t.useRef(),_=No(),C=t.useRef(!1),M=t.useRef(!1),N=t.useRef(null),k=t.useRef({x:0,y:0,zoom:0}),{d3Zoom:P,d3Selection:A,d3ZoomHandler:O,userSelectionActive:I}=Mo(Ri,o),D=gi(v),R=t.useRef(0),z=t.useRef(!1),$=t.useRef();return function(e){const n=No();t.useEffect((()=>{let t;const o=()=>{if(!e.current)return;const t=Io(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",vo()),n.setState({width:t.width||500,height:t.height||500})};return o(),window.addEventListener("resize",o),e.current&&(t=new ResizeObserver((()=>o())),t.observe(e.current)),()=>{window.removeEventListener("resize",o),t&&e.current&&t.unobserve(e.current)}}),[])}(N),t.useEffect((()=>{if(N.current){const e=N.current.getBoundingClientRect(),t=Qn().scaleExtent([m,y]).translateExtent(p),n=_e(N.current).call(t),o=Vn.translate(g.x,g.y).scale(Do(g.zoom,m,y)),r=[[0,0],[e.width,e.height]],i=t.constrain()(o,r,p);t.transform(n,i),t.wheelDelta(Di),_.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:N.current.closest(".react-flow")})}}),[]),t.useEffect((()=>{A&&P&&(!l||D||I?void 0!==O&&A.on("wheel.zoom",(function(e,t){if(!b&&"wheel"===e.type&&!e.ctrlKey||Oi(e,x))return null;e.preventDefault(),O.call(this,e,t)}),{passive:!1}):A.on("wheel.zoom",(t=>{if(Oi(t,x))return!1;t.preventDefault(),t.stopImmediatePropagation();const o=A.property("__zoom").k||1;if(t.ctrlKey&&s){const e=Ce(t),n=Di(t),r=o*Math.pow(2,n);return void P.scaleTo(A,r,e,t)}const i=1===t.deltaMode?20:1;let a=u===tr.Vertical?0:t.deltaX*i,l=u===tr.Horizontal?0:t.deltaY*i;!qo()&&t.shiftKey&&u!==tr.Vertical&&(a=t.deltaY*i,l=0),P.translateBy(A,-a/o*c,-l/o*c,{internal:!0});const d=Ai(A.property("__zoom")),{onViewportChangeStart:h,onViewportChange:f,onViewportChangeEnd:g}=_.getState();clearTimeout($.current),z.current||(z.current=!0,n?.(t,d),h?.(d)),z.current&&(e?.(t,d),f?.(d),$.current=setTimeout((()=>{r?.(t,d),g?.(d),z.current=!1}),150))}),{passive:!1}))}),[I,l,u,A,P,O,D,s,b,x,n,e,r]),t.useEffect((()=>{P&&P.on("start",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;R.current=e.sourceEvent?.button;const{onViewportChangeStart:t}=_.getState(),o=Ai(e.transform);C.current=!0,k.current=o,"mousedown"===e.sourceEvent?.type&&_.setState({paneDragging:!0}),t?.(o),n?.(e.sourceEvent,o)}))}),[P,n]),t.useEffect((()=>{P&&(I&&!C.current?P.on("zoom",null):I||P.on("zoom",(t=>{const{onViewportChange:n}=_.getState();if(_.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),M.current=!(!i||!Ii(f,R.current??0)),(e||n)&&!t.sourceEvent?.internal){const o=Ai(t.transform);n?.(o),e?.(t.sourceEvent,o)}})))}),[I,P,e,f,i]),t.useEffect((()=>{P&&P.on("end",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;const{onViewportChangeEnd:t}=_.getState();if(C.current=!1,_.setState({paneDragging:!1}),i&&Ii(f,R.current??0)&&!M.current&&i(e.sourceEvent),M.current=!1,(r||t)&&(n=k.current,o=e.transform,n.x!==o.x||n.y!==o.y||n.zoom!==o.k)){const n=Ai(e.transform);k.current=n,clearTimeout(E.current),E.current=setTimeout((()=>{t?.(n),r?.(e.sourceEvent,n)}),l?150:0)}var n,o}))}),[P,l,f,r,i]),t.useEffect((()=>{P&&P.filter((e=>{const t=D||a,n=s&&e.ctrlKey;if((!0===f||Array.isArray(f)&&f.includes(1))&&1===e.button&&"mousedown"===e.type&&(Oi(e,"react-flow__node")||Oi(e,"react-flow__edge")))return!0;if(!(f||t||l||d||s))return!1;if(I)return!1;if(!d&&"dblclick"===e.type)return!1;if(Oi(e,x)&&"wheel"===e.type)return!1;if(Oi(e,S)&&("wheel"!==e.type||l&&"wheel"===e.type&&!D))return!1;if(!s&&e.ctrlKey&&"wheel"===e.type)return!1;if(!t&&!l&&!n&&"wheel"===e.type)return!1;if(!f&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(f)&&!f.includes(e.button)&&"mousedown"===e.type)return!1;const o=Array.isArray(f)&&f.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o}))}),[I,P,a,s,l,d,f,h,D]),t.createElement("div",{className:"react-flow__renderer",ref:N,style:Pi},w)},$i=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Ti(){const{userSelectionActive:e,userSelectionRect:n}=Mo($i,o);return e&&n?t.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}function Bi(e,t){const n=t.parentNode||t.parentId,o=e.find((e=>e.id===n));if(o){const e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style}||{},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){const e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){const e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function Li(e,t){return function(e,t){if(e.some((e=>"reset"===e.type)))return e.filter((e=>"reset"===e.type)).map((e=>e.item));const n=e.filter((e=>"add"===e.type)).map((e=>e.item));return t.reduce(((t,n)=>{const o=e.filter((e=>e.id===n.id));if(0===o.length)return t.push(n),t;const r={...n};for(const e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&Bi(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&Bi(t,r);break;case"remove":return t}return t.push(r),t}),n)}(e,t)}const Yi=(e,t)=>({id:e,type:"select",selected:t});function Xi(e,t){return e.reduce(((e,n)=>{const o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(Yi(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(Yi(n.id,!1))),e}),[])}const Hi=(e,t)=>n=>{n.target===t.current&&e?.(n)},Vi=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),Ki=t.memo((({isSelecting:e,selectionMode:r=nr.Full,panOnDrag:i,onSelectionStart:a,onSelectionEnd:s,onPaneClick:l,onPaneContextMenu:c,onPaneScroll:u,onPaneMouseEnter:d,onPaneMouseMove:h,onPaneMouseLeave:f,children:g})=>{const p=t.useRef(null),m=No(),y=t.useRef(0),v=t.useRef(0),b=t.useRef(),{userSelectionActive:w,elementsSelectable:x,dragging:S}=Mo(Vi,o),E=()=>{m.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,v.current=0},_=e=>{l?.(e),m.getState().resetSelectedElements(),m.setState({nodesSelectionActive:!1})},C=u?e=>u(e):void 0,M=x&&(e||w);return t.createElement("div",{className:n(["react-flow__pane",{dragging:S,selection:e}]),onClick:M?void 0:Hi(_,p),onContextMenu:Hi((e=>{Array.isArray(i)&&i?.includes(2)?e.preventDefault():c?.(e)}),p),onWheel:Hi(C,p),onMouseEnter:M?void 0:d,onMouseDown:M?t=>{const{resetSelectedElements:n,domNode:o}=m.getState();if(b.current=o?.getBoundingClientRect(),!x||!e||0!==t.button||t.target!==p.current||!b.current)return;const{x:r,y:i}=jo(t,b.current);n(),m.setState({userSelectionRect:{width:0,height:0,startX:r,startY:i,x:r,y:i}}),a?.(t)}:void 0,onMouseMove:M?t=>{const{userSelectionRect:n,nodeInternals:o,edges:i,transform:a,onNodesChange:s,onEdgesChange:l,nodeOrigin:c,getNodes:u}=m.getState();if(!e||!b.current||!n)return;m.setState({userSelectionActive:!0,nodesSelectionActive:!1});const d=jo(t,b.current),h=n.startX??0,f=n.startY??0,g={...n,x:d.x<h?d.x:h,y:d.y<f?d.y:f,width:Math.abs(d.x-h),height:Math.abs(d.y-f)},p=u(),w=kr(o,g,a,r===nr.Partial,!0,c),x=Pr(w,i).map((e=>e.id)),S=w.map((e=>e.id));if(y.current!==S.length){y.current=S.length;const e=Xi(p,S);e.length&&s?.(e)}if(v.current!==x.length){v.current=x.length;const e=Xi(i,x);e.length&&l?.(e)}m.setState({userSelectionRect:g})}:h,onMouseUp:M?e=>{if(0!==e.button)return;const{userSelectionRect:t}=m.getState();!w&&t&&e.target===p.current&&_?.(e),m.setState({nodesSelectionActive:y.current>0}),E(),s?.(e)}:void 0,onMouseLeave:M?e=>{w&&(m.setState({nodesSelectionActive:y.current>0}),s?.(e)),E()}:f,ref:p,style:Pi},g,t.createElement(Ti,null))}));function Fi(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const o=t.get(n);return!!o&&(!!o.selected||Fi(o,t))}function Zi(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)return!1;o=o.parentElement}while(o);return!1}function Wi(e,t,n,o){return Array.from(e.values()).filter((n=>(n.selected||n.id===o)&&(!n.parentNode||n.parentId||!Fi(n,e))&&(n.draggable||t&&void 0===n.draggable))).map((e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:n.x-(e.positionAbsolute?.x??0),y:n.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})))}function ji(e,t,n,o,r=[0,0],i){const a=function(e,t){return t&&"parent"!==t?[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]:t}(e,e.extent||o);let s=a;const l=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&l&&"parent"!==e.extent){const t=n.get(l),{x:o,y:i}=Mr(t,r).positionAbsolute;s=[[e.extent[0][0]+o,e.extent[0][1]+i],[e.extent[1][0]+o,e.extent[1][1]+i]]}}else if(l&&e.width&&e.height){const t=n.get(l),{x:o,y:i}=Mr(t,r).positionAbsolute;s=t&&Vo(o)&&Vo(i)&&Vo(t.width)&&Vo(t.height)?[[o+e.width*r[0],i+e.height*r[1]],[o+t.width-e.width+e.width*r[0],i+t.height-e.height+e.height*r[1]]]:s}else i?.("005",bo()),s=a;let c={x:0,y:0};if(l){const e=n.get(l);c=Mr(e,r).positionAbsolute}const u=s&&"parent"!==s?Ro(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function qi({nodeId:e,dragItems:t,nodeInternals:n}){const o=t.map((e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute})));return[e?o.find((t=>t.id===e)):o[0],o]}Ki.displayName="Pane";const Ui=(e,t,n,o)=>{const r=t.querySelectorAll(e);if(!r||!r.length)return null;const i=Array.from(r),a=t.getBoundingClientRect(),s=a.width*o[0],l=a.height*o[1];return i.map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-s)/n,y:(t.top-a.top-l)/n,...Io(e)}}))};function Gi(e,t,n){return void 0===n?n:o=>{const r=t().nodeInternals.get(e);r&&n(o,{...r})}}function Qi({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",_o(e))}function Ji(e){return(t,n,o)=>e?.(t,o)}function ea({nodeRef:e,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:a,isSelectable:s,selectNodesOnDrag:l}){const c=No(),[u,d]=t.useState(!1),h=t.useRef([]),f=t.useRef({x:null,y:null}),g=t.useRef(0),p=t.useRef(null),m=t.useRef({x:0,y:0}),y=t.useRef(null),v=t.useRef(!1),b=t.useRef(!1),w=t.useRef(!1),x=function(){const e=No(),n=t.useCallback((({sourceEvent:t})=>{const{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,s={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(s.x/o[0]):s.x,ySnapped:r?o[1]*Math.round(s.y/o[1]):s.y,...s}}),[]);return n}();return t.useEffect((()=>{if(e?.current){const t=_e(e.current),u=({x:e,y:t})=>{const{nodeInternals:n,onNodeDrag:o,onSelectionDrag:r,updateNodePositions:i,nodeExtent:s,snapGrid:l,snapToGrid:u,nodeOrigin:g,onError:p}=c.getState();f.current={x:e,y:t};let m=!1,v={x:0,y:0,x2:0,y2:0};if(h.current.length>1&&s){const e=Nr(h.current,g);v=Lo(e)}if(h.current=h.current.map((o=>{const r={x:e-o.distance.x,y:t-o.distance.y};u&&(r.x=l[0]*Math.round(r.x/l[0]),r.y=l[1]*Math.round(r.y/l[1]));const i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];h.current.length>1&&s&&!o.extent&&(i[0][0]=o.positionAbsolute.x-v.x+s[0][0],i[1][0]=o.positionAbsolute.x+(o.width??0)-v.x2+s[1][0],i[0][1]=o.positionAbsolute.y-v.y+s[0][1],i[1][1]=o.positionAbsolute.y+(o.height??0)-v.y2+s[1][1]);const a=ji(o,r,n,i,g,p);return m=m||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o})),!m)return;i(h.current,!0,!0),d(!0);const b=a?o:Ji(r);if(b&&y.current){const[e,t]=qi({nodeId:a,dragItems:h.current,nodeInternals:n});b(y.current,e,t)}},S=()=>{if(!p.current)return;const[e,t]=$o(m.current,p.current);if(0!==e||0!==t){const{transform:n,panBy:o}=c.getState();f.current.x=(f.current.x??0)-e/n[2],f.current.y=(f.current.y??0)-t/n[2],o({x:e,y:t})&&u(f.current)}g.current=requestAnimationFrame(S)},E=t=>{const{nodeInternals:n,multiSelectionActive:o,nodesDraggable:r,unselectNodesAndEdges:i,onNodeDragStart:u,onSelectionDragStart:d}=c.getState();b.current=!0;const g=a?u:Ji(d);l&&s||o||!a||n.get(a)?.selected||i(),a&&s&&l&&Qi({id:a,store:c,nodeRef:e});const p=x(t);if(f.current=p,h.current=Wi(n,r,p,a),g&&h.current){const[e,o]=qi({nodeId:a,dragItems:h.current,nodeInternals:n});g(t.sourceEvent,e,o)}};if(!n){const n=function(){var e,t,n,o,r=Re,a=ze,s=$e,l=Te,c={},u=i("start","drag","end"),d=0,h=0;function f(e){e.on("mousedown.drag",g).filter(l).on("touchstart.drag",y).on("touchmove.drag",v,Me).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(i,s){if(!o&&r.call(this,i,s)){var l=w(this,a.call(this,i,s),i,s,"mouse");l&&(_e(i.view).on("mousemove.drag",p,Ne).on("mouseup.drag",m,Ne),Ae(i.view),ke(i),n=!1,e=i.clientX,t=i.clientY,l("start",i))}}function p(o){if(Pe(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>h}c.mouse("drag",o)}function m(e){_e(e.view).on("mousemove.drag mouseup.drag",null),Oe(e.view,n),Pe(e),c.mouse("end",e)}function y(e,t){if(r.call(this,e,t)){var n,o,i=e.changedTouches,s=a.call(this,e,t),l=i.length;for(n=0;n<l;++n)(o=w(this,s,e,t,i[n].identifier,i[n]))&&(ke(e),o("start",e,i[n]))}}function v(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=c[o[t].identifier])&&(Pe(e),n("drag",e,o[t]))}function b(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=c[r[t].identifier])&&(ke(e),n("end",e,r[t]))}function w(e,t,n,o,r,i){var a,l,h,g=u.copy(),p=Ce(i||n,t);if(null!=(h=s.call(e,new De("beforestart",{sourceEvent:n,target:f,identifier:r,active:d,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return a=h.x-p[0]||0,l=h.y-p[1]||0,function n(i,s,u){var m,y=p;switch(i){case"start":c[r]=n,m=d++;break;case"end":delete c[r],--d;case"drag":p=Ce(u||s,t),m=d}g.call(i,e,new De(i,{sourceEvent:s,subject:h,target:f,identifier:r,active:m,x:p[0]+a,y:p[1]+l,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return f.filter=function(e){return arguments.length?(r="function"==typeof e?e:Ie(!!e),f):r},f.container=function(e){return arguments.length?(a="function"==typeof e?e:Ie(e),f):a},f.subject=function(e){return arguments.length?(s="function"==typeof e?e:Ie(e),f):s},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:Ie(!!e),f):l},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,f):Math.sqrt(h)},f}().on("start",(e=>{const{domNode:t,nodeDragThreshold:n}=c.getState();0===n&&E(e),w.current=!1;const o=x(e);f.current=o,p.current=t?.getBoundingClientRect()||null,m.current=jo(e.sourceEvent,p.current)})).on("drag",(e=>{const t=x(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=c.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(w.current=!0),!w.current){if(!v.current&&b.current&&n&&(v.current=!0,S()),!b.current){const n=t.xSnapped-(f?.current?.x??0),r=t.ySnapped-(f?.current?.y??0);Math.sqrt(n*n+r*r)>o&&E(e)}(f.current.x!==t.xSnapped||f.current.y!==t.ySnapped)&&h.current&&b.current&&(y.current=e.sourceEvent,m.current=jo(e.sourceEvent,p.current),u(t))}})).on("end",(e=>{if(b.current&&!w.current&&(d(!1),v.current=!1,b.current=!1,cancelAnimationFrame(g.current),h.current)){const{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:r}=c.getState(),i=a?o:Ji(r);if(t(h.current,!1,!1),i){const[t,o]=qi({nodeId:a,dragItems:h.current,nodeInternals:n});i(e.sourceEvent,t,o)}}})).filter((t=>{const n=t.target;return!t.button&&(!o||!Zi(n,`.${o}`,e))&&(!r||Zi(n,r,e))}));return t.call(n),()=>{t.on(".drag",null)}}t.on(".drag",null)}}),[e,n,o,r,s,c,a,l,x]),u}function ta(){const e=No();return t.useCallback((t=>{const{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:i,snapToGrid:a,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=i().filter((e=>e.selected&&(e.draggable||c&&void 0===e.draggable))),d=a?s[0]:5,h=a?s[1]:5,f=t.isShiftPressed?4:1,g=t.x*d*f,p=t.y*h*f;r(u.map((e=>{if(e.positionAbsolute){const t={x:e.positionAbsolute.x+g,y:e.positionAbsolute.y+p};a&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));const{positionAbsolute:r,position:i}=ji(e,t,n,o,void 0,l);e.position=i,e.positionAbsolute=r}return e})),!0,!1)}),[])}const na={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var oa=e=>{const o=({id:o,type:r,data:i,xPos:a,yPos:s,xPosOrigin:l,yPosOrigin:c,selected:u,onClick:d,onMouseEnter:h,onMouseMove:f,onMouseLeave:g,onContextMenu:p,onDoubleClick:m,style:y,className:v,isDraggable:b,isSelectable:w,isConnectable:x,isFocusable:S,selectNodesOnDrag:E,sourcePosition:_,targetPosition:C,hidden:M,resizeObserver:N,dragHandle:k,zIndex:P,isParent:A,noDragClassName:O,noPanClassName:I,initialized:D,disableKeyboardA11y:R,ariaLabel:z,rfId:$,hasHandleBounds:T})=>{const B=No(),L=t.useRef(null),Y=t.useRef(null),X=t.useRef(_),H=t.useRef(C),V=t.useRef(r),K=w||b||d||h||f||g,F=ta(),Z=Gi(o,B.getState,h),W=Gi(o,B.getState,f),j=Gi(o,B.getState,g),q=Gi(o,B.getState,p),U=Gi(o,B.getState,m);t.useEffect((()=>()=>{Y.current&&(N?.unobserve(Y.current),Y.current=null)}),[]),t.useEffect((()=>{if(L.current&&!M){const e=L.current;D&&T&&Y.current===e||(Y.current&&N?.unobserve(Y.current),N?.observe(e),Y.current=e)}}),[M,D,T]),t.useEffect((()=>{const e=V.current!==r,t=X.current!==_,n=H.current!==C;L.current&&(e||t||n)&&(e&&(V.current=r),t&&(X.current=_),n&&(H.current=C),B.getState().updateNodeDimensions([{id:o,nodeElement:L.current,forceUpdate:!0}]))}),[o,r,_,C]);const G=ea({nodeRef:L,disabled:M||!b,noDragClassName:O,handleSelector:k,nodeId:o,isSelectable:w,selectNodesOnDrag:E});return M?null:t.createElement("div",{className:n(["react-flow__node",`react-flow__node-${r}`,{[I]:b},v,{selected:u,selectable:w,parent:A,dragging:G}]),ref:L,style:{zIndex:P,transform:`translate(${l}px,${c}px)`,pointerEvents:K?"all":"none",visibility:D?"visible":"hidden",...y},"data-id":o,"data-testid":`rf__node-${o}`,onMouseEnter:Z,onMouseMove:W,onMouseLeave:j,onContextMenu:q,onClick:e=>{const{nodeDragThreshold:t}=B.getState();if(w&&(!E||!b||t>0)&&Qi({id:o,store:B,nodeRef:L}),d){const t=B.getState().nodeInternals.get(o);t&&d(e,{...t})}},onDoubleClick:U,onKeyDown:S?e=>{if(!Zo(e)&&!R)if(Fo.includes(e.key)&&w){const t="Escape"===e.key;Qi({id:o,store:B,unselect:t,nodeRef:L})}else b&&u&&Object.prototype.hasOwnProperty.call(na,e.key)&&(B.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~a}, y: ${~~s}`}),F({x:na[e.key].x,y:na[e.key].y,isShiftPressed:e.shiftKey}))}:void 0,tabIndex:S?0:void 0,role:S?"button":void 0,"aria-describedby":R?void 0:`${ci}-${$}`,"aria-label":z},t.createElement(xr,{value:o},t.createElement(e,{id:o,data:i,type:r,xPos:a,yPos:s,selected:u,isConnectable:x,sourcePosition:_,targetPosition:C,dragging:G,dragHandle:k,zIndex:P})))};return o.displayName="NodeWrapper",t.memo(o)};const ra=e=>{const t=e.getNodes().filter((e=>e.selected));return{...Nr(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};var ia=t.memo((function({onSelectionContextMenu:e,noPanClassName:r,disableKeyboardA11y:i}){const a=No(),{width:s,height:l,x:c,y:u,transformString:d,userSelectionActive:h}=Mo(ra,o),f=ta(),g=t.useRef(null);if(t.useEffect((()=>{i||g.current?.focus({preventScroll:!0})}),[i]),ea({nodeRef:g}),h||!s||!l)return null;const p=e?t=>{const n=a.getState().getNodes().filter((e=>e.selected));e(t,n)}:void 0;return t.createElement("div",{className:n(["react-flow__nodesselection","react-flow__container",r]),style:{transform:d}},t.createElement("div",{ref:g,className:"react-flow__nodesselection-rect",onContextMenu:p,tabIndex:i?void 0:-1,onKeyDown:i?void 0:e=>{Object.prototype.hasOwnProperty.call(na,e.key)&&f({x:na[e.key].x,y:na[e.key].y,isShiftPressed:e.shiftKey})},style:{width:s,height:l,top:u,left:c}}))}));const aa=e=>e.nodesSelectionActive,sa=({children:e,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,deleteKeyCode:l,onMove:c,onMoveStart:u,onMoveEnd:d,selectionKeyCode:h,selectionOnDrag:f,selectionMode:g,onSelectionStart:p,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:b,elementsSelectable:w,zoomOnScroll:x,zoomOnPinch:S,panOnScroll:E,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:N,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,preventScrolling:I,onSelectionContextMenu:D,noWheelClassName:R,noPanClassName:z,disableKeyboardA11y:$})=>{const T=Mo(aa),B=gi(h),L=gi(v),Y=L||N,X=L||E,H=B||f&&!0!==Y;return(({deleteKeyCode:e,multiSelectionKeyCode:n})=>{const o=No(),{deleteElements:r}=Ni(),i=gi(e,ki),a=gi(n);t.useEffect((()=>{if(i){const{edges:e,getNodes:t}=o.getState(),n=t().filter((e=>e.selected)),i=e.filter((e=>e.selected));r({nodes:n,edges:i}),o.setState({nodesSelectionActive:!1})}}),[i]),t.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])})({deleteKeyCode:l,multiSelectionKeyCode:y}),t.createElement(zi,{onMove:c,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:a,elementsSelectable:w,zoomOnScroll:x,zoomOnPinch:S,panOnScroll:X,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:!B&&Y,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,zoomActivationKeyCode:b,preventScrolling:I,noWheelClassName:R,noPanClassName:z},t.createElement(Ki,{onSelectionStart:p,onSelectionEnd:m,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,panOnDrag:Y,isSelecting:!!H,selectionMode:g},e,T&&t.createElement(ia,{onSelectionContextMenu:D,noPanClassName:z,disableKeyboardA11y:$})))};sa.displayName="FlowRenderer";var la=t.memo(sa);function ca(e){return{...{input:oa(e.input||Wr),default:oa(e.default||Fr),output:oa(e.output||qr),group:oa(e.group||Ur)},...Object.keys(e).filter((e=>!["input","default","output","group"].includes(e))).reduce(((t,n)=>(t[n]=oa(e[n]||Fr),t)),{})}}const ua=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),da=e=>{const{nodesDraggable:n,nodesConnectable:r,nodesFocusable:i,elementsSelectable:a,updateNodeDimensions:s,onError:l}=Mo(ua,o),c=(u=e.onlyRenderVisibleElements,Mo(t.useCallback((e=>u?kr(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes()),[u])));var u;const d=t.useRef(),h=t.useMemo((()=>{if("undefined"==typeof ResizeObserver)return null;const e=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));s(t)}));return d.current=e,e}),[]);return t.useEffect((()=>()=>{d?.current?.disconnect()}),[]),t.createElement("div",{className:"react-flow__nodes",style:Pi},c.map((o=>{let s=o.type||"default";e.nodeTypes[s]||(l?.("003",yo(s)),s="default");const c=e.nodeTypes[s]||e.nodeTypes.default,u=!!(o.draggable||n&&void 0===o.draggable),d=!!(o.selectable||a&&void 0===o.selectable),f=!!(o.connectable||r&&void 0===o.connectable),g=!!(o.focusable||i&&void 0===o.focusable),p=e.nodeExtent?Ro(o.positionAbsolute,e.nodeExtent):o.positionAbsolute,m=p?.x??0,y=p?.y??0,v=(({x:e,y:t,width:n,height:o,origin:r})=>n&&o?r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]}:{x:e,y:t})({x:m,y:y,width:o.width??0,height:o.height??0,origin:e.nodeOrigin});return t.createElement(c,{key:o.id,id:o.id,className:o.className,style:o.style,type:s,data:o.data,sourcePosition:o.sourcePosition||ir.Bottom,targetPosition:o.targetPosition||ir.Top,hidden:o.hidden,xPos:m,yPos:y,xPosOrigin:v.x,yPosOrigin:v.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!o.selected,isDraggable:u,isSelectable:d,isConnectable:f,isFocusable:g,resizeObserver:h,dragHandle:o.dragHandle,zIndex:o[Ko]?.z??0,isParent:!!o[Ko]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!o.width&&!!o.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:o.ariaLabel,hasHandleBounds:!!o[Ko]?.handleBounds})})))};da.displayName="NodeRenderer";var ha=t.memo(da);const fa=(e,t,n)=>n===ir.Left?e-t:n===ir.Right?e+t:e,ga=(e,t,n)=>n===ir.Top?e-t:n===ir.Bottom?e+t:e,pa="react-flow__edgeupdater",ma=({position:e,centerX:o,centerY:r,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c})=>t.createElement("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:n([pa,`${pa}-${c}`]),cx:fa(o,i,e),cy:ga(r,i,e),r:i,stroke:"transparent",fill:"transparent"}),ya=()=>!0;var va=e=>{const o=({id:o,className:r,type:i,data:a,onClick:s,onEdgeDoubleClick:l,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:y,source:v,target:b,sourceX:w,sourceY:x,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,elementsSelectable:M,hidden:N,sourceHandleId:k,targetHandleId:P,onContextMenu:A,onMouseEnter:O,onMouseMove:I,onMouseLeave:D,reconnectRadius:R,onReconnect:z,onReconnectStart:$,onReconnectEnd:T,markerEnd:B,markerStart:L,rfId:Y,ariaLabel:X,isFocusable:H,isReconnectable:V,pathOptions:K,interactionWidth:F,disableKeyboardA11y:Z})=>{const W=t.useRef(null),[j,q]=t.useState(!1),[U,G]=t.useState(!1),Q=No(),J=t.useMemo((()=>`url('#${Er(L,Y)}')`),[L,Y]),ee=t.useMemo((()=>`url('#${Er(B,Y)}')`),[B,Y]);if(N)return null;const te=Go(o,Q.getState,l),ne=Go(o,Q.getState,A),oe=Go(o,Q.getState,O),re=Go(o,Q.getState,I),ie=Go(o,Q.getState,D),ae=(e,t)=>{if(0!==e.button)return;const{edges:n,isValidConnection:r}=Q.getState(),i=t?b:v,a=(t?P:k)||null,s=t?"target":"source",l=r||ya,c=t,u=n.find((e=>e.id===o));G(!0),$?.(e,u,s);Lr({event:e,handleId:a,nodeId:i,onConnect:e=>z?.(u,e),isTarget:c,getState:Q.getState,setState:Q.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{G(!1),T?.(e,u,s)}})},se=()=>q(!0),le=()=>q(!1),ce=!M&&!s;return t.createElement("g",{className:n(["react-flow__edge",`react-flow__edge-${i}`,r,{selected:c,animated:u,inactive:ce,updating:j}]),onClick:e=>{const{edges:t,addSelectedEdges:n,unselectNodesAndEdges:r,multiSelectionActive:i}=Q.getState(),a=t.find((e=>e.id===o));a&&(M&&(Q.setState({nodesSelectionActive:!1}),a.selected&&i?(r({nodes:[],edges:[a]}),W.current?.blur()):n([o])),s&&s(e,a))},onDoubleClick:te,onContextMenu:ne,onMouseEnter:oe,onMouseMove:re,onMouseLeave:ie,onKeyDown:H?e=>{if(!Z&&Fo.includes(e.key)&&M){const{unselectNodesAndEdges:t,addSelectedEdges:n,edges:r}=Q.getState();"Escape"===e.key?(W.current?.blur(),t({edges:[r.find((e=>e.id===o))]})):n([o])}}:void 0,tabIndex:H?0:void 0,role:H?"button":"img","data-testid":`rf__edge-${o}`,"aria-label":null===X?void 0:X||`Edge from ${v} to ${b}`,"aria-describedby":H?`${ui}-${Y}`:void 0,ref:W},!U&&t.createElement(e,{id:o,source:v,target:b,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,data:a,style:y,sourceX:w,sourceY:x,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,sourceHandleId:k,targetHandleId:P,markerStart:J,markerEnd:ee,pathOptions:K,interactionWidth:F}),V&&t.createElement(t.Fragment,null,("source"===V||!0===V)&&t.createElement(ma,{position:_,centerX:w,centerY:x,radius:R,onMouseDown:e=>ae(e,!0),onMouseEnter:se,onMouseOut:le,type:"source"}),("target"===V||!0===V)&&t.createElement(ma,{position:C,centerX:S,centerY:E,radius:R,onMouseDown:e=>ae(e,!1),onMouseEnter:se,onMouseOut:le,type:"target"})))};return o.displayName="EdgeWrapper",t.memo(o)};function ba(e){return{...{default:va(e.default||br),straight:va(e.bezier||pr),step:va(e.step||gr),smoothstep:va(e.step||fr),simplebezier:va(e.simplebezier||lr)},...Object.keys(e).filter((e=>!["default","bezier"].includes(e))).reduce(((t,n)=>(t[n]=va(e[n]||br),t)),{})}}function wa(e,t,n=null){const o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,i=n?.width||t.width,a=n?.height||t.height;switch(e){case ir.Top:return{x:o+i/2,y:r};case ir.Right:return{x:o+i,y:r+a/2};case ir.Bottom:return{x:o+i/2,y:r+a};case ir.Left:return{x:o,y:r+a/2}}}function xa(e,t){return e?1!==e.length&&t?t&&e.find((e=>e.id===t))||null:e[0]:null}function Sa(e){const t=e?.[Ko]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}const Ea=[{level:0,isMaxLevel:!0,edges:[]}];function _a(e,n,o){return function(e,t,n=!1){let o=-1;const r=e.reduce(((e,r)=>{const i=Vo(r.zIndex);let a=i?r.zIndex:0;if(n){const e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,s=Math.max(n?.[Ko]?.z||0,e?.[Ko]?.z||0,1e3);a=(i?r.zIndex:0)+(o?s:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e}),{}),i=Object.entries(r).map((([e,t])=>{const n=+e;return{edges:t,level:n,isMaxLevel:n===o}}));return 0===i.length?Ea:i}(Mo(t.useCallback((t=>e?t.edges.filter((e=>{const o=n.get(e.source),r=n.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:a,height:s,transform:l}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=Lo({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:a/l[2],height:s/l[2]}),d=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),h=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(d*h)>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:t.width,height:t.height,transform:t.transform})})):t.edges),[e,n])),n,o)}const Ca={[rr.Arrow]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[rr.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const Ma=({id:e,type:n,color:o,width:r=12.5,height:i=12.5,markerUnits:a="strokeWidth",strokeWidth:s,orient:l="auto-start-reverse"})=>{const c=function(e){const n=No();return t.useMemo((()=>Object.prototype.hasOwnProperty.call(Ca,e)?Ca[e]:(n.getState().onError?.("009",wo(e)),null)),[e])}(n);return c?t.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${i}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:l,refX:"0",refY:"0"},t.createElement(c,{color:o,strokeWidth:s})):null},Na=({defaultColor:e,rfId:n})=>{const o=Mo(t.useCallback((({defaultColor:e,rfId:t})=>n=>{const o=[];return n.edges.reduce(((n,r)=>([r.markerStart,r.markerEnd].forEach((r=>{if(r&&"object"==typeof r){const i=Er(r,t);o.includes(i)||(n.push({id:i,color:r.color||e,...r}),o.push(i))}})),n)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))})({defaultColor:e,rfId:n}),[e,n]),((e,t)=>!(e.length!==t.length||e.some(((e,n)=>e.id!==t[n].id)))));return t.createElement("defs",null,o.map((e=>t.createElement(Ma,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient}))))};Na.displayName="MarkerDefinitions";var ka=t.memo(Na);const Pa=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),Aa=({defaultMarkerColor:e,onlyRenderVisibleElements:r,elevateEdgesOnSelect:i,rfId:a,edgeTypes:s,noPanClassName:l,onEdgeContextMenu:c,onEdgeMouseEnter:u,onEdgeMouseMove:d,onEdgeMouseLeave:h,onEdgeClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,children:b,disableKeyboardA11y:w})=>{const{edgesFocusable:x,edgesUpdatable:S,elementsSelectable:E,width:_,height:C,connectionMode:M,nodeInternals:N,onError:k}=Mo(Pa,o),P=_a(r,N,i);return _?t.createElement(t.Fragment,null,P.map((({level:o,edges:r,isMaxLevel:i})=>t.createElement("svg",{key:o,style:{zIndex:o},width:_,height:C,className:"react-flow__edges react-flow__container"},i&&t.createElement(ka,{defaultColor:e,rfId:a}),t.createElement("g",null,r.map((e=>{const[o,r,i]=Sa(N.get(e.source)),[b,_,C]=Sa(N.get(e.target));if(!i||!C)return null;let P=e.type||"default";s[P]||(k?.("011",Eo(P)),P="default");const A=s[P]||s.default,O=M===er.Strict?_.target:(_.target??[]).concat(_.source??[]),I=xa(r.source,e.sourceHandle),D=xa(O,e.targetHandle),R=I?.position||ir.Bottom,z=D?.position||ir.Top,$=!!(e.focusable||x&&void 0===e.focusable),T=e.reconnectable||e.updatable,B=void 0!==p&&(T||S&&void 0===T);if(!I||!D)return k?.("008",xo(I,e)),null;const{sourceX:L,sourceY:Y,targetX:X,targetY:H}=((e,t,n,o,r,i)=>{const a=wa(n,e,t),s=wa(i,o,r);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}})(o,I,R,b,D,z);return t.createElement(A,{key:e.id,id:e.id,className:n([e.className,l]),type:P,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:L,sourceY:Y,targetX:X,targetY:H,sourcePosition:R,targetPosition:z,elementsSelectable:E,onContextMenu:c,onMouseEnter:u,onMouseMove:d,onMouseLeave:h,onClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,rfId:a,ariaLabel:e.ariaLabel,isFocusable:$,isReconnectable:B,pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:w})})))))),b):null};Aa.displayName="EdgeRenderer";var Oa=t.memo(Aa);const Ia=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function Da({children:e}){const n=Mo(Ia);return t.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:n}},e)}const Ra={[ir.Left]:ir.Right,[ir.Right]:ir.Left,[ir.Top]:ir.Bottom,[ir.Bottom]:ir.Top},za=({nodeId:e,handleType:n,style:r,type:i=or.Bezier,CustomComponent:a,connectionStatus:s})=>{const{fromNode:l,handleId:c,toX:u,toY:d,connectionMode:h}=Mo(t.useCallback((t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode})),[e]),o),f=l?.[Ko]?.handleBounds;let g=f?.[n];if(h===er.Loose&&(g=g||f?.["source"===n?"target":"source"]),!l||!g)return null;const p=c?g.find((e=>e.id===c)):g[0],m=p?p.x+p.width/2:(l.width??0)/2,y=p?p.y+p.height/2:l.height??0,v=(l.positionAbsolute?.x??0)+m,b=(l.positionAbsolute?.y??0)+y,w=p?.position,x=w?Ra[w]:null;if(!w||!x)return null;if(a)return t.createElement(a,{connectionLineType:i,connectionLineStyle:r,fromNode:l,fromHandle:p,fromX:v,fromY:b,toX:u,toY:d,fromPosition:w,toPosition:x,connectionStatus:s});let S="";const E={sourceX:v,sourceY:b,sourcePosition:w,targetX:u,targetY:d,targetPosition:x};return i===or.Bezier?[S]=vr(E):i===or.Step?[S]=hr({...E,borderRadius:0}):i===or.SmoothStep?[S]=hr(E):i===or.SimpleBezier?[S]=sr(E):S=`M${v},${b} ${u},${d}`,t.createElement("path",{d:S,fill:"none",className:"react-flow__connection-path",style:r})};za.displayName="ConnectionLine";const $a=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function Ta({containerStyle:e,style:r,type:i,component:a}){const{nodeId:s,handleType:l,nodesConnectable:c,width:u,height:d,connectionStatus:h}=Mo($a,o);return!!(s&&l&&u&&c)?t.createElement("svg",{style:e,width:u,height:d,className:"react-flow__edges react-flow__connectionline react-flow__container"},t.createElement("g",{className:n(["react-flow__connection",h])},t.createElement(za,{nodeId:s,handleType:l,style:r,type:i,CustomComponent:a,connectionStatus:h}))):null}function Ba(e,n){t.useRef(null),No();return t.useMemo((()=>n(e)),[e])}const La=({nodeTypes:e,edgeTypes:n,onMove:o,onMoveStart:r,onMoveEnd:i,onInit:a,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:b,connectionLineComponent:w,connectionLineContainerStyle:x,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,deleteKeyCode:k,onlyRenderVisibleElements:P,elementsSelectable:A,selectNodesOnDrag:O,defaultViewport:I,translateExtent:D,minZoom:R,maxZoom:z,preventScrolling:$,defaultMarkerColor:T,zoomOnScroll:B,zoomOnPinch:L,panOnScroll:Y,panOnScrollSpeed:X,panOnScrollMode:H,zoomOnDoubleClick:V,panOnDrag:K,onPaneClick:F,onPaneMouseEnter:Z,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneScroll:q,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,elevateEdgesOnSelect:le,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})=>{const fe=Ba(e,ca),ge=Ba(n,ba);return function(e){const n=Ni(),o=t.useRef(!1);t.useEffect((()=>{!o.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),o.current=!0)}),[e,n.viewportInitialized])}(a),t.createElement(la,{onPaneClick:F,onPaneMouseEnter:Z,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneContextMenu:U,onPaneScroll:q,deleteKeyCode:k,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,elementsSelectable:A,onMove:o,onMoveStart:r,onMoveEnd:i,zoomOnScroll:B,zoomOnPinch:L,zoomOnDoubleClick:V,panOnScroll:Y,panOnScrollSpeed:X,panOnScrollMode:H,panOnDrag:K,defaultViewport:I,translateExtent:D,minZoom:R,maxZoom:z,onSelectionContextMenu:p,preventScrolling:$,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,disableKeyboardA11y:ce},t.createElement(Da,null,t.createElement(Oa,{edgeTypes:ge,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:P,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,defaultMarkerColor:T,noPanClassName:se,elevateEdgesOnSelect:!!le,disableKeyboardA11y:ce,rfId:he},t.createElement(Ta,{style:b,type:v,component:w,containerStyle:x})),t.createElement("div",{className:"react-flow__edgelabel-renderer"}),t.createElement(ha,{nodeTypes:fe,onNodeClick:s,onNodeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,selectNodesOnDrag:O,onlyRenderVisibleElements:P,noPanClassName:se,noDragClassName:ie,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})))};La.displayName="GraphView";var Ya=t.memo(La);const Xa=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Ha={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Xa,nodeExtent:Xa,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:er.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:(e,t)=>{},isValidConnection:void 0},Va=()=>{return e=(e,t)=>({...Ha,setNodes:n=>{const{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:i}=t();e({nodeInternals:bi(n,o,r,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:o={}}=t();e({edges:n.map((e=>({...o,...e})))})},setDefaultNodesAndEdges:(n,o)=>{const r=void 0!==n,i=void 0!==o,a=r?bi(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?o:[],hasDefaultNodes:r,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:o,nodeInternals:r,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;const d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce(((e,t)=>{const n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[Ko]:{...n[Ko],handleBounds:void 0}});else if(n){const o=Io(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[Ko]:{...n[Ko],handleBounds:{source:Ui(".source",t.nodeElement,h,c),target:Ui(".target",t.nodeElement,h,c)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e}),[]);vi(r,c);const g=a||i&&!a&&wi(t,{initial:!0,...s});e({nodeInternals:new Map(r),fitViewOnInitDone:g}),f?.length>0&&o?.(f)},updateNodePositions:(e,n=!0,o=!1)=>{const{triggerNodeChanges:r}=t();r(e.map((e=>{const t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t})))},triggerNodeChanges:n=>{const{onNodesChange:o,nodeInternals:r,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:l}=t();if(n?.length){if(i){const t=bi(Li(n,s()),r,a,l);e({nodeInternals:t})}o?.(n)}},addSelectedNodes:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Yi(e,!0))):(a=Xi(i(),n),s=Xi(r,[])),Ei({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Yi(e,!0))):(a=Xi(r,n),s=Xi(i(),[])),Ei({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{const{edges:r,getNodes:i}=t(),a=o||r;Ei({changedNodes:(n||i()).map((e=>(e.selected=!1,Yi(e.id,!1)))),changedEdges:a.map((e=>Yi(e.id,!1))),get:t,set:e})},setMinZoom:n=>{const{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:o}=t();Ei({changedNodes:o().filter((e=>e.selected)).map((e=>Yi(e.id,!1))),changedEdges:n.filter((e=>e.selected)).map((e=>Yi(e.id,!1))),get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:o}=t();o.forEach((e=>{e.positionAbsolute=Ro(e.position,n)})),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{const{transform:n,width:o,height:r,d3Zoom:i,d3Selection:a,translateExtent:s}=t();if(!i||!a||!e.x&&!e.y)return!1;const l=Vn.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=[[0,0],[o,r]],u=i?.constrain()(l,c,s);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:Ha.connectionNodeId,connectionHandleId:Ha.connectionHandleId,connectionHandleType:Ha.connectionHandleType,connectionStatus:Ha.connectionStatus,connectionStartHandle:Ha.connectionStartHandle,connectionEndHandle:Ha.connectionEndHandle}),reset:()=>e({...Ha})}),t=Object.is,e?go(e,t):go;var e,t},Ka=({children:e})=>{const n=t.useRef(null);return n.current||(n.current=Va()),t.createElement(mo,{value:n.current},e)};Ka.displayName="ReactFlowProvider";const Fa=({children:e})=>t.useContext(po)?t.createElement(t.Fragment,null,e):t.createElement(Ka,null,e);Fa.displayName="ReactFlowWrapper";const Za={input:Wr,default:Fr,output:qr,group:Ur},Wa={default:br,straight:pr,step:gr,smoothstep:fr,simplebezier:lr},ja=[0,0],qa=[15,15],Ua={x:0,y:0,zoom:1},Ga={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},Qa=t.forwardRef((({nodes:e,edges:o,defaultNodes:r,defaultEdges:i,className:a,nodeTypes:s=Za,edgeTypes:l=Wa,onNodeClick:c,onEdgeClick:u,onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,onNodeDragStart:C,onNodeDrag:M,onNodeDragStop:N,onNodesDelete:k,onEdgesDelete:P,onSelectionChange:A,onSelectionDragStart:O,onSelectionDrag:I,onSelectionDragStop:D,onSelectionContextMenu:R,onSelectionStart:z,onSelectionEnd:$,connectionMode:T=er.Strict,connectionLineType:B=or.Bezier,connectionLineStyle:L,connectionLineComponent:Y,connectionLineContainerStyle:X,deleteKeyCode:H="Backspace",selectionKeyCode:V="Shift",selectionOnDrag:K=!1,selectionMode:F=nr.Full,panActivationKeyCode:Z="Space",multiSelectionKeyCode:W=(qo()?"Meta":"Control"),zoomActivationKeyCode:j=(qo()?"Meta":"Control"),snapToGrid:q=!1,snapGrid:U=qa,onlyRenderVisibleElements:G=!1,selectNodesOnDrag:Q=!0,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,nodeOrigin:ne=ja,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,defaultViewport:ae=Ua,minZoom:se=.5,maxZoom:le=2,translateExtent:ce=Xa,preventScrolling:ue=!0,nodeExtent:de,defaultMarkerColor:he="#b1b1b7",zoomOnScroll:fe=!0,zoomOnPinch:ge=!0,panOnScroll:pe=!1,panOnScrollSpeed:me=.5,panOnScrollMode:ye=tr.Free,zoomOnDoubleClick:ve=!0,panOnDrag:be=!0,onPaneClick:we,onPaneMouseEnter:xe,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,children:Me,onEdgeContextMenu:Ne,onEdgeDoubleClick:ke,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ae,onEdgeMouseLeave:Oe,onEdgeUpdate:Ie,onEdgeUpdateStart:De,onEdgeUpdateEnd:Re,onReconnect:ze,onReconnectStart:$e,onReconnectEnd:Te,reconnectRadius:Be=10,edgeUpdaterRadius:Le=10,onNodesChange:Ye,onEdgesChange:Xe,noDragClassName:He="nodrag",noWheelClassName:Ve="nowheel",noPanClassName:Ke="nopan",fitView:Fe=!1,fitViewOptions:Ze,connectOnClick:We=!0,attributionPosition:je,proOptions:qe,defaultEdgeOptions:Ue,elevateNodesOnSelect:Ge=!0,elevateEdgesOnSelect:Qe=!1,disableKeyboardA11y:Je=!1,autoPanOnConnect:et=!0,autoPanOnNodeDrag:tt=!0,connectionRadius:nt=20,isValidConnection:ot,onError:rt,style:it,id:at,nodeDragThreshold:st,...lt},ct)=>{const ut=at||"1";return t.createElement("div",{...lt,style:{...it,...Ga},ref:ct,className:n(["react-flow",a]),"data-testid":"rf__wrapper",id:at},t.createElement(Fa,null,t.createElement(Ya,{onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onNodeClick:c,onEdgeClick:u,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,nodeTypes:s,edgeTypes:l,connectionLineType:B,connectionLineStyle:L,connectionLineComponent:Y,connectionLineContainerStyle:X,selectionKeyCode:V,selectionOnDrag:K,selectionMode:F,deleteKeyCode:H,multiSelectionKeyCode:W,panActivationKeyCode:Z,zoomActivationKeyCode:j,onlyRenderVisibleElements:G,selectNodesOnDrag:Q,defaultViewport:ae,translateExtent:ce,minZoom:se,maxZoom:le,preventScrolling:ue,zoomOnScroll:fe,zoomOnPinch:ge,zoomOnDoubleClick:ve,panOnScroll:pe,panOnScrollSpeed:me,panOnScrollMode:ye,panOnDrag:be,onPaneClick:we,onPaneMouseEnter:xe,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,onSelectionContextMenu:R,onSelectionStart:z,onSelectionEnd:$,onEdgeContextMenu:Ne,onEdgeDoubleClick:ke,onEdgeMouseEnter:Pe,onEdgeMouseMove:Ae,onEdgeMouseLeave:Oe,onReconnect:ze??Ie,onReconnectStart:$e??De,onReconnectEnd:Te??Re,reconnectRadius:Be??Le,defaultMarkerColor:he,noDragClassName:He,noWheelClassName:Ve,noPanClassName:Ke,elevateEdgesOnSelect:Qe,rfId:ut,disableKeyboardA11y:Je,nodeOrigin:ne,nodeExtent:de}),t.createElement(ai,{nodes:e,edges:o,defaultNodes:r,defaultEdges:i,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,elevateNodesOnSelect:Ge,minZoom:se,maxZoom:le,nodeExtent:de,onNodesChange:Ye,onEdgesChange:Xe,snapToGrid:q,snapGrid:U,connectionMode:T,translateExtent:ce,connectOnClick:We,defaultEdgeOptions:Ue,fitView:Fe,fitViewOptions:Ze,onNodesDelete:k,onEdgesDelete:P,onNodeDragStart:C,onNodeDrag:M,onNodeDragStop:N,onSelectionDrag:I,onSelectionDragStart:O,onSelectionDragStop:D,noPanClassName:Ke,nodeOrigin:ne,rfId:ut,autoPanOnConnect:et,autoPanOnNodeDrag:tt,onError:rt,connectionRadius:nt,isValidConnection:ot,nodeDragThreshold:st}),t.createElement(ni,{onSelectionChange:A}),Me,t.createElement(Ao,{proOptions:qe,position:je}),t.createElement(fi,{rfId:ut,disableKeyboardA11y:Je})))}));Qa.displayName="ReactFlow";const Ja=({id:e,x:o,y:r,width:i,height:a,style:s,color:l,strokeColor:c,strokeWidth:u,className:d,borderRadius:h,shapeRendering:f,onClick:g,selected:p})=>{const{background:m,backgroundColor:y}=s||{},v=l||m||y;return t.createElement("rect",{className:n(["react-flow__minimap-node",{selected:p},d]),x:o,y:r,rx:h,ry:h,width:i,height:a,fill:v,stroke:c,strokeWidth:u,shapeRendering:f,onClick:g?t=>g(t,e):void 0})};Ja.displayName="MiniMapNode";var es=t.memo(Ja);const ts=e=>e.nodeOrigin,ns=e=>e.getNodes().filter((e=>!e.hidden&&e.width&&e.height)),os=e=>e instanceof Function?e:()=>e;var rs=t.memo((function({nodeStrokeColor:e="transparent",nodeColor:n="#e2e2e2",nodeClassName:r="",nodeBorderRadius:i=5,nodeStrokeWidth:a=2,nodeComponent:s=es,onClick:l}){const c=Mo(ns,o),u=Mo(ts),d=os(n),h=os(e),f=os(r),g="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return t.createElement(t.Fragment,null,c.map((e=>{const{x:n,y:o}=Mr(e,u).positionAbsolute;return t.createElement(s,{key:e.id,x:n,y:o,width:e.width,height:e.height,style:e.style,selected:e.selected,className:f(e),color:d(e),borderRadius:i,strokeColor:h(e),strokeWidth:a,shapeRendering:g,onClick:l,id:e.id})})))}));const is=e=>{const t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?(o=Nr(t,e.nodeOrigin),r=n,Yo(Bo(Lo(o),Lo(r)))):n,rfId:e.rfId};var o,r};function as({style:e,className:r,nodeStrokeColor:i="transparent",nodeColor:a="#e2e2e2",nodeClassName:s="",nodeBorderRadius:l=5,nodeStrokeWidth:c=2,nodeComponent:u,maskColor:d="rgb(240, 240, 240, 0.6)",maskStrokeColor:h="none",maskStrokeWidth:f=1,position:g="bottom-right",onClick:p,onNodeClick:m,pannable:y=!1,zoomable:v=!1,ariaLabel:b="React Flow mini map",inversePan:w=!1,zoomStep:x=10,offsetScale:S=5}){const E=No(),_=t.useRef(null),{boundingRect:C,viewBB:M,rfId:N}=Mo(is,o),k=e?.width??200,P=e?.height??150,A=C.width/k,O=C.height/P,I=Math.max(A,O),D=I*k,R=I*P,z=S*I,$=C.x-(D-C.width)/2-z,T=C.y-(R-C.height)/2-z,B=D+2*z,L=R+2*z,Y=`react-flow__minimap-desc-${N}`,X=t.useRef(0);X.current=I,t.useEffect((()=>{if(_.current){const e=_e(_.current),t=e=>{const{transform:t,d3Selection:n,d3Zoom:o}=E.getState();if("wheel"!==e.sourceEvent.type||!n||!o)return;const r=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*x,i=t[2]*Math.pow(2,r);o.scaleTo(n,i)},n=e=>{const{transform:t,d3Selection:n,d3Zoom:o,translateExtent:r,width:i,height:a}=E.getState();if("mousemove"!==e.sourceEvent.type||!n||!o)return;const s=X.current*Math.max(1,t[2])*(w?-1:1),l={x:t[0]-e.sourceEvent.movementX*s,y:t[1]-e.sourceEvent.movementY*s},c=[[0,0],[i,a]],u=Vn.translate(l.x,l.y).scale(t[2]),d=o.constrain()(u,c,r);o.transform(n,d)},o=Qn().on("zoom",y?n:null).on("zoom.wheel",v?t:null);return e.call(o),()=>{e.on("zoom",null)}}}),[y,v,w,x]);const H=p?e=>{const t=Ce(e);p(e,{x:t[0],y:t[1]})}:void 0,V=m?(e,t)=>{const n=E.getState().nodeInternals.get(t);m(e,n)}:void 0;return t.createElement(Po,{position:g,style:e,className:n(["react-flow__minimap",r]),"data-testid":"rf__minimap"},t.createElement("svg",{width:k,height:P,viewBox:`${$} ${T} ${B} ${L}`,role:"img","aria-labelledby":Y,ref:_,onClick:H},b&&t.createElement("title",{id:Y},b),t.createElement(rs,{onClick:V,nodeColor:a,nodeStrokeColor:i,nodeBorderRadius:l,nodeClassName:s,nodeStrokeWidth:c,nodeComponent:u}),t.createElement("path",{className:"react-flow__minimap-mask",d:`M${$-z},${T-z}h${B+2*z}v${L+2*z}h${-B-2*z}z\n        M${M.x},${M.y}h${M.width}v${M.height}h${-M.width}z`,fill:d,fillRule:"evenodd",stroke:h,strokeWidth:f,pointerEvents:"none"})))}as.displayName="MiniMap";var ss=t.memo(as);e.MiniMap=ss}));
