/* ============================================================================
 * 全局样式 - 量化交易平台
 * ============================================================================ */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import './responsive.css';

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --primary-color-light: #e6f7ff;
  
  /* 成功色 */
  --success-color: #52c41a;
  --success-color-hover: #73d13d;
  --success-color-active: #389e0d;
  --success-color-light: #f6ffed;
  
  /* 警告色 */
  --warning-color: #faad14;
  --warning-color-hover: #ffc53d;
  --warning-color-active: #d48806;
  --warning-color-light: #fffbe6;
  
  /* 错误色 */
  --error-color: #ff4d4f;
  --error-color-hover: #ff7875;
  --error-color-active: #d9363e;
  --error-color-light: #fff2f0;
  
  /* 中性色 */
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --text-color-disabled: #bfbfbf;
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  --background-color: #ffffff;
  --background-color-light: #fafafa;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing: 12px;
  --spacing-md: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* 暗色主题变量 */
[data-theme='dark'] {
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --text-color-disabled: #595959;
  --border-color: #434343;
  --border-color-light: #303030;
  --background-color: #141414;
  --background-color-light: #1f1f1f;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color-light);
  line-height: var(--line-height-normal);
  transition: color var(--transition), background-color var(--transition);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-color-hover);
}

a:active {
  color: var(--primary-color-active);
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all var(--transition-fast);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  text-align: left;
  padding: var(--spacing-sm) var(--spacing);
  border-bottom: 1px solid var(--border-color-light);
}

th {
  font-weight: 600;
  background-color: var(--background-color-light);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-color-light);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-secondary);
}

/* 工具类 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-word;
  overflow-wrap: break-word;
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/* 动画类 */
.fade-in {
  animation: fadeIn var(--transition) ease-in-out;
}

.fade-out {
  animation: fadeOut var(--transition) ease-in-out;
}

.slide-in-up {
  animation: slideInUp var(--transition) ease-out;
}

.slide-in-down {
  animation: slideInDown var(--transition) ease-out;
}

.slide-in-left {
  animation: slideInLeft var(--transition) ease-out;
}

.slide-in-right {
  animation: slideInRight var(--transition) ease-out;
}

.bounce-in {
  animation: bounceIn var(--transition-slow) ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

/* 关键帧动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义组件样式 */
.trading-card {
  background: var(--background-color);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast), transform var(--transition-fast);
}

.trading-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.price-up {
  color: var(--error-color);
}

.price-down {
  color: var(--success-color);
}

.price-neutral {
  color: var(--text-color-secondary);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1.2;
}

.status-badge.success {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-badge.warning {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.status-badge.error {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.status-badge.info {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* 数据表格样式 */
.data-table {
  background: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table .ant-table-thead > tr > th {
  background: var(--background-color-light);
  border-bottom: 2px solid var(--border-color-light);
  font-weight: 600;
  color: var(--text-color);
}

.data-table .ant-table-tbody > tr:hover > td {
  background: var(--primary-color-light);
}

/* 响应式隐藏类 */
@media (max-width: 767px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .show-mobile {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .trading-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
