'use client';

/**
 * JQData配置页面
 * 
 * 用户配置JQData账号、查看配额使用情况、测试连接等
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Alert,
  Progress,
  Statistic,
  Row,
  Col,
  Space,
  message,
  Modal,
  Descriptions,
  Tag,
  Spin,
  Steps,
  Radio,
  Tooltip,
} from 'antd';
import {
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  InfoCircleOutlined,
  LinkOutlined,
  WarningOutlined,
  MailOutlined,
  MobileOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { useAuth } from '@/store/auth';
import apiClient from '@/services/api';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface JQDataConfig {
  id?: number;
  username: string;
  loginType?: 'email' | 'mobile';
  isActive: boolean;
  quotaTotal: number;
  quotaUsed: number;
  quotaRemaining: number;
  quotaResetDate?: string;
  lastUsedAt?: string;
  totalApiCalls: number;
  lastAuthSuccess?: string;
  lastAuthError?: string;
  authFailureCount: number;
}

interface TestResult {
  success: boolean;
  message: string;
  quotaInfo?: any;
  responseTime?: number;
  errorDetails?: string;
}

export default function JQDataConfigPage() {
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [config, setConfig] = useState<JQDataConfig | null>(null);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [showTestModal, setShowTestModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [loginType, setLoginType] = useState<'email' | 'mobile'>('email');

  // 验证用户名格式
  const validateUsername = (rule: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入用户名'));
    }

    if (loginType === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return Promise.reject(new Error('请输入有效的邮箱地址'));
      }
    } else if (loginType === 'mobile') {
      const mobileRegex = /^1[3-9]\d{9}$/;
      if (!mobileRegex.test(value)) {
        return Promise.reject(new Error('请输入有效的手机号码'));
      }
    }

    return Promise.resolve();
  };

  // 加载JQData配置
  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/jqdata/config');
      
      if (response.code === 200 && response.data) {
        setConfig(response.data);
        const configLoginType = response.data.loginType || 'email';
        setLoginType(configLoginType);
        form.setFieldsValue({
          username: response.data.username,
          loginType: configLoginType,
        });
        setCurrentStep(2); // 已配置
      } else {
        setCurrentStep(0); // 未配置
      }
    } catch (error: any) {
      if (error.response?.status === 404) {
        setCurrentStep(0); // 未配置
      } else {
        message.error('加载配置失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      setCurrentStep(1); // 配置中
      
      const response = await apiClient.post('/jqdata/config', {
        username: values.username,
        password: values.password,
        loginType: values.loginType || loginType,
      });
      
      if (response.code === 200) {
        message.success('JQData配置保存成功！');
        setConfig(response.data);
        setCurrentStep(2); // 配置完成
        form.resetFields(['password']);
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      message.error(error.message || '配置保存失败');
      setCurrentStep(0); // 回到未配置状态
    } finally {
      setLoading(false);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      setTestLoading(true);
      const response = await apiClient.post('/jqdata/test-connection');
      
      setTestResult(response.data);
      setShowTestModal(true);
      
      if (response.data.success) {
        message.success('连接测试成功！');
      } else {
        message.error('连接测试失败');
      }
    } catch (error: any) {
      message.error('测试连接失败');
      setTestResult({
        success: false,
        message: '测试连接失败',
        errorDetails: error.message,
      });
      setShowTestModal(true);
    } finally {
      setTestLoading(false);
    }
  };

  // 删除配置
  const handleDelete = () => {
    Modal.confirm({
      title: '确认删除配置',
      content: '删除后将无法获取JQData数据，确定要删除吗？',
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiClient.delete('/jqdata/config');
          message.success('配置删除成功');
          setConfig(null);
          setCurrentStep(0);
          form.resetFields();
        } catch (error) {
          message.error('删除配置失败');
        }
      },
    });
  };

  useEffect(() => {
    loadConfig();
  }, []);

  // 配置步骤
  const steps = [
    {
      title: '配置账号',
      description: '输入JQData账号信息',
      icon: <ApiOutlined />,
    },
    {
      title: '验证连接',
      description: '验证账号有效性',
      icon: <LinkOutlined />,
    },
    {
      title: '配置完成',
      description: '开始使用JQData服务',
      icon: <CheckCircleOutlined />,
    },
  ];

  if (loading && !config) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="!mb-2">
          JQData配置
        </Title>
        <Text type="secondary">
          配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录
        </Text>
      </div>

      {/* 配置步骤 */}
      <Card>
        <Steps current={currentStep} items={steps} />
      </Card>

      {/* 配置状态提示 */}
      {config ? (
        <Alert
          message="JQData已配置"
          description={
            <div>
              <div>账号: {config.username}</div>
              <div>登录方式: {config.loginType === 'mobile' ? '手机号' : '邮箱'}</div>
              <div>状态: {config.isActive ? '正常' : '异常'}</div>
            </div>
          }
          type={config.isActive ? 'success' : 'warning'}
          showIcon
          action={
            <Space>
              <Button size="small" onClick={handleTestConnection} loading={testLoading}>
                测试连接
              </Button>
              <Button size="small" onClick={loadConfig}>
                刷新状态
              </Button>
            </Space>
          }
        />
      ) : (
        <Alert
          message="JQData未配置"
          description="请配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录"
          type="info"
          showIcon
        />
      )}

      <Row gutter={[16, 16]}>
        {/* 配置表单 */}
        <Col xs={24} lg={12}>
          <Card title="账号配置" extra={
            config && (
              <Button type="link" danger onClick={handleDelete}>
                删除配置
              </Button>
            )
          }>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              disabled={loading}
              initialValues={{ loginType: 'email' }}
            >
              <Form.Item
                name="loginType"
                label={
                  <span>
                    登录方式
                    <Tooltip title="选择您在JQData注册时使用的账号类型">
                      <QuestionCircleOutlined className="ml-1 text-gray-400" />
                    </Tooltip>
                  </span>
                }
              >
                <Radio.Group
                  value={loginType}
                  onChange={(e) => {
                    setLoginType(e.target.value);
                    form.setFieldsValue({ username: '' }); // 清空用户名
                  }}
                >
                  <Radio.Button value="email">
                    <MailOutlined /> 邮箱登录
                  </Radio.Button>
                  <Radio.Button value="mobile">
                    <MobileOutlined /> 手机号登录
                  </Radio.Button>
                </Radio.Group>
              </Form.Item>

              <Form.Item
                name="username"
                label={loginType === 'email' ? 'JQData邮箱' : 'JQData手机号'}
                rules={[
                  { validator: validateUsername },
                ]}
              >
                <Input
                  placeholder={
                    loginType === 'email'
                      ? '请输入JQData注册邮箱'
                      : '请输入JQData注册手机号'
                  }
                  prefix={loginType === 'email' ? <MailOutlined /> : <MobileOutlined />}
                />
              </Form.Item>

              <Form.Item
                name="password"
                label="JQData密码"
                rules={[
                  { required: !config, message: '请输入JQData密码' },
                  { min: 6, message: '密码长度至少6位' },
                ]}
              >
                <Input.Password
                  placeholder={config ? '留空表示不修改密码' : '请输入JQData密码'}
                  iconRender={(visible) => 
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<CheckCircleOutlined />}
                  >
                    {config ? '更新配置' : '保存配置'}
                  </Button>
                  
                  {config && (
                    <Button
                      onClick={handleTestConnection}
                      loading={testLoading}
                      icon={<LinkOutlined />}
                    >
                      测试连接
                    </Button>
                  )}
                </Space>
              </Form.Item>
            </Form>

            {/* 配置说明 */}
            <Alert
              message="配置说明"
              description={
                <div className="space-y-2 text-sm">
                  <div>• 支持邮箱或手机号登录，请选择您在JQData注册时使用的方式</div>
                  <div>• 邮箱格式：<EMAIL></div>
                  <div>• 手机号格式：13812345678（中国大陆手机号）</div>
                  <div>• 密码将被加密存储，确保账号安全</div>
                  <div>• 配置后可获取实时股票数据和历史数据</div>
                  <div>• 如遇问题请检查账号状态或联系客服</div>
                </div>
              }
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </Col>

        {/* 配额信息 */}
        <Col xs={24} lg={12}>
          <Card title="配额使用情况">
            {config ? (
              <div className="space-y-4">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="总配额"
                      value={config.quotaTotal}
                      suffix="次"
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="已使用"
                      value={config.quotaUsed}
                      suffix="次"
                      valueStyle={{ 
                        color: config.quotaUsed / config.quotaTotal > 0.8 ? '#ff4d4f' : '#1890ff' 
                      }}
                    />
                  </Col>
                </Row>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <Text>使用率</Text>
                    <Text strong>
                      {((config.quotaUsed / config.quotaTotal) * 100).toFixed(1)}%
                    </Text>
                  </div>
                  <Progress
                    percent={(config.quotaUsed / config.quotaTotal) * 100}
                    status={config.quotaUsed / config.quotaTotal > 0.9 ? 'exception' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                </div>

                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="剩余配额">
                    {config.quotaRemaining} 次
                  </Descriptions.Item>
                  <Descriptions.Item label="总调用次数">
                    {config.totalApiCalls} 次
                  </Descriptions.Item>
                  <Descriptions.Item label="最后使用">
                    {config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : '未使用'}
                  </Descriptions.Item>
                  <Descriptions.Item label="配额重置">
                    {config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : '未知'}
                  </Descriptions.Item>
                </Descriptions>

                {config.authFailureCount > 0 && (
                  <Alert
                    message={`认证失败 ${config.authFailureCount} 次`}
                    description={config.lastAuthError}
                    type="warning"
                    showIcon
                    className="mt-4"
                  />
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Text type="secondary">
                  配置JQData账号后查看配额信息
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 测试结果模态框 */}
      <Modal
        title="连接测试结果"
        open={showTestModal}
        onCancel={() => setShowTestModal(false)}
        footer={[
          <Button key="close" onClick={() => setShowTestModal(false)}>
            关闭
          </Button>,
        ]}
      >
        {testResult && (
          <div className="space-y-4">
            <Alert
              message={testResult.success ? '连接成功' : '连接失败'}
              description={testResult.message}
              type={testResult.success ? 'success' : 'error'}
              showIcon
            />

            {testResult.responseTime && (
              <div>
                <Text type="secondary">响应时间: </Text>
                <Text strong>{testResult.responseTime.toFixed(3)}秒</Text>
              </div>
            )}

            {testResult.quotaInfo && (
              <Descriptions title="配额信息" size="small" column={2}>
                <Descriptions.Item label="总配额">
                  {testResult.quotaInfo.quotaTotal}
                </Descriptions.Item>
                <Descriptions.Item label="已使用">
                  {testResult.quotaInfo.quotaUsed}
                </Descriptions.Item>
                <Descriptions.Item label="剩余">
                  {testResult.quotaInfo.quotaRemaining}
                </Descriptions.Item>
                <Descriptions.Item label="使用率">
                  {testResult.quotaInfo.quotaUsageRate?.toFixed(1)}%
                </Descriptions.Item>
              </Descriptions>
            )}

            {testResult.errorDetails && (
              <Alert
                message="错误详情"
                description={testResult.errorDetails}
                type="error"
                showIcon
              />
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
