"""add advanced features tables

Revision ID: 005
Revises: 004
Create Date: 2024-12-22 18:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建风险档案表
    op.create_table('risk_profiles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('risk_tolerance', sa.String(length=20), nullable=True),
        sa.Column('max_position_size', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('max_sector_exposure', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('max_drawdown_limit', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('var_confidence_level', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('var_time_horizon', sa.Integer(), nullable=True),
        sa.Column('var_method', sa.String(length=20), nullable=True),
        sa.Column('stop_loss_enabled', sa.Boolean(), nullable=True),
        sa.Column('stop_loss_percentage', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('trailing_stop_enabled', sa.Boolean(), nullable=True),
        sa.Column('trailing_stop_percentage', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_risk_profiles_id'), 'risk_profiles', ['id'], unique=False)
    op.create_index(op.f('ix_risk_profiles_user_id'), 'risk_profiles', ['user_id'], unique=False)

    # 创建风险指标表
    op.create_table('risk_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('portfolio_id', sa.Integer(), nullable=True),
        sa.Column('calculation_date', sa.DateTime(), nullable=False),
        sa.Column('var_1day', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('var_5day', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('var_10day', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('cvar_1day', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('portfolio_volatility', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('realized_volatility', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('implied_volatility', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('market_beta', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('market_correlation', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('diversification_ratio', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('current_drawdown', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('max_drawdown', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('drawdown_duration', sa.Integer(), nullable=True),
        sa.Column('concentration_hhi', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('top5_concentration', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('sector_concentration', sa.JSON(), nullable=True),
        sa.Column('liquidity_score', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('avg_daily_volume', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_risk_metrics_calculation_date'), 'risk_metrics', ['calculation_date'], unique=False)
    op.create_index(op.f('ix_risk_metrics_id'), 'risk_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_risk_metrics_user_id'), 'risk_metrics', ['user_id'], unique=False)

    # 创建风险预警表
    op.create_table('risk_alerts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('risk_profile_id', sa.Integer(), nullable=False),
        sa.Column('alert_type', sa.String(length=50), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=True),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('current_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('threshold_value', sa.Numeric(precision=15, scale=6), nullable=True),
        sa.Column('breach_percentage', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('symbol', sa.String(length=20), nullable=True),
        sa.Column('sector', sa.String(length=50), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
        sa.Column('resolved_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['risk_profile_id'], ['risk_profiles.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_risk_alerts_id'), 'risk_alerts', ['id'], unique=False)
    op.create_index(op.f('ix_risk_alerts_risk_profile_id'), 'risk_alerts', ['risk_profile_id'], unique=False)
    op.create_index(op.f('ix_risk_alerts_user_id'), 'risk_alerts', ['user_id'], unique=False)

    # 创建止损订单表
    op.create_table('stop_loss_orders',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('order_type', sa.String(length=20), nullable=True),
        sa.Column('quantity', sa.Integer(), nullable=False),
        sa.Column('trigger_price', sa.Numeric(precision=10, scale=4), nullable=False),
        sa.Column('stop_price', sa.Numeric(precision=10, scale=4), nullable=False),
        sa.Column('original_price', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('trailing_amount', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('trailing_percentage', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('highest_price', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('triggered_at', sa.DateTime(), nullable=True),
        sa.Column('executed_at', sa.DateTime(), nullable=True),
        sa.Column('execution_price', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('max_loss_amount', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('risk_reward_ratio', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stop_loss_orders_id'), 'stop_loss_orders', ['id'], unique=False)
    op.create_index(op.f('ix_stop_loss_orders_symbol'), 'stop_loss_orders', ['symbol'], unique=False)
    op.create_index(op.f('ix_stop_loss_orders_user_id'), 'stop_loss_orders', ['user_id'], unique=False)

    # 创建风险情景表
    op.create_table('risk_scenarios',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('scenario_type', sa.String(length=50), nullable=True),
        sa.Column('market_shock', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('volatility_multiplier', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('correlation_adjustment', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('time_horizon', sa.Integer(), nullable=True),
        sa.Column('scenario_results', sa.JSON(), nullable=True),
        sa.Column('portfolio_pnl', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('worst_case_loss', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('probability', sa.Numeric(precision=10, scale=6), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_risk_scenarios_id'), 'risk_scenarios', ['id'], unique=False)
    op.create_index(op.f('ix_risk_scenarios_user_id'), 'risk_scenarios', ['user_id'], unique=False)

    # 创建报告模板表
    op.create_table('report_templates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('template_type', sa.String(length=50), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('template_config', sa.JSON(), nullable=False),
        sa.Column('layout_config', sa.JSON(), nullable=True),
        sa.Column('style_config', sa.JSON(), nullable=True),
        sa.Column('sections', sa.JSON(), nullable=True),
        sa.Column('charts', sa.JSON(), nullable=True),
        sa.Column('tables', sa.JSON(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_templates_id'), 'report_templates', ['id'], unique=False)
    op.create_index(op.f('ix_report_templates_user_id'), 'report_templates', ['user_id'], unique=False)

    # 创建报告任务表
    op.create_table('report_tasks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('template_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('report_type', sa.String(length=50), nullable=False),
        sa.Column('date_range_start', sa.DateTime(), nullable=True),
        sa.Column('date_range_end', sa.DateTime(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('schedule_type', sa.String(length=20), nullable=True),
        sa.Column('schedule_config', sa.JSON(), nullable=True),
        sa.Column('next_run_time', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('output_format', sa.String(length=20), nullable=True),
        sa.Column('output_size', sa.Integer(), nullable=True),
        sa.Column('output_path', sa.String(length=500), nullable=True),
        sa.Column('email_enabled', sa.Boolean(), nullable=True),
        sa.Column('email_recipients', sa.JSON(), nullable=True),
        sa.Column('email_subject', sa.String(length=200), nullable=True),
        sa.Column('email_body', sa.Text(), nullable=True),
        sa.Column('email_sent_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['template_id'], ['report_templates.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_tasks_id'), 'report_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_report_tasks_template_id'), 'report_tasks', ['template_id'], unique=False)
    op.create_index(op.f('ix_report_tasks_user_id'), 'report_tasks', ['user_id'], unique=False)

    # 创建报告历史表
    op.create_table('report_history',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('task_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('report_name', sa.String(length=100), nullable=False),
        sa.Column('report_type', sa.String(length=50), nullable=False),
        sa.Column('file_name', sa.String(length=200), nullable=False),
        sa.Column('file_path', sa.String(length=500), nullable=False),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('file_hash', sa.String(length=64), nullable=True),
        sa.Column('content_summary', sa.JSON(), nullable=True),
        sa.Column('data_sources', sa.JSON(), nullable=True),
        sa.Column('generation_time', sa.Integer(), nullable=True),
        sa.Column('download_count', sa.Integer(), nullable=True),
        sa.Column('last_downloaded_at', sa.DateTime(), nullable=True),
        sa.Column('view_count', sa.Integer(), nullable=True),
        sa.Column('last_viewed_at', sa.DateTime(), nullable=True),
        sa.Column('is_shared', sa.Boolean(), nullable=True),
        sa.Column('share_token', sa.String(length=64), nullable=True),
        sa.Column('share_expires_at', sa.DateTime(), nullable=True),
        sa.Column('is_archived', sa.Boolean(), nullable=True),
        sa.Column('archived_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['task_id'], ['report_tasks.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('task_id')
    )
    op.create_index(op.f('ix_report_history_id'), 'report_history', ['id'], unique=False)
    op.create_index(op.f('ix_report_history_task_id'), 'report_history', ['task_id'], unique=True)
    op.create_index(op.f('ix_report_history_user_id'), 'report_history', ['user_id'], unique=False)

    # 创建报告订阅表
    op.create_table('report_subscriptions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('template_id', sa.Integer(), nullable=False),
        sa.Column('subscription_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('frequency', sa.String(length=20), nullable=False),
        sa.Column('schedule_time', sa.String(length=10), nullable=True),
        sa.Column('schedule_day', sa.Integer(), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        sa.Column('report_format', sa.String(length=20), nullable=True),
        sa.Column('include_charts', sa.Boolean(), nullable=True),
        sa.Column('include_tables', sa.Boolean(), nullable=True),
        sa.Column('include_analysis', sa.Boolean(), nullable=True),
        sa.Column('email_enabled', sa.Boolean(), nullable=True),
        sa.Column('email_recipients', sa.JSON(), nullable=False),
        sa.Column('email_subject_template', sa.String(length=200), nullable=True),
        sa.Column('email_body_template', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('last_sent_at', sa.DateTime(), nullable=True),
        sa.Column('next_send_at', sa.DateTime(), nullable=True),
        sa.Column('send_count', sa.Integer(), nullable=True),
        sa.Column('failure_count', sa.Integer(), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('last_error_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['template_id'], ['report_templates.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_subscriptions_id'), 'report_subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_report_subscriptions_template_id'), 'report_subscriptions', ['template_id'], unique=False)
    op.create_index(op.f('ix_report_subscriptions_user_id'), 'report_subscriptions', ['user_id'], unique=False)

    # 创建报告分析统计表
    op.create_table('report_analytics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.DateTime(), nullable=False),
        sa.Column('reports_generated', sa.Integer(), nullable=True),
        sa.Column('pdf_reports', sa.Integer(), nullable=True),
        sa.Column('excel_reports', sa.Integer(), nullable=True),
        sa.Column('html_reports', sa.Integer(), nullable=True),
        sa.Column('emails_sent', sa.Integer(), nullable=True),
        sa.Column('emails_opened', sa.Integer(), nullable=True),
        sa.Column('emails_clicked', sa.Integer(), nullable=True),
        sa.Column('downloads_total', sa.Integer(), nullable=True),
        sa.Column('downloads_pdf', sa.Integer(), nullable=True),
        sa.Column('downloads_excel', sa.Integer(), nullable=True),
        sa.Column('template_usage', sa.JSON(), nullable=True),
        sa.Column('avg_generation_time', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('total_file_size', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_report_analytics_date'), 'report_analytics', ['date'], unique=False)
    op.create_index(op.f('ix_report_analytics_id'), 'report_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_report_analytics_user_id'), 'report_analytics', ['user_id'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_report_analytics_user_id'), table_name='report_analytics')
    op.drop_index(op.f('ix_report_analytics_id'), table_name='report_analytics')
    op.drop_index(op.f('ix_report_analytics_date'), table_name='report_analytics')
    op.drop_table('report_analytics')
    
    op.drop_index(op.f('ix_report_subscriptions_user_id'), table_name='report_subscriptions')
    op.drop_index(op.f('ix_report_subscriptions_template_id'), table_name='report_subscriptions')
    op.drop_index(op.f('ix_report_subscriptions_id'), table_name='report_subscriptions')
    op.drop_table('report_subscriptions')
    
    op.drop_index(op.f('ix_report_history_user_id'), table_name='report_history')
    op.drop_index(op.f('ix_report_history_task_id'), table_name='report_history')
    op.drop_index(op.f('ix_report_history_id'), table_name='report_history')
    op.drop_table('report_history')
    
    op.drop_index(op.f('ix_report_tasks_user_id'), table_name='report_tasks')
    op.drop_index(op.f('ix_report_tasks_template_id'), table_name='report_tasks')
    op.drop_index(op.f('ix_report_tasks_id'), table_name='report_tasks')
    op.drop_table('report_tasks')
    
    op.drop_index(op.f('ix_report_templates_user_id'), table_name='report_templates')
    op.drop_index(op.f('ix_report_templates_id'), table_name='report_templates')
    op.drop_table('report_templates')
    
    op.drop_index(op.f('ix_risk_scenarios_user_id'), table_name='risk_scenarios')
    op.drop_index(op.f('ix_risk_scenarios_id'), table_name='risk_scenarios')
    op.drop_table('risk_scenarios')
    
    op.drop_index(op.f('ix_stop_loss_orders_user_id'), table_name='stop_loss_orders')
    op.drop_index(op.f('ix_stop_loss_orders_symbol'), table_name='stop_loss_orders')
    op.drop_index(op.f('ix_stop_loss_orders_id'), table_name='stop_loss_orders')
    op.drop_table('stop_loss_orders')
    
    op.drop_index(op.f('ix_risk_alerts_user_id'), table_name='risk_alerts')
    op.drop_index(op.f('ix_risk_alerts_risk_profile_id'), table_name='risk_alerts')
    op.drop_index(op.f('ix_risk_alerts_id'), table_name='risk_alerts')
    op.drop_table('risk_alerts')
    
    op.drop_index(op.f('ix_risk_metrics_user_id'), table_name='risk_metrics')
    op.drop_index(op.f('ix_risk_metrics_id'), table_name='risk_metrics')
    op.drop_index(op.f('ix_risk_metrics_calculation_date'), table_name='risk_metrics')
    op.drop_table('risk_metrics')
    
    op.drop_index(op.f('ix_risk_profiles_user_id'), table_name='risk_profiles')
    op.drop_index(op.f('ix_risk_profiles_id'), table_name='risk_profiles')
    op.drop_table('risk_profiles')
