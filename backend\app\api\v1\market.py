"""
市场数据相关API端点

包含股票列表、价格数据、市场概览等功能
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.market import DailyPrice, Stock
from app.models.user import User
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.schemas.market import (
    MarketOverviewResponse,
    PriceDataRequest,
    PriceDataResponse,
    StockListRequest,
    StockResponse,
    StockSearchRequest,
    StockSearchResponse,
)
from app.services.jqdata_service import JQDataService

router = APIRouter()

# JQData服务实例
jqdata_service = JQDataService()


# =============================================================================
# 股票基本信息
# =============================================================================

@router.get("/securities", response_model=BaseResponse[PaginatedResponse[StockResponse]])
async def get_securities_list(
    types: List[str] = Query(['stock'], description="标的类型列表，如stock,fund,index,futures,etf,lof,fja,fjb"),
    date: str = Query(None, description="查询日期，格式YYYY-MM-DD，默认为当前日期"),
    industry: str = Query(None, description="行业筛选"),
    sector: str = Query(None, description="板块筛选"),
    is_active: bool = Query(True, description="是否只返回活跃标的"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=1000, description="每页大小"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取标的信息列表
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取标的信息
        securities_df = await jqdata_service.get_all_securities(
            user_id=current_user.id,
            db=db,
            types=types,
            date=date
        )

        # 转换为列表格式
        securities_list = []
        for index, row in securities_df.iterrows():
            securities_list.append({
                'symbol': index,  # 标的代码作为索引
                'display_name': row.get('display_name', ''),
                'name': row.get('name', ''),
                'start_date': row.get('start_date', '').strftime('%Y-%m-%d') if row.get('start_date') else '',
                'end_date': row.get('end_date', '').strftime('%Y-%m-%d') if row.get('end_date') else '',
                'type': row.get('type', ''),
            })

        # 应用筛选条件
        if industry:
            # 这里可以添加行业筛选逻辑
            pass

        if sector:
            # 这里可以添加板块筛选逻辑
            pass

        if is_active:
            # 筛选活跃标的（结束日期为空或未来日期）
            from datetime import datetime
            current_date = datetime.now().strftime('%Y-%m-%d')
            securities_list = [
                s for s in securities_list
                if not s['end_date'] or s['end_date'] >= current_date
            ]

        # 分页处理
        total = len(securities_list)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_securities = securities_list[start_idx:end_idx]

        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )

        return BaseResponse(
            code=200,
            message="获取标的信息成功",
            data=PaginatedResponse(
                items=paginated_securities,
                pagination=pagination
            )
        )

    except Exception as e:
        logger.error(f"获取标的信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取标的信息失败: {str(e)}"
        )


@router.get("/price", response_model=BaseResponse[Dict[str, Any]])
async def get_price_data(
    security: str = Query(..., description="标的代码，如'000001.XSHE'"),
    start_date: str = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式YYYY-MM-DD"),
    frequency: str = Query('daily', description="频率：daily,1m,5m,15m,30m,60m"),
    fields: List[str] = Query(['open', 'close', 'high', 'low', 'volume', 'money'],
                             description="字段列表"),
    skip_paused: bool = Query(False, description="是否跳过停牌日期"),
    fq: str = Query('pre', description="复权选项：pre前复权,post后复权,None不复权"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取历史价格数据
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取价格数据
        price_df = await jqdata_service.get_price(
            user_id=current_user.id,
            db=db,
            security=security,
            start_date=start_date,
            end_date=end_date,
            frequency=frequency,
            fields=fields,
            skip_paused=skip_paused,
            fq=fq if fq != 'None' else None
        )

        # 转换为字典格式
        price_data = price_df.to_dict('records')

        # 添加时间索引
        for i, record in enumerate(price_data):
            if hasattr(price_df.index[i], 'strftime'):
                record['datetime'] = price_df.index[i].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['datetime'] = str(price_df.index[i])

        return BaseResponse(
            code=200,
            message="获取价格数据成功",
            data={
                'security': security,
                'frequency': frequency,
                'fq': fq,
                'count': len(price_data),
                'data': price_data
            }
        )

    except Exception as e:
        logger.error(f"获取价格数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取价格数据失败: {str(e)}"
        )


@router.get("/current", response_model=BaseResponse[Dict[str, Any]])
async def get_current_data(
    security: str = Query(..., description="标的代码，如'000001.XSHE'"),
    fields: List[str] = Query(['last_price', 'high_limit', 'low_limit', 'volume', 'money'],
                             description="字段列表"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前价格数据
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取当前数据
        current_df = await jqdata_service.get_current_data(
            user_id=current_user.id,
            db=db,
            security=security,
            fields=fields
        )

        # 转换为字典格式
        current_data = current_df.to_dict('records')[0] if len(current_df) > 0 else {}

        return BaseResponse(
            code=200,
            message="获取当前数据成功",
            data={
                'security': security,
                'fields': fields,
                'data': current_data
            }
        )

    except Exception as e:
        logger.error(f"获取当前数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取当前数据失败: {str(e)}"
        )


@router.get("/trade-days", response_model=BaseResponse[List[str]])
async def get_trade_days(
    start_date: str = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式YYYY-MM-DD"),
    count: int = Query(None, description="获取交易日数量"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定范围交易日
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取交易日
        trade_days = await jqdata_service.get_trade_days(
            user_id=current_user.id,
            db=db,
            start_date=start_date,
            end_date=end_date,
            count=count
        )

        return BaseResponse(
            code=200,
            message="获取交易日成功",
            data=trade_days
        )

    except Exception as e:
        logger.error(f"获取交易日失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取交易日失败: {str(e)}"
        )


@router.get("/security-info", response_model=BaseResponse[Dict[str, Any]])
async def get_security_info(
    code: str = Query(..., description="标的代码，如'000001.XSHE'"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取单支标的信息
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取标的信息
        security_info = await jqdata_service.get_security_info(
            user_id=current_user.id,
            db=db,
            code=code
        )

        return BaseResponse(
            code=200,
            message="获取标的信息成功",
            data=security_info
        )

    except Exception as e:
        logger.error(f"获取标的信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取标的信息失败: {str(e)}"
        )


@router.get("/bars", response_model=BaseResponse[Dict[str, Any]])
async def get_bars_data(
    security: str = Query(..., description="标的代码，如'000001.XSHE'"),
    count: int = Query(..., description="获取数据条数"),
    unit: str = Query('1d', description="时间单位：1d,1m,5m,15m,30m,60m"),
    fields: List[str] = Query(['open', 'close', 'high', 'low', 'volume', 'money'],
                             description="字段列表"),
    include_now: bool = Query(False, description="是否包含当前时间"),
    end_dt: str = Query(None, description="结束时间，格式YYYY-MM-DD HH:MM:SS"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取历史数据（按数量）
    按照JQData官方API规范实现，与get_price的区别见官方文档
    """
    try:
        # 使用JQData服务获取历史数据
        bars_df = await jqdata_service.get_bars(
            user_id=current_user.id,
            db=db,
            security=security,
            count=count,
            unit=unit,
            fields=fields,
            include_now=include_now,
            end_dt=end_dt
        )

        # 转换为字典格式
        bars_data = bars_df.to_dict('records')

        # 添加时间索引
        for i, record in enumerate(bars_data):
            if hasattr(bars_df.index[i], 'strftime'):
                record['datetime'] = bars_df.index[i].strftime('%Y-%m-%d %H:%M:%S')
            else:
                record['datetime'] = str(bars_df.index[i])

        return BaseResponse(
            code=200,
            message="获取历史数据成功",
            data={
                'security': security,
                'count': count,
                'unit': unit,
                'fields': fields,
                'data': bars_data
            }
        )

    except Exception as e:
        logger.error(f"获取历史数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取历史数据失败: {str(e)}"
        )


@router.get("/index-stocks", response_model=BaseResponse[List[str]])
async def get_index_stocks(
    index: str = Query(..., description="指数代码，如'000300.XSHG'"),
    date: str = Query(None, description="查询日期，格式YYYY-MM-DD"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指数成分股
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取指数成分股
        stocks = await jqdata_service.get_index_stocks(
            user_id=current_user.id,
            db=db,
            index=index,
            date=date
        )

        return BaseResponse(
            code=200,
            message="获取指数成分股成功",
            data=stocks
        )

    except Exception as e:
        logger.error(f"获取指数成分股失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取指数成分股失败: {str(e)}"
        )


@router.get("/index-weights", response_model=BaseResponse[Dict[str, Any]])
async def get_index_weights(
    index: str = Query(..., description="指数代码，如'000300.XSHG'"),
    date: str = Query(None, description="查询日期，格式YYYY-MM-DD"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指数成分股权重
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取指数权重
        weights_df = await jqdata_service.get_index_weights(
            user_id=current_user.id,
            db=db,
            index=index,
            date=date
        )

        # 转换为字典格式
        weights_data = weights_df.to_dict('records')

        return BaseResponse(
            code=200,
            message="获取指数权重成功",
            data={
                'index': index,
                'date': date,
                'count': len(weights_data),
                'data': weights_data
            }
        )

    except Exception as e:
        logger.error(f"获取指数权重失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取指数权重失败: {str(e)}"
        )


@router.get("/money-flow", response_model=BaseResponse[Dict[str, Any]])
async def get_money_flow(
    security: str = Query(..., description="标的代码，如'000001.XSHE'"),
    start_date: str = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式YYYY-MM-DD"),
    fields: List[str] = Query(['net_amount_main', 'net_pct_main'], description="字段列表"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取资金流向数据
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取资金流向数据
        flow_df = await jqdata_service.get_money_flow(
            user_id=current_user.id,
            db=db,
            security=security,
            start_date=start_date,
            end_date=end_date,
            fields=fields
        )

        # 转换为字典格式
        flow_data = flow_df.to_dict('records')

        # 添加时间索引
        for i, record in enumerate(flow_data):
            if hasattr(flow_df.index[i], 'strftime'):
                record['date'] = flow_df.index[i].strftime('%Y-%m-%d')
            else:
                record['date'] = str(flow_df.index[i])

        return BaseResponse(
            code=200,
            message="获取资金流向数据成功",
            data=PaginatedResponse(
                items=stock_responses,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票列表失败"
        )


@router.get("/stocks/{symbol}", response_model=BaseResponse[StockResponse])
async def get_stock_info(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取股票详细信息"""
    try:
        result = await db.execute(
            select(Stock).where(Stock.symbol == symbol)
        )
        stock = result.scalar_one_or_none()
        
        if not stock:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"股票 {symbol} 不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取股票信息成功",
            data=StockResponse.from_orm(stock)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票信息失败"
        )


@router.post("/stocks/search", response_model=BaseResponse[List[StockSearchResponse]])
async def search_stocks(
    search_request: StockSearchRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """搜索股票"""
    try:
        keyword = search_request.keyword.strip()
        conditions = []
        
        # 根据搜索类型构建条件
        if search_request.search_type in ["all", "code"]:
            conditions.append(Stock.symbol.ilike(f"%{keyword}%"))
        
        if search_request.search_type in ["all", "name"]:
            conditions.append(Stock.name.ilike(f"%{keyword}%"))
            conditions.append(Stock.display_name.ilike(f"%{keyword}%"))
        
        # 市场筛选
        query_conditions = []
        if conditions:
            from sqlalchemy import or_
            query_conditions.append(or_(*conditions))
        
        if search_request.market:
            query_conditions.append(Stock.market == search_request.market)
        
        query_conditions.append(Stock.is_active == True)
        
        # 执行查询
        query = (
            select(Stock)
            .where(and_(*query_conditions))
            .order_by(Stock.symbol)
            .limit(search_request.limit)
        )
        
        result = await db.execute(query)
        stocks = result.scalars().all()
        
        # 构建响应
        search_results = []
        for stock in stocks:
            # 简单的匹配评分逻辑
            match_score = 100.0
            match_type = "name"
            
            if keyword.upper() in stock.symbol.upper():
                match_type = "code"
                match_score = 95.0
            elif keyword in stock.name:
                match_type = "name"
                match_score = 90.0
            
            search_results.append(StockSearchResponse(
                symbol=stock.symbol,
                name=stock.name,
                market=stock.market,
                industry=stock.industry,
                match_type=match_type,
                match_score=match_score
            ))
        
        return BaseResponse(
            code=200,
            message="股票搜索成功",
            data=search_results
        )
        
    except Exception as e:
        logger.error(f"股票搜索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="股票搜索失败"
        )


# =============================================================================
# 价格数据
# =============================================================================

@router.post("/price-data", response_model=BaseResponse[List[PriceDataResponse]])
async def get_price_data(
    price_request: PriceDataRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取价格数据"""
    try:
        # 使用JQData服务获取数据
        price_data = await jqdata_service.get_price_data(
            user_id=current_user.id,
            db=db,
            symbols=price_request.symbols,
            start_date=price_request.start_date,
            end_date=price_request.end_date,
            frequency=price_request.frequency
        )
        
        # 转换为响应格式
        price_responses = []
        for _, row in price_data.iterrows():
            price_responses.append(PriceDataResponse(
                symbol=row.get('symbol', ''),
                datetime=str(row.name) if hasattr(row, 'name') else '',
                open_price=row.get('open'),
                high_price=row.get('high'),
                low_price=row.get('low'),
                close_price=row.get('close'),
                volume=row.get('volume'),
                turnover=row.get('money'),
                adj_close=row.get('close'),  # 简化处理
                change_pct=None,  # 需要计算
                turnover_rate=None,  # 需要计算
                is_trading_day=True,
                is_suspended=False
            ))
        
        return BaseResponse(
            code=200,
            message="获取价格数据成功",
            data=price_responses
        )
        
    except Exception as e:
        logger.error(f"获取价格数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取价格数据失败: {str(e)}"
        )


@router.get("/price-data/{symbol}/latest", response_model=BaseResponse[PriceDataResponse])
async def get_latest_price(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取最新价格"""
    try:
        # 从数据库获取最新价格
        query = (
            select(DailyPrice)
            .where(DailyPrice.symbol == symbol)
            .order_by(desc(DailyPrice.trade_date))
            .limit(1)
        )
        
        result = await db.execute(query)
        latest_price = result.scalar_one_or_none()
        
        if not latest_price:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"股票 {symbol} 的价格数据不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取最新价格成功",
            data=PriceDataResponse.from_orm(latest_price)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取最新价格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取最新价格失败"
        )


# =============================================================================
# 市场概览
# =============================================================================

@router.get("/overview", response_model=BaseResponse[MarketOverviewResponse])
async def get_market_overview(
    market: str = Query("A", pattern="^(A|HK|US)$", description="市场类型"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取市场概览"""
    try:
        # 查询股票统计
        stock_stats_query = (
            select(
                func.count(Stock.id).label('total_stocks'),
                func.count(Stock.id).filter(Stock.is_active == True).label('trading_stocks'),
                func.count(Stock.id).filter(Stock.is_active == False).label('suspended_stocks')
            )
            .where(Stock.market == market)
        )
        
        result = await db.execute(stock_stats_query)
        stats = result.first()
        
        # 构建市场概览响应
        overview = MarketOverviewResponse(
            market=market,
            total_stocks=stats.total_stocks or 0,
            trading_stocks=stats.trading_stocks or 0,
            suspended_stocks=stats.suspended_stocks or 0,
            up_stocks=0,  # 需要从价格数据计算
            down_stocks=0,  # 需要从价格数据计算
            flat_stocks=0,  # 需要从价格数据计算
            total_volume=None,
            total_turnover=None,
            avg_change_pct=None,
            last_update=datetime.utcnow()
        )
        
        return BaseResponse(
            code=200,
            message="获取市场概览成功",
            data=overview
        )
        
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取市场概览失败"
        )


# =============================================================================
# 数据同步
# =============================================================================

@router.post("/sync/stocks", response_model=BaseResponse[Dict[str, Any]])
async def sync_stock_list(
    market: str = Query("A", pattern="^(A|HK|US)$", description="市场类型"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """同步股票列表"""
    try:
        # 使用JQData服务获取股票列表
        stock_data = await jqdata_service.get_stock_list(
            user_id=current_user.id,
            db=db,
            market=market
        )
        
        # 这里应该将数据保存到数据库
        # 暂时返回成功响应
        
        return BaseResponse(
            code=200,
            message="股票列表同步成功",
            data={
                "market": market,
                "synced_count": len(stock_data),
                "sync_time": datetime.utcnow().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"同步股票列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步股票列表失败: {str(e)}"
        )


@router.get("/cctv-news", response_model=BaseResponse[Dict[str, Any]])
async def get_cctv_news(
    start_date: str = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式YYYY-MM-DD"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取CCTV新闻联播文本数据
    按照JQData官方API规范实现
    """
    try:
        # 使用JQData服务获取CCTV新闻数据
        news_df = await jqdata_service.get_cctv_news(
            user_id=current_user.id,
            db=db,
            start_date=start_date,
            end_date=end_date
        )

        # 转换为字典格式
        news_data = news_df.to_dict('records')

        # 添加日期格式化
        for record in news_data:
            if 'date' in record and hasattr(record['date'], 'strftime'):
                record['date'] = record['date'].strftime('%Y-%m-%d')

        return BaseResponse(
            code=200,
            message="获取CCTV新闻数据成功",
            data={
                'start_date': start_date,
                'end_date': end_date,
                'count': len(news_data),
                'news': news_data
            }
        )

    except Exception as e:
        logger.error(f"获取CCTV新闻数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取CCTV新闻数据失败: {str(e)}"
        )


@router.post("/news-sentiment", response_model=BaseResponse[Dict[str, Any]])
async def analyze_news_sentiment(
    start_date: str = Query(None, description="开始日期，格式YYYY-MM-DD"),
    end_date: str = Query(None, description="结束日期，格式YYYY-MM-DD"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    分析CCTV新闻联播情绪
    获取新闻数据并进行情绪分析，用于量化投资决策
    """
    try:
        # 首先获取新闻数据
        news_df = await jqdata_service.get_cctv_news(
            user_id=current_user.id,
            db=db,
            start_date=start_date,
            end_date=end_date
        )

        if news_df.empty:
            return BaseResponse(
                code=200,
                message="指定日期范围内无新闻数据",
                data={
                    'overall_sentiment': 0,
                    'sentiment_trend': 'neutral',
                    'total_news': 0
                }
            )

        # 进行情绪分析
        sentiment_result = await jqdata_service.analyze_news_sentiment(
            user_id=current_user.id,
            db=db,
            news_data=news_df
        )

        return BaseResponse(
            code=200,
            message="新闻情绪分析完成",
            data=sentiment_result
        )

    except Exception as e:
        logger.error(f"新闻情绪分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"新闻情绪分析失败: {str(e)}"
        )
