"""
市场数据相关API端点

包含股票列表、价格数据、市场概览等功能
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.market import DailyPrice, Stock
from app.models.user import User
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.schemas.market import (
    MarketOverviewResponse,
    PriceDataRequest,
    PriceDataResponse,
    StockListRequest,
    StockResponse,
    StockSearchRequest,
    StockSearchResponse,
)
from app.services.jqdata_service import JQDataService

router = APIRouter()

# JQData服务实例
jqdata_service = JQDataService()


# =============================================================================
# 股票基本信息
# =============================================================================

@router.get("/stocks", response_model=BaseResponse[PaginatedResponse[StockResponse]])
async def get_stock_list(
    market: str = Query("A", regex="^(A|HK|US)$", description="市场类型"),
    industry: str = Query(None, description="行业筛选"),
    sector: str = Query(None, description="板块筛选"),
    is_active: bool = Query(True, description="是否只返回活跃股票"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=1000, description="每页大小"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取股票列表"""
    try:
        # 构建查询条件
        conditions = [Stock.market == market]
        
        if is_active:
            conditions.append(Stock.is_active == True)
        
        if industry:
            conditions.append(Stock.industry == industry)
        
        if sector:
            conditions.append(Stock.sector == sector)
        
        # 查询总数
        count_query = select(func.count(Stock.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(Stock)
            .where(and_(*conditions))
            .order_by(Stock.symbol)
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        stocks = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        # 转换响应数据
        stock_responses = [StockResponse.from_orm(stock) for stock in stocks]
        
        return BaseResponse(
            code=200,
            message="获取股票列表成功",
            data=PaginatedResponse(
                items=stock_responses,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票列表失败"
        )


@router.get("/stocks/{symbol}", response_model=BaseResponse[StockResponse])
async def get_stock_info(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取股票详细信息"""
    try:
        result = await db.execute(
            select(Stock).where(Stock.symbol == symbol)
        )
        stock = result.scalar_one_or_none()
        
        if not stock:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"股票 {symbol} 不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取股票信息成功",
            data=StockResponse.from_orm(stock)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取股票信息失败"
        )


@router.post("/stocks/search", response_model=BaseResponse[List[StockSearchResponse]])
async def search_stocks(
    search_request: StockSearchRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """搜索股票"""
    try:
        keyword = search_request.keyword.strip()
        conditions = []
        
        # 根据搜索类型构建条件
        if search_request.search_type in ["all", "code"]:
            conditions.append(Stock.symbol.ilike(f"%{keyword}%"))
        
        if search_request.search_type in ["all", "name"]:
            conditions.append(Stock.name.ilike(f"%{keyword}%"))
            conditions.append(Stock.display_name.ilike(f"%{keyword}%"))
        
        # 市场筛选
        query_conditions = []
        if conditions:
            from sqlalchemy import or_
            query_conditions.append(or_(*conditions))
        
        if search_request.market:
            query_conditions.append(Stock.market == search_request.market)
        
        query_conditions.append(Stock.is_active == True)
        
        # 执行查询
        query = (
            select(Stock)
            .where(and_(*query_conditions))
            .order_by(Stock.symbol)
            .limit(search_request.limit)
        )
        
        result = await db.execute(query)
        stocks = result.scalars().all()
        
        # 构建响应
        search_results = []
        for stock in stocks:
            # 简单的匹配评分逻辑
            match_score = 100.0
            match_type = "name"
            
            if keyword.upper() in stock.symbol.upper():
                match_type = "code"
                match_score = 95.0
            elif keyword in stock.name:
                match_type = "name"
                match_score = 90.0
            
            search_results.append(StockSearchResponse(
                symbol=stock.symbol,
                name=stock.name,
                market=stock.market,
                industry=stock.industry,
                match_type=match_type,
                match_score=match_score
            ))
        
        return BaseResponse(
            code=200,
            message="股票搜索成功",
            data=search_results
        )
        
    except Exception as e:
        logger.error(f"股票搜索失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="股票搜索失败"
        )


# =============================================================================
# 价格数据
# =============================================================================

@router.post("/price-data", response_model=BaseResponse[List[PriceDataResponse]])
async def get_price_data(
    price_request: PriceDataRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取价格数据"""
    try:
        # 使用JQData服务获取数据
        price_data = await jqdata_service.get_price_data(
            user_id=current_user.id,
            db=db,
            symbols=price_request.symbols,
            start_date=price_request.start_date,
            end_date=price_request.end_date,
            frequency=price_request.frequency
        )
        
        # 转换为响应格式
        price_responses = []
        for _, row in price_data.iterrows():
            price_responses.append(PriceDataResponse(
                symbol=row.get('symbol', ''),
                datetime=str(row.name) if hasattr(row, 'name') else '',
                open_price=row.get('open'),
                high_price=row.get('high'),
                low_price=row.get('low'),
                close_price=row.get('close'),
                volume=row.get('volume'),
                turnover=row.get('money'),
                adj_close=row.get('close'),  # 简化处理
                change_pct=None,  # 需要计算
                turnover_rate=None,  # 需要计算
                is_trading_day=True,
                is_suspended=False
            ))
        
        return BaseResponse(
            code=200,
            message="获取价格数据成功",
            data=price_responses
        )
        
    except Exception as e:
        logger.error(f"获取价格数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取价格数据失败: {str(e)}"
        )


@router.get("/price-data/{symbol}/latest", response_model=BaseResponse[PriceDataResponse])
async def get_latest_price(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取最新价格"""
    try:
        # 从数据库获取最新价格
        query = (
            select(DailyPrice)
            .where(DailyPrice.symbol == symbol)
            .order_by(desc(DailyPrice.trade_date))
            .limit(1)
        )
        
        result = await db.execute(query)
        latest_price = result.scalar_one_or_none()
        
        if not latest_price:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"股票 {symbol} 的价格数据不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取最新价格成功",
            data=PriceDataResponse.from_orm(latest_price)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取最新价格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取最新价格失败"
        )


# =============================================================================
# 市场概览
# =============================================================================

@router.get("/overview", response_model=BaseResponse[MarketOverviewResponse])
async def get_market_overview(
    market: str = Query("A", regex="^(A|HK|US)$", description="市场类型"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取市场概览"""
    try:
        # 查询股票统计
        stock_stats_query = (
            select(
                func.count(Stock.id).label('total_stocks'),
                func.count(Stock.id).filter(Stock.is_active == True).label('trading_stocks'),
                func.count(Stock.id).filter(Stock.is_active == False).label('suspended_stocks')
            )
            .where(Stock.market == market)
        )
        
        result = await db.execute(stock_stats_query)
        stats = result.first()
        
        # 构建市场概览响应
        overview = MarketOverviewResponse(
            market=market,
            total_stocks=stats.total_stocks or 0,
            trading_stocks=stats.trading_stocks or 0,
            suspended_stocks=stats.suspended_stocks or 0,
            up_stocks=0,  # 需要从价格数据计算
            down_stocks=0,  # 需要从价格数据计算
            flat_stocks=0,  # 需要从价格数据计算
            total_volume=None,
            total_turnover=None,
            avg_change_pct=None,
            last_update=datetime.utcnow()
        )
        
        return BaseResponse(
            code=200,
            message="获取市场概览成功",
            data=overview
        )
        
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取市场概览失败"
        )


# =============================================================================
# 数据同步
# =============================================================================

@router.post("/sync/stocks", response_model=BaseResponse[Dict[str, Any]])
async def sync_stock_list(
    market: str = Query("A", regex="^(A|HK|US)$", description="市场类型"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """同步股票列表"""
    try:
        # 使用JQData服务获取股票列表
        stock_data = await jqdata_service.get_stock_list(
            user_id=current_user.id,
            db=db,
            market=market
        )
        
        # 这里应该将数据保存到数据库
        # 暂时返回成功响应
        
        return BaseResponse(
            code=200,
            message="股票列表同步成功",
            data={
                "market": market,
                "synced_count": len(stock_data),
                "sync_time": datetime.utcnow().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"同步股票列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步股票列表失败: {str(e)}"
        )
