'use client';

/**
 * 投资组合概览页面
 * 
 * 显示投资组合总览、持仓分布、收益分析等
 */

import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Table,
  Progress,
  Typography,
  Space,
  Button,
  Tag,
  Tooltip,
  Select,
  DatePicker,
  Alert,
  Empty,
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  TrophyOutlined,
  PieChartOutlined,
  LineChartOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';

import { useAuth } from '@/store/auth';
import { PortfolioChart } from '@/components/charts/PortfolioChart';
import { AssetAllocationChart } from '@/components/charts/AssetAllocationChart';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface Position {
  id: string;
  symbol: string;
  name: string;
  quantity: number;
  avgCost: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPct: number;
  weight: number;
  sector: string;
  lastUpdate: string;
}

interface PortfolioSummary {
  totalValue: number;
  totalCost: number;
  totalPnL: number;
  totalPnLPct: number;
  dayPnL: number;
  dayPnLPct: number;
  cashBalance: number;
  positionCount: number;
  diversificationScore: number;
}

export default function PortfolioOverviewPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('1M');
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({
    totalValue: 125680.50,
    totalCost: 110000.00,
    totalPnL: 15680.50,
    totalPnLPct: 14.25,
    dayPnL: 1234.56,
    dayPnLPct: 0.99,
    cashBalance: 25680.50,
    positionCount: 8,
    diversificationScore: 75,
  });
  
  const [positions, setPositions] = useState<Position[]>([
    {
      id: '1',
      symbol: '000001.XSHE',
      name: '平安银行',
      quantity: 1000,
      avgCost: 12.20,
      currentPrice: 12.45,
      marketValue: 12450,
      unrealizedPnL: 250,
      unrealizedPnLPct: 2.05,
      weight: 9.9,
      sector: '金融',
      lastUpdate: '2024-12-22 15:00:00',
    },
    {
      id: '2',
      symbol: '600036.XSHG',
      name: '招商银行',
      quantity: 500,
      avgCost: 40.80,
      currentPrice: 42.18,
      marketValue: 21090,
      unrealizedPnL: 690,
      unrealizedPnLPct: 3.38,
      weight: 16.8,
      sector: '金融',
      lastUpdate: '2024-12-22 15:00:00',
    },
    {
      id: '3',
      symbol: '600519.XSHG',
      name: '贵州茅台',
      quantity: 10,
      avgCost: 1650.00,
      currentPrice: 1678.50,
      marketValue: 16785,
      unrealizedPnL: 285,
      unrealizedPnLPct: 1.73,
      weight: 13.4,
      sector: '消费',
      lastUpdate: '2024-12-22 15:00:00',
    },
    {
      id: '4',
      symbol: '000858.XSHE',
      name: '五粮液',
      quantity: 100,
      avgCost: 158.20,
      currentPrice: 162.45,
      marketValue: 16245,
      unrealizedPnL: 425,
      unrealizedPnLPct: 2.69,
      weight: 12.9,
      sector: '消费',
      lastUpdate: '2024-12-22 15:00:00',
    },
  ]);

  // 持仓表格列定义
  const positionColumns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Position) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.symbol}</div>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'right' as const,
      render: (quantity: number) => (
        <Text className="font-mono">{quantity.toLocaleString()}</Text>
      ),
    },
    {
      title: '成本价',
      dataIndex: 'avgCost',
      key: 'avgCost',
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '市值',
      dataIndex: 'marketValue',
      key: 'marketValue',
      align: 'right' as const,
      render: (value: number) => (
        <Text className="font-mono">¥{value.toLocaleString()}</Text>
      ),
    },
    {
      title: '盈亏',
      dataIndex: 'unrealizedPnL',
      key: 'unrealizedPnL',
      align: 'right' as const,
      render: (pnl: number, record: Position) => (
        <div className="text-right">
          <div className={`font-mono ${pnl >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {pnl >= 0 ? '+' : ''}¥{pnl.toFixed(2)}
          </div>
          <div className={`text-xs font-mono ${record.unrealizedPnLPct >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {record.unrealizedPnLPct >= 0 ? '+' : ''}{record.unrealizedPnLPct.toFixed(2)}%
          </div>
        </div>
      ),
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'right' as const,
      render: (weight: number) => (
        <div className="text-right">
          <Text className="font-mono">{weight.toFixed(1)}%</Text>
          <Progress
            percent={weight}
            showInfo={false}
            size="small"
            strokeColor="#1890ff"
            className="mt-1"
          />
        </div>
      ),
    },
    {
      title: '行业',
      dataIndex: 'sector',
      key: 'sector',
      render: (sector: string) => (
        <Tag color="blue">{sector}</Tag>
      ),
    },
  ];

  // 刷新数据
  const handleRefresh = async () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // 导出报告
  const handleExport = () => {
    // 实现导出功能
    console.log('导出投资组合报告');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="!mb-2">
            投资组合概览
          </Title>
          <Text type="secondary">
            查看您的投资组合表现和持仓分布
          </Text>
        </div>
        
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 100 }}
          >
            <Option value="1D">1日</Option>
            <Option value="1W">1周</Option>
            <Option value="1M">1月</Option>
            <Option value="3M">3月</Option>
            <Option value="1Y">1年</Option>
          </Select>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出报告
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总资产"
              value={portfolioSummary.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收益"
              value={portfolioSummary.totalPnL}
              precision={2}
              prefix="¥"
              valueStyle={{ 
                color: portfolioSummary.totalPnL >= 0 ? '#cf1322' : '#389e0d',
                fontSize: '24px'
              }}
              suffix={
                <span className="text-sm">
                  ({portfolioSummary.totalPnLPct >= 0 ? '+' : ''}{portfolioSummary.totalPnLPct.toFixed(2)}%)
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收益"
              value={portfolioSummary.dayPnL}
              precision={2}
              prefix="¥"
              valueStyle={{ 
                color: portfolioSummary.dayPnL >= 0 ? '#cf1322' : '#389e0d',
                fontSize: '24px'
              }}
              suffix={
                <span className="text-sm">
                  ({portfolioSummary.dayPnLPct >= 0 ? '+' : ''}{portfolioSummary.dayPnLPct.toFixed(2)}%)
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="现金余额"
              value={portfolioSummary.cashBalance}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card
            title="收益曲线"
            extra={
              <Space>
                <Button type="text" size="small" icon={<SettingOutlined />} />
              </Space>
            }
          >
            <PortfolioChart timeRange={timeRange} />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="资产配置">
            <AssetAllocationChart data={positions} />
            
            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <Text type="secondary">持仓数量:</Text>
                <Text strong>{portfolioSummary.positionCount} 只</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">分散化评分:</Text>
                <div className="flex items-center space-x-2">
                  <Progress
                    percent={portfolioSummary.diversificationScore}
                    size="small"
                    style={{ width: 60 }}
                  />
                  <Text strong>{portfolioSummary.diversificationScore}</Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 持仓明细 */}
      <Card
        title="持仓明细"
        extra={
          <Space>
            <Text type="secondary">
              更新时间: {dayjs().format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          </Space>
        }
      >
        <Table
          columns={positionColumns}
          dataSource={positions}
          rowKey="id"
          pagination={false}
          size="small"
          summary={(pageData) => {
            const totalMarketValue = pageData.reduce((sum, record) => sum + record.marketValue, 0);
            const totalPnL = pageData.reduce((sum, record) => sum + record.unrealizedPnL, 0);
            const totalPnLPct = totalPnL / (totalMarketValue - totalPnL) * 100;

            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>
                  <Text strong>合计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} />
                <Table.Summary.Cell index={2} />
                <Table.Summary.Cell index={3} />
                <Table.Summary.Cell index={4}>
                  <Text strong className="font-mono">¥{totalMarketValue.toLocaleString()}</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <div className="text-right">
                    <div className={`font-mono font-bold ${totalPnL >= 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {totalPnL >= 0 ? '+' : ''}¥{totalPnL.toFixed(2)}
                    </div>
                    <div className={`text-xs font-mono ${totalPnLPct >= 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {totalPnLPct >= 0 ? '+' : ''}{totalPnLPct.toFixed(2)}%
                    </div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <Text strong className="font-mono">100.0%</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7} />
              </Table.Summary.Row>
            );
          }}
        />
      </Card>

      {/* 风险提示 */}
      <Alert
        message="投资风险提示"
        description="投资有风险，入市需谨慎。过往业绩不代表未来表现，请根据自身风险承受能力谨慎投资。"
        type="warning"
        showIcon
        closable
      />
    </div>
  );
}
