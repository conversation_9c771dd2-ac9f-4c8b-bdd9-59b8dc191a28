'use client';

/**
 * 投资组合概览页面
 * 
 * 显示投资组合总览、持仓分布、收益分析等
 */

import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Table,
  Progress,
  Typography,
  Space,
  Button,
  Tag,
  Tooltip,
  Select,
  DatePicker,
  Alert,
  Empty,
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  TrophyOutlined,
  PieChartOutlined,
  LineChartOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';

import { useAuth } from '@/store/auth';
import { apiClient } from '@/lib/api';
import { PortfolioChart } from '@/components/charts/PortfolioChart';
import { AssetAllocationChart } from '@/components/charts/AssetAllocationChart';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface Position {
  id: string;
  symbol: string;
  name: string;
  quantity: number;
  avgCost: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPct: number;
  weight: number;
  sector: string;
  lastUpdate: string;
}

interface PortfolioSummary {
  totalValue: number;
  totalCost: number;
  totalPnL: number;
  totalPnLPct: number;
  dayPnL: number;
  dayPnLPct: number;
  cashBalance: number;
  positionCount: number;
  diversificationScore: number;
}

export default function PortfolioOverviewPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('1M');
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({
    totalValue: 0,
    totalCost: 0,
    totalPnL: 0,
    totalPnLPct: 0,
    dayPnL: 0,
    dayPnLPct: 0,
    cashBalance: 0,
    positionCount: 0,
    diversificationScore: 0,
  });

  const [positions, setPositions] = useState<Position[]>([]);

  // 加载投资组合数据
  const loadPortfolioData = async () => {
    try {
      setLoading(true);

      // 获取投资组合概览
      const summaryResponse = await apiClient.get('/portfolio/summary');
      if (summaryResponse.code === 200 && summaryResponse.data) {
        setPortfolioSummary(summaryResponse.data);
      }

      // 获取持仓明细
      const positionsResponse = await apiClient.get('/portfolio/positions');
      if (positionsResponse.code === 200 && positionsResponse.data?.items) {
        setPositions(positionsResponse.data.items);
      }

    } catch (error: any) {
      console.error('加载投资组合数据失败:', error);
      message.error('加载投资组合数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据加载
  useEffect(() => {
    loadPortfolioData();
  }, []);

  // 监听时间范围变化
  useEffect(() => {
    if (timeRange) {
      loadPortfolioData();
    }
  }, [timeRange]);

  // 持仓表格列定义
  const positionColumns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Position) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.symbol}</div>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      align: 'right' as const,
      render: (quantity: number) => (
        <Text className="font-mono">{quantity.toLocaleString()}</Text>
      ),
    },
    {
      title: '成本价',
      dataIndex: 'avgCost',
      key: 'avgCost',
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '市值',
      dataIndex: 'marketValue',
      key: 'marketValue',
      align: 'right' as const,
      render: (value: number) => (
        <Text className="font-mono">¥{value.toLocaleString()}</Text>
      ),
    },
    {
      title: '盈亏',
      dataIndex: 'unrealizedPnL',
      key: 'unrealizedPnL',
      align: 'right' as const,
      render: (pnl: number, record: Position) => (
        <div className="text-right">
          <div className={`font-mono ${pnl >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {pnl >= 0 ? '+' : ''}¥{pnl.toFixed(2)}
          </div>
          <div className={`text-xs font-mono ${record.unrealizedPnLPct >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {record.unrealizedPnLPct >= 0 ? '+' : ''}{record.unrealizedPnLPct.toFixed(2)}%
          </div>
        </div>
      ),
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'right' as const,
      render: (weight: number) => (
        <div className="text-right">
          <Text className="font-mono">{weight.toFixed(1)}%</Text>
          <Progress
            percent={weight}
            showInfo={false}
            size="small"
            strokeColor="#1890ff"
            className="mt-1"
          />
        </div>
      ),
    },
    {
      title: '行业',
      dataIndex: 'sector',
      key: 'sector',
      render: (sector: string) => (
        <Tag color="blue">{sector}</Tag>
      ),
    },
  ];

  // 刷新数据
  const handleRefresh = async () => {
    await loadPortfolioData();
  };

  // 导出报告
  const handleExport = () => {
    // 实现导出功能
    console.log('导出投资组合报告');
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="!mb-2">
            投资组合概览
          </Title>
          <Text type="secondary">
            查看您的投资组合表现和持仓分布
          </Text>
        </div>
        
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 100 }}
          >
            <Option value="1D">1日</Option>
            <Option value="1W">1周</Option>
            <Option value="1M">1月</Option>
            <Option value="3M">3月</Option>
            <Option value="1Y">1年</Option>
          </Select>
          <Button
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出报告
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总资产"
              value={portfolioSummary.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收益"
              value={portfolioSummary.totalPnL}
              precision={2}
              prefix="¥"
              valueStyle={{ 
                color: portfolioSummary.totalPnL >= 0 ? '#cf1322' : '#389e0d',
                fontSize: '24px'
              }}
              suffix={
                <span className="text-sm">
                  ({portfolioSummary.totalPnLPct >= 0 ? '+' : ''}{portfolioSummary.totalPnLPct.toFixed(2)}%)
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收益"
              value={portfolioSummary.dayPnL}
              precision={2}
              prefix="¥"
              valueStyle={{ 
                color: portfolioSummary.dayPnL >= 0 ? '#cf1322' : '#389e0d',
                fontSize: '24px'
              }}
              suffix={
                <span className="text-sm">
                  ({portfolioSummary.dayPnLPct >= 0 ? '+' : ''}{portfolioSummary.dayPnLPct.toFixed(2)}%)
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="现金余额"
              value={portfolioSummary.cashBalance}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card
            title="收益曲线"
            extra={
              <Space>
                <Button type="text" size="small" icon={<SettingOutlined />} />
              </Space>
            }
          >
            <PortfolioChart timeRange={timeRange} />
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="资产配置">
            <AssetAllocationChart data={positions} />
            
            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <Text type="secondary">持仓数量:</Text>
                <Text strong>{portfolioSummary.positionCount} 只</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">分散化评分:</Text>
                <div className="flex items-center space-x-2">
                  <Progress
                    percent={portfolioSummary.diversificationScore}
                    size="small"
                    style={{ width: 60 }}
                  />
                  <Text strong>{portfolioSummary.diversificationScore}</Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 持仓明细 */}
      <Card
        title="持仓明细"
        extra={
          <Space>
            <Text type="secondary">
              更新时间: {dayjs().format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          </Space>
        }
      >
        <Table
          columns={positionColumns}
          dataSource={positions}
          rowKey="id"
          pagination={false}
          size="small"
          summary={(pageData) => {
            const totalMarketValue = pageData.reduce((sum, record) => sum + record.marketValue, 0);
            const totalPnL = pageData.reduce((sum, record) => sum + record.unrealizedPnL, 0);
            const totalPnLPct = totalPnL / (totalMarketValue - totalPnL) * 100;

            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0}>
                  <Text strong>合计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} />
                <Table.Summary.Cell index={2} />
                <Table.Summary.Cell index={3} />
                <Table.Summary.Cell index={4}>
                  <Text strong className="font-mono">¥{totalMarketValue.toLocaleString()}</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}>
                  <div className="text-right">
                    <div className={`font-mono font-bold ${totalPnL >= 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {totalPnL >= 0 ? '+' : ''}¥{totalPnL.toFixed(2)}
                    </div>
                    <div className={`text-xs font-mono ${totalPnLPct >= 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {totalPnLPct >= 0 ? '+' : ''}{totalPnLPct.toFixed(2)}%
                    </div>
                  </div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={6}>
                  <Text strong className="font-mono">100.0%</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7} />
              </Table.Summary.Row>
            );
          }}
        />
      </Card>

      {/* 风险提示 */}
      <Alert
        message="投资风险提示"
        description="投资有风险，入市需谨慎。过往业绩不代表未来表现，请根据自身风险承受能力谨慎投资。"
        type="warning"
        showIcon
        closable
      />
    </div>
  );
}
