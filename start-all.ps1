# ============================================================================
# 智能量化交易平台 - 一键启动脚本 (前端 + 后端)
# ============================================================================

Write-Host "🚀 启动智能量化交易平台 (前端 + 后端)..." -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Cyan

# 检查是否在项目根目录
if (-not (Test-Path "backend") -or -not (Test-Path "frontend")) {
    Write-Host "❌ 错误: 请在项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "💡 当前目录应包含 backend 和 frontend 文件夹" -ForegroundColor Yellow
    exit 1
}

# 函数：启动后端服务
function Start-Backend {
    Write-Host "🔧 启动后端服务..." -ForegroundColor Yellow

    # 在新的PowerShell窗口中启动后端
    $backendScript = Join-Path $PWD "start-backend.ps1"
    Start-Process powershell -ArgumentList "-NoExit", "-File", $backendScript -WindowStyle Normal

    Write-Host "✅ 后端服务启动中..." -ForegroundColor Green
    Write-Host "📍 后端地址: http://localhost:8000" -ForegroundColor Cyan
}

# 函数：启动前端服务
function Start-Frontend {
    Write-Host "🎨 启动前端服务..." -ForegroundColor Yellow

    # 等待后端启动
    Write-Host "⏳ 等待后端服务启动 (10秒)..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10

    # 在新的PowerShell窗口中启动前端
    $frontendScript = Join-Path $PWD "start-frontend.ps1"
    Start-Process powershell -ArgumentList "-NoExit", "-File", $frontendScript -WindowStyle Normal

    Write-Host "✅ 前端服务启动中..." -ForegroundColor Green
    Write-Host "📍 前端地址: http://localhost:3000" -ForegroundColor Cyan
}

# 主执行流程
try {
    # 启动后端
    Start-Backend

    # 启动前端
    Start-Frontend

    Write-Host ""
    Write-Host "🎉 智能量化交易平台启动完成!" -ForegroundColor Green
    Write-Host "============================================" -ForegroundColor Cyan
    Write-Host "📍 前端地址: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "📍 后端地址: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "📖 API文档: http://localhost:8000/docs" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 提示:" -ForegroundColor Yellow
    Write-Host "  - 前端和后端在独立的窗口中运行" -ForegroundColor White
    Write-Host "  - 关闭对应窗口可停止服务" -ForegroundColor White
    Write-Host "  - 首次启动可能需要安装依赖，请耐心等待" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 按任意键退出此窗口..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

} catch {
    Write-Host "❌ 启动失败: $_" -ForegroundColor Red
    Write-Host "💡 请检查错误信息并重试" -ForegroundColor Yellow
    exit 1
}
