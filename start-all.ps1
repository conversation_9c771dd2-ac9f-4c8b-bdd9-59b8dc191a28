# ============================================================================
# 智能量化交易平台 - 一键启动脚本 (前端 + 后端)
# ============================================================================

Write-Host "Starting Smart Quantitative Trading Platform (Frontend + Backend)..." -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Cyan

# Check if in project root directory
if (-not (Test-Path "backend") -or -not (Test-Path "frontend")) {
    Write-Host "ERROR: Please run this script in project root directory" -ForegroundColor Red
    Write-Host "Current directory should contain backend and frontend folders" -ForegroundColor Yellow
    exit 1
}

# Function: Start backend service
function Start-Backend {
    Write-Host "Starting backend service..." -ForegroundColor Yellow

    # Start backend in new PowerShell window
    $backendScript = Join-Path $PWD "start-backend-en.ps1"
    Start-Process powershell -ArgumentList "-NoExit", "-File", $backendScript -WindowStyle Normal

    Write-Host "Backend service starting..." -ForegroundColor Green
    Write-Host "Backend URL: http://localhost:8000" -ForegroundColor Cyan
}

# Function: Start frontend service
function Start-Frontend {
    Write-Host "Starting frontend service..." -ForegroundColor Yellow

    # Wait for backend to start
    Write-Host "Waiting for backend service to start (10 seconds)..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10

    # Start frontend in new PowerShell window
    $frontendScript = Join-Path $PWD "start-frontend.ps1"
    Start-Process powershell -ArgumentList "-NoExit", "-File", $frontendScript -WindowStyle Normal

    Write-Host "Frontend service starting..." -ForegroundColor Green
    Write-Host "Frontend URL: http://localhost:3000" -ForegroundColor Cyan
}

# Main execution flow
try {
    # Start backend
    Start-Backend

    # Start frontend
    Start-Frontend

    Write-Host ""
    Write-Host "Smart Quantitative Trading Platform started successfully!" -ForegroundColor Green
    Write-Host "============================================" -ForegroundColor Cyan
    Write-Host "Frontend URL: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "Backend URL: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Tips:" -ForegroundColor Yellow
    Write-Host "  - Frontend and backend run in separate windows" -ForegroundColor White
    Write-Host "  - Close corresponding window to stop service" -ForegroundColor White
    Write-Host "  - First startup may need to install dependencies, please wait" -ForegroundColor White
    Write-Host ""
    Write-Host "Press any key to exit this window..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

} catch {
    Write-Host "Startup failed: $_" -ForegroundColor Red
    Write-Host "Please check error messages and retry" -ForegroundColor Yellow
    exit 1
}
