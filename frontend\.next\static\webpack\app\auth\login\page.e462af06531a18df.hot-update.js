"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useAuthActions: function() { return /* binding */ useAuthActions; },\n/* harmony export */   useAuthStore: function() { return /* binding */ useAuthStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/**\n * 认证状态管理\n * \n * 使用Zustand管理用户认证状态，包括登录、登出、token管理等\n */ \n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // 初始状态\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        loadingState: \"idle\",\n        // 登录\n        login: async (loginData)=>{\n            try {\n                console.log(\"\\uD83D\\uDD10 Auth Store: 开始登录流程...\", loginData);\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                // 使用表单数据格式，因为后端使用 OAuth2PasswordRequestForm\n                const formData = new FormData();\n                formData.append(\"username\", loginData.email);\n                formData.append(\"password\", loginData.password);\n                console.log(\"\\uD83D\\uDCE1 Auth Store: 发送API请求...\");\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(\"/api/v1/auth/login\", formData, {\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\"\n                    }\n                });\n                console.log(\"\\uD83D\\uDCE5 Auth Store: 收到响应:\", response.data);\n                // 处理axios直接响应\n                const responseData = response.data;\n                if (responseData.code === 200 && responseData.data) {\n                    const { access_token, refresh_token, token_type, expires_in, user_info } = responseData.data;\n                    console.log(\"✅ Auth Store: 解析用户数据成功:\", user_info);\n                    const tokens = {\n                        accessToken: access_token,\n                        refreshToken: refresh_token || access_token,\n                        tokenType: token_type,\n                        expiresIn: expires_in\n                    };\n                    // 设置认证信息\n                    console.log(\"\\uD83D\\uDD11 Auth Store: 设置认证令牌...\");\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n                    console.log(\"\\uD83D\\uDCBE Auth Store: 更新状态...\");\n                    set({\n                        user: user_info,\n                        tokens,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    console.log(\"\\uD83C\\uDF89 Auth Store: 登录流程完成！\");\n                } else {\n                    console.error(\"❌ Auth Store: 响应格式错误:\", responseData);\n                    throw new Error(responseData.message || \"登录失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\",\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"登录失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 注册\n        register: async (registerData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/register\", registerData);\n                if (response.code === 200) {\n                    set({\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"注册成功！请登录您的账号\");\n                } else {\n                    throw new Error(response.message || \"注册失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"注册失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 登出\n        logout: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                // 调用登出API\n                await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/logout\");\n            } catch (error) {\n                console.error(\"登出API调用失败:\", error);\n            } finally{\n                // 无论API调用是否成功，都清理本地状态\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n                set({\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    loadingState: \"idle\"\n                });\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已安全登出\");\n            }\n        },\n        // 刷新Token\n        refreshToken: async ()=>{\n            try {\n                const { tokens } = get();\n                if (!(tokens === null || tokens === void 0 ? void 0 : tokens.refreshToken)) {\n                    throw new Error(\"没有刷新Token\");\n                }\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/refresh\", {\n                    refresh_token: tokens.refreshToken\n                });\n                if (response.code === 200 && response.data) {\n                    const newTokens = {\n                        accessToken: response.data.access_token,\n                        refreshToken: tokens.refreshToken,\n                        tokenType: response.data.token_type,\n                        expiresIn: response.data.expires_in\n                    };\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(newTokens);\n                    set({\n                        tokens: newTokens\n                    });\n                } else {\n                    throw new Error(\"Token刷新失败\");\n                }\n            } catch (error) {\n                console.error(\"Token刷新失败:\", error);\n                // Token刷新失败，清理认证状态\n                get().clearAuth();\n                throw error;\n            }\n        },\n        // 获取当前用户信息\n        getCurrentUser: async ()=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/auth/me\");\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                } else {\n                    throw new Error(\"获取用户信息失败\");\n                }\n            } catch (error) {\n                var _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                // 如果是401错误，清理认证状态\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                    get().clearAuth();\n                }\n                throw error;\n            }\n        },\n        // 更新用户资料\n        updateProfile: async (profileData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/api/v1/auth/profile\", profileData);\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"资料更新成功\");\n                } else {\n                    throw new Error(response.message || \"更新失败\");\n                }\n            } catch (error) {\n                var _error_response_data, _error_response;\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || \"更新失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 设置用户\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        // 设置Token\n        setTokens: (tokens)=>{\n            set({\n                tokens\n            });\n            if (tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n            } else {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            }\n        },\n        // 设置加载状态\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        // 设置加载状态\n        setLoadingState: (loadingState)=>{\n            set({\n                loadingState\n            });\n        },\n        // 清理认证状态\n        clearAuth: ()=>{\n            _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                isLoading: false,\n                loadingState: \"idle\"\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            // 恢复状态后，设置API客户端的认证信息\n            if (state === null || state === void 0 ? void 0 : state.tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(state.tokens);\n            }\n        }\n}));\n// 导出选择器函数\nconst useAuth = ()=>{\n    const { user, isAuthenticated, isLoading } = useAuthStore();\n    return {\n        user,\n        isAuthenticated,\n        isLoading\n    };\n};\nconst useAuthActions = ()=>{\n    const { login, register, logout, refreshToken, getCurrentUser, updateProfile } = useAuthStore();\n    return {\n        login,\n        register,\n        logout,\n        refreshToken,\n        getCurrentUser,\n        updateProfile\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/auth.ts\n"));

/***/ })

});