"""
深度学习相关API端点

提供Transformer、GAN、强化学习等深度学习模型的API接口
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
# from app.models.deep_learning import TransformerModel, GANModel, RLAgent, TransformerPrediction, GANGeneratedData, RLTradingSession
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.transformer_service import transformer_service
from app.services.gan_service import gan_service
from app.services.rl_service import rl_service

router = APIRouter()


# =============================================================================
# Transformer时间序列预测
# =============================================================================

@router.get("/transformer/models", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_transformer_models(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取Transformer模型列表"""
    try:
        # 查询总数
        count_query = select(func.count(TransformerModel.id)).where(
            TransformerModel.user_id == current_user.id
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(TransformerModel)
            .where(TransformerModel.user_id == current_user.id)
            .order_by(desc(TransformerModel.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        models = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "model_type": model.model_type,
                "status": model.status,
                "sequence_length": model.sequence_length,
                "prediction_length": model.prediction_length,
                "d_model": model.d_model,
                "n_heads": model.n_heads,
                "n_layers": model.n_layers,
                "training_progress": model.training_progress,
                "test_mse": float(model.test_mse) if model.test_mse else None,
                "test_mae": float(model.test_mae) if model.test_mae else None,
                "created_at": model.created_at.isoformat(),
                "trained_at": model.trained_at.isoformat() if model.trained_at else None,
            })
        
        return BaseResponse(
            code=200,
            message="获取Transformer模型列表成功",
            data=PaginatedResponse(
                items=model_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取Transformer模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.post("/transformer/models", response_model=BaseResponse[dict])
async def create_transformer_model(
    model_config: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建Transformer模型"""
    try:
        model = await transformer_service.create_model(
            current_user.id, model_config, db
        )
        
        return BaseResponse(
            code=200,
            message="创建Transformer模型成功",
            data={
                "model_id": model.id,
                "name": model.name,
                "status": model.status
            }
        )
        
    except Exception as e:
        logger.error(f"创建Transformer模型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建模型失败"
        )


@router.post("/transformer/models/{model_id}/train", response_model=BaseResponse[dict])
async def train_transformer_model(
    model_id: int,
    training_data: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """训练Transformer模型"""
    try:
        # 验证模型所有权
        result = await db.execute(
            select(TransformerModel).where(
                and_(
                    TransformerModel.id == model_id,
                    TransformerModel.user_id == current_user.id
                )
            )
        )
        model = result.scalar_one_or_none()
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模型不存在"
            )
        
        # 添加后台训练任务
        background_tasks.add_task(
            _train_transformer_task,
            model_id,
            training_data,
            db
        )
        
        return BaseResponse(
            code=200,
            message="Transformer训练任务已提交",
            data={
                "model_id": model_id,
                "task_submitted": True,
                "estimated_time": "30-120分钟"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交Transformer训练任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交训练任务失败"
        )


@router.post("/transformer/models/{model_id}/predict", response_model=BaseResponse[dict])
async def transformer_predict(
    model_id: int,
    prediction_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Transformer预测"""
    try:
        result = await transformer_service.predict(model_id, prediction_data, db)
        
        if result["success"]:
            return BaseResponse(
                code=200,
                message="预测完成",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Transformer预测失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="预测失败"
        )


# =============================================================================
# GAN生成对抗网络
# =============================================================================

@router.get("/gan/models", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_gan_models(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取GAN模型列表"""
    try:
        # 查询总数
        count_query = select(func.count(GANModel.id)).where(
            GANModel.user_id == current_user.id
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(GANModel)
            .where(GANModel.user_id == current_user.id)
            .order_by(desc(GANModel.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        models = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "name": model.name,
                "description": model.description,
                "gan_type": model.gan_type,
                "status": model.status,
                "training_progress": model.training_progress,
                "generator_loss": float(model.generator_loss) if model.generator_loss else None,
                "discriminator_loss": float(model.discriminator_loss) if model.discriminator_loss else None,
                "inception_score": float(model.inception_score) if model.inception_score else None,
                "fid_score": float(model.fid_score) if model.fid_score else None,
                "created_at": model.created_at.isoformat(),
                "trained_at": model.trained_at.isoformat() if model.trained_at else None,
            })
        
        return BaseResponse(
            code=200,
            message="获取GAN模型列表成功",
            data=PaginatedResponse(
                items=model_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取GAN模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取模型列表失败"
        )


@router.post("/gan/models", response_model=BaseResponse[dict])
async def create_gan_model(
    model_config: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建GAN模型"""
    try:
        model = await gan_service.create_model(
            current_user.id, model_config, db
        )
        
        return BaseResponse(
            code=200,
            message="创建GAN模型成功",
            data={
                "model_id": model.id,
                "name": model.name,
                "status": model.status
            }
        )
        
    except Exception as e:
        logger.error(f"创建GAN模型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建模型失败"
        )


@router.post("/gan/models/{model_id}/generate", response_model=BaseResponse[dict])
async def generate_data(
    model_id: int,
    generation_config: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """生成数据"""
    try:
        result = await gan_service.generate_data(model_id, generation_config, db)
        
        if result["success"]:
            return BaseResponse(
                code=200,
                message="数据生成完成",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"GAN数据生成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="数据生成失败"
        )


# =============================================================================
# 强化学习
# =============================================================================

@router.get("/rl/agents", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_rl_agents(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取强化学习智能体列表"""
    try:
        # 查询总数
        count_query = select(func.count(RLAgent.id)).where(
            RLAgent.user_id == current_user.id
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(RLAgent)
            .where(RLAgent.user_id == current_user.id)
            .order_by(desc(RLAgent.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        agents = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        agent_list = []
        for agent in agents:
            agent_list.append({
                "id": agent.id,
                "name": agent.name,
                "description": agent.description,
                "agent_type": agent.agent_type,
                "environment_type": agent.environment_type,
                "status": agent.status,
                "training_episodes": agent.training_episodes,
                "average_reward": float(agent.average_reward) if agent.average_reward else None,
                "max_reward": float(agent.max_reward) if agent.max_reward else None,
                "sharpe_ratio": float(agent.sharpe_ratio) if agent.sharpe_ratio else None,
                "max_drawdown": float(agent.max_drawdown) if agent.max_drawdown else None,
                "created_at": agent.created_at.isoformat(),
                "last_trained_at": agent.last_trained_at.isoformat() if agent.last_trained_at else None,
            })
        
        return BaseResponse(
            code=200,
            message="获取强化学习智能体列表成功",
            data=PaginatedResponse(
                items=agent_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取强化学习智能体列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取智能体列表失败"
        )


@router.post("/rl/agents", response_model=BaseResponse[dict])
async def create_rl_agent(
    agent_config: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建强化学习智能体"""
    try:
        agent = await rl_service.create_agent(
            current_user.id, agent_config, db
        )
        
        return BaseResponse(
            code=200,
            message="创建强化学习智能体成功",
            data={
                "agent_id": agent.id,
                "name": agent.name,
                "status": agent.status
            }
        )
        
    except Exception as e:
        logger.error(f"创建强化学习智能体失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建智能体失败"
        )


@router.post("/rl/agents/{agent_id}/train", response_model=BaseResponse[dict])
async def train_rl_agent(
    agent_id: int,
    training_data: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """训练强化学习智能体"""
    try:
        # 验证智能体所有权
        result = await db.execute(
            select(RLAgent).where(
                and_(
                    RLAgent.id == agent_id,
                    RLAgent.user_id == current_user.id
                )
            )
        )
        agent = result.scalar_one_or_none()
        
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="智能体不存在"
            )
        
        # 添加后台训练任务
        background_tasks.add_task(
            _train_rl_task,
            agent_id,
            training_data,
            db
        )
        
        return BaseResponse(
            code=200,
            message="强化学习训练任务已提交",
            data={
                "agent_id": agent_id,
                "task_submitted": True,
                "estimated_time": "60-300分钟"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交强化学习训练任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交训练任务失败"
        )


# =============================================================================
# 私有函数
# =============================================================================

async def _train_transformer_task(
    model_id: int, 
    training_data: Dict[str, Any], 
    db: AsyncSession
):
    """Transformer训练后台任务"""
    try:
        result = await transformer_service.train_model(model_id, training_data, db)
        if result["success"]:
            logger.info(f"Transformer模型训练完成: {model_id}")
        else:
            logger.error(f"Transformer模型训练失败: {model_id}, {result['error']}")
        
    except Exception as e:
        logger.error(f"Transformer训练任务失败: {e}")


async def _train_gan_task(
    model_id: int, 
    training_data: Dict[str, Any], 
    db: AsyncSession
):
    """GAN训练后台任务"""
    try:
        result = await gan_service.train_model(model_id, training_data, db)
        if result["success"]:
            logger.info(f"GAN模型训练完成: {model_id}")
        else:
            logger.error(f"GAN模型训练失败: {model_id}, {result['error']}")
        
    except Exception as e:
        logger.error(f"GAN训练任务失败: {e}")


async def _train_rl_task(
    agent_id: int, 
    training_data: Dict[str, Any], 
    db: AsyncSession
):
    """强化学习训练后台任务"""
    try:
        result = await rl_service.train_agent(agent_id, training_data, db)
        if result["success"]:
            logger.info(f"强化学习智能体训练完成: {agent_id}")
        else:
            logger.error(f"强化学习智能体训练失败: {agent_id}, {result['error']}")
        
    except Exception as e:
        logger.error(f"强化学习训练任务失败: {e}")
