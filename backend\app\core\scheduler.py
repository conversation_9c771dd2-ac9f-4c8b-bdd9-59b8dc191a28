"""
策略调度器

负责管理策略的定时执行、任务调度等功能
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from app.core.logging import logger


class StrategyScheduler:
    """策略调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.jobs: Dict[str, Any] = {}
        self.is_running = False
    
    async def start(self):
        """启动调度器"""
        if not self.is_running:
            self.scheduler.start()
            self.is_running = True
            logger.info("Strategy scheduler started")
    
    async def stop(self):
        """停止调度器"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("Strategy scheduler stopped")
    
    def add_strategy_job(
        self,
        strategy_id: int,
        func: Callable,
        trigger_type: str = 'interval',
        **trigger_kwargs
    ) -> bool:
        """添加策略执行任务"""
        try:
            job_id = f"strategy_{strategy_id}"
            
            # 如果任务已存在，先移除
            if job_id in self.jobs:
                self.remove_strategy_job(strategy_id)
            
            # 创建触发器
            if trigger_type == 'interval':
                trigger = IntervalTrigger(**trigger_kwargs)
            elif trigger_type == 'cron':
                trigger = CronTrigger(**trigger_kwargs)
            else:
                logger.error(f"Unsupported trigger type: {trigger_type}")
                return False
            
            # 添加任务
            job = self.scheduler.add_job(
                func,
                trigger=trigger,
                id=job_id,
                name=f"Strategy {strategy_id} Execution",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'strategy_id': strategy_id,
                'func': func,
                'trigger_type': trigger_type,
                'trigger_kwargs': trigger_kwargs,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added strategy job {job_id} with {trigger_type} trigger")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add strategy job for strategy {strategy_id}: {e}")
            return False
    
    def remove_strategy_job(self, strategy_id: int) -> bool:
        """移除策略执行任务"""
        try:
            job_id = f"strategy_{strategy_id}"
            
            if job_id in self.jobs:
                self.scheduler.remove_job(job_id)
                del self.jobs[job_id]
                logger.info(f"Removed strategy job {job_id}")
                return True
            else:
                logger.warning(f"Strategy job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Failed to remove strategy job for strategy {strategy_id}: {e}")
            return False
    
    def pause_strategy_job(self, strategy_id: int) -> bool:
        """暂停策略执行任务"""
        try:
            job_id = f"strategy_{strategy_id}"
            
            if job_id in self.jobs:
                self.scheduler.pause_job(job_id)
                logger.info(f"Paused strategy job {job_id}")
                return True
            else:
                logger.warning(f"Strategy job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Failed to pause strategy job for strategy {strategy_id}: {e}")
            return False
    
    def resume_strategy_job(self, strategy_id: int) -> bool:
        """恢复策略执行任务"""
        try:
            job_id = f"strategy_{strategy_id}"
            
            if job_id in self.jobs:
                self.scheduler.resume_job(job_id)
                logger.info(f"Resumed strategy job {job_id}")
                return True
            else:
                logger.warning(f"Strategy job {job_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Failed to resume strategy job for strategy {strategy_id}: {e}")
            return False
    
    def get_strategy_job_info(self, strategy_id: int) -> Optional[Dict[str, Any]]:
        """获取策略任务信息"""
        job_id = f"strategy_{strategy_id}"
        return self.jobs.get(job_id)
    
    def get_all_jobs(self) -> Dict[str, Any]:
        """获取所有任务信息"""
        return self.jobs.copy()
    
    def add_market_data_job(
        self,
        func: Callable,
        interval_seconds: int = 60
    ) -> bool:
        """添加市场数据更新任务"""
        try:
            job_id = "market_data_update"
            
            job = self.scheduler.add_job(
                func,
                trigger=IntervalTrigger(seconds=interval_seconds),
                id=job_id,
                name="Market Data Update",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'func': func,
                'interval_seconds': interval_seconds,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added market data update job with {interval_seconds}s interval")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add market data job: {e}")
            return False
    
    def add_risk_check_job(
        self,
        func: Callable,
        interval_minutes: int = 5
    ) -> bool:
        """添加风险检查任务"""
        try:
            job_id = "risk_check"
            
            job = self.scheduler.add_job(
                func,
                trigger=IntervalTrigger(minutes=interval_minutes),
                id=job_id,
                name="Risk Check",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'func': func,
                'interval_minutes': interval_minutes,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added risk check job with {interval_minutes}min interval")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add risk check job: {e}")
            return False
    
    def add_portfolio_update_job(
        self,
        func: Callable,
        interval_minutes: int = 10
    ) -> bool:
        """添加投资组合更新任务"""
        try:
            job_id = "portfolio_update"
            
            job = self.scheduler.add_job(
                func,
                trigger=IntervalTrigger(minutes=interval_minutes),
                id=job_id,
                name="Portfolio Update",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'func': func,
                'interval_minutes': interval_minutes,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added portfolio update job with {interval_minutes}min interval")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add portfolio update job: {e}")
            return False
    
    def add_daily_report_job(
        self,
        func: Callable,
        hour: int = 18,
        minute: int = 0
    ) -> bool:
        """添加每日报告生成任务"""
        try:
            job_id = "daily_report"
            
            job = self.scheduler.add_job(
                func,
                trigger=CronTrigger(hour=hour, minute=minute),
                id=job_id,
                name="Daily Report Generation",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'func': func,
                'hour': hour,
                'minute': minute,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added daily report job at {hour:02d}:{minute:02d}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add daily report job: {e}")
            return False
    
    def add_cleanup_job(
        self,
        func: Callable,
        hour: int = 2,
        minute: int = 0
    ) -> bool:
        """添加数据清理任务"""
        try:
            job_id = "data_cleanup"
            
            job = self.scheduler.add_job(
                func,
                trigger=CronTrigger(hour=hour, minute=minute),
                id=job_id,
                name="Data Cleanup",
                replace_existing=True
            )
            
            self.jobs[job_id] = {
                'job': job,
                'func': func,
                'hour': hour,
                'minute': minute,
                'created_at': datetime.utcnow()
            }
            
            logger.info(f"Added data cleanup job at {hour:02d}:{minute:02d}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add cleanup job: {e}")
            return False
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        try:
            if job_id in self.jobs:
                job = self.jobs[job_id]['job']
                return {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger),
                    'func': job.func.__name__ if hasattr(job.func, '__name__') else str(job.func)
                }
            return None
            
        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return None
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        return {
            'running': self.is_running,
            'job_count': len(self.jobs),
            'jobs': [
                {
                    'id': job_id,
                    'name': job_info['job'].name,
                    'next_run_time': job_info['job'].next_run_time.isoformat() 
                                   if job_info['job'].next_run_time else None
                }
                for job_id, job_info in self.jobs.items()
            ]
        }


# 全局调度器实例
scheduler = StrategyScheduler()
