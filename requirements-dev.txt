# ============================================================================
# 量化交易平台 - 开发环境依赖
# ============================================================================

# 包含生产环境依赖
-r requirements.txt

# 开发工具
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6
jupyterlab==4.0.9
ipywidgets==8.1.1

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
pytest-benchmark==4.0.0
coverage==7.3.2
factory-boy==3.3.0
faker==20.1.0

# 代码质量和格式化
black==23.11.0
isort==5.12.0
flake8==6.1.0
pylint==3.0.3
mypy==1.7.1
bandit==1.7.5
safety==2.3.5
pre-commit==3.6.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 性能分析
line-profiler==4.1.1
memory-profiler==0.61.0
py-spy==0.3.14
scalene==1.5.26

# 调试工具
pdb++==0.10.3
ipdb==0.13.13
pudb==2023.1

# 数据库工具
pgcli==4.0.1
mycli==1.27.0

# API测试
httpie==3.2.2
postman-cli==1.0.0

# 环境管理
python-dotenv==1.0.0
pipenv==2023.11.15

# 代码分析
radon==6.0.1
xenon==0.9.1
vulture==2.10

# 依赖管理
pip-tools==7.3.0
pipdeptree==2.13.1

# 构建工具
build==1.0.3
twine==4.0.2
wheel==0.42.0
setuptools==69.0.2

# 监控和日志
watchdog==3.0.0
colorlog==6.8.0

# 开发服务器
watchfiles==0.21.0
python-multipart==0.0.6
