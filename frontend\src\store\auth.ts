/**
 * 认证状态管理
 * 
 * 使用Zustand管理用户认证状态，包括登录、登出、token管理等
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { message } from 'antd';

import apiClient from '@/services/api';
import { 
  User, 
  LoginRequest, 
  RegisterRequest, 
  AuthTokens,
  LoadingState 
} from '@/types';

interface AuthState {
  // 状态
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  loadingState: LoadingState;
  
  // 操作
  login: (loginData: LoginRequest) => Promise<void>;
  register: (registerData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  updateProfile: (profileData: Partial<User>) => Promise<void>;
  
  // 内部方法
  setUser: (user: User | null) => void;
  setTokens: (tokens: AuthTokens | null) => void;
  setLoading: (loading: boolean) => void;
  setLoadingState: (state: LoadingState) => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      loadingState: 'idle',

      // 登录
      login: async (loginData: LoginRequest) => {
        try {
          set({ isLoading: true, loadingState: 'loading' });

          const response = await apiClient.post('/auth/login', {
            username: loginData.email, // FastAPI OAuth2PasswordRequestForm 使用 username 字段
            password: loginData.password,
          });

          if (response.code === 200 && response.data) {
            const { access_token, refresh_token, token_type, expires_in, user } = response.data;
            
            const tokens: AuthTokens = {
              accessToken: access_token,
              refreshToken: refresh_token,
              tokenType: token_type,
              expiresIn: expires_in,
            };

            // 设置认证信息
            apiClient.setAuthTokens(tokens);
            
            set({
              user,
              tokens,
              isAuthenticated: true,
              isLoading: false,
              loadingState: 'success',
            });
          } else {
            throw new Error(response.message || '登录失败');
          }
        } catch (error: any) {
          set({ 
            isLoading: false, 
            loadingState: 'error',
            user: null,
            tokens: null,
            isAuthenticated: false,
          });
          
          const errorMessage = error.response?.data?.message || error.message || '登录失败';
          throw new Error(errorMessage);
        }
      },

      // 注册
      register: async (registerData: RegisterRequest) => {
        try {
          set({ isLoading: true, loadingState: 'loading' });

          const response = await apiClient.post('/auth/register', registerData);

          if (response.code === 200) {
            set({ 
              isLoading: false, 
              loadingState: 'success' 
            });
            
            message.success('注册成功！请登录您的账号');
          } else {
            throw new Error(response.message || '注册失败');
          }
        } catch (error: any) {
          set({ 
            isLoading: false, 
            loadingState: 'error' 
          });
          
          const errorMessage = error.response?.data?.message || error.message || '注册失败';
          throw new Error(errorMessage);
        }
      },

      // 登出
      logout: async () => {
        try {
          set({ isLoading: true });

          // 调用登出API
          await apiClient.post('/auth/logout');
        } catch (error) {
          console.error('登出API调用失败:', error);
        } finally {
          // 无论API调用是否成功，都清理本地状态
          apiClient.clearAuth();
          
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
            loadingState: 'idle',
          });
          
          message.success('已安全登出');
        }
      },

      // 刷新Token
      refreshToken: async () => {
        try {
          const { tokens } = get();
          if (!tokens?.refreshToken) {
            throw new Error('没有刷新Token');
          }

          const response = await apiClient.post('/auth/refresh', {
            refresh_token: tokens.refreshToken,
          });

          if (response.code === 200 && response.data) {
            const newTokens: AuthTokens = {
              accessToken: response.data.access_token,
              refreshToken: tokens.refreshToken, // 保持原有的刷新Token
              tokenType: response.data.token_type,
              expiresIn: response.data.expires_in,
            };

            apiClient.setAuthTokens(newTokens);
            set({ tokens: newTokens });
          } else {
            throw new Error('Token刷新失败');
          }
        } catch (error) {
          console.error('Token刷新失败:', error);
          // Token刷新失败，清理认证状态
          get().clearAuth();
          throw error;
        }
      },

      // 获取当前用户信息
      getCurrentUser: async () => {
        try {
          set({ isLoading: true, loadingState: 'loading' });

          const response = await apiClient.get('/auth/me');

          if (response.code === 200 && response.data) {
            set({
              user: response.data,
              isAuthenticated: true,
              isLoading: false,
              loadingState: 'success',
            });
          } else {
            throw new Error('获取用户信息失败');
          }
        } catch (error: any) {
          set({ 
            isLoading: false, 
            loadingState: 'error' 
          });
          
          // 如果是401错误，清理认证状态
          if (error.response?.status === 401) {
            get().clearAuth();
          }
          
          throw error;
        }
      },

      // 更新用户资料
      updateProfile: async (profileData: Partial<User>) => {
        try {
          set({ isLoading: true, loadingState: 'loading' });

          const response = await apiClient.put('/auth/profile', profileData);

          if (response.code === 200 && response.data) {
            set({
              user: response.data,
              isLoading: false,
              loadingState: 'success',
            });
            
            message.success('资料更新成功');
          } else {
            throw new Error(response.message || '更新失败');
          }
        } catch (error: any) {
          set({ 
            isLoading: false, 
            loadingState: 'error' 
          });
          
          const errorMessage = error.response?.data?.message || error.message || '更新失败';
          throw new Error(errorMessage);
        }
      },

      // 设置用户
      setUser: (user: User | null) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        });
      },

      // 设置Token
      setTokens: (tokens: AuthTokens | null) => {
        set({ tokens });
        if (tokens) {
          apiClient.setAuthTokens(tokens);
        } else {
          apiClient.clearAuth();
        }
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // 设置加载状态
      setLoadingState: (loadingState: LoadingState) => {
        set({ loadingState });
      },

      // 清理认证状态
      clearAuth: () => {
        apiClient.clearAuth();
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
          loadingState: 'idle',
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 恢复状态后，设置API客户端的认证信息
        if (state?.tokens) {
          apiClient.setAuthTokens(state.tokens);
        }
      },
    }
  )
);

// 导出选择器函数
export const useAuth = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  return { user, isAuthenticated, isLoading };
};

export const useAuthActions = () => {
  const { login, register, logout, refreshToken, getCurrentUser, updateProfile } = useAuthStore();
  return { login, register, logout, refreshToken, getCurrentUser, updateProfile };
};
