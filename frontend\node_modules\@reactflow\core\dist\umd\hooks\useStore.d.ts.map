{"version": 3, "file": "useStore.d.ts", "sourceRoot": "", "sources": ["../../../../packages/core/src/hooks/useStore.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAIxC,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAI/C,KAAK,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS;IAAE,QAAQ,EAAE,MAAM,MAAM,CAAC,CAAA;CAAE,GAAG,CAAC,GAAG,KAAK,CAAC;AAE7F,iBAAS,QAAQ,CAAC,UAAU,GAAG,YAAY,EACzC,QAAQ,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,UAAU,EAC/C,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU,KAAK,OAAO,cASvD;AAED,QAAA,MAAM,WAAW;;;;;CAgBhB,CAAC;AAEF,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC"}