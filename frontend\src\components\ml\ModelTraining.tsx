'use client';

/**
 * 模型训练组件
 * 
 * 提供机器学习模型训练界面和配置
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Steps,
  Row,
  Col,
  Divider,
  Alert,
  Progress,
  Table,
  Tag,
  Space,
  message,
  Modal,
  Descriptions,
  Statistic
} from 'antd';
import {
  RobotOutlined,
  ExperimentOutlined,
  BarChartOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Step } = Steps;

interface ModelConfig {
  name: string;
  description: string;
  model_type: 'prediction' | 'classification';
  algorithm: string;
  symbols: string[];
  start_date: string;
  end_date: string;
  hyperparameters: Record<string, any>;
  target_days: number;
}

interface TrainingModel {
  id: number;
  name: string;
  model_type: string;
  algorithm: string;
  status: string;
  validation_score: number;
  created_at: string;
  performance_metrics: Record<string, any>;
}

export const ModelTraining: React.FC = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState<TrainingModel[]>([]);
  const [selectedModel, setSelectedModel] = useState<TrainingModel | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);

  // 模型配置选项
  const modelTypes = [
    { value: 'prediction', label: '价格预测', description: '预测股票未来价格' },
    { value: 'classification', label: '方向预测', description: '预测股票涨跌方向' }
  ];

  const algorithms = {
    prediction: [
      { value: 'xgboost', label: 'XGBoost', description: '梯度提升树，适合结构化数据' },
      { value: 'lightgbm', label: 'LightGBM', description: '轻量级梯度提升，训练速度快' },
      { value: 'random_forest', label: '随机森林', description: '集成学习，稳定性好' },
      { value: 'lstm', label: 'LSTM', description: '长短期记忆网络，适合时序数据' }
    ],
    classification: [
      { value: 'xgboost', label: 'XGBoost分类', description: '梯度提升分类器' },
      { value: 'lightgbm', label: 'LightGBM分类', description: '轻量级分类器' },
      { value: 'random_forest', label: '随机森林分类', description: '集成分类器' }
    ]
  };

  const stockOptions = [
    { value: '000001.XSHE', label: '平安银行' },
    { value: '000002.XSHE', label: '万科A' },
    { value: '000858.XSHE', label: '五粮液' },
    { value: '002415.XSHE', label: '海康威视' },
    { value: '600036.XSHG', label: '招商银行' },
    { value: '600519.XSHG', label: '贵州茅台' },
    { value: '600887.XSHG', label: '伊利股份' }
  ];

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    try {
      const response = await fetch('/api/v1/ml/models', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setModels(result.data.items);
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    
    try {
      const modelConfig: ModelConfig = {
        name: values.name,
        description: values.description,
        model_type: values.model_type,
        algorithm: values.algorithm,
        symbols: values.symbols,
        start_date: values.date_range[0].format('YYYY-MM-DD'),
        end_date: values.date_range[1].format('YYYY-MM-DD'),
        hyperparameters: values.hyperparameters || {},
        target_days: values.target_days || 1
      };

      const response = await fetch('/api/v1/ml/models/train', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(modelConfig)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('模型训练任务已提交，请稍后查看结果');
        form.resetFields();
        setCurrentStep(0);
        setTimeout(fetchModels, 2000); // 2秒后刷新列表
      } else {
        message.error(result.message || '提交训练任务失败');
      }
    } catch (error) {
      console.error('提交训练任务失败:', error);
      message.error('提交训练任务失败');
    } finally {
      setLoading(false);
    }
  };

  const showModelDetail = (model: TrainingModel) => {
    setSelectedModel(model);
    setDetailVisible(true);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'training': 'processing',
      'trained': 'success',
      'failed': 'error',
      'deployed': 'success'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'training': '训练中',
      'trained': '已训练',
      'failed': '训练失败',
      'deployed': '已部署'
    };
    return texts[status as keyof typeof texts] || status;
  };

  const columns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TrainingModel) => (
        <Space>
          <RobotOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (type: string) => (
        <Tag color={type === 'prediction' ? 'blue' : 'green'}>
          {type === 'prediction' ? '价格预测' : '方向预测'}
        </Tag>
      )
    },
    {
      title: '算法',
      dataIndex: 'algorithm',
      key: 'algorithm',
      render: (algorithm: string) => (
        <Tag color="purple">{algorithm.toUpperCase()}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '验证分数',
      dataIndex: 'validation_score',
      key: 'validation_score',
      render: (score: number) => score ? score.toFixed(4) : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: TrainingModel) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => showModelDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  const steps = [
    {
      title: '基本配置',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="模型名称"
              rules={[{ required: true, message: '请输入模型名称' }]}
            >
              <Input placeholder="请输入模型名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="model_type"
              label="模型类型"
              rules={[{ required: true, message: '请选择模型类型' }]}
            >
              <Select placeholder="请选择模型类型">
                {modelTypes.map(type => (
                  <Option key={type.value} value={type.value}>
                    <div>
                      <div>{type.label}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {type.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="description"
              label="模型描述"
            >
              <TextArea rows={3} placeholder="请输入模型描述" />
            </Form.Item>
          </Col>
        </Row>
      )
    },
    {
      title: '算法选择',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="algorithm"
              label="算法"
              rules={[{ required: true, message: '请选择算法' }]}
            >
              <Select placeholder="请选择算法">
                {(form.getFieldValue('model_type') ? 
                  algorithms[form.getFieldValue('model_type') as keyof typeof algorithms] : 
                  algorithms.prediction
                ).map(algo => (
                  <Option key={algo.value} value={algo.value}>
                    <div>
                      <div>{algo.label}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {algo.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="target_days"
              label="预测天数"
              initialValue={1}
            >
              <Select>
                <Option value={1}>1天</Option>
                <Option value={3}>3天</Option>
                <Option value={5}>5天</Option>
                <Option value={10}>10天</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      )
    },
    {
      title: '数据配置',
      content: (
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="symbols"
              label="股票代码"
              rules={[{ required: true, message: '请选择股票代码' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择股票代码"
                maxTagCount={3}
              >
                {stockOptions.map(stock => (
                  <Option key={stock.value} value={stock.value}>
                    {stock.value} - {stock.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="date_range"
              label="训练数据时间范围"
              rules={[{ required: true, message: '请选择时间范围' }]}
            >
              <RangePicker
                style={{ width: '100%' }}
                disabledDate={(current) => current && current > dayjs().endOf('day')}
              />
            </Form.Item>
          </Col>
        </Row>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">机器学习模型训练</h1>
          <p className="text-gray-600">训练和管理您的预测模型</p>
        </div>
      </div>

      {/* 训练表单 */}
      <Card title={
        <Space>
          <ExperimentOutlined />
          <span>新建训练任务</span>
        </Space>
      }>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Steps current={currentStep} className="mb-6">
            {steps.map((step, index) => (
              <Step key={index} title={step.title} />
            ))}
          </Steps>

          <div className="min-h-[300px]">
            {steps[currentStep].content}
          </div>

          <Divider />

          <div className="flex justify-between">
            <Button
              disabled={currentStep === 0}
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              上一步
            </Button>
            
            <Space>
              {currentStep < steps.length - 1 && (
                <Button
                  type="primary"
                  onClick={() => setCurrentStep(currentStep + 1)}
                >
                  下一步
                </Button>
              )}
              
              {currentStep === steps.length - 1 && (
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<PlayCircleOutlined />}
                >
                  开始训练
                </Button>
              )}
            </Space>
          </div>
        </Form>
      </Card>

      {/* 模型列表 */}
      <Card title={
        <Space>
          <BarChartOutlined />
          <span>我的模型</span>
        </Space>
      }>
        <Table
          columns={columns}
          dataSource={models}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个模型`
          }}
        />
      </Card>

      {/* 模型详情弹窗 */}
      <Modal
        title="模型详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedModel && (
          <div className="space-y-4">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="模型名称">
                {selectedModel.name}
              </Descriptions.Item>
              <Descriptions.Item label="模型类型">
                <Tag color={selectedModel.model_type === 'prediction' ? 'blue' : 'green'}>
                  {selectedModel.model_type === 'prediction' ? '价格预测' : '方向预测'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="算法">
                <Tag color="purple">{selectedModel.algorithm.toUpperCase()}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(selectedModel.status)}>
                  {getStatusText(selectedModel.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="验证分数">
                {selectedModel.validation_score ? selectedModel.validation_score.toFixed(4) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(selectedModel.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            {selectedModel.performance_metrics && (
              <div>
                <h4 className="font-medium mb-2">性能指标</h4>
                <Row gutter={16}>
                  {Object.entries(selectedModel.performance_metrics).map(([key, value]) => (
                    <Col span={6} key={key}>
                      <Statistic
                        title={key.toUpperCase()}
                        value={typeof value === 'number' ? value : 0}
                        precision={4}
                      />
                    </Col>
                  ))}
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};
