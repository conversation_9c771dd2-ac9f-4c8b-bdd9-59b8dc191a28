'use client';

/**
 * 高级图表组件库
 * 
 * 提供多种专业的金融图表类型
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Typography, Space, Tag } from 'antd';

const { Title, Text } = Typography;

interface ChartProps {
  data: any[];
  title?: string;
  height?: number;
  className?: string;
}

// K线图组件
export const CandlestickChart: React.FC<ChartProps> = ({ 
  data, 
  title = "K线图", 
  height = 400,
  className = ""
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params: any) {
            const data = params[0];
            return `
              日期: ${data.name}<br/>
              开盘: ${data.value[1]}<br/>
              收盘: ${data.value[2]}<br/>
              最低: ${data.value[3]}<br/>
              最高: ${data.value[4]}<br/>
              成交量: ${data.value[5]}
            `;
          }
        },
        grid: [
          {
            left: '10%',
            right: '8%',
            height: '60%'
          },
          {
            left: '10%',
            right: '8%',
            top: '75%',
            height: '16%'
          }
        ],
        xAxis: [
          {
            type: 'category',
            data: data.map(item => item.date),
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            min: 'dataMin',
            max: 'dataMax'
          },
          {
            type: 'category',
            gridIndex: 1,
            data: data.map(item => item.date),
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: { show: false },
            min: 'dataMin',
            max: 'dataMax'
          }
        ],
        yAxis: [
          {
            scale: true,
            splitArea: {
              show: true
            }
          },
          {
            scale: true,
            gridIndex: 1,
            splitNumber: 2,
            axisLabel: { show: false },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1],
            start: 80,
            end: 100
          },
          {
            show: true,
            xAxisIndex: [0, 1],
            type: 'slider',
            top: '85%',
            start: 80,
            end: 100
          }
        ],
        series: [
          {
            name: 'K线',
            type: 'candlestick',
            data: data.map(item => [item.open, item.close, item.low, item.high]),
            itemStyle: {
              color: '#ef232a',
              color0: '#14b143',
              borderColor: '#ef232a',
              borderColor0: '#14b143'
            }
          },
          {
            name: '成交量',
            type: 'bar',
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: data.map(item => item.volume),
            itemStyle: {
              color: function(params: any) {
                const dataIndex = params.dataIndex;
                const currentData = data[dataIndex];
                return currentData.close >= currentData.open ? '#ef232a' : '#14b143';
              }
            }
          }
        ]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
      className={className}
    />
  );
};

// 热力图组件
export const HeatmapChart: React.FC<{
  data: Array<[number, number, number]>;
  xAxisData: string[];
  yAxisData: string[];
  title?: string;
  height?: number;
}> = ({ 
  data, 
  xAxisData, 
  yAxisData, 
  title = "热力图", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          position: 'top',
          formatter: function (params: any) {
            return `${yAxisData[params.value[1]]}<br/>${xAxisData[params.value[0]]}: ${params.value[2]}`;
          }
        },
        grid: {
          height: '50%',
          top: '10%'
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          splitArea: {
            show: true
          }
        },
        yAxis: {
          type: 'category',
          data: yAxisData,
          splitArea: {
            show: true
          }
        },
        visualMap: {
          min: Math.min(...data.map(item => item[2])),
          max: Math.max(...data.map(item => item[2])),
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '15%',
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
          }
        },
        series: [{
          name: '热力图',
          type: 'heatmap',
          data: data,
          label: {
            show: true
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, xAxisData, yAxisData, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 雷达图组件
export const RadarChart: React.FC<{
  data: Array<{
    name: string;
    value: number[];
  }>;
  indicators: Array<{
    name: string;
    max: number;
  }>;
  title?: string;
  height?: number;
}> = ({ 
  data, 
  indicators, 
  title = "雷达图", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.map(item => item.name)
        },
        radar: {
          indicator: indicators,
          center: ['50%', '55%'],
          radius: '70%'
        },
        series: [{
          name: '雷达图',
          type: 'radar',
          data: data.map(item => ({
            value: item.value,
            name: item.name,
            areaStyle: {
              opacity: 0.3
            }
          }))
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, indicators, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 桑基图组件
export const SankeyChart: React.FC<{
  data: {
    nodes: Array<{ name: string }>;
    links: Array<{ source: string; target: string; value: number }>;
  };
  title?: string;
  height?: number;
}> = ({ 
  data, 
  title = "桑基图", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove'
        },
        series: [{
          type: 'sankey',
          data: data.nodes,
          links: data.links,
          emphasis: {
            focus: 'adjacency'
          },
          lineStyle: {
            color: 'gradient',
            curveness: 0.5
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 瀑布图组件
export const WaterfallChart: React.FC<{
  data: Array<{
    name: string;
    value: number;
    itemStyle?: any;
  }>;
  title?: string;
  height?: number;
}> = ({ 
  data, 
  title = "瀑布图", 
  height = 400 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      // 计算累计值
      let cumulative = 0;
      const processedData = data.map((item, index) => {
        if (index === 0 || index === data.length - 1) {
          // 起始值和结束值
          const result = {
            name: item.name,
            value: item.value,
            itemStyle: item.itemStyle || { color: '#5470c6' }
          };
          cumulative = item.value;
          return result;
        } else {
          // 中间的增量值
          const result = {
            name: item.name,
            value: item.value,
            stack: 'total',
            itemStyle: item.itemStyle || { 
              color: item.value >= 0 ? '#91cc75' : '#ee6666' 
            }
          };
          cumulative += item.value;
          return result;
        }
      });

      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params: any) {
            const data = params[0];
            return `${data.name}: ${data.value}`;
          }
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.name)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '瀑布图',
          type: 'bar',
          data: processedData
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};
