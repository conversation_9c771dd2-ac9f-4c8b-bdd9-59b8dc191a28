'use client';

/**
 * 股票列表页面
 * 
 * 显示股票列表，支持搜索、筛选、排序等功能
 */

import React, { useState, useEffect } from 'react';
import {
  Table,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Tag,
  Card,
  Row,
  Col,
  Statistic,
  message,
  Tooltip,
  Drawer,
  Form,
  Checkbox,
  Radio,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  StarOutlined,
  StarFilled,
  LineChartOutlined,
  SyncOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

import { useAuth } from '@/store/auth';
import apiClient from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface Stock {
  id: number;
  symbol: string;
  name: string;
  displayName?: string;
  market: string;
  exchange: string;
  industry?: string;
  sector?: string;
  listDate?: string;
  isActive: boolean;
  isSt: boolean;
  marketCap?: number;
  // 实时数据
  price?: number;
  change?: number;
  changePct?: number;
  volume?: number;
  turnover?: number;
}

interface FilterParams {
  market: string;
  industry?: string;
  sector?: string;
  isActive: boolean;
  priceRange?: [number, number];
  marketCapRange?: [number, number];
}

export default function StocksPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [filteredStocks, setFilteredStocks] = useState<Stock[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterParams, setFilterParams] = useState<FilterParams>({
    market: 'A',
    isActive: true,
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 50,
    total: 0,
  });
  const [favorites, setFavorites] = useState<string[]>([]);
  const [showFilter, setShowFilter] = useState(false);
  const [industries, setIndustries] = useState<string[]>([]);
  const [sectors, setSectors] = useState<string[]>([]);

  // 加载股票列表
  const loadStocks = async (params?: any) => {
    try {
      setLoading(true);
      const queryParams = {
        market: filterParams.market,
        industry: filterParams.industry,
        sector: filterParams.sector,
        is_active: filterParams.isActive,
        page: pagination.current,
        page_size: pagination.pageSize,
        ...params,
      };

      const response = await apiClient.get('/market/stocks', { params: queryParams });
      
      if (response.code === 200 && response.data) {
        const { items, pagination: paginationInfo } = response.data;
        setStocks(items);
        setFilteredStocks(items);
        setPagination(prev => ({
          ...prev,
          total: paginationInfo.total,
          current: paginationInfo.page,
        }));
        
        // 提取行业和板块信息
        const uniqueIndustries = [...new Set(items.map((stock: Stock) => stock.industry).filter(Boolean))];
        const uniqueSectors = [...new Set(items.map((stock: Stock) => stock.sector).filter(Boolean))];
        setIndustries(uniqueIndustries);
        setSectors(uniqueSectors);
      }
    } catch (error: any) {
      message.error('加载股票列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索股票
  const handleSearch = (value: string) => {
    setSearchText(value);
    if (!value.trim()) {
      setFilteredStocks(stocks);
      return;
    }

    const filtered = stocks.filter(stock =>
      stock.name.includes(value) ||
      stock.symbol.includes(value) ||
      stock.displayName?.includes(value)
    );
    setFilteredStocks(filtered);
  };

  // 应用筛选
  const handleFilter = (values: any) => {
    setFilterParams(values);
    setShowFilter(false);
    loadStocks({ page: 1 });
  };

  // 切换收藏
  const toggleFavorite = (symbol: string) => {
    setFavorites(prev => {
      const newFavorites = prev.includes(symbol)
        ? prev.filter(s => s !== symbol)
        : [...prev, symbol];
      
      // 这里应该调用API保存收藏状态
      message.success(
        prev.includes(symbol) ? '已取消收藏' : '已添加到收藏'
      );
      
      return newFavorites;
    });
  };

  // 查看图表
  const viewChart = (symbol: string) => {
    router.push(`/dashboard/market/charts?symbol=${symbol}`);
  };

  // 同步股票数据
  const syncStocks = async () => {
    try {
      setLoading(true);
      const response = await apiClient.post('/market/sync/stocks', {
        market: filterParams.market,
      });
      
      if (response.code === 200) {
        message.success('股票数据同步成功');
        loadStocks();
      }
    } catch (error) {
      message.error('同步股票数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left' as const,
      render: (text: string, record: Stock) => (
        <div className="flex items-center space-x-2">
          <div>
            <div className="font-medium flex items-center space-x-1">
              <span>{text}</span>
              {record.isSt && <Tag color="red" size="small">ST</Tag>}
            </div>
            <div className="text-xs text-gray-500">{record.symbol}</div>
          </div>
          <Button
            type="text"
            size="small"
            icon={
              favorites.includes(record.symbol) ? (
                <StarFilled className="text-yellow-500" />
              ) : (
                <StarOutlined />
              )
            }
            onClick={() => toggleFavorite(record.symbol)}
          />
        </div>
      ),
    },
    {
      title: '最新价',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">
          {price ? `¥${price.toFixed(2)}` : '--'}
        </Text>
      ),
    },
    {
      title: '涨跌',
      dataIndex: 'change',
      key: 'change',
      width: 120,
      align: 'right' as const,
      render: (change: number, record: Stock) => {
        if (!change && !record.changePct) return <Text>--</Text>;
        
        const isPositive = (change || 0) >= 0;
        const color = isPositive ? '#ef4444' : '#22c55e';
        
        return (
          <div className="text-right">
            <div className="font-mono" style={{ color }}>
              {isPositive ? '+' : ''}{(change || 0).toFixed(2)}
            </div>
            <div className="text-xs font-mono" style={{ color }}>
              {isPositive ? '+' : ''}{(record.changePct || 0).toFixed(2)}%
            </div>
          </div>
        );
      },
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      width: 100,
      align: 'right' as const,
      render: (volume: number) => (
        <Text className="font-mono text-xs">
          {volume ? `${(volume / 100000000).toFixed(2)}亿` : '--'}
        </Text>
      ),
    },
    {
      title: '市值',
      dataIndex: 'marketCap',
      key: 'marketCap',
      width: 100,
      align: 'right' as const,
      render: (marketCap: number) => (
        <Text className="font-mono text-xs">
          {marketCap ? `${(marketCap / 100000000).toFixed(0)}亿` : '--'}
        </Text>
      ),
    },
    {
      title: '行业',
      dataIndex: 'industry',
      key: 'industry',
      width: 120,
      render: (industry: string) => (
        industry ? <Tag color="blue">{industry}</Tag> : '--'
      ),
    },
    {
      title: '板块',
      dataIndex: 'sector',
      key: 'sector',
      width: 120,
      render: (sector: string) => (
        sector ? <Tag color="green">{sector}</Tag> : '--'
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (_, record: Stock) => (
        <Space size="small">
          <Tooltip title="查看图表">
            <Button
              type="text"
              size="small"
              icon={<LineChartOutlined />}
              onClick={() => viewChart(record.symbol)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    loadStocks();
  }, []);

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="!mb-2">
            股票列表
          </Title>
          <Text type="secondary">
            浏览和搜索股票信息
          </Text>
        </div>
        
        <Space>
          <Button
            icon={<SyncOutlined />}
            onClick={syncStocks}
            loading={loading}
          >
            同步数据
          </Button>
        </Space>
      </div>

      {/* 统计信息 */}
      <Row gutter={16}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总股票数"
              value={pagination.total}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="活跃股票"
              value={filteredStocks.filter(s => s.isActive).length}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="ST股票"
              value={filteredStocks.filter(s => s.isSt).length}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="收藏股票"
              value={favorites.length}
              suffix="只"
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card>
        <div className="flex items-center justify-between flex-wrap gap-4">
          <Space wrap>
            <Input.Search
              placeholder="搜索股票代码或名称"
              allowClear
              style={{ width: 300 }}
              onSearch={handleSearch}
              onChange={(e) => !e.target.value && handleSearch('')}
            />
            
            <Select
              value={filterParams.market}
              onChange={(value) => {
                setFilterParams(prev => ({ ...prev, market: value }));
                loadStocks({ market: value, page: 1 });
              }}
              style={{ width: 120 }}
            >
              <Option value="A">A股</Option>
              <Option value="HK">港股</Option>
              <Option value="US">美股</Option>
            </Select>
          </Space>
          
          <Space>
            <Button
              icon={<FilterOutlined />}
              onClick={() => setShowFilter(true)}
            >
              高级筛选
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadStocks()}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </Card>

      {/* 股票列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredStocks}
          rowKey="symbol"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
              loadStocks({ page, page_size: pageSize });
            },
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>

      {/* 高级筛选抽屉 */}
      <Drawer
        title="高级筛选"
        placement="right"
        onClose={() => setShowFilter(false)}
        open={showFilter}
        width={400}
      >
        <Form
          layout="vertical"
          initialValues={filterParams}
          onFinish={handleFilter}
        >
          <Form.Item name="market" label="市场">
            <Radio.Group>
              <Radio value="A">A股</Radio>
              <Radio value="HK">港股</Radio>
              <Radio value="US">美股</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item name="industry" label="行业">
            <Select placeholder="选择行业" allowClear>
              {industries.map(industry => (
                <Option key={industry} value={industry}>
                  {industry}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="sector" label="板块">
            <Select placeholder="选择板块" allowClear>
              {sectors.map(sector => (
                <Option key={sector} value={sector}>
                  {sector}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="isActive" valuePropName="checked" label="股票状态">
            <Checkbox>只显示活跃股票</Checkbox>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                应用筛选
              </Button>
              <Button onClick={() => setShowFilter(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
}
