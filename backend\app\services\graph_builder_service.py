"""
图数据构建服务

提供金融知识图谱的构建、更新、管理功能
"""

import numpy as np
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
import warnings
warnings.filterwarnings('ignore')

# 尝试导入图处理库
try:
    import networkx as nx
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

try:
    import igraph as ig
    IGRAPH_AVAILABLE = True
except ImportError:
    IGRAPH_AVAILABLE = False

from app.core.logging import logger
from app.models.knowledge_graph import (
    FinancialEntity, EntityRelation, KnowledgeGraph
)


class EntityExtractor:
    """实体提取器"""
    
    def __init__(self):
        self.entity_types = {
            'stock': self._extract_stock_entities,
            'company': self._extract_company_entities,
            'person': self._extract_person_entities,
            'event': self._extract_event_entities,
            'industry': self._extract_industry_entities,
            'concept': self._extract_concept_entities
        }
    
    def extract_entities_from_data(
        self,
        data: pd.DataFrame,
        entity_type: str,
        extraction_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """从数据中提取实体"""
        try:
            if entity_type not in self.entity_types:
                raise ValueError(f"Unsupported entity type: {entity_type}")
            
            return self.entity_types[entity_type](data, extraction_config)
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return []
    
    def _extract_stock_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取股票实体"""
        try:
            entities = []
            
            # 假设数据包含股票信息
            required_columns = ['code', 'name']
            if not all(col in data.columns for col in required_columns):
                logger.warning("股票数据缺少必要列")
                return entities
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"stock_{row['code']}",
                    'entity_name': row['name'],
                    'entity_type': 'stock',
                    'entity_category': 'financial_instrument',
                    'stock_code': row['code'],
                    'stock_name': row['name'],
                    'exchange': row.get('exchange', 'unknown'),
                    'industry': row.get('industry', 'unknown'),
                    'sector': row.get('sector', 'unknown'),
                    'market_cap': float(row['market_cap']) if 'market_cap' in row and pd.notna(row['market_cap']) else None,
                    'properties': {
                        'listing_date': row.get('listing_date'),
                        'total_shares': row.get('total_shares'),
                        'float_shares': row.get('float_shares'),
                        'pe_ratio': row.get('pe_ratio'),
                        'pb_ratio': row.get('pb_ratio')
                    },
                    'node_features': self._generate_stock_features(row),
                    'data_source': config.get('data_source', 'jqdata'),
                    'source_confidence': config.get('confidence', 0.9)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"股票实体提取失败: {e}")
            return []
    
    def _extract_company_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取公司实体"""
        try:
            entities = []
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"company_{row.get('company_id', row.get('name', '').replace(' ', '_'))}",
                    'entity_name': row.get('name', ''),
                    'entity_type': 'company',
                    'entity_category': 'organization',
                    'company_name': row.get('name', ''),
                    'company_type': row.get('company_type', 'unknown'),
                    'registration_country': row.get('country', 'unknown'),
                    'business_scope': row.get('business_scope', ''),
                    'properties': {
                        'founded_year': row.get('founded_year'),
                        'employee_count': row.get('employee_count'),
                        'revenue': row.get('revenue'),
                        'headquarters': row.get('headquarters')
                    },
                    'node_features': self._generate_company_features(row),
                    'data_source': config.get('data_source', 'manual'),
                    'source_confidence': config.get('confidence', 0.8)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"公司实体提取失败: {e}")
            return []
    
    def _extract_person_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取人员实体"""
        try:
            entities = []
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"person_{row.get('person_id', row.get('name', '').replace(' ', '_'))}",
                    'entity_name': row.get('name', ''),
                    'entity_type': 'person',
                    'entity_category': 'individual',
                    'person_name': row.get('name', ''),
                    'position': row.get('position', ''),
                    'department': row.get('department', ''),
                    'properties': {
                        'age': row.get('age'),
                        'education': row.get('education'),
                        'experience_years': row.get('experience_years'),
                        'specialization': row.get('specialization')
                    },
                    'node_features': self._generate_person_features(row),
                    'data_source': config.get('data_source', 'manual'),
                    'source_confidence': config.get('confidence', 0.7)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"人员实体提取失败: {e}")
            return []
    
    def _extract_event_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取事件实体"""
        try:
            entities = []
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"event_{row.get('event_id', hash(row.get('description', '')))}",
                    'entity_name': row.get('title', row.get('description', '')[:50]),
                    'entity_type': 'event',
                    'entity_category': 'temporal',
                    'event_type': row.get('event_type', 'unknown'),
                    'event_date': pd.to_datetime(row.get('date')) if 'date' in row else None,
                    'event_description': row.get('description', ''),
                    'properties': {
                        'impact_level': row.get('impact_level'),
                        'affected_entities': row.get('affected_entities'),
                        'event_source': row.get('source'),
                        'sentiment': row.get('sentiment')
                    },
                    'node_features': self._generate_event_features(row),
                    'data_source': config.get('data_source', 'news'),
                    'source_confidence': config.get('confidence', 0.6)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"事件实体提取失败: {e}")
            return []
    
    def _extract_industry_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取行业实体"""
        try:
            entities = []
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"industry_{row.get('industry_code', row.get('name', '').replace(' ', '_'))}",
                    'entity_name': row.get('name', ''),
                    'entity_type': 'industry',
                    'entity_category': 'classification',
                    'industry': row.get('name', ''),
                    'properties': {
                        'industry_code': row.get('industry_code'),
                        'parent_industry': row.get('parent_industry'),
                        'industry_level': row.get('level'),
                        'description': row.get('description')
                    },
                    'node_features': self._generate_industry_features(row),
                    'data_source': config.get('data_source', 'classification'),
                    'source_confidence': config.get('confidence', 0.9)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"行业实体提取失败: {e}")
            return []
    
    def _extract_concept_entities(
        self,
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取概念实体"""
        try:
            entities = []
            
            for _, row in data.iterrows():
                entity = {
                    'entity_id': f"concept_{row.get('concept_id', row.get('name', '').replace(' ', '_'))}",
                    'entity_name': row.get('name', ''),
                    'entity_type': 'concept',
                    'entity_category': 'abstract',
                    'properties': {
                        'concept_type': row.get('concept_type'),
                        'description': row.get('description'),
                        'keywords': row.get('keywords'),
                        'related_themes': row.get('related_themes')
                    },
                    'node_features': self._generate_concept_features(row),
                    'data_source': config.get('data_source', 'analysis'),
                    'source_confidence': config.get('confidence', 0.5)
                }
                entities.append(entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"概念实体提取失败: {e}")
            return []
    
    def _generate_stock_features(self, row: pd.Series) -> List[float]:
        """生成股票特征向量"""
        try:
            features = []
            
            # 市值特征
            market_cap = row.get('market_cap', 0)
            features.append(np.log1p(market_cap) if market_cap > 0 else 0)
            
            # 估值特征
            features.append(row.get('pe_ratio', 0) if pd.notna(row.get('pe_ratio')) else 0)
            features.append(row.get('pb_ratio', 0) if pd.notna(row.get('pb_ratio')) else 0)
            
            # 流动性特征
            total_shares = row.get('total_shares', 0)
            float_shares = row.get('float_shares', 0)
            features.append(float_shares / total_shares if total_shares > 0 else 0)
            
            # 行业编码（简化）
            industry_code = hash(row.get('industry', 'unknown')) % 100
            features.append(industry_code / 100.0)
            
            return features
            
        except Exception as e:
            logger.error(f"股票特征生成失败: {e}")
            return [0.0] * 5
    
    def _generate_company_features(self, row: pd.Series) -> List[float]:
        """生成公司特征向量"""
        try:
            features = []
            
            # 规模特征
            employee_count = row.get('employee_count', 0)
            features.append(np.log1p(employee_count) if employee_count > 0 else 0)
            
            revenue = row.get('revenue', 0)
            features.append(np.log1p(revenue) if revenue > 0 else 0)
            
            # 成立年限
            founded_year = row.get('founded_year', datetime.now().year)
            age = datetime.now().year - founded_year if founded_year > 0 else 0
            features.append(age / 100.0)
            
            # 公司类型编码
            company_type_code = hash(row.get('company_type', 'unknown')) % 50
            features.append(company_type_code / 50.0)
            
            return features
            
        except Exception as e:
            logger.error(f"公司特征生成失败: {e}")
            return [0.0] * 4
    
    def _generate_person_features(self, row: pd.Series) -> List[float]:
        """生成人员特征向量"""
        try:
            features = []
            
            # 年龄特征
            age = row.get('age', 40)
            features.append(age / 100.0 if age > 0 else 0.4)
            
            # 经验特征
            experience = row.get('experience_years', 10)
            features.append(experience / 50.0 if experience > 0 else 0.2)
            
            # 职位级别编码
            position_code = hash(row.get('position', 'unknown')) % 20
            features.append(position_code / 20.0)
            
            return features
            
        except Exception as e:
            logger.error(f"人员特征生成失败: {e}")
            return [0.0] * 3
    
    def _generate_event_features(self, row: pd.Series) -> List[float]:
        """生成事件特征向量"""
        try:
            features = []
            
            # 影响级别
            impact_level = row.get('impact_level', 'medium')
            impact_map = {'low': 0.2, 'medium': 0.5, 'high': 0.8, 'critical': 1.0}
            features.append(impact_map.get(impact_level, 0.5))
            
            # 情感分数
            sentiment = row.get('sentiment', 0.0)
            features.append((sentiment + 1) / 2)  # 归一化到[0,1]
            
            # 事件类型编码
            event_type_code = hash(row.get('event_type', 'unknown')) % 30
            features.append(event_type_code / 30.0)
            
            return features
            
        except Exception as e:
            logger.error(f"事件特征生成失败: {e}")
            return [0.0] * 3
    
    def _generate_industry_features(self, row: pd.Series) -> List[float]:
        """生成行业特征向量"""
        try:
            features = []
            
            # 行业级别
            level = row.get('level', 1)
            features.append(level / 5.0)
            
            # 行业编码
            industry_code = hash(row.get('industry_code', 'unknown')) % 100
            features.append(industry_code / 100.0)
            
            return features
            
        except Exception as e:
            logger.error(f"行业特征生成失败: {e}")
            return [0.0] * 2
    
    def _generate_concept_features(self, row: pd.Series) -> List[float]:
        """生成概念特征向量"""
        try:
            features = []
            
            # 概念类型编码
            concept_type_code = hash(row.get('concept_type', 'unknown')) % 50
            features.append(concept_type_code / 50.0)
            
            # 关键词数量
            keywords = row.get('keywords', [])
            if isinstance(keywords, str):
                keywords = keywords.split(',')
            features.append(len(keywords) / 20.0)
            
            return features
            
        except Exception as e:
            logger.error(f"概念特征生成失败: {e}")
            return [0.0] * 2


class RelationExtractor:
    """关系提取器"""
    
    def __init__(self):
        self.relation_types = {
            'ownership': self._extract_ownership_relations,
            'investment': self._extract_investment_relations,
            'partnership': self._extract_partnership_relations,
            'competition': self._extract_competition_relations,
            'supply_chain': self._extract_supply_chain_relations,
            'correlation': self._extract_correlation_relations,
            'temporal': self._extract_temporal_relations
        }
    
    def extract_relations_from_data(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        relation_type: str,
        extraction_config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """从数据中提取关系"""
        try:
            if relation_type not in self.relation_types:
                raise ValueError(f"Unsupported relation type: {relation_type}")
            
            return self.relation_types[relation_type](entities, data, extraction_config)
            
        except Exception as e:
            logger.error(f"关系提取失败: {e}")
            return []
    
    def _extract_ownership_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取所有权关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            for _, row in data.iterrows():
                owner_id = row.get('owner_entity_id')
                owned_id = row.get('owned_entity_id')
                
                if owner_id in entity_map and owned_id in entity_map:
                    relation = {
                        'relation_id': f"ownership_{owner_id}_{owned_id}",
                        'source_entity_id': entity_map[owner_id].id,
                        'target_entity_id': entity_map[owned_id].id,
                        'relation_type': 'ownership',
                        'relation_name': 'owns',
                        'properties': {
                            'ownership_percentage': row.get('percentage', 0),
                            'ownership_type': row.get('ownership_type', 'direct'),
                            'acquisition_date': row.get('acquisition_date')
                        },
                        'weight': row.get('percentage', 50) / 100.0,
                        'confidence': config.get('confidence', 0.8),
                        'is_directed': True,
                        'edge_features': [
                            row.get('percentage', 50) / 100.0,
                            1.0 if row.get('ownership_type') == 'direct' else 0.5
                        ]
                    }
                    relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"所有权关系提取失败: {e}")
            return []
    
    def _extract_investment_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取投资关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            for _, row in data.iterrows():
                investor_id = row.get('investor_entity_id')
                investee_id = row.get('investee_entity_id')
                
                if investor_id in entity_map and investee_id in entity_map:
                    relation = {
                        'relation_id': f"investment_{investor_id}_{investee_id}",
                        'source_entity_id': entity_map[investor_id].id,
                        'target_entity_id': entity_map[investee_id].id,
                        'relation_type': 'investment',
                        'relation_name': 'invests_in',
                        'properties': {
                            'investment_amount': row.get('amount', 0),
                            'investment_round': row.get('round', 'unknown'),
                            'investment_date': row.get('date')
                        },
                        'weight': min(np.log1p(row.get('amount', 1000000)) / 20, 1.0),
                        'confidence': config.get('confidence', 0.7),
                        'is_directed': True,
                        'edge_features': [
                            min(np.log1p(row.get('amount', 1000000)) / 20, 1.0),
                            hash(row.get('round', 'unknown')) % 10 / 10.0
                        ]
                    }
                    relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"投资关系提取失败: {e}")
            return []
    
    def _extract_partnership_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取合作关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            for _, row in data.iterrows():
                entity1_id = row.get('entity1_id')
                entity2_id = row.get('entity2_id')
                
                if entity1_id in entity_map and entity2_id in entity_map:
                    relation = {
                        'relation_id': f"partnership_{entity1_id}_{entity2_id}",
                        'source_entity_id': entity_map[entity1_id].id,
                        'target_entity_id': entity_map[entity2_id].id,
                        'relation_type': 'partnership',
                        'relation_name': 'partners_with',
                        'properties': {
                            'partnership_type': row.get('partnership_type', 'strategic'),
                            'partnership_scope': row.get('scope', 'business'),
                            'start_date': row.get('start_date')
                        },
                        'weight': row.get('strength', 0.5),
                        'confidence': config.get('confidence', 0.6),
                        'is_directed': False,
                        'is_symmetric': True,
                        'edge_features': [
                            row.get('strength', 0.5),
                            hash(row.get('partnership_type', 'strategic')) % 10 / 10.0
                        ]
                    }
                    relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"合作关系提取失败: {e}")
            return []
    
    def _extract_competition_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取竞争关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            # 基于行业相似性提取竞争关系
            stock_entities = [e for e in entities if e.entity_type == 'stock']
            
            for i, entity1 in enumerate(stock_entities):
                for entity2 in stock_entities[i+1:]:
                    if (entity1.industry == entity2.industry and 
                        entity1.industry and entity1.industry != 'unknown'):
                        
                        # 计算竞争强度
                        market_cap1 = entity1.market_cap or 0
                        market_cap2 = entity2.market_cap or 0
                        
                        if market_cap1 > 0 and market_cap2 > 0:
                            # 市值越接近，竞争越激烈
                            ratio = min(market_cap1, market_cap2) / max(market_cap1, market_cap2)
                            competition_strength = ratio * 0.8  # 最大0.8
                            
                            relation = {
                                'relation_id': f"competition_{entity1.entity_id}_{entity2.entity_id}",
                                'source_entity_id': entity1.id,
                                'target_entity_id': entity2.id,
                                'relation_type': 'competition',
                                'relation_name': 'competes_with',
                                'properties': {
                                    'competition_level': 'direct' if ratio > 0.5 else 'indirect',
                                    'market_overlap': entity1.industry,
                                    'market_cap_ratio': ratio
                                },
                                'weight': competition_strength,
                                'confidence': 0.6,
                                'is_directed': False,
                                'is_symmetric': True,
                                'edge_features': [
                                    competition_strength,
                                    ratio
                                ]
                            }
                            relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"竞争关系提取失败: {e}")
            return []
    
    def _extract_supply_chain_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取供应链关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            for _, row in data.iterrows():
                supplier_id = row.get('supplier_entity_id')
                customer_id = row.get('customer_entity_id')
                
                if supplier_id in entity_map and customer_id in entity_map:
                    relation = {
                        'relation_id': f"supply_chain_{supplier_id}_{customer_id}",
                        'source_entity_id': entity_map[supplier_id].id,
                        'target_entity_id': entity_map[customer_id].id,
                        'relation_type': 'supply_chain',
                        'relation_name': 'supplies_to',
                        'properties': {
                            'supply_type': row.get('supply_type', 'product'),
                            'contract_value': row.get('contract_value', 0),
                            'supply_percentage': row.get('percentage', 0)
                        },
                        'weight': row.get('percentage', 10) / 100.0,
                        'confidence': config.get('confidence', 0.7),
                        'is_directed': True,
                        'edge_features': [
                            row.get('percentage', 10) / 100.0,
                            min(np.log1p(row.get('contract_value', 1000000)) / 15, 1.0)
                        ]
                    }
                    relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"供应链关系提取失败: {e}")
            return []
    
    def _extract_correlation_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取相关性关系"""
        try:
            relations = []
            
            # 基于价格相关性数据提取关系
            if 'correlation_matrix' in data.columns:
                correlation_data = data['correlation_matrix'].iloc[0]
                if isinstance(correlation_data, str):
                    correlation_data = json.loads(correlation_data)
                
                entity_map = {e.stock_code: e for e in entities if e.stock_code}
                
                for stock1, correlations in correlation_data.items():
                    if stock1 in entity_map:
                        for stock2, correlation in correlations.items():
                            if (stock2 in entity_map and stock1 != stock2 and 
                                abs(correlation) > config.get('correlation_threshold', 0.5)):
                                
                                relation = {
                                    'relation_id': f"correlation_{stock1}_{stock2}",
                                    'source_entity_id': entity_map[stock1].id,
                                    'target_entity_id': entity_map[stock2].id,
                                    'relation_type': 'correlation',
                                    'relation_name': 'correlates_with',
                                    'properties': {
                                        'correlation_coefficient': correlation,
                                        'correlation_type': 'positive' if correlation > 0 else 'negative',
                                        'time_period': config.get('time_period', '1year')
                                    },
                                    'weight': abs(correlation),
                                    'confidence': min(abs(correlation) + 0.2, 1.0),
                                    'is_directed': False,
                                    'is_symmetric': True,
                                    'edge_features': [
                                        abs(correlation),
                                        1.0 if correlation > 0 else 0.0
                                    ]
                                }
                                relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"相关性关系提取失败: {e}")
            return []
    
    def _extract_temporal_relations(
        self,
        entities: List[FinancialEntity],
        data: pd.DataFrame,
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """提取时间关系"""
        try:
            relations = []
            entity_map = {e.entity_id: e for e in entities}
            
            for _, row in data.iterrows():
                source_id = row.get('source_entity_id')
                target_id = row.get('target_entity_id')
                
                if source_id in entity_map and target_id in entity_map:
                    relation = {
                        'relation_id': f"temporal_{source_id}_{target_id}",
                        'source_entity_id': entity_map[source_id].id,
                        'target_entity_id': entity_map[target_id].id,
                        'relation_type': 'temporal',
                        'relation_name': row.get('temporal_relation', 'precedes'),
                        'properties': {
                            'temporal_type': row.get('temporal_type', 'sequence'),
                            'time_lag': row.get('time_lag', 0),
                            'causality_strength': row.get('causality', 0.5)
                        },
                        'weight': row.get('causality', 0.5),
                        'confidence': config.get('confidence', 0.4),
                        'is_directed': True,
                        'is_temporal': True,
                        'start_date': pd.to_datetime(row.get('start_date')) if 'start_date' in row else None,
                        'edge_features': [
                            row.get('causality', 0.5),
                            min(row.get('time_lag', 1) / 30, 1.0)  # 归一化时间滞后
                        ]
                    }
                    relations.append(relation)
            
            return relations
            
        except Exception as e:
            logger.error(f"时间关系提取失败: {e}")
            return []


class GraphBuilder:
    """图构建器"""
    
    def __init__(self):
        self.entity_extractor = EntityExtractor()
        self.relation_extractor = RelationExtractor()
    
    async def build_knowledge_graph(
        self,
        user_id: int,
        graph_config: Dict[str, Any],
        data_sources: Dict[str, pd.DataFrame],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """构建知识图谱"""
        try:
            # 创建图谱记录
            knowledge_graph = KnowledgeGraph(
                user_id=user_id,
                graph_name=graph_config['name'],
                description=graph_config.get('description', ''),
                graph_type=graph_config.get('type', 'financial'),
                domain=graph_config.get('domain', 'finance'),
                construction_config=graph_config,
                status="building"
            )
            
            db.add(knowledge_graph)
            await db.commit()
            await db.refresh(knowledge_graph)
            
            # 提取实体
            all_entities = []
            entity_configs = graph_config.get('entity_extraction', {})
            
            for entity_type, config in entity_configs.items():
                if entity_type in data_sources:
                    entities = self.entity_extractor.extract_entities_from_data(
                        data_sources[entity_type], entity_type, config
                    )
                    
                    # 保存实体到数据库
                    for entity_data in entities:
                        entity = FinancialEntity(
                            user_id=user_id,
                            **entity_data
                        )
                        db.add(entity)
                        all_entities.append(entity)
            
            await db.commit()
            
            # 提取关系
            all_relations = []
            relation_configs = graph_config.get('relation_extraction', {})
            
            for relation_type, config in relation_configs.items():
                if relation_type in data_sources:
                    relations = self.relation_extractor.extract_relations_from_data(
                        all_entities, data_sources[relation_type], relation_type, config
                    )
                    
                    # 保存关系到数据库
                    for relation_data in relations:
                        relation = EntityRelation(
                            user_id=user_id,
                            **relation_data
                        )
                        db.add(relation)
                        all_relations.append(relation)
            
            await db.commit()
            
            # 构建图结构
            graph_structure = self._build_graph_structure(all_entities, all_relations)
            
            # 更新图谱统计信息
            knowledge_graph.entity_count = len(all_entities)
            knowledge_graph.relation_count = len(all_relations)
            knowledge_graph.node_count = len(all_entities)
            knowledge_graph.edge_count = len(all_relations)
            knowledge_graph.graph_data = graph_structure
            knowledge_graph.status = "completed"
            knowledge_graph.build_progress = 100
            knowledge_graph.last_build_time = datetime.utcnow()
            
            await db.commit()
            
            return {
                'success': True,
                'graph_id': knowledge_graph.id,
                'entity_count': len(all_entities),
                'relation_count': len(all_relations),
                'graph_structure': graph_structure
            }
            
        except Exception as e:
            logger.error(f"知识图谱构建失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_graph_structure(
        self,
        entities: List[FinancialEntity],
        relations: List[EntityRelation]
    ) -> Dict[str, Any]:
        """构建图结构"""
        try:
            # 构建节点列表
            nodes = []
            for entity in entities:
                node = {
                    'id': entity.id,
                    'entity_id': entity.entity_id,
                    'name': entity.entity_name,
                    'type': entity.entity_type,
                    'category': entity.entity_category,
                    'properties': entity.properties or {},
                    'features': entity.node_features or []
                }
                nodes.append(node)
            
            # 构建边列表
            edges = []
            for relation in relations:
                edge = {
                    'id': relation.id,
                    'source': relation.source_entity_id,
                    'target': relation.target_entity_id,
                    'type': relation.relation_type,
                    'name': relation.relation_name,
                    'weight': relation.weight,
                    'confidence': relation.confidence,
                    'properties': relation.properties or {},
                    'features': relation.edge_features or [],
                    'directed': relation.is_directed
                }
                edges.append(edge)
            
            return {
                'nodes': nodes,
                'edges': edges,
                'node_count': len(nodes),
                'edge_count': len(edges)
            }
            
        except Exception as e:
            logger.error(f"图结构构建失败: {e}")
            return {'nodes': [], 'edges': [], 'node_count': 0, 'edge_count': 0}


# 全局图构建服务实例
graph_builder_service = GraphBuilder()
