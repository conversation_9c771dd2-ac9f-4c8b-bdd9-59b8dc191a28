"""
技术指标计算服务

提供各种技术指标的计算功能，包括趋势、动量、波动率、成交量等指标
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
import talib

from app.core.logging import logger


class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: np.ndarray, period: int) -> np.ndarray:
        """简单移动平均线 (Simple Moving Average)"""
        try:
            return talib.SMA(data, timeperiod=period)
        except Exception as e:
            logger.error(f"SMA计算失败: {e}")
            return np.full_like(data, np.nan)
    
    @staticmethod
    def ema(data: np.ndarray, period: int) -> np.ndarray:
        """指数移动平均线 (Exponential Moving Average)"""
        try:
            return talib.EMA(data, timeperiod=period)
        except Exception as e:
            logger.error(f"EMA计算失败: {e}")
            return np.full_like(data, np.nan)
    
    @staticmethod
    def wma(data: np.ndarray, period: int) -> np.ndarray:
        """加权移动平均线 (Weighted Moving Average)"""
        try:
            return talib.WMA(data, timeperiod=period)
        except Exception as e:
            logger.error(f"WMA计算失败: {e}")
            return np.full_like(data, np.nan)
    
    @staticmethod
    def macd(data: np.ndarray, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """MACD指标 (Moving Average Convergence Divergence)"""
        try:
            macd_line, macd_signal, macd_histogram = talib.MACD(
                data, fastperiod=fast_period, slowperiod=slow_period, signalperiod=signal_period
            )
            return macd_line, macd_signal, macd_histogram
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return np.full_like(data, np.nan), np.full_like(data, np.nan), np.full_like(data, np.nan)
    
    @staticmethod
    def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """相对强弱指数 (Relative Strength Index)"""
        try:
            return talib.RSI(data, timeperiod=period)
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            return np.full_like(data, np.nan)
    
    @staticmethod
    def bollinger_bands(data: np.ndarray, period: int = 20, std_dev: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """布林带 (Bollinger Bands)"""
        try:
            upper_band, middle_band, lower_band = talib.BBANDS(
                data, timeperiod=period, nbdevup=std_dev, nbdevdn=std_dev, matype=0
            )
            return upper_band, middle_band, lower_band
        except Exception as e:
            logger.error(f"布林带计算失败: {e}")
            return np.full_like(data, np.nan), np.full_like(data, np.nan), np.full_like(data, np.nan)
    
    @staticmethod
    def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """随机指标 (Stochastic Oscillator)"""
        try:
            slowk, slowd = talib.STOCH(
                high, low, close, 
                fastk_period=k_period, slowk_period=3, slowk_matype=0,
                slowd_period=d_period, slowd_matype=0
            )
            return slowk, slowd
        except Exception as e:
            logger.error(f"随机指标计算失败: {e}")
            return np.full_like(close, np.nan), np.full_like(close, np.nan)
    
    @staticmethod
    def williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """威廉指标 (Williams %R)"""
        try:
            return talib.WILLR(high, low, close, timeperiod=period)
        except Exception as e:
            logger.error(f"威廉指标计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def cci(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """商品通道指数 (Commodity Channel Index)"""
        try:
            return talib.CCI(high, low, close, timeperiod=period)
        except Exception as e:
            logger.error(f"CCI计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """平均真实波幅 (Average True Range)"""
        try:
            return talib.ATR(high, low, close, timeperiod=period)
        except Exception as e:
            logger.error(f"ATR计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def adx(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """平均趋向指数 (Average Directional Index)"""
        try:
            return talib.ADX(high, low, close, timeperiod=period)
        except Exception as e:
            logger.error(f"ADX计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def obv(close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """能量潮 (On Balance Volume)"""
        try:
            return talib.OBV(close, volume)
        except Exception as e:
            logger.error(f"OBV计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def vwap(high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """成交量加权平均价 (Volume Weighted Average Price)"""
        try:
            typical_price = (high + low + close) / 3
            vwap = np.cumsum(typical_price * volume) / np.cumsum(volume)
            return vwap
        except Exception as e:
            logger.error(f"VWAP计算失败: {e}")
            return np.full_like(close, np.nan)
    
    @staticmethod
    def kdj(high: np.ndarray, low: np.ndarray, close: np.ndarray, 
            period: int = 9, k_period: int = 3, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """KDJ指标"""
        try:
            # 计算RSV
            lowest_low = pd.Series(low).rolling(window=period).min().values
            highest_high = pd.Series(high).rolling(window=period).max().values
            
            rsv = np.where(
                (highest_high - lowest_low) != 0,
                (close - lowest_low) / (highest_high - lowest_low) * 100,
                50
            )
            
            # 计算K值
            k = np.zeros_like(rsv)
            k[0] = 50  # 初始值
            for i in range(1, len(rsv)):
                k[i] = (2/3) * k[i-1] + (1/3) * rsv[i]
            
            # 计算D值
            d = np.zeros_like(k)
            d[0] = 50  # 初始值
            for i in range(1, len(k)):
                d[i] = (2/3) * d[i-1] + (1/3) * k[i]
            
            # 计算J值
            j = 3 * k - 2 * d
            
            return k, d, j
        except Exception as e:
            logger.error(f"KDJ计算失败: {e}")
            return np.full_like(close, np.nan), np.full_like(close, np.nan), np.full_like(close, np.nan)


class CustomIndicators:
    """自定义指标计算类"""
    
    @staticmethod
    def price_channel(high: np.ndarray, low: np.ndarray, period: int = 20) -> Tuple[np.ndarray, np.ndarray]:
        """价格通道"""
        try:
            upper_channel = pd.Series(high).rolling(window=period).max().values
            lower_channel = pd.Series(low).rolling(window=period).min().values
            return upper_channel, lower_channel
        except Exception as e:
            logger.error(f"价格通道计算失败: {e}")
            return np.full_like(high, np.nan), np.full_like(low, np.nan)
    
    @staticmethod
    def support_resistance(close: np.ndarray, window: int = 20, threshold: float = 0.02) -> Dict[str, List[float]]:
        """支撑阻力位识别"""
        try:
            support_levels = []
            resistance_levels = []
            
            # 寻找局部极值点
            for i in range(window, len(close) - window):
                # 局部最低点（支撑位）
                if close[i] == min(close[i-window:i+window+1]):
                    support_levels.append(float(close[i]))
                
                # 局部最高点（阻力位）
                if close[i] == max(close[i-window:i+window+1]):
                    resistance_levels.append(float(close[i]))
            
            # 去重并排序
            support_levels = sorted(list(set(support_levels)))
            resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
            
            return {
                "support": support_levels[:5],  # 返回前5个支撑位
                "resistance": resistance_levels[:5]  # 返回前5个阻力位
            }
        except Exception as e:
            logger.error(f"支撑阻力位计算失败: {e}")
            return {"support": [], "resistance": []}
    
    @staticmethod
    def volatility_bands(close: np.ndarray, period: int = 20, multiplier: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """波动率带"""
        try:
            # 计算移动平均
            ma = pd.Series(close).rolling(window=period).mean().values
            
            # 计算波动率
            returns = np.diff(np.log(close))
            volatility = pd.Series(returns).rolling(window=period-1).std().values
            volatility = np.concatenate([[np.nan], volatility])  # 补齐长度
            
            # 计算上下轨
            upper_band = ma + multiplier * volatility * close
            lower_band = ma - multiplier * volatility * close
            
            return upper_band, ma, lower_band
        except Exception as e:
            logger.error(f"波动率带计算失败: {e}")
            return np.full_like(close, np.nan), np.full_like(close, np.nan), np.full_like(close, np.nan)
    
    @staticmethod
    def trend_strength(close: np.ndarray, period: int = 14) -> np.ndarray:
        """趋势强度指标"""
        try:
            # 计算价格变化
            price_changes = np.diff(close)
            
            # 计算趋势强度
            trend_strength = np.zeros_like(close)
            
            for i in range(period, len(close)):
                recent_changes = price_changes[i-period:i]
                positive_changes = recent_changes[recent_changes > 0]
                negative_changes = recent_changes[recent_changes < 0]
                
                if len(recent_changes) > 0:
                    trend_strength[i] = (len(positive_changes) - len(negative_changes)) / len(recent_changes)
            
            return trend_strength
        except Exception as e:
            logger.error(f"趋势强度计算失败: {e}")
            return np.full_like(close, np.nan)


class IndicatorService:
    """指标服务类"""
    
    def __init__(self):
        self.technical = TechnicalIndicators()
        self.custom = CustomIndicators()
    
    def calculate_indicator(
        self, 
        indicator_name: str, 
        data: Dict[str, np.ndarray], 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算指定技术指标"""
        try:
            close = data.get('close')
            high = data.get('high')
            low = data.get('low')
            volume = data.get('volume')
            
            if close is None:
                raise ValueError("缺少收盘价数据")
            
            result = {}
            
            # 趋势指标
            if indicator_name == 'sma':
                period = parameters.get('period', 20)
                result['sma'] = self.technical.sma(close, period).tolist()
                
            elif indicator_name == 'ema':
                period = parameters.get('period', 20)
                result['ema'] = self.technical.ema(close, period).tolist()
                
            elif indicator_name == 'macd':
                fast = parameters.get('fast_period', 12)
                slow = parameters.get('slow_period', 26)
                signal = parameters.get('signal_period', 9)
                macd_line, macd_signal, macd_hist = self.technical.macd(close, fast, slow, signal)
                result['macd_line'] = macd_line.tolist()
                result['macd_signal'] = macd_signal.tolist()
                result['macd_histogram'] = macd_hist.tolist()
                
            elif indicator_name == 'bollinger_bands':
                period = parameters.get('period', 20)
                std_dev = parameters.get('std_dev', 2.0)
                upper, middle, lower = self.technical.bollinger_bands(close, period, std_dev)
                result['upper_band'] = upper.tolist()
                result['middle_band'] = middle.tolist()
                result['lower_band'] = lower.tolist()
                
            # 动量指标
            elif indicator_name == 'rsi':
                period = parameters.get('period', 14)
                result['rsi'] = self.technical.rsi(close, period).tolist()
                
            elif indicator_name == 'stochastic':
                if high is None or low is None:
                    raise ValueError("随机指标需要高低价数据")
                k_period = parameters.get('k_period', 14)
                d_period = parameters.get('d_period', 3)
                k, d = self.technical.stochastic(high, low, close, k_period, d_period)
                result['stoch_k'] = k.tolist()
                result['stoch_d'] = d.tolist()
                
            elif indicator_name == 'kdj':
                if high is None or low is None:
                    raise ValueError("KDJ指标需要高低价数据")
                period = parameters.get('period', 9)
                k, d, j = self.technical.kdj(high, low, close, period)
                result['k'] = k.tolist()
                result['d'] = d.tolist()
                result['j'] = j.tolist()
                
            # 波动率指标
            elif indicator_name == 'atr':
                if high is None or low is None:
                    raise ValueError("ATR指标需要高低价数据")
                period = parameters.get('period', 14)
                result['atr'] = self.technical.atr(high, low, close, period).tolist()
                
            # 成交量指标
            elif indicator_name == 'obv':
                if volume is None:
                    raise ValueError("OBV指标需要成交量数据")
                result['obv'] = self.technical.obv(close, volume).tolist()
                
            elif indicator_name == 'vwap':
                if high is None or low is None or volume is None:
                    raise ValueError("VWAP指标需要高低价和成交量数据")
                result['vwap'] = self.technical.vwap(high, low, close, volume).tolist()
                
            # 自定义指标
            elif indicator_name == 'support_resistance':
                window = parameters.get('window', 20)
                threshold = parameters.get('threshold', 0.02)
                result.update(self.custom.support_resistance(close, window, threshold))
                
            elif indicator_name == 'volatility_bands':
                period = parameters.get('period', 20)
                multiplier = parameters.get('multiplier', 2.0)
                upper, middle, lower = self.custom.volatility_bands(close, period, multiplier)
                result['upper_band'] = upper.tolist()
                result['middle_band'] = middle.tolist()
                result['lower_band'] = lower.tolist()
                
            else:
                raise ValueError(f"不支持的指标: {indicator_name}")
            
            return {
                "indicator": indicator_name,
                "parameters": parameters,
                "data": result,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"计算指标 {indicator_name} 失败: {e}")
            return {
                "indicator": indicator_name,
                "parameters": parameters,
                "error": str(e),
                "success": False
            }
    
    def get_available_indicators(self) -> Dict[str, Dict[str, Any]]:
        """获取可用指标列表"""
        return {
            "trend": {
                "sma": {"name": "简单移动平均", "parameters": ["period"]},
                "ema": {"name": "指数移动平均", "parameters": ["period"]},
                "macd": {"name": "MACD", "parameters": ["fast_period", "slow_period", "signal_period"]},
                "bollinger_bands": {"name": "布林带", "parameters": ["period", "std_dev"]},
            },
            "momentum": {
                "rsi": {"name": "相对强弱指数", "parameters": ["period"]},
                "stochastic": {"name": "随机指标", "parameters": ["k_period", "d_period"]},
                "kdj": {"name": "KDJ指标", "parameters": ["period"]},
                "williams_r": {"name": "威廉指标", "parameters": ["period"]},
                "cci": {"name": "商品通道指数", "parameters": ["period"]},
            },
            "volatility": {
                "atr": {"name": "平均真实波幅", "parameters": ["period"]},
                "volatility_bands": {"name": "波动率带", "parameters": ["period", "multiplier"]},
            },
            "volume": {
                "obv": {"name": "能量潮", "parameters": []},
                "vwap": {"name": "成交量加权平均价", "parameters": []},
            },
            "custom": {
                "support_resistance": {"name": "支撑阻力位", "parameters": ["window", "threshold"]},
                "trend_strength": {"name": "趋势强度", "parameters": ["period"]},
            }
        }

    async def calculate_williams_r(
        self,
        high_prices: List[float],
        low_prices: List[float],
        close_prices: List[float],
        period: int = 14
    ) -> List[float]:
        """计算威廉指标 %R"""
        try:
            if len(high_prices) < period:
                return []

            williams_r = []

            for i in range(period - 1, len(close_prices)):
                highest_high = max(high_prices[i - period + 1:i + 1])
                lowest_low = min(low_prices[i - period + 1:i + 1])
                current_close = close_prices[i]

                if highest_high == lowest_low:
                    wr = -50.0
                else:
                    wr = -100 * (highest_high - current_close) / (highest_high - lowest_low)

                williams_r.append(wr)

            return williams_r

        except Exception as e:
            logger.error(f"Williams %R calculation failed: {e}")
            return []

    async def calculate_cci(
        self,
        high_prices: List[float],
        low_prices: List[float],
        close_prices: List[float],
        period: int = 20
    ) -> List[float]:
        """计算商品通道指数 CCI"""
        try:
            if len(high_prices) < period:
                return []

            cci_values = []

            # 计算典型价格
            typical_prices = [(h + l + c) / 3 for h, l, c in zip(high_prices, low_prices, close_prices)]

            for i in range(period - 1, len(typical_prices)):
                # 计算简单移动平均
                sma = sum(typical_prices[i - period + 1:i + 1]) / period

                # 计算平均绝对偏差
                mad = sum(abs(tp - sma) for tp in typical_prices[i - period + 1:i + 1]) / period

                if mad == 0:
                    cci = 0
                else:
                    cci = (typical_prices[i] - sma) / (0.015 * mad)

                cci_values.append(cci)

            return cci_values

        except Exception as e:
            logger.error(f"CCI calculation failed: {e}")
            return []

    async def calculate_atr(
        self,
        high_prices: List[float],
        low_prices: List[float],
        close_prices: List[float],
        period: int = 14
    ) -> List[float]:
        """计算平均真实波幅 ATR"""
        try:
            if len(high_prices) < 2:
                return []

            # 计算真实波幅
            true_ranges = []
            for i in range(1, len(high_prices)):
                tr1 = high_prices[i] - low_prices[i]
                tr2 = abs(high_prices[i] - close_prices[i - 1])
                tr3 = abs(low_prices[i] - close_prices[i - 1])
                true_range = max(tr1, tr2, tr3)
                true_ranges.append(true_range)

            if len(true_ranges) < period:
                return []

            # 计算ATR
            atr_values = []

            # 第一个ATR值是简单移动平均
            first_atr = sum(true_ranges[:period]) / period
            atr_values.append(first_atr)

            # 后续ATR值使用指数移动平均
            for i in range(period, len(true_ranges)):
                atr = (atr_values[-1] * (period - 1) + true_ranges[i]) / period
                atr_values.append(atr)

            return atr_values

        except Exception as e:
            logger.error(f"ATR calculation failed: {e}")
            return []


# 全局指标服务实例
indicator_service = IndicatorService()
