# ============================================================================
# 智能量化交易平台 - 后端服务启动脚本
# ============================================================================

Write-Host "🚀 启动智能量化交易平台后端服务..." -ForegroundColor Green

# 检查Python环境
Write-Host "📋 检查Python环境..." -ForegroundColor Yellow
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 错误: 未找到Python，请先安装Python 3.8+" -ForegroundColor Red
    exit 1
}

$pythonVersion = python --version
Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green

# 进入后端目录
Set-Location backend

# 检查虚拟环境
Write-Host "📋 检查虚拟环境..." -ForegroundColor Yellow
if (-not (Test-Path "venv")) {
    Write-Host "⚠️  虚拟环境不存在，正在创建..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host "✅ 虚拟环境创建完成" -ForegroundColor Green
}

# 激活虚拟环境
Write-Host "🔄 激活虚拟环境..." -ForegroundColor Yellow
if (Test-Path "venv\Scripts\Activate.ps1") {
    & "venv\Scripts\Activate.ps1"
} else {
    Write-Host "❌ 错误: 无法激活虚拟环境" -ForegroundColor Red
    exit 1
}

# 检查requirements.txt
if (Test-Path "requirements.txt") {
    Write-Host "📦 安装Python依赖..." -ForegroundColor Yellow
    pip install -r requirements.txt
} else {
    Write-Host "⚠️  requirements.txt不存在，安装基础依赖..." -ForegroundColor Yellow
    pip install fastapi uvicorn sqlalchemy pydantic pydantic-settings python-multipart
    pip install jqdatasdk pandas numpy scipy scikit-learn
    pip install python-jose[cryptography] passlib[bcrypt]
    pip install fastapi-users[sqlalchemy]
}

# 检查环境变量文件
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  .env文件不存在，请配置环境变量" -ForegroundColor Yellow
    Write-Host "📝 创建示例.env文件..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env" -ErrorAction SilentlyContinue
}

# 启动服务
Write-Host "🌟 启动FastAPI服务器..." -ForegroundColor Green
Write-Host "📍 服务地址: http://localhost:8000" -ForegroundColor Cyan
Write-Host "📖 API文档: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "🔄 按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host ""

# 尝试启动主应用
if (Test-Path "app\main.py") {
    Write-Host "🌟 启动完整版应用..." -ForegroundColor Green
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
} elseif (Test-Path "app\main_simple.py") {
    Write-Host "⚠️  使用简化版本启动..." -ForegroundColor Yellow
    python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 --reload
} else {
    Write-Host "❌ 错误: 未找到主应用文件" -ForegroundColor Red
    exit 1
}

Write-Host "👋 后端服务已停止" -ForegroundColor Yellow
