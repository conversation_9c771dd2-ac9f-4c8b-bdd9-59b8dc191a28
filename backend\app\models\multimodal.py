"""
多模态数据相关数据模型

定义新闻情感分析、社交媒体数据、另类数据源等多模态数据结构
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class NewsSource(Base):
    """新闻数据源"""
    
    __tablename__ = "news_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 数据源基本信息
    name = Column(String(100), nullable=False, unique=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    source_type = Column(String(50), default="news")  # news, financial_news, research_report
    
    # API配置
    api_endpoint = Column(String(500))
    api_key_required = Column(Boolean, default=True)
    api_rate_limit = Column(Integer, default=1000)  # 每小时请求限制
    
    # 数据配置
    supported_languages = Column(JSON, default=["zh", "en"])
    supported_categories = Column(JSON)  # 支持的新闻分类
    data_format = Column(String(20), default="json")  # json, xml, rss
    
    # 质量配置
    reliability_score = Column(Float, default=0.8)  # 可靠性评分
    update_frequency = Column(String(20), default="realtime")  # realtime, hourly, daily
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)
    
    # 统计信息
    total_articles = Column(Integer, default=0)
    last_updated = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    news_articles = relationship("NewsArticle", back_populates="source", cascade="all, delete-orphan")


class NewsArticle(Base):
    """新闻文章"""
    
    __tablename__ = "news_articles"
    
    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("news_sources.id"), nullable=False, index=True)
    
    # 文章基本信息
    title = Column(String(500), nullable=False)
    content = Column(Text)
    summary = Column(Text)
    author = Column(String(200))
    category = Column(String(100))
    language = Column(String(10), default="zh")
    
    # URL和标识
    url = Column(String(1000))
    external_id = Column(String(200))  # 外部系统ID
    
    # 时间信息
    published_at = Column(DateTime, nullable=False, index=True)
    crawled_at = Column(DateTime, default=datetime.utcnow)
    
    # 相关性
    related_symbols = Column(JSON)  # 相关股票代码
    related_keywords = Column(JSON)  # 相关关键词
    relevance_score = Column(Float)  # 相关性评分
    
    # 内容质量
    word_count = Column(Integer)
    readability_score = Column(Float)
    credibility_score = Column(Float)
    
    # 传播信息
    view_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    
    # 处理状态
    is_processed = Column(Boolean, default=False)
    processing_status = Column(String(20), default="pending")  # pending, processing, completed, failed
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    source = relationship("NewsSource", back_populates="news_articles")
    sentiment_analysis = relationship("NewsSentiment", back_populates="article", cascade="all, delete-orphan")
    entity_extractions = relationship("NewsEntityExtraction", back_populates="article", cascade="all, delete-orphan")


class NewsSentiment(Base):
    """新闻情感分析结果"""
    
    __tablename__ = "news_sentiments"
    
    id = Column(Integer, primary_key=True, index=True)
    article_id = Column(Integer, ForeignKey("news_articles.id"), nullable=False, index=True)
    
    # 情感分析结果
    sentiment_label = Column(String(20), nullable=False)  # positive, negative, neutral
    sentiment_score = Column(Float, nullable=False)  # [-1, 1]
    confidence = Column(Float, nullable=False)  # [0, 1]
    
    # 细粒度情感
    emotions = Column(JSON)  # 具体情绪：joy, anger, fear, sadness, surprise, disgust
    emotion_scores = Column(JSON)  # 对应的情绪强度
    
    # 主题情感
    topic_sentiments = Column(JSON)  # 不同主题的情感
    aspect_sentiments = Column(JSON)  # 不同方面的情感
    
    # 分析模型信息
    model_name = Column(String(100))
    model_version = Column(String(20))
    analysis_method = Column(String(50))  # bert, lstm, lexicon, ensemble
    
    # 质量指标
    analysis_quality = Column(Float)  # 分析质量评分
    text_complexity = Column(Float)  # 文本复杂度
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    article = relationship("NewsArticle", back_populates="sentiment_analysis")


class NewsEntityExtraction(Base):
    """新闻实体提取结果"""
    
    __tablename__ = "news_entity_extractions"
    
    id = Column(Integer, primary_key=True, index=True)
    article_id = Column(Integer, ForeignKey("news_articles.id"), nullable=False, index=True)
    
    # 实体信息
    entity_text = Column(String(200), nullable=False)
    entity_type = Column(String(50), nullable=False)  # PERSON, ORG, STOCK, PRODUCT, EVENT
    entity_category = Column(String(50))  # 细分类别
    
    # 位置信息
    start_position = Column(Integer)
    end_position = Column(Integer)
    context = Column(String(500))  # 上下文
    
    # 置信度和相关性
    confidence = Column(Float, nullable=False)
    relevance_score = Column(Float)
    importance_score = Column(Float)
    
    # 标准化信息
    normalized_name = Column(String(200))  # 标准化名称
    entity_id = Column(String(100))  # 实体ID（如股票代码）
    
    # 关联信息
    related_entities = Column(JSON)  # 相关实体
    entity_attributes = Column(JSON)  # 实体属性
    
    # 分析模型信息
    extraction_model = Column(String(100))
    model_version = Column(String(20))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    article = relationship("NewsArticle", back_populates="entity_extractions")


class SocialMediaSource(Base):
    """社交媒体数据源"""
    
    __tablename__ = "social_media_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 数据源基本信息
    platform = Column(String(50), nullable=False)  # weibo, twitter, wechat, douban
    platform_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # API配置
    api_endpoint = Column(String(500))
    api_version = Column(String(20))
    authentication_type = Column(String(50))  # oauth, api_key, token
    rate_limit_per_hour = Column(Integer, default=1000)
    
    # 数据配置
    supported_data_types = Column(JSON)  # post, comment, user, trend
    max_history_days = Column(Integer, default=30)
    real_time_support = Column(Boolean, default=False)
    
    # 质量配置
    data_quality_score = Column(Float, default=0.7)
    spam_filter_enabled = Column(Boolean, default=True)
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)
    
    # 统计信息
    total_posts = Column(Integer, default=0)
    active_users = Column(Integer, default=0)
    last_sync = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    social_posts = relationship("SocialMediaPost", back_populates="source", cascade="all, delete-orphan")


class SocialMediaPost(Base):
    """社交媒体帖子"""
    
    __tablename__ = "social_media_posts"
    
    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("social_media_sources.id"), nullable=False, index=True)
    
    # 帖子基本信息
    post_id = Column(String(200), nullable=False)  # 平台内部ID
    content = Column(Text, nullable=False)
    content_type = Column(String(20), default="text")  # text, image, video, link
    language = Column(String(10), default="zh")
    
    # 用户信息
    user_id = Column(String(200))
    username = Column(String(200))
    user_followers = Column(Integer)
    user_verified = Column(Boolean, default=False)
    user_influence_score = Column(Float)
    
    # 时间信息
    posted_at = Column(DateTime, nullable=False, index=True)
    collected_at = Column(DateTime, default=datetime.utcnow)
    
    # 互动数据
    like_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    
    # 传播信息
    is_repost = Column(Boolean, default=False)
    original_post_id = Column(String(200))
    repost_chain_length = Column(Integer, default=0)
    viral_score = Column(Float)  # 病毒传播分数
    
    # 相关性
    related_symbols = Column(JSON)  # 相关股票
    related_topics = Column(JSON)  # 相关话题
    hashtags = Column(JSON)  # 标签
    mentions = Column(JSON)  # 提及的用户
    
    # 质量评估
    spam_score = Column(Float, default=0.0)
    credibility_score = Column(Float)
    influence_score = Column(Float)
    
    # 处理状态
    is_processed = Column(Boolean, default=False)
    processing_status = Column(String(20), default="pending")
    
    # 地理信息
    location = Column(String(200))
    coordinates = Column(JSON)  # [longitude, latitude]
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    source = relationship("SocialMediaSource", back_populates="social_posts")
    sentiment_analysis = relationship("SocialSentiment", back_populates="post", cascade="all, delete-orphan")


class SocialSentiment(Base):
    """社交媒体情感分析"""
    
    __tablename__ = "social_sentiments"
    
    id = Column(Integer, primary_key=True, index=True)
    post_id = Column(Integer, ForeignKey("social_media_posts.id"), nullable=False, index=True)
    
    # 情感分析结果
    sentiment_label = Column(String(20), nullable=False)
    sentiment_score = Column(Float, nullable=False)
    confidence = Column(Float, nullable=False)
    
    # 情绪细分
    emotions = Column(JSON)
    emotion_intensities = Column(JSON)
    
    # 主题情感
    topic_sentiments = Column(JSON)
    stock_sentiments = Column(JSON)  # 对特定股票的情感
    
    # 影响力加权
    weighted_sentiment = Column(Float)  # 考虑用户影响力的加权情感
    viral_adjusted_sentiment = Column(Float)  # 考虑传播度的调整情感
    
    # 分析信息
    analysis_model = Column(String(100))
    model_version = Column(String(20))
    analysis_timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    post = relationship("SocialMediaPost", back_populates="sentiment_analysis")


class AlternativeDataSource(Base):
    """另类数据源"""
    
    __tablename__ = "alternative_data_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 数据源基本信息
    name = Column(String(100), nullable=False, unique=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    data_category = Column(String(50))  # satellite, economic, weather, traffic, etc.
    
    # 数据提供商信息
    provider = Column(String(100))
    provider_contact = Column(String(200))
    data_license = Column(String(100))
    
    # 技术配置
    api_endpoint = Column(String(500))
    data_format = Column(String(20))  # json, csv, xml, binary
    authentication_method = Column(String(50))
    update_frequency = Column(String(50))  # realtime, hourly, daily, weekly, monthly
    
    # 数据特征
    geographic_coverage = Column(JSON)  # 地理覆盖范围
    temporal_coverage = Column(JSON)  # 时间覆盖范围
    data_resolution = Column(String(50))  # 数据分辨率
    data_latency = Column(String(50))  # 数据延迟
    
    # 质量和成本
    data_quality_score = Column(Float, default=0.8)
    cost_per_request = Column(SQLDecimal(10, 4))
    monthly_cost = Column(SQLDecimal(10, 2))
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=True)
    requires_approval = Column(Boolean, default=False)
    
    # 统计信息
    total_requests = Column(Integer, default=0)
    successful_requests = Column(Integer, default=0)
    last_request = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    alternative_data = relationship("AlternativeData", back_populates="source", cascade="all, delete-orphan")


class AlternativeData(Base):
    """另类数据记录"""
    
    __tablename__ = "alternative_data"
    
    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("alternative_data_sources.id"), nullable=False, index=True)
    
    # 数据基本信息
    data_type = Column(String(50), nullable=False)
    data_subtype = Column(String(50))
    data_identifier = Column(String(200))  # 数据唯一标识
    
    # 数据内容
    raw_data = Column(JSON)  # 原始数据
    processed_data = Column(JSON)  # 处理后数据
    metadata = Column(JSON)  # 元数据
    
    # 时空信息
    timestamp = Column(DateTime, nullable=False, index=True)
    location = Column(String(200))
    coordinates = Column(JSON)
    geographic_scope = Column(String(100))
    
    # 相关性
    related_symbols = Column(JSON)  # 相关股票
    related_sectors = Column(JSON)  # 相关行业
    impact_score = Column(Float)  # 影响分数
    
    # 数据质量
    quality_score = Column(Float)
    completeness = Column(Float)  # 完整性
    accuracy = Column(Float)  # 准确性
    timeliness = Column(Float)  # 及时性
    
    # 处理状态
    processing_status = Column(String(20), default="raw")  # raw, processing, processed, validated
    validation_status = Column(String(20), default="pending")  # pending, passed, failed
    
    # 使用统计
    access_count = Column(Integer, default=0)
    last_accessed = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    source = relationship("AlternativeDataSource", back_populates="alternative_data")


class MultimodalSignal(Base):
    """多模态信号"""
    
    __tablename__ = "multimodal_signals"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 信号基本信息
    signal_name = Column(String(100), nullable=False)
    signal_type = Column(String(50), nullable=False)  # sentiment, trend, event, anomaly
    signal_category = Column(String(50))  # news_based, social_based, alternative_based, composite
    
    # 目标信息
    target_symbol = Column(String(20), nullable=False, index=True)
    target_sector = Column(String(50))
    target_market = Column(String(20))
    
    # 信号值
    signal_value = Column(Float, nullable=False)
    signal_strength = Column(Float, nullable=False)  # [0, 1]
    confidence = Column(Float, nullable=False)  # [0, 1]
    
    # 时间信息
    signal_timestamp = Column(DateTime, nullable=False, index=True)
    valid_from = Column(DateTime)
    valid_until = Column(DateTime)
    
    # 数据来源
    data_sources = Column(JSON)  # 数据来源列表
    source_weights = Column(JSON)  # 各数据源权重
    
    # 计算信息
    calculation_method = Column(String(100))
    model_version = Column(String(20))
    feature_importance = Column(JSON)
    
    # 历史表现
    historical_accuracy = Column(Float)
    hit_rate = Column(Float)
    false_positive_rate = Column(Float)
    
    # 影响评估
    market_impact = Column(Float)  # 市场影响评估
    price_correlation = Column(Float)  # 与价格的相关性
    
    # 状态管理
    is_active = Column(Boolean, default=True)
    is_validated = Column(Boolean, default=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        {'mysql_engine': 'InnoDB'},
    )
