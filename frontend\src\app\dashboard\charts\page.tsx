'use client';

/**
 * 高级图表展示页面
 * 
 * 展示各种高级图表类型
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Typography,
  Space,
  Button,
  Row,
  Col,
  Select,
  Alert,
  message
} from 'antd';
import {
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>MapOutlined,
  Radar<PERSON>hartOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import {
  Candlestick<PERSON>hart,
  HeatmapChart,
  RadarChart,
  SankeyChart,
  WaterfallChart
} from '@/components/charts/AdvancedCharts';
import { Combined3DCharts } from '@/components/charts/Advanced3DCharts';
import { ChartConfigurator } from '@/components/charts/ChartConfigurator';
import {
  TechnicalIndicatorChart,
  ReturnDistributionChart,
  CorrelationMatrixChart,
  RiskReturnScatterChart,
  EnhancedPieChart
} from '@/components/charts/FinancialCharts';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

function ChartsContent() {
  const [activeTab, setActiveTab] = useState('candlestick');
  const [selectedSymbol, setSelectedSymbol] = useState('000001.XSHE');
  const [showConfigurator, setShowConfigurator] = useState(false);
  const [chartConfig, setChartConfig] = useState({});

  // 模拟K线数据
  const candlestickData = [
    { date: '2024-08-01', open: 12.50, close: 12.80, low: 12.30, high: 12.90, volume: 1500000 },
    { date: '2024-08-02', open: 12.80, close: 12.60, low: 12.40, high: 12.95, volume: 1200000 },
    { date: '2024-08-03', open: 12.60, close: 12.90, low: 12.55, high: 13.10, volume: 1800000 },
    { date: '2024-08-04', open: 12.90, close: 13.20, low: 12.85, high: 13.30, volume: 2100000 },
    { date: '2024-08-05', open: 13.20, close: 13.10, low: 12.95, high: 13.40, volume: 1900000 },
    { date: '2024-08-06', open: 13.10, close: 13.45, low: 13.05, high: 13.60, volume: 2300000 },
    { date: '2024-08-07', open: 13.45, close: 13.30, low: 13.15, high: 13.55, volume: 1700000 },
    { date: '2024-08-08', open: 13.30, close: 13.65, low: 13.25, high: 13.80, volume: 2500000 }
  ];

  // 模拟技术指标数据
  const technicalIndicators = {
    ma5: [12.65, 12.70, 12.75, 12.85, 12.95, 13.10, 13.20, 13.30],
    ma10: [12.60, 12.65, 12.68, 12.72, 12.78, 12.85, 12.92, 13.00],
    ma20: [12.55, 12.58, 12.60, 12.63, 12.67, 12.72, 12.78, 12.85],
    rsi: [45, 52, 58, 65, 62, 68, 64, 70],
    macd: {
      dif: [0.05, 0.08, 0.12, 0.15, 0.13, 0.18, 0.16, 0.22],
      dea: [0.03, 0.05, 0.08, 0.11, 0.12, 0.14, 0.15, 0.17],
      histogram: [0.02, 0.03, 0.04, 0.04, 0.01, 0.04, 0.01, 0.05]
    }
  };

  // 模拟热力图数据
  const heatmapData: Array<[number, number, number]> = [];
  const sectors = ['金融', '科技', '消费', '医药', '能源'];
  const timePoints = ['09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00'];
  
  for (let i = 0; i < timePoints.length; i++) {
    for (let j = 0; j < sectors.length; j++) {
      heatmapData.push([i, j, Math.random() * 10 - 5]);
    }
  }

  // 模拟雷达图数据
  const radarData = [
    {
      name: '平安银行',
      value: [85, 70, 90, 75, 80, 85]
    },
    {
      name: '招商银行',
      value: [90, 85, 85, 80, 85, 90]
    }
  ];

  const radarIndicators = [
    { name: '盈利能力', max: 100 },
    { name: '成长性', max: 100 },
    { name: '估值水平', max: 100 },
    { name: '财务健康', max: 100 },
    { name: '技术面', max: 100 },
    { name: '市场情绪', max: 100 }
  ];

  // 模拟桑基图数据
  const sankeyData = {
    nodes: [
      { name: '资金流入' },
      { name: '股票投资' },
      { name: '债券投资' },
      { name: '现金管理' },
      { name: '金融股' },
      { name: '科技股' },
      { name: '消费股' },
      { name: '国债' },
      { name: '企业债' }
    ],
    links: [
      { source: '资金流入', target: '股票投资', value: 60 },
      { source: '资金流入', target: '债券投资', value: 30 },
      { source: '资金流入', target: '现金管理', value: 10 },
      { source: '股票投资', target: '金融股', value: 25 },
      { source: '股票投资', target: '科技股', value: 20 },
      { source: '股票投资', target: '消费股', value: 15 },
      { source: '债券投资', target: '国债', value: 20 },
      { source: '债券投资', target: '企业债', value: 10 }
    ]
  };

  // 模拟瀑布图数据
  const waterfallData = [
    { name: '期初资产', value: 1000000 },
    { name: '股票收益', value: 150000 },
    { name: '债券收益', value: 50000 },
    { name: '分红收入', value: 30000 },
    { name: '交易费用', value: -15000 },
    { name: '管理费用', value: -8000 },
    { name: '期末资产', value: 1207000 }
  ];

  // 模拟收益率分布数据
  const returnDistribution = Array.from({ length: 100 }, () => 
    (Math.random() - 0.5) * 20 // -10% 到 10% 的收益率
  );

  // 模拟相关性矩阵数据
  const correlationMatrix = [
    [1.00, 0.75, 0.45, 0.30, -0.20],
    [0.75, 1.00, 0.60, 0.25, -0.15],
    [0.45, 0.60, 1.00, 0.40, -0.10],
    [0.30, 0.25, 0.40, 1.00, 0.05],
    [-0.20, -0.15, -0.10, 0.05, 1.00]
  ];
  const correlationLabels = ['平安银行', '招商银行', '贵州茅台', '五粮液', '比亚迪'];

  // 模拟风险收益散点数据
  const riskReturnData = [
    { name: '平安银行', risk: 15.2, return: 8.5, size: 25 },
    { name: '招商银行', risk: 12.8, return: 12.3, size: 30 },
    { name: '贵州茅台', risk: 18.5, return: 15.7, size: 35 },
    { name: '五粮液', risk: 20.1, return: 11.2, size: 28 },
    { name: '比亚迪', risk: 25.3, return: 22.8, size: 32 }
  ];

  // 模拟资产配置数据
  const assetAllocationData = [
    { name: '股票', value: 650000, return: 12.5, weight: 65 },
    { name: '债券', value: 200000, return: 4.2, weight: 20 },
    { name: '现金', value: 100000, return: 2.1, weight: 10 },
    { name: '其他', value: 50000, return: 8.7, weight: 5 }
  ];

  // 模拟3D图表数据
  const scatter3DData = [
    { name: '平安银行', value: [15.2, 8.5, 0.56], category: 'positive' },
    { name: '招商银行', value: [12.8, 12.3, 0.96], category: 'positive' },
    { name: '贵州茅台', value: [18.5, 15.7, 0.85], category: 'positive' },
    { name: '五粮液', value: [20.1, 11.2, 0.56], category: 'positive' },
    { name: '比亚迪', value: [25.3, 22.8, 0.90], category: 'positive' }
  ];

  const bar3DData = [
    { name: '金融', value: 15.2, category: 'Q1' },
    { name: '科技', value: 22.8, category: 'Q1' },
    { name: '消费', value: 18.5, category: 'Q1' },
    { name: '金融', value: 16.8, category: 'Q2' },
    { name: '科技', value: 25.3, category: 'Q2' },
    { name: '消费', value: 19.2, category: 'Q2' }
  ];

  const surface3DData = Array.from({ length: 6 }, (_, i) =>
    Array.from({ length: 5 }, (_, j) => Math.sin(i * 0.5) * Math.cos(j * 0.5) * 10 + 10)
  );

  const geo3DData = [
    { name: '上海', value: [121.4737, 31.2304, 2500] },
    { name: '北京', value: [116.4074, 39.9042, 3200] },
    { name: '深圳', value: [114.0579, 22.5431, 2800] },
    { name: '广州', value: [113.2644, 23.1291, 2100] }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                <BarChartOutlined />
                高级图表展示
              </Space>
            </Title>
            <Text type="secondary" className="text-lg">
              专业的金融数据可视化图表
            </Text>
          </div>
          <Space>
            <Select
              value={selectedSymbol}
              onChange={setSelectedSymbol}
              style={{ width: 150 }}
            >
              <Option value="000001.XSHE">平安银行</Option>
              <Option value="600036.XSHG">招商银行</Option>
              <Option value="600519.XSHG">贵州茅台</Option>
            </Select>
            <Button
              type={showConfigurator ? 'default' : 'dashed'}
              onClick={() => setShowConfigurator(!showConfigurator)}
            >
              {showConfigurator ? '隐藏配置器' : '图表配置器'}
            </Button>
            <Button type="primary">
              刷新数据
            </Button>
          </Space>
        </div>
      </motion.div>

      {/* 功能说明 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Alert
          message="图表功能说明"
          description="提供多种专业的金融图表类型，包括K线图、技术指标图、热力图、雷达图等，支持交互式操作和数据缩放。所有图表都支持导出和自定义配置。"
          type="info"
          showIcon
          closable
        />
      </motion.div>

      {/* 图表展示 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab} size="large">
            <TabPane
              tab={
                <Space>
                  <LineChartOutlined />
                  K线图
                </Space>
              }
              key="candlestick"
            >
              <CandlestickChart
                data={candlestickData}
                title={`${selectedSymbol} K线图`}
                height={500}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BarChartOutlined />
                  技术指标
                </Space>
              }
              key="technical"
            >
              <TechnicalIndicatorChart
                priceData={candlestickData}
                indicators={technicalIndicators}
                title={`${selectedSymbol} 技术指标图`}
                height={600}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <HeatMapOutlined />
                  热力图
                </Space>
              }
              key="heatmap"
            >
              <HeatmapChart
                data={heatmapData}
                xAxisData={timePoints}
                yAxisData={sectors}
                title="行业热力图"
                height={400}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <RadarChartOutlined />
                  雷达图
                </Space>
              }
              key="radar"
            >
              <RadarChart
                data={radarData}
                indicators={radarIndicators}
                title="股票综合评分雷达图"
                height={400}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <DotChartOutlined />
                  桑基图
                </Space>
              }
              key="sankey"
            >
              <SankeyChart
                data={sankeyData}
                title="资金流向桑基图"
                height={400}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BarChartOutlined />
                  瀑布图
                </Space>
              }
              key="waterfall"
            >
              <WaterfallChart
                data={waterfallData}
                title="投资组合收益瀑布图"
                height={400}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <DotChartOutlined />
                  3D图表
                </Space>
              }
              key="3d"
            >
              <Combined3DCharts
                scatterData={scatter3DData}
                barData={bar3DData}
                surfaceData={surface3DData}
                geoData={geo3DData}
              />
            </TabPane>
          </Tabs>
        </Card>
      </motion.div>

      {/* 图表配置器 */}
      {showConfigurator && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          <ChartConfigurator
            onConfigChange={setChartConfig}
            onSave={(config) => {
              console.log('Chart config saved:', config);
              message.success('图表配置已保存');
            }}
            onPreview={(config) => {
              console.log('Chart config preview:', config);
              message.info('图表预览已更新');
            }}
          />
        </motion.div>
      )}

      {/* 金融专用图表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="收益率分布图">
              <ReturnDistributionChart
                returns={returnDistribution}
                height={300}
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="相关性矩阵图">
              <CorrelationMatrixChart
                correlationMatrix={correlationMatrix}
                labels={correlationLabels}
                height={300}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="风险收益散点图">
              <RiskReturnScatterChart
                data={riskReturnData}
                height={300}
              />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="资产配置饼图">
              <EnhancedPieChart
                data={assetAllocationData}
                height={300}
                showStats={true}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>
    </div>
  );
}

export default function ChartsPage() {
  return (
    <ClientAuthWrapper>
      <ChartsContent />
    </ClientAuthWrapper>
  );
}
