"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ClusterOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ClusterOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ClusterOutlined = function ClusterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ClusterOutlined.default
  }));
};

/**![cluster](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4OCA2ODBoLTU0VjU0MEg1NDZ2LTkyaDIzOGM4LjggMCAxNi03LjIgMTYtMTZWMTY4YzAtOC44LTcuMi0xNi0xNi0xNkgyNDBjLTguOCAwLTE2IDcuMi0xNiAxNnYyNjRjMCA4LjggNy4yIDE2IDE2IDE2aDIzOHY5MkgxOTB2MTQwaC01NGMtNC40IDAtOCAzLjYtOCA4djE3NmMwIDQuNCAzLjYgOCA4IDhoMTc2YzQuNCAwIDgtMy42IDgtOFY2ODhjMC00LjQtMy42LTgtOC04aC01NHYtNzJoMjIwdjcyaC01NGMtNC40IDAtOCAzLjYtOCA4djE3NmMwIDQuNCAzLjYgOCA4IDhoMTc2YzQuNCAwIDgtMy42IDgtOFY2ODhjMC00LjQtMy42LTgtOC04aC01NHYtNzJoMjIwdjcyaC01NGMtNC40IDAtOCAzLjYtOCA4djE3NmMwIDQuNCAzLjYgOCA4IDhoMTc2YzQuNCAwIDgtMy42IDgtOFY2ODhjMC00LjQtMy42LTgtOC04ek0yNTYgODA1LjNjMCAxLjUtMS4yIDIuNy0yLjcgMi43aC01OC43Yy0xLjUgMC0yLjctMS4yLTIuNy0yLjd2LTU4LjdjMC0xLjUgMS4yLTIuNyAyLjctMi43aDU4LjdjMS41IDAgMi43IDEuMiAyLjcgMi43djU4Ljd6bTI4OCAwYzAgMS41LTEuMiAyLjctMi43IDIuN2gtNTguN2MtMS41IDAtMi43LTEuMi0yLjctMi43di01OC43YzAtMS41IDEuMi0yLjcgMi43LTIuN2g1OC43YzEuNSAwIDIuNyAxLjIgMi43IDIuN3Y1OC43ek0yODggMzg0VjIxNmg0NDh2MTY4SDI4OHptNTQ0IDQyMS4zYzAgMS41LTEuMiAyLjctMi43IDIuN2gtNTguN2MtMS41IDAtMi43LTEuMi0yLjctMi43di01OC43YzAtMS41IDEuMi0yLjcgMi43LTIuN2g1OC43YzEuNSAwIDIuNyAxLjIgMi43IDIuN3Y1OC43ek0zNjAgMzAwYTQwIDQwIDAgMTA4MCAwIDQwIDQwIDAgMTAtODAgMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ClusterOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ClusterOutlined';
}
var _default = exports.default = RefIcon;