/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/tailwindcss/base.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/tailwindcss/components.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
@tailwind components;

/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./node_modules/tailwindcss/utilities.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
@tailwind utilities;

/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/responsive.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* ============================================================================
 * 响应式设计样式
 * ============================================================================ */

/* 基础响应式断点 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
}

/* 容器最大宽度 */
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 576px) {
  .container-responsive {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 720px;
    padding: 0 24px;
  }
}

@media (min-width: 992px) {
  .container-responsive {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    max-width: 1140px;
  }
}

@media (min-width: 1600px) {
  .container-responsive {
    max-width: 1520px;
  }
}

/* 响应式网格系统 */
.grid-responsive {
  display: grid;
  grid-gap: 16px;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .grid-responsive {
    gap: 20px;
  }
  
  .grid-responsive.cols-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-responsive {
    gap: 24px;
  }
  
  .grid-responsive.cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.cols-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-responsive.cols-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 992px) {
  .grid-responsive.cols-lg-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-responsive.cols-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-responsive.cols-lg-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .grid-responsive.cols-lg-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (min-width: 1200px) {
  .grid-responsive.cols-xl-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .grid-responsive.cols-xl-8 {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* 响应式字体大小 */
.text-responsive {
  font-size: 14px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 16px;
  }
}

.text-responsive-sm {
  font-size: 12px;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 14px;
  }
}

.text-responsive-lg {
  font-size: 16px;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .text-responsive-lg {
    font-size: 20px;
  }
}

/* 响应式间距 */
.spacing-responsive {
  padding: 12px;
  margin: 8px 0;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 16px;
    margin: 12px 0;
  }
}

@media (min-width: 1200px) {
  .spacing-responsive {
    padding: 24px;
    margin: 16px 0;
  }
}

/* 响应式卡片 */
.card-responsive {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: white;
  overflow: hidden;
}

@media (min-width: 768px) {
  .card-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 响应式表格 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 767px) {
  .table-responsive table {
    font-size: 12px;
  }
  
  .table-responsive th,
  .table-responsive td {
    padding: 8px 4px;
    white-space: nowrap;
  }
}

/* 响应式按钮 */
.button-responsive {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  min-height: 32px;
}

@media (min-width: 768px) {
  .button-responsive {
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 6px;
    min-height: 36px;
  }
}

.button-responsive-sm {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  min-height: 24px;
}

@media (min-width: 768px) {
  .button-responsive-sm {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    min-height: 28px;
  }
}

/* 响应式导航 */
.nav-responsive {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (min-width: 768px) {
  .nav-responsive {
    flex-direction: row;
    gap: 16px;
  }
}

/* 响应式侧边栏 */
.sidebar-responsive {
  width: 100%;
  position: relative;
}

@media (min-width: 768px) {
  .sidebar-responsive {
    width: 240px;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
  }
}

@media (min-width: 1200px) {
  .sidebar-responsive {
    width: 280px;
  }
}

/* 响应式主内容区 */
.main-content-responsive {
  width: 100%;
  padding: 16px;
}

@media (min-width: 768px) {
  .main-content-responsive {
    margin-left: 240px;
    padding: 24px;
  }
}

@media (min-width: 1200px) {
  .main-content-responsive {
    margin-left: 280px;
    padding: 32px;
  }
}

/* 响应式图表容器 */
.chart-responsive {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

@media (min-width: 576px) {
  .chart-responsive {
    height: 250px;
    min-height: 250px;
  }
}

@media (min-width: 768px) {
  .chart-responsive {
    height: 300px;
    min-height: 300px;
  }
}

@media (min-width: 1200px) {
  .chart-responsive {
    height: 400px;
    min-height: 400px;
  }
}

/* 响应式模态框 */
.modal-responsive {
  width: 95%;
  max-width: 520px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modal-responsive {
    width: 80%;
    max-width: 720px;
  }
}

@media (min-width: 1200px) {
  .modal-responsive {
    width: 60%;
    max-width: 900px;
  }
}

/* 响应式抽屉 */
.drawer-responsive {
  width: 100%;
}

@media (min-width: 768px) {
  .drawer-responsive {
    width: 400px;
  }
}

@media (min-width: 1200px) {
  .drawer-responsive {
    width: 500px;
  }
}

/* 隐藏/显示工具类 */
.hidden-xs {
  display: none;
}

@media (min-width: 576px) {
  .hidden-xs {
    display: block;
  }
}

.hidden-sm {
  display: block;
}

@media (min-width: 576px) and (max-width: 767px) {
  .hidden-sm {
    display: none;
  }
}

.hidden-md {
  display: block;
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-md {
    display: none;
  }
}

.hidden-lg {
  display: block;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-lg {
    display: none;
  }
}

.hidden-xl {
  display: block;
}

@media (min-width: 1200px) {
  .hidden-xl {
    display: none;
  }
}

/* 仅在特定尺寸显示 */
.visible-xs {
  display: block;
}

@media (min-width: 576px) {
  .visible-xs {
    display: none;
  }
}

.visible-sm {
  display: none;
}

@media (min-width: 576px) and (max-width: 767px) {
  .visible-sm {
    display: block;
  }
}

.visible-md {
  display: none;
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-md {
    display: block;
  }
}

.visible-lg {
  display: none;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-lg {
    display: block;
  }
}

.visible-xl {
  display: none;
}

@media (min-width: 1200px) {
  .visible-xl {
    display: block;
  }
}

/* 响应式Flex布局 */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    gap: 16px;
  }
}

.flex-responsive-reverse {
  display: flex;
  flex-direction: column-reverse;
  gap: 12px;
}

@media (min-width: 768px) {
  .flex-responsive-reverse {
    flex-direction: row-reverse;
    gap: 16px;
  }
}

/* 响应式对齐 */
.text-center-mobile {
  text-align: center;
}

@media (min-width: 768px) {
  .text-center-mobile {
    text-align: left;
  }
}

/* 响应式滚动 */
.scroll-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.scroll-responsive::-webkit-scrollbar {
  height: 6px;
}

.scroll-responsive::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-responsive::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.scroll-responsive::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/* ============================================================================
 * 全局样式 - 量化交易平台
 * ============================================================================ */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --primary-color-light: #e6f7ff;
  
  /* 成功色 */
  --success-color: #52c41a;
  --success-color-hover: #73d13d;
  --success-color-active: #389e0d;
  --success-color-light: #f6ffed;
  
  /* 警告色 */
  --warning-color: #faad14;
  --warning-color-hover: #ffc53d;
  --warning-color-active: #d48806;
  --warning-color-light: #fffbe6;
  
  /* 错误色 */
  --error-color: #ff4d4f;
  --error-color-hover: #ff7875;
  --error-color-active: #d9363e;
  --error-color-light: #fff2f0;
  
  /* 中性色 */
  --text-color: #262626;
  --text-color-secondary: #8c8c8c;
  --text-color-disabled: #bfbfbf;
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  --background-color: #ffffff;
  --background-color-light: #fafafa;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 圆角 */
  --border-radius-sm: 4px;
  --border-radius: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing: 12px;
  --spacing-md: 16px;
  --spacing-lg: 20px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* 暗色主题变量 */
[data-theme='dark'] {
  --text-color: #ffffff;
  --text-color-secondary: #a6a6a6;
  --text-color-disabled: #595959;
  --border-color: #434343;
  --border-color-light: #303030;
  --background-color: #141414;
  --background-color-light: #1f1f1f;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color-light);
  line-height: var(--line-height-normal);
  transition: color var(--transition), background-color var(--transition);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-color-hover);
}

a:active {
  color: var(--primary-color-active);
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all var(--transition-fast);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  text-align: left;
  padding: var(--spacing-sm) var(--spacing);
  border-bottom: 1px solid var(--border-color-light);
}

th {
  font-weight: 600;
  background-color: var(--background-color-light);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-color-light);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--border-radius-sm);
  -webkit-transition: background var(--transition-fast);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-secondary);
}

/* 工具类 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-word;
  overflow-wrap: break-word;
}

.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/* 动画类 */
.fade-in {
  animation: fadeIn var(--transition) ease-in-out;
}

.fade-out {
  animation: fadeOut var(--transition) ease-in-out;
}

.slide-in-up {
  animation: slideInUp var(--transition) ease-out;
}

.slide-in-down {
  animation: slideInDown var(--transition) ease-out;
}

.slide-in-left {
  animation: slideInLeft var(--transition) ease-out;
}

.slide-in-right {
  animation: slideInRight var(--transition) ease-out;
}

.bounce-in {
  animation: bounceIn var(--transition-slow) ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

/* 关键帧动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义组件样式 */
.trading-card {
  background: var(--background-color);
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast), transform var(--transition-fast);
}

.trading-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.price-up {
  color: var(--error-color);
}

.price-down {
  color: var(--success-color);
}

.price-neutral {
  color: var(--text-color-secondary);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1.2;
}

.status-badge.success {
  background-color: var(--success-color-light);
  color: var(--success-color);
}

.status-badge.warning {
  background-color: var(--warning-color-light);
  color: var(--warning-color);
}

.status-badge.error {
  background-color: var(--error-color-light);
  color: var(--error-color);
}

.status-badge.info {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
}

/* 图表容器样式 */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

/* 数据表格样式 */
.data-table {
  background: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table .ant-table-thead > tr > th {
  background: var(--background-color-light);
  border-bottom: 2px solid var(--border-color-light);
  font-weight: 600;
  color: var(--text-color);
}

.data-table .ant-table-tbody > tr:hover > td {
  background: var(--primary-color-light);
}

/* 响应式隐藏类 */
@media (max-width: 767px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .show-mobile {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .trading-card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}

