'use client';

/**
 * 深度学习仪表板
 * 
 * 提供Transformer、GAN、强化学习等深度学习模型的管理界面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Descriptions,
  Alert,
  Badge,
  Tooltip
} from 'antd';
import {
  RobotOutlined,
  ExperimentOutlined,
  ThunderboltOutlined,
  BrainOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  PlusOutlined,
  SettingOutlined,
  TrophyOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface DeepLearningModel {
  id: number;
  name: string;
  description: string;
  model_type?: string;
  gan_type?: string;
  agent_type?: string;
  status: string;
  training_progress?: number;
  test_mse?: number;
  test_mae?: number;
  generator_loss?: number;
  discriminator_loss?: number;
  average_reward?: number;
  max_reward?: number;
  created_at: string;
  trained_at?: string;
}

export const DeepLearningDashboard: React.FC = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('transformer');
  const [transformerModels, setTransformerModels] = useState<DeepLearningModel[]>([]);
  const [ganModels, setGanModels] = useState<DeepLearningModel[]>([]);
  const [rlAgents, setRlAgents] = useState<DeepLearningModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedModel, setSelectedModel] = useState<DeepLearningModel | null>(null);

  useEffect(() => {
    fetchModels();
  }, [activeTab]);

  const fetchModels = async () => {
    setLoading(true);
    try {
      let endpoint = '';
      switch (activeTab) {
        case 'transformer':
          endpoint = '/api/v1/deep-learning/transformer/models';
          break;
        case 'gan':
          endpoint = '/api/v1/deep-learning/gan/models';
          break;
        case 'rl':
          endpoint = '/api/v1/deep-learning/rl/agents';
          break;
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        switch (activeTab) {
          case 'transformer':
            setTransformerModels(result.data.items);
            break;
          case 'gan':
            setGanModels(result.data.items);
            break;
          case 'rl':
            setRlAgents(result.data.items);
            break;
        }
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      message.error('获取模型列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateModel = async (values: any) => {
    try {
      let endpoint = '';
      switch (activeTab) {
        case 'transformer':
          endpoint = '/api/v1/deep-learning/transformer/models';
          break;
        case 'gan':
          endpoint = '/api/v1/deep-learning/gan/models';
          break;
        case 'rl':
          endpoint = '/api/v1/deep-learning/rl/agents';
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('模型创建成功');
        setCreateModalVisible(false);
        form.resetFields();
        fetchModels();
      } else {
        message.error(result.message || '模型创建失败');
      }
    } catch (error) {
      console.error('创建模型失败:', error);
      message.error('创建模型失败');
    }
  };

  const handleTrainModel = async (modelId: number) => {
    try {
      let endpoint = '';
      switch (activeTab) {
        case 'transformer':
          endpoint = `/api/v1/deep-learning/transformer/models/${modelId}/train`;
          break;
        case 'gan':
          endpoint = `/api/v1/deep-learning/gan/models/${modelId}/train`;
          break;
        case 'rl':
          endpoint = `/api/v1/deep-learning/rl/agents/${modelId}/train`;
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          // 这里应该包含训练数据配置
          symbols: ['000001.XSHE', '000002.XSHE'],
          start_date: '2020-01-01',
          end_date: '2023-12-31',
          num_episodes: 1000 // 仅用于强化学习
        })
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('训练任务已提交，请稍后查看结果');
        fetchModels();
      } else {
        message.error(result.message || '提交训练任务失败');
      }
    } catch (error) {
      console.error('训练模型失败:', error);
      message.error('训练模型失败');
    }
  };

  const showModelDetail = (model: DeepLearningModel) => {
    setSelectedModel(model);
    setDetailModalVisible(true);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'created': 'default',
      'training': 'processing',
      'trained': 'success',
      'failed': 'error',
      'deployed': 'success'
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusText = (status: string) => {
    const texts = {
      'created': '已创建',
      'training': '训练中',
      'trained': '已训练',
      'failed': '训练失败',
      'deployed': '已部署'
    };
    return texts[status as keyof typeof texts] || status;
  };

  const getModelIcon = (type: string) => {
    const icons = {
      'transformer': <LineChartOutlined />,
      'gan': <ExperimentOutlined />,
      'rl': <RobotOutlined />
    };
    return icons[type as keyof typeof icons] || <BrainOutlined />;
  };

  const transformerColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DeepLearningModel) => (
        <Space>
          <LineChartOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '模型类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (type: string) => (
        <Tag color="blue">{type?.toUpperCase()}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: DeepLearningModel) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
          {record.training_progress !== undefined && record.training_progress < 100 && (
            <Progress 
              percent={record.training_progress} 
              size="small" 
              showInfo={false}
            />
          )}
        </Space>
      )
    },
    {
      title: '性能指标',
      key: 'metrics',
      render: (_, record: DeepLearningModel) => (
        <Space direction="vertical" size="small">
          {record.test_mse && (
            <span className="text-xs">MSE: {record.test_mse.toFixed(4)}</span>
          )}
          {record.test_mae && (
            <span className="text-xs">MAE: {record.test_mae.toFixed(4)}</span>
          )}
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DeepLearningModel) => (
        <Space>
          {record.status === 'created' && (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTrainModel(record.id)}
            >
              训练
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showModelDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  const ganColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DeepLearningModel) => (
        <Space>
          <ExperimentOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: 'GAN类型',
      dataIndex: 'gan_type',
      key: 'gan_type',
      render: (type: string) => (
        <Tag color="purple">{type?.toUpperCase()}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: DeepLearningModel) => (
        <Space direction="vertical" size="small">
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
          {record.training_progress !== undefined && record.training_progress < 100 && (
            <Progress 
              percent={record.training_progress} 
              size="small" 
              showInfo={false}
            />
          )}
        </Space>
      )
    },
    {
      title: '损失函数',
      key: 'loss',
      render: (_, record: DeepLearningModel) => (
        <Space direction="vertical" size="small">
          {record.generator_loss && (
            <span className="text-xs">G: {record.generator_loss.toFixed(4)}</span>
          )}
          {record.discriminator_loss && (
            <span className="text-xs">D: {record.discriminator_loss.toFixed(4)}</span>
          )}
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DeepLearningModel) => (
        <Space>
          {record.status === 'created' && (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTrainModel(record.id)}
            >
              训练
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showModelDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  const rlColumns = [
    {
      title: '智能体名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: DeepLearningModel) => (
        <Space>
          <RobotOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '算法类型',
      dataIndex: 'agent_type',
      key: 'agent_type',
      render: (type: string) => (
        <Tag color="green">{type?.toUpperCase()}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '性能指标',
      key: 'performance',
      render: (_, record: DeepLearningModel) => (
        <Space direction="vertical" size="small">
          {record.average_reward && (
            <span className="text-xs">平均奖励: {record.average_reward.toFixed(2)}</span>
          )}
          {record.max_reward && (
            <span className="text-xs">最大奖励: {record.max_reward.toFixed(2)}</span>
          )}
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DeepLearningModel) => (
        <Space>
          {record.status === 'created' && (
            <Button
              type="primary"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleTrainModel(record.id)}
            >
              训练
            </Button>
          )}
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showModelDetail(record)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  const renderCreateForm = () => {
    switch (activeTab) {
      case 'transformer':
        return (
          <>
            <Form.Item name="name" label="模型名称" rules={[{ required: true }]}>
              <Input placeholder="请输入模型名称" />
            </Form.Item>
            <Form.Item name="description" label="模型描述">
              <TextArea rows={3} placeholder="请输入模型描述" />
            </Form.Item>
            <Form.Item name="model_type" label="模型类型" initialValue="transformer">
              <Select>
                <Option value="transformer">Transformer</Option>
                <Option value="informer">Informer</Option>
                <Option value="autoformer">Autoformer</Option>
              </Select>
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="sequence_length" label="序列长度" initialValue={60}>
                  <InputNumber min={10} max={200} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="prediction_length" label="预测长度" initialValue={1}>
                  <InputNumber min={1} max={30} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="d_model" label="模型维度" initialValue={512}>
                  <Select>
                    <Option value={256}>256</Option>
                    <Option value={512}>512</Option>
                    <Option value={1024}>1024</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="n_heads" label="注意力头数" initialValue={8}>
                  <Select>
                    <Option value={4}>4</Option>
                    <Option value={8}>8</Option>
                    <Option value={16}>16</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </>
        );
      case 'gan':
        return (
          <>
            <Form.Item name="name" label="模型名称" rules={[{ required: true }]}>
              <Input placeholder="请输入模型名称" />
            </Form.Item>
            <Form.Item name="description" label="模型描述">
              <TextArea rows={3} placeholder="请输入模型描述" />
            </Form.Item>
            <Form.Item name="gan_type" label="GAN类型" initialValue="vanilla">
              <Select>
                <Option value="vanilla">Vanilla GAN</Option>
                <Option value="dcgan">DCGAN</Option>
                <Option value="wgan">WGAN</Option>
              </Select>
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="generator_input_dim" label="噪声维度" initialValue={100}>
                  <InputNumber min={50} max={500} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="sequence_length" label="序列长度" initialValue={60}>
                  <InputNumber min={10} max={200} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </>
        );
      case 'rl':
        return (
          <>
            <Form.Item name="name" label="智能体名称" rules={[{ required: true }]}>
              <Input placeholder="请输入智能体名称" />
            </Form.Item>
            <Form.Item name="description" label="智能体描述">
              <TextArea rows={3} placeholder="请输入智能体描述" />
            </Form.Item>
            <Form.Item name="agent_type" label="算法类型" initialValue="dqn">
              <Select>
                <Option value="dqn">DQN</Option>
                <Option value="ddpg">DDPG</Option>
                <Option value="ppo">PPO</Option>
                <Option value="sac">SAC</Option>
              </Select>
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="state_space_dim" label="状态空间维度" initialValue={100}>
                  <InputNumber min={10} max={1000} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="action_space_dim" label="动作空间维度" initialValue={3}>
                  <InputNumber min={2} max={20} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="learning_rate" label="学习率" initialValue={0.001}>
                  <InputNumber min={0.0001} max={0.1} step={0.0001} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="gamma" label="折扣因子" initialValue={0.99}>
                  <InputNumber min={0.9} max={0.999} step={0.001} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">深度学习模型</h1>
          <p className="text-gray-600">管理Transformer、GAN、强化学习等深度学习模型</p>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建模型
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Transformer模型"
              value={transformerModels.length}
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="GAN模型"
              value={ganModels.length}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="强化学习智能体"
              value={rlAgents.length}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="训练中模型"
              value={[...transformerModels, ...ganModels, ...rlAgents].filter(m => m.status === 'training').length}
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 模型列表 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Badge count={transformerModels.length} size="small">
                <Space>
                  <LineChartOutlined />
                  <span>Transformer</span>
                </Space>
              </Badge>
            }
            key="transformer"
          >
            <Table
              columns={transformerColumns}
              dataSource={transformerModels}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个模型`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={ganModels.length} size="small">
                <Space>
                  <ExperimentOutlined />
                  <span>GAN</span>
                </Space>
              </Badge>
            }
            key="gan"
          >
            <Table
              columns={ganColumns}
              dataSource={ganModels}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个模型`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={rlAgents.length} size="small">
                <Space>
                  <RobotOutlined />
                  <span>强化学习</span>
                </Space>
              </Badge>
            }
            key="rl"
          >
            <Table
              columns={rlColumns}
              dataSource={rlAgents}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个智能体`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建模型弹窗 */}
      <Modal
        title={`创建${activeTab === 'transformer' ? 'Transformer' : activeTab === 'gan' ? 'GAN' : '强化学习'}模型`}
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateModel}
        >
          {renderCreateForm()}
        </Form>
      </Modal>

      {/* 模型详情弹窗 */}
      <Modal
        title="模型详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedModel && (
          <div className="space-y-4">
            <Alert
              message="模型信息"
              description="以下是模型的详细配置和性能指标"
              type="info"
              showIcon
            />

            <Descriptions column={2} bordered>
              <Descriptions.Item label="模型名称">
                {selectedModel.name}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(selectedModel.status)}>
                  {getStatusText(selectedModel.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(selectedModel.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="训练时间">
                {selectedModel.trained_at ? 
                  dayjs(selectedModel.trained_at).format('YYYY-MM-DD HH:mm:ss') : 
                  '未训练'
                }
              </Descriptions.Item>
            </Descriptions>

            {selectedModel.description && (
              <div>
                <h4 className="font-medium mb-2">模型描述</h4>
                <p className="text-gray-600">{selectedModel.description}</p>
              </div>
            )}

            {/* 性能指标 */}
            {(selectedModel.test_mse || selectedModel.test_mae || selectedModel.average_reward) && (
              <div>
                <h4 className="font-medium mb-2">性能指标</h4>
                <Row gutter={16}>
                  {selectedModel.test_mse && (
                    <Col span={8}>
                      <Statistic
                        title="MSE"
                        value={selectedModel.test_mse}
                        precision={4}
                      />
                    </Col>
                  )}
                  {selectedModel.test_mae && (
                    <Col span={8}>
                      <Statistic
                        title="MAE"
                        value={selectedModel.test_mae}
                        precision={4}
                      />
                    </Col>
                  )}
                  {selectedModel.average_reward && (
                    <Col span={8}>
                      <Statistic
                        title="平均奖励"
                        value={selectedModel.average_reward}
                        precision={2}
                      />
                    </Col>
                  )}
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};
