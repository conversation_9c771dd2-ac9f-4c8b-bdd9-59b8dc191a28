"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Chart = exports.stdlib = exports.geolib = exports.graphlib = exports.plotlib = exports.corelib = exports.litelib = void 0;
const lib_1 = require("./lib");
Object.defineProperty(exports, "litelib", { enumerable: true, get: function () { return lib_1.litelib; } });
Object.defineProperty(exports, "corelib", { enumerable: true, get: function () { return lib_1.corelib; } });
Object.defineProperty(exports, "plotlib", { enumerable: true, get: function () { return lib_1.plotlib; } });
Object.defineProperty(exports, "graphlib", { enumerable: true, get: function () { return lib_1.graphlib; } });
Object.defineProperty(exports, "geolib", { enumerable: true, get: function () { return lib_1.geolib; } });
Object.defineProperty(exports, "stdlib", { enumerable: true, get: function () { return lib_1.stdlib; } });
const api_1 = require("./api");
__exportStar(require("./exports"), exports);
/**
 * G2 standard library initial all the libs except 3D and auto.
 */
const library = Object.assign({}, (0, lib_1.stdlib)());
exports.Chart = (0, api_1.extend)(api_1.Runtime, library);
//# sourceMappingURL=index.js.map