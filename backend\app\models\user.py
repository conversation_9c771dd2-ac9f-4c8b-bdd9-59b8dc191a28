"""
用户相关数据模型

包含用户信息、JQData配置、权限管理等
"""

from datetime import datetime
from typing import Optional

from fastapi_users.db import SQLAlchemyBaseUserTable
from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, Integer, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base, BaseModel


class User(SQLAlchemyBaseUserTable[int], Base):
    """用户模型"""
    
    __tablename__ = "users"
    
    # FastAPI-Users 必需字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    email: Mapped[str] = mapped_column(String(320), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(1024), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # 扩展字段
    username: Mapped[str] = mapped_column(
        String(50), 
        unique=True, 
        index=True, 
        nullable=False,
        comment="用户名"
    )
    
    full_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="真实姓名"
    )
    
    phone: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True,
        comment="手机号码"
    )
    
    avatar_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    # 订阅信息
    subscription_type: Mapped[str] = mapped_column(
        String(20),
        default="free",
        nullable=False,
        comment="订阅类型: free, basic, premium, enterprise"
    )
    
    subscription_expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="订阅到期时间"
    )
    
    # 使用统计
    api_quota_daily: Mapped[int] = mapped_column(
        Integer,
        default=1000,
        nullable=False,
        comment="每日API配额"
    )
    
    api_quota_used_today: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="今日已使用配额"
    )
    
    last_quota_reset: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="配额最后重置时间"
    )
    
    # 安全信息
    failed_login_attempts: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="失败登录次数"
    )
    
    locked_until: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="账户锁定到期时间"
    )
    
    last_login_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后登录时间"
    )
    
    last_login_ip: Mapped[Optional[str]] = mapped_column(
        String(45),
        nullable=True,
        comment="最后登录IP"
    )
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 关系
    jqdata_configs = relationship("JQDataConfig", back_populates="user", cascade="all, delete-orphan")
    strategies = relationship("Strategy", back_populates="user", cascade="all, delete-orphan")
    # backtest_tasks = relationship("BacktestTask", back_populates="user", cascade="all, delete-orphan")
    # backtest_templates = relationship("BacktestTemplate", back_populates="user", cascade="all, delete-orphan")
    # risk_profile = relationship("RiskProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    # risk_metrics = relationship("RiskMetrics", back_populates="user", cascade="all, delete-orphan")
    # risk_alerts = relationship("RiskAlert", back_populates="user", cascade="all, delete-orphan")
    # stop_loss_orders = relationship("StopLossOrder", back_populates="user", cascade="all, delete-orphan")
    # risk_scenarios = relationship("RiskScenario", back_populates="user", cascade="all, delete-orphan")
    # 暂时注释掉不存在的关系，等相关模型创建后再启用
    # report_templates = relationship("ReportTemplate", back_populates="user", cascade="all, delete-orphan")
    # report_tasks = relationship("ReportTask", back_populates="user", cascade="all, delete-orphan")
    # report_history = relationship("ReportHistory", back_populates="user", cascade="all, delete-orphan")
    # report_subscriptions = relationship("ReportSubscription", back_populates="user", cascade="all, delete-orphan")
    # report_analytics = relationship("ReportAnalytics", back_populates="user", cascade="all, delete-orphan")
    # ml_models = relationship("MLModel", back_populates="user", cascade="all, delete-orphan")
    # ml_predictions = relationship("MLPrediction", back_populates="user", cascade="all, delete-orphan")
    # ml_training_jobs = relationship("MLTrainingJob", back_populates="user", cascade="all, delete-orphan")
    # ml_recommendations = relationship("MLRecommendation", back_populates="user", cascade="all, delete-orphan")
    # ml_experiments = relationship("MLExperiment", back_populates="user", cascade="all, delete-orphan")
    # transformer_models = relationship("TransformerModel", back_populates="user", cascade="all, delete-orphan")
    # transformer_predictions = relationship("TransformerPrediction", back_populates="user", cascade="all, delete-orphan")
    # gan_models = relationship("GANModel", back_populates="user", cascade="all, delete-orphan")
    # gan_generated_data = relationship("GANGeneratedData", back_populates="user", cascade="all, delete-orphan")
    # rl_agents = relationship("RLAgent", back_populates="user", cascade="all, delete-orphan")
    # rl_trading_sessions = relationship("RLTradingSession", back_populates="user", cascade="all, delete-orphan")
    # automl_experiments = relationship("AutoMLExperiment", back_populates="user", cascade="all, delete-orphan")
    # automl_models = relationship("AutoMLModel", back_populates="user", cascade="all, delete-orphan")
    # automl_predictions = relationship("AutoMLPrediction", back_populates="user", cascade="all, delete-orphan")
    # model_ensembles = relationship("ModelEnsemble", back_populates="user", cascade="all, delete-orphan")
    # automl_pipelines = relationship("AutoMLPipeline", back_populates="user", cascade="all, delete-orphan")
    explainability_analyses = relationship("ExplainabilityAnalysis", back_populates="user", cascade="all, delete-orphan")
    interpretability_metrics = relationship("ModelInterpretabilityMetrics", back_populates="user", cascade="all, delete-orphan")
    knowledge_graphs = relationship("KnowledgeGraph", back_populates="user", cascade="all, delete-orphan")
    financial_entities = relationship("FinancialEntity", back_populates="user", cascade="all, delete-orphan")
    entity_relations = relationship("EntityRelation", back_populates="user", cascade="all, delete-orphan")
    graph_analyses = relationship("GraphAnalysis", back_populates="user", cascade="all, delete-orphan")
    gnn_models = relationship("GNNModel", back_populates="user", cascade="all, delete-orphan")
    gnn_predictions = relationship("GNNPrediction", back_populates="user", cascade="all, delete-orphan")
    graph_embeddings = relationship("GraphEmbedding", back_populates="user", cascade="all, delete-orphan")
    entity_similarities = relationship("EntitySimilarity", back_populates="user", cascade="all, delete-orphan")
    strategies = relationship("Strategy", back_populates="user", cascade="all, delete-orphan")
    api_usage_records = relationship("ApiUsage", back_populates="user", cascade="all, delete-orphan")
    system_logs = relationship("SystemLog", back_populates="user", cascade="all, delete-orphan")


class JQDataConfig(BaseModel):
    """JQData配置模型"""
    
    __tablename__ = "jqdata_configs"
    
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    username: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="JQData用户名"
    )
    
    encrypted_password: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        comment="加密后的JQData密码"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否启用"
    )
    
    # 配额信息
    quota_total: Mapped[int] = mapped_column(
        Integer,
        default=10000,
        nullable=False,
        comment="总配额"
    )
    
    quota_used: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="已使用配额"
    )
    
    quota_reset_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="配额重置日期"
    )
    
    # 使用统计
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后使用时间"
    )
    
    total_api_calls: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="总API调用次数"
    )
    
    # 连接状态
    last_auth_success: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后认证成功时间"
    )
    
    last_auth_error: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="最后认证错误信息"
    )
    
    auth_failure_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="认证失败次数"
    )
    
    # 关系
    user = relationship("User", back_populates="jqdata_configs")


class UserSession(BaseModel):
    """用户会话模型"""
    
    __tablename__ = "user_sessions"
    
    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="用户ID"
    )
    
    session_token: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        comment="会话令牌"
    )
    
    refresh_token: Mapped[str] = mapped_column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        comment="刷新令牌"
    )
    
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="过期时间"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(45),
        nullable=True,
        comment="IP地址"
    )
    
    user_agent: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="用户代理"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否活跃"
    )
