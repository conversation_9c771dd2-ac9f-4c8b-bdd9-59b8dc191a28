'use client';

/**
 * AI功能主页面
 * 
 * 集成所有AI功能，包括价格预测、智能选股、情绪分析等
 */

import React, { useState } from 'react';
import { Card, Tabs, Typography, Space, Alert, Row, Col } from 'antd';
import {
  RobotOutlined,
  BulbOutlined,
  HeartOutlined,
  LineChartOutlined,
  TrophyOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { PricePrediction } from '@/components/ai/PricePrediction';
import { IntelligentSelection } from '@/components/ai/IntelligentSelection';
import { SentimentAnalysis } from '@/components/ai/SentimentAnalysis';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

function AIContent() {
  const [activeTab, setActiveTab] = useState('prediction');
  const [selectedSymbol, setSelectedSymbol] = useState('000001.XSHE');

  const handleStockSelect = (symbol: string) => {
    setSelectedSymbol(symbol);
    // 如果在智能选股页面选择了股票，切换到价格预测页面
    if (activeTab === 'selection') {
      setActiveTab('prediction');
    }
  };

  const aiFeatures = [
    {
      icon: <LineChartOutlined className="text-2xl text-blue-500" />,
      title: '价格预测',
      description: '基于机器学习模型预测股票未来价格走势',
      features: ['多种ML模型', '置信度评估', '可视化预测']
    },
    {
      icon: <TrophyOutlined className="text-2xl text-green-500" />,
      title: '智能选股',
      description: '多维度量化分析，智能筛选优质股票',
      features: ['多因子评分', '权重可调', '实时排名']
    },
    {
      icon: <HeartOutlined className="text-2xl text-red-500" />,
      title: '情绪分析',
      description: '分析新闻和社交媒体情绪对股价的影响',
      features: ['NLP技术', '情绪指数', '趋势分析']
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              <Space>
                <RobotOutlined />
                AI智能分析
              </Space>
            </Title>
            <Text type="secondary" className="text-lg">
              运用人工智能技术，为您提供专业的投资分析和决策支持
            </Text>
          </div>
        </div>
      </motion.div>

      {/* 功能介绍 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Alert
          message="AI功能说明"
          description="我们的AI系统集成了机器学习、自然语言处理等先进技术，通过分析历史数据、技术指标、新闻情绪等多维度信息，为您提供智能化的投资分析。所有AI分析结果仅供参考，投资决策请结合个人判断。"
          type="info"
          showIcon
          className="mb-6"
        />

        <Row gutter={[16, 16]} className="mb-6">
          {aiFeatures.map((feature, index) => (
            <Col xs={24} md={8} key={index}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
              >
                <Card
                  hoverable
                  className="h-full"
                  bodyStyle={{ padding: '20px' }}
                >
                  <div className="text-center space-y-3">
                    <div>{feature.icon}</div>
                    <Title level={4} className="!mb-2">
                      {feature.title}
                    </Title>
                    <Paragraph type="secondary" className="!mb-3">
                      {feature.description}
                    </Paragraph>
                    <div className="space-y-1">
                      {feature.features.map((item, i) => (
                        <div key={i} className="text-sm text-gray-600">
                          • {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </motion.div>
            </Col>
          ))}
        </Row>
      </motion.div>

      {/* AI功能标签页 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <Card>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            size="large"
            tabBarStyle={{ marginBottom: '24px' }}
          >
            <TabPane
              tab={
                <Space>
                  <LineChartOutlined />
                  <span>价格预测</span>
                </Space>
              }
              key="prediction"
            >
              <PricePrediction
                symbol={selectedSymbol}
                onSymbolChange={setSelectedSymbol}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BulbOutlined />
                  <span>智能选股</span>
                </Space>
              }
              key="selection"
            >
              <IntelligentSelection
                onStockSelect={handleStockSelect}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <HeartOutlined />
                  <span>情绪分析</span>
                </Space>
              }
              key="sentiment"
            >
              <SentimentAnalysis
                symbol={selectedSymbol}
                onSymbolChange={setSelectedSymbol}
              />
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BarChartOutlined />
                  <span>模型管理</span>
                </Space>
              }
              key="models"
            >
              <div className="text-center py-12">
                <RobotOutlined className="text-6xl text-gray-300 mb-4" />
                <Title level={3} type="secondary">
                  模型管理功能
                </Title>
                <Paragraph type="secondary">
                  模型训练、评估和部署功能正在开发中...
                </Paragraph>
              </div>
            </TabPane>
          </Tabs>
        </Card>
      </motion.div>

      {/* 使用提示 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card title="使用提示" size="small">
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <div className="space-y-2">
                <Title level={5}>
                  <LineChartOutlined className="mr-2" />
                  价格预测
                </Title>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• 选择合适的模型类型以获得更好的预测效果</li>
                  <li>• 预测天数建议不超过15天</li>
                  <li>• 关注置信度指标评估预测可靠性</li>
                </ul>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className="space-y-2">
                <Title level={5}>
                  <BulbOutlined className="mr-2" />
                  智能选股
                </Title>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• 根据投资偏好调整各项权重</li>
                  <li>• 结合市场环境选择合适的市场范围</li>
                  <li>• 定期更新选股结果以获得最新推荐</li>
                </ul>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className="space-y-2">
                <Title level={5}>
                  <HeartOutlined className="mr-2" />
                  情绪分析
                </Title>
                <ul className="text-sm text-gray-600 space-y-1 ml-4">
                  <li>• 情绪分析结果可作为辅助参考</li>
                  <li>• 建议结合基本面和技术面分析</li>
                  <li>• 关注情绪变化趋势而非绝对值</li>
                </ul>
              </div>
            </Col>
          </Row>
        </Card>
      </motion.div>
    </div>
  );
}

export default function AIPage() {
  return (
    <ClientAuthWrapper>
      <AIContent />
    </ClientAuthWrapper>
  );
}
