"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/jqdata/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/settings/jqdata/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JQDataConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/radio/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/descriptions/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MobileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MailOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * JQData配置页面\n * \n * 用户配置JQData账号、查看配额使用情况、测试连接等\n */ \n\n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nfunction JQDataConfigPage() {\n    var _testResult_quotaInfo_quotaUsageRate;\n    _s();\n    const { user, isLoading: authLoading } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testLoading, setTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTestModal, setShowTestModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    // 验证用户名格式\n    const validateUsername = (rule, value)=>{\n        if (!value) {\n            return Promise.reject(new Error(\"请输入用户名\"));\n        }\n        if (loginType === \"email\") {\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的邮箱地址\"));\n            }\n        } else if (loginType === \"mobile\") {\n            const mobileRegex = /^1[3-9]\\d{9}$/;\n            if (!mobileRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的手机号码\"));\n            }\n        }\n        return Promise.resolve();\n    };\n    // 加载JQData配置\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/jqdata/config\");\n            if (response.code === 200 && response.data) {\n                setConfig(response.data);\n                const configLoginType = response.data.loginType || \"mobile\";\n                setLoginType(configLoginType);\n                form.setFieldsValue({\n                    username: response.data.username,\n                    loginType: configLoginType\n                });\n                setCurrentStep(2); // 已配置\n            } else {\n                setCurrentStep(0); // 未配置\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                setCurrentStep(0); // 未配置\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"请先登录系统\");\n                setCurrentStep(0); // 未配置\n            } else {\n                console.error(\"加载配置失败:\", error);\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"加载配置失败，请稍后重试\");\n                setCurrentStep(0); // 未配置\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 保存配置\n    const handleSave = async (values)=>{\n        try {\n            setLoading(true);\n            setCurrentStep(1); // 配置中\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/config\", {\n                username: values.username,\n                password: values.password,\n                loginType: values.loginType || loginType\n            });\n            if (response.code === 200) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"JQData配置保存成功！\");\n                setConfig(response.data);\n                setCurrentStep(2); // 配置完成\n                form.resetFields([\n                    \"password\"\n                ]);\n            } else {\n                throw new Error(response.message);\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(error.message || \"配置保存失败\");\n            setCurrentStep(0); // 回到未配置状态\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试连接\n    const handleTestConnection = async ()=>{\n        try {\n            setTestLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/test-connection\");\n            setTestResult(response.data);\n            setShowTestModal(true);\n            if (response.data.success) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"连接测试成功！\");\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"连接测试失败\");\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"测试连接失败\");\n            setTestResult({\n                success: false,\n                message: \"测试连接失败\",\n                errorDetails: error.message\n            });\n            setShowTestModal(true);\n        } finally{\n            setTestLoading(false);\n        }\n    };\n    // 删除配置\n    const handleDelete = ()=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"].confirm({\n            title: \"确认删除配置\",\n            content: \"删除后将无法获取JQData数据，确定要删除吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            onOk: async ()=>{\n                try {\n                    await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/api/v1/jqdata/config\");\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"配置删除成功\");\n                    setConfig(null);\n                    setCurrentStep(0);\n                    form.resetFields();\n                } catch (error) {\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"删除配置失败\");\n                }\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // 配置步骤\n    const steps = [\n        {\n            title: \"配置账号\",\n            description: \"输入JQData账号信息\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"验证连接\",\n            description: \"验证账号有效性\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"配置完成\",\n            description: \"开始使用JQData服务\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 认证检查\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                size: \"large\",\n                tip: \"正在验证用户身份...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                            level: 2,\n                            className: \"!mb-2\",\n                            children: \"JQData配置\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            type: \"secondary\",\n                            children: \"配置您的JQData账号以获取实时市场数据\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    message: \"需要登录\",\n                    description: \"请先登录系统才能配置JQData账号\",\n                    type: \"warning\",\n                    showIcon: true,\n                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        type: \"primary\",\n                        onClick: ()=>router.push(\"/auth/login\"),\n                        children: \"前往登录\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 256,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading && !config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                size: \"large\",\n                tip: \"正在加载配置...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 279,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: \"JQData配置\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        children: \"配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    current: currentStep,\n                    items: steps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData已配置\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"账号: \",\n                                config.username\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"登录方式: \",\n                                config.loginType === \"mobile\" ? \"手机号\" : \"邮箱\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"状态: \",\n                                config.isActive ? \"正常\" : \"异常\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 13\n                }, void 0),\n                type: config.isActive ? \"success\" : \"warning\",\n                showIcon: true,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            onClick: handleTestConnection,\n                            loading: testLoading,\n                            children: \"测试连接\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: \"small\",\n                            onClick: loadConfig,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\",\n                type: \"info\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            title: \"账号配置\",\n                            extra: config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                onClick: handleDelete,\n                                children: \"删除配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"JQData登录说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"手机号登录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 24\n                                                    }, void 0),\n                                                    \"：JQData官方推荐的主要登录方式\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"• \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"邮箱登录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 24\n                                                    }, void 0),\n                                                    \"：适用于早期注册的用户\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"• 请使用您在JQData官网注册时的账号和密码\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    form: form,\n                                    layout: \"vertical\",\n                                    onFinish: handleSave,\n                                    disabled: loading,\n                                    initialValues: {\n                                        loginType: \"mobile\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                            name: \"loginType\",\n                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"登录方式\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        title: \"JQData主要支持手机号登录，部分老用户支持邮箱登录\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"ml-1 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Group, {\n                                                value: loginType,\n                                                onChange: (e)=>{\n                                                    setLoginType(e.target.value);\n                                                    form.setFieldsValue({\n                                                        username: \"\"\n                                                    }); // 清空用户名\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Button, {\n                                                        value: \"mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 手机号登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"].Button, {\n                                                        value: \"email\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 邮箱登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                            name: \"username\",\n                                            label: loginType === \"email\" ? \"JQData邮箱\" : \"JQData手机号\",\n                                            rules: [\n                                                {\n                                                    validator: validateUsername\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                placeholder: loginType === \"email\" ? \"请输入JQData注册邮箱\" : \"请输入JQData注册手机号\",\n                                                prefix: loginType === \"email\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 51\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 70\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                            name: \"password\",\n                                            label: \"JQData密码\",\n                                            rules: [\n                                                {\n                                                    required: !config,\n                                                    message: \"请输入JQData密码\"\n                                                },\n                                                {\n                                                    min: 6,\n                                                    message: \"密码长度至少6位\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"].Password, {\n                                                placeholder: config ? \"留空表示不修改密码\" : \"请输入JQData密码\",\n                                                iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 31\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 48\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        type: \"primary\",\n                                                        htmlType: \"submit\",\n                                                        loading: loading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        children: config ? \"更新配置\" : \"保存配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        onClick: handleTestConnection,\n                                                        loading: testLoading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: \"测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"配置说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 支持邮箱或手机号登录，请选择您在JQData注册时使用的方式\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 邮箱格式：<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 手机号格式：13812345678（中国大陆手机号）\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 密码将被加密存储，确保账号安全\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 配置后可获取实时股票数据和历史数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 如遇问题请检查账号状态或联系客服\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mt-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            title: \"配额使用情况\",\n                            children: config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        gutter: 16,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    title: \"总配额\",\n                                                    value: config.quotaTotal,\n                                                    suffix: \"次\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    title: \"已使用\",\n                                                    value: config.quotaUsed,\n                                                    suffix: \"次\",\n                                                    valueStyle: {\n                                                        color: config.quotaUsed / config.quotaTotal > 0.8 ? \"#ff4d4f\" : \"#1890ff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        children: \"使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        strong: true,\n                                                        children: [\n                                                            (config.quotaUsed / config.quotaTotal * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                percent: config.quotaUsed / config.quotaTotal * 100,\n                                                status: config.quotaUsed / config.quotaTotal > 0.9 ? \"exception\" : \"active\",\n                                                strokeColor: {\n                                                    \"0%\": \"#108ee9\",\n                                                    \"100%\": \"#87d068\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        size: \"small\",\n                                        column: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                                label: \"剩余配额\",\n                                                children: [\n                                                    config.quotaRemaining,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                                label: \"总调用次数\",\n                                                children: [\n                                                    config.totalApiCalls,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                                label: \"最后使用\",\n                                                children: config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : \"未使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                                label: \"配额重置\",\n                                                children: config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : \"未知\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this),\n                                    config.authFailureCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        message: \"认证失败 \".concat(config.authFailureCount, \" 次\"),\n                                        description: config.lastAuthError,\n                                        type: \"warning\",\n                                        showIcon: true,\n                                        className: \"mt-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"配置JQData账号后查看配额信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 538,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                title: \"连接测试结果\",\n                open: showTestModal,\n                onCancel: ()=>setShowTestModal(false),\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        onClick: ()=>setShowTestModal(false),\n                        children: \"关闭\"\n                    }, \"close\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: testResult.success ? \"连接成功\" : \"连接失败\",\n                            description: testResult.message,\n                            type: testResult.success ? \"success\" : \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 13\n                        }, this),\n                        testResult.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"响应时间: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: [\n                                        testResult.responseTime.toFixed(3),\n                                        \"秒\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 15\n                        }, this),\n                        testResult.quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            title: \"配额信息\",\n                            size: \"small\",\n                            column: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                    label: \"总配额\",\n                                    children: testResult.quotaInfo.quotaTotal\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                    label: \"已使用\",\n                                    children: testResult.quotaInfo.quotaUsed\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                    label: \"剩余\",\n                                    children: testResult.quotaInfo.quotaRemaining\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"].Item, {\n                                    label: \"使用率\",\n                                    children: [\n                                        (_testResult_quotaInfo_quotaUsageRate = testResult.quotaInfo.quotaUsageRate) === null || _testResult_quotaInfo_quotaUsageRate === void 0 ? void 0 : _testResult_quotaInfo_quotaUsageRate.toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 15\n                        }, this),\n                        testResult.errorDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: \"错误详情\",\n                            description: testResult.errorDetails,\n                            type: \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s(JQDataConfigPage, \"lHNRpe4wEX+mwSarH648odxDUZY=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = JQDataConfigPage;\nvar _c;\n$RefreshReg$(_c, \"JQDataConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3NldHRpbmdzL2pxZGF0YS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVBOzs7O0NBSUMsR0FFa0Q7QUFxQnJDO0FBY2E7QUFHWTtBQUNBO0FBQ0s7QUFFNUMsTUFBTSxFQUFFZ0MsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLFNBQVMsRUFBRSxHQUFHM0IsZ01BQVVBO0FBQzdDLE1BQU0sRUFBRTRCLElBQUksRUFBRSxHQUFHakIsZ01BQUtBO0FBMEJQLFNBQVNrQjtRQTZmTEM7O0lBNWZqQixNQUFNLEVBQUVDLElBQUksRUFBRUMsV0FBV0MsV0FBVyxFQUFFLEdBQUdYLG9EQUFPQTtJQUNoRCxNQUFNWSxTQUFTViwwREFBU0E7SUFDeEIsTUFBTSxDQUFDVyxLQUFLLEdBQUd0QyxnTUFBSUEsQ0FBQ3VDLE9BQU87SUFDM0IsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUc1QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM2QyxhQUFhQyxlQUFlLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUMrQyxRQUFRQyxVQUFVLEdBQUdoRCwrQ0FBUUEsQ0FBc0I7SUFDMUQsTUFBTSxDQUFDb0MsWUFBWWEsY0FBYyxHQUFHakQsK0NBQVFBLENBQW9CO0lBQ2hFLE1BQU0sQ0FBQ2tELGVBQWVDLGlCQUFpQixHQUFHbkQsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDb0QsYUFBYUMsZUFBZSxHQUFHckQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0QsV0FBV0MsYUFBYSxHQUFHdkQsK0NBQVFBLENBQXFCO0lBRS9ELFVBQVU7SUFDVixNQUFNd0QsbUJBQW1CLENBQUNDLE1BQVdDO1FBQ25DLElBQUksQ0FBQ0EsT0FBTztZQUNWLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQyxJQUFJQyxNQUFNO1FBQ2xDO1FBRUEsSUFBSVAsY0FBYyxTQUFTO1lBQ3pCLE1BQU1RLGFBQWE7WUFDbkIsSUFBSSxDQUFDQSxXQUFXQyxJQUFJLENBQUNMLFFBQVE7Z0JBQzNCLE9BQU9DLFFBQVFDLE1BQU0sQ0FBQyxJQUFJQyxNQUFNO1lBQ2xDO1FBQ0YsT0FBTyxJQUFJUCxjQUFjLFVBQVU7WUFDakMsTUFBTVUsY0FBYztZQUNwQixJQUFJLENBQUNBLFlBQVlELElBQUksQ0FBQ0wsUUFBUTtnQkFDNUIsT0FBT0MsUUFBUUMsTUFBTSxDQUFDLElBQUlDLE1BQU07WUFDbEM7UUFDRjtRQUVBLE9BQU9GLFFBQVFNLE9BQU87SUFDeEI7SUFFQSxhQUFhO0lBQ2IsTUFBTUMsYUFBYTtRQUNqQixJQUFJO1lBQ0Z0QixXQUFXO1lBQ1gsTUFBTXVCLFdBQVcsTUFBTXRDLHFEQUFTQSxDQUFDdUMsR0FBRyxDQUFDO1lBRXJDLElBQUlELFNBQVNFLElBQUksS0FBSyxPQUFPRixTQUFTRyxJQUFJLEVBQUU7Z0JBQzFDdEIsVUFBVW1CLFNBQVNHLElBQUk7Z0JBQ3ZCLE1BQU1DLGtCQUFrQkosU0FBU0csSUFBSSxDQUFDaEIsU0FBUyxJQUFJO2dCQUNuREMsYUFBYWdCO2dCQUNiOUIsS0FBSytCLGNBQWMsQ0FBQztvQkFDbEJDLFVBQVVOLFNBQVNHLElBQUksQ0FBQ0csUUFBUTtvQkFDaENuQixXQUFXaUI7Z0JBQ2I7Z0JBQ0FsQixlQUFlLElBQUksTUFBTTtZQUMzQixPQUFPO2dCQUNMQSxlQUFlLElBQUksTUFBTTtZQUMzQjtRQUNGLEVBQUUsT0FBT3FCLE9BQVk7Z0JBQ2ZBLGlCQUVPQTtZQUZYLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHNDQUFBQSxnQkFBZ0JDLE1BQU0sTUFBSyxLQUFLO2dCQUNsQ3RCLGVBQWUsSUFBSSxNQUFNO1lBQzNCLE9BQU8sSUFBSXFCLEVBQUFBLG1CQUFBQSxNQUFNUCxRQUFRLGNBQWRPLHVDQUFBQSxpQkFBZ0JDLE1BQU0sTUFBSyxLQUFLO2dCQUN6QzlELGdNQUFPQSxDQUFDNkQsS0FBSyxDQUFDO2dCQUNkckIsZUFBZSxJQUFJLE1BQU07WUFDM0IsT0FBTztnQkFDTHVCLFFBQVFGLEtBQUssQ0FBQyxXQUFXQTtnQkFDekI3RCxnTUFBT0EsQ0FBQzZELEtBQUssQ0FBQztnQkFDZHJCLGVBQWUsSUFBSSxNQUFNO1lBQzNCO1FBQ0YsU0FBVTtZQUNSVCxXQUFXO1FBQ2I7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNaUMsYUFBYSxPQUFPQztRQUN4QixJQUFJO1lBQ0ZsQyxXQUFXO1lBQ1hTLGVBQWUsSUFBSSxNQUFNO1lBRXpCLE1BQU1jLFdBQVcsTUFBTXRDLHFEQUFTQSxDQUFDa0QsSUFBSSxDQUFDLHlCQUF5QjtnQkFDN0ROLFVBQVVLLE9BQU9MLFFBQVE7Z0JBQ3pCTyxVQUFVRixPQUFPRSxRQUFRO2dCQUN6QjFCLFdBQVd3QixPQUFPeEIsU0FBUyxJQUFJQTtZQUNqQztZQUVBLElBQUlhLFNBQVNFLElBQUksS0FBSyxLQUFLO2dCQUN6QnhELGdNQUFPQSxDQUFDb0UsT0FBTyxDQUFDO2dCQUNoQmpDLFVBQVVtQixTQUFTRyxJQUFJO2dCQUN2QmpCLGVBQWUsSUFBSSxPQUFPO2dCQUMxQlosS0FBS3lDLFdBQVcsQ0FBQztvQkFBQztpQkFBVztZQUMvQixPQUFPO2dCQUNMLE1BQU0sSUFBSXJCLE1BQU1NLFNBQVN0RCxPQUFPO1lBQ2xDO1FBQ0YsRUFBRSxPQUFPNkQsT0FBWTtZQUNuQjdELGdNQUFPQSxDQUFDNkQsS0FBSyxDQUFDQSxNQUFNN0QsT0FBTyxJQUFJO1lBQy9Cd0MsZUFBZSxJQUFJLFVBQVU7UUFDL0IsU0FBVTtZQUNSVCxXQUFXO1FBQ2I7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNdUMsdUJBQXVCO1FBQzNCLElBQUk7WUFDRnJDLGVBQWU7WUFDZixNQUFNcUIsV0FBVyxNQUFNdEMscURBQVNBLENBQUNrRCxJQUFJLENBQUM7WUFFdEM5QixjQUFja0IsU0FBU0csSUFBSTtZQUMzQm5CLGlCQUFpQjtZQUVqQixJQUFJZ0IsU0FBU0csSUFBSSxDQUFDVyxPQUFPLEVBQUU7Z0JBQ3pCcEUsZ01BQU9BLENBQUNvRSxPQUFPLENBQUM7WUFDbEIsT0FBTztnQkFDTHBFLGdNQUFPQSxDQUFDNkQsS0FBSyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQSxPQUFZO1lBQ25CN0QsZ01BQU9BLENBQUM2RCxLQUFLLENBQUM7WUFDZHpCLGNBQWM7Z0JBQ1pnQyxTQUFTO2dCQUNUcEUsU0FBUztnQkFDVHVFLGNBQWNWLE1BQU03RCxPQUFPO1lBQzdCO1lBQ0FzQyxpQkFBaUI7UUFDbkIsU0FBVTtZQUNSTCxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTXVDLGVBQWU7UUFDbkJ2RSxnTUFBS0EsQ0FBQ3dFLE9BQU8sQ0FBQztZQUNaQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLFlBQVk7WUFDWkMsTUFBTTtnQkFDSixJQUFJO29CQUNGLE1BQU0vRCxxREFBU0EsQ0FBQ2dFLE1BQU0sQ0FBQztvQkFDdkJoRixnTUFBT0EsQ0FBQ29FLE9BQU8sQ0FBQztvQkFDaEJqQyxVQUFVO29CQUNWSyxlQUFlO29CQUNmWixLQUFLeUMsV0FBVztnQkFDbEIsRUFBRSxPQUFPUixPQUFPO29CQUNkN0QsZ01BQU9BLENBQUM2RCxLQUFLLENBQUM7Z0JBQ2hCO1lBQ0Y7UUFDRjtJQUNGO0lBRUF6RSxnREFBU0EsQ0FBQztRQUNSaUU7SUFDRixHQUFHLEVBQUU7SUFFTCxPQUFPO0lBQ1AsTUFBTTRCLFFBQVE7UUFDWjtZQUNFUCxPQUFPO1lBQ1BRLGFBQWE7WUFDYkMsb0JBQU0sOERBQUM1RSxnTkFBV0E7Ozs7O1FBQ3BCO1FBQ0E7WUFDRW1FLE9BQU87WUFDUFEsYUFBYTtZQUNiQyxvQkFBTSw4REFBQ3hFLGdOQUFZQTs7Ozs7UUFDckI7UUFDQTtZQUNFK0QsT0FBTztZQUNQUSxhQUFhO1lBQ2JDLG9CQUFNLDhEQUFDM0UsZ05BQW1CQTs7Ozs7UUFDNUI7S0FDRDtJQUVELE9BQU87SUFDUCxJQUFJa0IsYUFBYTtRQUNmLHFCQUNFLDhEQUFDMEQ7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ2xGLGlNQUFJQTtnQkFBQ21GLE1BQUs7Z0JBQVFDLEtBQUk7Ozs7Ozs7Ozs7O0lBRzdCO0lBRUEsSUFBSSxDQUFDL0QsTUFBTTtRQUNULHFCQUNFLDhEQUFDNEQ7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEOztzQ0FDQyw4REFBQ2xFOzRCQUFNc0UsT0FBTzs0QkFBR0gsV0FBVTtzQ0FBUTs7Ozs7O3NDQUNuQyw4REFBQ2xFOzRCQUFLc0UsTUFBSztzQ0FBWTs7Ozs7Ozs7Ozs7OzhCQUd6Qiw4REFBQy9GLGlNQUFLQTtvQkFDSk0sU0FBUTtvQkFDUmtGLGFBQVk7b0JBQ1pPLE1BQUs7b0JBQ0xDLFFBQVE7b0JBQ1JDLHNCQUNFLDhEQUFDbkcsaU1BQU1BO3dCQUFDaUcsTUFBSzt3QkFBVUcsU0FBUyxJQUFNakUsT0FBT2tFLElBQUksQ0FBQztrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTzVFO0lBRUEsSUFBSS9ELFdBQVcsQ0FBQ0ksUUFBUTtRQUN0QixxQkFDRSw4REFBQ2tEO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNsRixpTUFBSUE7Z0JBQUNtRixNQUFLO2dCQUFRQyxLQUFJOzs7Ozs7Ozs7OztJQUc3QjtJQUVBLHFCQUNFLDhEQUFDSDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7O2tDQUNDLDhEQUFDbEU7d0JBQU1zRSxPQUFPO3dCQUFHSCxXQUFVO2tDQUFROzs7Ozs7a0NBR25DLDhEQUFDbEU7d0JBQUtzRSxNQUFLO2tDQUFZOzs7Ozs7Ozs7Ozs7MEJBTXpCLDhEQUFDcEcsaU1BQUlBOzBCQUNILDRFQUFDZSxnTUFBS0E7b0JBQUMwRixTQUFTdkQ7b0JBQWF3RCxPQUFPZDs7Ozs7Ozs7Ozs7WUFJckMvQyx1QkFDQyw4REFBQ3hDLGlNQUFLQTtnQkFDSk0sU0FBUTtnQkFDUmtGLDJCQUNFLDhEQUFDRTs7c0NBQ0MsOERBQUNBOztnQ0FBSTtnQ0FBS2xELE9BQU8wQixRQUFROzs7Ozs7O3NDQUN6Qiw4REFBQ3dCOztnQ0FBSTtnQ0FBT2xELE9BQU9PLFNBQVMsS0FBSyxXQUFXLFFBQVE7Ozs7Ozs7c0NBQ3BELDhEQUFDMkM7O2dDQUFJO2dDQUFLbEQsT0FBTzhELFFBQVEsR0FBRyxPQUFPOzs7Ozs7Ozs7Ozs7O2dCQUd2Q1AsTUFBTXZELE9BQU84RCxRQUFRLEdBQUcsWUFBWTtnQkFDcENOLFFBQVE7Z0JBQ1JDLHNCQUNFLDhEQUFDNUYsaU1BQUtBOztzQ0FDSiw4REFBQ1AsaU1BQU1BOzRCQUFDOEYsTUFBSzs0QkFBUU0sU0FBU3RCOzRCQUFzQnhDLFNBQVNFO3NDQUFhOzs7Ozs7c0NBRzFFLDhEQUFDeEMsaU1BQU1BOzRCQUFDOEYsTUFBSzs0QkFBUU0sU0FBU3ZDO3NDQUFZOzs7Ozs7Ozs7Ozs7Ozs7O3FDQU9oRCw4REFBQzNELGlNQUFLQTtnQkFDSk0sU0FBUTtnQkFDUmtGLGFBQVk7Z0JBQ1pPLE1BQUs7Z0JBQ0xDLFFBQVE7Ozs7OzswQkFJWiw4REFBQzdGLGlNQUFHQTtnQkFBQ29HLFFBQVE7b0JBQUM7b0JBQUk7aUJBQUc7O2tDQUVuQiw4REFBQ25HLGlNQUFHQTt3QkFBQ29HLElBQUk7d0JBQUlDLElBQUk7a0NBQ2YsNEVBQUM5RyxpTUFBSUE7NEJBQUNxRixPQUFNOzRCQUFPMEIsT0FDakJsRSx3QkFDRSw4REFBQzFDLGlNQUFNQTtnQ0FBQ2lHLE1BQUs7Z0NBQU9ZLE1BQU07Z0NBQUNULFNBQVNwQjswQ0FBYzs7Ozs7Ozs4Q0FLcEQsOERBQUM5RSxpTUFBS0E7b0NBQ0pNLFNBQVE7b0NBQ1JrRiwyQkFDRSw4REFBQ0U7OzBEQUNDLDhEQUFDa0I7O29EQUFFO2tFQUFFLDhEQUFDQztrRUFBTzs7Ozs7O29EQUFjOzs7Ozs7OzBEQUMzQiw4REFBQ0Q7O29EQUFFO2tFQUFFLDhEQUFDQztrRUFBTzs7Ozs7O29EQUFhOzs7Ozs7OzBEQUMxQiw4REFBQ0Q7MERBQUU7Ozs7Ozs7Ozs7OztvQ0FHUGIsTUFBSztvQ0FDTEMsUUFBUTtvQ0FDUkwsV0FBVTs7Ozs7OzhDQUdaLDhEQUFDL0YsZ01BQUlBO29DQUNIc0MsTUFBTUE7b0NBQ040RSxRQUFPO29DQUNQQyxVQUFVekM7b0NBQ1YwQyxVQUFVNUU7b0NBQ1Y2RSxlQUFlO3dDQUFFbEUsV0FBVztvQ0FBUzs7c0RBRXJDLDhEQUFDbkQsZ01BQUlBLENBQUNzSCxJQUFJOzRDQUNSQyxNQUFLOzRDQUNMQyxxQkFDRSw4REFBQ0M7O29EQUFLO2tFQUVKLDhEQUFDekcsaU1BQU9BO3dEQUFDb0UsT0FBTTtrRUFDYiw0RUFBQzVELGdOQUFzQkE7NERBQUN1RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzREFLeEMsNEVBQUNoRix1TUFBVztnREFDVndDLE9BQU9KO2dEQUNQd0UsVUFBVSxDQUFDQztvREFDVHhFLGFBQWF3RSxFQUFFQyxNQUFNLENBQUN0RSxLQUFLO29EQUMzQmpCLEtBQUsrQixjQUFjLENBQUM7d0RBQUVDLFVBQVU7b0RBQUcsSUFBSSxRQUFRO2dEQUNqRDs7a0VBRUEsOERBQUN2RCx3TUFBWTt3REFBQ3dDLE9BQU07OzBFQUNsQiw4REFBQ2hDLGdOQUFjQTs7Ozs7NERBQUc7Ozs7Ozs7a0VBRXBCLDhEQUFDUix3TUFBWTt3REFBQ3dDLE9BQU07OzBFQUNsQiw4REFBQ2pDLGdOQUFZQTs7Ozs7NERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLdEIsOERBQUN0QixnTUFBSUEsQ0FBQ3NILElBQUk7NENBQ1JDLE1BQUs7NENBQ0xDLE9BQU9yRSxjQUFjLFVBQVUsYUFBYTs0Q0FDNUMyRSxPQUFPO2dEQUNMO29EQUFFQyxXQUFXMUU7Z0RBQWlCOzZDQUMvQjtzREFFRCw0RUFBQ3BELGlNQUFLQTtnREFDSitILGFBQ0U3RSxjQUFjLFVBQ1Ysa0JBQ0E7Z0RBRU44RSxRQUFROUUsY0FBYyx3QkFBVSw4REFBQzdCLGdOQUFZQTs7OzsyRUFBTSw4REFBQ0MsZ05BQWNBOzs7Ozs7Ozs7Ozs7Ozs7c0RBSXRFLDhEQUFDdkIsZ01BQUlBLENBQUNzSCxJQUFJOzRDQUNSQyxNQUFLOzRDQUNMQyxPQUFNOzRDQUNOTSxPQUFPO2dEQUNMO29EQUFFSSxVQUFVLENBQUN0RjtvREFBUWxDLFNBQVM7Z0RBQWM7Z0RBQzVDO29EQUFFeUgsS0FBSztvREFBR3pILFNBQVM7Z0RBQVc7NkNBQy9CO3NEQUVELDRFQUFDVCxpTUFBS0EsQ0FBQ21JLFFBQVE7Z0RBQ2JKLGFBQWFwRixTQUFTLGNBQWM7Z0RBQ3BDeUYsWUFBWSxDQUFDQyxVQUNYQSx3QkFBVSw4REFBQ2xILGdOQUFVQTs7OzsrRUFBTSw4REFBQ0QsZ05BQW9CQTs7Ozs7Ozs7Ozs7Ozs7O3NEQUt0RCw4REFBQ25CLGdNQUFJQSxDQUFDc0gsSUFBSTtzREFDUiw0RUFBQzdHLGlNQUFLQTs7a0VBQ0osOERBQUNQLGlNQUFNQTt3REFDTGlHLE1BQUs7d0RBQ0xvQyxVQUFTO3dEQUNUL0YsU0FBU0E7d0RBQ1RxRCxvQkFBTSw4REFBQzNFLGdOQUFtQkE7Ozs7O2tFQUV6QjBCLFNBQVMsU0FBUzs7Ozs7O29EQUdwQkEsd0JBQ0MsOERBQUMxQyxpTUFBTUE7d0RBQ0xvRyxTQUFTdEI7d0RBQ1R4QyxTQUFTRTt3REFDVG1ELG9CQUFNLDhEQUFDeEUsZ05BQVlBOzs7OztrRUFDcEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVNULDhEQUFDakIsaU1BQUtBO29DQUNKTSxTQUFRO29DQUNSa0YsMkJBQ0UsOERBQUNFO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7MERBQUk7Ozs7OzswREFDTCw4REFBQ0E7MERBQUk7Ozs7OzswREFDTCw4REFBQ0E7MERBQUk7Ozs7OzswREFDTCw4REFBQ0E7MERBQUk7Ozs7OzswREFDTCw4REFBQ0E7MERBQUk7Ozs7OzswREFDTCw4REFBQ0E7MERBQUk7Ozs7Ozs7Ozs7OztvQ0FHVEssTUFBSztvQ0FDTEMsUUFBUTtvQ0FDUkwsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTWhCLDhEQUFDdkYsaU1BQUdBO3dCQUFDb0csSUFBSTt3QkFBSUMsSUFBSTtrQ0FDZiw0RUFBQzlHLGlNQUFJQTs0QkFBQ3FGLE9BQU07c0NBQ1R4Qyx1QkFDQyw4REFBQ2tEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3hGLGlNQUFHQTt3Q0FBQ29HLFFBQVE7OzBEQUNYLDhEQUFDbkcsaU1BQUdBO2dEQUFDaUgsTUFBTTswREFDVCw0RUFBQ25ILGlNQUFTQTtvREFDUjhFLE9BQU07b0RBQ043QixPQUFPWCxPQUFPNEYsVUFBVTtvREFDeEJDLFFBQU87Ozs7Ozs7Ozs7OzBEQUdYLDhEQUFDakksaU1BQUdBO2dEQUFDaUgsTUFBTTswREFDVCw0RUFBQ25ILGlNQUFTQTtvREFDUjhFLE9BQU07b0RBQ043QixPQUFPWCxPQUFPOEYsU0FBUztvREFDdkJELFFBQU87b0RBQ1BFLFlBQVk7d0RBQ1ZDLE9BQU9oRyxPQUFPOEYsU0FBUyxHQUFHOUYsT0FBTzRGLFVBQVUsR0FBRyxNQUFNLFlBQVk7b0RBQ2xFOzs7Ozs7Ozs7Ozs7Ozs7OztrREFLTiw4REFBQzFDOzswREFDQyw4REFBQ0E7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbEU7a0VBQUs7Ozs7OztrRUFDTiw4REFBQ0E7d0RBQUtvRixNQUFNOzs0REFDUixRQUFReUIsU0FBUyxHQUFHOUYsT0FBTzRGLFVBQVUsR0FBSSxHQUFFLEVBQUdLLE9BQU8sQ0FBQzs0REFBRzs7Ozs7Ozs7Ozs7OzswREFHL0QsOERBQUN4SSxpTUFBUUE7Z0RBQ1B5SSxTQUFTLE9BQVFKLFNBQVMsR0FBRzlGLE9BQU80RixVQUFVLEdBQUk7Z0RBQ2xEaEUsUUFBUTVCLE9BQU84RixTQUFTLEdBQUc5RixPQUFPNEYsVUFBVSxHQUFHLE1BQU0sY0FBYztnREFDbkVPLGFBQWE7b0RBQ1gsTUFBTTtvREFDTixRQUFRO2dEQUNWOzs7Ozs7Ozs7Ozs7a0RBSUosOERBQUNuSSxpTUFBWUE7d0NBQUNvRixNQUFLO3dDQUFRZ0QsUUFBUTs7MERBQ2pDLDhEQUFDcEksaU1BQVlBLENBQUMwRyxJQUFJO2dEQUFDRSxPQUFNOztvREFDdEI1RSxPQUFPcUcsY0FBYztvREFBQzs7Ozs7OzswREFFekIsOERBQUNySSxpTUFBWUEsQ0FBQzBHLElBQUk7Z0RBQUNFLE9BQU07O29EQUN0QjVFLE9BQU9zRyxhQUFhO29EQUFDOzs7Ozs7OzBEQUV4Qiw4REFBQ3RJLGlNQUFZQSxDQUFDMEcsSUFBSTtnREFBQ0UsT0FBTTswREFDdEI1RSxPQUFPdUcsVUFBVSxHQUFHLElBQUlDLEtBQUt4RyxPQUFPdUcsVUFBVSxFQUFFRSxjQUFjLEtBQUs7Ozs7OzswREFFdEUsOERBQUN6SSxpTUFBWUEsQ0FBQzBHLElBQUk7Z0RBQUNFLE9BQU07MERBQ3RCNUUsT0FBTzBHLGNBQWMsR0FBRyxJQUFJRixLQUFLeEcsT0FBTzBHLGNBQWMsRUFBRUMsa0JBQWtCLEtBQUs7Ozs7Ozs7Ozs7OztvQ0FJbkYzRyxPQUFPNEcsZ0JBQWdCLEdBQUcsbUJBQ3pCLDhEQUFDcEosaU1BQUtBO3dDQUNKTSxTQUFTLFFBQWdDLE9BQXhCa0MsT0FBTzRHLGdCQUFnQixFQUFDO3dDQUN6QzVELGFBQWFoRCxPQUFPNkcsYUFBYTt3Q0FDakN0RCxNQUFLO3dDQUNMQyxRQUFRO3dDQUNSTCxXQUFVOzs7Ozs7Ozs7OztxREFLaEIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDbEU7b0NBQUtzRSxNQUFLOzhDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVWpDLDhEQUFDeEYsZ01BQUtBO2dCQUNKeUUsT0FBTTtnQkFDTnNFLE1BQU0zRztnQkFDTjRHLFVBQVUsSUFBTTNHLGlCQUFpQjtnQkFDakM0RyxRQUFRO2tDQUNOLDhEQUFDMUosaU1BQU1BO3dCQUFhb0csU0FBUyxJQUFNdEQsaUJBQWlCO2tDQUFRO3VCQUFoRDs7Ozs7aUJBR2I7MEJBRUFmLDRCQUNDLDhEQUFDNkQ7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDM0YsaU1BQUtBOzRCQUNKTSxTQUFTdUIsV0FBVzZDLE9BQU8sR0FBRyxTQUFTOzRCQUN2Q2MsYUFBYTNELFdBQVd2QixPQUFPOzRCQUMvQnlGLE1BQU1sRSxXQUFXNkMsT0FBTyxHQUFHLFlBQVk7NEJBQ3ZDc0IsUUFBUTs7Ozs7O3dCQUdUbkUsV0FBVzRILFlBQVksa0JBQ3RCLDhEQUFDL0Q7OzhDQUNDLDhEQUFDakU7b0NBQUtzRSxNQUFLOzhDQUFZOzs7Ozs7OENBQ3ZCLDhEQUFDdEU7b0NBQUtvRixNQUFNOzt3Q0FBRWhGLFdBQVc0SCxZQUFZLENBQUNoQixPQUFPLENBQUM7d0NBQUc7Ozs7Ozs7Ozs7Ozs7d0JBSXBENUcsV0FBVzZILFNBQVMsa0JBQ25CLDhEQUFDbEosaU1BQVlBOzRCQUFDd0UsT0FBTTs0QkFBT1ksTUFBSzs0QkFBUWdELFFBQVE7OzhDQUM5Qyw4REFBQ3BJLGlNQUFZQSxDQUFDMEcsSUFBSTtvQ0FBQ0UsT0FBTTs4Q0FDdEJ2RixXQUFXNkgsU0FBUyxDQUFDdEIsVUFBVTs7Ozs7OzhDQUVsQyw4REFBQzVILGlNQUFZQSxDQUFDMEcsSUFBSTtvQ0FBQ0UsT0FBTTs4Q0FDdEJ2RixXQUFXNkgsU0FBUyxDQUFDcEIsU0FBUzs7Ozs7OzhDQUVqQyw4REFBQzlILGlNQUFZQSxDQUFDMEcsSUFBSTtvQ0FBQ0UsT0FBTTs4Q0FDdEJ2RixXQUFXNkgsU0FBUyxDQUFDYixjQUFjOzs7Ozs7OENBRXRDLDhEQUFDckksaU1BQVlBLENBQUMwRyxJQUFJO29DQUFDRSxPQUFNOzt5Q0FDdEJ2Rix1Q0FBQUEsV0FBVzZILFNBQVMsQ0FBQ0MsY0FBYyxjQUFuQzlILDJEQUFBQSxxQ0FBcUM0RyxPQUFPLENBQUM7d0NBQUc7Ozs7Ozs7Ozs7Ozs7d0JBS3RENUcsV0FBV2dELFlBQVksa0JBQ3RCLDhEQUFDN0UsaU1BQUtBOzRCQUNKTSxTQUFROzRCQUNSa0YsYUFBYTNELFdBQVdnRCxZQUFZOzRCQUNwQ2tCLE1BQUs7NEJBQ0xDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXhCO0dBL2dCd0JwRTs7UUFDbUJQLGdEQUFPQTtRQUNqQ0Usc0RBQVNBO1FBQ1QzQixnTUFBSUEsQ0FBQ3VDOzs7S0FIRVAiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvc2V0dGluZ3MvanFkYXRhL3BhZ2UudHN4PzY5YzMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG4vKipcbiAqIEpRRGF0YemFjee9rumhtemdolxuICogXG4gKiDnlKjmiLfphY3nva5KUURhdGHotKblj7fjgIHmn6XnnIvphY3pop3kvb/nlKjmg4XlhrXjgIHmtYvor5Xov57mjqXnrYlcbiAqL1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIENhcmQsXG4gIEZvcm0sXG4gIElucHV0LFxuICBCdXR0b24sXG4gIFR5cG9ncmFwaHksXG4gIEFsZXJ0LFxuICBQcm9ncmVzcyxcbiAgU3RhdGlzdGljLFxuICBSb3csXG4gIENvbCxcbiAgU3BhY2UsXG4gIG1lc3NhZ2UsXG4gIE1vZGFsLFxuICBEZXNjcmlwdGlvbnMsXG4gIFRhZyxcbiAgU3BpbixcbiAgU3RlcHMsXG4gIFJhZGlvLFxuICBUb29sdGlwLFxufSBmcm9tICdhbnRkJztcbmltcG9ydCB7XG4gIEFwaU91dGxpbmVkLFxuICBDaGVja0NpcmNsZU91dGxpbmVkLFxuICBFeGNsYW1hdGlvbkNpcmNsZU91dGxpbmVkLFxuICBSZWxvYWRPdXRsaW5lZCxcbiAgRXllSW52aXNpYmxlT3V0bGluZWQsXG4gIEV5ZVR3b1RvbmUsXG4gIEluZm9DaXJjbGVPdXRsaW5lZCxcbiAgTGlua091dGxpbmVkLFxuICBXYXJuaW5nT3V0bGluZWQsXG4gIE1haWxPdXRsaW5lZCxcbiAgTW9iaWxlT3V0bGluZWQsXG4gIFF1ZXN0aW9uQ2lyY2xlT3V0bGluZWQsXG59IGZyb20gJ0BhbnQtZGVzaWduL2ljb25zJztcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9zdG9yZS9hdXRoJztcbmltcG9ydCBhcGlDbGllbnQgZnJvbSAnQC9zZXJ2aWNlcy9hcGknO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcblxuY29uc3QgeyBUaXRsZSwgVGV4dCwgUGFyYWdyYXBoIH0gPSBUeXBvZ3JhcGh5O1xuY29uc3QgeyBTdGVwIH0gPSBTdGVwcztcblxuaW50ZXJmYWNlIEpRRGF0YUNvbmZpZyB7XG4gIGlkPzogbnVtYmVyO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBsb2dpblR5cGU/OiAnZW1haWwnIHwgJ21vYmlsZSc7XG4gIGlzQWN0aXZlOiBib29sZWFuO1xuICBxdW90YVRvdGFsOiBudW1iZXI7XG4gIHF1b3RhVXNlZDogbnVtYmVyO1xuICBxdW90YVJlbWFpbmluZzogbnVtYmVyO1xuICBxdW90YVJlc2V0RGF0ZT86IHN0cmluZztcbiAgbGFzdFVzZWRBdD86IHN0cmluZztcbiAgdG90YWxBcGlDYWxsczogbnVtYmVyO1xuICBsYXN0QXV0aFN1Y2Nlc3M/OiBzdHJpbmc7XG4gIGxhc3RBdXRoRXJyb3I/OiBzdHJpbmc7XG4gIGF1dGhGYWlsdXJlQ291bnQ6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFRlc3RSZXN1bHQge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIHF1b3RhSW5mbz86IGFueTtcbiAgcmVzcG9uc2VUaW1lPzogbnVtYmVyO1xuICBlcnJvckRldGFpbHM/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEpRRGF0YUNvbmZpZ1BhZ2UoKSB7XG4gIGNvbnN0IHsgdXNlciwgaXNMb2FkaW5nOiBhdXRoTG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW2Zvcm1dID0gRm9ybS51c2VGb3JtKCk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Rlc3RMb2FkaW5nLCBzZXRUZXN0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjb25maWcsIHNldENvbmZpZ10gPSB1c2VTdGF0ZTxKUURhdGFDb25maWcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Rlc3RSZXN1bHQsIHNldFRlc3RSZXN1bHRdID0gdXNlU3RhdGU8VGVzdFJlc3VsdCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2hvd1Rlc3RNb2RhbCwgc2V0U2hvd1Rlc3RNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjdXJyZW50U3RlcCwgc2V0Q3VycmVudFN0ZXBdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtsb2dpblR5cGUsIHNldExvZ2luVHlwZV0gPSB1c2VTdGF0ZTwnZW1haWwnIHwgJ21vYmlsZSc+KCdtb2JpbGUnKTtcblxuICAvLyDpqozor4HnlKjmiLflkI3moLzlvI9cbiAgY29uc3QgdmFsaWRhdGVVc2VybmFtZSA9IChydWxlOiBhbnksIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXZhbHVlKSB7XG4gICAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QobmV3IEVycm9yKCfor7fovpPlhaXnlKjmiLflkI0nKSk7XG4gICAgfVxuXG4gICAgaWYgKGxvZ2luVHlwZSA9PT0gJ2VtYWlsJykge1xuICAgICAgY29uc3QgZW1haWxSZWdleCA9IC9eW15cXHNAXStAW15cXHNAXStcXC5bXlxcc0BdKyQvO1xuICAgICAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QodmFsdWUpKSB7XG4gICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChuZXcgRXJyb3IoJ+ivt+i+k+WFpeacieaViOeahOmCrueuseWcsOWdgCcpKTtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKGxvZ2luVHlwZSA9PT0gJ21vYmlsZScpIHtcbiAgICAgIGNvbnN0IG1vYmlsZVJlZ2V4ID0gL14xWzMtOV1cXGR7OX0kLztcbiAgICAgIGlmICghbW9iaWxlUmVnZXgudGVzdCh2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KG5ldyBFcnJvcign6K+36L6T5YWl5pyJ5pWI55qE5omL5py65Y+356CBJykpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTtcbiAgfTtcblxuICAvLyDliqDovb1KUURhdGHphY3nva5cbiAgY29uc3QgbG9hZENvbmZpZyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LmdldCgnL2FwaS92MS9qcWRhdGEvY29uZmlnJyk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBzZXRDb25maWcocmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIGNvbnN0IGNvbmZpZ0xvZ2luVHlwZSA9IHJlc3BvbnNlLmRhdGEubG9naW5UeXBlIHx8ICdtb2JpbGUnO1xuICAgICAgICBzZXRMb2dpblR5cGUoY29uZmlnTG9naW5UeXBlKTtcbiAgICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7XG4gICAgICAgICAgdXNlcm5hbWU6IHJlc3BvbnNlLmRhdGEudXNlcm5hbWUsXG4gICAgICAgICAgbG9naW5UeXBlOiBjb25maWdMb2dpblR5cGUsXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRDdXJyZW50U3RlcCgyKTsgLy8g5bey6YWN572uXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRDdXJyZW50U3RlcCgwKTsgLy8g5pyq6YWN572uXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICBzZXRDdXJyZW50U3RlcCgwKTsgLy8g5pyq6YWN572uXG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBtZXNzYWdlLmVycm9yKCfor7flhYjnmbvlvZXns7vnu58nKTtcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAoMCk7IC8vIOacqumFjee9rlxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L296YWN572u5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgbWVzc2FnZS5lcnJvcign5Yqg6L296YWN572u5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7XG4gICAgICAgIHNldEN1cnJlbnRTdGVwKDApOyAvLyDmnKrphY3nva5cbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOS/neWtmOmFjee9rlxuICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKHZhbHVlczogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRDdXJyZW50U3RlcCgxKTsgLy8g6YWN572u5LitXG4gICAgICBcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3QoJy9hcGkvdjEvanFkYXRhL2NvbmZpZycsIHtcbiAgICAgICAgdXNlcm5hbWU6IHZhbHVlcy51c2VybmFtZSxcbiAgICAgICAgcGFzc3dvcmQ6IHZhbHVlcy5wYXNzd29yZCxcbiAgICAgICAgbG9naW5UeXBlOiB2YWx1ZXMubG9naW5UeXBlIHx8IGxvZ2luVHlwZSxcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG1lc3NhZ2Uuc3VjY2VzcygnSlFEYXRh6YWN572u5L+d5a2Y5oiQ5Yqf77yBJyk7XG4gICAgICAgIHNldENvbmZpZyhyZXNwb25zZS5kYXRhKTtcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAoMik7IC8vIOmFjee9ruWujOaIkFxuICAgICAgICBmb3JtLnJlc2V0RmllbGRzKFsncGFzc3dvcmQnXSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICfphY3nva7kv53lrZjlpLHotKUnKTtcbiAgICAgIHNldEN1cnJlbnRTdGVwKDApOyAvLyDlm57liLDmnKrphY3nva7nirbmgIFcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa1i+ivlei/nuaOpVxuICBjb25zdCBoYW5kbGVUZXN0Q29ubmVjdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0VGVzdExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0KCcvYXBpL3YxL2pxZGF0YS90ZXN0LWNvbm5lY3Rpb24nKTtcbiAgICAgIFxuICAgICAgc2V0VGVzdFJlc3VsdChyZXNwb25zZS5kYXRhKTtcbiAgICAgIHNldFNob3dUZXN0TW9kYWwodHJ1ZSk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfov57mjqXmtYvor5XmiJDlip/vvIEnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+i/nuaOpea1i+ivleWksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+a1i+ivlei/nuaOpeWksei0pScpO1xuICAgICAgc2V0VGVzdFJlc3VsdCh7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAn5rWL6K+V6L+e5o6l5aSx6LSlJyxcbiAgICAgICAgZXJyb3JEZXRhaWxzOiBlcnJvci5tZXNzYWdlLFxuICAgICAgfSk7XG4gICAgICBzZXRTaG93VGVzdE1vZGFsKHRydWUpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRUZXN0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWIoOmZpOmFjee9rlxuICBjb25zdCBoYW5kbGVEZWxldGUgPSAoKSA9PiB7XG4gICAgTW9kYWwuY29uZmlybSh7XG4gICAgICB0aXRsZTogJ+ehruiupOWIoOmZpOmFjee9ricsXG4gICAgICBjb250ZW50OiAn5Yig6Zmk5ZCO5bCG5peg5rOV6I635Y+WSlFEYXRh5pWw5o2u77yM56Gu5a6a6KaB5Yig6Zmk5ZCX77yfJyxcbiAgICAgIG9rVGV4dDogJ+ehruWumuWIoOmZpCcsXG4gICAgICBva1R5cGU6ICdkYW5nZXInLFxuICAgICAgY2FuY2VsVGV4dDogJ+WPlua2iCcsXG4gICAgICBvbk9rOiBhc3luYyAoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgYXdhaXQgYXBpQ2xpZW50LmRlbGV0ZSgnL2FwaS92MS9qcWRhdGEvY29uZmlnJyk7XG4gICAgICAgICAgbWVzc2FnZS5zdWNjZXNzKCfphY3nva7liKDpmaTmiJDlip8nKTtcbiAgICAgICAgICBzZXRDb25maWcobnVsbCk7XG4gICAgICAgICAgc2V0Q3VycmVudFN0ZXAoMCk7XG4gICAgICAgICAgZm9ybS5yZXNldEZpZWxkcygpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIG1lc3NhZ2UuZXJyb3IoJ+WIoOmZpOmFjee9ruWksei0pScpO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgIH0pO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZENvbmZpZygpO1xuICB9LCBbXSk7XG5cbiAgLy8g6YWN572u5q2l6aqkXG4gIGNvbnN0IHN0ZXBzID0gW1xuICAgIHtcbiAgICAgIHRpdGxlOiAn6YWN572u6LSm5Y+3JyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn6L6T5YWlSlFEYXRh6LSm5Y+35L+h5oGvJyxcbiAgICAgIGljb246IDxBcGlPdXRsaW5lZCAvPixcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn6aqM6K+B6L+e5o6lJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn6aqM6K+B6LSm5Y+35pyJ5pWI5oCnJyxcbiAgICAgIGljb246IDxMaW5rT3V0bGluZWQgLz4sXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogJ+mFjee9ruWujOaIkCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ+W8gOWni+S9v+eUqEpRRGF0YeacjeWKoScsXG4gICAgICBpY29uOiA8Q2hlY2tDaXJjbGVPdXRsaW5lZCAvPixcbiAgICB9LFxuICBdO1xuXG4gIC8vIOiupOivgeajgOafpVxuICBpZiAoYXV0aExvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTY0XCI+XG4gICAgICAgIDxTcGluIHNpemU9XCJsYXJnZVwiIHRpcD1cIuato+WcqOmqjOivgeeUqOaIt+i6q+S7vS4uLlwiIC8+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCF1c2VyKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPFRpdGxlIGxldmVsPXsyfSBjbGFzc05hbWU9XCIhbWItMlwiPkpRRGF0YemFjee9rjwvVGl0bGU+XG4gICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiPumFjee9ruaCqOeahEpRRGF0Yei0puWPt+S7peiOt+WPluWunuaXtuW4guWcuuaVsOaNrjwvVGV4dD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPEFsZXJ0XG4gICAgICAgICAgbWVzc2FnZT1cIumcgOimgeeZu+W9lVwiXG4gICAgICAgICAgZGVzY3JpcHRpb249XCLor7flhYjnmbvlvZXns7vnu5/miY3og73phY3nva5KUURhdGHotKblj7dcIlxuICAgICAgICAgIHR5cGU9XCJ3YXJuaW5nXCJcbiAgICAgICAgICBzaG93SWNvblxuICAgICAgICAgIGFjdGlvbj17XG4gICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJwcmltYXJ5XCIgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9hdXRoL2xvZ2luJyl9PlxuICAgICAgICAgICAgICDliY3lvoDnmbvlvZVcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIH1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAobG9hZGluZyAmJiAhY29uZmlnKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8U3BpbiBzaXplPVwibGFyZ2VcIiB0aXA9XCLmraPlnKjliqDovb3phY3nva4uLi5cIiAvPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiDpobXpnaLmoIfpopggKi99XG4gICAgICA8ZGl2PlxuICAgICAgICA8VGl0bGUgbGV2ZWw9ezJ9IGNsYXNzTmFtZT1cIiFtYi0yXCI+XG4gICAgICAgICAgSlFEYXRh6YWN572uXG4gICAgICAgIDwvVGl0bGU+XG4gICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICDphY3nva7mgqjnmoRKUURhdGHotKblj7fku6Xojrflj5blrp7ml7bluILlnLrmlbDmja7vvIzmlK/mjIHpgq7nrrHmiJbmiYvmnLrlj7fnmbvlvZVcbiAgICAgICAgPC9UZXh0PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDphY3nva7mraXpqqQgKi99XG4gICAgICA8Q2FyZD5cbiAgICAgICAgPFN0ZXBzIGN1cnJlbnQ9e2N1cnJlbnRTdGVwfSBpdGVtcz17c3RlcHN9IC8+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIHsvKiDphY3nva7nirbmgIHmj5DnpLogKi99XG4gICAgICB7Y29uZmlnID8gKFxuICAgICAgICA8QWxlcnRcbiAgICAgICAgICBtZXNzYWdlPVwiSlFEYXRh5bey6YWN572uXCJcbiAgICAgICAgICBkZXNjcmlwdGlvbj17XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2Pui0puWPtzoge2NvbmZpZy51c2VybmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj7nmbvlvZXmlrnlvI86IHtjb25maWcubG9naW5UeXBlID09PSAnbW9iaWxlJyA/ICfmiYvmnLrlj7cnIDogJ+mCrueusSd9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+54q25oCBOiB7Y29uZmlnLmlzQWN0aXZlID8gJ+ato+W4uCcgOiAn5byC5bi4J308L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIH1cbiAgICAgICAgICB0eXBlPXtjb25maWcuaXNBY3RpdmUgPyAnc3VjY2VzcycgOiAnd2FybmluZyd9XG4gICAgICAgICAgc2hvd0ljb25cbiAgICAgICAgICBhY3Rpb249e1xuICAgICAgICAgICAgPFNwYWNlPlxuICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbWFsbFwiIG9uQ2xpY2s9e2hhbmRsZVRlc3RDb25uZWN0aW9ufSBsb2FkaW5nPXt0ZXN0TG9hZGluZ30+XG4gICAgICAgICAgICAgICAg5rWL6K+V6L+e5o6lXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbWFsbFwiIG9uQ2xpY2s9e2xvYWRDb25maWd9PlxuICAgICAgICAgICAgICAgIOWIt+aWsOeKtuaAgVxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgfVxuICAgICAgICAvPlxuICAgICAgKSA6IChcbiAgICAgICAgPEFsZXJ0XG4gICAgICAgICAgbWVzc2FnZT1cIkpRRGF0YeacqumFjee9rlwiXG4gICAgICAgICAgZGVzY3JpcHRpb249XCLor7fphY3nva7mgqjnmoRKUURhdGHotKblj7fku6Xojrflj5blrp7ml7bluILlnLrmlbDmja7vvIzmlK/mjIHpgq7nrrHmiJbmiYvmnLrlj7fnmbvlvZVcIlxuICAgICAgICAgIHR5cGU9XCJpbmZvXCJcbiAgICAgICAgICBzaG93SWNvblxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgPFJvdyBndXR0ZXI9e1sxNiwgMTZdfT5cbiAgICAgICAgey8qIOmFjee9ruihqOWNlSAqL31cbiAgICAgICAgPENvbCB4cz17MjR9IGxnPXsxMn0+XG4gICAgICAgICAgPENhcmQgdGl0bGU9XCLotKblj7fphY3nva5cIiBleHRyYT17XG4gICAgICAgICAgICBjb25maWcgJiYgKFxuICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJsaW5rXCIgZGFuZ2VyIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZX0+XG4gICAgICAgICAgICAgICAg5Yig6Zmk6YWN572uXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgKVxuICAgICAgICAgIH0+XG4gICAgICAgICAgICA8QWxlcnRcbiAgICAgICAgICAgICAgbWVzc2FnZT1cIkpRRGF0YeeZu+W9leivtOaYjlwiXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtcbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIDxzdHJvbmc+5omL5py65Y+355m75b2VPC9zdHJvbmc+77yaSlFEYXRh5a6Y5pa55o6o6I2Q55qE5Li76KaB55m75b2V5pa55byPPC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIDxzdHJvbmc+6YKu566x55m75b2VPC9zdHJvbmc+77ya6YCC55So5LqO5pep5pyf5rOo5YaM55qE55So5oi3PC9wPlxuICAgICAgICAgICAgICAgICAgPHA+4oCiIOivt+S9v+eUqOaCqOWcqEpRRGF0YeWumOe9keazqOWGjOaXtueahOi0puWPt+WSjOWvhueggTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB0eXBlPVwiaW5mb1wiXG4gICAgICAgICAgICAgIHNob3dJY29uXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1iLTRcIlxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgPEZvcm1cbiAgICAgICAgICAgICAgZm9ybT17Zm9ybX1cbiAgICAgICAgICAgICAgbGF5b3V0PVwidmVydGljYWxcIlxuICAgICAgICAgICAgICBvbkZpbmlzaD17aGFuZGxlU2F2ZX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgIGluaXRpYWxWYWx1ZXM9e3sgbG9naW5UeXBlOiAnbW9iaWxlJyB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICAgICAgbmFtZT1cImxvZ2luVHlwZVwiXG4gICAgICAgICAgICAgICAgbGFiZWw9e1xuICAgICAgICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgICAgICAgIOeZu+W9leaWueW8j1xuICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCB0aXRsZT1cIkpRRGF0YeS4u+imgeaUr+aMgeaJi+acuuWPt+eZu+W9le+8jOmDqOWIhuiAgeeUqOaIt+aUr+aMgemCrueuseeZu+W9lVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxRdWVzdGlvbkNpcmNsZU91dGxpbmVkIGNsYXNzTmFtZT1cIm1sLTEgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UmFkaW8uR3JvdXBcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dpblR5cGV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgc2V0TG9naW5UeXBlKGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgZm9ybS5zZXRGaWVsZHNWYWx1ZSh7IHVzZXJuYW1lOiAnJyB9KTsgLy8g5riF56m655So5oi35ZCNXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxSYWRpby5CdXR0b24gdmFsdWU9XCJtb2JpbGVcIj5cbiAgICAgICAgICAgICAgICAgICAgPE1vYmlsZU91dGxpbmVkIC8+IOaJi+acuuWPt+eZu+W9lVxuICAgICAgICAgICAgICAgICAgPC9SYWRpby5CdXR0b24+XG4gICAgICAgICAgICAgICAgICA8UmFkaW8uQnV0dG9uIHZhbHVlPVwiZW1haWxcIj5cbiAgICAgICAgICAgICAgICAgICAgPE1haWxPdXRsaW5lZCAvPiDpgq7nrrHnmbvlvZVcbiAgICAgICAgICAgICAgICAgIDwvUmFkaW8uQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvUmFkaW8uR3JvdXA+XG4gICAgICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgICAgIDxGb3JtLkl0ZW1cbiAgICAgICAgICAgICAgICBuYW1lPVwidXNlcm5hbWVcIlxuICAgICAgICAgICAgICAgIGxhYmVsPXtsb2dpblR5cGUgPT09ICdlbWFpbCcgPyAnSlFEYXRh6YKu566xJyA6ICdKUURhdGHmiYvmnLrlj7cnfVxuICAgICAgICAgICAgICAgIHJ1bGVzPXtbXG4gICAgICAgICAgICAgICAgICB7IHZhbGlkYXRvcjogdmFsaWRhdGVVc2VybmFtZSB9LFxuICAgICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcbiAgICAgICAgICAgICAgICAgICAgbG9naW5UeXBlID09PSAnZW1haWwnXG4gICAgICAgICAgICAgICAgICAgICAgPyAn6K+36L6T5YWlSlFEYXRh5rOo5YaM6YKu566xJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+ivt+i+k+WFpUpRRGF0YeazqOWGjOaJi+acuuWPtydcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIHByZWZpeD17bG9naW5UeXBlID09PSAnZW1haWwnID8gPE1haWxPdXRsaW5lZCAvPiA6IDxNb2JpbGVPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L0Zvcm0uSXRlbT5cblxuICAgICAgICAgICAgICA8Rm9ybS5JdGVtXG4gICAgICAgICAgICAgICAgbmFtZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIkpRRGF0YeWvhueggVwiXG4gICAgICAgICAgICAgICAgcnVsZXM9e1tcbiAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6ICFjb25maWcsIG1lc3NhZ2U6ICfor7fovpPlhaVKUURhdGHlr4bnoIEnIH0sXG4gICAgICAgICAgICAgICAgICB7IG1pbjogNiwgbWVzc2FnZTogJ+WvhueggemVv+W6puiHs+WwkTbkvY0nIH0sXG4gICAgICAgICAgICAgICAgXX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxJbnB1dC5QYXNzd29yZFxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2NvbmZpZyA/ICfnlZnnqbrooajnpLrkuI3kv67mlLnlr4bnoIEnIDogJ+ivt+i+k+WFpUpRRGF0YeWvhueggSd9XG4gICAgICAgICAgICAgICAgICBpY29uUmVuZGVyPXsodmlzaWJsZSkgPT4gXG4gICAgICAgICAgICAgICAgICAgIHZpc2libGUgPyA8RXllVHdvVG9uZSAvPiA6IDxFeWVJbnZpc2libGVPdXRsaW5lZCAvPlxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvRm9ybS5JdGVtPlxuXG4gICAgICAgICAgICAgIDxGb3JtLkl0ZW0+XG4gICAgICAgICAgICAgICAgPFNwYWNlPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgIGh0bWxUeXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgaWNvbj17PENoZWNrQ2lyY2xlT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtjb25maWcgPyAn5pu05paw6YWN572uJyA6ICfkv53lrZjphY3nva4nfVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIHtjb25maWcgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlVGVzdENvbm5lY3Rpb259XG4gICAgICAgICAgICAgICAgICAgICAgbG9hZGluZz17dGVzdExvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgaWNvbj17PExpbmtPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIOa1i+ivlei/nuaOpVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICAgICAgPC9Gb3JtLkl0ZW0+XG4gICAgICAgICAgICA8L0Zvcm0+XG5cbiAgICAgICAgICAgIHsvKiDphY3nva7or7TmmI4gKi99XG4gICAgICAgICAgICA8QWxlcnRcbiAgICAgICAgICAgICAgbWVzc2FnZT1cIumFjee9ruivtOaYjlwiXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PuKAoiDmlK/mjIHpgq7nrrHmiJbmiYvmnLrlj7fnmbvlvZXvvIzor7fpgInmi6nmgqjlnKhKUURhdGHms6jlhozml7bkvb/nlKjnmoTmlrnlvI88L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+4oCiIOmCrueuseagvOW8j++8mmV4YW1wbGVAZG9tYWluLmNvbTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj7igKIg5omL5py65Y+35qC85byP77yaMTM4MTIzNDU2NzjvvIjkuK3lm73lpKfpmYbmiYvmnLrlj7fvvIk8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+4oCiIOWvhueggeWwhuiiq+WKoOWvhuWtmOWCqO+8jOehruS/nei0puWPt+WuieWFqDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj7igKIg6YWN572u5ZCO5Y+v6I635Y+W5a6e5pe26IKh56Wo5pWw5o2u5ZKM5Y6G5Y+y5pWw5o2uPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PuKAoiDlpoLpgYfpl67popjor7fmo4Dmn6XotKblj7fnirbmgIHmiJbogZTns7vlrqLmnI08L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB0eXBlPVwiaW5mb1wiXG4gICAgICAgICAgICAgIHNob3dJY29uXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgIDwvQ29sPlxuXG4gICAgICAgIHsvKiDphY3pop3kv6Hmga8gKi99XG4gICAgICAgIDxDb2wgeHM9ezI0fSBsZz17MTJ9PlxuICAgICAgICAgIDxDYXJkIHRpdGxlPVwi6YWN6aKd5L2/55So5oOF5Ya1XCI+XG4gICAgICAgICAgICB7Y29uZmlnID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIDxSb3cgZ3V0dGVyPXsxNn0+XG4gICAgICAgICAgICAgICAgICA8Q29sIHNwYW49ezEyfT5cbiAgICAgICAgICAgICAgICAgICAgPFN0YXRpc3RpY1xuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5oC76YWN6aKdXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y29uZmlnLnF1b3RhVG90YWx9XG4gICAgICAgICAgICAgICAgICAgICAgc3VmZml4PVwi5qyhXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgICAgICAgICAgPENvbCBzcGFuPXsxMn0+XG4gICAgICAgICAgICAgICAgICAgIDxTdGF0aXN0aWNcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuW3suS9v+eUqFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5xdW90YVVzZWR9XG4gICAgICAgICAgICAgICAgICAgICAgc3VmZml4PVwi5qyhXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZVN0eWxlPXt7IFxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGNvbmZpZy5xdW90YVVzZWQgLyBjb25maWcucXVvdGFUb3RhbCA+IDAuOCA/ICcjZmY0ZDRmJyA6ICcjMTg5MGZmJyBcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgICAgICAgPC9Sb3c+XG5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8VGV4dD7kvb/nlKjnjoc8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz5cbiAgICAgICAgICAgICAgICAgICAgICB7KChjb25maWcucXVvdGFVc2VkIC8gY29uZmlnLnF1b3RhVG90YWwpICogMTAwKS50b0ZpeGVkKDEpfSVcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8UHJvZ3Jlc3NcbiAgICAgICAgICAgICAgICAgICAgcGVyY2VudD17KGNvbmZpZy5xdW90YVVzZWQgLyBjb25maWcucXVvdGFUb3RhbCkgKiAxMDB9XG4gICAgICAgICAgICAgICAgICAgIHN0YXR1cz17Y29uZmlnLnF1b3RhVXNlZCAvIGNvbmZpZy5xdW90YVRvdGFsID4gMC45ID8gJ2V4Y2VwdGlvbicgOiAnYWN0aXZlJ31cbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlQ29sb3I9e3tcbiAgICAgICAgICAgICAgICAgICAgICAnMCUnOiAnIzEwOGVlOScsXG4gICAgICAgICAgICAgICAgICAgICAgJzEwMCUnOiAnIzg3ZDA2OCcsXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPERlc2NyaXB0aW9ucyBzaXplPVwic21hbGxcIiBjb2x1bW49ezF9PlxuICAgICAgICAgICAgICAgICAgPERlc2NyaXB0aW9ucy5JdGVtIGxhYmVsPVwi5Ymp5L2Z6YWN6aKdXCI+XG4gICAgICAgICAgICAgICAgICAgIHtjb25maWcucXVvdGFSZW1haW5pbmd9IOasoVxuICAgICAgICAgICAgICAgICAgPC9EZXNjcmlwdGlvbnMuSXRlbT5cbiAgICAgICAgICAgICAgICAgIDxEZXNjcmlwdGlvbnMuSXRlbSBsYWJlbD1cIuaAu+iwg+eUqOasoeaVsFwiPlxuICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLnRvdGFsQXBpQ2FsbHN9IOasoVxuICAgICAgICAgICAgICAgICAgPC9EZXNjcmlwdGlvbnMuSXRlbT5cbiAgICAgICAgICAgICAgICAgIDxEZXNjcmlwdGlvbnMuSXRlbSBsYWJlbD1cIuacgOWQjuS9v+eUqFwiPlxuICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLmxhc3RVc2VkQXQgPyBuZXcgRGF0ZShjb25maWcubGFzdFVzZWRBdCkudG9Mb2NhbGVTdHJpbmcoKSA6ICfmnKrkvb/nlKgnfVxuICAgICAgICAgICAgICAgICAgPC9EZXNjcmlwdGlvbnMuSXRlbT5cbiAgICAgICAgICAgICAgICAgIDxEZXNjcmlwdGlvbnMuSXRlbSBsYWJlbD1cIumFjeminemHjee9rlwiPlxuICAgICAgICAgICAgICAgICAgICB7Y29uZmlnLnF1b3RhUmVzZXREYXRlID8gbmV3IERhdGUoY29uZmlnLnF1b3RhUmVzZXREYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoKSA6ICfmnKrnn6UnfVxuICAgICAgICAgICAgICAgICAgPC9EZXNjcmlwdGlvbnMuSXRlbT5cbiAgICAgICAgICAgICAgICA8L0Rlc2NyaXB0aW9ucz5cblxuICAgICAgICAgICAgICAgIHtjb25maWcuYXV0aEZhaWx1cmVDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgPEFsZXJ0XG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U9e2DorqTor4HlpLHotKUgJHtjb25maWcuYXV0aEZhaWx1cmVDb3VudH0g5qyhYH1cbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249e2NvbmZpZy5sYXN0QXV0aEVycm9yfVxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwid2FybmluZ1wiXG4gICAgICAgICAgICAgICAgICAgIHNob3dJY29uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgIOmFjee9rkpRRGF0Yei0puWPt+WQjuafpeeci+mFjemineS/oeaBr1xuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9Db2w+XG4gICAgICA8L1Jvdz5cblxuICAgICAgey8qIOa1i+ivlee7k+aenOaooeaAgeahhiAqL31cbiAgICAgIDxNb2RhbFxuICAgICAgICB0aXRsZT1cIui/nuaOpea1i+ivlee7k+aenFwiXG4gICAgICAgIG9wZW49e3Nob3dUZXN0TW9kYWx9XG4gICAgICAgIG9uQ2FuY2VsPXsoKSA9PiBzZXRTaG93VGVzdE1vZGFsKGZhbHNlKX1cbiAgICAgICAgZm9vdGVyPXtbXG4gICAgICAgICAgPEJ1dHRvbiBrZXk9XCJjbG9zZVwiIG9uQ2xpY2s9eygpID0+IHNldFNob3dUZXN0TW9kYWwoZmFsc2UpfT5cbiAgICAgICAgICAgIOWFs+mXrVxuICAgICAgICAgIDwvQnV0dG9uPixcbiAgICAgICAgXX1cbiAgICAgID5cbiAgICAgICAge3Rlc3RSZXN1bHQgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8QWxlcnRcbiAgICAgICAgICAgICAgbWVzc2FnZT17dGVzdFJlc3VsdC5zdWNjZXNzID8gJ+i/nuaOpeaIkOWKnycgOiAn6L+e5o6l5aSx6LSlJ31cbiAgICAgICAgICAgICAgZGVzY3JpcHRpb249e3Rlc3RSZXN1bHQubWVzc2FnZX1cbiAgICAgICAgICAgICAgdHlwZT17dGVzdFJlc3VsdC5zdWNjZXNzID8gJ3N1Y2Nlc3MnIDogJ2Vycm9yJ31cbiAgICAgICAgICAgICAgc2hvd0ljb25cbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIHt0ZXN0UmVzdWx0LnJlc3BvbnNlVGltZSAmJiAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPFRleHQgdHlwZT1cInNlY29uZGFyeVwiPuWTjeW6lOaXtumXtDogPC9UZXh0PlxuICAgICAgICAgICAgICAgIDxUZXh0IHN0cm9uZz57dGVzdFJlc3VsdC5yZXNwb25zZVRpbWUudG9GaXhlZCgzKX3np5I8L1RleHQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge3Rlc3RSZXN1bHQucXVvdGFJbmZvICYmIChcbiAgICAgICAgICAgICAgPERlc2NyaXB0aW9ucyB0aXRsZT1cIumFjemineS/oeaBr1wiIHNpemU9XCJzbWFsbFwiIGNvbHVtbj17Mn0+XG4gICAgICAgICAgICAgICAgPERlc2NyaXB0aW9ucy5JdGVtIGxhYmVsPVwi5oC76YWN6aKdXCI+XG4gICAgICAgICAgICAgICAgICB7dGVzdFJlc3VsdC5xdW90YUluZm8ucXVvdGFUb3RhbH1cbiAgICAgICAgICAgICAgICA8L0Rlc2NyaXB0aW9ucy5JdGVtPlxuICAgICAgICAgICAgICAgIDxEZXNjcmlwdGlvbnMuSXRlbSBsYWJlbD1cIuW3suS9v+eUqFwiPlxuICAgICAgICAgICAgICAgICAge3Rlc3RSZXN1bHQucXVvdGFJbmZvLnF1b3RhVXNlZH1cbiAgICAgICAgICAgICAgICA8L0Rlc2NyaXB0aW9ucy5JdGVtPlxuICAgICAgICAgICAgICAgIDxEZXNjcmlwdGlvbnMuSXRlbSBsYWJlbD1cIuWJqeS9mVwiPlxuICAgICAgICAgICAgICAgICAge3Rlc3RSZXN1bHQucXVvdGFJbmZvLnF1b3RhUmVtYWluaW5nfVxuICAgICAgICAgICAgICAgIDwvRGVzY3JpcHRpb25zLkl0ZW0+XG4gICAgICAgICAgICAgICAgPERlc2NyaXB0aW9ucy5JdGVtIGxhYmVsPVwi5L2/55So546HXCI+XG4gICAgICAgICAgICAgICAgICB7dGVzdFJlc3VsdC5xdW90YUluZm8ucXVvdGFVc2FnZVJhdGU/LnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgIDwvRGVzY3JpcHRpb25zLkl0ZW0+XG4gICAgICAgICAgICAgIDwvRGVzY3JpcHRpb25zPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge3Rlc3RSZXN1bHQuZXJyb3JEZXRhaWxzICYmIChcbiAgICAgICAgICAgICAgPEFsZXJ0XG4gICAgICAgICAgICAgICAgbWVzc2FnZT1cIumUmeivr+ivpuaDhVwiXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb249e3Rlc3RSZXN1bHQuZXJyb3JEZXRhaWxzfVxuICAgICAgICAgICAgICAgIHR5cGU9XCJlcnJvclwiXG4gICAgICAgICAgICAgICAgc2hvd0ljb25cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L01vZGFsPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDYXJkIiwiRm9ybSIsIklucHV0IiwiQnV0dG9uIiwiVHlwb2dyYXBoeSIsIkFsZXJ0IiwiUHJvZ3Jlc3MiLCJTdGF0aXN0aWMiLCJSb3ciLCJDb2wiLCJTcGFjZSIsIm1lc3NhZ2UiLCJNb2RhbCIsIkRlc2NyaXB0aW9ucyIsIlNwaW4iLCJTdGVwcyIsIlJhZGlvIiwiVG9vbHRpcCIsIkFwaU91dGxpbmVkIiwiQ2hlY2tDaXJjbGVPdXRsaW5lZCIsIkV5ZUludmlzaWJsZU91dGxpbmVkIiwiRXllVHdvVG9uZSIsIkxpbmtPdXRsaW5lZCIsIk1haWxPdXRsaW5lZCIsIk1vYmlsZU91dGxpbmVkIiwiUXVlc3Rpb25DaXJjbGVPdXRsaW5lZCIsInVzZUF1dGgiLCJhcGlDbGllbnQiLCJ1c2VSb3V0ZXIiLCJUaXRsZSIsIlRleHQiLCJQYXJhZ3JhcGgiLCJTdGVwIiwiSlFEYXRhQ29uZmlnUGFnZSIsInRlc3RSZXN1bHQiLCJ1c2VyIiwiaXNMb2FkaW5nIiwiYXV0aExvYWRpbmciLCJyb3V0ZXIiLCJmb3JtIiwidXNlRm9ybSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwidGVzdExvYWRpbmciLCJzZXRUZXN0TG9hZGluZyIsImNvbmZpZyIsInNldENvbmZpZyIsInNldFRlc3RSZXN1bHQiLCJzaG93VGVzdE1vZGFsIiwic2V0U2hvd1Rlc3RNb2RhbCIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJsb2dpblR5cGUiLCJzZXRMb2dpblR5cGUiLCJ2YWxpZGF0ZVVzZXJuYW1lIiwicnVsZSIsInZhbHVlIiwiUHJvbWlzZSIsInJlamVjdCIsIkVycm9yIiwiZW1haWxSZWdleCIsInRlc3QiLCJtb2JpbGVSZWdleCIsInJlc29sdmUiLCJsb2FkQ29uZmlnIiwicmVzcG9uc2UiLCJnZXQiLCJjb2RlIiwiZGF0YSIsImNvbmZpZ0xvZ2luVHlwZSIsInNldEZpZWxkc1ZhbHVlIiwidXNlcm5hbWUiLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiLCJoYW5kbGVTYXZlIiwidmFsdWVzIiwicG9zdCIsInBhc3N3b3JkIiwic3VjY2VzcyIsInJlc2V0RmllbGRzIiwiaGFuZGxlVGVzdENvbm5lY3Rpb24iLCJlcnJvckRldGFpbHMiLCJoYW5kbGVEZWxldGUiLCJjb25maXJtIiwidGl0bGUiLCJjb250ZW50Iiwib2tUZXh0Iiwib2tUeXBlIiwiY2FuY2VsVGV4dCIsIm9uT2siLCJkZWxldGUiLCJzdGVwcyIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImRpdiIsImNsYXNzTmFtZSIsInNpemUiLCJ0aXAiLCJsZXZlbCIsInR5cGUiLCJzaG93SWNvbiIsImFjdGlvbiIsIm9uQ2xpY2siLCJwdXNoIiwiY3VycmVudCIsIml0ZW1zIiwiaXNBY3RpdmUiLCJndXR0ZXIiLCJ4cyIsImxnIiwiZXh0cmEiLCJkYW5nZXIiLCJwIiwic3Ryb25nIiwibGF5b3V0Iiwib25GaW5pc2giLCJkaXNhYmxlZCIsImluaXRpYWxWYWx1ZXMiLCJJdGVtIiwibmFtZSIsImxhYmVsIiwic3BhbiIsIkdyb3VwIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicnVsZXMiLCJ2YWxpZGF0b3IiLCJwbGFjZWhvbGRlciIsInByZWZpeCIsInJlcXVpcmVkIiwibWluIiwiUGFzc3dvcmQiLCJpY29uUmVuZGVyIiwidmlzaWJsZSIsImh0bWxUeXBlIiwicXVvdGFUb3RhbCIsInN1ZmZpeCIsInF1b3RhVXNlZCIsInZhbHVlU3R5bGUiLCJjb2xvciIsInRvRml4ZWQiLCJwZXJjZW50Iiwic3Ryb2tlQ29sb3IiLCJjb2x1bW4iLCJxdW90YVJlbWFpbmluZyIsInRvdGFsQXBpQ2FsbHMiLCJsYXN0VXNlZEF0IiwiRGF0ZSIsInRvTG9jYWxlU3RyaW5nIiwicXVvdGFSZXNldERhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJhdXRoRmFpbHVyZUNvdW50IiwibGFzdEF1dGhFcnJvciIsIm9wZW4iLCJvbkNhbmNlbCIsImZvb3RlciIsInJlc3BvbnNlVGltZSIsInF1b3RhSW5mbyIsInF1b3RhVXNhZ2VSYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx\n"));

/***/ })

});