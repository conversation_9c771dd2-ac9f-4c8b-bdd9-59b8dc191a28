"""
数据库连接配置模块

提供PostgreSQL和Redis的连接管理
支持异步操作和连接池配置
"""

from typing import AsyncGenerator, Optional

import redis.asyncio as aioredis
from redis.asyncio import Redis
from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool, QueuePool

from app.core.config import settings
from app.core.logging import logger

# =============================================================================
# SQLAlchemy 配置
# =============================================================================

# 声明基类
Base = declarative_base()

# 异步引擎配置
async_engine = create_async_engine(
    settings.database_url_async,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    pool_pre_ping=True,
    echo=settings.DEBUG,
    future=True,
)

# 同步引擎配置（用于Alembic迁移）
sync_engine = create_engine(
    settings.database_url_sync,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
    pool_recycle=settings.DATABASE_POOL_RECYCLE,
    pool_pre_ping=True,
    echo=settings.DEBUG,
    future=True,
)

# 异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)

# 同步会话工厂
SessionLocal = sessionmaker(
    bind=sync_engine,
    autoflush=True,
    autocommit=False,
)


# =============================================================================
# 数据库事件监听器
# =============================================================================

@event.listens_for(sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置数据库连接参数"""
    if "postgresql" in str(sync_engine.url):
        # PostgreSQL 连接参数
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SET timezone TO 'Asia/Shanghai'")
            cursor.execute("SET statement_timeout = '30s'")


@event.listens_for(async_engine.sync_engine, "connect")
def set_async_sqlite_pragma(dbapi_connection, connection_record):
    """设置异步数据库连接参数"""
    if "postgresql" in str(async_engine.url):
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SET timezone TO 'Asia/Shanghai'")
            cursor.execute("SET statement_timeout = '30s'")


# =============================================================================
# 数据库会话依赖
# =============================================================================

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            # 只有在有未提交的更改时才提交
            if session.dirty or session.new or session.deleted:
                await session.commit()
        except Exception as e:
            await session.rollback()
            # 只有在非HTTP异常时才记录错误日志
            from fastapi import HTTPException
            if not isinstance(e, HTTPException):
                logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()


def get_sync_db():
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error(f"同步数据库会话错误: {e}")
        raise
    finally:
        db.close()


def get_db_session():
    """获取数据库会话（用于服务层）"""
    return AsyncSessionLocal()


# =============================================================================
# Redis 配置
# =============================================================================

class RedisManager:
    """Redis连接管理器"""

    def __init__(self):
        self._redis: Optional[Redis] = None
        self._connection_pool = None

    async def connect(self) -> None:
        """建立Redis连接"""
        try:
            # 在开发环境中使用模拟Redis
            if settings.USE_MOCK_REDIS or settings.ENVIRONMENT == "development":
                from app.services.mock_redis import get_mock_redis
                self._redis = await get_mock_redis()
                logger.info("使用MockRedis模拟服务 (开发环境)")
                return

            # 生产环境使用真实Redis
            self._connection_pool = aioredis.ConnectionPool.from_url(
                settings.redis_url_str,
                max_connections=settings.REDIS_MAX_CONNECTIONS,
                socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                socket_connect_timeout=settings.REDIS_SOCKET_CONNECT_TIMEOUT,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            self._redis = aioredis.Redis(
                connection_pool=self._connection_pool,
                decode_responses=True,
            )
            
            # 测试连接
            await self._redis.ping()
            logger.info("Redis连接成功")

        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            logger.warning("Redis不可用，缓存功能将被禁用")
            self._redis = None

    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self._redis:
            # 检查是否是模拟Redis
            if hasattr(self._redis, '_connected'):  # MockRedis特征
                await self._redis.disconnect()
            else:
                await self._redis.close()
            logger.info("Redis连接已断开")

    @property
    def redis(self) -> Optional[Redis]:
        """获取Redis实例"""
        return self._redis

    async def get_redis(self) -> Optional[Redis]:
        """获取Redis实例（异步）"""
        if not self._redis:
            await self.connect()
        return self._redis


# 全局Redis管理器实例
redis_manager = RedisManager()


async def get_redis() -> Optional[Redis]:
    """获取Redis连接依赖"""
    return await redis_manager.get_redis()


# =============================================================================
# 数据库初始化和清理
# =============================================================================

async def init_db() -> None:
    """初始化数据库"""
    try:
        # 创建所有表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_db() -> None:
    """关闭数据库连接"""
    try:
        await async_engine.dispose()
        sync_engine.dispose()
        logger.info("数据库连接已关闭")
        
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {e}")


# =============================================================================
# 健康检查
# =============================================================================

async def check_database_health() -> bool:
    """检查数据库健康状态"""
    try:
        from sqlalchemy import text
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False


async def check_redis_health() -> bool:
    """检查Redis健康状态"""
    try:
        redis_client = await redis_manager.get_redis()
        if redis_client is None:
            return False
        pong = await redis_client.ping()
        return pong is True
    except Exception as e:
        logger.error(f"Redis健康检查失败: {e}")
        return False


# =============================================================================
# 数据库工具函数
# =============================================================================

async def execute_raw_sql(sql: str, params: Optional[dict] = None) -> any:
    """执行原始SQL语句"""
    async with AsyncSessionLocal() as session:
        try:
            result = await session.execute(sql, params or {})
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            logger.error(f"执行SQL失败: {sql}, 错误: {e}")
            raise


async def get_table_row_count(table_name: str) -> int:
    """获取表行数"""
    sql = f"SELECT COUNT(*) FROM {table_name}"
    result = await execute_raw_sql(sql)
    return result.scalar()


async def truncate_table(table_name: str) -> None:
    """清空表数据"""
    sql = f"TRUNCATE TABLE {table_name} RESTART IDENTITY CASCADE"
    await execute_raw_sql(sql)
    logger.info(f"表 {table_name} 已清空")
