# ============================================================================
# JQData量化数据平台 - 完整功能依赖
# ============================================================================

# 基础依赖
-r requirements.txt

# 深度学习框架 (大型依赖)
tensorflow==2.13.0
torch==2.0.0
torchvision==0.15.0

# 高级数据处理
polars==0.19.19
statsmodels==0.14.0

# 机器学习扩展
xgboost==2.0.2
lightgbm==4.1.0
catboost==1.2.2
optuna==3.4.0
hyperopt==0.2.7

# 任务监控
flower==2.0.1

# 高级技术指标
ta==0.10.2
finta==1.3

# 数据可视化
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 金融数据处理
yfinance==0.2.28
tushare==1.2.89

# 风险管理
pyfolio==0.9.2
empyrical==0.5.5

# 回测框架
backtrader==**********
zipline-reloaded==3.0.4

# ============================================================================
# 安装说明
# ============================================================================
# 
# 完整功能安装:
#   pip install -r requirements-full.txt
#
# 注意事项:
# 1. 这些依赖包体积较大，安装时间较长
# 2. 深度学习框架需要较多系统资源
# 3. 某些包可能需要额外的系统依赖
# 4. 建议在虚拟环境中安装
# ============================================================================
