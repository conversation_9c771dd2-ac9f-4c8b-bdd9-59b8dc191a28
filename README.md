# 🚀 智能量化交易平台

一个基于AI的全栈量化交易平台，集成了机器学习、深度学习、知识图谱等先进技术，为量化投资提供全方位的解决方案。

## ✨ 核心特性

### 📊 数据管理
- **多源数据接入**: 支持聚宽、Tushare、AKShare等多个数据源
- **实时行情**: WebSocket实时推送股票、期货、外汇等金融数据
- **数据清洗**: 自动化数据预处理和质量检查
- **历史数据**: 完整的历史行情和基本面数据存储

### 🤖 AI模型引擎
- **机器学习**: 支持XGBoost、LightGBM、CatBoost等主流算法
- **深度学习**: 集成PyTorch，支持LSTM、Transformer等神经网络
- **AutoML**: 自动化机器学习流水线，智能模型选择和调参
- **模型可解释性**: SHAP、LIME等可解释性分析工具
- **多模态学习**: 文本、图像、音频等多模态数据融合

### 🕸️ 知识图谱
- **金融实体识别**: 自动识别股票、公司、人员、事件等实体
- **关系抽取**: 挖掘实体间的复杂关系网络
- **图神经网络**: GCN、GAT、GraphSAGE等图学习算法
- **图分析**: 社区发现、中心性分析、路径分析等

### 📈 交易系统
- **策略回测**: 高性能向量化回测引擎
- **实盘交易**: 支持多个券商接口的实盘交易
- **风险管理**: 实时风险监控和预警系统
- **投资组合**: 智能资产配置和组合优化

### 🎯 用户界面
- **响应式设计**: 支持桌面端和移动端的完美适配
- **实时图表**: 基于ECharts和D3.js的专业金融图表
- **交互式仪表板**: 可定制的数据可视化面板
- **暗色主题**: 支持明暗主题切换

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI + Python 3.9+
- **数据库**: PostgreSQL + Redis
- **ORM**: SQLAlchemy 2.0 (异步)
- **任务队列**: Celery + Redis
- **机器学习**: scikit-learn, XGBoost, LightGBM, CatBoost
- **深度学习**: PyTorch, Transformers, torch-geometric
- **数据处理**: pandas, numpy, scipy
- **API文档**: OpenAPI 3.0 + Swagger UI

### 前端技术栈
- **框架**: Next.js 14 + React 18 + TypeScript
- **UI组件**: Ant Design 5.x
- **状态管理**: Zustand + React Query
- **样式**: Tailwind CSS + CSS Modules
- **图表**: ECharts, D3.js, @ant-design/charts
- **动画**: Framer Motion
- **构建工具**: Webpack 5 + SWC

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (可选)

### 使用Docker部署 (推荐)

1. **克隆项目**
```bash
git clone https://github.com/your-repo/quantitative-trading-platform.git
cd quantitative-trading-platform
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接、API密钥等
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 本地开发部署

#### 后端设置

1. **创建虚拟环境**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

2. **安装依赖**
```bash
pip install -r requirements-dev.txt
```

3. **数据库迁移**
```bash
alembic upgrade head
```

4. **启动后端服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端设置

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

3. **访问应用**
```bash
open http://localhost:3000
```

## 📚 功能模块

### 1. 数据管理模块
- 数据源配置和管理
- 实时数据订阅和推送
- 历史数据存储和查询
- 数据质量监控

### 2. 机器学习模块
- 特征工程和选择
- 模型训练和验证
- 超参数优化
- 模型部署和监控

### 3. 深度学习模块
- 神经网络架构设计
- 分布式训练支持
- 模型压缩和加速
- GPU/TPU支持

### 4. AutoML模块
- 自动特征工程
- 神经架构搜索
- 超参数自动调优
- 模型自动选择

### 5. 知识图谱模块
- 实体和关系抽取
- 图数据库存储
- 图神经网络训练
- 图分析和可视化

### 6. 交易系统模块
- 策略开发框架
- 回测引擎
- 实盘交易接口
- 风险管理系统

### 7. 可解释性模块
- SHAP值分析
- LIME局部解释
- 特征重要性分析
- 模型决策路径可视化

## 🔧 配置说明

### 环境变量配置
详细的环境变量配置请参考 `.env.example` 文件，主要包括：

- **数据库配置**: PostgreSQL和Redis连接信息
- **数据源配置**: 各个金融数据API的密钥
- **机器学习配置**: 模型训练相关参数
- **安全配置**: JWT密钥、CORS设置等

### 数据源配置
支持的数据源包括：
- 聚宽数据 (JQData)
- Tushare
- AKShare
- Yahoo Finance
- Alpha Vantage

## 📖 API文档

启动后端服务后，可以通过以下地址访问API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试

### 后端测试
```bash
cd backend
pytest tests/ -v --cov=app
```

### 前端测试
```bash
cd frontend
npm run test
npm run test:coverage
```

## 📊 性能监控

### 系统监控
- **Prometheus**: 指标收集
- **Grafana**: 可视化面板
- **AlertManager**: 告警管理

### 应用监控
- **APM**: 应用性能监控
- **日志聚合**: ELK Stack
- **错误追踪**: Sentry

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的支持：
- [FastAPI](https://fastapi.tiangolo.com/)
- [Next.js](https://nextjs.org/)
- [Ant Design](https://ant.design/)
- [PyTorch](https://pytorch.org/)
- [scikit-learn](https://scikit-learn.org/)

## 📞 联系我们

- 项目主页: https://github.com/your-repo/quantitative-trading-platform
- 问题反馈: https://github.com/your-repo/quantitative-trading-platform/issues
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
