# 🚀 JQData量化交易平台

基于JQData API的专业量化交易平台，严格按照[JQData官方API文档](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842)规范实现，提供完整的数据获取、策略开发、回测分析和投资组合管理功能。

## ✨ 核心特性

### 📊 JQData数据集成
- **标准化API**: 完全按照JQData官方文档规范实现所有数据接口
- **多账号支持**: 支持邮箱和手机号两种JQData登录方式
- **实时数据**: 股票、基金、指数、期货等多种标的的实时和历史数据
- **财务数据**: 完整的上市公司财务数据和基本面信息
- **行业数据**: 行业分类、概念板块等结构化数据

### 🔐 认证与安全
- **JQData认证**: 按照官方规范实现邮箱和手机号登录
- **配额管理**: 实时监控JQData API调用配额和使用情况
- **数据缓存**: 智能缓存策略减少API调用，提高响应速度
- **加密存储**: 用户JQData密码采用加密存储，确保安全

### 📈 数据获取规范
严格按照[JQData数据获取规范](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842)实现：
- **get_all_securities()**: 获取所有标的信息
- **get_price()**: 获取历史价格数据，支持多种频率和复权方式
- **get_current_data()**: 获取实时价格数据
- **get_fundamentals()**: 获取财务数据
- **get_industry()**: 获取行业信息

### 📈 交易系统
- **策略回测**: 高性能向量化回测引擎
- **实盘交易**: 支持多个券商接口的实盘交易
- **风险管理**: 实时风险监控和预警系统
- **投资组合**: 智能资产配置和组合优化

### 🎯 用户界面
- **响应式设计**: 支持桌面端和移动端的完美适配
- **实时图表**: 基于ECharts和D3.js的专业金融图表
- **交互式仪表板**: 可定制的数据可视化面板
- **暗色主题**: 支持明暗主题切换

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI + Python 3.12+
- **数据库**: SQLite (可扩展至PostgreSQL) + Redis
- **ORM**: SQLAlchemy 2.0 (异步)
- **数据源**: JQData官方API + jqdatasdk
- **认证**: JWT Token + 加密存储
- **缓存**: Redis缓存 + 智能缓存策略
- **数据处理**: pandas, numpy
- **API文档**: OpenAPI 3.0 + Swagger UI

### 前端技术栈
- **框架**: Next.js 14 + React 18 + TypeScript
- **UI组件**: Ant Design 5.x
- **状态管理**: Zustand + React Query
- **样式**: Tailwind CSS + CSS Modules
- **图表**: ECharts, D3.js, @ant-design/charts
- **动画**: Framer Motion
- **构建工具**: Webpack 5 + SWC

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 🚀 快速开始

### 环境要求
- Python 3.12+
- Node.js 18+
- JQData账号 ([注册地址](https://www.joinquant.com/default/index/sdk))
- Redis 6+ (可选，用于缓存)

### 🔑 默认管理员账号
系统初始化后会自动创建默认管理员账号：

```
邮箱: <EMAIL>
用户名: admin
密码: admin123456
```

**⚠️ 重要提醒**: 首次登录后请立即修改默认密码！

### 🎯 Cursor开发环境 (推荐)

如果您使用Cursor IDE开发，我们提供了专门的开发环境配置：

#### Windows用户
```bash
# 双击运行启动脚本
start-cursor-dev.bat
```

#### Linux/Mac用户
```bash
# 运行启动脚本
./start-cursor-dev.sh
```

#### 🌟 Cursor开发环境特性
- ✅ **无需安装Redis** - 使用内存模拟Redis服务
- ✅ **无需安装PostgreSQL** - 使用SQLite数据库
- ✅ **一键启动** - 自动配置开发环境
- ✅ **热重载** - 代码修改自动重启
- ✅ **详细日志** - 便于调试开发

### 1. 克隆项目
```bash
git clone <repository-url>
cd JQData
```

### 2. 后端设置
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置必要的配置

# 初始化数据库
python -m alembic upgrade head

# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 前端设置
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. 访问应用
- 前端地址: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 5. 登录系统
使用默认管理员账号登录：
- 邮箱: `<EMAIL>`
- 密码: `admin123456`

登录后请立即：
1. 修改默认密码
2. 配置JQData账号信息
3. 开始使用量化交易功能

## 📊 JQData API集成规范

### 认证方式
按照[JQData官方认证文档](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10748)实现：

```python
# 邮箱登录
jq.auth('<EMAIL>', 'your_password')

# 手机号登录
jq.auth('13812345678', 'your_password', host='https://dataapi.joinquant.com', port=443)
```

### 数据获取API
严格按照[JQData数据获取规范](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842)实现：

#### 1. 获取标的信息
```http
GET /api/v1/market/securities?types=stock&date=2024-01-01
```

#### 2. 获取价格数据
```http
GET /api/v1/market/price?security=000001.XSHE&start_date=2024-01-01&end_date=2024-01-31&frequency=daily&fq=pre
```

#### 3. 获取当前数据
```http
GET /api/v1/market/current?security=000001.XSHE&fields=last_price,volume,money
```

### 📋 JQData配置详细指南

#### 🔑 准备JQData账号
1. **注册JQData账号**
   - 访问 [聚宽官网](https://www.joinquant.com/) 注册账号
   - 完成实名认证

2. **申请API权限**
   - 访问 [JQData SDK申请页面](https://www.joinquant.com/default/index/sdk)
   - 提交API调用权限申请
   - 等待审核通过（通常1-2个工作日）

#### ⚙️ 系统配置步骤
1. **登录系统**
   - 访问 http://localhost:3000
   - 使用管理员账号登录：
     - 邮箱：`<EMAIL>`
     - 密码：`admin123456`

2. **进入JQData配置页面**
   - 导航到：Dashboard → Settings → JQData配置
   - 或直接访问：http://localhost:3000/dashboard/settings/jqdata

3. **配置JQData账号**
   - 选择登录方式（手机号或邮箱）
   - 输入您的JQData用户名
   - 输入您的JQData密码
   - 点击"保存配置"

4. **验证配置**
   - 系统会自动验证JQData账号的有效性
   - 验证成功后显示配额信息
   - 配置保存后即可开始使用

#### 🔧 常见问题解决

**问题1：404错误 - "JQData配置不存在"**
- **原因**：用户还没有配置JQData账号
- **解决**：按照上述步骤配置JQData账号

**问题2：400错误 - "JQData账号验证失败"**
- **原因**：JQData账号或密码错误，或者没有API权限
- **解决**：
  1. 检查账号密码是否正确
  2. 确认已申请并获得JQData API权限
  3. 访问 https://www.joinquant.com/default/index/sdk 申请权限

**问题3：bcrypt版本警告**
- **现象**：日志中出现 `(trapped) error reading bcrypt version`
- **影响**：不影响功能正常使用，仅为兼容性警告
- **解决**：可以忽略，系统功能正常

#### 📊 配额管理
配置成功后，系统会显示：
- 总配额：您的JQData账号总配额
- 已使用配额：当前已使用的配额
- 剩余配额：可用的剩余配额
- 配额重置日期：配额重置的日期

## 🔧 环境配置
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接、API密钥等
```

3. **启动服务**
```bash
docker-compose up -d
```

4. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 本地开发部署

#### 后端设置

1. **创建虚拟环境**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

2. **安装依赖**
```bash
pip install -r requirements-dev.txt
```

3. **数据库迁移**
```bash
alembic upgrade head
```

4. **启动后端服务**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端设置

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

3. **访问应用**
```bash
open http://localhost:3000
```

## 📚 功能模块

### 1. 数据管理模块
- 数据源配置和管理
- 实时数据订阅和推送
- 历史数据存储和查询
- 数据质量监控

### 2. 机器学习模块
- 特征工程和选择
- 模型训练和验证
- 超参数优化
- 模型部署和监控

### 3. 深度学习模块
- 神经网络架构设计
- 分布式训练支持
- 模型压缩和加速
- GPU/TPU支持

### 4. AutoML模块
- 自动特征工程
- 神经架构搜索
- 超参数自动调优
- 模型自动选择

### 5. 知识图谱模块
- 实体和关系抽取
- 图数据库存储
- 图神经网络训练
- 图分析和可视化

### 6. 交易系统模块
- 策略开发框架
- 回测引擎
- 实盘交易接口
- 风险管理系统

### 7. 可解释性模块
- SHAP值分析
- LIME局部解释
- 特征重要性分析
- 模型决策路径可视化

## 🔧 配置说明

### 环境变量配置
详细的环境变量配置请参考 `.env.example` 文件，主要包括：

- **数据库配置**: PostgreSQL和Redis连接信息
- **数据源配置**: 各个金融数据API的密钥
- **机器学习配置**: 模型训练相关参数
- **安全配置**: JWT密钥、CORS设置等

### 数据源配置
支持的数据源包括：
- 聚宽数据 (JQData)
- Tushare
- AKShare
- Yahoo Finance
- Alpha Vantage

## 📖 API文档

启动后端服务后，可以通过以下地址访问API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试

### 后端测试
```bash
cd backend
pytest tests/ -v --cov=app
```

### 前端测试
```bash
cd frontend
npm run test
npm run test:coverage
```

## � 功能完成度分析

### ✅ 已完成功能 (65%)

#### 🔐 用户认证系统 (95%)
- [x] JWT认证机制
- [x] JQData账号集成 (邮箱/手机号)
- [x] 密码加密存储
- [x] 权限控制
- [ ] 多因子认证

#### 📊 市场数据模块 (70%)
- [x] 基础K线图表
- [x] 股票详情页面
- [x] 市场概览
- [x] 新闻分析
- [ ] 实时数据推送
- [ ] 高级技术指标
- [ ] WebSocket连接

#### 💼 投资组合管理 (80%)
- [x] 持仓管理
- [x] 绩效分析
- [x] 收益统计
- [x] 风险指标
- [ ] 真实交易执行
- [ ] 高级风险管理

#### 🎯 策略开发 (75%)
- [x] 策略编辑器
- [x] 回测框架
- [x] 策略列表管理
- [x] 可视化回测结果
- [ ] 策略自动执行
- [ ] 高级回测功能

#### ⚙️ 系统设置 (90%)
- [x] 个人资料管理
- [x] JQData配置
- [x] 系统偏好设置
- [ ] 高级系统配置

### 🔴 未完成功能 (35%)

#### 高优先级 (需要立即完成)
1. **📊 实时数据集成**
   - WebSocket实时推送
   - 真实股票数据API
   - 数据缓存优化

2. **📈 高级图表功能**
   - 技术指标叠加 (MACD, RSI, BOLL)
   - 多时间周期切换
   - 图表标注工具

3. **🔄 策略自动执行引擎**
   - 策略调度器
   - 信号生成器
   - 风险控制器

#### 中优先级 (后续完成)
4. **📄 报告生成系统**
   - PDF/Excel报告
   - 定期报告
   - 自定义报告

5. **🔔 智能通知系统**
   - 邮件通知
   - 价格预警
   - 策略信号提醒

6. **🤖 AI/ML功能模块**
   - 智能选股
   - 价格预测
   - 情绪分析

#### 低优先级 (长期规划)
7. **🌐 多模态数据分析**
   - 新闻情绪分析
   - 社交媒体监控

8. **🕸️ 知识图谱**
   - 公司关系图谱
   - 行业关联分析

### 📊 模块完成度统计

| 模块 | 完成度 | 状态 | 主要缺失功能 |
|------|--------|------|-------------|
| 🔐 用户认证 | 95% | ✅ | 多因子认证 |
| 📊 市场数据 | 70% | ⚠️ | 实时数据、高级图表 |
| 💼 投资组合 | 80% | ⚠️ | 真实交易、高级分析 |
| 🎯 策略开发 | 75% | ⚠️ | 自动执行、高级回测 |
| ⚙️ 系统设置 | 90% | ✅ | 高级配置 |
| 🔔 通知系统 | 10% | ❌ | 完整通知功能 |
| 📄 报告系统 | 20% | ❌ | PDF生成、定期报告 |
| 🤖 AI/ML | 5% | ❌ | 所有AI功能 |
| 🔒 安全性 | 70% | ⚠️ | 高级安全功能 |

## 📋 开发路线图

### 🚀 第一阶段 - 核心功能完善 (2-4周)
- [ ] 实时数据集成和WebSocket推送
- [ ] 高级图表功能 (技术指标)
- [ ] 策略自动执行引擎
- [ ] 性能优化和缓存

### 🎯 第二阶段 - 高级功能 (1-2月)
- [ ] 报告生成系统
- [ ] 智能通知系统
- [ ] 高级风险管理
- [ ] 数据库优化

### 🧠 第三阶段 - AI功能 (2-3月)
- [ ] 机器学习模型集成
- [ ] 智能选股和预测
- [ ] 多模态数据分析
- [ ] 知识图谱构建

## �📊 性能监控

### 系统监控
- **Prometheus**: 指标收集
- **Grafana**: 可视化面板
- **AlertManager**: 告警管理

### 应用监控
- **APM**: 应用性能监控
- **日志聚合**: ELK Stack
- **错误追踪**: Sentry

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📋 JQData数据处理规范

### 常见数据问题处理

#### 融资融券数据
- **上交所**: 当日收盘后更新融资融券数据
- **深交所**: 下一个交易日10点更新融资融券数据
- **注意**: 深交所周五的融资融券数据需等到周一上午10点才能获取

#### 申万行业数据
- **2014年2月21日重大调整**:
  - 新增11个行业代码：['801710','801720','801730','801740','801750','801760','801770','801780','801790','801880','801890']
  - 弃用6个行业代码：['801060','801070','801090','801100','801190','801220']
- **数据规则**: 新增行业代码在2014-02-21之前没有成分股，弃用代码在2014-02-21之后没有成分股

#### 指数数据处理
- **上证指数(000001) vs A股指数(000002)**:
  - 000001包含上交所A股和B股
  - 000002只包含上交所A股
  - 由于未提供B股数据，成分股完全一致
- **申万行业指数**: 基日1999-12-31，提供自基日开始的完整行情数据

#### 财务数据规则
- **报告期数据**: 严格按照官方报告期规则处理
- **单季度数据**: 支持单季度和年度财务数据查询
- **数据更新**: 遵循JQData官方数据更新时间规则

### 数据获取最佳实践

#### 1. get_price vs get_bars
- **get_price**: 按时间范围获取数据，适合获取指定时间段的历史数据
- **get_bars**: 按数量获取数据，适合获取最近N条数据

#### 2. 复权处理
- **前复权(pre)**: 以当前价格为基准向前复权
- **后复权(post)**: 以上市价格为基准向后复权
- **不复权(None)**: 使用原始价格数据

#### 3. 缓存策略
- **标的信息**: 缓存1小时，变化频率低
- **日线数据**: 缓存1小时，盘后更新
- **分钟数据**: 缓存5分钟，实时性要求高
- **财务数据**: 缓存24小时，更新频率低

## 🙏 致谢

感谢以下项目和服务的支持：
- [JQData](https://www.joinquant.com/) - 提供专业的金融数据服务
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Next.js](https://nextjs.org/) - React生产级框架
- [Ant Design](https://ant.design/) - 企业级UI设计语言

## 📞 联系我们

- 项目主页: https://github.com/your-repo/quantitative-trading-platform
- 问题反馈: https://github.com/your-repo/quantitative-trading-platform/issues
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
