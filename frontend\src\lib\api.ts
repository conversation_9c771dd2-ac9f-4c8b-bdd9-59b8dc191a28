import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  success?: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
}

class ApiClient {
  private instance: AxiosInstance;
  private tokens: AuthTokens | null = null;

  constructor() {
    this.instance = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        if (this.tokens?.accessToken) {
          config.headers.Authorization = `Bearer ${this.tokens.accessToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // 尝试刷新token
            if (this.tokens?.refreshToken) {
              const response = await this.refreshToken();
              if (response.data?.accessToken) {
                this.setAuthTokens(response.data);
                originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`;
                return this.instance(originalRequest);
              }
            }
          } catch (refreshError) {
            // 刷新失败，清除tokens并跳转到登录页
            this.clearAuthTokens();
            if (typeof window !== 'undefined') {
              window.location.href = '/auth/login';
            }
          }
        }

        return Promise.reject(error);
      }
    );
  }

  getInstance(): AxiosInstance {
    return this.instance;
  }

  setAuthTokens(tokens: AuthTokens): void {
    this.tokens = tokens;
    // 可以选择存储到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_tokens', JSON.stringify(tokens));
    }
  }

  getAuthTokens(): AuthTokens | null {
    if (!this.tokens && typeof window !== 'undefined') {
      const stored = localStorage.getItem('auth_tokens');
      if (stored) {
        try {
          this.tokens = JSON.parse(stored);
        } catch (e) {
          console.error('Failed to parse stored tokens:', e);
        }
      }
    }
    return this.tokens;
  }

  clearAuthTokens(): void {
    this.tokens = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_tokens');
    }
  }

  private async refreshToken(): Promise<AxiosResponse<AuthTokens>> {
    if (!this.tokens?.refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.instance.post('/api/v1/auth/refresh', {
      refreshToken: this.tokens.refreshToken,
    });
  }

  // 通用API方法
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post(url, data, config);
    return response.data;
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put(url, data, config);
    return response.data;
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete(url, config);
    return response.data;
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch(url, data, config);
    return response.data;
  }
}

// 创建单例实例
export const apiClient = new ApiClient();

// 导出默认实例
export default apiClient;
