"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/jqdata/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/settings/jqdata/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JQDataConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/radio/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/descriptions/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MailOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MobileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * JQData配置页面\n * \n * 用户配置JQData账号、查看配额使用情况、测试连接等\n */ \n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction JQDataConfigPage() {\n    var _testResult_quotaInfo_quotaUsageRate;\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testLoading, setTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTestModal, setShowTestModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"email\");\n    // 验证用户名格式\n    const validateUsername = (rule, value)=>{\n        if (!value) {\n            return Promise.reject(new Error(\"请输入用户名\"));\n        }\n        if (loginType === \"email\") {\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的邮箱地址\"));\n            }\n        } else if (loginType === \"mobile\") {\n            const mobileRegex = /^1[3-9]\\d{9}$/;\n            if (!mobileRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的手机号码\"));\n            }\n        }\n        return Promise.resolve();\n    };\n    // 加载JQData配置\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/jqdata/config\");\n            if (response.code === 200 && response.data) {\n                setConfig(response.data);\n                const configLoginType = response.data.loginType || \"email\";\n                setLoginType(configLoginType);\n                form.setFieldsValue({\n                    username: response.data.username,\n                    loginType: configLoginType\n                });\n                setCurrentStep(2); // 已配置\n            } else {\n                setCurrentStep(0); // 未配置\n            }\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                setCurrentStep(0); // 未配置\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"加载配置失败\");\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 保存配置\n    const handleSave = async (values)=>{\n        try {\n            setLoading(true);\n            setCurrentStep(1); // 配置中\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/config\", {\n                username: values.username,\n                password: values.password,\n                loginType: values.loginType || loginType\n            });\n            if (response.code === 200) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"JQData配置保存成功！\");\n                setConfig(response.data);\n                setCurrentStep(2); // 配置完成\n                form.resetFields([\n                    \"password\"\n                ]);\n            } else {\n                throw new Error(response.message);\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"配置保存失败\");\n            setCurrentStep(0); // 回到未配置状态\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试连接\n    const handleTestConnection = async ()=>{\n        try {\n            setTestLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/test-connection\");\n            setTestResult(response.data);\n            setShowTestModal(true);\n            if (response.data.success) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"连接测试成功！\");\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"连接测试失败\");\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"测试连接失败\");\n            setTestResult({\n                success: false,\n                message: \"测试连接失败\",\n                errorDetails: error.message\n            });\n            setShowTestModal(true);\n        } finally{\n            setTestLoading(false);\n        }\n    };\n    // 删除配置\n    const handleDelete = ()=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].confirm({\n            title: \"确认删除配置\",\n            content: \"删除后将无法获取JQData数据，确定要删除吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            onOk: async ()=>{\n                try {\n                    await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/jqdata/config\");\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"配置删除成功\");\n                    setConfig(null);\n                    setCurrentStep(0);\n                    form.resetFields();\n                } catch (error) {\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"删除配置失败\");\n                }\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // 配置步骤\n    const steps = [\n        {\n            title: \"配置账号\",\n            description: \"输入JQData账号信息\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"验证连接\",\n            description: \"验证账号有效性\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"配置完成\",\n            description: \"开始使用JQData服务\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    if (loading && !config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: \"JQData配置\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        children: \"配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    current: currentStep,\n                    items: steps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData已配置\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"账号: \",\n                                config.username\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"登录方式: \",\n                                config.loginType === \"mobile\" ? \"手机号\" : \"邮箱\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"状态: \",\n                                config.isActive ? \"正常\" : \"异常\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 13\n                }, void 0),\n                type: config.isActive ? \"success\" : \"warning\",\n                showIcon: true,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: handleTestConnection,\n                            loading: testLoading,\n                            children: \"测试连接\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: loadConfig,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\",\n                type: \"info\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"账号配置\",\n                            extra: config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                onClick: handleDelete,\n                                children: \"删除配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    form: form,\n                                    layout: \"vertical\",\n                                    onFinish: handleSave,\n                                    disabled: loading,\n                                    initialValues: {\n                                        loginType: \"email\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"loginType\",\n                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"登录方式\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        title: \"选择您在JQData注册时使用的账号类型\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"ml-1 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Group, {\n                                                value: loginType,\n                                                onChange: (e)=>{\n                                                    setLoginType(e.target.value);\n                                                    form.setFieldsValue({\n                                                        username: \"\"\n                                                    }); // 清空用户名\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Button, {\n                                                        value: \"email\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 邮箱登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Button, {\n                                                        value: \"mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 手机号登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"username\",\n                                            label: loginType === \"email\" ? \"JQData邮箱\" : \"JQData手机号\",\n                                            rules: [\n                                                {\n                                                    validator: validateUsername\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                placeholder: loginType === \"email\" ? \"请输入JQData注册邮箱\" : \"请输入JQData注册手机号\",\n                                                prefix: loginType === \"email\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 51\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 70\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"password\",\n                                            label: \"JQData密码\",\n                                            rules: [\n                                                {\n                                                    required: !config,\n                                                    message: \"请输入JQData密码\"\n                                                },\n                                                {\n                                                    min: 6,\n                                                    message: \"密码长度至少6位\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Password, {\n                                                placeholder: config ? \"留空表示不修改密码\" : \"请输入JQData密码\",\n                                                iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 31\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 48\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        type: \"primary\",\n                                                        htmlType: \"submit\",\n                                                        loading: loading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        children: config ? \"更新配置\" : \"保存配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        onClick: handleTestConnection,\n                                                        loading: testLoading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: \"测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"配置说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 支持邮箱或手机号登录，请选择您在JQData注册时使用的方式\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 邮箱格式：<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 手机号格式：13812345678（中国大陆手机号）\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 密码将被加密存储，确保账号安全\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 配置后可获取实时股票数据和历史数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 如遇问题请检查账号状态或联系客服\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mt-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"配额使用情况\",\n                            children: config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        gutter: 16,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    title: \"总配额\",\n                                                    value: config.quotaTotal,\n                                                    suffix: \"次\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    title: \"已使用\",\n                                                    value: config.quotaUsed,\n                                                    suffix: \"次\",\n                                                    valueStyle: {\n                                                        color: config.quotaUsed / config.quotaTotal > 0.8 ? \"#ff4d4f\" : \"#1890ff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        children: \"使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        strong: true,\n                                                        children: [\n                                                            (config.quotaUsed / config.quotaTotal * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                percent: config.quotaUsed / config.quotaTotal * 100,\n                                                status: config.quotaUsed / config.quotaTotal > 0.9 ? \"exception\" : \"active\",\n                                                strokeColor: {\n                                                    \"0%\": \"#108ee9\",\n                                                    \"100%\": \"#87d068\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        size: \"small\",\n                                        column: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"剩余配额\",\n                                                children: [\n                                                    config.quotaRemaining,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"总调用次数\",\n                                                children: [\n                                                    config.totalApiCalls,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"最后使用\",\n                                                children: config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : \"未使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"配额重置\",\n                                                children: config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : \"未知\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this),\n                                    config.authFailureCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        message: \"认证失败 \".concat(config.authFailureCount, \" 次\"),\n                                        description: config.lastAuthError,\n                                        type: \"warning\",\n                                        showIcon: true,\n                                        className: \"mt-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"配置JQData账号后查看配额信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                title: \"连接测试结果\",\n                open: showTestModal,\n                onCancel: ()=>setShowTestModal(false),\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>setShowTestModal(false),\n                        children: \"关闭\"\n                    }, \"close\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: testResult.success ? \"连接成功\" : \"连接失败\",\n                            description: testResult.message,\n                            type: testResult.success ? \"success\" : \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this),\n                        testResult.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"响应时间: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: [\n                                        testResult.responseTime.toFixed(3),\n                                        \"秒\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 15\n                        }, this),\n                        testResult.quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            title: \"配额信息\",\n                            size: \"small\",\n                            column: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"总配额\",\n                                    children: testResult.quotaInfo.quotaTotal\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"已使用\",\n                                    children: testResult.quotaInfo.quotaUsed\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"剩余\",\n                                    children: testResult.quotaInfo.quotaRemaining\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"使用率\",\n                                    children: [\n                                        (_testResult_quotaInfo_quotaUsageRate = testResult.quotaInfo.quotaUsageRate) === null || _testResult_quotaInfo_quotaUsageRate === void 0 ? void 0 : _testResult_quotaInfo_quotaUsageRate.toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 15\n                        }, this),\n                        testResult.errorDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: \"错误详情\",\n                            description: testResult.errorDetails,\n                            type: \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(JQDataConfigPage, \"w2C8f7+qmF4P4nT/eLuB5icmSIA=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = JQDataConfigPage;\nvar _c;\n$RefreshReg$(_c, \"JQDataConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx\n"));

/***/ })

});