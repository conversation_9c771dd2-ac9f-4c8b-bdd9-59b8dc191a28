# =============================================================================
# Cursor开发环境配置
# =============================================================================

# 应用基础配置
APP_NAME=JQData量化交易平台
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development
TZ=Asia/Shanghai

# 数据库配置 (使用SQLite)
DATABASE_URL=sqlite+aiosqlite:///./quantitative_trading.db
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30
DATABASE_POOL_RECYCLE=3600

# Redis配置 (使用模拟Redis)
USE_MOCK_REDIS=true
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# JWT安全配置
SECRET_KEY=cursor-dev-secret-key-for-development-only-change-in-production-please
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 缓存配置
CACHE_TTL=3600
CACHE_KEY_PREFIX=jqdata_dev

# 加密配置
ENCRYPTION_KEY=LyciGXAswARrYg_bONAjbWPAk9GgYZQ9jlzTRiS6qEM=

# JQData配置
JQDATA_USERNAME=
JQDATA_PASSWORD=
JQDATA_TIMEOUT=30
JQDATA_RETRY_TIMES=3
JQDATA_RETRY_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=detailed
LOG_FILE_ENABLED=false
LOG_FILE_PATH=./logs/app.log
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_BACKUP_COUNT=5

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# 开发环境特定配置
DEV_AUTO_RELOAD=true
DEV_DEBUG_SQL=false
DEV_MOCK_JQDATA=false
DEV_SKIP_AUTH=false

# 性能配置
MAX_WORKERS=1
WORKER_TIMEOUT=30
KEEP_ALIVE=2

# 安全配置 (开发环境宽松设置)
SECURE_COOKIES=false
CSRF_PROTECTION=false
RATE_LIMIT_ENABLED=false

# 监控配置
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false
PROMETHEUS_ENABLED=false

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXTENSIONS=[".csv", ".xlsx", ".json"]
UPLOAD_PATH=./uploads

# 邮件配置 (开发环境可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>

# 第三方服务配置 (开发环境可选)
WEBHOOK_URL=
DINGTALK_WEBHOOK=
WECHAT_WEBHOOK=

# 备份配置
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_STORAGE_PATH=./backups
