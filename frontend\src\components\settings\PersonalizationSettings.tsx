'use client';

/**
 * 个性化设置组件
 * 
 * 用户个性化配置管理
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Switch,
  Slider,
  ColorPicker,
  Input,
  Button,
  Space,
  Tabs,
  Row,
  Col,
  Typography,
  Divider,
  message,
  Upload,
  Avatar,
  Radio,
  InputNumber,
  TimePicker
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  UserOutlined,
  BgColorsOutlined,
  BellOutlined,
  DashboardOutlined,
  BarChartOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface PersonalizationConfig {
  // 主题设置
  theme: {
    mode: 'light' | 'dark' | 'auto';
    primaryColor: string;
    fontSize: number;
    borderRadius: number;
    compactMode: boolean;
  };
  // 仪表板设置
  dashboard: {
    layout: 'grid' | 'list' | 'masonry';
    widgets: string[];
    refreshInterval: number;
    showWelcome: boolean;
    defaultPage: string;
  };
  // 图表设置
  charts: {
    defaultType: string;
    colorScheme: string[];
    animation: boolean;
    showDataLabels: boolean;
    gridLines: boolean;
  };
  // 通知设置
  notifications: {
    email: boolean;
    push: boolean;
    sound: boolean;
    priceAlerts: boolean;
    portfolioUpdates: boolean;
    marketNews: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
  // 数据设置
  data: {
    defaultCurrency: string;
    numberFormat: string;
    dateFormat: string;
    timezone: string;
    autoRefresh: boolean;
    cacheEnabled: boolean;
  };
  // 快捷键设置
  shortcuts: {
    enabled: boolean;
    customKeys: Record<string, string>;
  };
}

export const PersonalizationSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<PersonalizationConfig>({
    theme: {
      mode: 'light',
      primaryColor: '#1890ff',
      fontSize: 14,
      borderRadius: 6,
      compactMode: false
    },
    dashboard: {
      layout: 'grid',
      widgets: ['market-overview', 'portfolio-summary', 'recent-trades'],
      refreshInterval: 30,
      showWelcome: true,
      defaultPage: '/dashboard'
    },
    charts: {
      defaultType: 'line',
      colorScheme: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
      animation: true,
      showDataLabels: false,
      gridLines: true
    },
    notifications: {
      email: true,
      push: true,
      sound: false,
      priceAlerts: true,
      portfolioUpdates: true,
      marketNews: false,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00'
      }
    },
    data: {
      defaultCurrency: 'CNY',
      numberFormat: 'standard',
      dateFormat: 'YYYY-MM-DD',
      timezone: 'Asia/Shanghai',
      autoRefresh: true,
      cacheEnabled: true
    },
    shortcuts: {
      enabled: true,
      customKeys: {
        'dashboard': 'Ctrl+D',
        'portfolio': 'Ctrl+P',
        'charts': 'Ctrl+C',
        'search': 'Ctrl+K'
      }
    }
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // 从localStorage或API加载设置
      const savedSettings = localStorage.getItem('personalization_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setConfig(parsedSettings);
        form.setFieldsValue(parsedSettings);
      }
    } catch (error) {
      console.error('Load settings error:', error);
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      const newConfig = { ...config, ...values };
      setConfig(newConfig);
      
      // 保存到localStorage
      localStorage.setItem('personalization_settings', JSON.stringify(newConfig));
      
      // 应用主题设置
      applyThemeSettings(newConfig.theme);
      
      message.success('个性化设置已保存');
    } catch (error) {
      console.error('Save settings error:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const applyThemeSettings = (themeConfig: PersonalizationConfig['theme']) => {
    // 应用主题模式
    if (themeConfig.mode === 'dark') {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else if (themeConfig.mode === 'light') {
      document.documentElement.setAttribute('data-theme', 'light');
    } else {
      // 自动模式
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
    }

    // 应用主色调
    document.documentElement.style.setProperty('--primary-color', themeConfig.primaryColor);
    
    // 应用字体大小
    document.documentElement.style.setProperty('--font-size-base', `${themeConfig.fontSize}px`);
    
    // 应用圆角
    document.documentElement.style.setProperty('--border-radius-base', `${themeConfig.borderRadius}px`);
  };

  const handleReset = () => {
    form.resetFields();
    setConfig({
      theme: {
        mode: 'light',
        primaryColor: '#1890ff',
        fontSize: 14,
        borderRadius: 6,
        compactMode: false
      },
      dashboard: {
        layout: 'grid',
        widgets: ['market-overview', 'portfolio-summary', 'recent-trades'],
        refreshInterval: 30,
        showWelcome: true,
        defaultPage: '/dashboard'
      },
      charts: {
        defaultType: 'line',
        colorScheme: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
        animation: true,
        showDataLabels: false,
        gridLines: true
      },
      notifications: {
        email: true,
        push: true,
        sound: false,
        priceAlerts: true,
        portfolioUpdates: true,
        marketNews: false,
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00'
        }
      },
      data: {
        defaultCurrency: 'CNY',
        numberFormat: 'standard',
        dateFormat: 'YYYY-MM-DD',
        timezone: 'Asia/Shanghai',
        autoRefresh: true,
        cacheEnabled: true
      },
      shortcuts: {
        enabled: true,
        customKeys: {
          'dashboard': 'Ctrl+D',
          'portfolio': 'Ctrl+P',
          'charts': 'Ctrl+C',
          'search': 'Ctrl+K'
        }
      }
    });
    message.success('设置已重置为默认值');
  };

  const widgetOptions = [
    { label: '市场概览', value: 'market-overview' },
    { label: '投资组合摘要', value: 'portfolio-summary' },
    { label: '最近交易', value: 'recent-trades' },
    { label: '价格提醒', value: 'price-alerts' },
    { label: '新闻资讯', value: 'news-feed' },
    { label: '技术指标', value: 'technical-indicators' },
    { label: '收益图表', value: 'performance-chart' },
    { label: '热门股票', value: 'trending-stocks' }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        title={
          <Space>
            <SettingOutlined />
            个性化设置
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              loading={loading}
              onClick={() => form.submit()}
            >
              保存设置
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          initialValues={config}
        >
          <Tabs defaultActiveKey="theme">
            <TabPane
              tab={
                <Space>
                  <BgColorsOutlined />
                  主题外观
                </Space>
              }
              key="theme"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Form.Item label="主题模式" name={['theme', 'mode']}>
                    <Radio.Group>
                      <Radio.Button value="light">浅色</Radio.Button>
                      <Radio.Button value="dark">深色</Radio.Button>
                      <Radio.Button value="auto">自动</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="主色调" name={['theme', 'primaryColor']}>
                    <ColorPicker showText />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="字体大小" name={['theme', 'fontSize']}>
                    <Slider min={12} max={18} marks={{ 12: '小', 14: '中', 16: '大', 18: '特大' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="圆角大小" name={['theme', 'borderRadius']}>
                    <Slider min={0} max={12} marks={{ 0: '直角', 6: '中等', 12: '圆润' }} />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item label="紧凑模式" name={['theme', 'compactMode']} valuePropName="checked">
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <DashboardOutlined />
                  仪表板
                </Space>
              }
              key="dashboard"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Form.Item label="布局方式" name={['dashboard', 'layout']}>
                    <Select>
                      <Option value="grid">网格布局</Option>
                      <Option value="list">列表布局</Option>
                      <Option value="masonry">瀑布流布局</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="默认页面" name={['dashboard', 'defaultPage']}>
                    <Select>
                      <Option value="/dashboard">仪表板</Option>
                      <Option value="/portfolio">投资组合</Option>
                      <Option value="/market">市场数据</Option>
                      <Option value="/charts">图表分析</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item label="显示小部件" name={['dashboard', 'widgets']}>
                    <Select mode="multiple" options={widgetOptions} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="刷新间隔(秒)" name={['dashboard', 'refreshInterval']}>
                    <InputNumber min={10} max={300} />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="显示欢迎信息" name={['dashboard', 'showWelcome']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BarChartOutlined />
                  图表设置
                </Space>
              }
              key="charts"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Form.Item label="默认图表类型" name={['charts', 'defaultType']}>
                    <Select>
                      <Option value="line">折线图</Option>
                      <Option value="candlestick">K线图</Option>
                      <Option value="bar">柱状图</Option>
                      <Option value="area">面积图</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="启用动画" name={['charts', 'animation']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="显示数据标签" name={['charts', 'showDataLabels']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="显示网格线" name={['charts', 'gridLines']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item label="配色方案" name={['charts', 'colorScheme']}>
                    <div className="space-x-2">
                      {config.charts.colorScheme.map((color, index) => (
                        <ColorPicker key={index} value={color} showText size="small" />
                      ))}
                    </div>
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <BellOutlined />
                  通知设置
                </Space>
              }
              key="notifications"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Form.Item label="邮件通知" name={['notifications', 'email']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="推送通知" name={['notifications', 'push']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="声音提醒" name={['notifications', 'sound']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="价格提醒" name={['notifications', 'priceAlerts']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="组合更新" name={['notifications', 'portfolioUpdates']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="市场新闻" name={['notifications', 'marketNews']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Divider>免打扰时间</Divider>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="启用免打扰" name={['notifications', 'quietHours', 'enabled']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="开始时间" name={['notifications', 'quietHours', 'start']}>
                    <TimePicker format="HH:mm" />
                  </Form.Item>
                </Col>
                <Col xs={24} md={8}>
                  <Form.Item label="结束时间" name={['notifications', 'quietHours', 'end']}>
                    <TimePicker format="HH:mm" />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>

            <TabPane
              tab={
                <Space>
                  <SettingOutlined />
                  数据设置
                </Space>
              }
              key="data"
            >
              <Row gutter={[24, 24]}>
                <Col xs={24} md={12}>
                  <Form.Item label="默认货币" name={['data', 'defaultCurrency']}>
                    <Select>
                      <Option value="CNY">人民币 (CNY)</Option>
                      <Option value="USD">美元 (USD)</Option>
                      <Option value="EUR">欧元 (EUR)</Option>
                      <Option value="HKD">港币 (HKD)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="数字格式" name={['data', 'numberFormat']}>
                    <Select>
                      <Option value="standard">标准格式</Option>
                      <Option value="compact">紧凑格式</Option>
                      <Option value="scientific">科学计数法</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="日期格式" name={['data', 'dateFormat']}>
                    <Select>
                      <Option value="YYYY-MM-DD">2024-08-28</Option>
                      <Option value="MM/DD/YYYY">08/28/2024</Option>
                      <Option value="DD/MM/YYYY">28/08/2024</Option>
                      <Option value="YYYY年MM月DD日">2024年08月28日</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="时区" name={['data', 'timezone']}>
                    <Select>
                      <Option value="Asia/Shanghai">北京时间</Option>
                      <Option value="America/New_York">纽约时间</Option>
                      <Option value="Europe/London">伦敦时间</Option>
                      <Option value="Asia/Tokyo">东京时间</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="自动刷新" name={['data', 'autoRefresh']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
                <Col xs={24} md={12}>
                  <Form.Item label="启用缓存" name={['data', 'cacheEnabled']} valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        </Form>
      </Card>
    </motion.div>
  );
};
