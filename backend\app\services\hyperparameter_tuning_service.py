"""
超参数优化服务

提供多种超参数优化方法，包括网格搜索、随机搜索、贝叶斯优化等
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, Ridge, Lasso
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
import warnings
warnings.filterwarnings('ignore')

# 尝试导入高级优化库
try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

try:
    from skopt import BayesSearchCV
    from skopt.space import Real, Integer, Categorical
    SKOPT_AVAILABLE = True
except ImportError:
    SKOPT_AVAILABLE = False

from app.core.logging import logger
from app.models.automl import HyperparameterTuning


class SearchSpaceDefinition:
    """搜索空间定义"""
    
    def __init__(self):
        self.search_spaces = self._define_search_spaces()
    
    def _define_search_spaces(self) -> Dict[str, Dict[str, Any]]:
        """定义各算法的搜索空间"""
        return {
            'random_forest': {
                'grid': {
                    'n_estimators': [50, 100, 200, 300],
                    'max_depth': [None, 5, 10, 15, 20],
                    'min_samples_split': [2, 5, 10],
                    'min_samples_leaf': [1, 2, 4],
                    'max_features': ['sqrt', 'log2', None]
                },
                'random': {
                    'n_estimators': [10, 500],
                    'max_depth': [1, 50],
                    'min_samples_split': [2, 20],
                    'min_samples_leaf': [1, 10],
                    'max_features': ['sqrt', 'log2', None, 0.1, 0.5, 0.9]
                },
                'bayesian': {
                    'n_estimators': Integer(10, 500),
                    'max_depth': Integer(1, 50),
                    'min_samples_split': Integer(2, 20),
                    'min_samples_leaf': Integer(1, 10),
                    'max_features': Categorical(['sqrt', 'log2', None])
                } if SKOPT_AVAILABLE else {},
                'optuna': {
                    'n_estimators': ('int', 10, 500),
                    'max_depth': ('int', 1, 50),
                    'min_samples_split': ('int', 2, 20),
                    'min_samples_leaf': ('int', 1, 10),
                    'max_features': ('categorical', ['sqrt', 'log2', None])
                }
            },
            'logistic_regression': {
                'grid': {
                    'C': [0.001, 0.01, 0.1, 1, 10, 100],
                    'penalty': ['l1', 'l2', 'elasticnet'],
                    'solver': ['liblinear', 'saga'],
                    'max_iter': [1000, 2000, 5000]
                },
                'random': {
                    'C': [0.001, 100],
                    'penalty': ['l1', 'l2', 'elasticnet'],
                    'solver': ['liblinear', 'saga'],
                    'max_iter': [500, 5000]
                },
                'bayesian': {
                    'C': Real(0.001, 100, prior='log-uniform'),
                    'penalty': Categorical(['l1', 'l2']),
                    'solver': Categorical(['liblinear', 'saga']),
                    'max_iter': Integer(500, 5000)
                } if SKOPT_AVAILABLE else {},
                'optuna': {
                    'C': ('loguniform', 0.001, 100),
                    'penalty': ('categorical', ['l1', 'l2']),
                    'solver': ('categorical', ['liblinear', 'saga']),
                    'max_iter': ('int', 500, 5000)
                }
            },
            'svm': {
                'grid': {
                    'C': [0.1, 1, 10, 100],
                    'kernel': ['linear', 'rbf', 'poly'],
                    'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1]
                },
                'random': {
                    'C': [0.01, 1000],
                    'kernel': ['linear', 'rbf', 'poly'],
                    'gamma': ['scale', 'auto', 0.0001, 10]
                },
                'bayesian': {
                    'C': Real(0.01, 1000, prior='log-uniform'),
                    'kernel': Categorical(['linear', 'rbf', 'poly']),
                    'gamma': Real(0.0001, 10, prior='log-uniform')
                } if SKOPT_AVAILABLE else {},
                'optuna': {
                    'C': ('loguniform', 0.01, 1000),
                    'kernel': ('categorical', ['linear', 'rbf', 'poly']),
                    'gamma': ('loguniform', 0.0001, 10)
                }
            },
            'knn': {
                'grid': {
                    'n_neighbors': [3, 5, 7, 9, 11, 15, 21],
                    'weights': ['uniform', 'distance'],
                    'metric': ['euclidean', 'manhattan', 'minkowski']
                },
                'random': {
                    'n_neighbors': [1, 50],
                    'weights': ['uniform', 'distance'],
                    'metric': ['euclidean', 'manhattan', 'minkowski']
                },
                'bayesian': {
                    'n_neighbors': Integer(1, 50),
                    'weights': Categorical(['uniform', 'distance']),
                    'metric': Categorical(['euclidean', 'manhattan'])
                } if SKOPT_AVAILABLE else {},
                'optuna': {
                    'n_neighbors': ('int', 1, 50),
                    'weights': ('categorical', ['uniform', 'distance']),
                    'metric': ('categorical', ['euclidean', 'manhattan'])
                }
            },
            'decision_tree': {
                'grid': {
                    'max_depth': [None, 5, 10, 15, 20, 25],
                    'min_samples_split': [2, 5, 10, 20],
                    'min_samples_leaf': [1, 2, 5, 10],
                    'criterion': ['gini', 'entropy']
                },
                'random': {
                    'max_depth': [1, 50],
                    'min_samples_split': [2, 50],
                    'min_samples_leaf': [1, 20],
                    'criterion': ['gini', 'entropy']
                },
                'bayesian': {
                    'max_depth': Integer(1, 50),
                    'min_samples_split': Integer(2, 50),
                    'min_samples_leaf': Integer(1, 20),
                    'criterion': Categorical(['gini', 'entropy'])
                } if SKOPT_AVAILABLE else {},
                'optuna': {
                    'max_depth': ('int', 1, 50),
                    'min_samples_split': ('int', 2, 50),
                    'min_samples_leaf': ('int', 1, 20),
                    'criterion': ('categorical', ['gini', 'entropy'])
                }
            }
        }
    
    def get_search_space(self, algorithm: str, method: str) -> Dict[str, Any]:
        """获取指定算法和方法的搜索空间"""
        return self.search_spaces.get(algorithm, {}).get(method, {})


class GridSearchOptimizer:
    """网格搜索优化器"""
    
    def __init__(self):
        self.search_space = SearchSpaceDefinition()
    
    def optimize(
        self,
        estimator,
        X: np.ndarray,
        y: np.ndarray,
        algorithm: str,
        cv: int = 5,
        scoring: str = 'accuracy',
        n_jobs: int = -1
    ) -> Dict[str, Any]:
        """执行网格搜索优化"""
        try:
            param_grid = self.search_space.get_search_space(algorithm, 'grid')
            
            if not param_grid:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': f'No grid search space defined for {algorithm}'
                }
            
            grid_search = GridSearchCV(
                estimator=estimator,
                param_grid=param_grid,
                cv=cv,
                scoring=scoring,
                n_jobs=n_jobs,
                return_train_score=True
            )
            
            grid_search.fit(X, y)
            
            return {
                'best_params': grid_search.best_params_,
                'best_score': grid_search.best_score_,
                'cv_results': grid_search.cv_results_,
                'best_estimator': grid_search.best_estimator_,
                'total_trials': len(grid_search.cv_results_['params'])
            }
            
        except Exception as e:
            logger.error(f"网格搜索优化失败: {e}")
            return {
                'best_params': {},
                'best_score': 0.0,
                'cv_results': {},
                'error': str(e)
            }


class RandomSearchOptimizer:
    """随机搜索优化器"""
    
    def __init__(self):
        self.search_space = SearchSpaceDefinition()
    
    def optimize(
        self,
        estimator,
        X: np.ndarray,
        y: np.ndarray,
        algorithm: str,
        n_iter: int = 100,
        cv: int = 5,
        scoring: str = 'accuracy',
        n_jobs: int = -1,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """执行随机搜索优化"""
        try:
            param_distributions = self._convert_to_distributions(
                self.search_space.get_search_space(algorithm, 'random')
            )
            
            if not param_distributions:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': f'No random search space defined for {algorithm}'
                }
            
            random_search = RandomizedSearchCV(
                estimator=estimator,
                param_distributions=param_distributions,
                n_iter=n_iter,
                cv=cv,
                scoring=scoring,
                n_jobs=n_jobs,
                random_state=random_state,
                return_train_score=True
            )
            
            random_search.fit(X, y)
            
            return {
                'best_params': random_search.best_params_,
                'best_score': random_search.best_score_,
                'cv_results': random_search.cv_results_,
                'best_estimator': random_search.best_estimator_,
                'total_trials': n_iter
            }
            
        except Exception as e:
            logger.error(f"随机搜索优化失败: {e}")
            return {
                'best_params': {},
                'best_score': 0.0,
                'cv_results': {},
                'error': str(e)
            }
    
    def _convert_to_distributions(self, param_space: Dict[str, Any]) -> Dict[str, Any]:
        """转换参数空间为分布"""
        from scipy.stats import uniform, randint
        
        distributions = {}
        
        for param, values in param_space.items():
            if isinstance(values, list):
                if len(values) == 2 and all(isinstance(v, (int, float)) for v in values):
                    # 数值范围
                    if all(isinstance(v, int) for v in values):
                        distributions[param] = randint(values[0], values[1] + 1)
                    else:
                        distributions[param] = uniform(values[0], values[1] - values[0])
                else:
                    # 离散选择
                    distributions[param] = values
            else:
                distributions[param] = values
        
        return distributions


class BayesianOptimizer:
    """贝叶斯优化器"""
    
    def __init__(self):
        self.search_space = SearchSpaceDefinition()
    
    def optimize(
        self,
        estimator,
        X: np.ndarray,
        y: np.ndarray,
        algorithm: str,
        n_iter: int = 50,
        cv: int = 5,
        scoring: str = 'accuracy',
        n_jobs: int = -1,
        random_state: int = 42
    ) -> Dict[str, Any]:
        """执行贝叶斯优化"""
        try:
            if not SKOPT_AVAILABLE:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': 'scikit-optimize not available'
                }
            
            search_spaces = self.search_space.get_search_space(algorithm, 'bayesian')
            
            if not search_spaces:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': f'No Bayesian search space defined for {algorithm}'
                }
            
            bayes_search = BayesSearchCV(
                estimator=estimator,
                search_spaces=search_spaces,
                n_iter=n_iter,
                cv=cv,
                scoring=scoring,
                n_jobs=n_jobs,
                random_state=random_state,
                return_train_score=True
            )
            
            bayes_search.fit(X, y)
            
            return {
                'best_params': bayes_search.best_params_,
                'best_score': bayes_search.best_score_,
                'cv_results': bayes_search.cv_results_,
                'best_estimator': bayes_search.best_estimator_,
                'total_trials': n_iter
            }
            
        except Exception as e:
            logger.error(f"贝叶斯优化失败: {e}")
            return {
                'best_params': {},
                'best_score': 0.0,
                'cv_results': {},
                'error': str(e)
            }


class OptunaOptimizer:
    """Optuna优化器"""
    
    def __init__(self):
        self.search_space = SearchSpaceDefinition()
    
    def optimize(
        self,
        estimator,
        X: np.ndarray,
        y: np.ndarray,
        algorithm: str,
        n_trials: int = 100,
        cv: int = 5,
        scoring: str = 'accuracy',
        timeout: int = None
    ) -> Dict[str, Any]:
        """执行Optuna优化"""
        try:
            if not OPTUNA_AVAILABLE:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': 'Optuna not available'
                }
            
            search_space = self.search_space.get_search_space(algorithm, 'optuna')
            
            if not search_space:
                return {
                    'best_params': {},
                    'best_score': 0.0,
                    'cv_results': {},
                    'error': f'No Optuna search space defined for {algorithm}'
                }
            
            def objective(trial):
                params = {}
                for param_name, param_config in search_space.items():
                    param_type, *param_args = param_config
                    
                    if param_type == 'int':
                        params[param_name] = trial.suggest_int(param_name, *param_args)
                    elif param_type == 'float':
                        params[param_name] = trial.suggest_float(param_name, *param_args)
                    elif param_type == 'uniform':
                        params[param_name] = trial.suggest_uniform(param_name, *param_args)
                    elif param_type == 'loguniform':
                        params[param_name] = trial.suggest_loguniform(param_name, *param_args)
                    elif param_type == 'categorical':
                        params[param_name] = trial.suggest_categorical(param_name, param_args[0])
                
                # 设置参数并评估
                estimator.set_params(**params)
                scores = cross_val_score(estimator, X, y, cv=cv, scoring=scoring)
                return scores.mean()
            
            study = optuna.create_study(direction='maximize')
            study.optimize(objective, n_trials=n_trials, timeout=timeout)
            
            # 获取最佳参数和分数
            best_params = study.best_params
            best_score = study.best_value
            
            # 构建试验历史
            trials_data = []
            for trial in study.trials:
                trials_data.append({
                    'number': trial.number,
                    'value': trial.value,
                    'params': trial.params,
                    'state': trial.state.name
                })
            
            return {
                'best_params': best_params,
                'best_score': best_score,
                'study': study,
                'trials_data': trials_data,
                'total_trials': len(study.trials),
                'completed_trials': len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
            }
            
        except Exception as e:
            logger.error(f"Optuna优化失败: {e}")
            return {
                'best_params': {},
                'best_score': 0.0,
                'cv_results': {},
                'error': str(e)
            }


class HyperparameterTuningService:
    """超参数调优服务"""
    
    def __init__(self):
        self.grid_optimizer = GridSearchOptimizer()
        self.random_optimizer = RandomSearchOptimizer()
        self.bayesian_optimizer = BayesianOptimizer()
        self.optuna_optimizer = OptunaOptimizer()
    
    async def tune_hyperparameters(
        self,
        experiment_id: int,
        estimator,
        X: np.ndarray,
        y: np.ndarray,
        algorithm: str,
        tuning_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """执行超参数调优"""
        try:
            start_time = datetime.utcnow()
            
            method = tuning_config.get('method', 'random_search')
            cv = tuning_config.get('cv', 5)
            scoring = tuning_config.get('scoring', 'accuracy')
            
            # 根据方法选择优化器
            if method == 'grid_search':
                result = self.grid_optimizer.optimize(
                    estimator, X, y, algorithm, cv=cv, scoring=scoring
                )
            elif method == 'random_search':
                result = self.random_optimizer.optimize(
                    estimator, X, y, algorithm,
                    n_iter=tuning_config.get('n_iter', 100),
                    cv=cv, scoring=scoring
                )
            elif method == 'bayesian':
                result = self.bayesian_optimizer.optimize(
                    estimator, X, y, algorithm,
                    n_iter=tuning_config.get('n_iter', 50),
                    cv=cv, scoring=scoring
                )
            elif method == 'optuna':
                result = self.optuna_optimizer.optimize(
                    estimator, X, y, algorithm,
                    n_trials=tuning_config.get('n_trials', 100),
                    cv=cv, scoring=scoring,
                    timeout=tuning_config.get('timeout')
                )
            else:
                raise ValueError(f"Unknown tuning method: {method}")
            
            # 检查是否有错误
            if 'error' in result:
                raise Exception(result['error'])
            
            # 计算调优时间
            tuning_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 保存调优记录
            tuning_record = HyperparameterTuning(
                experiment_id=experiment_id,
                algorithm=algorithm,
                tuning_method=method,
                tuning_config=tuning_config,
                search_space=self._get_search_space_info(algorithm, method),
                total_trials=result.get('total_trials', 0),
                completed_trials=result.get('completed_trials', result.get('total_trials', 0)),
                best_parameters=result['best_params'],
                best_score=result['best_score'],
                trial_history=result.get('trials_data', []),
                tuning_time_seconds=tuning_time,
                status="completed"
            )
            
            db.add(tuning_record)
            await db.commit()
            
            return {
                'success': True,
                'tuning_id': tuning_record.id,
                'best_params': result['best_params'],
                'best_score': result['best_score'],
                'total_trials': result.get('total_trials', 0),
                'tuning_time': tuning_time,
                'best_estimator': result.get('best_estimator'),
                'method': method
            }
            
        except Exception as e:
            logger.error(f"超参数调优失败: {e}")
            
            # 保存失败记录
            try:
                tuning_record = HyperparameterTuning(
                    experiment_id=experiment_id,
                    algorithm=algorithm,
                    tuning_method=tuning_config.get('method', 'unknown'),
                    tuning_config=tuning_config,
                    status="failed"
                )
                db.add(tuning_record)
                await db.commit()
            except:
                pass
            
            return {
                'success': False,
                'error': str(e),
                'best_params': {},
                'best_score': 0.0
            }
    
    def _get_search_space_info(self, algorithm: str, method: str) -> Dict[str, Any]:
        """获取搜索空间信息"""
        try:
            search_space = SearchSpaceDefinition()
            space = search_space.get_search_space(algorithm, method)
            
            # 转换为可序列化的格式
            serializable_space = {}
            for key, value in space.items():
                if hasattr(value, '__dict__'):
                    # 对于scikit-optimize的对象
                    serializable_space[key] = str(value)
                else:
                    serializable_space[key] = value
            
            return serializable_space
            
        except Exception as e:
            logger.error(f"获取搜索空间信息失败: {e}")
            return {}
    
    def get_default_tuning_config(self, algorithm: str, problem_type: str) -> Dict[str, Any]:
        """获取默认调优配置"""
        base_config = {
            'method': 'random_search',
            'cv': 5,
            'n_iter': 100,
            'scoring': 'accuracy' if problem_type == 'classification' else 'r2',
            'n_jobs': -1,
            'random_state': 42
        }
        
        # 根据算法调整配置
        if algorithm in ['random_forest', 'decision_tree']:
            base_config['n_iter'] = 50  # 树模型训练较快，可以减少迭代次数
        elif algorithm in ['svm']:
            base_config['n_iter'] = 30  # SVM训练较慢，减少迭代次数
            base_config['method'] = 'bayesian'  # 使用贝叶斯优化提高效率
        
        return base_config


# 全局超参数调优服务实例
hyperparameter_tuning_service = HyperparameterTuningService()
