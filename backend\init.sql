-- JQData量化平台数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建数据库（如果不存在）
-- 注意：在docker-entrypoint-initdb.d中，数据库已经由环境变量创建

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户（如果需要额外的用户）
-- CREATE USER jqdata_app WITH PASSWORD 'jqdata_app_password';
-- GRANT ALL PRIVILEGES ON DATABASE jqdata TO jqdata_app;

-- 输出初始化完成信息
SELECT 'JQData数据库初始化完成' as message;
