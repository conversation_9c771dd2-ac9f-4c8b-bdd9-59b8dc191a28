'use client';

/**
 * 3D高级图表组件
 * 
 * 提供3D可视化图表类型
 */

import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-gl'; // 3D图表支持
import { Card, Typography, Space, Tag } from 'antd';

const { Title, Text } = Typography;

// 3D散点图组件
export const Scatter3DChart: React.FC<{
  data: Array<{
    name: string;
    value: [number, number, number];
    category?: string;
  }>;
  xAxisName?: string;
  yAxisName?: string;
  zAxisName?: string;
  title?: string;
  height?: number;
}> = ({ 
  data, 
  xAxisName = 'X轴',
  yAxisName = 'Y轴', 
  zAxisName = 'Z轴',
  title = "3D散点图", 
  height = 500 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          formatter: function (params: any) {
            const data = params.data;
            return `${data.name}<br/>
                    ${xAxisName}: ${data.value[0]}<br/>
                    ${yAxisName}: ${data.value[1]}<br/>
                    ${zAxisName}: ${data.value[2]}`;
          }
        },
        xAxis3D: {
          name: xAxisName,
          type: 'value'
        },
        yAxis3D: {
          name: yAxisName,
          type: 'value'
        },
        zAxis3D: {
          name: zAxisName,
          type: 'value'
        },
        grid3D: {
          boxWidth: 200,
          boxDepth: 80,
          boxHeight: 120,
          viewControl: {
            projection: 'perspective',
            autoRotate: true,
            autoRotateSpeed: 5
          },
          light: {
            main: {
              intensity: 1.2,
              shadow: true
            },
            ambient: {
              intensity: 0.3
            }
          }
        },
        series: [{
          name: '3D散点',
          type: 'scatter3D',
          data: data.map(item => ({
            name: item.name,
            value: item.value,
            itemStyle: {
              color: item.category === 'positive' ? '#52c41a' : 
                     item.category === 'negative' ? '#ff4d4f' : '#1890ff'
            }
          })),
          symbolSize: 12,
          emphasis: {
            itemStyle: {
              color: '#faad14'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title, xAxisName, yAxisName, zAxisName]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 3D柱状图组件
export const Bar3DChart: React.FC<{
  data: Array<{
    name: string;
    value: number;
    category: string;
  }>;
  categories: string[];
  title?: string;
  height?: number;
}> = ({ 
  data, 
  categories,
  title = "3D柱状图", 
  height = 500 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      // 转换数据格式
      const seriesData: Array<[number, number, number]> = [];
      const names = [...new Set(data.map(item => item.name))];
      
      data.forEach(item => {
        const xIndex = names.indexOf(item.name);
        const yIndex = categories.indexOf(item.category);
        seriesData.push([xIndex, yIndex, item.value]);
      });
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          formatter: function (params: any) {
            const data = params.data;
            return `${names[data.value[0]]}<br/>
                    ${categories[data.value[1]]}<br/>
                    数值: ${data.value[2]}`;
          }
        },
        xAxis3D: {
          type: 'category',
          data: names,
          name: '项目'
        },
        yAxis3D: {
          type: 'category',
          data: categories,
          name: '类别'
        },
        zAxis3D: {
          type: 'value',
          name: '数值'
        },
        grid3D: {
          boxWidth: 200,
          boxDepth: 80,
          boxHeight: 120,
          viewControl: {
            projection: 'perspective',
            autoRotate: false
          },
          light: {
            main: {
              intensity: 1.2,
              shadow: true
            },
            ambient: {
              intensity: 0.3
            }
          }
        },
        series: [{
          type: 'bar3D',
          data: seriesData,
          shading: 'lambert',
          itemStyle: {
            opacity: 0.8
          },
          emphasis: {
            itemStyle: {
              color: '#faad14'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, categories, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 3D曲面图组件
export const Surface3DChart: React.FC<{
  data: number[][];
  xAxisData: string[];
  yAxisData: string[];
  title?: string;
  height?: number;
}> = ({ 
  data, 
  xAxisData,
  yAxisData,
  title = "3D曲面图", 
  height = 500 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {},
        xAxis3D: {
          type: 'category',
          data: xAxisData
        },
        yAxis3D: {
          type: 'category',
          data: yAxisData
        },
        zAxis3D: {
          type: 'value'
        },
        grid3D: {
          boxWidth: 200,
          boxDepth: 80,
          boxHeight: 120,
          viewControl: {
            projection: 'perspective',
            autoRotate: true,
            autoRotateSpeed: 10
          },
          light: {
            main: {
              intensity: 1.2,
              shadow: true
            },
            ambient: {
              intensity: 0.3
            }
          }
        },
        series: [{
          type: 'surface',
          data: data,
          shading: 'color',
          itemStyle: {
            opacity: 0.8
          },
          wireframe: {
            show: false
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, xAxisData, yAxisData, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 地理3D图组件
export const Geo3DChart: React.FC<{
  data: Array<{
    name: string;
    value: [number, number, number]; // [经度, 纬度, 数值]
  }>;
  title?: string;
  height?: number;
}> = ({ 
  data, 
  title = "地理3D图", 
  height = 500 
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      
      const option: echarts.EChartsOption = {
        title: {
          text: title,
          left: 'center'
        },
        tooltip: {
          formatter: function (params: any) {
            const data = params.data;
            return `${data.name}<br/>数值: ${data.value[2]}`;
          }
        },
        geo3D: {
          map: 'world',
          shading: 'realistic',
          realisticMaterial: {
            roughness: 0.8,
            metalness: 0
          },
          postEffect: {
            enable: true
          },
          groundPlane: {
            show: false
          },
          light: {
            main: {
              intensity: 1,
              shadow: true,
              shadowQuality: 'high',
              alpha: 30
            },
            ambient: {
              intensity: 0.4
            }
          },
          viewControl: {
            autoRotate: true,
            autoRotateSpeed: 5
          }
        },
        series: [{
          type: 'scatter3D',
          coordinateSystem: 'geo3D',
          data: data,
          symbolSize: function (val: number[]) {
            return Math.sqrt(val[2]) * 2;
          },
          itemStyle: {
            color: '#1890ff',
            opacity: 0.8
          },
          emphasis: {
            itemStyle: {
              color: '#faad14'
            }
          }
        }]
      };

      chartInstance.current.setOption(option);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [data, title]);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: `${height}px` }}
    />
  );
};

// 组合3D图表展示组件
export const Combined3DCharts: React.FC<{
  scatterData?: any[];
  barData?: any[];
  surfaceData?: number[][];
  geoData?: any[];
}> = ({ 
  scatterData = [],
  barData = [],
  surfaceData = [],
  geoData = []
}) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {scatterData.length > 0 && (
          <Card title="3D散点图 - 风险收益分析">
            <Scatter3DChart
              data={scatterData}
              xAxisName="风险"
              yAxisName="收益"
              zAxisName="夏普比率"
              height={400}
            />
          </Card>
        )}
        
        {barData.length > 0 && (
          <Card title="3D柱状图 - 行业表现">
            <Bar3DChart
              data={barData}
              categories={['Q1', 'Q2', 'Q3', 'Q4']}
              height={400}
            />
          </Card>
        )}
      </div>
      
      {surfaceData.length > 0 && (
        <Card title="3D曲面图 - 波动率曲面">
          <Surface3DChart
            data={surfaceData}
            xAxisData={['1M', '3M', '6M', '1Y', '2Y', '3Y']}
            yAxisData={['0.1', '0.2', '0.3', '0.4', '0.5']}
            height={500}
          />
        </Card>
      )}
      
      {geoData.length > 0 && (
        <Card title="地理3D图 - 全球市场分布">
          <Geo3DChart
            data={geoData}
            height={500}
          />
        </Card>
      )}
    </div>
  );
};
