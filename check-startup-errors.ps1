# ============================================================================
# Smart Quantitative Trading Platform - Startup Error Checker
# ============================================================================

Write-Host "Checking Smart Quantitative Trading Platform startup errors..." -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Cyan

# Function to check if port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Function to check service status
function Check-ServiceStatus {
    param([string]$ServiceName, [int]$Port, [string]$URL)
    
    Write-Host "Checking $ServiceName..." -ForegroundColor Yellow
    
    if (Test-Port -Port $Port) {
        Write-Host "  Port $Port is in use" -ForegroundColor Green
        
        try {
            $response = Invoke-WebRequest -Uri $URL -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Host "  $ServiceName is responding correctly" -ForegroundColor Green
                Write-Host "  Status: $($response.StatusCode)" -ForegroundColor Green
                return $true
            } else {
                Write-Host "  $ServiceName returned status: $($response.StatusCode)" -ForegroundColor Yellow
                return $false
            }
        } catch {
            Write-Host "  $ServiceName is not responding: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "  Port $Port is not in use - $ServiceName is not running" -ForegroundColor Red
        return $false
    }
}

# Check system requirements
Write-Host "1. Checking System Requirements..." -ForegroundColor Cyan

# Check Python
if (Get-Command python -ErrorAction SilentlyContinue) {
    $pythonVersion = python --version
    Write-Host "  Python: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "  Python: NOT FOUND" -ForegroundColor Red
    Write-Host "    Please install Python 3.8+ from https://python.org" -ForegroundColor Yellow
}

# Check Node.js
if (Get-Command node -ErrorAction SilentlyContinue) {
    $nodeVersion = node --version
    Write-Host "  Node.js: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "  Node.js: NOT FOUND" -ForegroundColor Red
    Write-Host "    Please install Node.js 18+ from https://nodejs.org" -ForegroundColor Yellow
}

# Check npm
if (Get-Command npm -ErrorAction SilentlyContinue) {
    $npmVersion = npm --version
    Write-Host "  npm: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "  npm: NOT FOUND" -ForegroundColor Red
}

Write-Host ""

# Check project structure
Write-Host "2. Checking Project Structure..." -ForegroundColor Cyan

if (Test-Path "backend") {
    Write-Host "  backend/ directory: EXISTS" -ForegroundColor Green
    
    if (Test-Path "backend/app") {
        Write-Host "  backend/app/ directory: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  backend/app/ directory: MISSING" -ForegroundColor Red
    }
    
    if (Test-Path "backend/requirements.txt") {
        Write-Host "  backend/requirements.txt: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  backend/requirements.txt: MISSING" -ForegroundColor Yellow
    }
    
    if (Test-Path "backend/.env") {
        Write-Host "  backend/.env: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  backend/.env: MISSING" -ForegroundColor Yellow
        Write-Host "    Create .env file with required environment variables" -ForegroundColor Yellow
    }
} else {
    Write-Host "  backend/ directory: MISSING" -ForegroundColor Red
}

if (Test-Path "frontend") {
    Write-Host "  frontend/ directory: EXISTS" -ForegroundColor Green
    
    if (Test-Path "frontend/package.json") {
        Write-Host "  frontend/package.json: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  frontend/package.json: MISSING" -ForegroundColor Red
    }
    
    if (Test-Path "frontend/node_modules") {
        Write-Host "  frontend/node_modules/: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "  frontend/node_modules/: MISSING" -ForegroundColor Yellow
        Write-Host "    Run 'npm install' in frontend directory" -ForegroundColor Yellow
    }
} else {
    Write-Host "  frontend/ directory: MISSING" -ForegroundColor Red
}

Write-Host ""

# Check services
Write-Host "3. Checking Service Status..." -ForegroundColor Cyan

$backendRunning = Check-ServiceStatus -ServiceName "Backend API" -Port 8000 -URL "http://localhost:8000/"
$frontendRunning = Check-ServiceStatus -ServiceName "Frontend App" -Port 3000 -URL "http://localhost:3000/"

Write-Host ""

# Summary
Write-Host "4. Summary..." -ForegroundColor Cyan

if ($backendRunning -and $frontendRunning) {
    Write-Host "  Status: ALL SERVICES RUNNING" -ForegroundColor Green
    Write-Host "  Frontend: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "  Backend API: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "  API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
} elseif ($backendRunning) {
    Write-Host "  Status: BACKEND ONLY" -ForegroundColor Yellow
    Write-Host "  Backend API: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "  Frontend: NOT RUNNING" -ForegroundColor Red
} elseif ($frontendRunning) {
    Write-Host "  Status: FRONTEND ONLY" -ForegroundColor Yellow
    Write-Host "  Frontend: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "  Backend API: NOT RUNNING" -ForegroundColor Red
} else {
    Write-Host "  Status: NO SERVICES RUNNING" -ForegroundColor Red
    Write-Host "  Run start-all.ps1 to start both services" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Check completed!" -ForegroundColor Green
