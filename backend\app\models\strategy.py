"""
策略相关数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import Base


class Strategy(Base):
    """策略模型"""
    __tablename__ = "strategies"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    strategy_type = Column(String(50), nullable=False)  # 策略类型：trend, mean_reversion, arbitrage等
    status = Column(String(20), default="draft")  # draft, active, paused, stopped
    
    # 策略配置
    config = Column(JSON)  # 策略参数配置
    code = Column(Text)  # 策略代码
    version = Column(String(20), default="1.0.0")
    
    # 性能指标
    total_return = Column(Float, default=0.0)
    annual_return = Column(Float, default=0.0)
    sharpe_ratio = Column(Float, default=0.0)
    max_drawdown = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    
    # 风险控制
    max_position = Column(Float, default=1.0)  # 最大仓位
    stop_loss = Column(Float)  # 止损比例
    take_profit = Column(Float)  # 止盈比例
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_run_at = Column(DateTime)
    
    # 关系
    user = relationship("User", back_populates="strategies")
    nodes = relationship("StrategyNode", back_populates="strategy", cascade="all, delete-orphan")
    backtests = relationship("StrategyBacktest", back_populates="strategy", cascade="all, delete-orphan")


class StrategyNode(Base):
    """策略节点模型（用于可视化策略构建）"""
    __tablename__ = "strategy_nodes"

    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, index=True)
    node_id = Column(String(50), nullable=False)  # 前端节点ID
    node_type = Column(String(50), nullable=False)  # 节点类型：data, indicator, signal, filter等
    
    # 节点配置
    config = Column(JSON)  # 节点参数配置
    position = Column(JSON)  # 节点在画布中的位置 {x: number, y: number}
    
    # 连接信息
    inputs = Column(JSON)  # 输入连接 [{nodeId: string, output: string}]
    outputs = Column(JSON)  # 输出定义 [{name: string, type: string}]
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    strategy = relationship("Strategy", back_populates="nodes")


class StrategyBacktest(Base):
    """策略回测模型"""
    __tablename__ = "strategy_backtests"

    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    
    # 回测配置
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_capital = Column(Float, default=100000.0)
    benchmark = Column(String(20), default="000300.XSHG")  # 基准指数
    
    # 回测结果
    status = Column(String(20), default="pending")  # pending, running, completed, failed
    progress = Column(Integer, default=0)  # 进度百分比
    
    # 性能指标
    total_return = Column(Float)
    annual_return = Column(Float)
    benchmark_return = Column(Float)
    alpha = Column(Float)
    beta = Column(Float)
    sharpe_ratio = Column(Float)
    sortino_ratio = Column(Float)
    max_drawdown = Column(Float)
    max_drawdown_duration = Column(Integer)  # 最大回撤持续天数
    
    # 交易统计
    total_trades = Column(Integer, default=0)
    win_trades = Column(Integer, default=0)
    lose_trades = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    avg_win = Column(Float, default=0.0)
    avg_loss = Column(Float, default=0.0)
    profit_loss_ratio = Column(Float, default=0.0)
    
    # 详细结果
    equity_curve = Column(JSON)  # 净值曲线数据
    positions = Column(JSON)  # 持仓记录
    trades = Column(JSON)  # 交易记录
    daily_returns = Column(JSON)  # 日收益率
    
    # 错误信息
    error_message = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 关系
    strategy = relationship("Strategy", back_populates="backtests")
