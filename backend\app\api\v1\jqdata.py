"""
JQData配置相关API端点

包含JQData账号配置、配额管理、连接测试等功能
"""

from datetime import datetime
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import JQDataConfig, User
from app.schemas.base import BaseResponse
from app.schemas.jqdata import (
    JQDataConfigRequest,
    JQDataConfigResponse,
    JQDataQuotaResponse,
    JQDataTestConnectionResponse,
    JQDataConfigUpdateRequest,
    JQDataUsageStatsResponse,
)
from app.services.jqdata_service import JQDataService, JQDataError, AuthenticationError

router = APIRouter()

# JQData服务实例
jqdata_service = JQDataService()


# =============================================================================
# JQData配置管理
# =============================================================================

@router.post("/config", response_model=BaseResponse[JQDataConfigResponse])
async def save_jqdata_config(
    config_data: JQDataConfigRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """保存JQData配置"""
    try:
        # 验证JQData账号有效性
        try:
            import jqdatasdk as jq
            jq.auth(config_data.username, config_data.password)
            jq.logout()  # 验证后立即登出
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"JQData账号验证失败: {str(e)}"
            )
        
        # 加密密码
        encrypted_password = jqdata_service._encrypt_password(config_data.password)
        
        # 检查是否已存在配置
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
        )
        existing_config = result.scalar_one_or_none()
        
        if existing_config:
            # 更新现有配置
            existing_config.username = config_data.username
            existing_config.encrypted_password = encrypted_password
            existing_config.is_active = True
            existing_config.auth_failure_count = 0
            existing_config.last_auth_error = None
            existing_config.updated_at = datetime.utcnow()
            
            await db.commit()
            await db.refresh(existing_config)
            
            logger.info(f"用户 {current_user.id} 更新JQData配置成功")
            
            return BaseResponse(
                code=200,
                message="JQData配置更新成功",
                data=JQDataConfigResponse.from_orm(existing_config)
            )
        else:
            # 创建新配置
            new_config = JQDataConfig(
                user_id=current_user.id,
                username=config_data.username,
                encrypted_password=encrypted_password,
                is_active=True,
                quota_total=10000,  # 默认配额
                quota_used=0,
                total_api_calls=0,
                auth_failure_count=0
            )
            
            db.add(new_config)
            await db.commit()
            await db.refresh(new_config)
            
            logger.info(f"用户 {current_user.id} 创建JQData配置成功")
            
            return BaseResponse(
                code=200,
                message="JQData配置保存成功",
                data=JQDataConfigResponse.from_orm(new_config)
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存JQData配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存配置失败，请稍后重试"
        )


@router.get("/config", response_model=BaseResponse[JQDataConfigResponse])
async def get_jqdata_config(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取JQData配置"""
    try:
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="JQData配置不存在，请先配置"
            )
        
        return BaseResponse(
            code=200,
            message="获取JQData配置成功",
            data=JQDataConfigResponse.from_orm(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取JQData配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )


@router.put("/config", response_model=BaseResponse[JQDataConfigResponse])
async def update_jqdata_config(
    config_data: JQDataConfigUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """更新JQData配置"""
    try:
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="JQData配置不存在"
            )
        
        # 更新字段
        update_data = {}
        
        if config_data.username is not None:
            update_data['username'] = config_data.username
        
        if config_data.password is not None:
            # 验证新密码
            try:
                import jqdatasdk as jq
                username = config_data.username or config.username
                jq.auth(username, config_data.password)
                jq.logout()
                
                update_data['encrypted_password'] = jqdata_service._encrypt_password(config_data.password)
                update_data['auth_failure_count'] = 0
                update_data['last_auth_error'] = None
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"JQData密码验证失败: {str(e)}"
                )
        
        if config_data.is_active is not None:
            update_data['is_active'] = config_data.is_active
        
        if update_data:
            update_data['updated_at'] = datetime.utcnow()
            
            await db.execute(
                update(JQDataConfig)
                .where(JQDataConfig.id == config.id)
                .values(**update_data)
            )
            await db.commit()
            await db.refresh(config)
        
        logger.info(f"用户 {current_user.id} 更新JQData配置成功")
        
        return BaseResponse(
            code=200,
            message="JQData配置更新成功",
            data=JQDataConfigResponse.from_orm(config)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新JQData配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )


@router.delete("/config", response_model=BaseResponse[Dict[str, Any]])
async def delete_jqdata_config(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """删除JQData配置"""
    try:
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="JQData配置不存在"
            )
        
        await db.delete(config)
        await db.commit()
        
        # 清理认证缓存
        await jqdata_service.logout_user(current_user.id)
        
        logger.info(f"用户 {current_user.id} 删除JQData配置成功")
        
        return BaseResponse(
            code=200,
            message="JQData配置删除成功",
            data={}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除JQData配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除配置失败"
        )


# =============================================================================
# 连接测试和配额管理
# =============================================================================

@router.post("/test-connection", response_model=BaseResponse[JQDataTestConnectionResponse])
async def test_jqdata_connection(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """测试JQData连接"""
    try:
        import time
        start_time = time.time()
        
        # 尝试认证
        success = await jqdata_service.authenticate_user(current_user.id, db)
        
        response_time = time.time() - start_time
        
        if success:
            # 获取配额信息
            quota_info = await jqdata_service.get_quota_info(current_user.id, db)
            
            return BaseResponse(
                code=200,
                message="JQData连接测试成功",
                data=JQDataTestConnectionResponse(
                    success=True,
                    message="连接正常，认证成功",
                    quota_info=quota_info,
                    response_time=round(response_time, 3)
                )
            )
        else:
            return BaseResponse(
                code=400,
                message="JQData连接测试失败",
                data=JQDataTestConnectionResponse(
                    success=False,
                    message="连接失败，请检查配置",
                    response_time=round(response_time, 3)
                )
            )
            
    except AuthenticationError as e:
        return BaseResponse(
            code=400,
            message="JQData连接测试失败",
            data=JQDataTestConnectionResponse(
                success=False,
                message="认证失败",
                error_details=str(e)
            )
        )
    except Exception as e:
        logger.error(f"JQData连接测试失败: {e}")
        return BaseResponse(
            code=500,
            message="连接测试失败",
            data=JQDataTestConnectionResponse(
                success=False,
                message="测试过程中发生错误",
                error_details=str(e)
            )
        )


@router.get("/quota", response_model=BaseResponse[JQDataQuotaResponse])
async def get_jqdata_quota(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取JQData配额信息"""
    try:
        quota_info = await jqdata_service.get_quota_info(current_user.id, db)
        
        return BaseResponse(
            code=200,
            message="获取配额信息成功",
            data=JQDataQuotaResponse(**quota_info)
        )
        
    except Exception as e:
        logger.error(f"获取JQData配额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配额信息失败"
        )


@router.post("/quota/reset", response_model=BaseResponse[Dict[str, Any]])
async def reset_jqdata_quota(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """重置JQData配额（仅管理员）"""
    try:
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，仅管理员可操作"
            )
        
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="JQData配置不存在"
            )
        
        # 重置配额
        await db.execute(
            update(JQDataConfig)
            .where(JQDataConfig.id == config.id)
            .values(
                quota_used=0,
                quota_reset_date=datetime.utcnow().date(),
                updated_at=datetime.utcnow()
            )
        )
        await db.commit()
        
        logger.info(f"管理员 {current_user.id} 重置JQData配额成功")
        
        return BaseResponse(
            code=200,
            message="配额重置成功",
            data={}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置JQData配额失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置配额失败"
        )


@router.get("/usage-stats", response_model=BaseResponse[JQDataUsageStatsResponse])
async def get_jqdata_usage_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取JQData使用统计"""
    try:
        # 这里应该从数据库查询实际的使用统计
        # 暂时返回模拟数据
        stats = JQDataUsageStatsResponse(
            today_calls=0,
            week_calls=0,
            month_calls=0,
            total_calls=0,
            avg_daily_calls=0.0,
            quota_usage_trend=[],
            most_used_apis=[],
            peak_usage_hours=[]
        )
        
        return BaseResponse(
            code=200,
            message="获取使用统计成功",
            data=stats
        )
        
    except Exception as e:
        logger.error(f"获取JQData使用统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取使用统计失败"
        )
