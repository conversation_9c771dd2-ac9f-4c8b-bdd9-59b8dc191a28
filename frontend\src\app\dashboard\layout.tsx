'use client';

/**
 * 仪表板布局组件
 * 
 * 提供统一的仪表板布局，包含侧边栏、顶部导航、面包屑等
 */

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Button,
  Badge,
  Space,
  Typography,
  Breadcrumb,
  theme,
  Drawer,
  Grid,
} from 'antd';
import {
  DashboardOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  ProfileOutlined,
  ApiOutlined,
  BarChartOutlined,
  FundOutlined,
  RobotOutlined,
  BookOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth, useAuthActions } from '@/store/auth';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;
const { useBreakpoint } = Grid;

interface MenuItem {
  key: string;
  icon: React.ReactNode;
  label: string;
  path: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    key: 'overview',
    icon: <DashboardOutlined />,
    label: '概览',
    path: '/dashboard/overview',
  },
  {
    key: 'market',
    icon: <LineChartOutlined />,
    label: '市场数据',
    path: '/dashboard/market',
    children: [
      {
        key: 'market-overview',
        icon: <BarChartOutlined />,
        label: '市场概览',
        path: '/dashboard/market/overview',
      },
      {
        key: 'stock-list',
        icon: <DatabaseOutlined />,
        label: '股票列表',
        path: '/dashboard/market/stocks',
      },
      {
        key: 'charts',
        icon: <LineChartOutlined />,
        label: '图表分析',
        path: '/dashboard/market/charts',
      },
    ],
  },
  {
    key: 'portfolio',
    icon: <FundOutlined />,
    label: '投资组合',
    path: '/dashboard/portfolio',
    children: [
      {
        key: 'portfolio-overview',
        icon: <DashboardOutlined />,
        label: '组合概览',
        path: '/dashboard/portfolio/overview',
      },
      {
        key: 'positions',
        icon: <DatabaseOutlined />,
        label: '持仓管理',
        path: '/dashboard/portfolio/positions',
      },
      {
        key: 'performance',
        icon: <BarChartOutlined />,
        label: '业绩分析',
        path: '/dashboard/portfolio/performance',
      },
    ],
  },
  {
    key: 'strategy',
    icon: <RobotOutlined />,
    label: '策略中心',
    path: '/dashboard/strategy',
    children: [
      {
        key: 'strategy-list',
        icon: <BookOutlined />,
        label: '策略列表',
        path: '/dashboard/strategy/list',
      },
      {
        key: 'strategy-editor',
        icon: <SettingOutlined />,
        label: '策略编辑器',
        path: '/dashboard/strategy/editor',
      },
      {
        key: 'backtest',
        icon: <LineChartOutlined />,
        label: '回测分析',
        path: '/dashboard/strategy/backtest',
      },
    ],
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '设置',
    path: '/dashboard/settings',
    children: [
      {
        key: 'jqdata-config',
        icon: <ApiOutlined />,
        label: 'JQData配置',
        path: '/dashboard/settings/jqdata',
      },
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人资料',
        path: '/dashboard/settings/profile',
      },
      {
        key: 'preferences',
        icon: <SettingOutlined />,
        label: '偏好设置',
        path: '/dashboard/settings/preferences',
      },
    ],
  },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user } = useAuth();
  const { logout } = useAuthActions();
  const screens = useBreakpoint();
  
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const isMobile = !screens.md;

  // 根据当前路径设置选中的菜单项
  useEffect(() => {
    const currentPath = pathname;
    const findSelectedKey = (items: MenuItem[]): string | null => {
      for (const item of items) {
        if (item.path === currentPath) {
          return item.key;
        }
        if (item.children) {
          const childKey = findSelectedKey(item.children);
          if (childKey) {
            setOpenKeys(prev => [...new Set([...prev, item.key])]);
            return childKey;
          }
        }
      }
      return null;
    };

    const selectedKey = findSelectedKey(menuItems);
    if (selectedKey) {
      setSelectedKeys([selectedKey]);
    }
  }, [pathname]);

  // 生成面包屑
  const generateBreadcrumb = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbItems = [
      {
        title: '首页',
        href: '/dashboard',
      },
    ];

    let currentPath = '';
    for (const segment of pathSegments.slice(1)) { // 跳过 'dashboard'
      currentPath += `/${segment}`;
      const fullPath = `/dashboard${currentPath}`;
      
      // 查找对应的菜单项
      const findMenuItem = (items: MenuItem[]): MenuItem | null => {
        for (const item of items) {
          if (item.path === fullPath) {
            return item;
          }
          if (item.children) {
            const child = findMenuItem(item.children);
            if (child) return child;
          }
        }
        return null;
      };

      const menuItem = findMenuItem(menuItems);
      if (menuItem) {
        breadcrumbItems.push({
          title: menuItem.label,
          href: menuItem.path,
        });
      }
    }

    return breadcrumbItems;
  };

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const findMenuItem = (items: MenuItem[]): MenuItem | null => {
      for (const item of items) {
        if (item.key === key) {
          return item;
        }
        if (item.children) {
          const child = findMenuItem(item.children);
          if (child) return child;
        }
      }
      return null;
    };

    const menuItem = findMenuItem(menuItems);
    if (menuItem) {
      router.push(menuItem.path);
      if (isMobile) {
        setMobileDrawerOpen(false);
      }
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
      onClick: () => router.push('/dashboard/settings/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => router.push('/dashboard/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      onClick: () => window.open('/help', '_blank'),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  // 侧边栏内容
  const sidebarContent = (
    <div className="h-full flex flex-col">
      {/* Logo */}
      <div className="h-16 flex items-center justify-center border-b border-gray-200">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="flex items-center space-x-2"
        >
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <LineChartOutlined className="text-white text-lg" />
          </div>
          {!collapsed && (
            <Text strong className="text-lg">
              JQData
            </Text>
          )}
        </motion.div>
      </div>

      {/* 菜单 */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onOpenChange={setOpenKeys}
          onClick={handleMenuClick}
          className="border-none"
          items={menuItems.map(item => ({
            key: item.key,
            icon: item.icon,
            label: item.label,
            children: item.children?.map(child => ({
              key: child.key,
              icon: child.icon,
              label: child.label,
            })),
          }))}
        />
      </div>
    </div>
  );

  return (
    <Layout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={256}
          className="shadow-lg"
          style={{
            background: colorBgContainer,
          }}
        >
          {sidebarContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title="导航菜单"
          placement="left"
          onClose={() => setMobileDrawerOpen(false)}
          open={mobileDrawerOpen}
          bodyStyle={{ padding: 0 }}
          width={256}
        >
          {sidebarContent}
        </Drawer>
      )}

      <Layout>
        {/* 顶部导航 */}
        <Header
          style={{
            padding: '0 24px',
            background: colorBgContainer,
            borderBottom: '1px solid #f0f0f0',
          }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => {
                if (isMobile) {
                  setMobileDrawerOpen(true);
                } else {
                  setCollapsed(!collapsed);
                }
              }}
              className="text-lg"
            />

            {/* 面包屑 */}
            <Breadcrumb
              items={generateBreadcrumb()}
              className="hidden sm:block"
            />
          </div>

          <Space size="middle">
            {/* 通知 */}
            <Badge count={5} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-lg"
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded-lg transition-colors">
                <Avatar
                  size="small"
                  src={user?.avatarUrl}
                  icon={<UserOutlined />}
                />
                <div className="hidden sm:block">
                  <Text strong className="text-sm">
                    {user?.fullName || user?.username}
                  </Text>
                  <div className="text-xs text-gray-500">
                    {user?.subscriptionType}
                  </div>
                </div>
              </div>
            </Dropdown>
          </Space>
        </Header>

        {/* 主内容区 */}
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            minHeight: 'calc(100vh - 112px)',
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          <div key={pathname}>
            {children}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
