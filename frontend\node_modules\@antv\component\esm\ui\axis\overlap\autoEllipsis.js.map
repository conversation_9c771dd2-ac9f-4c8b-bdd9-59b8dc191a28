{"version": 3, "file": "autoEllipsis.js", "sourceRoot": "", "sources": ["../../../../src/ui/axis/overlap/autoEllipsis.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAGnC,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAE1D,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAO1C,SAAS,iBAAiB,CAAC,GAAoB,EAAE,IAAS;IAAT,qBAAA,EAAA,SAAS;IACxD,IAAI,KAAK,CAAC,GAAG,CAAC;QAAE,OAAO,CAAC,CAAC;IACzB,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,OAAO,GAAG,CAAC;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACjD,CAAC;AAED,MAAM,CAAC,OAAO,UAAU,aAAa,CACnC,MAAuB,EACvB,UAA8B,EAC9B,IAAoB,EACpB,KAAY;IAEZ,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC;QAAE,OAAO;IAE7B,IAAA,KAKE,UAAU,OALE,EAAd,MAAM,mBAAG,KAAK,KAAA,EACd,SAAS,GAIP,UAAU,UAJH,EACT,KAGE,UAAU,UAHQ,EAApB,SAAS,mBAAG,QAAQ,KAAA,EACpB,KAEE,UAAU,KAFY,EAAlB,YAAY,mBAAG,GAAG,KAAA,EACxB,KACE,UAAU,OADS,EAArB,MAAM,mBAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAA,CACR;IAEf,IAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,IAAM,IAAI,GAAG,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACnD,IAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClE,IAAI,GAAG,GAAG,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7C,yCAAyC;IACzC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,QAAQ,EAAE,CAAC;QACnC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAClB,IAAI,EACJ,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,OAAO,EAAE,CAAC,KAAK,EAAjB,CAAiB,CAAC,CACrC,CAAC;IACJ,CAAC;IACD,yGAAyG;IACzG,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,IAAA,KAAA,OAAmD,MAAkB,IAAA,EAApE,UAAO,EAAP,GAAG,mBAAG,CAAC,KAAA,EAAE,UAAS,EAAT,KAAK,mBAAG,CAAC,KAAA,EAAE,UAAY,EAAZ,MAAM,mBAAG,GAAG,KAAA,EAAE,UAAY,EAAZ,IAAI,mBAAG,KAAK,KAAsB,CAAC;4BACnE,aAAa;QACpB,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;YACnB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACzC,OAAO;QACP,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;qCAAS;;IAPhC,KAAK,IAAI,aAAa,GAAG,GAAG,EAAE,aAAa,GAAG,GAAG,GAAG,IAAI,EAAE,aAAa,IAAI,IAAI;8BAAtE,aAAa;;;KAQrB;AACH,CAAC"}