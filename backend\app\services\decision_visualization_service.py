"""
决策可视化服务

提供决策树可视化、决策边界、决策路径等可视化功能
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
import warnings
warnings.filterwarnings('ignore')

from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor, export_text
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, r2_score

try:
    import graphviz
    GRAPHVIZ_AVAILABLE = True
except ImportError:
    GRAPHVIZ_AVAILABLE = False

from app.core.logging import logger
from app.models.explainability import DecisionTreeVisualization


class SurrogateTreeBuilder:
    """代理决策树构建器"""
    
    def __init__(self):
        pass
    
    def build_surrogate_tree(
        self,
        model,
        X: np.ndarray,
        y: np.ndarray,
        feature_names: List[str],
        max_depth: int = 5,
        min_samples_split: int = 20,
        min_samples_leaf: int = 10
    ) -> <PERSON><PERSON>[Any, Dict[str, Any]]:
        """构建代理决策树"""
        try:
            # 使用原模型生成预测作为代理树的目标
            if hasattr(model, 'predict_proba'):
                # 分类问题：使用概率预测
                y_surrogate = model.predict_proba(X)
                if y_surrogate.shape[1] == 2:
                    # 二分类：使用正类概率
                    y_surrogate = y_surrogate[:, 1]
                else:
                    # 多分类：使用预测类别
                    y_surrogate = model.predict(X)
                
                surrogate_tree = DecisionTreeClassifier(
                    max_depth=max_depth,
                    min_samples_split=min_samples_split,
                    min_samples_leaf=min_samples_leaf,
                    random_state=42
                )
                problem_type = 'classification'
            else:
                # 回归问题
                y_surrogate = model.predict(X)
                surrogate_tree = DecisionTreeRegressor(
                    max_depth=max_depth,
                    min_samples_split=min_samples_split,
                    min_samples_leaf=min_samples_leaf,
                    random_state=42
                )
                problem_type = 'regression'
            
            # 训练代理树
            surrogate_tree.fit(X, y_surrogate)
            
            # 计算保真度
            tree_predictions = surrogate_tree.predict(X)
            original_predictions = y_surrogate
            
            if problem_type == 'classification':
                if len(np.unique(y_surrogate)) == 2:
                    # 二分类保真度
                    fidelity = accuracy_score(original_predictions > 0.5, tree_predictions > 0.5)
                else:
                    # 多分类保真度
                    fidelity = accuracy_score(original_predictions, tree_predictions)
            else:
                # 回归保真度
                fidelity = r2_score(original_predictions, tree_predictions)
            
            # 构建树信息
            tree_info = {
                'tree_depth': surrogate_tree.get_depth(),
                'node_count': surrogate_tree.tree_.node_count,
                'leaf_count': np.sum(surrogate_tree.tree_.children_left == -1),
                'fidelity': float(fidelity),
                'problem_type': problem_type,
                'feature_importances': dict(zip(feature_names, surrogate_tree.feature_importances_))
            }
            
            return surrogate_tree, tree_info
            
        except Exception as e:
            logger.error(f"构建代理决策树失败: {e}")
            raise
    
    def extract_tree_structure(
        self,
        tree_model,
        feature_names: List[str],
        class_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """提取树结构"""
        try:
            tree = tree_model.tree_
            
            def recurse(node_id, depth=0):
                """递归提取节点信息"""
                # 检查是否为叶子节点
                if tree.children_left[node_id] == tree.children_right[node_id]:
                    # 叶子节点
                    if hasattr(tree, 'value'):
                        if tree.value[node_id].shape[1] == 1:
                            # 回归或二分类
                            value = float(tree.value[node_id][0, 0])
                        else:
                            # 多分类
                            value = tree.value[node_id][0].tolist()
                    else:
                        value = 0.0
                    
                    return {
                        'node_id': int(node_id),
                        'type': 'leaf',
                        'depth': depth,
                        'samples': int(tree.n_node_samples[node_id]),
                        'value': value,
                        'impurity': float(tree.impurity[node_id])
                    }
                else:
                    # 内部节点
                    feature_idx = tree.feature[node_id]
                    feature_name = feature_names[feature_idx] if feature_idx < len(feature_names) else f"feature_{feature_idx}"
                    
                    return {
                        'node_id': int(node_id),
                        'type': 'internal',
                        'depth': depth,
                        'feature': feature_name,
                        'feature_idx': int(feature_idx),
                        'threshold': float(tree.threshold[node_id]),
                        'samples': int(tree.n_node_samples[node_id]),
                        'impurity': float(tree.impurity[node_id]),
                        'left_child': recurse(tree.children_left[node_id], depth + 1),
                        'right_child': recurse(tree.children_right[node_id], depth + 1)
                    }
            
            tree_structure = recurse(0)
            
            return {
                'tree_structure': tree_structure,
                'max_depth': tree_model.get_depth(),
                'n_nodes': tree.node_count,
                'n_leaves': np.sum(tree.children_left == -1)
            }
            
        except Exception as e:
            logger.error(f"提取树结构失败: {e}")
            return {}


class DecisionPathAnalyzer:
    """决策路径分析器"""
    
    def __init__(self):
        pass
    
    def extract_decision_paths(
        self,
        tree_model,
        X: np.ndarray,
        feature_names: List[str],
        max_instances: int = 100
    ) -> Dict[str, Any]:
        """提取决策路径"""
        try:
            tree = tree_model.tree_
            
            # 获取决策路径
            decision_paths = tree_model.decision_path(X[:max_instances])
            leaf_ids = tree_model.apply(X[:max_instances])
            
            paths_info = []
            
            for i in range(min(max_instances, len(X))):
                # 获取实例的路径
                path = decision_paths[i].toarray()[0]
                path_nodes = np.where(path)[0]
                
                # 构建路径描述
                path_description = []
                for node_id in path_nodes[:-1]:  # 排除叶子节点
                    feature_idx = tree.feature[node_id]
                    threshold = tree.threshold[node_id]
                    feature_name = feature_names[feature_idx] if feature_idx < len(feature_names) else f"feature_{feature_idx}"
                    
                    # 确定分支方向
                    if i < len(X) and node_id + 1 < len(path_nodes):
                        next_node = path_nodes[list(path_nodes).index(node_id) + 1]
                        if next_node == tree.children_left[node_id]:
                            condition = f"{feature_name} <= {threshold:.3f}"
                        else:
                            condition = f"{feature_name} > {threshold:.3f}"
                        
                        path_description.append({
                            'node_id': int(node_id),
                            'feature': feature_name,
                            'feature_idx': int(feature_idx),
                            'threshold': float(threshold),
                            'condition': condition,
                            'feature_value': float(X[i, feature_idx]) if feature_idx < X.shape[1] else 0.0
                        })
                
                # 叶子节点信息
                leaf_id = leaf_ids[i]
                if hasattr(tree, 'value'):
                    if tree.value[leaf_id].shape[1] == 1:
                        leaf_value = float(tree.value[leaf_id][0, 0])
                    else:
                        leaf_value = tree.value[leaf_id][0].tolist()
                else:
                    leaf_value = 0.0
                
                paths_info.append({
                    'instance_idx': i,
                    'path_length': len(path_description),
                    'path_nodes': path_nodes.tolist(),
                    'path_description': path_description,
                    'leaf_id': int(leaf_id),
                    'leaf_value': leaf_value,
                    'prediction': float(tree_model.predict([X[i]])[0])
                })
            
            return {
                'paths': paths_info,
                'total_instances': len(paths_info)
            }
            
        except Exception as e:
            logger.error(f"提取决策路径失败: {e}")
            return {}
    
    def analyze_feature_usage(
        self,
        paths_info: Dict[str, Any],
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """分析特征使用情况"""
        try:
            feature_usage = {}
            feature_splits = {}
            
            for path in paths_info.get('paths', []):
                for step in path['path_description']:
                    feature = step['feature']
                    
                    if feature not in feature_usage:
                        feature_usage[feature] = 0
                        feature_splits[feature] = []
                    
                    feature_usage[feature] += 1
                    feature_splits[feature].append(step['threshold'])
            
            # 计算使用频率
            total_paths = len(paths_info.get('paths', []))
            feature_frequency = {}
            feature_split_stats = {}
            
            for feature, count in feature_usage.items():
                feature_frequency[feature] = count / total_paths if total_paths > 0 else 0
                
                splits = feature_splits[feature]
                if splits:
                    feature_split_stats[feature] = {
                        'mean_threshold': float(np.mean(splits)),
                        'std_threshold': float(np.std(splits)),
                        'min_threshold': float(np.min(splits)),
                        'max_threshold': float(np.max(splits)),
                        'unique_thresholds': len(set(splits))
                    }
            
            return {
                'feature_usage_count': feature_usage,
                'feature_usage_frequency': feature_frequency,
                'feature_split_statistics': feature_split_stats,
                'most_used_features': sorted(feature_frequency.items(), key=lambda x: x[1], reverse=True)[:10]
            }
            
        except Exception as e:
            logger.error(f"特征使用分析失败: {e}")
            return {}


class DecisionBoundaryVisualizer:
    """决策边界可视化器"""
    
    def __init__(self):
        pass
    
    def generate_2d_decision_boundary(
        self,
        model,
        X: np.ndarray,
        y: np.ndarray,
        feature_indices: Tuple[int, int],
        feature_names: List[str],
        resolution: int = 100
    ) -> Dict[str, Any]:
        """生成2D决策边界数据"""
        try:
            if X.shape[1] < 2:
                return {}
            
            # 选择两个特征
            feature_idx1, feature_idx2 = feature_indices
            X_2d = X[:, [feature_idx1, feature_idx2]]
            
            # 创建网格
            x_min, x_max = X_2d[:, 0].min() - 0.1, X_2d[:, 0].max() + 0.1
            y_min, y_max = X_2d[:, 1].min() - 0.1, X_2d[:, 1].max() + 0.1
            
            xx, yy = np.meshgrid(
                np.linspace(x_min, x_max, resolution),
                np.linspace(y_min, y_max, resolution)
            )
            
            # 创建完整特征向量（其他特征使用均值）
            grid_points = np.c_[xx.ravel(), yy.ravel()]
            full_grid = np.zeros((grid_points.shape[0], X.shape[1]))
            
            # 设置选中的两个特征
            full_grid[:, feature_idx1] = grid_points[:, 0]
            full_grid[:, feature_idx2] = grid_points[:, 1]
            
            # 其他特征使用训练数据的均值
            for i in range(X.shape[1]):
                if i not in [feature_idx1, feature_idx2]:
                    full_grid[:, i] = np.mean(X[:, i])
            
            # 预测
            if hasattr(model, 'predict_proba'):
                Z = model.predict_proba(full_grid)
                if Z.shape[1] == 2:
                    Z = Z[:, 1]  # 使用正类概率
                else:
                    Z = model.predict(full_grid)
            else:
                Z = model.predict(full_grid)
            
            Z = Z.reshape(xx.shape)
            
            return {
                'xx': xx.tolist(),
                'yy': yy.tolist(),
                'Z': Z.tolist(),
                'X_2d': X_2d.tolist(),
                'y': y.tolist(),
                'feature_names': [feature_names[feature_idx1], feature_names[feature_idx2]],
                'feature_indices': [feature_idx1, feature_idx2],
                'x_range': [float(x_min), float(x_max)],
                'y_range': [float(y_min), float(y_max)]
            }
            
        except Exception as e:
            logger.error(f"生成2D决策边界失败: {e}")
            return {}


class DecisionVisualizationService:
    """决策可视化服务"""
    
    def __init__(self):
        self.surrogate_builder = SurrogateTreeBuilder()
        self.path_analyzer = DecisionPathAnalyzer()
        self.boundary_visualizer = DecisionBoundaryVisualizer()
    
    async def create_decision_visualization(
        self,
        analysis_id: int,
        model,
        X: np.ndarray,
        y: np.ndarray,
        feature_names: List[str],
        visualization_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """创建决策可视化"""
        try:
            start_time = datetime.utcnow()
            
            # 构建代理决策树
            max_depth = visualization_config.get('max_depth', 5)
            surrogate_tree, tree_info = self.surrogate_builder.build_surrogate_tree(
                model, X, y, feature_names, max_depth=max_depth
            )
            
            # 提取树结构
            tree_structure_info = self.surrogate_builder.extract_tree_structure(
                surrogate_tree, feature_names
            )
            
            # 分析决策路径
            max_instances = visualization_config.get('max_instances', 100)
            paths_info = self.path_analyzer.extract_decision_paths(
                surrogate_tree, X, feature_names, max_instances
            )
            
            # 分析特征使用
            feature_usage_info = self.path_analyzer.analyze_feature_usage(
                paths_info, feature_names
            )
            
            # 生成决策边界（如果特征数量合适）
            boundary_data = {}
            if len(feature_names) >= 2:
                # 选择最重要的两个特征
                feature_importance = tree_info.get('feature_importances', {})
                if feature_importance:
                    top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:2]
                    feature_indices = []
                    for feature_name, _ in top_features:
                        try:
                            idx = feature_names.index(feature_name)
                            feature_indices.append(idx)
                        except ValueError:
                            continue
                    
                    if len(feature_indices) == 2:
                        boundary_data = self.boundary_visualizer.generate_2d_decision_boundary(
                            surrogate_tree, X, y, tuple(feature_indices), feature_names
                        )
            
            # 生成文本表示
            text_representation = self._generate_text_representation(
                surrogate_tree, feature_names
            )
            
            # 计算执行时间
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 保存决策树可视化结果
            decision_viz = DecisionTreeVisualization(
                analysis_id=analysis_id,
                tree_type="surrogate",
                tree_depth=tree_info['tree_depth'],
                node_count=tree_info['node_count'],
                leaf_count=tree_info['leaf_count'],
                tree_structure=tree_structure_info,
                node_information=tree_info,
                decision_paths=paths_info,
                feature_usage=feature_usage_info,
                visualization_config=visualization_config,
                tree_plot_data=boundary_data,
                decision_boundary_data=boundary_data,
                tree_fidelity=tree_info['fidelity'],
                generation_time_seconds=generation_time,
                status="completed"
            )
            
            db.add(decision_viz)
            await db.commit()
            
            return {
                'success': True,
                'visualization_id': decision_viz.id,
                'tree_info': tree_info,
                'tree_structure': tree_structure_info,
                'paths_analysis': paths_info,
                'feature_usage': feature_usage_info,
                'decision_boundary': boundary_data,
                'text_representation': text_representation,
                'generation_time': generation_time
            }
            
        except Exception as e:
            logger.error(f"创建决策可视化失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_text_representation(
        self,
        tree_model,
        feature_names: List[str],
        max_depth: int = 3
    ) -> str:
        """生成文本表示"""
        try:
            return export_text(
                tree_model,
                feature_names=feature_names,
                max_depth=max_depth,
                spacing=2,
                decimals=3,
                show_weights=True
            )
        except Exception as e:
            logger.error(f"生成文本表示失败: {e}")
            return ""


# 全局决策可视化服务实例
decision_visualization_service = DecisionVisualizationService()
