/**
 * WebSocket服务
 * 
 * 提供实时数据推送功能，包括股票价格、交易信号、系统通知等
 */

import { message } from 'antd';

export interface WebSocketMessage {
  type: 'price_update' | 'trade_signal' | 'system_notification' | 'portfolio_update';
  data: any;
  timestamp: number;
}

export interface PriceUpdate {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: number;
}

export interface TradeSignal {
  strategyId: string;
  symbol: string;
  action: 'BUY' | 'SELL';
  price: number;
  quantity: number;
  confidence: number;
  timestamp: number;
}

export interface SystemNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: number;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();
  private isConnecting = false;

  constructor(private url: string) {
    this.initializeListeners();
  }

  private initializeListeners() {
    // 初始化事件监听器映射
    this.listeners.set('price_update', new Set());
    this.listeners.set('trade_signal', new Set());
    this.listeners.set('system_notification', new Set());
    this.listeners.set('portfolio_update', new Set());
    this.listeners.set('connection_status', new Set());
  }

  /**
   * 连接WebSocket
   */
  connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      this.isConnecting = true;
      
      try {
        const wsUrl = token ? `${this.url}?token=${token}` : this.url;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connection_status', { connected: true });
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.isConnecting = false;
          this.stopHeartbeat();
          this.emit('connection_status', { connected: false });
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.stopHeartbeat();
    this.reconnectAttempts = this.maxReconnectAttempts; // 阻止自动重连
  }

  /**
   * 发送消息
   */
  send(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  /**
   * 订阅股票价格更新
   */
  subscribePrice(symbols: string[]) {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'price',
        symbols
      }
    });
  }

  /**
   * 取消订阅股票价格
   */
  unsubscribePrice(symbols: string[]) {
    this.send({
      type: 'unsubscribe',
      data: {
        channel: 'price',
        symbols
      }
    });
  }

  /**
   * 订阅交易信号
   */
  subscribeTradeSignals(strategyIds: string[]) {
    this.send({
      type: 'subscribe',
      data: {
        channel: 'trade_signal',
        strategyIds
      }
    });
  }

  /**
   * 添加事件监听器
   */
  on(event: string, callback: (data: any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback: (data: any) => void) {
    this.listeners.get(event)?.delete(callback);
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any) {
    this.listeners.get(event)?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in WebSocket event handler for ${event}:`, error);
      }
    });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'price_update':
        this.emit('price_update', message.data as PriceUpdate);
        break;
      
      case 'trade_signal':
        this.emit('trade_signal', message.data as TradeSignal);
        this.showTradeSignalNotification(message.data as TradeSignal);
        break;
      
      case 'system_notification':
        this.emit('system_notification', message.data as SystemNotification);
        this.showSystemNotification(message.data as SystemNotification);
        break;
      
      case 'portfolio_update':
        this.emit('portfolio_update', message.data);
        break;
      
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  /**
   * 显示交易信号通知
   */
  private showTradeSignalNotification(signal: TradeSignal) {
    const action = signal.action === 'BUY' ? '买入' : '卖出';
    message.info({
      content: `交易信号: ${action} ${signal.symbol} @ ¥${signal.price}`,
      duration: 5,
    });
  }

  /**
   * 显示系统通知
   */
  private showSystemNotification(notification: SystemNotification) {
    const messageType = notification.type === 'error' ? 'error' : 
                       notification.type === 'warning' ? 'warning' :
                       notification.type === 'success' ? 'success' : 'info';
    
    message[messageType]({
      content: `${notification.title}: ${notification.message}`,
      duration: 5,
    });
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.send({ type: 'ping' });
      }
    }, 30000); // 30秒心跳
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect() {
    this.reconnectAttempts++;
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
    
    setTimeout(() => {
      if (this.reconnectAttempts <= this.maxReconnectAttempts) {
        this.connect().catch(error => {
          console.error('Reconnect failed:', error);
        });
      }
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  /**
   * 获取连接状态
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 获取连接状态文本
   */
  get connectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }
}

// 创建全局WebSocket实例
const wsUrl = process.env.NODE_ENV === 'production' 
  ? 'wss://your-domain.com/ws'
  : 'ws://localhost:8000/ws';

export const websocketService = new WebSocketService(wsUrl);

// 导出类型
export type { WebSocketMessage, PriceUpdate, TradeSignal, SystemNotification };
