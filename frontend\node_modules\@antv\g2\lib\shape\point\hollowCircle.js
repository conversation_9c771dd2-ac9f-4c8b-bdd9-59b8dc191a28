"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowCircle = void 0;
const circle_1 = require("./circle");
/**
 * ○
 */
const HollowCircle = (options, context) => {
    return (0, circle_1.BaseCircle)(Object.assign({ colorAttribute: 'stroke' }, options), context);
};
exports.HollowCircle = HollowCircle;
exports.HollowCircle.props = Object.assign({ defaultMarker: 'hollowPoint' }, circle_1.Circle.props);
//# sourceMappingURL=hollowCircle.js.map