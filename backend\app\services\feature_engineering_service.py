"""
特征工程服务

提供股票数据特征提取、特征计算、特征存储等功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.logging import logger
from app.models.ml import MLFeature, MLFeatureValue
from app.services.jqdata_service import JQDataService
from app.services.indicators_service import indicator_service


class TechnicalFeatureExtractor:
    """技术指标特征提取器"""
    
    def __init__(self):
        self.indicator_service = indicator_service
    
    def extract_price_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """提取价格相关特征"""
        features = {}
        
        try:
            # 基础价格特征
            features['close_price'] = data['close'].values
            features['open_price'] = data['open'].values
            features['high_price'] = data['high'].values
            features['low_price'] = data['low'].values
            features['volume'] = data['volume'].values
            
            # 价格变化特征
            features['price_change'] = data['close'].pct_change().values
            features['price_change_abs'] = np.abs(data['close'].pct_change().values)
            features['high_low_ratio'] = (data['high'] / data['low']).values
            features['open_close_ratio'] = (data['open'] / data['close']).values
            
            # 价格位置特征
            features['close_position'] = ((data['close'] - data['low']) / (data['high'] - data['low'])).values
            
            # 成交量特征
            features['volume_change'] = data['volume'].pct_change().values
            features['volume_price_trend'] = (data['volume'] * data['close'].pct_change()).values
            
            # 波动率特征
            features['volatility_5d'] = data['close'].pct_change().rolling(5).std().values
            features['volatility_20d'] = data['close'].pct_change().rolling(20).std().values
            
            return features
            
        except Exception as e:
            logger.error(f"提取价格特征失败: {e}")
            return {}
    
    def extract_technical_indicators(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """提取技术指标特征"""
        features = {}
        
        try:
            # 准备数据
            close = data['close'].values
            high = data['high'].values
            low = data['low'].values
            volume = data['volume'].values
            
            # 移动平均线
            sma_5 = self.indicator_service.calculate_indicator('sma', {'close': close}, {'period': 5})
            sma_20 = self.indicator_service.calculate_indicator('sma', {'close': close}, {'period': 20})
            sma_60 = self.indicator_service.calculate_indicator('sma', {'close': close}, {'period': 60})
            
            if sma_5['success']:
                features['sma_5'] = np.array(sma_5['data']['sma'])
                features['price_sma5_ratio'] = close / np.array(sma_5['data']['sma'])
            
            if sma_20['success']:
                features['sma_20'] = np.array(sma_20['data']['sma'])
                features['price_sma20_ratio'] = close / np.array(sma_20['data']['sma'])
            
            if sma_60['success']:
                features['sma_60'] = np.array(sma_60['data']['sma'])
                features['price_sma60_ratio'] = close / np.array(sma_60['data']['sma'])
            
            # MACD
            macd_result = self.indicator_service.calculate_indicator('macd', {'close': close}, {})
            if macd_result['success']:
                features['macd_line'] = np.array(macd_result['data']['macd_line'])
                features['macd_signal'] = np.array(macd_result['data']['macd_signal'])
                features['macd_histogram'] = np.array(macd_result['data']['macd_histogram'])
            
            # RSI
            rsi_result = self.indicator_service.calculate_indicator('rsi', {'close': close}, {'period': 14})
            if rsi_result['success']:
                features['rsi_14'] = np.array(rsi_result['data']['rsi'])
                features['rsi_overbought'] = (np.array(rsi_result['data']['rsi']) > 70).astype(int)
                features['rsi_oversold'] = (np.array(rsi_result['data']['rsi']) < 30).astype(int)
            
            # 布林带
            bb_result = self.indicator_service.calculate_indicator('bollinger_bands', {'close': close}, {'period': 20})
            if bb_result['success']:
                upper_band = np.array(bb_result['data']['upper_band'])
                lower_band = np.array(bb_result['data']['lower_band'])
                features['bb_upper'] = upper_band
                features['bb_lower'] = lower_band
                features['bb_position'] = (close - lower_band) / (upper_band - lower_band)
            
            # KDJ
            if len(high) > 0 and len(low) > 0:
                kdj_result = self.indicator_service.calculate_indicator(
                    'kdj', {'high': high, 'low': low, 'close': close}, {'period': 9}
                )
                if kdj_result['success']:
                    features['kdj_k'] = np.array(kdj_result['data']['k'])
                    features['kdj_d'] = np.array(kdj_result['data']['d'])
                    features['kdj_j'] = np.array(kdj_result['data']['j'])
            
            return features
            
        except Exception as e:
            logger.error(f"提取技术指标特征失败: {e}")
            return {}
    
    def extract_momentum_features(self, data: pd.DataFrame) -> Dict[str, np.ndarray]:
        """提取动量特征"""
        features = {}
        
        try:
            close = data['close']
            
            # 价格动量
            features['momentum_1d'] = close.pct_change(1).values
            features['momentum_5d'] = close.pct_change(5).values
            features['momentum_20d'] = close.pct_change(20).values
            features['momentum_60d'] = close.pct_change(60).values
            
            # 累积收益
            features['cumulative_return_5d'] = (close / close.shift(5) - 1).values
            features['cumulative_return_20d'] = (close / close.shift(20) - 1).values
            
            # 动量强度
            returns = close.pct_change()
            features['positive_momentum_ratio_5d'] = returns.rolling(5).apply(lambda x: (x > 0).sum() / len(x)).values
            features['positive_momentum_ratio_20d'] = returns.rolling(20).apply(lambda x: (x > 0).sum() / len(x)).values
            
            # 趋势强度
            features['trend_strength_5d'] = returns.rolling(5).apply(lambda x: x.sum() / x.abs().sum() if x.abs().sum() > 0 else 0).values
            features['trend_strength_20d'] = returns.rolling(20).apply(lambda x: x.sum() / x.abs().sum() if x.abs().sum() > 0 else 0).values
            
            return features
            
        except Exception as e:
            logger.error(f"提取动量特征失败: {e}")
            return {}


class FundamentalFeatureExtractor:
    """基本面特征提取器"""
    
    def extract_valuation_features(self, symbol: str, date: datetime) -> Dict[str, float]:
        """提取估值特征"""
        features = {}
        
        try:
            # 这里应该从JQData获取基本面数据
            # 目前返回模拟数据
            features.update({
                'pe_ratio': 15.5,  # 市盈率
                'pb_ratio': 1.8,   # 市净率
                'ps_ratio': 2.3,   # 市销率
                'pcf_ratio': 8.2,  # 市现率
                'ev_ebitda': 12.1, # 企业价值倍数
                'dividend_yield': 0.025,  # 股息率
                'roe': 0.15,       # 净资产收益率
                'roa': 0.08,       # 总资产收益率
                'debt_to_equity': 0.45,  # 负债权益比
                'current_ratio': 1.8,    # 流动比率
            })
            
            return features
            
        except Exception as e:
            logger.error(f"提取估值特征失败: {e}")
            return {}
    
    def extract_growth_features(self, symbol: str, date: datetime) -> Dict[str, float]:
        """提取成长性特征"""
        features = {}
        
        try:
            # 这里应该从JQData获取财务数据
            # 目前返回模拟数据
            features.update({
                'revenue_growth_yoy': 0.12,    # 营收同比增长
                'profit_growth_yoy': 0.18,     # 利润同比增长
                'revenue_growth_qoq': 0.03,    # 营收环比增长
                'profit_growth_qoq': 0.05,     # 利润环比增长
                'eps_growth_yoy': 0.15,        # 每股收益增长
                'book_value_growth': 0.10,     # 每股净资产增长
                'operating_cf_growth': 0.08,   # 经营现金流增长
            })
            
            return features
            
        except Exception as e:
            logger.error(f"提取成长性特征失败: {e}")
            return {}


class MarketFeatureExtractor:
    """市场特征提取器"""
    
    def extract_market_features(self, date: datetime) -> Dict[str, float]:
        """提取市场环境特征"""
        features = {}
        
        try:
            # 这里应该获取真实的市场数据
            # 目前返回模拟数据
            features.update({
                'market_return': 0.01,      # 市场收益率
                'market_volatility': 0.02,  # 市场波动率
                'vix_index': 18.5,          # 恐慌指数
                'risk_free_rate': 0.025,    # 无风险利率
                'term_spread': 0.015,       # 期限利差
                'credit_spread': 0.008,     # 信用利差
                'money_flow': 1.2,          # 资金流向指标
                'sentiment_index': 0.6,     # 市场情绪指数
            })
            
            return features
            
        except Exception as e:
            logger.error(f"提取市场特征失败: {e}")
            return {}


class FeatureEngineeringService:
    """特征工程服务"""
    
    def __init__(self):
        self.technical_extractor = TechnicalFeatureExtractor()
        self.fundamental_extractor = FundamentalFeatureExtractor()
        self.market_extractor = MarketFeatureExtractor()
        self.jqdata_service = JQDataService()
    
    async def extract_all_features(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """提取所有特征"""
        try:
            # 获取价格数据
            price_data = await self._get_price_data(symbol, start_date, end_date, db)
            if price_data.empty:
                logger.warning(f"无法获取 {symbol} 的价格数据")
                return {}
            
            all_features = {}
            
            # 提取技术特征
            technical_features = self.technical_extractor.extract_price_features(price_data)
            all_features.update(technical_features)
            
            # 提取技术指标特征
            indicator_features = self.technical_extractor.extract_technical_indicators(price_data)
            all_features.update(indicator_features)
            
            # 提取动量特征
            momentum_features = self.technical_extractor.extract_momentum_features(price_data)
            all_features.update(momentum_features)
            
            # 提取基本面特征（每个日期）
            for date in price_data.index:
                fundamental_features = self.fundamental_extractor.extract_valuation_features(symbol, date)
                growth_features = self.fundamental_extractor.extract_growth_features(symbol, date)
                
                # 将基本面特征添加到对应日期
                for feature_name, value in {**fundamental_features, **growth_features}.items():
                    if feature_name not in all_features:
                        all_features[feature_name] = np.full(len(price_data), np.nan)
                    
                    date_index = price_data.index.get_loc(date)
                    all_features[feature_name][date_index] = value
            
            # 提取市场特征
            market_features = self.market_extractor.extract_market_features(end_date)
            for feature_name, value in market_features.items():
                all_features[feature_name] = np.full(len(price_data), value)
            
            # 添加日期信息
            all_features['dates'] = price_data.index.tolist()
            
            return all_features
            
        except Exception as e:
            logger.error(f"提取特征失败: {e}")
            return {}
    
    async def _get_price_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        db: AsyncSession
    ) -> pd.DataFrame:
        """获取价格数据"""
        try:
            # 这里应该从JQData获取真实数据
            # 目前生成模拟数据
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # 生成模拟价格数据
            np.random.seed(42)
            n_days = len(date_range)
            
            # 模拟价格走势
            returns = np.random.normal(0.001, 0.02, n_days)  # 日收益率
            prices = [100.0]  # 初始价格
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # 生成OHLCV数据
            data = []
            for i, (date, price) in enumerate(zip(date_range, prices)):
                high = price * (1 + abs(np.random.normal(0, 0.01)))
                low = price * (1 - abs(np.random.normal(0, 0.01)))
                open_price = prices[i-1] if i > 0 else price
                volume = np.random.randint(1000000, 10000000)
                
                data.append({
                    'date': date,
                    'open': open_price,
                    'high': high,
                    'low': low,
                    'close': price,
                    'volume': volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('date', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            return pd.DataFrame()
    
    async def save_features_to_db(
        self, 
        features: Dict[str, Any], 
        symbol: str,
        db: AsyncSession
    ):
        """保存特征到数据库"""
        try:
            dates = features.get('dates', [])
            if not dates:
                return
            
            for feature_name, values in features.items():
                if feature_name == 'dates':
                    continue
                
                # 检查特征是否存在
                result = await db.execute(
                    select(MLFeature).where(MLFeature.name == feature_name)
                )
                feature = result.scalar_one_or_none()
                
                if not feature:
                    # 创建新特征
                    feature = MLFeature(
                        name=feature_name,
                        display_name=feature_name.replace('_', ' ').title(),
                        description=f"Auto-generated feature: {feature_name}",
                        category=self._get_feature_category(feature_name),
                        data_type="numeric"
                    )
                    db.add(feature)
                    await db.commit()
                    await db.refresh(feature)
                
                # 保存特征值
                if isinstance(values, (list, np.ndarray)):
                    for date, value in zip(dates, values):
                        if not np.isnan(value):
                            feature_value = MLFeatureValue(
                                feature_id=feature.id,
                                symbol=symbol,
                                date=date,
                                value=float(value),
                                data_source="feature_engineering",
                                calculation_version="1.0"
                            )
                            db.add(feature_value)
                
            await db.commit()
            logger.info(f"保存 {symbol} 的特征到数据库成功")
            
        except Exception as e:
            logger.error(f"保存特征到数据库失败: {e}")
            await db.rollback()
    
    def _get_feature_category(self, feature_name: str) -> str:
        """根据特征名称判断类别"""
        if any(keyword in feature_name.lower() for keyword in ['price', 'open', 'high', 'low', 'close', 'volume']):
            return 'technical'
        elif any(keyword in feature_name.lower() for keyword in ['sma', 'ema', 'macd', 'rsi', 'bb', 'kdj']):
            return 'technical'
        elif any(keyword in feature_name.lower() for keyword in ['pe', 'pb', 'roe', 'debt', 'growth']):
            return 'fundamental'
        elif any(keyword in feature_name.lower() for keyword in ['market', 'vix', 'sentiment']):
            return 'macro'
        else:
            return 'other'
    
    async def get_feature_matrix(
        self, 
        symbols: List[str], 
        feature_names: List[str],
        start_date: datetime,
        end_date: datetime,
        db: AsyncSession
    ) -> pd.DataFrame:
        """获取特征矩阵"""
        try:
            # 查询特征值
            query = select(MLFeatureValue).join(MLFeature).where(
                and_(
                    MLFeatureValue.symbol.in_(symbols),
                    MLFeature.name.in_(feature_names),
                    MLFeatureValue.date >= start_date,
                    MLFeatureValue.date <= end_date
                )
            )
            
            result = await db.execute(query)
            feature_values = result.scalars().all()
            
            # 构建特征矩阵
            data = []
            for fv in feature_values:
                data.append({
                    'symbol': fv.symbol,
                    'date': fv.date,
                    'feature': fv.feature.name,
                    'value': float(fv.value)
                })
            
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data)
            
            # 透视表转换
            feature_matrix = df.pivot_table(
                index=['symbol', 'date'],
                columns='feature',
                values='value',
                fill_value=np.nan
            )
            
            return feature_matrix
            
        except Exception as e:
            logger.error(f"获取特征矩阵失败: {e}")
            return pd.DataFrame()


# 全局特征工程服务实例
feature_engineering_service = FeatureEngineeringService()
