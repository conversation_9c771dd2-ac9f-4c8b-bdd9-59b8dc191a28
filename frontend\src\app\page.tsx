'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button, Card, Row, Col, Typography, Space } from 'antd';
import {
  LineChartOutlined,
  BarChartOutlined,
  RobotOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

export default function HomePage() {
  const router = useRouter()

  const features = [
    {
      icon: <LineChartOutlined style={{ fontSize: '48px', color: '#1890ff' }} />,
      title: '实时市场数据',
      description: '接入JQData专业金融数据，提供实时股票、期货、期权等市场数据'
    },
    {
      icon: <BarChartOutlined style={{ fontSize: '48px', color: '#52c41a' }} />,
      title: '量化策略回测',
      description: '强大的回测引擎，支持多种策略类型，提供详细的性能分析报告'
    },
    {
      icon: <RobotOutlined style={{ fontSize: '48px', color: '#722ed1' }} />,
      title: 'AI智能分析',
      description: '集成机器学习算法，提供智能选股、风险预警等AI驱动功能'
    },
    {
      icon: <SecurityScanOutlined style={{ fontSize: '48px', color: '#fa8c16' }} />,
      title: '风险管理',
      description: '全面的风险控制体系，实时监控投资组合风险，保障资金安全'
    }
  ]

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '40px 20px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 头部标题 */}
        <div style={{ textAlign: 'center', marginBottom: '60px' }}>
          <Title 
            level={1} 
            style={{ 
              color: 'white', 
              fontSize: '48px', 
              marginBottom: '20px',
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
            }}
          >
            智能量化交易平台
          </Title>
          <Paragraph 
            style={{ 
              color: 'rgba(255,255,255,0.9)', 
              fontSize: '20px',
              textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
            }}
          >
            基于AI的量化投资解决方案，让投资更智能、更高效
          </Paragraph>
          
          <Space size="large" style={{ marginTop: '30px' }}>
            <Button 
              type="primary" 
              size="large"
              onClick={() => router.push('/auth/login')}
              style={{ 
                height: '50px', 
                fontSize: '16px',
                borderRadius: '25px',
                paddingLeft: '30px',
                paddingRight: '30px'
              }}
            >
              立即开始
            </Button>
            <Button 
              size="large"
              onClick={() => router.push('/dashboard')}
              style={{ 
                height: '50px', 
                fontSize: '16px',
                borderRadius: '25px',
                paddingLeft: '30px',
                paddingRight: '30px',
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderColor: 'rgba(255,255,255,0.5)',
                color: 'white'
              }}
            >
              查看演示
            </Button>
          </Space>
        </div>

        {/* 功能特性 */}
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                style={{
                  height: '280px',
                  borderRadius: '16px',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                  border: 'none'
                }}
                bodyStyle={{
                  padding: '30px 20px',
                  textAlign: 'center',
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}
              >
                <div style={{ marginBottom: '20px' }}>
                  {feature.icon}
                </div>
                <Title level={4} style={{ marginBottom: '15px' }}>
                  {feature.title}
                </Title>
                <Paragraph style={{ color: '#666', lineHeight: '1.6' }}>
                  {feature.description}
                </Paragraph>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 底部信息 */}
        <div style={{ 
          textAlign: 'center', 
          marginTop: '80px',
          color: 'rgba(255,255,255,0.8)'
        }}>
          <Paragraph style={{ color: 'inherit', marginBottom: '10px' }}>
            © 2025 智能量化交易平台. 版本 1.0.0
          </Paragraph>
          <Paragraph style={{ color: 'inherit' }}>
            基于 Next.js + FastAPI + JQData 构建
          </Paragraph>
        </div>
      </div>
    </div>
  )
}
