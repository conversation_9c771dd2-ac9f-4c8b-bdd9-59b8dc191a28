"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/jqdata/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/settings/jqdata/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JQDataConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/descriptions/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * JQData配置页面\n * \n * 用户配置JQData账号、查看配额使用情况、测试连接等\n */ \n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction JQDataConfigPage() {\n    var _testResult_quotaInfo_quotaUsageRate;\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testLoading, setTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTestModal, setShowTestModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"email\");\n    // 验证用户名格式\n    const validateUsername = (rule, value)=>{\n        if (!value) {\n            return Promise.reject(new Error(\"请输入用户名\"));\n        }\n        if (loginType === \"email\") {\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的邮箱地址\"));\n            }\n        } else if (loginType === \"mobile\") {\n            const mobileRegex = /^1[3-9]\\d{9}$/;\n            if (!mobileRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的手机号码\"));\n            }\n        }\n        return Promise.resolve();\n    };\n    // 加载JQData配置\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/jqdata/config\");\n            if (response.code === 200 && response.data) {\n                setConfig(response.data);\n                form.setFieldsValue({\n                    username: response.data.username\n                });\n                setCurrentStep(2); // 已配置\n            } else {\n                setCurrentStep(0); // 未配置\n            }\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                setCurrentStep(0); // 未配置\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"加载配置失败\");\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 保存配置\n    const handleSave = async (values)=>{\n        try {\n            setLoading(true);\n            setCurrentStep(1); // 配置中\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/jqdata/config\", {\n                username: values.username,\n                password: values.password\n            });\n            if (response.code === 200) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"JQData配置保存成功！\");\n                setConfig(response.data);\n                setCurrentStep(2); // 配置完成\n                form.resetFields([\n                    \"password\"\n                ]);\n            } else {\n                throw new Error(response.message);\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"配置保存失败\");\n            setCurrentStep(0); // 回到未配置状态\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试连接\n    const handleTestConnection = async ()=>{\n        try {\n            setTestLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/jqdata/test-connection\");\n            setTestResult(response.data);\n            setShowTestModal(true);\n            if (response.data.success) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"连接测试成功！\");\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"连接测试失败\");\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"测试连接失败\");\n            setTestResult({\n                success: false,\n                message: \"测试连接失败\",\n                errorDetails: error.message\n            });\n            setShowTestModal(true);\n        } finally{\n            setTestLoading(false);\n        }\n    };\n    // 删除配置\n    const handleDelete = ()=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].confirm({\n            title: \"确认删除配置\",\n            content: \"删除后将无法获取JQData数据，确定要删除吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            onOk: async ()=>{\n                try {\n                    await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/jqdata/config\");\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"配置删除成功\");\n                    setConfig(null);\n                    setCurrentStep(0);\n                    form.resetFields();\n                } catch (error) {\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"删除配置失败\");\n                }\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // 配置步骤\n    const steps = [\n        {\n            title: \"配置账号\",\n            description: \"输入JQData账号信息\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"验证连接\",\n            description: \"验证账号有效性\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"配置完成\",\n            description: \"开始使用JQData服务\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    if (loading && !config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: \"JQData配置\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        children: \"配置您的JQData账号以获取实时市场数据\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    current: currentStep,\n                    items: steps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData已配置\",\n                description: \"账号: \".concat(config.username, \" | 状态: \").concat(config.isActive ? \"正常\" : \"异常\"),\n                type: config.isActive ? \"success\" : \"warning\",\n                showIcon: true,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: handleTestConnection,\n                            loading: testLoading,\n                            children: \"测试连接\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: loadConfig,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请配置您的JQData账号以获取实时市场数据\",\n                type: \"info\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"账号配置\",\n                            extra: config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                onClick: handleDelete,\n                                children: \"删除配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    form: form,\n                                    layout: \"vertical\",\n                                    onFinish: handleSave,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"username\",\n                                            label: \"JQData用户名\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入JQData用户名\"\n                                                },\n                                                {\n                                                    type: \"email\",\n                                                    message: \"请输入有效的邮箱地址\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                placeholder: \"请输入JQData注册邮箱\",\n                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"password\",\n                                            label: \"JQData密码\",\n                                            rules: [\n                                                {\n                                                    required: !config,\n                                                    message: \"请输入JQData密码\"\n                                                },\n                                                {\n                                                    min: 6,\n                                                    message: \"密码长度至少6位\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Password, {\n                                                placeholder: config ? \"留空表示不修改密码\" : \"请输入JQData密码\",\n                                                iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 31\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 48\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        type: \"primary\",\n                                                        htmlType: \"submit\",\n                                                        loading: loading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        children: config ? \"更新配置\" : \"保存配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        onClick: handleTestConnection,\n                                                        loading: testLoading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: \"测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"配置说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 请使用您在JQData官网注册的邮箱和密码\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 密码将被加密存储，确保账号安全\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 配置后可获取实时股票数据和历史数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 如遇问题请检查账号状态或联系客服\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mt-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"配额使用情况\",\n                            children: config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        gutter: 16,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    title: \"总配额\",\n                                                    value: config.quotaTotal,\n                                                    suffix: \"次\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    title: \"已使用\",\n                                                    value: config.quotaUsed,\n                                                    suffix: \"次\",\n                                                    valueStyle: {\n                                                        color: config.quotaUsed / config.quotaTotal > 0.8 ? \"#ff4d4f\" : \"#1890ff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        children: \"使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        strong: true,\n                                                        children: [\n                                                            (config.quotaUsed / config.quotaTotal * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                percent: config.quotaUsed / config.quotaTotal * 100,\n                                                status: config.quotaUsed / config.quotaTotal > 0.9 ? \"exception\" : \"active\",\n                                                strokeColor: {\n                                                    \"0%\": \"#108ee9\",\n                                                    \"100%\": \"#87d068\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        size: \"small\",\n                                        column: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"剩余配额\",\n                                                children: [\n                                                    config.quotaRemaining,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"总调用次数\",\n                                                children: [\n                                                    config.totalApiCalls,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"最后使用\",\n                                                children: config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : \"未使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"配额重置\",\n                                                children: config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : \"未知\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, this),\n                                    config.authFailureCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        message: \"认证失败 \".concat(config.authFailureCount, \" 次\"),\n                                        description: config.lastAuthError,\n                                        type: \"warning\",\n                                        showIcon: true,\n                                        className: \"mt-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"配置JQData账号后查看配额信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                title: \"连接测试结果\",\n                open: showTestModal,\n                onCancel: ()=>setShowTestModal(false),\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>setShowTestModal(false),\n                        children: \"关闭\"\n                    }, \"close\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: testResult.success ? \"连接成功\" : \"连接失败\",\n                            description: testResult.message,\n                            type: testResult.success ? \"success\" : \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 13\n                        }, this),\n                        testResult.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"响应时间: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: [\n                                        testResult.responseTime.toFixed(3),\n                                        \"秒\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 15\n                        }, this),\n                        testResult.quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"配额信息\",\n                            size: \"small\",\n                            column: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"总配额\",\n                                    children: testResult.quotaInfo.quotaTotal\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"已使用\",\n                                    children: testResult.quotaInfo.quotaUsed\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"剩余\",\n                                    children: testResult.quotaInfo.quotaRemaining\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"使用率\",\n                                    children: [\n                                        (_testResult_quotaInfo_quotaUsageRate = testResult.quotaInfo.quotaUsageRate) === null || _testResult_quotaInfo_quotaUsageRate === void 0 ? void 0 : _testResult_quotaInfo_quotaUsageRate.toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 15\n                        }, this),\n                        testResult.errorDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: \"错误详情\",\n                            description: testResult.errorDetails,\n                            type: \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 453,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(JQDataConfigPage, \"w2C8f7+qmF4P4nT/eLuB5icmSIA=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = JQDataConfigPage;\nvar _c;\n$RefreshReg$(_c, \"JQDataConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx\n"));

/***/ })

});