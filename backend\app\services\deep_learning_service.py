"""
深度学习服务

提供深度学习模型的训练和预测功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import asyncio
import joblib
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from app.core.logging import logger
from app.services.jqdata_service import JQDataService

try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow not available, deep learning features will be limited")

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    logger.warning("PyTorch not available, some deep learning features will be limited")


class DeepLearningService:
    """深度学习服务类"""
    
    def __init__(self, jqdata_service: JQDataService):
        self.jqdata_service = jqdata_service
        self.models = {}
        self.scalers = {}
        
    async def train_lstm_model(
        self,
        symbol: str,
        sequence_length: int = 60,
        epochs: int = 50,
        batch_size: int = 32,
        model_type: str = 'tensorflow'
    ) -> Dict[str, Any]:
        """训练LSTM模型"""
        try:
            if not TENSORFLOW_AVAILABLE and model_type == 'tensorflow':
                return {
                    'success': False,
                    'error': 'TensorFlow not available'
                }
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365 * 2)  # 2年数据
            
            price_data = await self.jqdata_service.get_price_data(
                symbol, 
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if not price_data:
                return {
                    'success': False,
                    'error': 'No price data available'
                }
            
            # 准备数据
            df = pd.DataFrame(price_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 特征工程
            features = self._create_features(df)
            
            # 创建序列数据
            X, y = self._create_sequences(features, sequence_length)
            
            if len(X) == 0:
                return {
                    'success': False,
                    'error': 'Insufficient data for sequence creation'
                }
            
            # 数据标准化
            scaler = MinMaxScaler()
            X_scaled = scaler.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            if model_type == 'tensorflow':
                model, history = await self._train_tensorflow_lstm(
                    X_train, y_train, X_test, y_test, epochs, batch_size
                )
            else:
                model, history = await self._train_pytorch_lstm(
                    X_train, y_train, X_test, y_test, epochs, batch_size
                )
            
            # 评估模型
            predictions = model.predict(X_test) if model_type == 'tensorflow' else self._predict_pytorch(model, X_test)
            
            mse = mean_squared_error(y_test, predictions)
            mae = mean_absolute_error(y_test, predictions)
            r2 = r2_score(y_test, predictions)
            
            # 保存模型和标准化器
            model_key = f"{symbol}_lstm_{model_type}"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            
            return {
                'success': True,
                'model_key': model_key,
                'metrics': {
                    'mse': float(mse),
                    'mae': float(mae),
                    'r2_score': float(r2),
                    'rmse': float(np.sqrt(mse))
                },
                'training_history': history,
                'data_shape': {
                    'train_samples': len(X_train),
                    'test_samples': len(X_test),
                    'features': X_train.shape[-1],
                    'sequence_length': sequence_length
                }
            }
            
        except Exception as e:
            logger.error(f"LSTM model training failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def train_cnn_model(
        self,
        symbol: str,
        window_size: int = 20,
        epochs: int = 50,
        batch_size: int = 32
    ) -> Dict[str, Any]:
        """训练CNN模型"""
        try:
            if not TENSORFLOW_AVAILABLE:
                return {
                    'success': False,
                    'error': 'TensorFlow not available'
                }
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365 * 2)
            
            price_data = await self.jqdata_service.get_price_data(
                symbol,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if not price_data:
                return {
                    'success': False,
                    'error': 'No price data available'
                }
            
            # 准备数据
            df = pd.DataFrame(price_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 特征工程
            features = self._create_features(df)
            
            # 创建窗口数据
            X, y = self._create_windows(features, window_size)
            
            if len(X) == 0:
                return {
                    'success': False,
                    'error': 'Insufficient data for window creation'
                }
            
            # 数据标准化
            scaler = MinMaxScaler()
            X_scaled = scaler.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # 构建CNN模型
            model = keras.Sequential([
                layers.Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=(window_size, X_train.shape[-1])),
                layers.Conv1D(filters=64, kernel_size=3, activation='relu'),
                layers.Dropout(0.5),
                layers.MaxPooling1D(pool_size=2),
                layers.Conv1D(filters=50, kernel_size=3, activation='relu'),
                layers.Dropout(0.5),
                layers.GlobalMaxPooling1D(),
                layers.Dense(50, activation='relu'),
                layers.Dense(1)
            ])
            
            model.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
            # 训练模型
            history = model.fit(
                X_train, y_train,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=(X_test, y_test),
                verbose=0
            )
            
            # 评估模型
            predictions = model.predict(X_test)
            
            mse = mean_squared_error(y_test, predictions)
            mae = mean_absolute_error(y_test, predictions)
            r2 = r2_score(y_test, predictions)
            
            # 保存模型
            model_key = f"{symbol}_cnn"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            
            return {
                'success': True,
                'model_key': model_key,
                'metrics': {
                    'mse': float(mse),
                    'mae': float(mae),
                    'r2_score': float(r2),
                    'rmse': float(np.sqrt(mse))
                },
                'training_history': {
                    'loss': [float(x) for x in history.history['loss']],
                    'val_loss': [float(x) for x in history.history['val_loss']],
                    'mae': [float(x) for x in history.history['mae']],
                    'val_mae': [float(x) for x in history.history['val_mae']]
                },
                'data_shape': {
                    'train_samples': len(X_train),
                    'test_samples': len(X_test),
                    'features': X_train.shape[-1],
                    'window_size': window_size
                }
            }
            
        except Exception as e:
            logger.error(f"CNN model training failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def train_transformer_model(
        self,
        symbol: str,
        sequence_length: int = 60,
        d_model: int = 64,
        num_heads: int = 8,
        epochs: int = 50,
        batch_size: int = 32
    ) -> Dict[str, Any]:
        """训练Transformer模型"""
        try:
            if not TENSORFLOW_AVAILABLE:
                return {
                    'success': False,
                    'error': 'TensorFlow not available'
                }
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365 * 2)
            
            price_data = await self.jqdata_service.get_price_data(
                symbol,
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d')
            )
            
            if not price_data:
                return {
                    'success': False,
                    'error': 'No price data available'
                }
            
            # 准备数据
            df = pd.DataFrame(price_data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 特征工程
            features = self._create_features(df)
            
            # 创建序列数据
            X, y = self._create_sequences(features, sequence_length)
            
            if len(X) == 0:
                return {
                    'success': False,
                    'error': 'Insufficient data for sequence creation'
                }
            
            # 数据标准化
            scaler = MinMaxScaler()
            X_scaled = scaler.fit_transform(X.reshape(-1, X.shape[-1])).reshape(X.shape)
            
            # 分割数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # 构建Transformer模型
            model = self._build_transformer_model(
                sequence_length, X_train.shape[-1], d_model, num_heads
            )
            
            model.compile(optimizer='adam', loss='mse', metrics=['mae'])
            
            # 训练模型
            history = model.fit(
                X_train, y_train,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=(X_test, y_test),
                verbose=0
            )
            
            # 评估模型
            predictions = model.predict(X_test)
            
            mse = mean_squared_error(y_test, predictions)
            mae = mean_absolute_error(y_test, predictions)
            r2 = r2_score(y_test, predictions)
            
            # 保存模型
            model_key = f"{symbol}_transformer"
            self.models[model_key] = model
            self.scalers[model_key] = scaler
            
            return {
                'success': True,
                'model_key': model_key,
                'metrics': {
                    'mse': float(mse),
                    'mae': float(mae),
                    'r2_score': float(r2),
                    'rmse': float(np.sqrt(mse))
                },
                'training_history': {
                    'loss': [float(x) for x in history.history['loss']],
                    'val_loss': [float(x) for x in history.history['val_loss']],
                    'mae': [float(x) for x in history.history['mae']],
                    'val_mae': [float(x) for x in history.history['val_mae']]
                },
                'model_params': {
                    'd_model': d_model,
                    'num_heads': num_heads,
                    'sequence_length': sequence_length
                }
            }
            
        except Exception as e:
            logger.error(f"Transformer model training failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_features(self, df: pd.DataFrame) -> np.ndarray:
        """创建特征"""
        features = []
        
        # 价格特征
        features.extend([
            df['close'].values,
            df['high'].values,
            df['low'].values,
            df['open'].values,
            df['volume'].values
        ])
        
        # 技术指标
        close_prices = df['close'].values
        
        # 移动平均线
        for window in [5, 10, 20]:
            ma = pd.Series(close_prices).rolling(window=window).mean().fillna(method='bfill').values
            features.append(ma)
        
        # RSI
        rsi = self._calculate_rsi(close_prices, 14)
        features.append(rsi)
        
        # MACD
        macd, signal = self._calculate_macd(close_prices)
        features.extend([macd, signal])
        
        # 收益率
        returns = np.diff(close_prices) / close_prices[:-1]
        returns = np.concatenate([[0], returns])  # 第一个值设为0
        features.append(returns)
        
        return np.column_stack(features)
    
    def _create_sequences(self, data: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """创建序列数据"""
        X, y = [], []
        
        for i in range(sequence_length, len(data)):
            X.append(data[i-sequence_length:i])
            y.append(data[i, 0])  # 预测收盘价
        
        return np.array(X), np.array(y)
    
    def _create_windows(self, data: np.ndarray, window_size: int) -> Tuple[np.ndarray, np.ndarray]:
        """创建窗口数据"""
        X, y = [], []
        
        for i in range(window_size, len(data)):
            X.append(data[i-window_size:i])
            y.append(data[i, 0])  # 预测收盘价
        
        return np.array(X), np.array(y)
    
    async def _train_tensorflow_lstm(
        self, 
        X_train: np.ndarray, 
        y_train: np.ndarray,
        X_test: np.ndarray,
        y_test: np.ndarray,
        epochs: int,
        batch_size: int
    ) -> Tuple[Any, Dict]:
        """训练TensorFlow LSTM模型"""
        model = keras.Sequential([
            layers.LSTM(50, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])),
            layers.Dropout(0.2),
            layers.LSTM(50, return_sequences=True),
            layers.Dropout(0.2),
            layers.LSTM(50),
            layers.Dropout(0.2),
            layers.Dense(1)
        ])
        
        model.compile(optimizer='adam', loss='mse', metrics=['mae'])
        
        history = model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_data=(X_test, y_test),
            verbose=0
        )
        
        return model, {
            'loss': [float(x) for x in history.history['loss']],
            'val_loss': [float(x) for x in history.history['val_loss']],
            'mae': [float(x) for x in history.history['mae']],
            'val_mae': [float(x) for x in history.history['val_mae']]
        }
    
    async def _train_pytorch_lstm(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_test: np.ndarray,
        y_test: np.ndarray,
        epochs: int,
        batch_size: int
    ) -> Tuple[Any, Dict]:
        """训练PyTorch LSTM模型"""
        if not PYTORCH_AVAILABLE:
            raise ImportError("PyTorch not available")
        
        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(X_train)
        y_train_tensor = torch.FloatTensor(y_train).view(-1, 1)
        X_test_tensor = torch.FloatTensor(X_test)
        y_test_tensor = torch.FloatTensor(y_test).view(-1, 1)
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # 定义模型
        class LSTMModel(nn.Module):
            def __init__(self, input_size, hidden_size=50, num_layers=2):
                super(LSTMModel, self).__init__()
                self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
                self.fc = nn.Linear(hidden_size, 1)
                
            def forward(self, x):
                lstm_out, _ = self.lstm(x)
                output = self.fc(lstm_out[:, -1, :])
                return output
        
        model = LSTMModel(X_train.shape[2])
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters())
        
        # 训练模型
        train_losses = []
        val_losses = []
        
        for epoch in range(epochs):
            model.train()
            epoch_loss = 0
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            # 验证
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_test_tensor)
                val_loss = criterion(val_outputs, y_test_tensor)
            
            train_losses.append(epoch_loss / len(train_loader))
            val_losses.append(val_loss.item())
        
        return model, {
            'loss': train_losses,
            'val_loss': val_losses
        }
    
    def _build_transformer_model(
        self, 
        sequence_length: int, 
        num_features: int, 
        d_model: int, 
        num_heads: int
    ):
        """构建Transformer模型"""
        inputs = layers.Input(shape=(sequence_length, num_features))
        
        # 位置编码
        x = layers.Dense(d_model)(inputs)
        
        # Multi-head attention
        attention_output = layers.MultiHeadAttention(
            num_heads=num_heads, key_dim=d_model
        )(x, x)
        
        # Add & Norm
        x = layers.Add()([x, attention_output])
        x = layers.LayerNormalization()(x)
        
        # Feed Forward
        ff_output = layers.Dense(d_model * 4, activation='relu')(x)
        ff_output = layers.Dense(d_model)(ff_output)
        
        # Add & Norm
        x = layers.Add()([x, ff_output])
        x = layers.LayerNormalization()(x)
        
        # Global pooling and output
        x = layers.GlobalAveragePooling1D()(x)
        x = layers.Dropout(0.1)(x)
        outputs = layers.Dense(1)(x)
        
        model = keras.Model(inputs, outputs)
        return model
    
    def _calculate_rsi(self, prices: np.ndarray, window: int = 14) -> np.ndarray:
        """计算RSI指标"""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = pd.Series(gains).rolling(window=window).mean().fillna(50).values
        avg_losses = pd.Series(losses).rolling(window=window).mean().fillna(50).values
        
        rs = avg_gains / (avg_losses + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        
        return np.concatenate([[50], rsi])  # 第一个值设为50
    
    def _calculate_macd(self, prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray]:
        """计算MACD指标"""
        ema_fast = pd.Series(prices).ewm(span=fast).mean().values
        ema_slow = pd.Series(prices).ewm(span=slow).mean().values
        
        macd = ema_fast - ema_slow
        signal_line = pd.Series(macd).ewm(span=signal).mean().values
        
        return macd, signal_line
    
    def _predict_pytorch(self, model, X_test: np.ndarray) -> np.ndarray:
        """PyTorch模型预测"""
        if not PYTORCH_AVAILABLE:
            raise ImportError("PyTorch not available")
        
        model.eval()
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test)
            predictions = model(X_test_tensor)
            return predictions.numpy().flatten()
    
    async def predict_with_model(
        self,
        model_key: str,
        recent_data: List[Dict[str, Any]],
        steps_ahead: int = 1
    ) -> Dict[str, Any]:
        """使用训练好的模型进行预测"""
        try:
            if model_key not in self.models:
                return {
                    'success': False,
                    'error': 'Model not found'
                }
            
            model = self.models[model_key]
            scaler = self.scalers[model_key]
            
            # 准备预测数据
            df = pd.DataFrame(recent_data)
            features = self._create_features(df)
            
            # 标准化
            features_scaled = scaler.transform(features)
            
            # 预测
            if 'tensorflow' in model_key or 'cnn' in model_key or 'transformer' in model_key:
                # TensorFlow模型
                if 'lstm' in model_key or 'transformer' in model_key:
                    # 序列模型
                    X_pred = features_scaled[-60:].reshape(1, 60, -1)  # 假设序列长度为60
                else:
                    # CNN模型
                    X_pred = features_scaled[-20:].reshape(1, 20, -1)  # 假设窗口大小为20
                
                predictions = model.predict(X_pred)
            else:
                # PyTorch模型
                X_pred = features_scaled[-60:].reshape(1, 60, -1)
                predictions = self._predict_pytorch(model, X_pred)
            
            return {
                'success': True,
                'predictions': predictions.tolist() if hasattr(predictions, 'tolist') else [float(predictions)],
                'model_type': model_key.split('_')[-1],
                'prediction_steps': steps_ahead
            }
            
        except Exception as e:
            logger.error(f"Model prediction failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
