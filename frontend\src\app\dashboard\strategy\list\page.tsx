'use client';

import React from 'react';
import { Card, Table, Tag, Button, Space, Empty, Typography } from 'antd';
import { 
  BookOutlined,
  PlusOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function StrategyListContent() {
  const router = useRouter();

  // 示例策略数据
  const strategies = [
    {
      key: '1',
      name: '双均线策略',
      description: '基于5日和20日移动平均线的交易策略',
      status: 'running',
      return: 12.5,
      profit: 2500,
      maxDrawdown: -5.2,
      sharpeRatio: 1.35,
      lastUpdate: '2024-01-15 14:30:00',
      createTime: '2024-01-01 10:00:00'
    },
    {
      key: '2',
      name: 'RSI反转策略',
      description: '基于RSI指标的超买超卖反转策略',
      status: 'stopped',
      return: -3.2,
      profit: -640,
      maxDrawdown: -8.5,
      sharpeRatio: 0.85,
      lastUpdate: '2024-01-14 09:15:00',
      createTime: '2023-12-15 16:20:00'
    }
  ];

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'running' ? 'green' : 'red'}>
          {status === 'running' ? (
            <>
              <PlayCircleOutlined /> 运行中
            </>
          ) : (
            <>
              <PauseCircleOutlined /> 已停止
            </>
          )}
        </Tag>
      ),
    },
    {
      title: '收益率',
      dataIndex: 'return',
      key: 'return',
      render: (value: number) => (
        <span className={value >= 0 ? 'text-green-600' : 'text-red-600'}>
          {value >= 0 ? <RiseOutlined /> : <FallOutlined />}
          {' '}{value > 0 ? '+' : ''}{value.toFixed(2)}%
        </span>
      ),
    },
    {
      title: '盈亏',
      dataIndex: 'profit',
      key: 'profit',
      render: (value: number) => (
        <span className={value >= 0 ? 'text-green-600' : 'text-red-600'}>
          ¥{value > 0 ? '+' : ''}{value.toLocaleString()}
        </span>
      ),
    },
    {
      title: '最大回撤',
      dataIndex: 'maxDrawdown',
      key: 'maxDrawdown',
      render: (value: number) => (
        <span className="text-red-600">
          {value.toFixed(2)}%
        </span>
      ),
    },
    {
      title: '夏普比率',
      dataIndex: 'sharpeRatio',
      key: 'sharpeRatio',
      render: (value: number) => value.toFixed(2),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={record.status === 'running' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          >
            {record.status === 'running' ? '停止' : '启动'}
          </Button>
          <Button type="link" icon={<DeleteOutlined />} danger>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <BookOutlined className="mr-3" />
          策略列表
        </Title>
        <Text type="secondary" className="text-lg">
          管理您的所有量化交易策略
        </Text>
      </div>

      {/* 策略列表 */}
      <Card 
        title="我的策略" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            创建策略
          </Button>
        }
      >
        {strategies.length > 0 ? (
          <Table 
            columns={columns} 
            dataSource={strategies} 
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条策略`
            }}
            scroll={{ x: 1200 }}
          />
        ) : (
          <Empty 
            description="暂无策略"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => router.push('/dashboard/strategy/editor')}
            >
              创建第一个策略
            </Button>
          </Empty>
        )}
      </Card>
    </div>
  );
}

export default function StrategyListPage() {
  return (
    <ClientAuthWrapper>
      <StrategyListContent />
    </ClientAuthWrapper>
  );
}
