"""
模型可解释性核心服务

整合SHAP、LIME、决策可视化等功能，提供完整的模型解释流程
"""

import numpy as np
import pandas as pd
import pickle
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from app.core.logging import logger
from app.models.explainability import (
    ExplainabilityAnalysis, ModelInterpretabilityMetrics, ExplanationReport
)
from app.models.ml import MLModel
from app.models.automl import AutoMLModel
from app.services.shap_explainer_service import shap_explainer_service
from app.services.lime_explainer_service import lime_explainer_service
from app.services.decision_visualization_service import decision_visualization_service


class ModelLoader:
    """模型加载器"""
    
    def __init__(self):
        pass
    
    async def load_model(
        self,
        model_id: int,
        model_type: str,
        db: AsyncSession
    ) -> <PERSON><PERSON>[Any, Dict[str, Any]]:
        """加载模型"""
        try:
            if model_type == 'ml_model':
                return await self._load_ml_model(model_id, db)
            elif model_type == 'automl_model':
                return await self._load_automl_model(model_id, db)
            else:
                raise ValueError(f"Unsupported model type: {model_type}")
                
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    async def _load_ml_model(self, model_id: int, db: AsyncSession) -> Tuple[Any, Dict[str, Any]]:
        """加载ML模型"""
        try:
            result = await db.execute(
                select(MLModel).where(MLModel.id == model_id)
            )
            model_record = result.scalar_one_or_none()
            
            if not model_record:
                raise ValueError(f"ML model {model_id} not found")
            
            # 反序列化模型
            if model_record.model_binary:
                model = pickle.loads(model_record.model_binary)
            else:
                raise ValueError("Model binary data not found")
            
            model_info = {
                'model_id': model_record.id,
                'model_name': model_record.model_name,
                'algorithm': model_record.algorithm,
                'feature_names': model_record.feature_names or [],
                'target_column': model_record.target_column,
                'problem_type': model_record.problem_type,
                'performance_metrics': model_record.performance_metrics or {}
            }
            
            return model, model_info
            
        except Exception as e:
            logger.error(f"加载ML模型失败: {e}")
            raise
    
    async def _load_automl_model(self, model_id: int, db: AsyncSession) -> Tuple[Any, Dict[str, Any]]:
        """加载AutoML模型"""
        try:
            result = await db.execute(
                select(AutoMLModel).where(AutoMLModel.id == model_id)
            )
            model_record = result.scalar_one_or_none()
            
            if not model_record:
                raise ValueError(f"AutoML model {model_id} not found")
            
            # 反序列化模型
            if model_record.model_binary:
                model = pickle.loads(model_record.model_binary)
            else:
                raise ValueError("Model binary data not found")
            
            model_info = {
                'model_id': model_record.id,
                'model_name': model_record.model_name,
                'algorithm': model_record.algorithm,
                'feature_names': model_record.feature_list or [],
                'hyperparameters': model_record.hyperparameters or {},
                'performance_metrics': model_record.performance_metrics or {}
            }
            
            return model, model_info
            
        except Exception as e:
            logger.error(f"加载AutoML模型失败: {e}")
            raise


class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self):
        pass
    
    def prepare_explanation_data(
        self,
        X: np.ndarray,
        y: Optional[np.ndarray],
        feature_names: List[str],
        sample_size: Optional[int] = None
    ) -> Tuple[np.ndarray, Optional[np.ndarray], List[str]]:
        """准备解释数据"""
        try:
            # 确保数据是numpy数组
            if isinstance(X, pd.DataFrame):
                feature_names = list(X.columns)
                X = X.values
            
            if isinstance(y, pd.Series):
                y = y.values
            
            # 采样数据（如果需要）
            if sample_size and len(X) > sample_size:
                indices = np.random.choice(len(X), sample_size, replace=False)
                X = X[indices]
                if y is not None:
                    y = y[indices]
            
            # 处理缺失值
            if np.any(np.isnan(X)):
                X = np.nan_to_num(X, nan=0.0)
            
            if y is not None and np.any(np.isnan(y)):
                y = np.nan_to_num(y, nan=0.0)
            
            # 确保特征名称数量匹配
            if len(feature_names) != X.shape[1]:
                feature_names = [f"feature_{i}" for i in range(X.shape[1])]
            
            return X, y, feature_names
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            raise


class InterpretabilityMetricsCalculator:
    """可解释性指标计算器"""
    
    def __init__(self):
        pass
    
    def calculate_interpretability_metrics(
        self,
        model,
        model_info: Dict[str, Any],
        explanation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算可解释性指标"""
        try:
            metrics = {}
            
            # 模型复杂度指标
            metrics.update(self._calculate_complexity_metrics(model, model_info))
            
            # 解释质量指标
            metrics.update(self._calculate_explanation_quality_metrics(explanation_results))
            
            # 稳定性指标
            metrics.update(self._calculate_stability_metrics(explanation_results))
            
            # 一致性指标
            metrics.update(self._calculate_consistency_metrics(explanation_results))
            
            # 总体可解释性评分
            metrics['overall_interpretability_score'] = self._calculate_overall_score(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算可解释性指标失败: {e}")
            return {}
    
    def _calculate_complexity_metrics(self, model, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """计算复杂度指标"""
        try:
            metrics = {}
            
            # 基于算法类型的复杂度
            algorithm = model_info.get('algorithm', '').lower()
            
            if 'tree' in algorithm or 'forest' in algorithm:
                # 树模型复杂度
                if hasattr(model, 'get_depth'):
                    metrics['model_complexity'] = min(model.get_depth() / 20.0, 1.0)
                elif hasattr(model, 'estimators_'):
                    # 随机森林等集成模型
                    avg_depth = np.mean([tree.get_depth() for tree in model.estimators_])
                    metrics['model_complexity'] = min(avg_depth / 20.0, 1.0)
                else:
                    metrics['model_complexity'] = 0.5
            elif 'linear' in algorithm:
                # 线性模型复杂度较低
                metrics['model_complexity'] = 0.2
            elif 'svm' in algorithm:
                # SVM复杂度中等
                metrics['model_complexity'] = 0.6
            else:
                # 其他模型默认复杂度
                metrics['model_complexity'] = 0.7
            
            # 特征复杂度
            n_features = len(model_info.get('feature_names', []))
            metrics['feature_complexity'] = min(n_features / 100.0, 1.0)
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算复杂度指标失败: {e}")
            return {}
    
    def _calculate_explanation_quality_metrics(self, explanation_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算解释质量指标"""
        try:
            metrics = {}
            
            # SHAP解释质量
            if 'shap_results' in explanation_results:
                shap_results = explanation_results['shap_results']
                if 'global_analysis' in shap_results:
                    global_analysis = shap_results['global_analysis']
                    if 'total_importance' in global_analysis:
                        # 基于总重要性的质量评分
                        total_importance = global_analysis['total_importance']
                        metrics['global_fidelity'] = min(total_importance / 10.0, 1.0)
            
            # LIME解释质量
            if 'lime_results' in explanation_results:
                lime_results = explanation_results['lime_results']
                if 'contribution_analysis' in lime_results:
                    contrib_analysis = lime_results['contribution_analysis']
                    if 'contribution_stats' in contrib_analysis:
                        # 基于贡献统计的质量评分
                        stats = contrib_analysis['contribution_stats']
                        avg_selection_rate = np.mean([
                            stat.get('selection_rate', 0) for stat in stats.values()
                        ])
                        metrics['local_fidelity'] = avg_selection_rate
            
            # 决策可视化质量
            if 'decision_viz_results' in explanation_results:
                viz_results = explanation_results['decision_viz_results']
                if 'tree_info' in viz_results:
                    tree_info = viz_results['tree_info']
                    metrics['approximation_quality'] = tree_info.get('fidelity', 0.0)
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算解释质量指标失败: {e}")
            return {}
    
    def _calculate_stability_metrics(self, explanation_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算稳定性指标"""
        try:
            metrics = {}
            
            # LIME稳定性
            if 'lime_results' in explanation_results:
                lime_results = explanation_results['lime_results']
                if 'consistency_analysis' in lime_results:
                    consistency = lime_results['consistency_analysis']
                    metrics['explanation_stability'] = consistency.get('consistency_score', 0.0)
            
            # 默认稳定性评分
            if 'explanation_stability' not in metrics:
                metrics['explanation_stability'] = 0.7  # 默认中等稳定性
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算稳定性指标失败: {e}")
            return {}
    
    def _calculate_consistency_metrics(self, explanation_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算一致性指标"""
        try:
            metrics = {}
            
            # 方法间一致性
            methods_used = []
            if 'shap_results' in explanation_results:
                methods_used.append('shap')
            if 'lime_results' in explanation_results:
                methods_used.append('lime')
            if 'decision_viz_results' in explanation_results:
                methods_used.append('decision_viz')
            
            # 基于使用方法数量的一致性评分
            if len(methods_used) >= 2:
                metrics['method_consistency'] = 0.8  # 多方法验证
            else:
                metrics['method_consistency'] = 0.6  # 单方法
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算一致性指标失败: {e}")
            return {}
    
    def _calculate_overall_score(self, metrics: Dict[str, Any]) -> float:
        """计算总体可解释性评分"""
        try:
            # 权重配置
            weights = {
                'model_complexity': -0.2,  # 复杂度越低越好
                'feature_complexity': -0.1,
                'global_fidelity': 0.3,
                'local_fidelity': 0.3,
                'approximation_quality': 0.2,
                'explanation_stability': 0.2,
                'method_consistency': 0.1
            }
            
            score = 0.0
            total_weight = 0.0
            
            for metric, weight in weights.items():
                if metric in metrics:
                    if weight < 0:
                        # 复杂度指标：越低越好
                        score += abs(weight) * (1.0 - metrics[metric])
                    else:
                        # 质量指标：越高越好
                        score += weight * metrics[metric]
                    total_weight += abs(weight)
            
            return score / total_weight if total_weight > 0 else 0.5
            
        except Exception as e:
            logger.error(f"计算总体评分失败: {e}")
            return 0.5


class ExplainabilityService:
    """模型可解释性核心服务"""
    
    def __init__(self):
        self.model_loader = ModelLoader()
        self.data_preprocessor = DataPreprocessor()
        self.metrics_calculator = InterpretabilityMetricsCalculator()
    
    async def create_explainability_analysis(
        self,
        user_id: int,
        model_id: int,
        model_type: str,
        X: np.ndarray,
        y: Optional[np.ndarray],
        analysis_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """创建可解释性分析"""
        try:
            # 加载模型
            model, model_info = await self.model_loader.load_model(model_id, model_type, db)
            
            # 预处理数据
            feature_names = model_info.get('feature_names', [])
            X_processed, y_processed, feature_names = self.data_preprocessor.prepare_explanation_data(
                X, y, feature_names, analysis_config.get('sample_size')
            )
            
            # 创建分析记录
            analysis = ExplainabilityAnalysis(
                user_id=user_id,
                model_id=model_id,
                model_type=model_type,
                analysis_name=analysis_config.get('name', f"Analysis_{model_id}"),
                description=analysis_config.get('description', ''),
                analysis_type=analysis_config.get('analysis_type', 'global'),
                explanation_methods=analysis_config.get('methods', ['shap', 'lime']),
                method_configs=analysis_config.get('method_configs', {}),
                dataset_info={
                    'n_samples': len(X_processed),
                    'n_features': len(feature_names),
                    'feature_names': feature_names
                },
                feature_names=feature_names,
                analysis_scope=analysis_config.get('scope', 'sample'),
                sample_size=len(X_processed),
                status="created"
            )
            
            db.add(analysis)
            await db.commit()
            await db.refresh(analysis)
            
            # 异步执行分析
            asyncio.create_task(
                self._run_analysis_async(analysis.id, model, model_info, X_processed, y_processed, analysis_config)
            )
            
            return {
                'success': True,
                'analysis_id': analysis.id,
                'message': '可解释性分析已启动'
            }
            
        except Exception as e:
            logger.error(f"创建可解释性分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _run_analysis_async(
        self,
        analysis_id: int,
        model,
        model_info: Dict[str, Any],
        X: np.ndarray,
        y: Optional[np.ndarray],
        analysis_config: Dict[str, Any]
    ):
        """异步运行分析"""
        from app.core.database import get_db
        
        async with get_db() as db:
            try:
                # 更新状态
                await self._update_analysis_status(analysis_id, 'running', 0, 'initializing', db)
                
                explanation_methods = analysis_config.get('methods', ['shap', 'lime'])
                method_configs = analysis_config.get('method_configs', {})
                feature_names = model_info.get('feature_names', [])
                
                explanation_results = {}
                completed_methods = 0
                total_methods = len(explanation_methods)
                
                # 执行SHAP解释
                if 'shap' in explanation_methods:
                    await self._update_analysis_status(analysis_id, 'running', 20, 'shap_analysis', db)
                    
                    shap_config = method_configs.get('shap', {})
                    shap_result = await shap_explainer_service.explain_model(
                        analysis_id, model, X, feature_names, shap_config, db
                    )
                    explanation_results['shap_results'] = shap_result
                    completed_methods += 1
                
                # 执行LIME解释
                if 'lime' in explanation_methods:
                    progress = 20 + (completed_methods / total_methods) * 60
                    await self._update_analysis_status(analysis_id, 'running', int(progress), 'lime_analysis', db)
                    
                    lime_config = method_configs.get('lime', {})
                    # 需要训练数据用于LIME
                    X_train = X[:min(1000, len(X))]  # 使用部分数据作为训练集
                    X_explain = X[:min(100, len(X))]  # 解释部分实例
                    
                    lime_result = await lime_explainer_service.explain_instances(
                        analysis_id, model, X_train, X_explain, feature_names, lime_config, db
                    )
                    explanation_results['lime_results'] = lime_result
                    completed_methods += 1
                
                # 执行决策可视化
                if 'decision_viz' in explanation_methods:
                    progress = 20 + (completed_methods / total_methods) * 60
                    await self._update_analysis_status(analysis_id, 'running', int(progress), 'decision_visualization', db)
                    
                    viz_config = method_configs.get('decision_viz', {})
                    if y is not None:
                        viz_result = await decision_visualization_service.create_decision_visualization(
                            analysis_id, model, X, y, feature_names, viz_config, db
                        )
                        explanation_results['decision_viz_results'] = viz_result
                    completed_methods += 1
                
                # 计算可解释性指标
                await self._update_analysis_status(analysis_id, 'running', 90, 'calculating_metrics', db)
                
                metrics = self.metrics_calculator.calculate_interpretability_metrics(
                    model, model_info, explanation_results
                )
                
                # 保存指标
                await self._save_interpretability_metrics(analysis_id, model_info, metrics, db)
                
                # 完成分析
                await self._finalize_analysis(analysis_id, explanation_results, db)
                await self._update_analysis_status(analysis_id, 'completed', 100, 'completed', db)
                
                logger.info(f"可解释性分析 {analysis_id} 完成")
                
            except Exception as e:
                logger.error(f"可解释性分析 {analysis_id} 运行失败: {e}")
                await self._update_analysis_status(analysis_id, 'failed', None, f'error: {str(e)}', db)
    
    async def _update_analysis_status(
        self,
        analysis_id: int,
        status: str,
        progress: Optional[int],
        current_method: Optional[str],
        db: AsyncSession
    ):
        """更新分析状态"""
        try:
            from sqlalchemy import update
            
            update_data = {'status': status, 'updated_at': datetime.utcnow()}
            
            if progress is not None:
                update_data['progress'] = progress
            
            if current_method is not None:
                update_data['current_method'] = current_method
            
            if status == 'running':
                update_data['started_at'] = datetime.utcnow()
            elif status in ['completed', 'failed']:
                update_data['completed_at'] = datetime.utcnow()
            
            await db.execute(
                update(ExplainabilityAnalysis)
                .where(ExplainabilityAnalysis.id == analysis_id)
                .values(**update_data)
            )
            await db.commit()
            
        except Exception as e:
            logger.error(f"更新分析状态失败: {e}")
    
    async def _save_interpretability_metrics(
        self,
        analysis_id: int,
        model_info: Dict[str, Any],
        metrics: Dict[str, Any],
        db: AsyncSession
    ):
        """保存可解释性指标"""
        try:
            interpretability_metrics = ModelInterpretabilityMetrics(
                user_id=1,  # 这里应该从上下文获取用户ID
                model_id=model_info['model_id'],
                model_type=model_info.get('model_type', 'unknown'),
                overall_interpretability_score=metrics.get('overall_interpretability_score', 0.0),
                transparency_score=metrics.get('global_fidelity', 0.0),
                comprehensibility_score=metrics.get('local_fidelity', 0.0),
                model_complexity=metrics.get('model_complexity', 0.0),
                feature_complexity=metrics.get('feature_complexity', 0.0),
                explanation_stability=metrics.get('explanation_stability', 0.0),
                method_consistency=metrics.get('method_consistency', 0.0),
                local_fidelity=metrics.get('local_fidelity', 0.0),
                global_fidelity=metrics.get('global_fidelity', 0.0),
                approximation_quality=metrics.get('approximation_quality', 0.0)
            )
            
            db.add(interpretability_metrics)
            await db.commit()
            
        except Exception as e:
            logger.error(f"保存可解释性指标失败: {e}")
    
    async def _finalize_analysis(
        self,
        analysis_id: int,
        explanation_results: Dict[str, Any],
        db: AsyncSession
    ):
        """完成分析"""
        try:
            from sqlalchemy import update
            
            # 统计结果
            has_shap = 'shap_results' in explanation_results
            has_lime = 'lime_results' in explanation_results
            has_viz = 'decision_viz_results' in explanation_results
            
            update_data = {
                'has_global_explanations': has_shap,
                'has_local_explanations': has_lime,
                'has_feature_importance': has_shap or has_viz,
                'has_visualizations': has_viz,
                'completed_methods': sum([has_shap, has_lime, has_viz]),
                'updated_at': datetime.utcnow()
            }
            
            await db.execute(
                update(ExplainabilityAnalysis)
                .where(ExplainabilityAnalysis.id == analysis_id)
                .values(**update_data)
            )
            await db.commit()
            
        except Exception as e:
            logger.error(f"完成分析失败: {e}")
    
    async def get_analysis_results(
        self,
        analysis_id: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取分析结果"""
        try:
            # 获取分析记录
            result = await db.execute(
                select(ExplainabilityAnalysis).where(ExplainabilityAnalysis.id == analysis_id)
            )
            analysis = result.scalar_one_or_none()
            
            if not analysis:
                return {'success': False, 'error': 'Analysis not found'}
            
            # 获取各种解释结果
            results = {
                'analysis_info': {
                    'id': analysis.id,
                    'name': analysis.analysis_name,
                    'status': analysis.status,
                    'progress': analysis.progress,
                    'methods_used': analysis.explanation_methods,
                    'created_at': analysis.created_at.isoformat(),
                    'completed_at': analysis.completed_at.isoformat() if analysis.completed_at else None
                }
            }
            
            # 获取SHAP结果
            if analysis.shap_explanations:
                shap_exp = analysis.shap_explanations[0]  # 取第一个
                results['shap_results'] = {
                    'global_importance': shap_exp.global_feature_importance,
                    'feature_interactions': shap_exp.feature_interactions,
                    'summary_plot_data': shap_exp.summary_plot_data,
                    'waterfall_plots': shap_exp.waterfall_plot_data
                }
            
            # 获取LIME结果
            if analysis.lime_explanations:
                lime_exp = analysis.lime_explanations[0]  # 取第一个
                results['lime_results'] = {
                    'instance_explanations': lime_exp.instance_explanations,
                    'feature_contributions': lime_exp.feature_contributions,
                    'explanation_plots': lime_exp.explanation_plots
                }
            
            # 获取决策可视化结果
            if analysis.decision_trees:
                decision_viz = analysis.decision_trees[0]  # 取第一个
                results['decision_viz_results'] = {
                    'tree_structure': decision_viz.tree_structure,
                    'decision_paths': decision_viz.decision_paths,
                    'feature_usage': decision_viz.feature_usage,
                    'tree_fidelity': decision_viz.tree_fidelity
                }
            
            return {
                'success': True,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return {'success': False, 'error': str(e)}


# 全局可解释性服务实例
explainability_service = ExplainabilityService()
