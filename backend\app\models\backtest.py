"""
回测相关数据模型

定义策略、回测任务、回测结果等数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship

from app.models.base import Base


class Strategy(Base):
    """策略模型"""
    
    __tablename__ = "strategies"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    code = Column(Text, nullable=False)  # 策略代码
    config = Column(JSON)  # 策略配置参数
    nodes = Column(JSON)  # 可视化节点数据
    edges = Column(JSON)  # 节点连接数据
    
    # 策略状态
    status = Column(String(20), default="draft")  # draft, active, inactive, archived
    is_public = Column(Boolean, default=False)  # 是否公开
    
    # 统计信息
    backtest_count = Column(Integer, default=0)  # 回测次数
    avg_return = Column(SQLDecimal(10, 4))  # 平均收益率
    max_drawdown = Column(SQLDecimal(10, 4))  # 最大回撤
    sharpe_ratio = Column(SQLDecimal(10, 4))  # 夏普比率
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="strategies")
    backtests = relationship("BacktestTask", back_populates="strategy", cascade="all, delete-orphan")


class BacktestTask(Base):
    """回测任务模型"""
    
    __tablename__ = "backtest_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    strategy_id = Column(Integer, ForeignKey("strategies.id"), nullable=False, index=True)
    
    # 任务信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 回测参数
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    initial_capital = Column(SQLDecimal(15, 2), nullable=False)  # 初始资金
    benchmark = Column(String(20), default="000300.XSHG")  # 基准指数
    
    # 回测配置
    config = Column(JSON)  # 回测配置参数
    
    # 任务状态
    status = Column(String(20), default="pending")  # pending, running, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 进度百分比
    
    # 执行信息
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    error_message = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="backtest_tasks")
    strategy = relationship("Strategy", back_populates="backtests")
    result = relationship("BacktestResult", back_populates="task", uselist=False, cascade="all, delete-orphan")


class BacktestResult(Base):
    """回测结果模型"""
    
    __tablename__ = "backtest_results"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("backtest_tasks.id"), nullable=False, unique=True, index=True)
    
    # 基本统计
    total_return = Column(SQLDecimal(10, 4))  # 总收益率
    annual_return = Column(SQLDecimal(10, 4))  # 年化收益率
    benchmark_return = Column(SQLDecimal(10, 4))  # 基准收益率
    alpha = Column(SQLDecimal(10, 4))  # 阿尔法
    beta = Column(SQLDecimal(10, 4))  # 贝塔
    
    # 风险指标
    volatility = Column(SQLDecimal(10, 4))  # 波动率
    sharpe_ratio = Column(SQLDecimal(10, 4))  # 夏普比率
    sortino_ratio = Column(SQLDecimal(10, 4))  # 索提诺比率
    max_drawdown = Column(SQLDecimal(10, 4))  # 最大回撤
    max_drawdown_duration = Column(Integer)  # 最大回撤持续天数
    
    # 交易统计
    total_trades = Column(Integer)  # 总交易次数
    winning_trades = Column(Integer)  # 盈利交易次数
    losing_trades = Column(Integer)  # 亏损交易次数
    win_rate = Column(SQLDecimal(10, 4))  # 胜率
    avg_win = Column(SQLDecimal(10, 4))  # 平均盈利
    avg_loss = Column(SQLDecimal(10, 4))  # 平均亏损
    profit_factor = Column(SQLDecimal(10, 4))  # 盈亏比
    
    # 详细数据
    daily_returns = Column(JSON)  # 每日收益率
    portfolio_values = Column(JSON)  # 组合价值序列
    positions = Column(JSON)  # 持仓记录
    trades = Column(JSON)  # 交易记录
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    task = relationship("BacktestTask", back_populates="result")


class BacktestTemplate(Base):
    """回测模板模型"""
    
    __tablename__ = "backtest_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模板信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    category = Column(String(50))  # 模板分类
    
    # 模板配置
    config = Column(JSON, nullable=False)  # 模板配置参数
    
    # 状态
    is_public = Column(Boolean, default=False)  # 是否公开
    is_active = Column(Boolean, default=True)  # 是否启用
    
    # 统计
    usage_count = Column(Integer, default=0)  # 使用次数
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="backtest_templates")
