{"name": "jqdata-frontend", "version": "1.0.0", "description": "JQData量化数据平台前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist", "prepare": "husky install"}, "dependencies": {"next": "^14.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.2", "@ant-design/icons": "^5.2.6", "@ant-design/charts": "^2.0.3", "antd": "^5.12.8", "tailwindcss": "^3.3.6", "@tailwindcss/typography": "^0.5.10", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "socket.io-client": "^4.7.4", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "dnd-kit": "^6.1.0", "react-flow-renderer": "^10.3.17", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "lightweight-charts": "^4.1.3", "d3": "^7.8.5", "framer-motion": "^10.16.16", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "ahooks": "^3.7.8", "classnames": "^2.3.2", "js-cookie": "^3.0.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/lodash-es": "^4.17.12", "@types/js-cookie": "^3.0.6", "@types/uuid": "^9.0.7", "@types/d3": "^7.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "cross-env": "^7.0.3", "rimraf": "^5.0.5", "@next/bundle-analyzer": "^14.0.3", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "sass": "^1.69.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "keywords": ["jqdata", "quantitative", "trading", "finance", "react", "nextjs", "typescript"], "author": "JQData Team", "license": "MIT"}