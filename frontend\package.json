{"name": "jqdata-frontend", "version": "1.0.0", "description": "JQData量化数据平台前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/colors": "^7.0.2", "@ant-design/charts": "^2.0.3", "@ant-design/plots": "^2.0.3", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "@tanstack/react-query": "^5.12.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "swr": "^2.2.4", "dayjs": "^1.11.10", "date-fns": "^2.30.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "ramda": "^0.29.1", "immer": "^10.0.3", "zustand": "^4.4.7", "jotai": "^2.6.0", "recharts": "^2.8.0", "d3": "^7.8.5", "@visx/visx": "^3.6.0", "plotly.js": "^2.27.1", "react-plotly.js": "^2.6.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "lightweight-charts": "^4.1.3", "vis-network": "^9.1.9", "vis-data": "^7.1.9", "cytoscape": "^3.28.1", "react-cytoscapejs": "^2.0.0", "sigma": "^2.4.0", "react-sigma-v2": "^4.0.2", "framer-motion": "^10.16.16", "react-spring": "^9.7.3", "lottie-react": "^2.4.0", "react-beautiful-dnd": "^13.1.1", "react-grid-layout": "^1.4.4", "react-resizable": "^3.0.5", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "dnd-kit": "^6.1.0", "react-flow-renderer": "^10.3.17", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "socket.io-client": "^4.7.4", "ws": "^8.14.2", "classnames": "^2.3.2", "clsx": "^2.0.0", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "js-cookie": "^3.0.5", "uuid": "^9.0.1", "nanoid": "^5.0.4", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "papaparse": "^5.4.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "react-hotkeys-hook": "^4.4.1", "use-debounce": "^10.0.0", "use-local-storage-state": "^19.2.0", "ahooks": "^3.7.8"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/lodash": "^4.14.202", "@types/lodash-es": "^4.17.12", "@types/d3": "^7.4.3", "@types/js-cookie": "^3.0.6", "@types/uuid": "^9.0.7", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.14", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "@storybook/react": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/testing-library": "^0.2.2", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "sass": "^1.69.5", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4", "rimraf": "^5.0.5", "husky": "^8.0.3", "lint-staged": "^15.2.0", "msw": "^2.0.11", "@faker-js/faker": "^8.3.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "keywords": ["jqdata", "quantitative", "trading", "finance", "react", "nextjs", "typescript"], "author": "JQData Team", "license": "MIT"}