#!/bin/bash
# ============================================================================
# 智能量化交易平台 - 后端服务启动脚本 (Linux/Mac)
# ============================================================================

echo "🚀 启动智能量化交易平台后端服务..."

# 检查Python环境
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
echo "✅ Python版本: $PYTHON_VERSION"

# 进入后端目录
cd backend || {
    echo "❌ 错误: 无法进入backend目录"
    exit 1
}

# 检查虚拟环境
echo "📋 检查虚拟环境..."
if [ ! -d "venv" ]; then
    echo "⚠️  虚拟环境不存在，正在创建..."
    python3 -m venv venv
    echo "✅ 虚拟环境创建完成"
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate || {
    echo "❌ 错误: 无法激活虚拟环境"
    exit 1
}

# 检查requirements.txt
if [ -f "requirements.txt" ]; then
    echo "📦 安装Python依赖..."
    pip install -r requirements.txt
else
    echo "⚠️  requirements.txt不存在，安装基础依赖..."
    pip install fastapi uvicorn sqlalchemy pydantic pydantic-settings python-multipart
    pip install jqdatasdk pandas numpy scipy scikit-learn
    pip install python-jose[cryptography] passlib[bcrypt]
    pip install fastapi-users[sqlalchemy]
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  .env文件不存在，请配置环境变量"
    echo "📝 创建示例.env文件..."
    cp .env.example .env 2>/dev/null || true
fi

# 启动服务
echo "🌟 启动FastAPI服务器..."
echo "📍 服务地址: http://localhost:8000"
echo "📖 API文档: http://localhost:8000/docs"
echo "🔄 按 Ctrl+C 停止服务"
echo ""

# 尝试启动主应用
if [ -f "app/main.py" ]; then
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
elif [ -f "app/main_simple.py" ]; then
    echo "⚠️  使用简化版本启动..."
    python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 --reload
else
    echo "❌ 错误: 未找到主应用文件"
    exit 1
fi

echo "👋 后端服务已停止"
