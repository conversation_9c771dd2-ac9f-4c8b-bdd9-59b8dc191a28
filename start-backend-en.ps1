# ============================================================================
# Smart Quantitative Trading Platform - Backend Service Startup Script
# ============================================================================

Write-Host "Starting Smart Quantitative Trading Platform Backend..." -ForegroundColor Green

# Check Python environment
Write-Host "Checking Python environment..." -ForegroundColor Yellow
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: Python not found, please install Python 3.8+" -ForegroundColor Red
    exit 1
}

$pythonVersion = python --version
Write-Host "Python version: $pythonVersion" -ForegroundColor Green

# Enter backend directory
Set-Location backend

# Check virtual environment
Write-Host "Checking virtual environment..." -ForegroundColor Yellow
if (-not (Test-Path "venv")) {
    Write-Host "Virtual environment not found, creating..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host "Virtual environment created" -ForegroundColor Green
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
if (Test-Path "venv\Scripts\Activate.ps1") {
    & "venv\Scripts\Activate.ps1"
} else {
    Write-Host "ERROR: Cannot activate virtual environment" -ForegroundColor Red
    exit 1
}

# Check requirements.txt
if (Test-Path "requirements.txt") {
    Write-Host "Installing Python dependencies..." -ForegroundColor Yellow
    pip install -r requirements.txt
} else {
    Write-Host "requirements.txt not found, installing basic dependencies..." -ForegroundColor Yellow
    pip install fastapi uvicorn sqlalchemy pydantic pydantic-settings python-multipart
    pip install jqdatasdk pandas numpy scipy scikit-learn
    pip install python-jose[cryptography] passlib[bcrypt]
    pip install fastapi-users[sqlalchemy]
}

# Check environment file
if (-not (Test-Path ".env")) {
    Write-Host ".env file not found, please configure environment variables" -ForegroundColor Yellow
    Write-Host "Creating example .env file..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
    }
}

# Start service
Write-Host "Starting FastAPI server..." -ForegroundColor Green
Write-Host "Service URL: http://localhost:8000" -ForegroundColor Cyan
Write-Host "API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop service" -ForegroundColor Yellow
Write-Host ""

# Try to start main application
if (Test-Path "app\main.py") {
    Write-Host "Starting full application..." -ForegroundColor Green
    python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
} elseif (Test-Path "app\main_simple.py") {
    Write-Host "Starting simplified version..." -ForegroundColor Yellow
    python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 --reload
} else {
    Write-Host "ERROR: Main application file not found" -ForegroundColor Red
    exit 1
}

Write-Host "Backend service stopped" -ForegroundColor Yellow
