'use client';

/**
 * 模型可解释性仪表板
 * 
 * 提供SHAP、LIME、决策可视化等模型解释功能的管理界面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  Upload,
  message,
  Descriptions,
  Alert,
  Badge,
  Tooltip,
  List,
  Avatar,
  Timeline,
  Spin,
  Steps,
  Divider,
  Radio,
  Checkbox,
  Slider
} from 'antd';
import {
  ExperimentOutlined,
  EyeOutlined,
  BulbOutlined,
  BarChartOutlined,
  LineChartOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  DownloadOutlined,
  SettingOutlined,
  FileTextOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  Radar<PERSON>hartOutlined,
  <PERSON>MapOutlined,
  ScatterPlotOutlined
} from '@ant-design/icons';
import { Line, Column, Radar, Heatmap, Scatter } from '@ant-design/plots';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { Step } = Steps;
const { TextArea } = Input;
const { Dragger } = Upload;

interface ExplainabilityAnalysis {
  id: number;
  name: string;
  description: string;
  model_id: number;
  model_type: string;
  analysis_type: string;
  status: string;
  progress: number;
  current_method: string;
  explanation_methods: string[];
  has_global_explanations: boolean;
  has_local_explanations: boolean;
  has_visualizations: boolean;
  created_at: string;
  completed_at?: string;
}

interface InterpretabilityMetrics {
  id: number;
  model_id: number;
  model_type: string;
  overall_interpretability_score: number;
  transparency_score: number;
  comprehensibility_score: number;
  model_complexity: number;
  explanation_stability: number;
  method_consistency: number;
  calculated_at: string;
}

export const ExplainabilityDashboard: React.FC = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('analyses');
  const [analyses, setAnalyses] = useState<ExplainabilityAnalysis[]>([]);
  const [metrics, setMetrics] = useState<InterpretabilityMetrics[]>([]);
  const [selectedAnalysis, setSelectedAnalysis] = useState<ExplainabilityAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [quickExplainModalVisible, setQuickExplainModalVisible] = useState(false);
  const [quickExplainType, setQuickExplainType] = useState<'shap' | 'lime'>('shap');

  useEffect(() => {
    fetchAnalyses();
  }, []);

  useEffect(() => {
    if (activeTab === 'metrics') {
      fetchMetrics();
    }
  }, [activeTab]);

  const fetchAnalyses = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/explainability/analyses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setAnalyses(result.data.items);
      }
    } catch (error) {
      console.error('获取可解释性分析列表失败:', error);
      message.error('获取可解释性分析列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/v1/explainability/metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setMetrics(result.data.items);
      }
    } catch (error) {
      console.error('获取可解释性指标失败:', error);
      message.error('获取可解释性指标失败');
    }
  };

  const handleCreateAnalysis = async (values: any) => {
    try {
      const formData = new FormData();
      
      // 添加基本字段
      Object.keys(values).forEach(key => {
        if (key !== 'data_file' && values[key] !== undefined) {
          if (typeof values[key] === 'object') {
            formData.append(key, JSON.stringify(values[key]));
          } else {
            formData.append(key, values[key]);
          }
        }
      });
      
      // 添加文件
      if (values.data_file && values.data_file.length > 0) {
        formData.append('data_file', values.data_file[0].originFileObj);
      }

      const response = await fetch('/api/v1/explainability/analyses', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('可解释性分析创建成功');
        setCreateModalVisible(false);
        form.resetFields();
        fetchAnalyses();
      } else {
        message.error(result.message || '可解释性分析创建失败');
      }
    } catch (error) {
      console.error('创建可解释性分析失败:', error);
      message.error('创建可解释性分析失败');
    }
  };

  const handleQuickExplain = async (values: any) => {
    try {
      const endpoint = quickExplainType === 'shap' 
        ? '/api/v1/explainability/quick-explain/shap'
        : '/api/v1/explainability/quick-explain/lime';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success(`快速${quickExplainType.toUpperCase()}解释已启动`);
        setQuickExplainModalVisible(false);
        fetchAnalyses();
      } else {
        message.error(result.message || '快速解释失败');
      }
    } catch (error) {
      console.error('快速解释失败:', error);
      message.error('快速解释失败');
    }
  };

  const handleViewAnalysisDetails = async (analysis: ExplainabilityAnalysis) => {
    try {
      const response = await fetch(`/api/v1/explainability/analyses/${analysis.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setSelectedAnalysis({ ...analysis, results: result.data });
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取分析详情失败:', error);
      message.error('获取分析详情失败');
    }
  };

  const handleDeleteAnalysis = async (analysisId: number) => {
    try {
      const response = await fetch(`/api/v1/explainability/analyses/${analysisId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('分析删除成功');
        fetchAnalyses();
      } else {
        message.error(result.message || '分析删除失败');
      }
    } catch (error) {
      console.error('删除分析失败:', error);
      message.error('删除分析失败');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderMethodTags = (methods: string[]) => {
    const methodColors = {
      'shap': 'blue',
      'lime': 'green',
      'decision_viz': 'orange'
    };

    return methods.map(method => (
      <Tag key={method} color={methodColors[method] || 'default'}>
        {method.toUpperCase()}
      </Tag>
    ));
  };

  const renderInterpretabilityRadar = (metric: InterpretabilityMetrics) => {
    const data = [
      { item: '总体可解释性', score: metric.overall_interpretability_score * 100 },
      { item: '透明度', score: metric.transparency_score * 100 },
      { item: '可理解性', score: metric.comprehensibility_score * 100 },
      { item: '模型复杂度', score: (1 - metric.model_complexity) * 100 }, // 复杂度越低越好
      { item: '解释稳定性', score: metric.explanation_stability * 100 },
      { item: '方法一致性', score: metric.method_consistency * 100 }
    ];

    const config = {
      data,
      xField: 'item',
      yField: 'score',
      area: {},
      point: {
        size: 2,
      },
      line: {
        color: '#1890ff',
      },
    };

    return <Radar {...config} height={300} />;
  };

  const analysisColumns = [
    {
      title: '分析名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ExplainabilityAnalysis) => (
        <Space>
          {getStatusIcon(record.status)}
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">模型ID: {record.model_id}</div>
          </div>
        </Space>
      )
    },
    {
      title: '模型类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      )
    },
    {
      title: '分析类型',
      dataIndex: 'analysis_type',
      key: 'analysis_type',
      render: (type: string) => (
        <Tag color="purple">{type}</Tag>
      )
    },
    {
      title: '解释方法',
      dataIndex: 'explanation_methods',
      key: 'explanation_methods',
      render: (methods: string[]) => renderMethodTags(methods)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: ExplainabilityAnalysis) => (
        <div>
          <Tag color={getStatusColor(status)}>{status}</Tag>
          {status === 'running' && (
            <div>
              <Progress 
                percent={record.progress} 
                size="small" 
                style={{ width: 100, marginTop: 4 }}
              />
              <div className="text-xs text-gray-500">{record.current_method}</div>
            </div>
          )}
        </div>
      )
    },
    {
      title: '结果',
      key: 'results',
      render: (_, record: ExplainabilityAnalysis) => (
        <Space>
          {record.has_global_explanations && <Tag color="blue">全局</Tag>}
          {record.has_local_explanations && <Tag color="green">局部</Tag>}
          {record.has_visualizations && <Tag color="orange">可视化</Tag>}
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: ExplainabilityAnalysis) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewAnalysisDetails(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个分析吗？此操作不可恢复。',
                onOk: () => handleDeleteAnalysis(record.id)
              });
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  const metricsColumns = [
    {
      title: '模型',
      key: 'model',
      render: (_, record: InterpretabilityMetrics) => (
        <div>
          <div className="font-medium">模型 {record.model_id}</div>
          <Tag color="blue">{record.model_type}</Tag>
        </div>
      )
    },
    {
      title: '总体评分',
      dataIndex: 'overall_interpretability_score',
      key: 'overall_interpretability_score',
      render: (score: number) => (
        <div>
          <Progress 
            percent={score * 100} 
            size="small" 
            strokeColor={score > 0.7 ? '#52c41a' : score > 0.4 ? '#faad14' : '#ff4d4f'}
          />
          <span className="text-sm">{(score * 100).toFixed(1)}%</span>
        </div>
      ),
      sorter: (a: InterpretabilityMetrics, b: InterpretabilityMetrics) => 
        a.overall_interpretability_score - b.overall_interpretability_score
    },
    {
      title: '透明度',
      dataIndex: 'transparency_score',
      key: 'transparency_score',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          showInfo={false}
          strokeColor="#1890ff"
        />
      )
    },
    {
      title: '可理解性',
      dataIndex: 'comprehensibility_score',
      key: 'comprehensibility_score',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          showInfo={false}
          strokeColor="#52c41a"
        />
      )
    },
    {
      title: '稳定性',
      dataIndex: 'explanation_stability',
      key: 'explanation_stability',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          showInfo={false}
          strokeColor="#722ed1"
        />
      )
    },
    {
      title: '计算时间',
      dataIndex: 'calculated_at',
      key: 'calculated_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: InterpretabilityMetrics) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<RadarChartOutlined />}
            onClick={() => {
              Modal.info({
                title: '可解释性雷达图',
                width: 600,
                content: renderInterpretabilityRadar(record)
              });
            }}
          >
            雷达图
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">模型可解释性</h1>
          <p className="text-gray-600">SHAP、LIME、决策可视化等模型解释功能</p>
        </div>
        <Space>
          <Button
            icon={<ThunderboltOutlined />}
            onClick={() => setQuickExplainModalVisible(true)}
          >
            快速解释
          </Button>
          <Button
            type="primary"
            icon={<ExperimentOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建分析
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总分析数"
              value={analyses.length}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={analyses.filter(a => a.status === 'running').length}
              prefix={<LoadingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={analyses.filter(a => a.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={metrics.length > 0 ? 
                (metrics.reduce((sum, m) => sum + m.overall_interpretability_score, 0) / metrics.length * 100).toFixed(1) : 0
              }
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Badge count={analyses.length} size="small">
                <Space>
                  <ExperimentOutlined />
                  <span>可解释性分析</span>
                </Space>
              </Badge>
            }
            key="analyses"
          >
            <Table
              columns={analysisColumns}
              dataSource={analyses}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个分析`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={metrics.length} size="small">
                <Space>
                  <BarChartOutlined />
                  <span>可解释性指标</span>
                </Space>
              </Badge>
            }
            key="metrics"
          >
            <Table
              columns={metricsColumns}
              dataSource={metrics}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个指标`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建分析弹窗 */}
      <Modal
        title="创建可解释性分析"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateAnalysis}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="分析名称" rules={[{ required: true }]}>
                <Input placeholder="请输入分析名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="model_id" label="模型ID" rules={[{ required: true }]}>
                <InputNumber placeholder="请输入模型ID" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="分析描述">
            <TextArea rows={3} placeholder="请输入分析描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="model_type" label="模型类型" rules={[{ required: true }]}>
                <Select placeholder="选择模型类型">
                  <Option value="ml_model">机器学习模型</Option>
                  <Option value="automl_model">AutoML模型</Option>
                  <Option value="transformer_model">Transformer模型</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="analysis_type" label="分析类型" initialValue="global">
                <Select>
                  <Option value="global">全局分析</Option>
                  <Option value="local">局部分析</Option>
                  <Option value="feature_importance">特征重要性</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="sample_size" label="样本大小" initialValue={1000}>
                <InputNumber min={10} max={10000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="methods" label="解释方法" initialValue={['shap', 'lime']}>
            <Checkbox.Group>
              <Checkbox value="shap">SHAP</Checkbox>
              <Checkbox value="lime">LIME</Checkbox>
              <Checkbox value="decision_viz">决策可视化</Checkbox>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item name="use_uploaded_data" label="使用上传数据" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.use_uploaded_data !== currentValues.use_uploaded_data
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('use_uploaded_data') ? (
                <Form.Item name="data_file" label="数据文件">
                  <Dragger
                    accept=".csv,.xlsx"
                    beforeUpload={() => false}
                    maxCount={1}
                  >
                    <p className="ant-upload-drag-icon">
                      <UploadOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                    <p className="ant-upload-hint">支持CSV和Excel文件</p>
                  </Dragger>
                </Form.Item>
              ) : null
            }
          </Form.Item>
        </Form>
      </Modal>

      {/* 快速解释弹窗 */}
      <Modal
        title="快速模型解释"
        open={quickExplainModalVisible}
        onCancel={() => setQuickExplainModalVisible(false)}
        onOk={() => {
          const values = {
            model_id: 1, // 示例值
            model_type: 'ml_model',
            ...(quickExplainType === 'shap' ? {
              explainer_type: 'auto',
              max_samples: 1000
            } : {
              explainer_type: 'tabular',
              num_features: 10,
              num_samples: 5000,
              max_instances: 100
            })
          };
          handleQuickExplain(values);
        }}
        width={600}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">解释方法</label>
            <Radio.Group 
              value={quickExplainType} 
              onChange={(e) => setQuickExplainType(e.target.value)}
            >
              <Radio.Button value="shap">SHAP</Radio.Button>
              <Radio.Button value="lime">LIME</Radio.Button>
            </Radio.Group>
          </div>

          <Alert
            message={`${quickExplainType.toUpperCase()}解释说明`}
            description={
              quickExplainType === 'shap' 
                ? 'SHAP提供全局和局部特征重要性解释，适用于理解模型整体行为'
                : 'LIME提供局部可解释性，通过扰动输入来解释单个预测'
            }
            type="info"
            showIcon
          />

          <div>
            <label className="block text-sm font-medium mb-2">模型ID</label>
            <InputNumber placeholder="请输入模型ID" style={{ width: '100%' }} />
          </div>

          {quickExplainType === 'shap' ? (
            <div>
              <label className="block text-sm font-medium mb-2">最大样本数</label>
              <Slider min={100} max={5000} defaultValue={1000} />
            </div>
          ) : (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium mb-2">解释特征数量</label>
                <Slider min={5} max={20} defaultValue={10} />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">最大实例数</label>
                <Slider min={10} max={500} defaultValue={100} />
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* 分析详情弹窗 */}
      <Modal
        title="分析详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={1000}
      >
        {selectedAnalysis && (
          <div className="space-y-4">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="分析名称">{selectedAnalysis.name}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(selectedAnalysis.status)}>
                  {selectedAnalysis.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="模型类型">{selectedAnalysis.model_type}</Descriptions.Item>
              <Descriptions.Item label="分析类型">{selectedAnalysis.analysis_type}</Descriptions.Item>
              <Descriptions.Item label="解释方法" span={2}>
                {renderMethodTags(selectedAnalysis.explanation_methods)}
              </Descriptions.Item>
            </Descriptions>

            {selectedAnalysis.status === 'running' && (
              <div>
                <h4 className="mb-2">分析进度</h4>
                <Progress percent={selectedAnalysis.progress} />
                <p className="text-sm text-gray-500 mt-1">
                  当前阶段: {selectedAnalysis.current_method}
                </p>
              </div>
            )}

            {selectedAnalysis.status === 'completed' && (
              <div>
                <h4 className="mb-2">分析结果</h4>
                <Row gutter={16}>
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="全局解释"
                        value={selectedAnalysis.has_global_explanations ? '已生成' : '未生成'}
                        prefix={<BulbOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="局部解释"
                        value={selectedAnalysis.has_local_explanations ? '已生成' : '未生成'}
                        prefix={<NodeIndexOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="可视化"
                        value={selectedAnalysis.has_visualizations ? '已生成' : '未生成'}
                        prefix={<BarChartOutlined />}
                      />
                    </Card>
                  </Col>
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};
