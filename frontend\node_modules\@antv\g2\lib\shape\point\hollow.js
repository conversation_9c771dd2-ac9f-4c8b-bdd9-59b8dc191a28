"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowPoint = void 0;
const color_1 = require("./color");
/**
 * ○
 */
const HollowPoint = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'point' }, options), context);
};
exports.HollowPoint = HollowPoint;
exports.HollowPoint.props = Object.assign({ defaultMarker: 'hollowPoint' }, color_1.Color.props);
//# sourceMappingURL=hollow.js.map