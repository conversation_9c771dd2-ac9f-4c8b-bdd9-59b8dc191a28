"""
风险管理相关数据模型

定义风险指标、风险预警、止损策略等数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Numeric, Boolean, ForeignKey, JSON, Float
from sqlalchemy.orm import relationship

from app.models.base import Base


class RiskProfile(Base):
    """风险档案模型"""
    
    __tablename__ = "risk_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 风险偏好设置
    risk_tolerance = Column(String(20), default="moderate")  # conservative, moderate, aggressive
    max_position_size = Column(Numeric(5, 4), default=0.1)  # 单个持仓最大占比
    max_sector_exposure = Column(Numeric(5, 4), default=0.3)  # 单个行业最大暴露
    max_drawdown_limit = Column(Numeric(5, 4), default=0.2)  # 最大回撤限制
    
    # VaR设置
    var_confidence_level = Column(Numeric(5, 4), default=0.95)  # VaR置信水平
    var_time_horizon = Column(Integer, default=1)  # VaR时间窗口（天）
    var_method = Column(String(20), default="historical")  # historical, parametric, monte_carlo
    
    # 止损设置
    stop_loss_enabled = Column(Boolean, default=True)
    stop_loss_percentage = Column(Numeric(5, 4), default=0.05)  # 止损百分比
    trailing_stop_enabled = Column(Boolean, default=False)
    trailing_stop_percentage = Column(Numeric(5, 4), default=0.03)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用
    risk_alerts = relationship("RiskAlert", back_populates="risk_profile", cascade="all, delete-orphan")


class RiskMetrics(Base):
    """风险指标模型"""
    
    __tablename__ = "risk_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    portfolio_id = Column(Integer, nullable=True)  # 投资组合ID（预留）
    
    # 计算日期
    calculation_date = Column(DateTime, nullable=False, index=True)
    
    # VaR指标
    var_1day = Column(Numeric(15, 2))  # 1日VaR
    var_5day = Column(Numeric(15, 2))  # 5日VaR
    var_10day = Column(Numeric(15, 2))  # 10日VaR
    cvar_1day = Column(Numeric(15, 2))  # 1日条件VaR
    
    # 波动率指标
    portfolio_volatility = Column(Numeric(10, 6))  # 组合波动率
    realized_volatility = Column(Numeric(10, 6))  # 已实现波动率
    implied_volatility = Column(Numeric(10, 6))  # 隐含波动率
    
    # 相关性指标
    market_beta = Column(Numeric(10, 6))  # 市场贝塔
    market_correlation = Column(Numeric(10, 6))  # 市场相关性
    diversification_ratio = Column(Numeric(10, 6))  # 分散化比率
    
    # 回撤指标
    current_drawdown = Column(Numeric(10, 6))  # 当前回撤
    max_drawdown = Column(Numeric(10, 6))  # 最大回撤
    drawdown_duration = Column(Integer)  # 回撤持续天数
    
    # 集中度指标
    concentration_hhi = Column(Numeric(10, 6))  # HHI集中度指数
    top5_concentration = Column(Numeric(10, 6))  # 前5大持仓集中度
    sector_concentration = Column(JSON)  # 行业集中度分布
    
    # 流动性指标
    liquidity_score = Column(Numeric(10, 6))  # 流动性评分
    avg_daily_volume = Column(Numeric(15, 2))  # 平均日成交量
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用


class RiskAlert(Base):
    """风险预警模型"""
    
    __tablename__ = "risk_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    risk_profile_id = Column(Integer, ForeignKey("risk_profiles.id"), nullable=False, index=True)
    
    # 预警信息
    alert_type = Column(String(50), nullable=False)  # var_breach, drawdown_limit, concentration, etc.
    severity = Column(String(20), default="medium")  # low, medium, high, critical
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    
    # 预警数据
    current_value = Column(Numeric(15, 6))  # 当前值
    threshold_value = Column(Numeric(15, 6))  # 阈值
    breach_percentage = Column(Numeric(10, 6))  # 突破百分比
    
    # 相关资产
    symbol = Column(String(20))  # 相关股票代码
    sector = Column(String(50))  # 相关行业
    
    # 状态管理
    status = Column(String(20), default="active")  # active, acknowledged, resolved
    acknowledged_at = Column(DateTime)
    resolved_at = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用
    risk_profile = relationship("RiskProfile", back_populates="risk_alerts")


class StopLossOrder(Base):
    """止损订单模型"""
    
    __tablename__ = "stop_loss_orders"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 订单信息
    symbol = Column(String(20), nullable=False, index=True)
    order_type = Column(String(20), default="stop_loss")  # stop_loss, trailing_stop
    quantity = Column(Integer, nullable=False)
    
    # 价格设置
    trigger_price = Column(Numeric(10, 4), nullable=False)  # 触发价格
    stop_price = Column(Numeric(10, 4), nullable=False)  # 止损价格
    original_price = Column(Numeric(10, 4))  # 原始买入价格
    
    # 追踪止损设置
    trailing_amount = Column(Numeric(10, 4))  # 追踪金额
    trailing_percentage = Column(Numeric(5, 4))  # 追踪百分比
    highest_price = Column(Numeric(10, 4))  # 最高价格（用于追踪）
    
    # 订单状态
    status = Column(String(20), default="active")  # active, triggered, cancelled, expired
    triggered_at = Column(DateTime)
    executed_at = Column(DateTime)
    execution_price = Column(Numeric(10, 4))
    
    # 风险管理
    max_loss_amount = Column(Numeric(15, 2))  # 最大损失金额
    risk_reward_ratio = Column(Numeric(10, 4))  # 风险收益比
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = Column(DateTime)  # 过期时间
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用


class RiskScenario(Base):
    """风险情景分析模型"""
    
    __tablename__ = "risk_scenarios"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 情景信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    scenario_type = Column(String(50))  # stress_test, monte_carlo, historical
    
    # 情景参数
    market_shock = Column(Numeric(10, 6))  # 市场冲击幅度
    volatility_multiplier = Column(Numeric(10, 6))  # 波动率倍数
    correlation_adjustment = Column(Numeric(10, 6))  # 相关性调整
    time_horizon = Column(Integer)  # 时间窗口（天）
    
    # 情景结果
    scenario_results = Column(JSON)  # 情景分析结果
    portfolio_pnl = Column(Numeric(15, 2))  # 组合损益
    worst_case_loss = Column(Numeric(15, 2))  # 最坏情况损失
    probability = Column(Numeric(10, 6))  # 发生概率
    
    # 状态
    status = Column(String(20), default="draft")  # draft, running, completed, failed
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系 - 移除back_populates避免循环引用错误
    user = relationship("User")  # 移除back_populates，避免User模型中未定义的关系引用
