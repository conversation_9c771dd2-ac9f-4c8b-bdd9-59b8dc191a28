"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/jqdata/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/settings/jqdata/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JQDataConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/radio/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Radio,Row,Space,Spin,Statistic,Steps,Tooltip,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/descriptions/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MobileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MailOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined,MailOutlined,MobileOutlined,QuestionCircleOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * JQData配置页面\n * \n * 用户配置JQData账号、查看配额使用情况、测试连接等\n */ \n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction JQDataConfigPage() {\n    var _testResult_quotaInfo_quotaUsageRate;\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testLoading, setTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTestModal, setShowTestModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"mobile\");\n    // 验证用户名格式\n    const validateUsername = (rule, value)=>{\n        if (!value) {\n            return Promise.reject(new Error(\"请输入用户名\"));\n        }\n        if (loginType === \"email\") {\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            if (!emailRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的邮箱地址\"));\n            }\n        } else if (loginType === \"mobile\") {\n            const mobileRegex = /^1[3-9]\\d{9}$/;\n            if (!mobileRegex.test(value)) {\n                return Promise.reject(new Error(\"请输入有效的手机号码\"));\n            }\n        }\n        return Promise.resolve();\n    };\n    // 加载JQData配置\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/api/v1/jqdata/config\");\n            if (response.code === 200 && response.data) {\n                setConfig(response.data);\n                const configLoginType = response.data.loginType || \"mobile\";\n                setLoginType(configLoginType);\n                form.setFieldsValue({\n                    username: response.data.username,\n                    loginType: configLoginType\n                });\n                setCurrentStep(2); // 已配置\n            } else {\n                setCurrentStep(0); // 未配置\n            }\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                setCurrentStep(0); // 未配置\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"加载配置失败\");\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 保存配置\n    const handleSave = async (values)=>{\n        try {\n            setLoading(true);\n            setCurrentStep(1); // 配置中\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/config\", {\n                username: values.username,\n                password: values.password,\n                loginType: values.loginType || loginType\n            });\n            if (response.code === 200) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"JQData配置保存成功！\");\n                setConfig(response.data);\n                setCurrentStep(2); // 配置完成\n                form.resetFields([\n                    \"password\"\n                ]);\n            } else {\n                throw new Error(response.message);\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"配置保存失败\");\n            setCurrentStep(0); // 回到未配置状态\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试连接\n    const handleTestConnection = async ()=>{\n        try {\n            setTestLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/api/v1/jqdata/test-connection\");\n            setTestResult(response.data);\n            setShowTestModal(true);\n            if (response.data.success) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"连接测试成功！\");\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"连接测试失败\");\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"测试连接失败\");\n            setTestResult({\n                success: false,\n                message: \"测试连接失败\",\n                errorDetails: error.message\n            });\n            setShowTestModal(true);\n        } finally{\n            setTestLoading(false);\n        }\n    };\n    // 删除配置\n    const handleDelete = ()=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].confirm({\n            title: \"确认删除配置\",\n            content: \"删除后将无法获取JQData数据，确定要删除吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            onOk: async ()=>{\n                try {\n                    await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/api/v1/jqdata/config\");\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"配置删除成功\");\n                    setConfig(null);\n                    setCurrentStep(0);\n                    form.resetFields();\n                } catch (error) {\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"删除配置失败\");\n                }\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // 配置步骤\n    const steps = [\n        {\n            title: \"配置账号\",\n            description: \"输入JQData账号信息\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"验证连接\",\n            description: \"验证账号有效性\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"配置完成\",\n            description: \"开始使用JQData服务\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 234,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    if (loading && !config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: \"JQData配置\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        children: \"配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    current: currentStep,\n                    items: steps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData已配置\",\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"账号: \",\n                                config.username\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"登录方式: \",\n                                config.loginType === \"mobile\" ? \"手机号\" : \"邮箱\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"状态: \",\n                                config.isActive ? \"正常\" : \"异常\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 13\n                }, void 0),\n                type: config.isActive ? \"success\" : \"warning\",\n                showIcon: true,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: handleTestConnection,\n                            loading: testLoading,\n                            children: \"测试连接\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: loadConfig,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请配置您的JQData账号以获取实时市场数据，支持邮箱或手机号登录\",\n                type: \"info\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"账号配置\",\n                            extra: config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                onClick: handleDelete,\n                                children: \"删除配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    form: form,\n                                    layout: \"vertical\",\n                                    onFinish: handleSave,\n                                    disabled: loading,\n                                    initialValues: {\n                                        loginType: \"mobile\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"loginType\",\n                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"登录方式\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        title: \"JQData主要支持手机号登录，部分老用户支持邮箱登录\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"ml-1 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Group, {\n                                                value: loginType,\n                                                onChange: (e)=>{\n                                                    setLoginType(e.target.value);\n                                                    form.setFieldsValue({\n                                                        username: \"\"\n                                                    }); // 清空用户名\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Button, {\n                                                        value: \"mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 手机号登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Button, {\n                                                        value: \"email\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" 邮箱登录\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"username\",\n                                            label: loginType === \"email\" ? \"JQData邮箱\" : \"JQData手机号\",\n                                            rules: [\n                                                {\n                                                    validator: validateUsername\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                placeholder: loginType === \"email\" ? \"请输入JQData注册邮箱\" : \"请输入JQData注册手机号\",\n                                                prefix: loginType === \"email\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 51\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 70\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"password\",\n                                            label: \"JQData密码\",\n                                            rules: [\n                                                {\n                                                    required: !config,\n                                                    message: \"请输入JQData密码\"\n                                                },\n                                                {\n                                                    min: 6,\n                                                    message: \"密码长度至少6位\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Password, {\n                                                placeholder: config ? \"留空表示不修改密码\" : \"请输入JQData密码\",\n                                                iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 31\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 48\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        type: \"primary\",\n                                                        htmlType: \"submit\",\n                                                        loading: loading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        children: config ? \"更新配置\" : \"保存配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        onClick: handleTestConnection,\n                                                        loading: testLoading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_MailOutlined_MobileOutlined_QuestionCircleOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: \"测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"配置说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 支持邮箱或手机号登录，请选择您在JQData注册时使用的方式\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 邮箱格式：<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 手机号格式：13812345678（中国大陆手机号）\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 密码将被加密存储，确保账号安全\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 配置后可获取实时股票数据和历史数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 如遇问题请检查账号状态或联系客服\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mt-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"配额使用情况\",\n                            children: config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        gutter: 16,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    title: \"总配额\",\n                                                    value: config.quotaTotal,\n                                                    suffix: \"次\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    title: \"已使用\",\n                                                    value: config.quotaUsed,\n                                                    suffix: \"次\",\n                                                    valueStyle: {\n                                                        color: config.quotaUsed / config.quotaTotal > 0.8 ? \"#ff4d4f\" : \"#1890ff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        children: \"使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        strong: true,\n                                                        children: [\n                                                            (config.quotaUsed / config.quotaTotal * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                percent: config.quotaUsed / config.quotaTotal * 100,\n                                                status: config.quotaUsed / config.quotaTotal > 0.9 ? \"exception\" : \"active\",\n                                                strokeColor: {\n                                                    \"0%\": \"#108ee9\",\n                                                    \"100%\": \"#87d068\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        size: \"small\",\n                                        column: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"剩余配额\",\n                                                children: [\n                                                    config.quotaRemaining,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"总调用次数\",\n                                                children: [\n                                                    config.totalApiCalls,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"最后使用\",\n                                                children: config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : \"未使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                                label: \"配额重置\",\n                                                children: config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : \"未知\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this),\n                                    config.authFailureCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        message: \"认证失败 \".concat(config.authFailureCount, \" 次\"),\n                                        description: config.lastAuthError,\n                                        type: \"warning\",\n                                        showIcon: true,\n                                        className: \"mt-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"配置JQData账号后查看配额信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                title: \"连接测试结果\",\n                open: showTestModal,\n                onCancel: ()=>setShowTestModal(false),\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>setShowTestModal(false),\n                        children: \"关闭\"\n                    }, \"close\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: testResult.success ? \"连接成功\" : \"连接失败\",\n                            description: testResult.message,\n                            type: testResult.success ? \"success\" : \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, this),\n                        testResult.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"响应时间: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: [\n                                        testResult.responseTime.toFixed(3),\n                                        \"秒\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 15\n                        }, this),\n                        testResult.quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            title: \"配额信息\",\n                            size: \"small\",\n                            column: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"总配额\",\n                                    children: testResult.quotaInfo.quotaTotal\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"已使用\",\n                                    children: testResult.quotaInfo.quotaUsed\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"剩余\",\n                                    children: testResult.quotaInfo.quotaRemaining\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"].Item, {\n                                    label: \"使用率\",\n                                    children: [\n                                        (_testResult_quotaInfo_quotaUsageRate = testResult.quotaInfo.quotaUsageRate) === null || _testResult_quotaInfo_quotaUsageRate === void 0 ? void 0 : _testResult_quotaInfo_quotaUsageRate.toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 15\n                        }, this),\n                        testResult.errorDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: \"错误详情\",\n                            description: testResult.errorDetails,\n                            type: \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 496,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(JQDataConfigPage, \"V82JzUSL3BnaQIhMi/vEoYYC4xw=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Radio_Row_Space_Spin_Statistic_Steps_Tooltip_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = JQDataConfigPage;\nvar _c;\n$RefreshReg$(_c, \"JQDataConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx\n"));

/***/ })

});