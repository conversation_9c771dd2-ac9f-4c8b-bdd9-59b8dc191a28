"""
JQData相关数据模式

定义JQData配置、配额管理等相关的请求和响应模式
"""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, validator
import re


class JQDataConfigRequest(BaseModel):
    """JQData配置请求"""

    username: str = Field(..., description="JQData用户名(邮箱或手机号)")
    password: str = Field(..., min_length=6, max_length=128, description="JQData密码")
    login_type: Optional[str] = Field("email", description="登录类型: email 或 mobile")

    @validator('username')
    def validate_username(cls, v, values):
        """验证用户名格式"""
        login_type = values.get('login_type', 'email')

        if login_type == 'email':
            # 验证邮箱格式
            email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
            if not re.match(email_pattern, v):
                raise ValueError('请输入有效的邮箱地址')
        elif login_type == 'mobile':
            # 验证中国大陆手机号格式
            mobile_pattern = r'^1[3-9]\d{9}$'
            if not re.match(mobile_pattern, v):
                raise ValueError('请输入有效的手机号码')
        else:
            raise ValueError('登录类型必须是 email 或 mobile')

        return v


class JQDataConfigResponse(BaseModel):
    """JQData配置响应"""

    id: int = Field(..., description="配置ID")
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="JQData用户名")
    login_type: Optional[str] = Field("email", description="登录类型")
    is_active: bool = Field(..., description="是否启用")
    quota_total: int = Field(..., description="总配额")
    quota_used: int = Field(..., description="已使用配额")
    quota_remaining: int = Field(..., description="剩余配额")
    quota_reset_date: Optional[datetime] = Field(None, description="配额重置日期")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    total_api_calls: int = Field(..., description="总API调用次数")
    last_auth_success: Optional[datetime] = Field(None, description="最后认证成功时间")
    last_auth_error: Optional[str] = Field(None, description="最后认证错误")
    auth_failure_count: int = Field(..., description="认证失败次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    @validator('quota_remaining', pre=True, always=True)
    def calculate_quota_remaining(cls, v, values):
        """计算剩余配额"""
        if 'quota_total' in values and 'quota_used' in values:
            return values['quota_total'] - values['quota_used']
        return v
    
    class Config:
        from_attributes = True


class JQDataQuotaResponse(BaseModel):
    """JQData配额响应"""
    
    configured: bool = Field(..., description="是否已配置")
    quota_total: Optional[int] = Field(None, description="总配额")
    quota_used: Optional[int] = Field(None, description="已使用配额")
    quota_remaining: Optional[int] = Field(None, description="剩余配额")
    quota_usage_rate: Optional[float] = Field(None, description="配额使用率")
    quota_reset_date: Optional[datetime] = Field(None, description="配额重置日期")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    total_api_calls: Optional[int] = Field(None, description="总API调用次数")
    is_active: Optional[bool] = Field(None, description="是否启用")
    
    @validator('quota_usage_rate', pre=True, always=True)
    def calculate_usage_rate(cls, v, values):
        """计算配额使用率"""
        if values.get('configured') and values.get('quota_total'):
            quota_used = values.get('quota_used', 0)
            quota_total = values['quota_total']
            return round((quota_used / quota_total) * 100, 2)
        return None


class JQDataTestConnectionResponse(BaseModel):
    """JQData连接测试响应"""
    
    success: bool = Field(..., description="连接是否成功")
    message: str = Field(..., description="测试结果消息")
    quota_info: Optional[dict] = Field(None, description="配额信息")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")
    error_details: Optional[str] = Field(None, description="错误详情")


class JQDataConfigUpdateRequest(BaseModel):
    """JQData配置更新请求"""
    
    username: Optional[EmailStr] = Field(None, description="JQData用户名(邮箱)")
    password: Optional[str] = Field(None, min_length=6, max_length=128, description="JQData密码")
    is_active: Optional[bool] = Field(None, description="是否启用")


class JQDataUsageStatsResponse(BaseModel):
    """JQData使用统计响应"""
    
    today_calls: int = Field(..., description="今日调用次数")
    week_calls: int = Field(..., description="本周调用次数")
    month_calls: int = Field(..., description="本月调用次数")
    total_calls: int = Field(..., description="总调用次数")
    avg_daily_calls: float = Field(..., description="日均调用次数")
    quota_usage_trend: list = Field(..., description="配额使用趋势")
    most_used_apis: list = Field(..., description="最常用API")
    peak_usage_hours: list = Field(..., description="使用高峰时段")


class JQDataApiCallRecord(BaseModel):
    """JQData API调用记录"""
    
    id: int = Field(..., description="记录ID")
    user_id: int = Field(..., description="用户ID")
    api_name: str = Field(..., description="API名称")
    parameters: Optional[dict] = Field(None, description="调用参数")
    response_time: float = Field(..., description="响应时间(秒)")
    success: bool = Field(..., description="是否成功")
    error_message: Optional[str] = Field(None, description="错误消息")
    quota_consumed: int = Field(..., description="消耗配额")
    called_at: datetime = Field(..., description="调用时间")
    
    class Config:
        from_attributes = True


class JQDataBatchConfigRequest(BaseModel):
    """JQData批量配置请求"""
    
    configs: list[JQDataConfigRequest] = Field(..., min_items=1, max_items=10, description="配置列表")


class JQDataBatchConfigResponse(BaseModel):
    """JQData批量配置响应"""
    
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    results: list[dict] = Field(..., description="详细结果")


class JQDataQuotaAlertRequest(BaseModel):
    """JQData配额告警请求"""
    
    threshold: int = Field(..., ge=50, le=95, description="告警阈值(百分比)")
    enabled: bool = Field(..., description="是否启用告警")
    notification_methods: list[str] = Field(
        ..., 
        description="通知方式",
        example=["email", "sms", "push"]
    )


class JQDataQuotaAlertResponse(BaseModel):
    """JQData配额告警响应"""
    
    id: int = Field(..., description="告警ID")
    user_id: int = Field(..., description="用户ID")
    threshold: int = Field(..., description="告警阈值")
    enabled: bool = Field(..., description="是否启用")
    notification_methods: list[str] = Field(..., description="通知方式")
    last_triggered: Optional[datetime] = Field(None, description="最后触发时间")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class JQDataMarketStatusResponse(BaseModel):
    """JQData市场状态响应"""
    
    market: str = Field(..., description="市场代码")
    is_trading: bool = Field(..., description="是否交易时间")
    trading_session: Optional[str] = Field(None, description="交易时段")
    next_trading_day: Optional[str] = Field(None, description="下一交易日")
    market_status: str = Field(..., description="市场状态")
    last_update: datetime = Field(..., description="最后更新时间")


class JQDataDataSourceInfo(BaseModel):
    """JQData数据源信息"""
    
    source_name: str = Field(..., description="数据源名称")
    data_types: list[str] = Field(..., description="支持的数据类型")
    update_frequency: str = Field(..., description="更新频率")
    delay_minutes: int = Field(..., description="延迟分钟数")
    coverage: dict = Field(..., description="覆盖范围")
    quality_score: float = Field(..., ge=0, le=100, description="数据质量评分")


class JQDataSubscriptionInfo(BaseModel):
    """JQData订阅信息"""
    
    plan_name: str = Field(..., description="套餐名称")
    quota_limit: int = Field(..., description="配额限制")
    api_rate_limit: int = Field(..., description="API频率限制")
    data_delay: int = Field(..., description="数据延迟(分钟)")
    supported_markets: list[str] = Field(..., description="支持的市场")
    features: list[str] = Field(..., description="功能特性")
    price: Optional[float] = Field(None, description="价格")
    expires_at: Optional[datetime] = Field(None, description="到期时间")
