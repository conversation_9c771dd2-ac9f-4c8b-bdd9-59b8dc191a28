"""
风险管理相关API端点

提供风险指标计算、风险预警、止损管理等API接口
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import select, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.risk import RiskProfile, RiskMetrics, RiskAlert, StopLossOrder
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.risk_service import risk_monitoring_service, stop_loss_service

router = APIRouter()


# =============================================================================
# 风险档案管理
# =============================================================================

@router.get("/profile", response_model=BaseResponse[dict])
async def get_risk_profile(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户风险档案"""
    try:
        result = await db.execute(
            select(RiskProfile).where(RiskProfile.user_id == current_user.id)
        )
        profile = result.scalar_one_or_none()
        
        if not profile:
            # 创建默认风险档案
            profile = RiskProfile(
                user_id=current_user.id,
                risk_tolerance="moderate",
                max_position_size=0.1,
                max_sector_exposure=0.3,
                max_drawdown_limit=0.2,
                var_confidence_level=0.95,
                var_time_horizon=1,
                var_method="historical",
                stop_loss_enabled=True,
                stop_loss_percentage=0.05
            )
            db.add(profile)
            await db.commit()
            await db.refresh(profile)
        
        return BaseResponse(
            code=200,
            message="获取风险档案成功",
            data={
                "id": profile.id,
                "risk_tolerance": profile.risk_tolerance,
                "max_position_size": float(profile.max_position_size),
                "max_sector_exposure": float(profile.max_sector_exposure),
                "max_drawdown_limit": float(profile.max_drawdown_limit),
                "var_confidence_level": float(profile.var_confidence_level),
                "var_time_horizon": profile.var_time_horizon,
                "var_method": profile.var_method,
                "stop_loss_enabled": profile.stop_loss_enabled,
                "stop_loss_percentage": float(profile.stop_loss_percentage),
                "trailing_stop_enabled": profile.trailing_stop_enabled,
                "trailing_stop_percentage": float(profile.trailing_stop_percentage),
                "created_at": profile.created_at.isoformat(),
                "updated_at": profile.updated_at.isoformat(),
            }
        )
        
    except Exception as e:
        logger.error(f"获取风险档案失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取风险档案失败"
        )


@router.put("/profile", response_model=BaseResponse[dict])
async def update_risk_profile(
    profile_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """更新用户风险档案"""
    try:
        result = await db.execute(
            select(RiskProfile).where(RiskProfile.user_id == current_user.id)
        )
        profile = result.scalar_one_or_none()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="风险档案不存在"
            )
        
        # 更新字段
        for field, value in profile_data.items():
            if hasattr(profile, field):
                setattr(profile, field, value)
        
        profile.updated_at = datetime.utcnow()
        await db.commit()
        
        return BaseResponse(
            code=200,
            message="更新风险档案成功",
            data={"updated": True}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新风险档案失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新风险档案失败"
        )


# =============================================================================
# 风险指标
# =============================================================================

@router.get("/metrics", response_model=BaseResponse[dict])
async def get_risk_metrics(
    date: Optional[str] = Query(None, description="指定日期，格式YYYY-MM-DD"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取风险指标"""
    try:
        # 如果没有指定日期，获取最新的风险指标
        if date:
            target_date = datetime.fromisoformat(date)
        else:
            target_date = datetime.utcnow().date()
        
        result = await db.execute(
            select(RiskMetrics)
            .where(
                and_(
                    RiskMetrics.user_id == current_user.id,
                    RiskMetrics.calculation_date >= target_date,
                    RiskMetrics.calculation_date < target_date + timedelta(days=1)
                )
            )
            .order_by(desc(RiskMetrics.calculation_date))
            .limit(1)
        )
        metrics = result.scalar_one_or_none()
        
        if not metrics:
            # 如果没有风险指标，触发计算
            # 这里应该获取用户的投资组合数据
            portfolio_data = {"positions": []}  # 实际应该从投资组合服务获取
            
            calculated_metrics = await risk_monitoring_service.calculate_portfolio_risk_metrics(
                current_user.id, db, portfolio_data
            )
            
            if calculated_metrics:
                # 保存计算结果
                metrics = RiskMetrics(
                    user_id=current_user.id,
                    calculation_date=calculated_metrics["calculation_date"],
                    var_1day=calculated_metrics.get("var_1day"),
                    var_5day=calculated_metrics.get("var_5day"),
                    var_10day=calculated_metrics.get("var_10day"),
                    cvar_1day=calculated_metrics.get("cvar_1day"),
                    portfolio_volatility=calculated_metrics.get("portfolio_volatility"),
                    market_beta=calculated_metrics.get("market_beta"),
                    concentration_hhi=calculated_metrics.get("concentration_hhi"),
                    top5_concentration=calculated_metrics.get("top5_concentration"),
                )
                db.add(metrics)
                await db.commit()
                await db.refresh(metrics)
        
        if not metrics:
            return BaseResponse(
                code=200,
                message="暂无风险指标数据",
                data={}
            )
        
        return BaseResponse(
            code=200,
            message="获取风险指标成功",
            data={
                "calculation_date": metrics.calculation_date.isoformat(),
                "var_1day": float(metrics.var_1day) if metrics.var_1day else 0,
                "var_5day": float(metrics.var_5day) if metrics.var_5day else 0,
                "var_10day": float(metrics.var_10day) if metrics.var_10day else 0,
                "cvar_1day": float(metrics.cvar_1day) if metrics.cvar_1day else 0,
                "portfolio_volatility": float(metrics.portfolio_volatility) if metrics.portfolio_volatility else 0,
                "market_beta": float(metrics.market_beta) if metrics.market_beta else 1,
                "concentration_hhi": float(metrics.concentration_hhi) if metrics.concentration_hhi else 0,
                "top5_concentration": float(metrics.top5_concentration) if metrics.top5_concentration else 0,
                "current_drawdown": float(metrics.current_drawdown) if metrics.current_drawdown else 0,
                "max_drawdown": float(metrics.max_drawdown) if metrics.max_drawdown else 0,
            }
        )
        
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取风险指标失败"
        )


@router.post("/metrics/calculate", response_model=BaseResponse[dict])
async def calculate_risk_metrics(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """手动触发风险指标计算"""
    try:
        # 添加后台任务计算风险指标
        background_tasks.add_task(
            _calculate_risk_metrics_task,
            current_user.id,
            db
        )
        
        return BaseResponse(
            code=200,
            message="风险指标计算任务已提交",
            data={"task_submitted": True}
        )
        
    except Exception as e:
        logger.error(f"提交风险指标计算任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交风险指标计算任务失败"
        )


# =============================================================================
# 风险预警
# =============================================================================

@router.get("/alerts", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_risk_alerts(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None, description="状态筛选"),
    severity_filter: Optional[str] = Query(None, description="严重程度筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取风险预警列表"""
    try:
        # 构建查询条件
        conditions = [RiskAlert.user_id == current_user.id]
        if status_filter:
            conditions.append(RiskAlert.status == status_filter)
        if severity_filter:
            conditions.append(RiskAlert.severity == severity_filter)
        
        # 查询总数
        from sqlalchemy import func
        count_query = select(func.count(RiskAlert.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(RiskAlert)
            .where(and_(*conditions))
            .order_by(desc(RiskAlert.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        alerts = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        alert_list = []
        for alert in alerts:
            alert_list.append({
                "id": alert.id,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "title": alert.title,
                "message": alert.message,
                "current_value": float(alert.current_value) if alert.current_value else None,
                "threshold_value": float(alert.threshold_value) if alert.threshold_value else None,
                "symbol": alert.symbol,
                "sector": alert.sector,
                "status": alert.status,
                "created_at": alert.created_at.isoformat(),
            })
        
        return BaseResponse(
            code=200,
            message="获取风险预警列表成功",
            data=PaginatedResponse(
                items=alert_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取风险预警列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取风险预警列表失败"
        )


# =============================================================================
# 止损管理
# =============================================================================

@router.post("/stop-loss", response_model=BaseResponse[dict])
async def create_stop_loss_order(
    order_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建止损订单"""
    try:
        order = await stop_loss_service.create_stop_loss_order(
            current_user.id, db, order_data
        )
        
        return BaseResponse(
            code=200,
            message="创建止损订单成功",
            data={
                "id": order.id,
                "symbol": order.symbol,
                "order_type": order.order_type,
                "status": order.status,
                "created_at": order.created_at.isoformat(),
            }
        )
        
    except Exception as e:
        logger.error(f"创建止损订单失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建止损订单失败"
        )


@router.get("/stop-loss", response_model=BaseResponse[List[dict]])
async def get_stop_loss_orders(
    status_filter: Optional[str] = Query(None, description="状态筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取止损订单列表"""
    try:
        conditions = [StopLossOrder.user_id == current_user.id]
        if status_filter:
            conditions.append(StopLossOrder.status == status_filter)
        
        result = await db.execute(
            select(StopLossOrder)
            .where(and_(*conditions))
            .order_by(desc(StopLossOrder.created_at))
        )
        orders = result.scalars().all()
        
        order_list = []
        for order in orders:
            order_list.append({
                "id": order.id,
                "symbol": order.symbol,
                "order_type": order.order_type,
                "quantity": order.quantity,
                "trigger_price": float(order.trigger_price),
                "stop_price": float(order.stop_price),
                "status": order.status,
                "created_at": order.created_at.isoformat(),
                "triggered_at": order.triggered_at.isoformat() if order.triggered_at else None,
            })
        
        return BaseResponse(
            code=200,
            message="获取止损订单列表成功",
            data=order_list
        )
        
    except Exception as e:
        logger.error(f"获取止损订单列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取止损订单列表失败"
        )


# =============================================================================
# 私有函数
# =============================================================================

async def _calculate_risk_metrics_task(user_id: int, db: AsyncSession):
    """计算风险指标的后台任务"""
    try:
        # 获取用户投资组合数据
        portfolio_data = {"positions": []}  # 实际应该从投资组合服务获取
        
        # 计算风险指标
        calculated_metrics = await risk_monitoring_service.calculate_portfolio_risk_metrics(
            user_id, db, portfolio_data
        )
        
        if calculated_metrics:
            # 保存到数据库
            metrics = RiskMetrics(
                user_id=user_id,
                calculation_date=calculated_metrics["calculation_date"],
                var_1day=calculated_metrics.get("var_1day"),
                var_5day=calculated_metrics.get("var_5day"),
                var_10day=calculated_metrics.get("var_10day"),
                cvar_1day=calculated_metrics.get("cvar_1day"),
                portfolio_volatility=calculated_metrics.get("portfolio_volatility"),
                market_beta=calculated_metrics.get("market_beta"),
                concentration_hhi=calculated_metrics.get("concentration_hhi"),
                top5_concentration=calculated_metrics.get("top5_concentration"),
            )
            db.add(metrics)
            await db.commit()
            
            # 检查风险预警
            alerts = await risk_monitoring_service.check_risk_alerts(
                user_id, db, calculated_metrics
            )
            
            # 保存预警
            for alert_data in alerts:
                alert = RiskAlert(
                    user_id=user_id,
                    risk_profile_id=1,  # 实际应该获取用户的风险档案ID
                    alert_type=alert_data["alert_type"],
                    severity=alert_data["severity"],
                    title=alert_data["title"],
                    message=alert_data["message"],
                    current_value=alert_data.get("current_value"),
                    threshold_value=alert_data.get("threshold_value"),
                )
                db.add(alert)
            
            await db.commit()
            logger.info(f"用户 {user_id} 风险指标计算完成")
        
    except Exception as e:
        logger.error(f"风险指标计算任务失败: {e}")
