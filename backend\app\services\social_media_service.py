"""
社交媒体数据服务

提供微博、Twitter等社交媒体数据采集和情感分析功能
"""

import re
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import requests
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.logging import logger
from app.models.multimodal import SocialMediaSource, SocialMediaPost, SocialSentiment
from app.services.news_sentiment_service import SentimentAnalyzer


class WeiboCollector:
    """微博数据采集器"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def collect_posts_by_keyword(
        self, 
        keyword: str, 
        max_posts: int = 100,
        time_range: int = 24  # 小时
    ) -> List[Dict[str, Any]]:
        """根据关键词采集微博"""
        try:
            posts = []
            
            # 模拟微博API调用（实际需要真实的API密钥和端点）
            # 这里提供一个框架结构
            
            if self.api_key:
                posts = self._collect_via_api(keyword, max_posts, time_range)
            else:
                posts = self._collect_via_web_scraping(keyword, max_posts, time_range)
            
            return posts
            
        except Exception as e:
            logger.error(f"微博采集失败: {e}")
            return []
    
    def _collect_via_api(self, keyword: str, max_posts: int, time_range: int) -> List[Dict[str, Any]]:
        """通过API采集微博"""
        try:
            # 微博API调用示例
            api_url = "https://api.weibo.com/2/search/topics.json"
            params = {
                'access_token': self.api_key,
                'q': keyword,
                'count': min(max_posts, 200),
                'page': 1
            }
            
            response = self.session.get(api_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            posts = []
            
            if 'statuses' in data:
                for item in data['statuses']:
                    post = self._parse_weibo_post(item)
                    posts.append(post)
            
            return posts
            
        except Exception as e:
            logger.error(f"微博API采集失败: {e}")
            return []
    
    def _collect_via_web_scraping(self, keyword: str, max_posts: int, time_range: int) -> List[Dict[str, Any]]:
        """通过网页抓取采集微博（模拟数据）"""
        try:
            # 这里返回模拟数据，实际应用中需要实现真实的网页抓取
            posts = []
            
            for i in range(min(max_posts, 50)):
                post = {
                    'post_id': f"weibo_{i}_{int(datetime.now().timestamp())}",
                    'content': f"关于{keyword}的微博内容 #{keyword}# 这是一条测试微博",
                    'content_type': 'text',
                    'language': 'zh',
                    'user_id': f"user_{i % 10}",
                    'username': f"用户{i % 10}",
                    'user_followers': (i % 10 + 1) * 1000,
                    'user_verified': i % 3 == 0,
                    'posted_at': datetime.now() - timedelta(hours=i % time_range),
                    'like_count': i * 10,
                    'share_count': i * 2,
                    'comment_count': i * 5,
                    'hashtags': [keyword],
                    'mentions': [],
                    'is_repost': i % 4 == 0,
                    'location': '北京' if i % 2 == 0 else '上海'
                }
                posts.append(post)
            
            return posts
            
        except Exception as e:
            logger.error(f"微博网页抓取失败: {e}")
            return []
    
    def _parse_weibo_post(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """解析微博数据"""
        try:
            user = item.get('user', {})
            
            return {
                'post_id': str(item.get('id', '')),
                'content': item.get('text', ''),
                'content_type': 'text',
                'language': 'zh',
                'user_id': str(user.get('id', '')),
                'username': user.get('screen_name', ''),
                'user_followers': user.get('followers_count', 0),
                'user_verified': user.get('verified', False),
                'posted_at': self._parse_weibo_time(item.get('created_at', '')),
                'like_count': item.get('attitudes_count', 0),
                'share_count': item.get('reposts_count', 0),
                'comment_count': item.get('comments_count', 0),
                'hashtags': self._extract_hashtags(item.get('text', '')),
                'mentions': self._extract_mentions(item.get('text', '')),
                'is_repost': 'retweeted_status' in item,
                'location': item.get('geo', {}).get('coordinates', '')
            }
            
        except Exception as e:
            logger.error(f"微博数据解析失败: {e}")
            return {}
    
    def _parse_weibo_time(self, time_str: str) -> datetime:
        """解析微博时间"""
        try:
            # 微博时间格式解析
            import dateutil.parser
            return dateutil.parser.parse(time_str)
        except:
            return datetime.now()
    
    def _extract_hashtags(self, text: str) -> List[str]:
        """提取话题标签"""
        hashtag_pattern = r'#([^#]+)#'
        return re.findall(hashtag_pattern, text)
    
    def _extract_mentions(self, text: str) -> List[str]:
        """提取@用户"""
        mention_pattern = r'@([^\s@]+)'
        return re.findall(mention_pattern, text)


class TwitterCollector:
    """Twitter数据采集器"""
    
    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()
    
    def collect_tweets_by_keyword(
        self, 
        keyword: str, 
        max_tweets: int = 100,
        time_range: int = 24
    ) -> List[Dict[str, Any]]:
        """根据关键词采集推文"""
        try:
            tweets = []
            
            if self.api_key and self.api_secret:
                tweets = self._collect_via_twitter_api(keyword, max_tweets, time_range)
            else:
                tweets = self._generate_mock_tweets(keyword, max_tweets, time_range)
            
            return tweets
            
        except Exception as e:
            logger.error(f"Twitter采集失败: {e}")
            return []
    
    def _collect_via_twitter_api(self, keyword: str, max_tweets: int, time_range: int) -> List[Dict[str, Any]]:
        """通过Twitter API采集"""
        try:
            # Twitter API v2调用示例
            # 实际使用需要配置OAuth认证
            
            api_url = "https://api.twitter.com/2/tweets/search/recent"
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            params = {
                'query': keyword,
                'max_results': min(max_tweets, 100),
                'tweet.fields': 'created_at,author_id,public_metrics,lang,geo',
                'user.fields': 'verified,public_metrics'
            }
            
            response = self.session.get(api_url, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            tweets = []
            
            if 'data' in data:
                for item in data['data']:
                    tweet = self._parse_twitter_post(item)
                    tweets.append(tweet)
            
            return tweets
            
        except Exception as e:
            logger.error(f"Twitter API采集失败: {e}")
            return []
    
    def _generate_mock_tweets(self, keyword: str, max_tweets: int, time_range: int) -> List[Dict[str, Any]]:
        """生成模拟推文数据"""
        try:
            tweets = []
            
            for i in range(min(max_tweets, 50)):
                tweet = {
                    'post_id': f"twitter_{i}_{int(datetime.now().timestamp())}",
                    'content': f"Tweet about {keyword} #investing #stocks",
                    'content_type': 'text',
                    'language': 'en',
                    'user_id': f"twitter_user_{i % 10}",
                    'username': f"user{i % 10}",
                    'user_followers': (i % 10 + 1) * 5000,
                    'user_verified': i % 5 == 0,
                    'posted_at': datetime.now() - timedelta(hours=i % time_range),
                    'like_count': i * 20,
                    'share_count': i * 5,
                    'comment_count': i * 8,
                    'hashtags': ['investing', 'stocks', keyword.lower()],
                    'mentions': [],
                    'is_repost': i % 6 == 0,
                    'location': 'New York' if i % 2 == 0 else 'San Francisco'
                }
                tweets.append(tweet)
            
            return tweets
            
        except Exception as e:
            logger.error(f"模拟推文生成失败: {e}")
            return []
    
    def _parse_twitter_post(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """解析Twitter数据"""
        try:
            metrics = item.get('public_metrics', {})
            
            return {
                'post_id': item.get('id', ''),
                'content': item.get('text', ''),
                'content_type': 'text',
                'language': item.get('lang', 'en'),
                'user_id': item.get('author_id', ''),
                'username': '',  # 需要额外查询用户信息
                'user_followers': 0,  # 需要额外查询用户信息
                'user_verified': False,  # 需要额外查询用户信息
                'posted_at': self._parse_twitter_time(item.get('created_at', '')),
                'like_count': metrics.get('like_count', 0),
                'share_count': metrics.get('retweet_count', 0),
                'comment_count': metrics.get('reply_count', 0),
                'hashtags': self._extract_twitter_hashtags(item.get('text', '')),
                'mentions': self._extract_twitter_mentions(item.get('text', '')),
                'is_repost': item.get('text', '').startswith('RT @'),
                'location': ''
            }
            
        except Exception as e:
            logger.error(f"Twitter数据解析失败: {e}")
            return {}
    
    def _parse_twitter_time(self, time_str: str) -> datetime:
        """解析Twitter时间"""
        try:
            return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        except:
            return datetime.now()
    
    def _extract_twitter_hashtags(self, text: str) -> List[str]:
        """提取Twitter话题标签"""
        hashtag_pattern = r'#(\w+)'
        return re.findall(hashtag_pattern, text)
    
    def _extract_twitter_mentions(self, text: str) -> List[str]:
        """提取Twitter@用户"""
        mention_pattern = r'@(\w+)'
        return re.findall(mention_pattern, text)


class SocialInfluenceCalculator:
    """社交影响力计算器"""
    
    def __init__(self):
        pass
    
    def calculate_user_influence(self, user_data: Dict[str, Any]) -> float:
        """计算用户影响力分数"""
        try:
            followers = user_data.get('user_followers', 0)
            verified = user_data.get('user_verified', False)
            
            # 基础影响力分数
            base_score = min(followers / 100000, 1.0)  # 标准化到[0,1]
            
            # 认证用户加成
            if verified:
                base_score *= 1.5
            
            # 限制在[0,1]范围内
            return min(base_score, 1.0)
            
        except Exception as e:
            logger.error(f"用户影响力计算失败: {e}")
            return 0.0
    
    def calculate_viral_score(self, post_data: Dict[str, Any]) -> float:
        """计算病毒传播分数"""
        try:
            likes = post_data.get('like_count', 0)
            shares = post_data.get('share_count', 0)
            comments = post_data.get('comment_count', 0)
            
            # 加权计算传播分数
            viral_score = (likes * 0.3 + shares * 0.5 + comments * 0.2) / 1000
            
            # 标准化到[0,1]
            return min(viral_score, 1.0)
            
        except Exception as e:
            logger.error(f"病毒传播分数计算失败: {e}")
            return 0.0
    
    def calculate_credibility_score(self, post_data: Dict[str, Any]) -> float:
        """计算可信度分数"""
        try:
            # 基于多个因素计算可信度
            user_verified = post_data.get('user_verified', False)
            user_followers = post_data.get('user_followers', 0)
            is_repost = post_data.get('is_repost', False)
            
            credibility = 0.5  # 基础分数
            
            if user_verified:
                credibility += 0.2
            
            if user_followers > 10000:
                credibility += 0.1
            elif user_followers > 100000:
                credibility += 0.2
            
            if is_repost:
                credibility -= 0.1  # 转发内容可信度略低
            
            return max(0.0, min(credibility, 1.0))
            
        except Exception as e:
            logger.error(f"可信度分数计算失败: {e}")
            return 0.5


class SocialMediaService:
    """社交媒体服务"""
    
    def __init__(self):
        self.weibo_collector = WeiboCollector()
        self.twitter_collector = TwitterCollector()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.influence_calculator = SocialInfluenceCalculator()
    
    async def collect_social_media_data(
        self,
        source_id: int,
        keywords: List[str],
        db: AsyncSession,
        max_posts: int = 100
    ) -> Dict[str, Any]:
        """采集社交媒体数据"""
        try:
            # 获取数据源配置
            result = await db.execute(
                select(SocialMediaSource).where(SocialMediaSource.id == source_id)
            )
            source = result.scalar_one_or_none()
            
            if not source or not source.is_active:
                return {"success": False, "error": "数据源不存在或未激活"}
            
            all_posts = []
            
            # 根据平台类型采集数据
            for keyword in keywords:
                if source.platform == "weibo":
                    posts = self.weibo_collector.collect_posts_by_keyword(
                        keyword, max_posts // len(keywords)
                    )
                elif source.platform == "twitter":
                    posts = self.twitter_collector.collect_tweets_by_keyword(
                        keyword, max_posts // len(keywords)
                    )
                else:
                    continue
                
                all_posts.extend(posts)
            
            processed_count = 0
            
            for post_data in all_posts:
                # 检查帖子是否已存在
                existing = await db.execute(
                    select(SocialMediaPost).where(
                        and_(
                            SocialMediaPost.source_id == source_id,
                            SocialMediaPost.post_id == post_data['post_id']
                        )
                    )
                )
                
                if existing.scalar_one_or_none():
                    continue
                
                # 计算影响力和可信度分数
                influence_score = self.influence_calculator.calculate_user_influence(post_data)
                viral_score = self.influence_calculator.calculate_viral_score(post_data)
                credibility_score = self.influence_calculator.calculate_credibility_score(post_data)
                
                # 创建社交媒体帖子记录
                post = SocialMediaPost(
                    source_id=source_id,
                    post_id=post_data['post_id'],
                    content=post_data['content'],
                    content_type=post_data['content_type'],
                    language=post_data['language'],
                    user_id=post_data['user_id'],
                    username=post_data['username'],
                    user_followers=post_data['user_followers'],
                    user_verified=post_data['user_verified'],
                    posted_at=post_data['posted_at'],
                    like_count=post_data['like_count'],
                    share_count=post_data['share_count'],
                    comment_count=post_data['comment_count'],
                    hashtags=post_data['hashtags'],
                    mentions=post_data['mentions'],
                    is_repost=post_data['is_repost'],
                    location=post_data['location'],
                    user_influence_score=influence_score,
                    viral_score=viral_score,
                    credibility_score=credibility_score,
                    processing_status="processing"
                )
                
                db.add(post)
                await db.flush()  # 获取ID
                
                # 进行情感分析
                await self._analyze_post_sentiment(post, db)
                
                # 更新处理状态
                post.processing_status = "completed"
                post.is_processed = True
                
                processed_count += 1
            
            # 更新数据源统计
            source.total_posts += processed_count
            source.last_sync = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "collected_posts": len(all_posts),
                "processed_posts": processed_count,
                "platform": source.platform
            }
            
        except Exception as e:
            logger.error(f"社交媒体数据采集失败: {e}")
            await db.rollback()
            return {"success": False, "error": str(e)}
    
    async def _analyze_post_sentiment(self, post: SocialMediaPost, db: AsyncSession):
        """分析帖子情感"""
        try:
            # 进行情感分析
            sentiment_result = self.sentiment_analyzer.analyze_sentiment(
                post.content, post.language
            )
            
            # 计算影响力加权情感
            weighted_sentiment = (sentiment_result['sentiment_score'] * 
                                post.user_influence_score)
            
            # 计算病毒传播调整情感
            viral_adjusted_sentiment = (sentiment_result['sentiment_score'] * 
                                      (1 + post.viral_score))
            
            # 保存情感分析结果
            sentiment = SocialSentiment(
                post_id=post.id,
                sentiment_label=sentiment_result['sentiment_label'],
                sentiment_score=sentiment_result['sentiment_score'],
                confidence=sentiment_result['confidence'],
                emotions=sentiment_result.get('component_results', {}),
                weighted_sentiment=weighted_sentiment,
                viral_adjusted_sentiment=viral_adjusted_sentiment,
                analysis_model=sentiment_result.get('method', 'ensemble'),
                model_version="1.0"
            )
            
            db.add(sentiment)
            
        except Exception as e:
            logger.error(f"帖子情感分析失败: {e}")
    
    async def get_sentiment_trends(
        self,
        symbol: str,
        time_range: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取情感趋势"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=time_range)
            
            # 查询相关帖子的情感数据
            query = (
                select(SocialSentiment, SocialMediaPost)
                .join(SocialMediaPost, SocialSentiment.post_id == SocialMediaPost.id)
                .where(
                    and_(
                        SocialMediaPost.posted_at >= start_time,
                        SocialMediaPost.related_symbols.contains([symbol])
                    )
                )
                .order_by(SocialMediaPost.posted_at)
            )
            
            result = await db.execute(query)
            sentiment_data = result.all()
            
            if not sentiment_data:
                return {"success": False, "error": "没有找到相关数据"}
            
            # 计算趋势指标
            sentiments = [s.SocialSentiment for s in sentiment_data]
            posts = [s.SocialMediaPost for s in sentiment_data]
            
            avg_sentiment = np.mean([s.sentiment_score for s in sentiments])
            weighted_avg_sentiment = np.mean([s.weighted_sentiment for s in sentiments])
            sentiment_volatility = np.std([s.sentiment_score for s in sentiments])
            
            positive_count = len([s for s in sentiments if s.sentiment_label == 'positive'])
            negative_count = len([s for s in sentiments if s.sentiment_label == 'negative'])
            neutral_count = len([s for s in sentiments if s.sentiment_label == 'neutral'])
            
            total_engagement = sum([p.like_count + p.share_count + p.comment_count for p in posts])
            
            return {
                "success": True,
                "symbol": symbol,
                "time_range_hours": time_range,
                "total_posts": len(sentiment_data),
                "average_sentiment": float(avg_sentiment),
                "weighted_average_sentiment": float(weighted_avg_sentiment),
                "sentiment_volatility": float(sentiment_volatility),
                "sentiment_distribution": {
                    "positive": positive_count,
                    "negative": negative_count,
                    "neutral": neutral_count
                },
                "total_engagement": total_engagement,
                "sentiment_timeline": [
                    {
                        "timestamp": p.posted_at.isoformat(),
                        "sentiment_score": s.sentiment_score,
                        "weighted_sentiment": s.weighted_sentiment,
                        "engagement": p.like_count + p.share_count + p.comment_count
                    }
                    for s, p in zip(sentiments, posts)
                ]
            }
            
        except Exception as e:
            logger.error(f"获取情感趋势失败: {e}")
            return {"success": False, "error": str(e)}


# 全局社交媒体服务实例
social_media_service = SocialMediaService()
