"""
LIME解释服务

提供基于LIME的模型解释功能，专注于局部可解释性
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from sqlalchemy.ext.asyncio import AsyncSession
import warnings
warnings.filterwarnings('ignore')

# 尝试导入LIME
try:
    import lime
    import lime.lime_tabular
    import lime.lime_text
    import lime.lime_image
    LIME_AVAILABLE = True
except ImportError:
    LIME_AVAILABLE = False

from sklearn.metrics import r2_score, accuracy_score
from sklearn.preprocessing import LabelEncoder

from app.core.logging import logger
from app.models.explainability import LIMEExplanation


class LIMEExplainerFactory:
    """LIME解释器工厂"""
    
    def __init__(self):
        self.explainer_types = {
            'tabular': self._create_tabular_explainer,
            'text': self._create_text_explainer,
            'image': self._create_image_explainer
        }
    
    def create_explainer(
        self,
        explainer_type: str,
        training_data: np.ndarray,
        feature_names: List[str],
        class_names: Optional[List[str]] = None,
        mode: str = 'classification',
        **kwargs
    ):
        """创建LIME解释器"""
        try:
            if not LIME_AVAILABLE:
                raise ImportError("LIME library is not available")
            
            if explainer_type not in self.explainer_types:
                raise ValueError(f"Unsupported explainer type: {explainer_type}")
            
            return self.explainer_types[explainer_type](
                training_data, feature_names, class_names, mode, **kwargs
            )
            
        except Exception as e:
            logger.error(f"创建LIME解释器失败: {e}")
            raise
    
    def _create_tabular_explainer(
        self,
        training_data: np.ndarray,
        feature_names: List[str],
        class_names: Optional[List[str]] = None,
        mode: str = 'classification',
        **kwargs
    ):
        """创建表格数据解释器"""
        return lime.lime_tabular.LimeTabularExplainer(
            training_data,
            feature_names=feature_names,
            class_names=class_names,
            mode=mode,
            **kwargs
        )
    
    def _create_text_explainer(
        self,
        training_data: np.ndarray,
        feature_names: List[str],
        class_names: Optional[List[str]] = None,
        mode: str = 'classification',
        **kwargs
    ):
        """创建文本解释器"""
        return lime.lime_text.LimeTextExplainer(
            class_names=class_names,
            mode=mode,
            **kwargs
        )
    
    def _create_image_explainer(
        self,
        training_data: np.ndarray,
        feature_names: List[str],
        class_names: Optional[List[str]] = None,
        mode: str = 'classification',
        **kwargs
    ):
        """创建图像解释器"""
        return lime.lime_image.LimeImageExplainer(
            mode=mode,
            **kwargs
        )


class LIMEAnalyzer:
    """LIME分析器"""
    
    def __init__(self):
        pass
    
    def analyze_explanation_quality(
        self,
        explanation,
        model_predict_fn: Callable,
        instance: np.ndarray,
        num_samples: int = 1000
    ) -> Dict[str, Any]:
        """分析解释质量"""
        try:
            # 获取局部模型
            local_model = explanation.local_exp
            intercept = explanation.intercept[explanation.available_labels[0]]
            
            # 计算局部模型的R²分数
            if hasattr(explanation, 'local_pred'):
                local_r2 = explanation.score
            else:
                local_r2 = 0.0
            
            # 分析特征重要性
            feature_importance = {}
            for feature_idx, importance in local_model:
                feature_importance[feature_idx] = importance
            
            # 计算解释的稳定性（通过多次采样）
            stability_scores = []
            for _ in range(5):  # 进行5次稳定性测试
                try:
                    temp_explanation = explanation.explainer.explain_instance(
                        instance,
                        model_predict_fn,
                        num_features=len(feature_importance),
                        num_samples=num_samples // 5
                    )
                    temp_importance = dict(temp_explanation.local_exp[temp_explanation.available_labels[0]])
                    
                    # 计算与原解释的相关性
                    original_values = [feature_importance.get(k, 0) for k in temp_importance.keys()]
                    temp_values = list(temp_importance.values())
                    
                    if len(original_values) > 1 and len(temp_values) > 1:
                        correlation = np.corrcoef(original_values, temp_values)[0, 1]
                        if not np.isnan(correlation):
                            stability_scores.append(correlation)
                except:
                    continue
            
            stability = np.mean(stability_scores) if stability_scores else 0.0
            
            return {
                'local_r2': float(local_r2),
                'feature_importance': feature_importance,
                'intercept': float(intercept),
                'stability': float(stability),
                'num_features_used': len(feature_importance)
            }
            
        except Exception as e:
            logger.error(f"解释质量分析失败: {e}")
            return {}
    
    def analyze_feature_contributions(
        self,
        explanations: List[Any],
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """分析特征贡献"""
        try:
            # 收集所有解释的特征重要性
            all_contributions = {}
            feature_selection_frequency = {}
            
            for explanation in explanations:
                local_exp = explanation.local_exp[explanation.available_labels[0]]
                
                for feature_idx, importance in local_exp:
                    feature_name = feature_names[feature_idx] if feature_idx < len(feature_names) else f"feature_{feature_idx}"
                    
                    if feature_name not in all_contributions:
                        all_contributions[feature_name] = []
                        feature_selection_frequency[feature_name] = 0
                    
                    all_contributions[feature_name].append(importance)
                    feature_selection_frequency[feature_name] += 1
            
            # 计算统计信息
            contribution_stats = {}
            for feature_name, contributions in all_contributions.items():
                contribution_stats[feature_name] = {
                    'mean': float(np.mean(contributions)),
                    'std': float(np.std(contributions)),
                    'min': float(np.min(contributions)),
                    'max': float(np.max(contributions)),
                    'selection_frequency': feature_selection_frequency[feature_name],
                    'selection_rate': feature_selection_frequency[feature_name] / len(explanations)
                }
            
            return {
                'contribution_stats': contribution_stats,
                'total_explanations': len(explanations),
                'features_analyzed': len(all_contributions)
            }
            
        except Exception as e:
            logger.error(f"特征贡献分析失败: {e}")
            return {}
    
    def analyze_explanation_consistency(
        self,
        explanations: List[Any],
        similarity_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """分析解释一致性"""
        try:
            if len(explanations) < 2:
                return {'consistency_score': 1.0, 'similar_pairs': 0, 'total_pairs': 0}
            
            # 提取所有解释的特征重要性向量
            importance_vectors = []
            all_features = set()
            
            for explanation in explanations:
                local_exp = explanation.local_exp[explanation.available_labels[0]]
                importance_dict = dict(local_exp)
                all_features.update(importance_dict.keys())
                importance_vectors.append(importance_dict)
            
            # 创建标准化的重要性矩阵
            feature_list = sorted(list(all_features))
            importance_matrix = []
            
            for importance_dict in importance_vectors:
                vector = [importance_dict.get(feature, 0.0) for feature in feature_list]
                importance_matrix.append(vector)
            
            importance_matrix = np.array(importance_matrix)
            
            # 计算成对相似性
            similarities = []
            similar_pairs = 0
            total_pairs = 0
            
            for i in range(len(importance_matrix)):
                for j in range(i + 1, len(importance_matrix)):
                    if np.linalg.norm(importance_matrix[i]) > 0 and np.linalg.norm(importance_matrix[j]) > 0:
                        similarity = np.dot(importance_matrix[i], importance_matrix[j]) / (
                            np.linalg.norm(importance_matrix[i]) * np.linalg.norm(importance_matrix[j])
                        )
                        similarities.append(similarity)
                        
                        if similarity >= similarity_threshold:
                            similar_pairs += 1
                    
                    total_pairs += 1
            
            consistency_score = np.mean(similarities) if similarities else 0.0
            
            return {
                'consistency_score': float(consistency_score),
                'similar_pairs': similar_pairs,
                'total_pairs': total_pairs,
                'similarity_distribution': {
                    'mean': float(np.mean(similarities)) if similarities else 0.0,
                    'std': float(np.std(similarities)) if similarities else 0.0,
                    'min': float(np.min(similarities)) if similarities else 0.0,
                    'max': float(np.max(similarities)) if similarities else 0.0
                }
            }
            
        except Exception as e:
            logger.error(f"解释一致性分析失败: {e}")
            return {}


class LIMEVisualizationDataGenerator:
    """LIME可视化数据生成器"""
    
    def __init__(self):
        pass
    
    def generate_explanation_plot_data(
        self,
        explanation,
        feature_names: List[str],
        max_features: int = 10
    ) -> Dict[str, Any]:
        """生成解释图数据"""
        try:
            local_exp = explanation.local_exp[explanation.available_labels[0]]
            
            # 按重要性排序
            sorted_exp = sorted(local_exp, key=lambda x: abs(x[1]), reverse=True)[:max_features]
            
            plot_data = {
                'features': [],
                'importances': [],
                'feature_names': [],
                'colors': []
            }
            
            for feature_idx, importance in sorted_exp:
                feature_name = feature_names[feature_idx] if feature_idx < len(feature_names) else f"feature_{feature_idx}"
                
                plot_data['features'].append(feature_idx)
                plot_data['importances'].append(float(importance))
                plot_data['feature_names'].append(feature_name)
                plot_data['colors'].append('positive' if importance > 0 else 'negative')
            
            return plot_data
            
        except Exception as e:
            logger.error(f"解释图数据生成失败: {e}")
            return {}
    
    def generate_feature_importance_plot_data(
        self,
        explanations: List[Any],
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """生成特征重要性图数据"""
        try:
            # 收集所有特征的重要性
            feature_importances = {}
            
            for explanation in explanations:
                local_exp = explanation.local_exp[explanation.available_labels[0]]
                
                for feature_idx, importance in local_exp:
                    feature_name = feature_names[feature_idx] if feature_idx < len(feature_names) else f"feature_{feature_idx}"
                    
                    if feature_name not in feature_importances:
                        feature_importances[feature_name] = []
                    
                    feature_importances[feature_name].append(importance)
            
            # 计算平均重要性
            avg_importances = {}
            for feature_name, importances in feature_importances.items():
                avg_importances[feature_name] = np.mean(np.abs(importances))
            
            # 排序
            sorted_features = sorted(avg_importances.items(), key=lambda x: x[1], reverse=True)
            
            plot_data = {
                'feature_names': [item[0] for item in sorted_features],
                'avg_importances': [float(item[1]) for item in sorted_features],
                'importance_distributions': {}
            }
            
            # 添加分布信息
            for feature_name, importances in feature_importances.items():
                plot_data['importance_distributions'][feature_name] = {
                    'mean': float(np.mean(importances)),
                    'std': float(np.std(importances)),
                    'values': [float(x) for x in importances]
                }
            
            return plot_data
            
        except Exception as e:
            logger.error(f"特征重要性图数据生成失败: {e}")
            return {}


class LIMEExplainerService:
    """LIME解释服务"""
    
    def __init__(self):
        self.explainer_factory = LIMEExplainerFactory()
        self.analyzer = LIMEAnalyzer()
        self.viz_generator = LIMEVisualizationDataGenerator()
    
    async def explain_instances(
        self,
        analysis_id: int,
        model,
        X_train: np.ndarray,
        X_explain: np.ndarray,
        feature_names: List[str],
        explainer_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """解释实例"""
        try:
            start_time = datetime.utcnow()
            
            # 确定模式
            mode = explainer_config.get('mode', 'classification')
            explainer_type = explainer_config.get('explainer_type', 'tabular')
            
            # 创建预测函数
            if mode == 'classification':
                if hasattr(model, 'predict_proba'):
                    predict_fn = model.predict_proba
                else:
                    predict_fn = lambda x: np.column_stack([1 - model.predict(x), model.predict(x)])
            else:
                predict_fn = model.predict
            
            # 创建LIME解释器
            explainer = self.explainer_factory.create_explainer(
                explainer_type=explainer_type,
                training_data=X_train,
                feature_names=feature_names,
                mode=mode,
                **explainer_config.get('explainer_params', {})
            )
            
            # 解释参数
            num_features = explainer_config.get('num_features', 10)
            num_samples = explainer_config.get('num_samples', 5000)
            
            # 解释实例
            explanations = []
            instance_explanations = []
            
            max_instances = min(explainer_config.get('max_instances', 100), len(X_explain))
            
            for i in range(max_instances):
                try:
                    explanation = explainer.explain_instance(
                        X_explain[i],
                        predict_fn,
                        num_features=num_features,
                        num_samples=num_samples
                    )
                    
                    explanations.append(explanation)
                    
                    # 分析解释质量
                    quality_analysis = self.analyzer.analyze_explanation_quality(
                        explanation, predict_fn, X_explain[i], num_samples
                    )
                    
                    instance_explanations.append({
                        'instance_idx': i,
                        'local_exp': dict(explanation.local_exp[explanation.available_labels[0]]),
                        'intercept': float(explanation.intercept[explanation.available_labels[0]]),
                        'r2_score': quality_analysis.get('local_r2', 0.0),
                        'prediction': float(explanation.local_pred) if hasattr(explanation, 'local_pred') else None
                    })
                    
                except Exception as e:
                    logger.warning(f"解释实例 {i} 失败: {e}")
                    continue
            
            # 分析特征贡献
            contribution_analysis = self.analyzer.analyze_feature_contributions(
                explanations, feature_names
            )
            
            # 分析解释一致性
            consistency_analysis = self.analyzer.analyze_explanation_consistency(explanations)
            
            # 生成可视化数据
            viz_data = self._generate_visualization_data(explanations, feature_names)
            
            # 计算执行时间
            computation_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 保存LIME解释结果
            lime_explanation = LIMEExplanation(
                analysis_id=analysis_id,
                explainer_type=explainer_type,
                explainer_config=explainer_config,
                num_features=num_features,
                num_samples=num_samples,
                instance_explanations=instance_explanations,
                feature_contributions=contribution_analysis.get('contribution_stats', {}),
                local_model_r2=[exp.get('r2_score', 0.0) for exp in instance_explanations],
                explanation_plots=viz_data.get('explanation_plots', []),
                feature_importance_plots=viz_data.get('feature_importance_plot', {}),
                stability_analysis=consistency_analysis,
                computation_time_seconds=computation_time,
                status="completed"
            )
            
            db.add(lime_explanation)
            await db.commit()
            
            return {
                'success': True,
                'explanation_id': lime_explanation.id,
                'instances_explained': len(instance_explanations),
                'contribution_analysis': contribution_analysis,
                'consistency_analysis': consistency_analysis,
                'visualization_data': viz_data,
                'computation_time': computation_time
            }
            
        except Exception as e:
            logger.error(f"LIME实例解释失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_visualization_data(
        self,
        explanations: List[Any],
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """生成可视化数据"""
        try:
            viz_data = {}
            
            # 单个解释图数据
            explanation_plots = []
            for i, explanation in enumerate(explanations[:5]):  # 只取前5个
                plot_data = self.viz_generator.generate_explanation_plot_data(
                    explanation, feature_names
                )
                if plot_data:
                    explanation_plots.append({
                        'instance_idx': i,
                        'data': plot_data
                    })
            
            viz_data['explanation_plots'] = explanation_plots
            
            # 特征重要性图数据
            importance_plot = self.viz_generator.generate_feature_importance_plot_data(
                explanations, feature_names
            )
            viz_data['feature_importance_plot'] = importance_plot
            
            return viz_data
            
        except Exception as e:
            logger.error(f"可视化数据生成失败: {e}")
            return {}


# 全局LIME解释服务实例
lime_explainer_service = LIMEExplainerService()
