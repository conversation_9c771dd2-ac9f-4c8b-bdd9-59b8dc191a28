"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Hyphen = void 0;
const color_1 = require("./color");
/**
 * -
 */
const Hyphen = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'hyphen' }, options), context);
};
exports.Hyphen = Hyphen;
exports.Hyphen.props = Object.assign({ defaultMarker: 'hyphen' }, color_1.Color.props);
//# sourceMappingURL=hyphen.js.map