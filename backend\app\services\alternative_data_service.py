"""
另类数据集成服务

提供卫星数据、经济指标、天气数据等另类数据源的集成和处理
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import requests
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func

from app.core.logging import logger
from app.models.multimodal import AlternativeDataSource, AlternativeData


class SatelliteDataCollector:
    """卫星数据采集器"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session = requests.Session()
    
    def collect_economic_activity_data(
        self,
        region: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """采集经济活动卫星数据"""
        try:
            # 模拟卫星数据API调用
            data_points = []
            
            current_date = start_date
            while current_date <= end_date:
                # 生成模拟的经济活动指标
                data_point = {
                    'data_type': 'economic_activity',
                    'data_subtype': 'nighttime_lights',
                    'timestamp': current_date,
                    'location': region,
                    'coordinates': self._get_region_coordinates(region),
                    'raw_data': {
                        'light_intensity': np.random.normal(100, 20),
                        'coverage_area': np.random.normal(1000, 100),
                        'brightness_change': np.random.normal(0, 5)
                    },
                    'quality_score': np.random.uniform(0.8, 1.0)
                }
                
                data_points.append(data_point)
                current_date += timedelta(days=1)
            
            return data_points
            
        except Exception as e:
            logger.error(f"卫星数据采集失败: {e}")
            return []
    
    def collect_infrastructure_data(
        self,
        region: str,
        infrastructure_type: str = "construction"
    ) -> List[Dict[str, Any]]:
        """采集基础设施建设数据"""
        try:
            data_points = []
            
            # 模拟基础设施数据
            for i in range(30):  # 30天的数据
                date = datetime.now() - timedelta(days=i)
                
                data_point = {
                    'data_type': 'infrastructure',
                    'data_subtype': infrastructure_type,
                    'timestamp': date,
                    'location': region,
                    'coordinates': self._get_region_coordinates(region),
                    'raw_data': {
                        'construction_area': np.random.normal(5000, 1000),
                        'active_sites': np.random.randint(10, 50),
                        'completion_rate': np.random.uniform(0.1, 0.9),
                        'equipment_count': np.random.randint(20, 100)
                    },
                    'quality_score': np.random.uniform(0.7, 0.95)
                }
                
                data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"基础设施数据采集失败: {e}")
            return []
    
    def _get_region_coordinates(self, region: str) -> List[float]:
        """获取区域坐标"""
        # 简化的区域坐标映射
        coordinates_map = {
            '北京': [116.4074, 39.9042],
            '上海': [121.4737, 31.2304],
            '深圳': [114.0579, 22.5431],
            '广州': [113.2644, 23.1291],
            '杭州': [120.1551, 30.2741]
        }
        
        return coordinates_map.get(region, [0.0, 0.0])


class EconomicDataCollector:
    """经济指标数据采集器"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session = requests.Session()
    
    def collect_macro_indicators(
        self,
        country: str = "CN",
        indicators: List[str] = None
    ) -> List[Dict[str, Any]]:
        """采集宏观经济指标"""
        try:
            if indicators is None:
                indicators = ['GDP', 'CPI', 'PMI', 'unemployment_rate', 'interest_rate']
            
            data_points = []
            
            for indicator in indicators:
                # 模拟经济指标数据
                for i in range(12):  # 12个月的数据
                    date = datetime.now().replace(day=1) - timedelta(days=30*i)
                    
                    data_point = {
                        'data_type': 'economic_indicator',
                        'data_subtype': indicator,
                        'timestamp': date,
                        'location': country,
                        'raw_data': {
                            'value': self._generate_indicator_value(indicator),
                            'unit': self._get_indicator_unit(indicator),
                            'period': 'monthly',
                            'revision': 'preliminary'
                        },
                        'quality_score': 0.95
                    }
                    
                    data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"经济指标采集失败: {e}")
            return []
    
    def collect_trade_data(
        self,
        country: str = "CN",
        trade_partners: List[str] = None
    ) -> List[Dict[str, Any]]:
        """采集贸易数据"""
        try:
            if trade_partners is None:
                trade_partners = ['US', 'EU', 'JP', 'KR', 'ASEAN']
            
            data_points = []
            
            for partner in trade_partners:
                for i in range(6):  # 6个月的数据
                    date = datetime.now().replace(day=1) - timedelta(days=30*i)
                    
                    data_point = {
                        'data_type': 'trade_data',
                        'data_subtype': 'bilateral_trade',
                        'timestamp': date,
                        'location': f"{country}-{partner}",
                        'raw_data': {
                            'export_value': np.random.normal(50000, 10000),
                            'import_value': np.random.normal(45000, 8000),
                            'trade_balance': 0,  # 将在处理时计算
                            'export_growth': np.random.normal(0.05, 0.1),
                            'import_growth': np.random.normal(0.03, 0.12)
                        },
                        'quality_score': 0.9
                    }
                    
                    # 计算贸易差额
                    data_point['raw_data']['trade_balance'] = (
                        data_point['raw_data']['export_value'] - 
                        data_point['raw_data']['import_value']
                    )
                    
                    data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"贸易数据采集失败: {e}")
            return []
    
    def _generate_indicator_value(self, indicator: str) -> float:
        """生成指标值"""
        value_ranges = {
            'GDP': np.random.normal(6.5, 1.0),  # GDP增长率
            'CPI': np.random.normal(2.5, 0.5),  # 通胀率
            'PMI': np.random.normal(50.5, 2.0),  # PMI指数
            'unemployment_rate': np.random.normal(4.5, 0.5),  # 失业率
            'interest_rate': np.random.normal(3.5, 0.3)  # 利率
        }
        
        return float(value_ranges.get(indicator, np.random.normal(50, 10)))
    
    def _get_indicator_unit(self, indicator: str) -> str:
        """获取指标单位"""
        units = {
            'GDP': '%',
            'CPI': '%',
            'PMI': 'index',
            'unemployment_rate': '%',
            'interest_rate': '%'
        }
        
        return units.get(indicator, 'value')


class WeatherDataCollector:
    """天气数据采集器"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session = requests.Session()
    
    def collect_weather_data(
        self,
        locations: List[str],
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """采集天气数据"""
        try:
            data_points = []
            
            for location in locations:
                for i in range(days):
                    date = datetime.now() - timedelta(days=i)
                    
                    data_point = {
                        'data_type': 'weather',
                        'data_subtype': 'daily_weather',
                        'timestamp': date,
                        'location': location,
                        'coordinates': self._get_location_coordinates(location),
                        'raw_data': {
                            'temperature_max': np.random.normal(25, 8),
                            'temperature_min': np.random.normal(15, 6),
                            'humidity': np.random.uniform(30, 90),
                            'precipitation': max(0, np.random.exponential(2)),
                            'wind_speed': np.random.exponential(5),
                            'pressure': np.random.normal(1013, 20),
                            'visibility': np.random.uniform(5, 20)
                        },
                        'quality_score': 0.95
                    }
                    
                    data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"天气数据采集失败: {e}")
            return []
    
    def collect_extreme_weather_events(
        self,
        region: str,
        event_types: List[str] = None
    ) -> List[Dict[str, Any]]:
        """采集极端天气事件"""
        try:
            if event_types is None:
                event_types = ['typhoon', 'flood', 'drought', 'heatwave', 'coldwave']
            
            data_points = []
            
            # 模拟极端天气事件
            for i in range(5):  # 5个事件
                event_type = np.random.choice(event_types)
                date = datetime.now() - timedelta(days=np.random.randint(1, 90))
                
                data_point = {
                    'data_type': 'extreme_weather',
                    'data_subtype': event_type,
                    'timestamp': date,
                    'location': region,
                    'coordinates': self._get_location_coordinates(region),
                    'raw_data': {
                        'severity': np.random.uniform(0.3, 1.0),
                        'duration_hours': np.random.randint(6, 72),
                        'affected_area': np.random.uniform(100, 10000),
                        'economic_impact': np.random.uniform(1000000, 100000000)
                    },
                    'quality_score': 0.85
                }
                
                data_points.append(data_point)
            
            return data_points
            
        except Exception as e:
            logger.error(f"极端天气事件采集失败: {e}")
            return []
    
    def _get_location_coordinates(self, location: str) -> List[float]:
        """获取位置坐标"""
        # 简化的位置坐标映射
        coordinates_map = {
            '北京': [116.4074, 39.9042],
            '上海': [121.4737, 31.2304],
            '深圳': [114.0579, 22.5431],
            '广州': [113.2644, 23.1291],
            '杭州': [120.1551, 30.2741],
            '成都': [104.0668, 30.5728],
            '西安': [108.9402, 34.3416]
        }
        
        return coordinates_map.get(location, [0.0, 0.0])


class AlternativeDataProcessor:
    """另类数据处理器"""
    
    def __init__(self):
        pass
    
    def process_satellite_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理卫星数据"""
        try:
            processed_data = {}
            
            if raw_data.get('data_subtype') == 'nighttime_lights':
                # 处理夜间灯光数据
                light_intensity = raw_data['raw_data']['light_intensity']
                coverage_area = raw_data['raw_data']['coverage_area']
                
                # 计算经济活动指数
                economic_activity_index = (light_intensity * coverage_area) / 100000
                
                processed_data = {
                    'economic_activity_index': economic_activity_index,
                    'light_intensity_normalized': light_intensity / 255,
                    'coverage_density': coverage_area / 1000,
                    'brightness_trend': raw_data['raw_data'].get('brightness_change', 0)
                }
            
            elif raw_data.get('data_subtype') == 'construction':
                # 处理建设数据
                construction_area = raw_data['raw_data']['construction_area']
                active_sites = raw_data['raw_data']['active_sites']
                
                processed_data = {
                    'construction_intensity': construction_area / 10000,
                    'site_density': active_sites / 100,
                    'development_index': (construction_area * active_sites) / 500000,
                    'completion_rate': raw_data['raw_data'].get('completion_rate', 0)
                }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"卫星数据处理失败: {e}")
            return {}
    
    def process_economic_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理经济数据"""
        try:
            processed_data = {}
            
            if raw_data.get('data_type') == 'economic_indicator':
                value = raw_data['raw_data']['value']
                indicator = raw_data.get('data_subtype')
                
                # 标准化处理
                if indicator == 'PMI':
                    processed_data['pmi_signal'] = 1 if value > 50 else -1
                    processed_data['pmi_strength'] = abs(value - 50) / 50
                elif indicator in ['GDP', 'CPI']:
                    processed_data[f'{indicator.lower()}_growth'] = value / 100
                    processed_data[f'{indicator.lower()}_trend'] = 1 if value > 0 else -1
                
                processed_data['normalized_value'] = value
                processed_data['indicator_type'] = indicator
            
            elif raw_data.get('data_type') == 'trade_data':
                export_value = raw_data['raw_data']['export_value']
                import_value = raw_data['raw_data']['import_value']
                trade_balance = raw_data['raw_data']['trade_balance']
                
                processed_data = {
                    'trade_surplus_ratio': trade_balance / (export_value + import_value),
                    'export_import_ratio': export_value / import_value,
                    'trade_volume': export_value + import_value,
                    'trade_growth': raw_data['raw_data'].get('export_growth', 0)
                }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"经济数据处理失败: {e}")
            return {}
    
    def process_weather_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理天气数据"""
        try:
            processed_data = {}
            
            if raw_data.get('data_subtype') == 'daily_weather':
                temp_max = raw_data['raw_data']['temperature_max']
                temp_min = raw_data['raw_data']['temperature_min']
                precipitation = raw_data['raw_data']['precipitation']
                
                processed_data = {
                    'temperature_range': temp_max - temp_min,
                    'average_temperature': (temp_max + temp_min) / 2,
                    'precipitation_level': min(precipitation / 50, 1.0),  # 标准化
                    'weather_comfort_index': self._calculate_comfort_index(
                        (temp_max + temp_min) / 2,
                        raw_data['raw_data']['humidity'],
                        raw_data['raw_data']['wind_speed']
                    )
                }
            
            elif raw_data.get('data_type') == 'extreme_weather':
                severity = raw_data['raw_data']['severity']
                duration = raw_data['raw_data']['duration_hours']
                economic_impact = raw_data['raw_data']['economic_impact']
                
                processed_data = {
                    'disaster_severity_index': severity,
                    'duration_impact': min(duration / 72, 1.0),
                    'economic_impact_normalized': min(economic_impact / 1000000000, 1.0),
                    'overall_impact_score': severity * (duration / 24) * (economic_impact / 100000000)
                }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"天气数据处理失败: {e}")
            return {}
    
    def _calculate_comfort_index(self, temperature: float, humidity: float, wind_speed: float) -> float:
        """计算天气舒适度指数"""
        try:
            # 简化的舒适度计算
            temp_comfort = 1 - abs(temperature - 22) / 20  # 22度为最舒适温度
            humidity_comfort = 1 - abs(humidity - 50) / 50  # 50%为最舒适湿度
            wind_comfort = 1 - min(wind_speed / 20, 1)  # 风速越小越舒适
            
            comfort_index = (temp_comfort + humidity_comfort + wind_comfort) / 3
            return max(0, min(comfort_index, 1))
            
        except Exception as e:
            logger.error(f"舒适度指数计算失败: {e}")
            return 0.5


class AlternativeDataService:
    """另类数据服务"""
    
    def __init__(self):
        self.satellite_collector = SatelliteDataCollector()
        self.economic_collector = EconomicDataCollector()
        self.weather_collector = WeatherDataCollector()
        self.processor = AlternativeDataProcessor()
    
    async def collect_alternative_data(
        self,
        source_id: int,
        collection_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """采集另类数据"""
        try:
            # 获取数据源配置
            result = await db.execute(
                select(AlternativeDataSource).where(AlternativeDataSource.id == source_id)
            )
            source = result.scalar_one_or_none()
            
            if not source or not source.is_active:
                return {"success": False, "error": "数据源不存在或未激活"}
            
            data_points = []
            
            # 根据数据类别采集数据
            if source.data_category == "satellite":
                if collection_config.get('data_type') == 'economic_activity':
                    data_points = self.satellite_collector.collect_economic_activity_data(
                        collection_config.get('region', '北京'),
                        collection_config.get('start_date', datetime.now() - timedelta(days=30)),
                        collection_config.get('end_date', datetime.now())
                    )
                elif collection_config.get('data_type') == 'infrastructure':
                    data_points = self.satellite_collector.collect_infrastructure_data(
                        collection_config.get('region', '北京'),
                        collection_config.get('infrastructure_type', 'construction')
                    )
            
            elif source.data_category == "economic":
                if collection_config.get('data_type') == 'macro_indicators':
                    data_points = self.economic_collector.collect_macro_indicators(
                        collection_config.get('country', 'CN'),
                        collection_config.get('indicators')
                    )
                elif collection_config.get('data_type') == 'trade_data':
                    data_points = self.economic_collector.collect_trade_data(
                        collection_config.get('country', 'CN'),
                        collection_config.get('trade_partners')
                    )
            
            elif source.data_category == "weather":
                if collection_config.get('data_type') == 'daily_weather':
                    data_points = self.weather_collector.collect_weather_data(
                        collection_config.get('locations', ['北京', '上海']),
                        collection_config.get('days', 30)
                    )
                elif collection_config.get('data_type') == 'extreme_weather':
                    data_points = self.weather_collector.collect_extreme_weather_events(
                        collection_config.get('region', '华东'),
                        collection_config.get('event_types')
                    )
            
            processed_count = 0
            
            for data_point in data_points:
                # 生成数据标识符
                data_identifier = self._generate_data_identifier(data_point)
                
                # 检查数据是否已存在
                existing = await db.execute(
                    select(AlternativeData).where(
                        and_(
                            AlternativeData.source_id == source_id,
                            AlternativeData.data_identifier == data_identifier
                        )
                    )
                )
                
                if existing.scalar_one_or_none():
                    continue
                
                # 处理数据
                processed_data = self._process_data_point(data_point)
                
                # 计算质量分数
                quality_metrics = self._calculate_quality_metrics(data_point, processed_data)
                
                # 创建另类数据记录
                alt_data = AlternativeData(
                    source_id=source_id,
                    data_type=data_point['data_type'],
                    data_subtype=data_point.get('data_subtype', ''),
                    data_identifier=data_identifier,
                    raw_data=data_point['raw_data'],
                    processed_data=processed_data,
                    metadata={
                        'collection_config': collection_config,
                        'processing_timestamp': datetime.utcnow().isoformat()
                    },
                    timestamp=data_point['timestamp'],
                    location=data_point.get('location', ''),
                    coordinates=data_point.get('coordinates', []),
                    quality_score=quality_metrics['overall_quality'],
                    completeness=quality_metrics['completeness'],
                    accuracy=quality_metrics['accuracy'],
                    timeliness=quality_metrics['timeliness'],
                    processing_status="processed"
                )
                
                db.add(alt_data)
                processed_count += 1
            
            # 更新数据源统计
            source.total_requests += 1
            source.successful_requests += 1
            source.last_request = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "collected_data_points": len(data_points),
                "processed_data_points": processed_count,
                "data_category": source.data_category
            }
            
        except Exception as e:
            logger.error(f"另类数据采集失败: {e}")
            await db.rollback()
            return {"success": False, "error": str(e)}
    
    def _generate_data_identifier(self, data_point: Dict[str, Any]) -> str:
        """生成数据标识符"""
        try:
            identifier_parts = [
                data_point['data_type'],
                data_point.get('data_subtype', ''),
                data_point.get('location', ''),
                data_point['timestamp'].strftime('%Y%m%d')
            ]
            
            identifier_string = "_".join(str(part) for part in identifier_parts if part)
            
            # 生成哈希值作为标识符
            import hashlib
            return hashlib.md5(identifier_string.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"数据标识符生成失败: {e}")
            return str(datetime.now().timestamp())
    
    def _process_data_point(self, data_point: Dict[str, Any]) -> Dict[str, Any]:
        """处理数据点"""
        try:
            data_category = data_point.get('data_type', '')
            
            if data_category in ['economic_activity', 'infrastructure']:
                return self.processor.process_satellite_data(data_point)
            elif data_category in ['economic_indicator', 'trade_data']:
                return self.processor.process_economic_data(data_point)
            elif data_category in ['weather', 'extreme_weather']:
                return self.processor.process_weather_data(data_point)
            else:
                return data_point.get('raw_data', {})
                
        except Exception as e:
            logger.error(f"数据点处理失败: {e}")
            return {}
    
    def _calculate_quality_metrics(
        self, 
        raw_data: Dict[str, Any], 
        processed_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """计算数据质量指标"""
        try:
            # 完整性评估
            raw_fields = len([v for v in raw_data.get('raw_data', {}).values() if v is not None])
            total_fields = len(raw_data.get('raw_data', {}))
            completeness = raw_fields / max(total_fields, 1)
            
            # 准确性评估（基于数据源质量分数）
            accuracy = raw_data.get('quality_score', 0.8)
            
            # 及时性评估
            data_age = (datetime.now() - raw_data['timestamp']).total_seconds() / 3600  # 小时
            timeliness = max(0, 1 - data_age / 168)  # 一周内数据认为是及时的
            
            # 综合质量分数
            overall_quality = (completeness * 0.3 + accuracy * 0.5 + timeliness * 0.2)
            
            return {
                'completeness': completeness,
                'accuracy': accuracy,
                'timeliness': timeliness,
                'overall_quality': overall_quality
            }
            
        except Exception as e:
            logger.error(f"质量指标计算失败: {e}")
            return {
                'completeness': 0.5,
                'accuracy': 0.5,
                'timeliness': 0.5,
                'overall_quality': 0.5
            }


# 全局另类数据服务实例
alternative_data_service = AlternativeDataService()
