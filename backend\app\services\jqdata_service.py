"""
JQData服务模块

负责JQData API的集成、认证、数据获取和配额管理
实现每个用户独立的JQData账号配置和使用监控
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import jqdatasdk as jq
import pandas as pd
from cryptography.fernet import Fernet
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import JQDataConfig, User
from app.services.cache_service import CacheService


class JQDataError(Exception):
    """JQData相关异常"""
    pass


class QuotaExceededError(JQDataError):
    """配额超限异常"""
    pass


class AuthenticationError(JQDataError):
    """认证失败异常"""
    pass


class JQDataService:
    """JQData服务类"""
    
    def __init__(self):
        self.auth_cache: Dict[int, Dict] = {}  # 用户认证缓存
        self.rate_limiter: Dict[int, List[float]] = {}  # 用户限流记录
        self.cache_service = CacheService()
        self._cipher = Fernet(settings.ENCRYPTION_KEY.encode())
    
    # =============================================================================
    # 认证管理
    # =============================================================================
    
    async def authenticate_user(self, user_id: int, db: AsyncSession) -> bool:
        """用户JQData认证"""
        try:
            # 检查缓存中的认证状态
            if self._is_auth_cached(user_id):
                return True
            
            # 获取用户配置
            config = await self._get_user_config(user_id, db)
            if not config:
                raise AuthenticationError("请先配置JQData账号信息")
            
            if not config.is_active:
                raise AuthenticationError("JQData配置已禁用")
            
            # 解密密码
            decrypted_password = self._decrypt_password(config.encrypted_password)
            
            # JQData认证 - 按照官方文档规范
            # 支持邮箱和手机号两种登录方式
            if config.login_type == 'mobile':
                # 手机号登录
                jq.auth(config.username, decrypted_password, host='https://dataapi.joinquant.com', port=443)
            else:
                # 邮箱登录（默认）
                jq.auth(config.username, decrypted_password)
            
            # 更新认证成功记录
            await self._update_auth_success(config.id, db)
            
            # 缓存认证状态
            self.auth_cache[user_id] = {
                'authenticated': True,
                'timestamp': datetime.utcnow(),
                'config_id': config.id,
                'username': config.username
            }
            
            logger.info(f"用户 {user_id} JQData认证成功")
            return True
            
        except Exception as e:
            # 更新认证失败记录
            if 'config' in locals():
                await self._update_auth_failure(config.id, str(e), db)
            
            logger.error(f"用户 {user_id} JQData认证失败: {e}")
            raise AuthenticationError(f"JQData认证失败: {str(e)}")
    
    def _is_auth_cached(self, user_id: int) -> bool:
        """检查认证缓存是否有效"""
        if user_id not in self.auth_cache:
            return False
        
        cache_data = self.auth_cache[user_id]
        if not cache_data.get('authenticated'):
            return False
        
        # 检查缓存是否过期（30分钟）
        cache_time = cache_data.get('timestamp')
        if not cache_time or datetime.utcnow() - cache_time > timedelta(minutes=30):
            del self.auth_cache[user_id]
            return False
        
        return True
    
    async def logout_user(self, user_id: int) -> None:
        """用户登出JQData"""
        try:
            if user_id in self.auth_cache:
                del self.auth_cache[user_id]
            
            # JQData登出
            jq.logout()
            logger.info(f"用户 {user_id} JQData登出成功")
            
        except Exception as e:
            logger.error(f"用户 {user_id} JQData登出失败: {e}")
    
    # =============================================================================
    # 配额管理
    # =============================================================================
    
    async def check_quota(self, user_id: int, db: AsyncSession, api_calls: int = 1) -> bool:
        """检查用户配额"""
        config = await self._get_user_config(user_id, db)
        if not config:
            return False
        
        # 检查今日配额
        today = datetime.utcnow().date()
        if config.quota_reset_date != today:
            # 重置今日配额
            await self._reset_daily_quota(config.id, db)
            config.quota_used = 0
        
        # 检查是否超限
        if config.quota_used + api_calls > config.quota_total:
            raise QuotaExceededError(f"API配额不足，今日已使用 {config.quota_used}/{config.quota_total}")
        
        return True
    
    async def consume_quota(self, user_id: int, db: AsyncSession, api_calls: int = 1) -> None:
        """消费用户配额"""
        config = await self._get_user_config(user_id, db)
        if not config:
            return
        
        # 更新配额使用量
        await db.execute(
            update(JQDataConfig)
            .where(JQDataConfig.id == config.id)
            .values(
                quota_used=JQDataConfig.quota_used + api_calls,
                last_used_at=datetime.utcnow(),
                total_api_calls=JQDataConfig.total_api_calls + api_calls
            )
        )
        await db.commit()
    
    async def get_quota_info(self, user_id: int, db: AsyncSession) -> Dict[str, Any]:
        """获取用户配额信息"""
        config = await self._get_user_config(user_id, db)
        if not config:
            return {"configured": False}
        
        return {
            "configured": True,
            "quota_total": config.quota_total,
            "quota_used": config.quota_used,
            "quota_remaining": config.quota_total - config.quota_used,
            "quota_reset_date": config.quota_reset_date,
            "last_used_at": config.last_used_at,
            "total_api_calls": config.total_api_calls
        }
    
    # =============================================================================
    # 限流管理
    # =============================================================================
    
    async def check_rate_limit(self, user_id: int) -> bool:
        """检查用户API调用频率限制"""
        now = time.time()
        minute_ago = now - 60
        
        # 初始化用户限流记录
        if user_id not in self.rate_limiter:
            self.rate_limiter[user_id] = []
        
        # 清理过期记录
        self.rate_limiter[user_id] = [
            timestamp for timestamp in self.rate_limiter[user_id]
            if timestamp > minute_ago
        ]
        
        # 检查是否超过限制
        if len(self.rate_limiter[user_id]) >= settings.JQDATA_RATE_LIMIT_PER_MINUTE:
            return False
        
        # 记录当前请求
        self.rate_limiter[user_id].append(now)
        return True
    
    # =============================================================================
    # 数据获取方法
    # =============================================================================
    
    async def get_all_securities(self, user_id: int, db: AsyncSession,
                                types: List[str] = None, date: str = None) -> pd.DataFrame:
        """
        获取所有标的信息
        按照JQData官方文档规范：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842

        Args:
            user_id: 用户ID
            db: 数据库会话
            types: 标的类型列表，如['stock', 'fund', 'index', 'futures', 'etf', 'lof', 'fja', 'fjb']
            date: 日期，格式为YYYY-MM-DD，默认为当前日期
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            if types is None:
                types = ['stock']  # 默认获取股票

            # 检查缓存
            cache_key = f"all_securities:{':'.join(types)}:{date or 'current'}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 从JQData获取 - 按照官方API规范
            if date:
                data = jq.get_all_securities(types=types, date=date)
            else:
                data = jq.get_all_securities(types=types)

            # 缓存数据（1小时）
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=3600)

            logger.info(f"用户 {user_id} 获取标的信息成功，类型: {types}, 共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取标的信息失败: {e}")
            raise JQDataError(f"获取标的信息失败: {str(e)}")

    async def get_price(self, user_id: int, db: AsyncSession,
                       security: str, start_date: str = None, end_date: str = None,
                       frequency: str = 'daily', fields: List[str] = None,
                       skip_paused: bool = False, fq: str = 'pre') -> pd.DataFrame:
        """
        获取历史价格数据
        按照JQData官方文档规范

        Args:
            user_id: 用户ID
            db: 数据库会话
            security: 标的代码，如'000001.XSHE'
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD
            frequency: 频率，支持'daily', '1m', '5m', '15m', '30m', '60m'
            fields: 字段列表，如['open', 'close', 'high', 'low', 'volume', 'money']
            skip_paused: 是否跳过停牌日期
            fq: 复权选项，'pre'前复权, 'post'后复权, None不复权
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            if fields is None:
                fields = ['open', 'close', 'high', 'low', 'volume', 'money']

            # 检查缓存
            cache_key = f"price:{security}:{start_date}:{end_date}:{frequency}:{':'.join(fields)}:{fq}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 从JQData获取价格数据
            data = jq.get_price(
                security=security,
                start_date=start_date,
                end_date=end_date,
                frequency=frequency,
                fields=fields,
                skip_paused=skip_paused,
                fq=fq
            )

            # 缓存数据（根据频率设置不同的缓存时间）
            cache_ttl = 3600 if frequency == 'daily' else 300  # 日线缓存1小时，分钟线缓存5分钟
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=cache_ttl)

            logger.info(f"用户 {user_id} 获取价格数据成功，标的: {security}, 共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            raise JQDataError(f"获取价格数据失败: {str(e)}")

    async def get_current_data(self, user_id: int, db: AsyncSession,
                              security: str, fields: List[str] = None) -> pd.DataFrame:
        """
        获取当前价格数据
        按照JQData官方文档规范

        Args:
            user_id: 用户ID
            db: 数据库会话
            security: 标的代码或代码列表
            fields: 字段列表，如['last_price', 'high_limit', 'low_limit', 'volume', 'money']
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            if fields is None:
                fields = ['last_price', 'high_limit', 'low_limit', 'volume', 'money', 'avg', 'bid1', 'ask1']

            # 获取当前数据（不缓存，因为是实时数据）
            data = jq.get_current_data(security, fields)

            logger.info(f"用户 {user_id} 获取当前数据成功，标的: {security}")
            return data

        except Exception as e:
            logger.error(f"获取当前数据失败: {e}")
            raise JQDataError(f"获取当前数据失败: {str(e)}")

    async def get_fundamentals(self, user_id: int, db: AsyncSession,
                              query_object, date: str = None, statDate: str = None) -> pd.DataFrame:
        """
        获取财务数据
        按照JQData官方文档规范

        Args:
            user_id: 用户ID
            db: 数据库会话
            query_object: 查询对象，使用jq.query()构建
            date: 查询日期
            statDate: 统计日期
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 获取财务数据
            data = jq.get_fundamentals(query_object, date=date, statDate=statDate)

            logger.info(f"用户 {user_id} 获取财务数据成功，共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取财务数据失败: {e}")
            raise JQDataError(f"获取财务数据失败: {str(e)}")

    async def get_industry(self, user_id: int, db: AsyncSession,
                          security: str, date: str = None) -> Dict[str, Any]:
        """
        获取行业信息
        按照JQData官方文档规范

        Args:
            user_id: 用户ID
            db: 数据库会话
            security: 标的代码
            date: 查询日期
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"industry:{security}:{date or 'current'}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data

            # 获取行业信息
            data = jq.get_industry(security, date=date)

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, data, ttl=86400)

            logger.info(f"用户 {user_id} 获取行业信息成功，标的: {security}")
            return data

        except Exception as e:
            logger.error(f"获取行业信息失败: {e}")
            raise JQDataError(f"获取行业信息失败: {str(e)}")

    async def normalize_code(self, user_id: int, db: AsyncSession,
                            code: str) -> str:
        """
        将标的代码转化成聚宽标准格式
        按照官方文档：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9839
        """
        await self._ensure_authenticated(user_id, db)

        try:
            # 使用JQData的normalize_code函数
            normalized_code = jq.normalize_code(code)

            logger.info(f"用户 {user_id} 标准化代码成功: {code} -> {normalized_code}")
            return normalized_code

        except Exception as e:
            logger.error(f"标准化代码失败: {e}")
            raise JQDataError(f"标准化代码失败: {str(e)}")

    async def get_trade_days(self, user_id: int, db: AsyncSession,
                            start_date: str = None, end_date: str = None,
                            count: int = None) -> List[str]:
        """
        获取指定范围交易日
        按照官方文档：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9837
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"trade_days:{start_date}:{end_date}:{count}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data

            # 获取交易日
            if count:
                trade_days = jq.get_trade_days(start_date=start_date, end_date=end_date, count=count)
            else:
                trade_days = jq.get_trade_days(start_date=start_date, end_date=end_date)

            # 转换为字符串列表
            trade_days_list = [day.strftime('%Y-%m-%d') for day in trade_days]

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, trade_days_list, ttl=86400)

            logger.info(f"用户 {user_id} 获取交易日成功，共 {len(trade_days_list)} 天")
            return trade_days_list

        except Exception as e:
            logger.error(f"获取交易日失败: {e}")
            raise JQDataError(f"获取交易日失败: {str(e)}")

    async def get_security_info(self, user_id: int, db: AsyncSession,
                               code: str) -> Dict[str, Any]:
        """
        获取单支标的信息
        按照官方文档：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9840
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"security_info:{code}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data

            # 获取标的信息
            security_info = jq.get_security_info(code)

            # 转换为字典格式
            info_dict = {
                'display_name': security_info.display_name,
                'name': security_info.name,
                'start_date': security_info.start_date.strftime('%Y-%m-%d') if security_info.start_date else None,
                'end_date': security_info.end_date.strftime('%Y-%m-%d') if security_info.end_date else None,
                'type': security_info.type,
                'parent': security_info.parent if hasattr(security_info, 'parent') else None,
            }

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, info_dict, ttl=86400)

            logger.info(f"用户 {user_id} 获取标的信息成功: {code}")
            return info_dict

        except Exception as e:
            logger.error(f"获取标的信息失败: {e}")
            raise JQDataError(f"获取标的信息失败: {str(e)}")

    async def get_bars(self, user_id: int, db: AsyncSession,
                      security: str, count: int, unit: str = '1d',
                      fields: List[str] = None, include_now: bool = False,
                      end_dt: str = None, fq_ref_date: str = None) -> pd.DataFrame:
        """
        获取历史数据（按数量）
        按照官方文档，get_bars和get_price的区别：
        https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10242
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            if fields is None:
                fields = ['open', 'close', 'high', 'low', 'volume', 'money']

            # 检查缓存
            cache_key = f"bars:{security}:{count}:{unit}:{':'.join(fields)}:{end_dt}:{include_now}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 获取历史数据
            data = jq.get_bars(
                security=security,
                count=count,
                unit=unit,
                fields=fields,
                include_now=include_now,
                end_dt=end_dt,
                fq_ref_date=fq_ref_date
            )

            # 缓存数据
            cache_ttl = 3600 if '1d' in unit else 300  # 日线缓存1小时，其他缓存5分钟
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=cache_ttl)

            logger.info(f"用户 {user_id} 获取历史数据成功，标的: {security}, 共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            raise JQDataError(f"获取历史数据失败: {str(e)}")

    async def get_concept(self, user_id: int, db: AsyncSession,
                         security: str, date: str = None) -> Dict[str, Any]:
        """
        获取概念信息
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"concept:{security}:{date or 'current'}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data

            # 获取概念信息
            data = jq.get_concept(security, date=date)

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, data, ttl=86400)

            logger.info(f"用户 {user_id} 获取概念信息成功，标的: {security}")
            return data

        except Exception as e:
            logger.error(f"获取概念信息失败: {e}")
            raise JQDataError(f"获取概念信息失败: {str(e)}")

    async def get_index_stocks(self, user_id: int, db: AsyncSession,
                              index: str, date: str = None) -> List[str]:
        """
        获取指数成分股
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"index_stocks:{index}:{date or 'current'}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return cached_data

            # 获取指数成分股
            stocks = jq.get_index_stocks(index, date=date)

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, stocks, ttl=86400)

            logger.info(f"用户 {user_id} 获取指数成分股成功，指数: {index}, 共 {len(stocks)} 只")
            return stocks

        except Exception as e:
            logger.error(f"获取指数成分股失败: {e}")
            raise JQDataError(f"获取指数成分股失败: {str(e)}")

    async def get_index_weights(self, user_id: int, db: AsyncSession,
                               index: str, date: str = None) -> pd.DataFrame:
        """
        获取指数成分股权重
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"index_weights:{index}:{date or 'current'}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 获取指数成分股权重
            weights = jq.get_index_weights(index, date=date)

            # 缓存数据（24小时）
            await self.cache_service.set(cache_key, weights.to_dict('records'), ttl=86400)

            logger.info(f"用户 {user_id} 获取指数权重成功，指数: {index}, 共 {len(weights)} 条")
            return weights

        except Exception as e:
            logger.error(f"获取指数权重失败: {e}")
            raise JQDataError(f"获取指数权重失败: {str(e)}")

    async def get_money_flow(self, user_id: int, db: AsyncSession,
                            security: str, start_date: str = None,
                            end_date: str = None, fields: List[str] = None) -> pd.DataFrame:
        """
        获取资金流向数据
        按照官方文档：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10665
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            if fields is None:
                fields = ['net_amount_main', 'net_pct_main', 'net_amount_xl', 'net_pct_xl',
                         'net_amount_l', 'net_pct_l', 'net_amount_m', 'net_pct_m',
                         'net_amount_s', 'net_pct_s']

            # 检查缓存
            cache_key = f"money_flow:{security}:{start_date}:{end_date}:{':'.join(fields)}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 获取资金流向数据
            data = jq.get_money_flow(
                security=security,
                start_date=start_date,
                end_date=end_date,
                fields=fields
            )

            # 缓存数据（1小时）
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=3600)

            logger.info(f"用户 {user_id} 获取资金流向数据成功，标的: {security}, 共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取资金流向数据失败: {e}")
            raise JQDataError(f"获取资金流向数据失败: {str(e)}")

    async def get_cctv_news(self, user_id: int, db: AsyncSession,
                           start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取CCTV新闻联播文本数据
        按照官方文档：https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9961

        Args:
            user_id: 用户ID
            db: 数据库会话
            start_date: 开始日期，格式YYYY-MM-DD
            end_date: 结束日期，格式YYYY-MM-DD

        Returns:
            包含新闻联播文本数据的DataFrame
        """
        await self._ensure_authenticated(user_id, db)
        await self._check_and_consume_quota(user_id, db, 1)

        try:
            # 检查缓存
            cache_key = f"cctv_news:{start_date}:{end_date}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)

            # 获取CCTV新闻联播数据
            # 根据JQData官方文档，使用get_cctv_news函数
            if start_date and end_date:
                data = jq.get_cctv_news(start_date=start_date, end_date=end_date)
            elif start_date:
                data = jq.get_cctv_news(start_date=start_date)
            else:
                # 默认获取最近7天的新闻
                from datetime import datetime, timedelta
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
                data = jq.get_cctv_news(start_date=start_date, end_date=end_date)

            # 缓存数据（6小时，新闻数据更新频率较低）
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=21600)

            logger.info(f"用户 {user_id} 获取CCTV新闻数据成功，共 {len(data)} 条")
            return data

        except Exception as e:
            logger.error(f"获取CCTV新闻数据失败: {e}")
            raise JQDataError(f"获取CCTV新闻数据失败: {str(e)}")

    async def analyze_news_sentiment(self, user_id: int, db: AsyncSession,
                                   news_data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析新闻情绪

        Args:
            user_id: 用户ID
            db: 数据库会话
            news_data: 新闻数据DataFrame

        Returns:
            情绪分析结果
        """
        try:
            # 简单的情绪分析逻辑
            # 这里可以集成更复杂的NLP模型

            positive_keywords = [
                '增长', '上涨', '利好', '积极', '稳定', '发展', '提升', '改善',
                '优化', '创新', '突破', '成功', '繁荣', '振兴', '复苏'
            ]

            negative_keywords = [
                '下跌', '下降', '风险', '困难', '挑战', '问题', '危机', '衰退',
                '减少', '恶化', '担忧', '压力', '不确定', '波动', '调整'
            ]

            neutral_keywords = [
                '会议', '讨论', '研究', '分析', '报告', '数据', '统计', '调查'
            ]

            sentiment_scores = []

            for _, row in news_data.iterrows():
                content = str(row.get('content', ''))
                title = str(row.get('title', ''))
                text = content + ' ' + title

                positive_count = sum(1 for keyword in positive_keywords if keyword in text)
                negative_count = sum(1 for keyword in negative_keywords if keyword in text)
                neutral_count = sum(1 for keyword in neutral_keywords if keyword in text)

                total_count = positive_count + negative_count + neutral_count

                if total_count == 0:
                    sentiment_score = 0  # 中性
                else:
                    sentiment_score = (positive_count - negative_count) / total_count

                sentiment_scores.append({
                    'date': row.get('date'),
                    'sentiment_score': sentiment_score,
                    'positive_count': positive_count,
                    'negative_count': negative_count,
                    'neutral_count': neutral_count
                })

            # 计算整体情绪指标
            avg_sentiment = sum(s['sentiment_score'] for s in sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0

            result = {
                'overall_sentiment': avg_sentiment,
                'sentiment_trend': 'positive' if avg_sentiment > 0.1 else 'negative' if avg_sentiment < -0.1 else 'neutral',
                'daily_sentiment': sentiment_scores,
                'total_news': len(news_data),
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            logger.info(f"用户 {user_id} 新闻情绪分析完成，整体情绪: {result['sentiment_trend']}")
            return result

        except Exception as e:
            logger.error(f"新闻情绪分析失败: {e}")
            raise JQDataError(f"新闻情绪分析失败: {str(e)}")
    
    async def get_price_data(
        self,
        user_id: int,
        db: AsyncSession,
        symbols: List[str],
        start_date: str,
        end_date: str,
        frequency: str = "daily"
    ) -> pd.DataFrame:
        """获取价格数据"""
        await self._ensure_authenticated(user_id, db)
        
        # 根据数据量估算API调用次数
        api_calls = max(1, len(symbols) // 50)  # 每50只股票算1次调用
        await self._check_and_consume_quota(user_id, db, api_calls)
        
        try:
            # 检查缓存
            cache_key = f"price_data:{':'.join(symbols)}:{start_date}:{end_date}:{frequency}"
            cached_data = await self.cache_service.get(cache_key)
            if cached_data:
                return pd.DataFrame(cached_data)
            
            # 从JQData获取
            data = jq.get_price(
                symbols,
                start_date=start_date,
                end_date=end_date,
                frequency=frequency,
                fields=['open', 'close', 'high', 'low', 'volume', 'money']
            )
            
            # 缓存数据
            cache_ttl = 3600 if frequency == "daily" else 300  # 日线数据缓存1小时，分钟数据5分钟
            await self.cache_service.set(cache_key, data.to_dict('records'), ttl=cache_ttl)
            
            logger.info(f"用户 {user_id} 获取价格数据成功，{len(symbols)} 只股票，{len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            raise JQDataError(f"获取价格数据失败: {str(e)}")
    
    # =============================================================================
    # 私有方法
    # =============================================================================
    
    async def _get_user_config(self, user_id: int, db: AsyncSession) -> Optional[JQDataConfig]:
        """获取用户JQData配置"""
        result = await db.execute(
            select(JQDataConfig).where(JQDataConfig.user_id == user_id)
        )
        return result.scalar_one_or_none()
    
    def _encrypt_password(self, password: str) -> str:
        """加密密码"""
        return self._cipher.encrypt(password.encode()).decode()
    
    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        return self._cipher.decrypt(encrypted_password.encode()).decode()
    
    async def _ensure_authenticated(self, user_id: int, db: AsyncSession) -> None:
        """确保用户已认证"""
        if not await self.authenticate_user(user_id, db):
            raise AuthenticationError("JQData认证失败")
    
    async def _check_and_consume_quota(self, user_id: int, db: AsyncSession, api_calls: int) -> None:
        """检查并消费配额"""
        if not await self.check_rate_limit(user_id):
            raise JQDataError("API调用频率超限，请稍后重试")
        
        await self.check_quota(user_id, db, api_calls)
        await self.consume_quota(user_id, db, api_calls)
    
    async def _update_auth_success(self, config_id: int, db: AsyncSession) -> None:
        """更新认证成功记录"""
        await db.execute(
            update(JQDataConfig)
            .where(JQDataConfig.id == config_id)
            .values(
                last_auth_success=datetime.utcnow(),
                auth_failure_count=0,
                last_auth_error=None
            )
        )
        await db.commit()
    
    async def _update_auth_failure(self, config_id: int, error_msg: str, db: AsyncSession) -> None:
        """更新认证失败记录"""
        await db.execute(
            update(JQDataConfig)
            .where(JQDataConfig.id == config_id)
            .values(
                last_auth_error=error_msg,
                auth_failure_count=JQDataConfig.auth_failure_count + 1
            )
        )
        await db.commit()
    
    async def _reset_daily_quota(self, config_id: int, db: AsyncSession) -> None:
        """重置每日配额"""
        await db.execute(
            update(JQDataConfig)
            .where(JQDataConfig.id == config_id)
            .values(
                quota_used=0,
                quota_reset_date=datetime.utcnow().date()
            )
        )
        await db.commit()

    async def get_current_data(
        self,
        symbols: List[str],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取实时数据"""
        try:
            # 这里应该调用JQData API获取实时数据
            # 由于没有真实的JQData配置，返回空数据
            logger.warning("JQData实时数据接口未实现，返回空数据")

            current_data = {
                'success': False,
                'data': {},
                'message': 'JQData实时数据接口未配置'
            }

            return current_data

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            raise JQDataError(f"获取实时数据失败: {str(e)}")


# 全局JQData服务实例
jqdata_service = JQDataService()
