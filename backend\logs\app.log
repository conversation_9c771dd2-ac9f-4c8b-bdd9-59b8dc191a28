{"text": "2025-08-25T15:41:11.673498+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016574", "seconds": 0.016574}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 6316, "name": "MainProcess"}, "thread": {"id": 34868, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:41:11.673498+08:00", "timestamp": 1756107671.673498}}}
{"text": "2025-08-25T15:42:12.560287+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.027879", "seconds": 0.027879}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33640, "name": "MainProcess"}, "thread": {"id": 23220, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:42:12.560287+08:00", "timestamp": 1756107732.560287}}}
{"text": "2025-08-25T15:43:30.656580+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016732", "seconds": 0.016732}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22124, "name": "MainProcess"}, "thread": {"id": 35580, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:43:30.656580+08:00", "timestamp": 1756107810.65658}}}
{"text": "2025-08-25T15:54:59.309598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021286", "seconds": 0.021286}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11704, "name": "MainProcess"}, "thread": {"id": 11248, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:54:59.309598+08:00", "timestamp": 1756108499.309598}}}
{"text": "2025-08-25T15:55:34.227297+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012456", "seconds": 0.012456}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34896, "name": "MainProcess"}, "thread": {"id": 24432, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:55:34.227297+08:00", "timestamp": 1756108534.227297}}}
{"text": "2025-08-25T15:57:05.503448+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013665", "seconds": 0.013665}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33444, "name": "MainProcess"}, "thread": {"id": 10596, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:57:05.503448+08:00", "timestamp": 1756108625.503448}}}
{"text": "2025-08-25T15:58:48.300238+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013064", "seconds": 0.013064}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 1192, "name": "MainProcess"}, "thread": {"id": 39076, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:58:48.300238+08:00", "timestamp": 1756108728.300238}}}
{"text": "2025-08-25T16:00:08.916536+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012267", "seconds": 0.012267}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12428, "name": "MainProcess"}, "thread": {"id": 11828, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:00:08.916536+08:00", "timestamp": 1756108808.916536}}}
{"text": "2025-08-25T16:01:23.471462+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025699", "seconds": 0.025699}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29588, "name": "MainProcess"}, "thread": {"id": 2516, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:23.471462+08:00", "timestamp": 1756108883.471462}}}
{"text": "2025-08-25T16:01:40.164598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016917", "seconds": 0.016917}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8620, "name": "MainProcess"}, "thread": {"id": 34500, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:40.164598+08:00", "timestamp": 1756108900.164598}}}
{"text": "2025-08-25T16:03:17.001901+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018285", "seconds": 0.018285}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26752, "name": "MainProcess"}, "thread": {"id": 15424, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:03:17.001901+08:00", "timestamp": 1756108997.001901}}}
{"text": "2025-08-25T16:05:26.759085+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014915", "seconds": 0.014915}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15296, "name": "MainProcess"}, "thread": {"id": 35092, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:05:26.759085+08:00", "timestamp": 1756109126.759085}}}
{"text": "2025-08-25T16:07:20.072929+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.040320", "seconds": 0.04032}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14668, "name": "MainProcess"}, "thread": {"id": 34080, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:20.072929+08:00", "timestamp": 1756109240.072929}}}
{"text": "2025-08-25T16:07:42.823960+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011571", "seconds": 0.011571}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 17652, "name": "MainProcess"}, "thread": {"id": 15056, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:42.823960+08:00", "timestamp": 1756109262.82396}}}
{"text": "2025-08-25T16:08:36.933605+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.023327", "seconds": 0.023327}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 20416, "name": "MainProcess"}, "thread": {"id": 7440, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:08:36.933605+08:00", "timestamp": 1756109316.933605}}}
{"text": "2025-08-25T16:09:56.064018+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012766", "seconds": 0.012766}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23572, "name": "MainProcess"}, "thread": {"id": 23904, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:09:56.064018+08:00", "timestamp": 1756109396.064018}}}
{"text": "2025-08-25T16:12:06.815575+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013296", "seconds": 0.013296}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24400, "name": "MainProcess"}, "thread": {"id": 11084, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:12:06.815575+08:00", "timestamp": 1756109526.815575}}}
{"text": "2025-08-25T16:14:24.610105+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021843", "seconds": 0.021843}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 21084, "name": "MainProcess"}, "thread": {"id": 26248, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:14:24.610105+08:00", "timestamp": 1756109664.610105}}}
{"text": "2025-08-25T16:15:35.349640+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.028526", "seconds": 0.028526}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24604, "name": "MainProcess"}, "thread": {"id": 20428, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:35.349640+08:00", "timestamp": 1756109735.34964}}}
{"text": "2025-08-25T16:15:55.928450+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014180", "seconds": 0.01418}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11800, "name": "MainProcess"}, "thread": {"id": 8952, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:55.928450+08:00", "timestamp": 1756109755.92845}}}
{"text": "2025-08-25T16:18:10.990551+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015095", "seconds": 0.015095}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25744, "name": "MainProcess"}, "thread": {"id": 15804, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:18:10.990551+08:00", "timestamp": 1756109890.990551}}}
{"text": "2025-08-25T16:19:20.000520+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025328", "seconds": 0.025328}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26100, "name": "MainProcess"}, "thread": {"id": 3044, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:20.000520+08:00", "timestamp": 1756109960.00052}}}
{"text": "2025-08-25T16:19:54.140265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018340", "seconds": 0.01834}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11244, "name": "MainProcess"}, "thread": {"id": 20612, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:54.140265+08:00", "timestamp": 1756109994.140265}}}
{"text": "2025-08-25T16:20:58.841288+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016417", "seconds": 0.016417}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29052, "name": "MainProcess"}, "thread": {"id": 26900, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:20:58.841288+08:00", "timestamp": 1756110058.841288}}}
{"text": "2025-08-25T17:37:34.605265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024741", "seconds": 0.024741}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23044, "name": "MainProcess"}, "thread": {"id": 7880, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:37:34.605265+08:00", "timestamp": 1756114654.605265}}}
{"text": "2025-08-25T17:38:13.697391+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.020144", "seconds": 0.020144}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2312, "name": "MainProcess"}, "thread": {"id": 2348, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:38:13.697391+08:00", "timestamp": 1756114693.697391}}}
{"text": "2025-08-25T17:39:18.429524+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012511", "seconds": 0.012511}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25248, "name": "MainProcess"}, "thread": {"id": 35204, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:18.429524+08:00", "timestamp": 1756114758.429524}}}
{"text": "2025-08-25T17:39:40.523061+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012918", "seconds": 0.012918}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14412, "name": "MainProcess"}, "thread": {"id": 38212, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:40.523061+08:00", "timestamp": 1756114780.523061}}}
{"text": "2025-08-25T17:41:34.377101+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013387", "seconds": 0.013387}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19540, "name": "MainProcess"}, "thread": {"id": 37248, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:41:34.377101+08:00", "timestamp": 1756114894.377101}}}
{"text": "2025-08-25T17:43:50.331959+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.019421", "seconds": 0.019421}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 30872, "name": "MainProcess"}, "thread": {"id": 24588, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:43:50.331959+08:00", "timestamp": 1756115030.331959}}}
{"text": "2025-08-25T17:44:58.314236+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.186816", "seconds": 0.186816}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25272, "name": "MainProcess"}, "thread": {"id": 11768, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:44:58.314236+08:00", "timestamp": 1756115098.314236}}}
{"text": "2025-08-25T17:45:21.727969+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.022318", "seconds": 0.022318}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7824, "name": "MainProcess"}, "thread": {"id": 22860, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:21.727969+08:00", "timestamp": 1756115121.727969}}}
{"text": "2025-08-25T17:45:58.603882+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012697", "seconds": 0.012697}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 32092, "name": "MainProcess"}, "thread": {"id": 13604, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:58.603882+08:00", "timestamp": 1756115158.603882}}}
{"text": "2025-08-25T17:46:45.420354+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024966", "seconds": 0.024966}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16672, "name": "MainProcess"}, "thread": {"id": 14608, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:46:45.420354+08:00", "timestamp": 1756115205.420354}}}
{"text": "2025-08-25T17:47:32.205034+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018365", "seconds": 0.018365}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15256, "name": "MainProcess"}, "thread": {"id": 30528, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:47:32.205034+08:00", "timestamp": 1756115252.205034}}}
{"text": "2025-08-25T17:48:17.421548+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016666", "seconds": 0.016666}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.421548+08:00", "timestamp": 1756115297.421548}}}
{"text": "2025-08-25T17:48:17.426160+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021278", "seconds": 0.021278}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.426160+08:00", "timestamp": 1756115297.42616}}}
{"text": "2025-08-26T10:23:42.578015+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.030022", "seconds": 0.030022}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34056, "name": "SpawnProcess-1"}, "thread": {"id": 11136, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:23:42.578015+08:00", "timestamp": 1756175022.578015}}}
{"text": "2025-08-26T10:38:25.467423+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012902", "seconds": 0.012902}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8400, "name": "SpawnProcess-1"}, "thread": {"id": 37884, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:38:25.467423+08:00", "timestamp": 1756175905.467423}}}
{"text": "2025-08-26T10:39:33.573534+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016416", "seconds": 0.016416}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23316, "name": "SpawnProcess-1"}, "thread": {"id": 6036, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:39:33.573534+08:00", "timestamp": 1756175973.573534}}}
{"text": "2025-08-26T10:57:33.882595+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013681", "seconds": 0.013681}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 9460, "name": "SpawnProcess-1"}, "thread": {"id": 16588, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:57:33.882595+08:00", "timestamp": 1756177053.882595}}}
{"text": "2025-08-26T11:01:51.490621+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015196", "seconds": 0.015196}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11956, "name": "SpawnProcess-1"}, "thread": {"id": 8792, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:01:51.490621+08:00", "timestamp": 1756177311.490621}}}
{"text": "2025-08-26T11:03:47.977917+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014096", "seconds": 0.014096}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10872, "name": "SpawnProcess-1"}, "thread": {"id": 19792, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:03:47.977917+08:00", "timestamp": 1756177427.977917}}}
{"text": "2025-08-26T11:05:21.095898+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.193370", "seconds": 0.19337}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 5468, "name": "MainProcess"}, "thread": {"id": 13396, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:21.095898+08:00", "timestamp": 1756177521.095898}}}
{"text": "2025-08-26T11:05:53.502188+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009972", "seconds": 0.009972}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10380, "name": "SpawnProcess-2"}, "thread": {"id": 1824, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:53.502188+08:00", "timestamp": 1756177553.502188}}}
{"text": "2025-08-26T11:05:53.550286+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015269", "seconds": 0.015269}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22320, "name": "SpawnProcess-2"}, "thread": {"id": 24840, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:53.550286+08:00", "timestamp": 1756177553.550286}}}
{"text": "2025-08-26T11:05:57.538399+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.046183", "seconds": 4.046183}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10380, "name": "SpawnProcess-2"}, "thread": {"id": 1824, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:57.538399+08:00", "timestamp": 1756177557.538399}}}
{"text": "2025-08-26T11:05:57.539397+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.004380", "seconds": 4.00438}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22320, "name": "SpawnProcess-2"}, "thread": {"id": 24840, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:57.539397+08:00", "timestamp": 1756177557.539397}}}
{"text": "2025-08-26T11:06:09.706153+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012510", "seconds": 0.01251}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15424, "name": "MainProcess"}, "thread": {"id": 11436, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:09.706153+08:00", "timestamp": 1756177569.706153}}}
{"text": "2025-08-26T11:06:13.312041+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.618398", "seconds": 3.618398}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 15424, "name": "MainProcess"}, "thread": {"id": 11436, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:13.312041+08:00", "timestamp": 1756177573.312041}}}
{"text": "2025-08-26T11:06:45.300814+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013985", "seconds": 0.013985}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16004, "name": "SpawnProcess-3"}, "thread": {"id": 5904, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:45.300814+08:00", "timestamp": 1756177605.300814}}}
{"text": "2025-08-26T11:06:45.326821+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013948", "seconds": 0.013948}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12580, "name": "SpawnProcess-3"}, "thread": {"id": 10568, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:45.326821+08:00", "timestamp": 1756177605.326821}}}
{"text": "2025-08-26T11:06:49.581393+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.294564", "seconds": 4.294564}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 16004, "name": "SpawnProcess-3"}, "thread": {"id": 5904, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:49.581393+08:00", "timestamp": 1756177609.581393}}}
{"text": "2025-08-26T11:06:49.596872+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.283999", "seconds": 4.283999}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 12580, "name": "SpawnProcess-3"}, "thread": {"id": 10568, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:49.596872+08:00", "timestamp": 1756177609.596872}}}
{"text": "2025-08-26T11:07:12.321995+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011499", "seconds": 0.011499}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25728, "name": "SpawnProcess-4"}, "thread": {"id": 17108, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:12.321995+08:00", "timestamp": 1756177632.321995}}}
{"text": "2025-08-26T11:07:12.322992+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010500", "seconds": 0.0105}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19996, "name": "SpawnProcess-4"}, "thread": {"id": 24764, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:12.322992+08:00", "timestamp": 1756177632.322992}}}
{"text": "2025-08-26T11:07:16.058529+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.748033", "seconds": 3.748033}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25728, "name": "SpawnProcess-4"}, "thread": {"id": 17108, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:16.058529+08:00", "timestamp": 1756177636.058529}}}
{"text": "2025-08-26T11:07:16.063516+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.751024", "seconds": 3.751024}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19996, "name": "SpawnProcess-4"}, "thread": {"id": 24764, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:16.063516+08:00", "timestamp": 1756177636.063516}}}
{"text": "2025-08-26T11:07:30.344451+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010824", "seconds": 0.010824}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10668, "name": "SpawnProcess-5"}, "thread": {"id": 17340, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:30.344451+08:00", "timestamp": 1756177650.344451}}}
{"text": "2025-08-26T11:07:30.350599+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010467", "seconds": 0.010467}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14956, "name": "SpawnProcess-5"}, "thread": {"id": 6536, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:30.350599+08:00", "timestamp": 1756177650.350599}}}
{"text": "2025-08-26T11:07:33.893591+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.553459", "seconds": 3.553459}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 14956, "name": "SpawnProcess-5"}, "thread": {"id": 6536, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:33.893591+08:00", "timestamp": 1756177653.893591}}}
{"text": "2025-08-26T11:07:33.925955+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.592328", "seconds": 3.592328}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10668, "name": "SpawnProcess-5"}, "thread": {"id": 17340, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:33.925955+08:00", "timestamp": 1756177653.925955}}}
{"text": "2025-08-26T11:07:45.675717+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009615", "seconds": 0.009615}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15236, "name": "SpawnProcess-6"}, "thread": {"id": 22828, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:45.675717+08:00", "timestamp": 1756177665.675717}}}
{"text": "2025-08-26T11:07:45.719412+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012304", "seconds": 0.012304}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22124, "name": "SpawnProcess-6"}, "thread": {"id": 21992, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:45.719412+08:00", "timestamp": 1756177665.719412}}}
{"text": "2025-08-26T11:07:49.631363+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.924255", "seconds": 3.924255}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22124, "name": "SpawnProcess-6"}, "thread": {"id": 21992, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:49.631363+08:00", "timestamp": 1756177669.631363}}}
{"text": "2025-08-26T11:07:49.651418+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.985316", "seconds": 3.985316}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 15236, "name": "SpawnProcess-6"}, "thread": {"id": 22828, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:49.651418+08:00", "timestamp": 1756177669.651418}}}
{"text": "2025-08-26T11:08:05.030693+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012398", "seconds": 0.012398}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8412, "name": "MainProcess"}, "thread": {"id": 25128, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:08:05.030693+08:00", "timestamp": 1756177685.030693}}}
{"text": "2025-08-26T11:08:08.457388+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.439093", "seconds": 3.439093}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 8412, "name": "MainProcess"}, "thread": {"id": 25128, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:08:08.457388+08:00", "timestamp": 1756177688.457388}}}
{"text": "2025-08-26T11:19:19.681335+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011913", "seconds": 0.011913}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:19.681335+08:00", "timestamp": 1756178359.681335}}}
{"text": "2025-08-26T11:19:23.143155+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.473733", "seconds": 3.473733}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.143155+08:00", "timestamp": 1756178363.143155}}}
{"text": "2025-08-26T11:19:23.144647+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.475225", "seconds": 3.475225}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.144647+08:00", "timestamp": 1756178363.144647}}}
{"text": "2025-08-26T11:19:23.145430+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.476008", "seconds": 3.476008}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.145430+08:00", "timestamp": 1756178363.14543}}}
{"text": "2025-08-26T11:21:23.930016+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013335", "seconds": 0.013335}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:23.930016+08:00", "timestamp": 1756178483.930016}}}
{"text": "2025-08-26T11:21:27.629575+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.712894", "seconds": 3.712894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.629575+08:00", "timestamp": 1756178487.629575}}}
{"text": "2025-08-26T11:21:27.629575+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.712894", "seconds": 3.712894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.629575+08:00", "timestamp": 1756178487.629575}}}
{"text": "2025-08-26T11:21:27.630893+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.714212", "seconds": 3.714212}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.630893+08:00", "timestamp": 1756178487.630893}}}
{"text": "2025-08-26T11:22:14.762396+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013016", "seconds": 0.013016}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:14.762396+08:00", "timestamp": 1756178534.762396}}}
{"text": "2025-08-26T11:22:19.001282+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.251902", "seconds": 4.251902}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.001282+08:00", "timestamp": 1756178539.001282}}}
{"text": "2025-08-26T11:22:19.004274+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.254894", "seconds": 4.254894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.004274+08:00", "timestamp": 1756178539.004274}}}
{"text": "2025-08-26T11:22:19.005272+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.255892", "seconds": 4.255892}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.005272+08:00", "timestamp": 1756178539.005272}}}
{"text": "2025-08-26T11:23:02.080861+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018238", "seconds": 0.018238}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 4740, "name": "SpawnProcess-1"}, "thread": {"id": 21892, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:23:02.080861+08:00", "timestamp": 1756178582.080861}}}
{"text": "2025-08-26T11:25:24.188003+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010772", "seconds": 0.010772}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10380, "name": "SpawnProcess-1"}, "thread": {"id": 9776, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:25:24.188003+08:00", "timestamp": 1756178724.188003}}}
{"text": "2025-08-26T11:26:26.942096+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014615", "seconds": 0.014615}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16700, "name": "SpawnProcess-2"}, "thread": {"id": 25480, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:26:26.942096+08:00", "timestamp": 1756178786.942096}}}
{"text": "2025-08-26T11:27:14.381641+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011386", "seconds": 0.011386}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22336, "name": "SpawnProcess-1"}, "thread": {"id": 9664, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:27:14.381641+08:00", "timestamp": 1756178834.381641}}}
{"text": "2025-08-26T11:30:16.778793+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013381", "seconds": 0.013381}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:16.778793+08:00", "timestamp": 1756179016.778793}}}
{"text": "2025-08-26T11:30:21.082362+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.316950", "seconds": 4.31695}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.082362+08:00", "timestamp": 1756179021.082362}}}
{"text": "2025-08-26T11:30:21.139256+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.373844", "seconds": 4.373844}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.139256+08:00", "timestamp": 1756179021.139256}}}
{"text": "2025-08-26T11:30:21.173636+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.408224", "seconds": 4.408224}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.173636+08:00", "timestamp": 1756179021.173636}}}
{"text": "2025-08-26T11:30:21.192759+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.427347", "seconds": 4.427347}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.192759+08:00", "timestamp": 1756179021.192759}}}
{"text": "2025-08-26T11:30:21.211419+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446007", "seconds": 4.446007}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.211419+08:00", "timestamp": 1756179021.211419}}}
{"text": "2025-08-26T11:30:21.211419+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.446007", "seconds": 4.446007}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.211419+08:00", "timestamp": 1756179021.211419}}}
{"text": "2025-08-26T11:31:12.090838+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015033", "seconds": 0.015033}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:12.090838+08:00", "timestamp": 1756179072.090838}}}
{"text": "2025-08-26T11:31:16.783245+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.707440", "seconds": 4.70744}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.783245+08:00", "timestamp": 1756179076.783245}}}
{"text": "2025-08-26T11:31:16.784250+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.708445", "seconds": 4.708445}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.784250+08:00", "timestamp": 1756179076.78425}}}
{"text": "2025-08-26T11:31:16.784250+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.708445", "seconds": 4.708445}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.784250+08:00", "timestamp": 1756179076.78425}}}
{"text": "2025-08-26T11:31:16.788922+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.713117", "seconds": 4.713117}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.788922+08:00", "timestamp": 1756179076.788922}}}
{"text": "2025-08-26T11:31:16.790951+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.715146", "seconds": 4.715146}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.790951+08:00", "timestamp": 1756179076.790951}}}
{"text": "2025-08-26T11:31:16.790951+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.715146", "seconds": 4.715146}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.790951+08:00", "timestamp": 1756179076.790951}}}
{"text": "2025-08-26T11:32:01.638897+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014086", "seconds": 0.014086}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:01.638897+08:00", "timestamp": 1756179121.638897}}}
{"text": "2025-08-26T11:32:05.758193+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.133382", "seconds": 4.133382}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.758193+08:00", "timestamp": 1756179125.758193}}}
{"text": "2025-08-26T11:32:05.759693+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.134882", "seconds": 4.134882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.759693+08:00", "timestamp": 1756179125.759693}}}
{"text": "2025-08-26T11:32:05.759693+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.134882", "seconds": 4.134882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.759693+08:00", "timestamp": 1756179125.759693}}}
{"text": "2025-08-26T11:32:05.760938+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.136127", "seconds": 4.136127}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.760938+08:00", "timestamp": 1756179125.760938}}}
{"text": "2025-08-26T11:32:05.761964+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.137153", "seconds": 4.137153}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.761964+08:00", "timestamp": 1756179125.761964}}}
{"text": "2025-08-26T11:32:05.761964+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.137153", "seconds": 4.137153}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.761964+08:00", "timestamp": 1756179125.761964}}}
{"text": "2025-08-26T11:33:03.780286+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009925", "seconds": 0.009925}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:03.780286+08:00", "timestamp": 1756179183.780286}}}
{"text": "2025-08-26T11:33:08.436395+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.666034", "seconds": 4.666034}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.436395+08:00", "timestamp": 1756179188.436395}}}
{"text": "2025-08-26T11:33:08.437393+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.667032", "seconds": 4.667032}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.437393+08:00", "timestamp": 1756179188.437393}}}
{"text": "2025-08-26T11:33:08.438493+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.668132", "seconds": 4.668132}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.438493+08:00", "timestamp": 1756179188.438493}}}
{"text": "2025-08-26T11:33:08.439967+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.669606", "seconds": 4.669606}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.439967+08:00", "timestamp": 1756179188.439967}}}
{"text": "2025-08-26T11:33:08.441231+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.670870", "seconds": 4.67087}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.441231+08:00", "timestamp": 1756179188.441231}}}
{"text": "2025-08-26T11:33:08.441231+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.670870", "seconds": 4.67087}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.441231+08:00", "timestamp": 1756179188.441231}}}
{"text": "2025-08-26T11:34:16.246830+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012497", "seconds": 0.012497}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:16.246830+08:00", "timestamp": 1756179256.24683}}}
{"text": "2025-08-26T11:34:20.284243+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.049910", "seconds": 4.04991}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.284243+08:00", "timestamp": 1756179260.284243}}}
{"text": "2025-08-26T11:34:20.285247+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.050914", "seconds": 4.050914}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.285247+08:00", "timestamp": 1756179260.285247}}}
{"text": "2025-08-26T11:34:20.286309+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.051976", "seconds": 4.051976}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.286309+08:00", "timestamp": 1756179260.286309}}}
{"text": "2025-08-26T11:34:20.286813+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.052480", "seconds": 4.05248}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.286813+08:00", "timestamp": 1756179260.286813}}}
{"text": "2025-08-26T11:34:20.287859+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.053526", "seconds": 4.053526}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.287859+08:00", "timestamp": 1756179260.287859}}}
{"text": "2025-08-26T11:34:20.287859+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.053526", "seconds": 4.053526}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.287859+08:00", "timestamp": 1756179260.287859}}}
{"text": "2025-08-26T11:35:29.025251+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014050", "seconds": 0.01405}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:29.025251+08:00", "timestamp": 1756179329.025251}}}
{"text": "2025-08-26T11:35:34.160021+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.148820", "seconds": 5.14882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.160021+08:00", "timestamp": 1756179334.160021}}}
{"text": "2025-08-26T11:35:34.229394+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.218193", "seconds": 5.218193}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.229394+08:00", "timestamp": 1756179334.229394}}}
{"text": "2025-08-26T11:35:34.245655+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.234454", "seconds": 5.234454}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.245655+08:00", "timestamp": 1756179334.245655}}}
{"text": "2025-08-26T11:35:34.250645+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.239444", "seconds": 5.239444}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.250645+08:00", "timestamp": 1756179334.250645}}}
{"text": "2025-08-26T11:35:34.253033+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.241832", "seconds": 5.241832}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.253033+08:00", "timestamp": 1756179334.253033}}}
{"text": "2025-08-26T11:35:34.253033+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:05.241832", "seconds": 5.241832}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.253033+08:00", "timestamp": 1756179334.253033}}}
{"text": "2025-08-26T11:42:12.049570+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014752", "seconds": 0.014752}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:12.049570+08:00", "timestamp": 1756179732.04957}}}
{"text": "2025-08-26T11:42:16.475933+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.441115", "seconds": 4.441115}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.475933+08:00", "timestamp": 1756179736.475933}}}
{"text": "2025-08-26T11:42:16.478931+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.444113", "seconds": 4.444113}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.478931+08:00", "timestamp": 1756179736.478931}}}
{"text": "2025-08-26T11:42:16.479944+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.445126", "seconds": 4.445126}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.479944+08:00", "timestamp": 1756179736.479944}}}
{"text": "2025-08-26T11:42:16.480946+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446128", "seconds": 4.446128}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.480946+08:00", "timestamp": 1756179736.480946}}}
{"text": "2025-08-26T11:42:16.480946+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446128", "seconds": 4.446128}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.480946+08:00", "timestamp": 1756179736.480946}}}
{"text": "2025-08-26T11:42:16.482401+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.447583", "seconds": 4.447583}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.482401+08:00", "timestamp": 1756179736.482401}}}
{"text": "2025-08-26T11:43:23.318776+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010464", "seconds": 0.010464}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:23.318776+08:00", "timestamp": 1756179803.318776}}}
{"text": "2025-08-26T11:43:27.364579+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.056267", "seconds": 4.056267}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.364579+08:00", "timestamp": 1756179807.364579}}}
{"text": "2025-08-26T11:43:27.364579+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.056267", "seconds": 4.056267}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.364579+08:00", "timestamp": 1756179807.364579}}}
{"text": "2025-08-26T11:43:27.365944+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.057632", "seconds": 4.057632}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.365944+08:00", "timestamp": 1756179807.365944}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:45:30.539483+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015066", "seconds": 0.015066}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:30.539483+08:00", "timestamp": 1756179930.539483}}}
{"text": "2025-08-26T11:45:34.616207+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.091790", "seconds": 4.09179}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.616207+08:00", "timestamp": 1756179934.616207}}}
{"text": "2025-08-26T11:45:34.617210+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.092793", "seconds": 4.092793}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.617210+08:00", "timestamp": 1756179934.61721}}}
{"text": "2025-08-26T11:45:34.617920+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.093503", "seconds": 4.093503}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.617920+08:00", "timestamp": 1756179934.61792}}}
{"text": "2025-08-26T11:45:34.618663+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.094246", "seconds": 4.094246}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.618663+08:00", "timestamp": 1756179934.618663}}}
{"text": "2025-08-26T11:45:34.619354+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.094937", "seconds": 4.094937}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.619354+08:00", "timestamp": 1756179934.619354}}}
{"text": "2025-08-26T11:45:34.620040+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.095623", "seconds": 4.095623}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.620040+08:00", "timestamp": 1756179934.62004}}}
{"text": "2025-08-26T11:46:36.534287+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011085", "seconds": 0.011085}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:36.534287+08:00", "timestamp": 1756179996.534287}}}
{"text": "2025-08-26T11:46:40.756304+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.233102", "seconds": 4.233102}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.756304+08:00", "timestamp": 1756180000.756304}}}
{"text": "2025-08-26T11:46:40.757660+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.234458", "seconds": 4.234458}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.757660+08:00", "timestamp": 1756180000.75766}}}
{"text": "2025-08-26T11:46:40.757660+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.234458", "seconds": 4.234458}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.757660+08:00", "timestamp": 1756180000.75766}}}
{"text": "2025-08-26T11:46:40.758703+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.235501", "seconds": 4.235501}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.758703+08:00", "timestamp": 1756180000.758703}}}
{"text": "2025-08-26T11:46:40.759700+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.236498", "seconds": 4.236498}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.759700+08:00", "timestamp": 1756180000.7597}}}
{"text": "2025-08-26T11:46:40.759700+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.236498", "seconds": 4.236498}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.759700+08:00", "timestamp": 1756180000.7597}}}
{"text": "2025-08-26T11:47:05.685580+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012582", "seconds": 0.012582}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:05.685580+08:00", "timestamp": 1756180025.68558}}}
{"text": "2025-08-26T11:47:09.089340+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.416342", "seconds": 3.416342}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.089340+08:00", "timestamp": 1756180029.08934}}}
{"text": "2025-08-26T11:47:09.253820+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.580822", "seconds": 3.580822}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.253820+08:00", "timestamp": 1756180029.25382}}}
{"text": "2025-08-26T11:47:09.372320+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.699322", "seconds": 3.699322}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.372320+08:00", "timestamp": 1756180029.37232}}}
{"text": "2025-08-26T11:47:09.373327+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.700329", "seconds": 3.700329}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.373327+08:00", "timestamp": 1756180029.373327}}}
{"text": "2025-08-26T11:47:09.373327+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.700329", "seconds": 3.700329}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.373327+08:00", "timestamp": 1756180029.373327}}}
{"text": "2025-08-26T11:47:09.374323+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.701325", "seconds": 3.701325}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.374323+08:00", "timestamp": 1756180029.374323}}}
{"text": "2025-08-26T11:47:09.947159+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.274161", "seconds": 4.274161}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.947159+08:00", "timestamp": 1756180029.947159}}}
{"text": "2025-08-26T11:47:09.948156+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.275158", "seconds": 4.275158}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.948156+08:00", "timestamp": 1756180029.948156}}}
{"text": "2025-08-26T11:47:09.949216+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.276218", "seconds": 4.276218}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.949216+08:00", "timestamp": 1756180029.949216}}}
{"text": "2025-08-26T11:47:09.958751+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.285753", "seconds": 4.285753}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.958751+08:00", "timestamp": 1756180029.958751}}}
{"text": "2025-08-26T11:47:48.122189+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014048", "seconds": 0.014048}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:48.122189+08:00", "timestamp": 1756180068.122189}}}
{"text": "2025-08-26T11:47:51.400860+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.292719", "seconds": 3.292719}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.400860+08:00", "timestamp": 1756180071.40086}}}
{"text": "2025-08-26T11:47:51.553455+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.445314", "seconds": 3.445314}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.553455+08:00", "timestamp": 1756180071.553455}}}
{"text": "2025-08-26T11:47:51.581890+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.473749", "seconds": 3.473749}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.581890+08:00", "timestamp": 1756180071.58189}}}
{"text": "2025-08-26T11:47:51.582895+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.474754", "seconds": 3.474754}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.582895+08:00", "timestamp": 1756180071.582895}}}
{"text": "2025-08-26T11:47:51.582895+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.474754", "seconds": 3.474754}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.582895+08:00", "timestamp": 1756180071.582895}}}
{"text": "2025-08-26T11:47:51.584145+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.476004", "seconds": 3.476004}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.584145+08:00", "timestamp": 1756180071.584145}}}
{"text": "2025-08-26T11:47:52.096553+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:03.988412", "seconds": 3.988412}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.096553+08:00", "timestamp": 1756180072.096553}}}
{"text": "2025-08-26T11:47:52.097703+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.989562", "seconds": 3.989562}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.097703+08:00", "timestamp": 1756180072.097703}}}
{"text": "2025-08-26T11:47:52.097703+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:03.989562", "seconds": 3.989562}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.097703+08:00", "timestamp": 1756180072.097703}}}
{"text": "2025-08-26T11:47:52.100075+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:03.991934", "seconds": 3.991934}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.100075+08:00", "timestamp": 1756180072.100075}}}
