{"text": "2025-08-25T15:41:11.673498+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016574", "seconds": 0.016574}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 6316, "name": "MainProcess"}, "thread": {"id": 34868, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:41:11.673498+08:00", "timestamp": 1756107671.673498}}}
{"text": "2025-08-25T15:42:12.560287+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.027879", "seconds": 0.027879}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33640, "name": "MainProcess"}, "thread": {"id": 23220, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:42:12.560287+08:00", "timestamp": 1756107732.560287}}}
{"text": "2025-08-25T15:43:30.656580+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016732", "seconds": 0.016732}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22124, "name": "MainProcess"}, "thread": {"id": 35580, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:43:30.656580+08:00", "timestamp": 1756107810.65658}}}
{"text": "2025-08-25T15:54:59.309598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021286", "seconds": 0.021286}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11704, "name": "MainProcess"}, "thread": {"id": 11248, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:54:59.309598+08:00", "timestamp": 1756108499.309598}}}
{"text": "2025-08-25T15:55:34.227297+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012456", "seconds": 0.012456}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34896, "name": "MainProcess"}, "thread": {"id": 24432, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:55:34.227297+08:00", "timestamp": 1756108534.227297}}}
{"text": "2025-08-25T15:57:05.503448+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013665", "seconds": 0.013665}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33444, "name": "MainProcess"}, "thread": {"id": 10596, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:57:05.503448+08:00", "timestamp": 1756108625.503448}}}
{"text": "2025-08-25T15:58:48.300238+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013064", "seconds": 0.013064}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 1192, "name": "MainProcess"}, "thread": {"id": 39076, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:58:48.300238+08:00", "timestamp": 1756108728.300238}}}
{"text": "2025-08-25T16:00:08.916536+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012267", "seconds": 0.012267}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12428, "name": "MainProcess"}, "thread": {"id": 11828, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:00:08.916536+08:00", "timestamp": 1756108808.916536}}}
{"text": "2025-08-25T16:01:23.471462+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025699", "seconds": 0.025699}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29588, "name": "MainProcess"}, "thread": {"id": 2516, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:23.471462+08:00", "timestamp": 1756108883.471462}}}
{"text": "2025-08-25T16:01:40.164598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016917", "seconds": 0.016917}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8620, "name": "MainProcess"}, "thread": {"id": 34500, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:40.164598+08:00", "timestamp": 1756108900.164598}}}
{"text": "2025-08-25T16:03:17.001901+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018285", "seconds": 0.018285}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26752, "name": "MainProcess"}, "thread": {"id": 15424, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:03:17.001901+08:00", "timestamp": 1756108997.001901}}}
{"text": "2025-08-25T16:05:26.759085+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014915", "seconds": 0.014915}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15296, "name": "MainProcess"}, "thread": {"id": 35092, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:05:26.759085+08:00", "timestamp": 1756109126.759085}}}
{"text": "2025-08-25T16:07:20.072929+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.040320", "seconds": 0.04032}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14668, "name": "MainProcess"}, "thread": {"id": 34080, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:20.072929+08:00", "timestamp": 1756109240.072929}}}
{"text": "2025-08-25T16:07:42.823960+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011571", "seconds": 0.011571}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 17652, "name": "MainProcess"}, "thread": {"id": 15056, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:42.823960+08:00", "timestamp": 1756109262.82396}}}
{"text": "2025-08-25T16:08:36.933605+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.023327", "seconds": 0.023327}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 20416, "name": "MainProcess"}, "thread": {"id": 7440, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:08:36.933605+08:00", "timestamp": 1756109316.933605}}}
{"text": "2025-08-25T16:09:56.064018+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012766", "seconds": 0.012766}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23572, "name": "MainProcess"}, "thread": {"id": 23904, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:09:56.064018+08:00", "timestamp": 1756109396.064018}}}
{"text": "2025-08-25T16:12:06.815575+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013296", "seconds": 0.013296}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24400, "name": "MainProcess"}, "thread": {"id": 11084, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:12:06.815575+08:00", "timestamp": 1756109526.815575}}}
{"text": "2025-08-25T16:14:24.610105+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021843", "seconds": 0.021843}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 21084, "name": "MainProcess"}, "thread": {"id": 26248, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:14:24.610105+08:00", "timestamp": 1756109664.610105}}}
{"text": "2025-08-25T16:15:35.349640+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.028526", "seconds": 0.028526}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24604, "name": "MainProcess"}, "thread": {"id": 20428, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:35.349640+08:00", "timestamp": 1756109735.34964}}}
{"text": "2025-08-25T16:15:55.928450+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014180", "seconds": 0.01418}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11800, "name": "MainProcess"}, "thread": {"id": 8952, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:55.928450+08:00", "timestamp": 1756109755.92845}}}
{"text": "2025-08-25T16:18:10.990551+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015095", "seconds": 0.015095}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25744, "name": "MainProcess"}, "thread": {"id": 15804, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:18:10.990551+08:00", "timestamp": 1756109890.990551}}}
{"text": "2025-08-25T16:19:20.000520+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025328", "seconds": 0.025328}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26100, "name": "MainProcess"}, "thread": {"id": 3044, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:20.000520+08:00", "timestamp": 1756109960.00052}}}
{"text": "2025-08-25T16:19:54.140265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018340", "seconds": 0.01834}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11244, "name": "MainProcess"}, "thread": {"id": 20612, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:54.140265+08:00", "timestamp": 1756109994.140265}}}
{"text": "2025-08-25T16:20:58.841288+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016417", "seconds": 0.016417}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29052, "name": "MainProcess"}, "thread": {"id": 26900, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:20:58.841288+08:00", "timestamp": 1756110058.841288}}}
{"text": "2025-08-25T17:37:34.605265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024741", "seconds": 0.024741}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23044, "name": "MainProcess"}, "thread": {"id": 7880, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:37:34.605265+08:00", "timestamp": 1756114654.605265}}}
{"text": "2025-08-25T17:38:13.697391+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.020144", "seconds": 0.020144}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2312, "name": "MainProcess"}, "thread": {"id": 2348, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:38:13.697391+08:00", "timestamp": 1756114693.697391}}}
{"text": "2025-08-25T17:39:18.429524+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012511", "seconds": 0.012511}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25248, "name": "MainProcess"}, "thread": {"id": 35204, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:18.429524+08:00", "timestamp": 1756114758.429524}}}
{"text": "2025-08-25T17:39:40.523061+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012918", "seconds": 0.012918}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14412, "name": "MainProcess"}, "thread": {"id": 38212, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:40.523061+08:00", "timestamp": 1756114780.523061}}}
{"text": "2025-08-25T17:41:34.377101+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013387", "seconds": 0.013387}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19540, "name": "MainProcess"}, "thread": {"id": 37248, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:41:34.377101+08:00", "timestamp": 1756114894.377101}}}
{"text": "2025-08-25T17:43:50.331959+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.019421", "seconds": 0.019421}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 30872, "name": "MainProcess"}, "thread": {"id": 24588, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:43:50.331959+08:00", "timestamp": 1756115030.331959}}}
{"text": "2025-08-25T17:44:58.314236+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.186816", "seconds": 0.186816}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25272, "name": "MainProcess"}, "thread": {"id": 11768, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:44:58.314236+08:00", "timestamp": 1756115098.314236}}}
{"text": "2025-08-25T17:45:21.727969+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.022318", "seconds": 0.022318}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7824, "name": "MainProcess"}, "thread": {"id": 22860, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:21.727969+08:00", "timestamp": 1756115121.727969}}}
{"text": "2025-08-25T17:45:58.603882+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012697", "seconds": 0.012697}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 32092, "name": "MainProcess"}, "thread": {"id": 13604, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:58.603882+08:00", "timestamp": 1756115158.603882}}}
{"text": "2025-08-25T17:46:45.420354+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024966", "seconds": 0.024966}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16672, "name": "MainProcess"}, "thread": {"id": 14608, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:46:45.420354+08:00", "timestamp": 1756115205.420354}}}
{"text": "2025-08-25T17:47:32.205034+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018365", "seconds": 0.018365}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15256, "name": "MainProcess"}, "thread": {"id": 30528, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:47:32.205034+08:00", "timestamp": 1756115252.205034}}}
{"text": "2025-08-25T17:48:17.421548+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016666", "seconds": 0.016666}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.421548+08:00", "timestamp": 1756115297.421548}}}
{"text": "2025-08-25T17:48:17.426160+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021278", "seconds": 0.021278}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.426160+08:00", "timestamp": 1756115297.42616}}}
{"text": "2025-08-26T10:23:42.578015+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.030022", "seconds": 0.030022}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34056, "name": "SpawnProcess-1"}, "thread": {"id": 11136, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:23:42.578015+08:00", "timestamp": 1756175022.578015}}}
{"text": "2025-08-26T10:38:25.467423+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012902", "seconds": 0.012902}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8400, "name": "SpawnProcess-1"}, "thread": {"id": 37884, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:38:25.467423+08:00", "timestamp": 1756175905.467423}}}
{"text": "2025-08-26T10:39:33.573534+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016416", "seconds": 0.016416}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23316, "name": "SpawnProcess-1"}, "thread": {"id": 6036, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:39:33.573534+08:00", "timestamp": 1756175973.573534}}}
{"text": "2025-08-26T10:57:33.882595+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013681", "seconds": 0.013681}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 9460, "name": "SpawnProcess-1"}, "thread": {"id": 16588, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:57:33.882595+08:00", "timestamp": 1756177053.882595}}}
{"text": "2025-08-26T11:01:51.490621+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015196", "seconds": 0.015196}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11956, "name": "SpawnProcess-1"}, "thread": {"id": 8792, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:01:51.490621+08:00", "timestamp": 1756177311.490621}}}
