{"text": "2025-08-25T15:41:11.673498+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016574", "seconds": 0.016574}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 6316, "name": "MainProcess"}, "thread": {"id": 34868, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:41:11.673498+08:00", "timestamp": 1756107671.673498}}}
{"text": "2025-08-25T15:42:12.560287+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.027879", "seconds": 0.027879}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33640, "name": "MainProcess"}, "thread": {"id": 23220, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:42:12.560287+08:00", "timestamp": 1756107732.560287}}}
{"text": "2025-08-25T15:43:30.656580+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016732", "seconds": 0.016732}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22124, "name": "MainProcess"}, "thread": {"id": 35580, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:43:30.656580+08:00", "timestamp": 1756107810.65658}}}
{"text": "2025-08-25T15:54:59.309598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021286", "seconds": 0.021286}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11704, "name": "MainProcess"}, "thread": {"id": 11248, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:54:59.309598+08:00", "timestamp": 1756108499.309598}}}
{"text": "2025-08-25T15:55:34.227297+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012456", "seconds": 0.012456}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34896, "name": "MainProcess"}, "thread": {"id": 24432, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:55:34.227297+08:00", "timestamp": 1756108534.227297}}}
{"text": "2025-08-25T15:57:05.503448+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013665", "seconds": 0.013665}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 33444, "name": "MainProcess"}, "thread": {"id": 10596, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:57:05.503448+08:00", "timestamp": 1756108625.503448}}}
{"text": "2025-08-25T15:58:48.300238+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013064", "seconds": 0.013064}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 1192, "name": "MainProcess"}, "thread": {"id": 39076, "name": "MainThread"}, "time": {"repr": "2025-08-25 15:58:48.300238+08:00", "timestamp": 1756108728.300238}}}
{"text": "2025-08-25T16:00:08.916536+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012267", "seconds": 0.012267}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12428, "name": "MainProcess"}, "thread": {"id": 11828, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:00:08.916536+08:00", "timestamp": 1756108808.916536}}}
{"text": "2025-08-25T16:01:23.471462+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025699", "seconds": 0.025699}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29588, "name": "MainProcess"}, "thread": {"id": 2516, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:23.471462+08:00", "timestamp": 1756108883.471462}}}
{"text": "2025-08-25T16:01:40.164598+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016917", "seconds": 0.016917}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8620, "name": "MainProcess"}, "thread": {"id": 34500, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:01:40.164598+08:00", "timestamp": 1756108900.164598}}}
{"text": "2025-08-25T16:03:17.001901+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018285", "seconds": 0.018285}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26752, "name": "MainProcess"}, "thread": {"id": 15424, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:03:17.001901+08:00", "timestamp": 1756108997.001901}}}
{"text": "2025-08-25T16:05:26.759085+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014915", "seconds": 0.014915}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15296, "name": "MainProcess"}, "thread": {"id": 35092, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:05:26.759085+08:00", "timestamp": 1756109126.759085}}}
{"text": "2025-08-25T16:07:20.072929+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.040320", "seconds": 0.04032}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14668, "name": "MainProcess"}, "thread": {"id": 34080, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:20.072929+08:00", "timestamp": 1756109240.072929}}}
{"text": "2025-08-25T16:07:42.823960+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011571", "seconds": 0.011571}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 17652, "name": "MainProcess"}, "thread": {"id": 15056, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:07:42.823960+08:00", "timestamp": 1756109262.82396}}}
{"text": "2025-08-25T16:08:36.933605+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.023327", "seconds": 0.023327}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 20416, "name": "MainProcess"}, "thread": {"id": 7440, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:08:36.933605+08:00", "timestamp": 1756109316.933605}}}
{"text": "2025-08-25T16:09:56.064018+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012766", "seconds": 0.012766}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23572, "name": "MainProcess"}, "thread": {"id": 23904, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:09:56.064018+08:00", "timestamp": 1756109396.064018}}}
{"text": "2025-08-25T16:12:06.815575+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013296", "seconds": 0.013296}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24400, "name": "MainProcess"}, "thread": {"id": 11084, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:12:06.815575+08:00", "timestamp": 1756109526.815575}}}
{"text": "2025-08-25T16:14:24.610105+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021843", "seconds": 0.021843}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 21084, "name": "MainProcess"}, "thread": {"id": 26248, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:14:24.610105+08:00", "timestamp": 1756109664.610105}}}
{"text": "2025-08-25T16:15:35.349640+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.028526", "seconds": 0.028526}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 24604, "name": "MainProcess"}, "thread": {"id": 20428, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:35.349640+08:00", "timestamp": 1756109735.34964}}}
{"text": "2025-08-25T16:15:55.928450+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014180", "seconds": 0.01418}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11800, "name": "MainProcess"}, "thread": {"id": 8952, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:15:55.928450+08:00", "timestamp": 1756109755.92845}}}
{"text": "2025-08-25T16:18:10.990551+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015095", "seconds": 0.015095}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25744, "name": "MainProcess"}, "thread": {"id": 15804, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:18:10.990551+08:00", "timestamp": 1756109890.990551}}}
{"text": "2025-08-25T16:19:20.000520+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.025328", "seconds": 0.025328}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26100, "name": "MainProcess"}, "thread": {"id": 3044, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:20.000520+08:00", "timestamp": 1756109960.00052}}}
{"text": "2025-08-25T16:19:54.140265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018340", "seconds": 0.01834}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11244, "name": "MainProcess"}, "thread": {"id": 20612, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:19:54.140265+08:00", "timestamp": 1756109994.140265}}}
{"text": "2025-08-25T16:20:58.841288+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016417", "seconds": 0.016417}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 29052, "name": "MainProcess"}, "thread": {"id": 26900, "name": "MainThread"}, "time": {"repr": "2025-08-25 16:20:58.841288+08:00", "timestamp": 1756110058.841288}}}
{"text": "2025-08-25T17:37:34.605265+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024741", "seconds": 0.024741}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23044, "name": "MainProcess"}, "thread": {"id": 7880, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:37:34.605265+08:00", "timestamp": 1756114654.605265}}}
{"text": "2025-08-25T17:38:13.697391+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.020144", "seconds": 0.020144}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2312, "name": "MainProcess"}, "thread": {"id": 2348, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:38:13.697391+08:00", "timestamp": 1756114693.697391}}}
{"text": "2025-08-25T17:39:18.429524+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012511", "seconds": 0.012511}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25248, "name": "MainProcess"}, "thread": {"id": 35204, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:18.429524+08:00", "timestamp": 1756114758.429524}}}
{"text": "2025-08-25T17:39:40.523061+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012918", "seconds": 0.012918}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14412, "name": "MainProcess"}, "thread": {"id": 38212, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:39:40.523061+08:00", "timestamp": 1756114780.523061}}}
{"text": "2025-08-25T17:41:34.377101+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013387", "seconds": 0.013387}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19540, "name": "MainProcess"}, "thread": {"id": 37248, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:41:34.377101+08:00", "timestamp": 1756114894.377101}}}
{"text": "2025-08-25T17:43:50.331959+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.019421", "seconds": 0.019421}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 30872, "name": "MainProcess"}, "thread": {"id": 24588, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:43:50.331959+08:00", "timestamp": 1756115030.331959}}}
{"text": "2025-08-25T17:44:58.314236+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.186816", "seconds": 0.186816}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25272, "name": "MainProcess"}, "thread": {"id": 11768, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:44:58.314236+08:00", "timestamp": 1756115098.314236}}}
{"text": "2025-08-25T17:45:21.727969+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.022318", "seconds": 0.022318}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7824, "name": "MainProcess"}, "thread": {"id": 22860, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:21.727969+08:00", "timestamp": 1756115121.727969}}}
{"text": "2025-08-25T17:45:58.603882+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012697", "seconds": 0.012697}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 32092, "name": "MainProcess"}, "thread": {"id": 13604, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:45:58.603882+08:00", "timestamp": 1756115158.603882}}}
{"text": "2025-08-25T17:46:45.420354+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024966", "seconds": 0.024966}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16672, "name": "MainProcess"}, "thread": {"id": 14608, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:46:45.420354+08:00", "timestamp": 1756115205.420354}}}
{"text": "2025-08-25T17:47:32.205034+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018365", "seconds": 0.018365}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15256, "name": "MainProcess"}, "thread": {"id": 30528, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:47:32.205034+08:00", "timestamp": 1756115252.205034}}}
{"text": "2025-08-25T17:48:17.421548+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016666", "seconds": 0.016666}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.421548+08:00", "timestamp": 1756115297.421548}}}
{"text": "2025-08-25T17:48:17.426160+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.021278", "seconds": 0.021278}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7056, "name": "MainProcess"}, "thread": {"id": 3920, "name": "MainThread"}, "time": {"repr": "2025-08-25 17:48:17.426160+08:00", "timestamp": 1756115297.42616}}}
{"text": "2025-08-26T10:23:42.578015+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.030022", "seconds": 0.030022}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 34056, "name": "SpawnProcess-1"}, "thread": {"id": 11136, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:23:42.578015+08:00", "timestamp": 1756175022.578015}}}
{"text": "2025-08-26T10:38:25.467423+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012902", "seconds": 0.012902}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8400, "name": "SpawnProcess-1"}, "thread": {"id": 37884, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:38:25.467423+08:00", "timestamp": 1756175905.467423}}}
{"text": "2025-08-26T10:39:33.573534+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.016416", "seconds": 0.016416}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23316, "name": "SpawnProcess-1"}, "thread": {"id": 6036, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:39:33.573534+08:00", "timestamp": 1756175973.573534}}}
{"text": "2025-08-26T10:57:33.882595+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013681", "seconds": 0.013681}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 9460, "name": "SpawnProcess-1"}, "thread": {"id": 16588, "name": "MainThread"}, "time": {"repr": "2025-08-26 10:57:33.882595+08:00", "timestamp": 1756177053.882595}}}
{"text": "2025-08-26T11:01:51.490621+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015196", "seconds": 0.015196}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11956, "name": "SpawnProcess-1"}, "thread": {"id": 8792, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:01:51.490621+08:00", "timestamp": 1756177311.490621}}}
{"text": "2025-08-26T11:03:47.977917+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014096", "seconds": 0.014096}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10872, "name": "SpawnProcess-1"}, "thread": {"id": 19792, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:03:47.977917+08:00", "timestamp": 1756177427.977917}}}
{"text": "2025-08-26T11:05:21.095898+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.193370", "seconds": 0.19337}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 5468, "name": "MainProcess"}, "thread": {"id": 13396, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:21.095898+08:00", "timestamp": 1756177521.095898}}}
{"text": "2025-08-26T11:05:53.502188+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009972", "seconds": 0.009972}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10380, "name": "SpawnProcess-2"}, "thread": {"id": 1824, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:53.502188+08:00", "timestamp": 1756177553.502188}}}
{"text": "2025-08-26T11:05:53.550286+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015269", "seconds": 0.015269}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22320, "name": "SpawnProcess-2"}, "thread": {"id": 24840, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:53.550286+08:00", "timestamp": 1756177553.550286}}}
{"text": "2025-08-26T11:05:57.538399+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.046183", "seconds": 4.046183}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10380, "name": "SpawnProcess-2"}, "thread": {"id": 1824, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:57.538399+08:00", "timestamp": 1756177557.538399}}}
{"text": "2025-08-26T11:05:57.539397+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.004380", "seconds": 4.00438}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22320, "name": "SpawnProcess-2"}, "thread": {"id": 24840, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:05:57.539397+08:00", "timestamp": 1756177557.539397}}}
{"text": "2025-08-26T11:06:09.706153+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012510", "seconds": 0.01251}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15424, "name": "MainProcess"}, "thread": {"id": 11436, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:09.706153+08:00", "timestamp": 1756177569.706153}}}
{"text": "2025-08-26T11:06:13.312041+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.618398", "seconds": 3.618398}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 15424, "name": "MainProcess"}, "thread": {"id": 11436, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:13.312041+08:00", "timestamp": 1756177573.312041}}}
{"text": "2025-08-26T11:06:45.300814+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013985", "seconds": 0.013985}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16004, "name": "SpawnProcess-3"}, "thread": {"id": 5904, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:45.300814+08:00", "timestamp": 1756177605.300814}}}
{"text": "2025-08-26T11:06:45.326821+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013948", "seconds": 0.013948}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12580, "name": "SpawnProcess-3"}, "thread": {"id": 10568, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:45.326821+08:00", "timestamp": 1756177605.326821}}}
{"text": "2025-08-26T11:06:49.581393+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.294564", "seconds": 4.294564}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 16004, "name": "SpawnProcess-3"}, "thread": {"id": 5904, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:49.581393+08:00", "timestamp": 1756177609.581393}}}
{"text": "2025-08-26T11:06:49.596872+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.283999", "seconds": 4.283999}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 12580, "name": "SpawnProcess-3"}, "thread": {"id": 10568, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:06:49.596872+08:00", "timestamp": 1756177609.596872}}}
{"text": "2025-08-26T11:07:12.321995+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011499", "seconds": 0.011499}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25728, "name": "SpawnProcess-4"}, "thread": {"id": 17108, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:12.321995+08:00", "timestamp": 1756177632.321995}}}
{"text": "2025-08-26T11:07:12.322992+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010500", "seconds": 0.0105}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19996, "name": "SpawnProcess-4"}, "thread": {"id": 24764, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:12.322992+08:00", "timestamp": 1756177632.322992}}}
{"text": "2025-08-26T11:07:16.058529+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.748033", "seconds": 3.748033}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25728, "name": "SpawnProcess-4"}, "thread": {"id": 17108, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:16.058529+08:00", "timestamp": 1756177636.058529}}}
{"text": "2025-08-26T11:07:16.063516+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.751024", "seconds": 3.751024}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19996, "name": "SpawnProcess-4"}, "thread": {"id": 24764, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:16.063516+08:00", "timestamp": 1756177636.063516}}}
{"text": "2025-08-26T11:07:30.344451+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010824", "seconds": 0.010824}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10668, "name": "SpawnProcess-5"}, "thread": {"id": 17340, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:30.344451+08:00", "timestamp": 1756177650.344451}}}
{"text": "2025-08-26T11:07:30.350599+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010467", "seconds": 0.010467}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 14956, "name": "SpawnProcess-5"}, "thread": {"id": 6536, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:30.350599+08:00", "timestamp": 1756177650.350599}}}
{"text": "2025-08-26T11:07:33.893591+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.553459", "seconds": 3.553459}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 14956, "name": "SpawnProcess-5"}, "thread": {"id": 6536, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:33.893591+08:00", "timestamp": 1756177653.893591}}}
{"text": "2025-08-26T11:07:33.925955+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.592328", "seconds": 3.592328}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10668, "name": "SpawnProcess-5"}, "thread": {"id": 17340, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:33.925955+08:00", "timestamp": 1756177653.925955}}}
{"text": "2025-08-26T11:07:45.675717+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009615", "seconds": 0.009615}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 15236, "name": "SpawnProcess-6"}, "thread": {"id": 22828, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:45.675717+08:00", "timestamp": 1756177665.675717}}}
{"text": "2025-08-26T11:07:45.719412+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012304", "seconds": 0.012304}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22124, "name": "SpawnProcess-6"}, "thread": {"id": 21992, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:45.719412+08:00", "timestamp": 1756177665.719412}}}
{"text": "2025-08-26T11:07:49.631363+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.924255", "seconds": 3.924255}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22124, "name": "SpawnProcess-6"}, "thread": {"id": 21992, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:49.631363+08:00", "timestamp": 1756177669.631363}}}
{"text": "2025-08-26T11:07:49.651418+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.985316", "seconds": 3.985316}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 15236, "name": "SpawnProcess-6"}, "thread": {"id": 22828, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:07:49.651418+08:00", "timestamp": 1756177669.651418}}}
{"text": "2025-08-26T11:08:05.030693+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012398", "seconds": 0.012398}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 8412, "name": "MainProcess"}, "thread": {"id": 25128, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:08:05.030693+08:00", "timestamp": 1756177685.030693}}}
{"text": "2025-08-26T11:08:08.457388+0800 | WARNING | app.services.ml_model_service | <module> | 39 | TensorFlow未安装，深度学习功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.439093", "seconds": 3.439093}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 39, "message": "TensorFlow未安装，深度学习功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 8412, "name": "MainProcess"}, "thread": {"id": 25128, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:08:08.457388+08:00", "timestamp": 1756177688.457388}}}
{"text": "2025-08-26T11:19:19.681335+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011913", "seconds": 0.011913}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:19.681335+08:00", "timestamp": 1756178359.681335}}}
{"text": "2025-08-26T11:19:23.143155+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.473733", "seconds": 3.473733}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.143155+08:00", "timestamp": 1756178363.143155}}}
{"text": "2025-08-26T11:19:23.144647+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.475225", "seconds": 3.475225}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.144647+08:00", "timestamp": 1756178363.144647}}}
{"text": "2025-08-26T11:19:23.145430+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.476008", "seconds": 3.476008}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19492, "name": "MainProcess"}, "thread": {"id": 18924, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:19:23.145430+08:00", "timestamp": 1756178363.14543}}}
{"text": "2025-08-26T11:21:23.930016+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013335", "seconds": 0.013335}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:23.930016+08:00", "timestamp": 1756178483.930016}}}
{"text": "2025-08-26T11:21:27.629575+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.712894", "seconds": 3.712894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.629575+08:00", "timestamp": 1756178487.629575}}}
{"text": "2025-08-26T11:21:27.629575+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.712894", "seconds": 3.712894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.629575+08:00", "timestamp": 1756178487.629575}}}
{"text": "2025-08-26T11:21:27.630893+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.714212", "seconds": 3.714212}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 10148, "name": "MainProcess"}, "thread": {"id": 6352, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:21:27.630893+08:00", "timestamp": 1756178487.630893}}}
{"text": "2025-08-26T11:22:14.762396+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013016", "seconds": 0.013016}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:14.762396+08:00", "timestamp": 1756178534.762396}}}
{"text": "2025-08-26T11:22:19.001282+0800 | WARNING | app.services.ml_model_service | <module> | 42 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.251902", "seconds": 4.251902}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 42, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.001282+08:00", "timestamp": 1756178539.001282}}}
{"text": "2025-08-26T11:22:19.004274+0800 | WARNING | app.services.ml_model_service | <module> | 51 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.254894", "seconds": 4.254894}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.004274+08:00", "timestamp": 1756178539.004274}}}
{"text": "2025-08-26T11:22:19.005272+0800 | WARNING | app.services.ml_model_service | <module> | 54 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.255892", "seconds": 4.255892}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 54, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23760, "name": "MainProcess"}, "thread": {"id": 3104, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:22:19.005272+08:00", "timestamp": 1756178539.005272}}}
{"text": "2025-08-26T11:23:02.080861+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.018238", "seconds": 0.018238}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 4740, "name": "SpawnProcess-1"}, "thread": {"id": 21892, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:23:02.080861+08:00", "timestamp": 1756178582.080861}}}
{"text": "2025-08-26T11:25:24.188003+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010772", "seconds": 0.010772}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 10380, "name": "SpawnProcess-1"}, "thread": {"id": 9776, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:25:24.188003+08:00", "timestamp": 1756178724.188003}}}
{"text": "2025-08-26T11:26:26.942096+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014615", "seconds": 0.014615}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 16700, "name": "SpawnProcess-2"}, "thread": {"id": 25480, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:26:26.942096+08:00", "timestamp": 1756178786.942096}}}
{"text": "2025-08-26T11:27:14.381641+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011386", "seconds": 0.011386}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22336, "name": "SpawnProcess-1"}, "thread": {"id": 9664, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:27:14.381641+08:00", "timestamp": 1756178834.381641}}}
{"text": "2025-08-26T11:30:16.778793+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013381", "seconds": 0.013381}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:16.778793+08:00", "timestamp": 1756179016.778793}}}
{"text": "2025-08-26T11:30:21.082362+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.316950", "seconds": 4.31695}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.082362+08:00", "timestamp": 1756179021.082362}}}
{"text": "2025-08-26T11:30:21.139256+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.373844", "seconds": 4.373844}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.139256+08:00", "timestamp": 1756179021.139256}}}
{"text": "2025-08-26T11:30:21.173636+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.408224", "seconds": 4.408224}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.173636+08:00", "timestamp": 1756179021.173636}}}
{"text": "2025-08-26T11:30:21.192759+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.427347", "seconds": 4.427347}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.192759+08:00", "timestamp": 1756179021.192759}}}
{"text": "2025-08-26T11:30:21.211419+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446007", "seconds": 4.446007}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.211419+08:00", "timestamp": 1756179021.211419}}}
{"text": "2025-08-26T11:30:21.211419+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.446007", "seconds": 4.446007}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2168, "name": "SpawnProcess-1"}, "thread": {"id": 7700, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:30:21.211419+08:00", "timestamp": 1756179021.211419}}}
{"text": "2025-08-26T11:31:12.090838+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015033", "seconds": 0.015033}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:12.090838+08:00", "timestamp": 1756179072.090838}}}
{"text": "2025-08-26T11:31:16.783245+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.707440", "seconds": 4.70744}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.783245+08:00", "timestamp": 1756179076.783245}}}
{"text": "2025-08-26T11:31:16.784250+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.708445", "seconds": 4.708445}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.784250+08:00", "timestamp": 1756179076.78425}}}
{"text": "2025-08-26T11:31:16.784250+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.708445", "seconds": 4.708445}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.784250+08:00", "timestamp": 1756179076.78425}}}
{"text": "2025-08-26T11:31:16.788922+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.713117", "seconds": 4.713117}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.788922+08:00", "timestamp": 1756179076.788922}}}
{"text": "2025-08-26T11:31:16.790951+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.715146", "seconds": 4.715146}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.790951+08:00", "timestamp": 1756179076.790951}}}
{"text": "2025-08-26T11:31:16.790951+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.715146", "seconds": 4.715146}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 21584, "name": "SpawnProcess-2"}, "thread": {"id": 21624, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:31:16.790951+08:00", "timestamp": 1756179076.790951}}}
{"text": "2025-08-26T11:32:01.638897+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014086", "seconds": 0.014086}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:01.638897+08:00", "timestamp": 1756179121.638897}}}
{"text": "2025-08-26T11:32:05.758193+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.133382", "seconds": 4.133382}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.758193+08:00", "timestamp": 1756179125.758193}}}
{"text": "2025-08-26T11:32:05.759693+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.134882", "seconds": 4.134882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.759693+08:00", "timestamp": 1756179125.759693}}}
{"text": "2025-08-26T11:32:05.759693+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.134882", "seconds": 4.134882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.759693+08:00", "timestamp": 1756179125.759693}}}
{"text": "2025-08-26T11:32:05.760938+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.136127", "seconds": 4.136127}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.760938+08:00", "timestamp": 1756179125.760938}}}
{"text": "2025-08-26T11:32:05.761964+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.137153", "seconds": 4.137153}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.761964+08:00", "timestamp": 1756179125.761964}}}
{"text": "2025-08-26T11:32:05.761964+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.137153", "seconds": 4.137153}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 22768, "name": "SpawnProcess-1"}, "thread": {"id": 4912, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:32:05.761964+08:00", "timestamp": 1756179125.761964}}}
{"text": "2025-08-26T11:33:03.780286+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009925", "seconds": 0.009925}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:03.780286+08:00", "timestamp": 1756179183.780286}}}
{"text": "2025-08-26T11:33:08.436395+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.666034", "seconds": 4.666034}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.436395+08:00", "timestamp": 1756179188.436395}}}
{"text": "2025-08-26T11:33:08.437393+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.667032", "seconds": 4.667032}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.437393+08:00", "timestamp": 1756179188.437393}}}
{"text": "2025-08-26T11:33:08.438493+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.668132", "seconds": 4.668132}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.438493+08:00", "timestamp": 1756179188.438493}}}
{"text": "2025-08-26T11:33:08.439967+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.669606", "seconds": 4.669606}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.439967+08:00", "timestamp": 1756179188.439967}}}
{"text": "2025-08-26T11:33:08.441231+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.670870", "seconds": 4.67087}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.441231+08:00", "timestamp": 1756179188.441231}}}
{"text": "2025-08-26T11:33:08.441231+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.670870", "seconds": 4.67087}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 928, "name": "SpawnProcess-2"}, "thread": {"id": 17452, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:33:08.441231+08:00", "timestamp": 1756179188.441231}}}
{"text": "2025-08-26T11:34:16.246830+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012497", "seconds": 0.012497}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:16.246830+08:00", "timestamp": 1756179256.24683}}}
{"text": "2025-08-26T11:34:20.284243+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.049910", "seconds": 4.04991}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.284243+08:00", "timestamp": 1756179260.284243}}}
{"text": "2025-08-26T11:34:20.285247+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.050914", "seconds": 4.050914}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.285247+08:00", "timestamp": 1756179260.285247}}}
{"text": "2025-08-26T11:34:20.286309+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.051976", "seconds": 4.051976}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.286309+08:00", "timestamp": 1756179260.286309}}}
{"text": "2025-08-26T11:34:20.286813+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.052480", "seconds": 4.05248}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.286813+08:00", "timestamp": 1756179260.286813}}}
{"text": "2025-08-26T11:34:20.287859+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.053526", "seconds": 4.053526}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.287859+08:00", "timestamp": 1756179260.287859}}}
{"text": "2025-08-26T11:34:20.287859+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.053526", "seconds": 4.053526}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13260, "name": "SpawnProcess-1"}, "thread": {"id": 17024, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:34:20.287859+08:00", "timestamp": 1756179260.287859}}}
{"text": "2025-08-26T11:35:29.025251+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014050", "seconds": 0.01405}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:29.025251+08:00", "timestamp": 1756179329.025251}}}
{"text": "2025-08-26T11:35:34.160021+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.148820", "seconds": 5.14882}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.160021+08:00", "timestamp": 1756179334.160021}}}
{"text": "2025-08-26T11:35:34.229394+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.218193", "seconds": 5.218193}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.229394+08:00", "timestamp": 1756179334.229394}}}
{"text": "2025-08-26T11:35:34.245655+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.234454", "seconds": 5.234454}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.245655+08:00", "timestamp": 1756179334.245655}}}
{"text": "2025-08-26T11:35:34.250645+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.239444", "seconds": 5.239444}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.250645+08:00", "timestamp": 1756179334.250645}}}
{"text": "2025-08-26T11:35:34.253033+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:05.241832", "seconds": 5.241832}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.253033+08:00", "timestamp": 1756179334.253033}}}
{"text": "2025-08-26T11:35:34.253033+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:05.241832", "seconds": 5.241832}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 9188, "name": "SpawnProcess-2"}, "thread": {"id": 22608, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:35:34.253033+08:00", "timestamp": 1756179334.253033}}}
{"text": "2025-08-26T11:42:12.049570+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014752", "seconds": 0.014752}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:12.049570+08:00", "timestamp": 1756179732.04957}}}
{"text": "2025-08-26T11:42:16.475933+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.441115", "seconds": 4.441115}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.475933+08:00", "timestamp": 1756179736.475933}}}
{"text": "2025-08-26T11:42:16.478931+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.444113", "seconds": 4.444113}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.478931+08:00", "timestamp": 1756179736.478931}}}
{"text": "2025-08-26T11:42:16.479944+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.445126", "seconds": 4.445126}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.479944+08:00", "timestamp": 1756179736.479944}}}
{"text": "2025-08-26T11:42:16.480946+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446128", "seconds": 4.446128}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.480946+08:00", "timestamp": 1756179736.480946}}}
{"text": "2025-08-26T11:42:16.480946+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.446128", "seconds": 4.446128}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.480946+08:00", "timestamp": 1756179736.480946}}}
{"text": "2025-08-26T11:42:16.482401+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.447583", "seconds": 4.447583}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2360, "name": "SpawnProcess-1"}, "thread": {"id": 11252, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:42:16.482401+08:00", "timestamp": 1756179736.482401}}}
{"text": "2025-08-26T11:43:23.318776+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010464", "seconds": 0.010464}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:23.318776+08:00", "timestamp": 1756179803.318776}}}
{"text": "2025-08-26T11:43:27.364579+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.056267", "seconds": 4.056267}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.364579+08:00", "timestamp": 1756179807.364579}}}
{"text": "2025-08-26T11:43:27.364579+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.056267", "seconds": 4.056267}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.364579+08:00", "timestamp": 1756179807.364579}}}
{"text": "2025-08-26T11:43:27.365944+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.057632", "seconds": 4.057632}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.365944+08:00", "timestamp": 1756179807.365944}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:43:27.366946+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.058634", "seconds": 4.058634}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 9036, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:43:27.366946+08:00", "timestamp": 1756179807.366946}}}
{"text": "2025-08-26T11:45:30.539483+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015066", "seconds": 0.015066}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:30.539483+08:00", "timestamp": 1756179930.539483}}}
{"text": "2025-08-26T11:45:34.616207+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.091790", "seconds": 4.09179}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.616207+08:00", "timestamp": 1756179934.616207}}}
{"text": "2025-08-26T11:45:34.617210+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.092793", "seconds": 4.092793}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.617210+08:00", "timestamp": 1756179934.61721}}}
{"text": "2025-08-26T11:45:34.617920+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.093503", "seconds": 4.093503}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.617920+08:00", "timestamp": 1756179934.61792}}}
{"text": "2025-08-26T11:45:34.618663+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.094246", "seconds": 4.094246}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.618663+08:00", "timestamp": 1756179934.618663}}}
{"text": "2025-08-26T11:45:34.619354+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.094937", "seconds": 4.094937}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.619354+08:00", "timestamp": 1756179934.619354}}}
{"text": "2025-08-26T11:45:34.620040+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.095623", "seconds": 4.095623}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 7336, "name": "MainProcess"}, "thread": {"id": 6984, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:45:34.620040+08:00", "timestamp": 1756179934.62004}}}
{"text": "2025-08-26T11:46:36.534287+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011085", "seconds": 0.011085}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:36.534287+08:00", "timestamp": 1756179996.534287}}}
{"text": "2025-08-26T11:46:40.756304+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.233102", "seconds": 4.233102}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.756304+08:00", "timestamp": 1756180000.756304}}}
{"text": "2025-08-26T11:46:40.757660+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.234458", "seconds": 4.234458}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.757660+08:00", "timestamp": 1756180000.75766}}}
{"text": "2025-08-26T11:46:40.757660+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.234458", "seconds": 4.234458}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.757660+08:00", "timestamp": 1756180000.75766}}}
{"text": "2025-08-26T11:46:40.758703+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.235501", "seconds": 4.235501}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.758703+08:00", "timestamp": 1756180000.758703}}}
{"text": "2025-08-26T11:46:40.759700+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.236498", "seconds": 4.236498}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.759700+08:00", "timestamp": 1756180000.7597}}}
{"text": "2025-08-26T11:46:40.759700+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.236498", "seconds": 4.236498}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 23020, "name": "MainProcess"}, "thread": {"id": 21560, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:46:40.759700+08:00", "timestamp": 1756180000.7597}}}
{"text": "2025-08-26T11:47:05.685580+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012582", "seconds": 0.012582}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:05.685580+08:00", "timestamp": 1756180025.68558}}}
{"text": "2025-08-26T11:47:09.089340+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.416342", "seconds": 3.416342}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.089340+08:00", "timestamp": 1756180029.08934}}}
{"text": "2025-08-26T11:47:09.253820+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.580822", "seconds": 3.580822}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.253820+08:00", "timestamp": 1756180029.25382}}}
{"text": "2025-08-26T11:47:09.372320+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.699322", "seconds": 3.699322}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.372320+08:00", "timestamp": 1756180029.37232}}}
{"text": "2025-08-26T11:47:09.373327+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.700329", "seconds": 3.700329}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.373327+08:00", "timestamp": 1756180029.373327}}}
{"text": "2025-08-26T11:47:09.373327+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.700329", "seconds": 3.700329}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.373327+08:00", "timestamp": 1756180029.373327}}}
{"text": "2025-08-26T11:47:09.374323+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.701325", "seconds": 3.701325}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.374323+08:00", "timestamp": 1756180029.374323}}}
{"text": "2025-08-26T11:47:09.947159+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.274161", "seconds": 4.274161}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.947159+08:00", "timestamp": 1756180029.947159}}}
{"text": "2025-08-26T11:47:09.948156+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.275158", "seconds": 4.275158}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.948156+08:00", "timestamp": 1756180029.948156}}}
{"text": "2025-08-26T11:47:09.949216+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.276218", "seconds": 4.276218}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.949216+08:00", "timestamp": 1756180029.949216}}}
{"text": "2025-08-26T11:47:09.958751+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.285753", "seconds": 4.285753}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19624, "name": "MainProcess"}, "thread": {"id": 26224, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:09.958751+08:00", "timestamp": 1756180029.958751}}}
{"text": "2025-08-26T11:47:48.122189+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014048", "seconds": 0.014048}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:48.122189+08:00", "timestamp": 1756180068.122189}}}
{"text": "2025-08-26T11:47:51.400860+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.292719", "seconds": 3.292719}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.400860+08:00", "timestamp": 1756180071.40086}}}
{"text": "2025-08-26T11:47:51.553455+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.445314", "seconds": 3.445314}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.553455+08:00", "timestamp": 1756180071.553455}}}
{"text": "2025-08-26T11:47:51.581890+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.473749", "seconds": 3.473749}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.581890+08:00", "timestamp": 1756180071.58189}}}
{"text": "2025-08-26T11:47:51.582895+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.474754", "seconds": 3.474754}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.582895+08:00", "timestamp": 1756180071.582895}}}
{"text": "2025-08-26T11:47:51.582895+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.474754", "seconds": 3.474754}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.582895+08:00", "timestamp": 1756180071.582895}}}
{"text": "2025-08-26T11:47:51.584145+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.476004", "seconds": 3.476004}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:51.584145+08:00", "timestamp": 1756180071.584145}}}
{"text": "2025-08-26T11:47:52.096553+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:03.988412", "seconds": 3.988412}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.096553+08:00", "timestamp": 1756180072.096553}}}
{"text": "2025-08-26T11:47:52.097703+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.989562", "seconds": 3.989562}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.097703+08:00", "timestamp": 1756180072.097703}}}
{"text": "2025-08-26T11:47:52.097703+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:03.989562", "seconds": 3.989562}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.097703+08:00", "timestamp": 1756180072.097703}}}
{"text": "2025-08-26T11:47:52.100075+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:03.991934", "seconds": 3.991934}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 268, "name": "MainProcess"}, "thread": {"id": 22956, "name": "MainThread"}, "time": {"repr": "2025-08-26 11:47:52.100075+08:00", "timestamp": 1756180072.100075}}}
{"text": "2025-08-26T12:29:43.830995+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013560", "seconds": 0.01356}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:43.830995+08:00", "timestamp": 1756182583.830995}}}
{"text": "2025-08-26T12:29:47.328834+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.511399", "seconds": 3.511399}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.328834+08:00", "timestamp": 1756182587.328834}}}
{"text": "2025-08-26T12:29:47.529800+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.712365", "seconds": 3.712365}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.529800+08:00", "timestamp": 1756182587.5298}}}
{"text": "2025-08-26T12:29:47.558609+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.741174", "seconds": 3.741174}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.558609+08:00", "timestamp": 1756182587.558609}}}
{"text": "2025-08-26T12:29:47.559797+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.742362", "seconds": 3.742362}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.559797+08:00", "timestamp": 1756182587.559797}}}
{"text": "2025-08-26T12:29:47.559797+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.742362", "seconds": 3.742362}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.559797+08:00", "timestamp": 1756182587.559797}}}
{"text": "2025-08-26T12:29:47.560803+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.743368", "seconds": 3.743368}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:47.560803+08:00", "timestamp": 1756182587.560803}}}
{"text": "2025-08-26T12:29:48.045631+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.228196", "seconds": 4.228196}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:48.045631+08:00", "timestamp": 1756182588.045631}}}
{"text": "2025-08-26T12:29:48.046642+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.229207", "seconds": 4.229207}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:48.046642+08:00", "timestamp": 1756182588.046642}}}
{"text": "2025-08-26T12:29:48.046642+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.229207", "seconds": 4.229207}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:48.046642+08:00", "timestamp": 1756182588.046642}}}
{"text": "2025-08-26T12:29:48.048135+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.230700", "seconds": 4.2307}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 25992, "name": "MainProcess"}, "thread": {"id": 7464, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:29:48.048135+08:00", "timestamp": 1756182588.048135}}}
{"text": "2025-08-26T12:30:42.569682+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012337", "seconds": 0.012337}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:42.569682+08:00", "timestamp": 1756182642.569682}}}
{"text": "2025-08-26T12:30:46.077443+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.520098", "seconds": 3.520098}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.077443+08:00", "timestamp": 1756182646.077443}}}
{"text": "2025-08-26T12:30:46.247909+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.690564", "seconds": 3.690564}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.247909+08:00", "timestamp": 1756182646.247909}}}
{"text": "2025-08-26T12:30:46.273748+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.716403", "seconds": 3.716403}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.273748+08:00", "timestamp": 1756182646.273748}}}
{"text": "2025-08-26T12:30:46.274756+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.717411", "seconds": 3.717411}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.274756+08:00", "timestamp": 1756182646.274756}}}
{"text": "2025-08-26T12:30:46.275868+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.718523", "seconds": 3.718523}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.275868+08:00", "timestamp": 1756182646.275868}}}
{"text": "2025-08-26T12:30:46.275868+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.718523", "seconds": 3.718523}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.275868+08:00", "timestamp": 1756182646.275868}}}
{"text": "2025-08-26T12:30:46.825331+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.267986", "seconds": 4.267986}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.825331+08:00", "timestamp": 1756182646.825331}}}
{"text": "2025-08-26T12:30:46.826355+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.269010", "seconds": 4.26901}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.826355+08:00", "timestamp": 1756182646.826355}}}
{"text": "2025-08-26T12:30:46.827375+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.270030", "seconds": 4.27003}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.827375+08:00", "timestamp": 1756182646.827375}}}
{"text": "2025-08-26T12:30:46.828418+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.271073", "seconds": 4.271073}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3612, "name": "MainProcess"}, "thread": {"id": 18472, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:30:46.828418+08:00", "timestamp": 1756182646.828418}}}
{"text": "2025-08-26T12:31:12.126212+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.015417", "seconds": 0.015417}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:12.126212+08:00", "timestamp": 1756182672.126212}}}
{"text": "2025-08-26T12:31:16.525156+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.414361", "seconds": 4.414361}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.525156+08:00", "timestamp": 1756182676.525156}}}
{"text": "2025-08-26T12:31:16.528154+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.417359", "seconds": 4.417359}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.528154+08:00", "timestamp": 1756182676.528154}}}
{"text": "2025-08-26T12:31:16.528154+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.417359", "seconds": 4.417359}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.528154+08:00", "timestamp": 1756182676.528154}}}
{"text": "2025-08-26T12:31:16.529447+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.418652", "seconds": 4.418652}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.529447+08:00", "timestamp": 1756182676.529447}}}
{"text": "2025-08-26T12:31:16.530717+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.419922", "seconds": 4.419922}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.530717+08:00", "timestamp": 1756182676.530717}}}
{"text": "2025-08-26T12:31:16.530717+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.419922", "seconds": 4.419922}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 25436, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:31:16.530717+08:00", "timestamp": 1756182676.530717}}}
{"text": "2025-08-26T12:32:30.984814+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011613", "seconds": 0.011613}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:30.984814+08:00", "timestamp": 1756182750.984814}}}
{"text": "2025-08-26T12:32:37.721048+0800 | WARNING | app.services.ml_model_service | <module> | 37 | XGBoost未安装，XGBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:06.747847", "seconds": 6.747847}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "XGBoost未安装，XGBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.721048+08:00", "timestamp": 1756182757.721048}}}
{"text": "2025-08-26T12:32:37.764665+0800 | WARNING | app.services.ml_model_service | <module> | 44 | LightGBM未安装，LightGBM功能不可用\n", "record": {"elapsed": {"repr": "0:00:06.791464", "seconds": 6.791464}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 44, "message": "LightGBM未安装，LightGBM功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.764665+08:00", "timestamp": 1756182757.764665}}}
{"text": "2025-08-26T12:32:37.767657+0800 | WARNING | app.services.ml_model_service | <module> | 51 | CatBoost未安装，CatBoost功能不可用\n", "record": {"elapsed": {"repr": "0:00:06.794456", "seconds": 6.794456}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 51, "message": "CatBoost未安装，CatBoost功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.767657+08:00", "timestamp": 1756182757.767657}}}
{"text": "2025-08-26T12:32:37.768656+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:06.795455", "seconds": 6.795455}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.768656+08:00", "timestamp": 1756182757.768656}}}
{"text": "2025-08-26T12:32:37.769653+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:06.796452", "seconds": 6.796452}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.769653+08:00", "timestamp": 1756182757.769653}}}
{"text": "2025-08-26T12:32:37.769653+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:06.796452", "seconds": 6.796452}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19364, "name": "MainProcess"}, "thread": {"id": 12176, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:32:37.769653+08:00", "timestamp": 1756182757.769653}}}
{"text": "2025-08-26T12:33:03.851649+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010271", "seconds": 0.010271}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:03.851649+08:00", "timestamp": 1756182783.851649}}}
{"text": "2025-08-26T12:33:07.268571+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.427193", "seconds": 3.427193}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.268571+08:00", "timestamp": 1756182787.268571}}}
{"text": "2025-08-26T12:33:07.437568+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.596190", "seconds": 3.59619}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.437568+08:00", "timestamp": 1756182787.437568}}}
{"text": "2025-08-26T12:33:07.466089+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.624711", "seconds": 3.624711}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.466089+08:00", "timestamp": 1756182787.466089}}}
{"text": "2025-08-26T12:33:07.467092+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.625714", "seconds": 3.625714}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.467092+08:00", "timestamp": 1756182787.467092}}}
{"text": "2025-08-26T12:33:07.471133+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.629755", "seconds": 3.629755}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.471133+08:00", "timestamp": 1756182787.471133}}}
{"text": "2025-08-26T12:33:07.471638+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.630260", "seconds": 3.63026}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.471638+08:00", "timestamp": 1756182787.471638}}}
{"text": "2025-08-26T12:33:07.970920+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.129542", "seconds": 4.129542}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.970920+08:00", "timestamp": 1756182787.97092}}}
{"text": "2025-08-26T12:33:07.971945+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.130567", "seconds": 4.130567}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.971945+08:00", "timestamp": 1756182787.971945}}}
{"text": "2025-08-26T12:33:07.973453+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.132075", "seconds": 4.132075}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.973453+08:00", "timestamp": 1756182787.973453}}}
{"text": "2025-08-26T12:33:07.974462+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.133084", "seconds": 4.133084}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3504, "name": "MainProcess"}, "thread": {"id": 12160, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:07.974462+08:00", "timestamp": 1756182787.974462}}}
{"text": "2025-08-26T12:33:48.174147+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011104", "seconds": 0.011104}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:48.174147+08:00", "timestamp": 1756182828.174147}}}
{"text": "2025-08-26T12:33:51.478033+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.314990", "seconds": 3.31499}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.478033+08:00", "timestamp": 1756182831.478033}}}
{"text": "2025-08-26T12:33:51.708832+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.545789", "seconds": 3.545789}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.708832+08:00", "timestamp": 1756182831.708832}}}
{"text": "2025-08-26T12:33:51.765307+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.602264", "seconds": 3.602264}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.765307+08:00", "timestamp": 1756182831.765307}}}
{"text": "2025-08-26T12:33:51.766385+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.603342", "seconds": 3.603342}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.766385+08:00", "timestamp": 1756182831.766385}}}
{"text": "2025-08-26T12:33:51.766385+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.603342", "seconds": 3.603342}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.766385+08:00", "timestamp": 1756182831.766385}}}
{"text": "2025-08-26T12:33:51.767387+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.604344", "seconds": 3.604344}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:51.767387+08:00", "timestamp": 1756182831.767387}}}
{"text": "2025-08-26T12:33:52.600103+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.437060", "seconds": 4.43706}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:52.600103+08:00", "timestamp": 1756182832.600103}}}
{"text": "2025-08-26T12:33:52.631397+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.468354", "seconds": 4.468354}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:52.631397+08:00", "timestamp": 1756182832.631397}}}
{"text": "2025-08-26T12:33:52.632399+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.469356", "seconds": 4.469356}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:52.632399+08:00", "timestamp": 1756182832.632399}}}
{"text": "2025-08-26T12:33:52.635391+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.472348", "seconds": 4.472348}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 13240, "name": "MainProcess"}, "thread": {"id": 10888, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:33:52.635391+08:00", "timestamp": 1756182832.635391}}}
{"text": "2025-08-26T12:35:00.502406+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013329", "seconds": 0.013329}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:00.502406+08:00", "timestamp": 1756182900.502406}}}
{"text": "2025-08-26T12:35:03.793712+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.304635", "seconds": 3.304635}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.793712+08:00", "timestamp": 1756182903.793712}}}
{"text": "2025-08-26T12:35:03.963395+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.474318", "seconds": 3.474318}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.963395+08:00", "timestamp": 1756182903.963395}}}
{"text": "2025-08-26T12:35:03.990409+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.501332", "seconds": 3.501332}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.990409+08:00", "timestamp": 1756182903.990409}}}
{"text": "2025-08-26T12:35:03.991413+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.502336", "seconds": 3.502336}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.991413+08:00", "timestamp": 1756182903.991413}}}
{"text": "2025-08-26T12:35:03.992554+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.503477", "seconds": 3.503477}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.992554+08:00", "timestamp": 1756182903.992554}}}
{"text": "2025-08-26T12:35:03.992554+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.503477", "seconds": 3.503477}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:03.992554+08:00", "timestamp": 1756182903.992554}}}
{"text": "2025-08-26T12:35:04.489658+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.000581", "seconds": 4.000581}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:04.489658+08:00", "timestamp": 1756182904.489658}}}
{"text": "2025-08-26T12:35:04.490971+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.001894", "seconds": 4.001894}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:04.490971+08:00", "timestamp": 1756182904.490971}}}
{"text": "2025-08-26T12:35:04.492104+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.003027", "seconds": 4.003027}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:04.492104+08:00", "timestamp": 1756182904.492104}}}
{"text": "2025-08-26T12:35:04.493811+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.004734", "seconds": 4.004734}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19356, "name": "MainProcess"}, "thread": {"id": 9456, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:35:04.493811+08:00", "timestamp": 1756182904.493811}}}
{"text": "2025-08-26T12:37:03.656041+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.012243", "seconds": 0.012243}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:03.656041+08:00", "timestamp": 1756183023.656041}}}
{"text": "2025-08-26T12:37:07.342133+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.698335", "seconds": 3.698335}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.342133+08:00", "timestamp": 1756183027.342133}}}
{"text": "2025-08-26T12:37:07.535046+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.891248", "seconds": 3.891248}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.535046+08:00", "timestamp": 1756183027.535046}}}
{"text": "2025-08-26T12:37:07.567814+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.924016", "seconds": 3.924016}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.567814+08:00", "timestamp": 1756183027.567814}}}
{"text": "2025-08-26T12:37:07.571805+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.928007", "seconds": 3.928007}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.571805+08:00", "timestamp": 1756183027.571805}}}
{"text": "2025-08-26T12:37:07.575179+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.931381", "seconds": 3.931381}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.575179+08:00", "timestamp": 1756183027.575179}}}
{"text": "2025-08-26T12:37:07.576181+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.932383", "seconds": 3.932383}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:07.576181+08:00", "timestamp": 1756183027.576181}}}
{"text": "2025-08-26T12:37:08.178912+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.535114", "seconds": 4.535114}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:08.178912+08:00", "timestamp": 1756183028.178912}}}
{"text": "2025-08-26T12:37:08.180202+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.536404", "seconds": 4.536404}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:08.180202+08:00", "timestamp": 1756183028.180202}}}
{"text": "2025-08-26T12:37:08.181205+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.537407", "seconds": 4.537407}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:08.181205+08:00", "timestamp": 1756183028.181205}}}
{"text": "2025-08-26T12:37:08.183205+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.539407", "seconds": 4.539407}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:08.183205+08:00", "timestamp": 1756183028.183205}}}
{"text": "2025-08-26T12:37:10.231855+0800 | INFO | app.main | lifespan | 28 | 🚀 JQData量化平台启动中...\n", "record": {"elapsed": {"repr": "0:00:06.588057", "seconds": 6.588057}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "🚀 JQData量化平台启动中...", "module": "main", "name": "app.main", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:10.231855+08:00", "timestamp": 1756183030.231855}}}
{"text": "2025-08-26T12:37:14.326891+0800 | ERROR | app.core.database | init_db | 210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。\n", "record": {"elapsed": {"repr": "0:00:10.683093", "seconds": 10.683093}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "init_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 210, "message": "数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。", "module": "database", "name": "app.core.database", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:14.326891+08:00", "timestamp": 1756183034.326891}}}
{"text": "2025-08-26T12:37:14.338612+0800 | ERROR | app.main | lifespan | 42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。\n", "record": {"elapsed": {"repr": "0:00:10.694814", "seconds": 10.694814}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 42, "message": "❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。", "module": "main", "name": "app.main", "process": {"id": 2772, "name": "MainProcess"}, "thread": {"id": 11684, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:37:14.338612+08:00", "timestamp": 1756183034.338612}}}
{"text": "2025-08-26T12:39:20.253197+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.010348", "seconds": 0.010348}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:20.253197+08:00", "timestamp": 1756183160.253197}}}
{"text": "2025-08-26T12:39:23.801024+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.558175", "seconds": 3.558175}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:23.801024+08:00", "timestamp": 1756183163.801024}}}
{"text": "2025-08-26T12:39:23.988159+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.745310", "seconds": 3.74531}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:23.988159+08:00", "timestamp": 1756183163.988159}}}
{"text": "2025-08-26T12:39:24.016771+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.773922", "seconds": 3.773922}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.016771+08:00", "timestamp": 1756183164.016771}}}
{"text": "2025-08-26T12:39:24.017768+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.774919", "seconds": 3.774919}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.017768+08:00", "timestamp": 1756183164.017768}}}
{"text": "2025-08-26T12:39:24.019321+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.776472", "seconds": 3.776472}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.019321+08:00", "timestamp": 1756183164.019321}}}
{"text": "2025-08-26T12:39:24.019321+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.776472", "seconds": 3.776472}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.019321+08:00", "timestamp": 1756183164.019321}}}
{"text": "2025-08-26T12:39:24.553729+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.310880", "seconds": 4.31088}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.553729+08:00", "timestamp": 1756183164.553729}}}
{"text": "2025-08-26T12:39:24.554726+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.311877", "seconds": 4.311877}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.554726+08:00", "timestamp": 1756183164.554726}}}
{"text": "2025-08-26T12:39:24.556723+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.313874", "seconds": 4.313874}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.556723+08:00", "timestamp": 1756183164.556723}}}
{"text": "2025-08-26T12:39:24.560161+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.317312", "seconds": 4.317312}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:24.560161+08:00", "timestamp": 1756183164.560161}}}
{"text": "2025-08-26T12:39:26.597333+0800 | INFO | app.main | lifespan | 28 | 🚀 JQData量化平台启动中...\n", "record": {"elapsed": {"repr": "0:00:06.354484", "seconds": 6.354484}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "🚀 JQData量化平台启动中...", "module": "main", "name": "app.main", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:26.597333+08:00", "timestamp": 1756183166.597333}}}
{"text": "2025-08-26T12:39:30.641414+0800 | ERROR | app.core.database | init_db | 210 | 数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。\n", "record": {"elapsed": {"repr": "0:00:10.398565", "seconds": 10.398565}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "init_db", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 210, "message": "数据库初始化失败: [WinError 1225] 远程计算机拒绝网络连接。", "module": "database", "name": "app.core.database", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:30.641414+08:00", "timestamp": 1756183170.641414}}}
{"text": "2025-08-26T12:39:30.645108+0800 | ERROR | app.main | lifespan | 42 | ❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。\n", "record": {"elapsed": {"repr": "0:00:10.402259", "seconds": 10.402259}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 42, "message": "❌ 应用启动失败: [WinError 1225] 远程计算机拒绝网络连接。", "module": "main", "name": "app.main", "process": {"id": 19876, "name": "MainProcess"}, "thread": {"id": 4900, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:39:30.645108+08:00", "timestamp": 1756183170.645108}}}
{"text": "2025-08-26T12:41:17.981867+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011698", "seconds": 0.011698}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:17.981867+08:00", "timestamp": 1756183277.981867}}}
{"text": "2025-08-26T12:41:21.407114+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.436945", "seconds": 3.436945}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.407114+08:00", "timestamp": 1756183281.407114}}}
{"text": "2025-08-26T12:41:21.587065+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.616896", "seconds": 3.616896}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.587065+08:00", "timestamp": 1756183281.587065}}}
{"text": "2025-08-26T12:41:21.618773+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.648604", "seconds": 3.648604}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.618773+08:00", "timestamp": 1756183281.618773}}}
{"text": "2025-08-26T12:41:21.619770+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.649601", "seconds": 3.649601}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.619770+08:00", "timestamp": 1756183281.61977}}}
{"text": "2025-08-26T12:41:21.620768+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.650599", "seconds": 3.650599}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.620768+08:00", "timestamp": 1756183281.620768}}}
{"text": "2025-08-26T12:41:21.620768+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.650599", "seconds": 3.650599}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:21.620768+08:00", "timestamp": 1756183281.620768}}}
{"text": "2025-08-26T12:41:22.197196+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.227027", "seconds": 4.227027}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:22.197196+08:00", "timestamp": 1756183282.197196}}}
{"text": "2025-08-26T12:41:22.198200+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.228031", "seconds": 4.228031}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:22.198200+08:00", "timestamp": 1756183282.1982}}}
{"text": "2025-08-26T12:41:22.198200+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.228031", "seconds": 4.228031}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:22.198200+08:00", "timestamp": 1756183282.1982}}}
{"text": "2025-08-26T12:41:22.202598+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.232429", "seconds": 4.232429}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:22.202598+08:00", "timestamp": 1756183282.202598}}}
{"text": "2025-08-26T12:41:24.001420+0800 | INFO | app.main | lifespan | 28 | 🚀 JQData量化平台启动中...\n", "record": {"elapsed": {"repr": "0:00:06.031251", "seconds": 6.031251}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "🚀 JQData量化平台启动中...", "module": "main", "name": "app.main", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:24.001420+08:00", "timestamp": 1756183284.00142}}}
{"text": "2025-08-26T12:41:24.007150+0800 | INFO | app.core.database | init_db | 207 | 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:06.036981", "seconds": 6.036981}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 207, "message": "数据库初始化完成", "module": "database", "name": "app.core.database", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:24.007150+08:00", "timestamp": 1756183284.00715}}}
{"text": "2025-08-26T12:41:24.008149+0800 | INFO | app.main | lifespan | 33 | ✅ 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:06.037980", "seconds": 6.03798}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 33, "message": "✅ 数据库初始化完成", "module": "main", "name": "app.main", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:24.008149+08:00", "timestamp": 1756183284.008149}}}
{"text": "2025-08-26T12:41:28.087780+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:00:10.117611", "seconds": 10.117611}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:28.087780+08:00", "timestamp": 1756183288.08778}}}
{"text": "2025-08-26T12:41:28.088404+0800 | ERROR | app.main | lifespan | 42 | ❌ 应用启动失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:00:10.118235", "seconds": 10.118235}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 42, "message": "❌ 应用启动失败: Error 22 connecting to localhost:6379. 22.", "module": "main", "name": "app.main", "process": {"id": 11728, "name": "MainProcess"}, "thread": {"id": 23128, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:41:28.088404+08:00", "timestamp": 1756183288.088404}}}
{"text": "2025-08-26T12:42:57.836068+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013810", "seconds": 0.01381}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:42:57.836068+08:00", "timestamp": 1756183377.836068}}}
{"text": "2025-08-26T12:43:01.869969+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:04.047711", "seconds": 4.047711}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:01.869969+08:00", "timestamp": 1756183381.869969}}}
{"text": "2025-08-26T12:43:02.070051+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:04.247793", "seconds": 4.247793}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.070051+08:00", "timestamp": 1756183382.070051}}}
{"text": "2025-08-26T12:43:02.102384+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:04.280126", "seconds": 4.280126}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.102384+08:00", "timestamp": 1756183382.102384}}}
{"text": "2025-08-26T12:43:02.103652+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.281394", "seconds": 4.281394}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.103652+08:00", "timestamp": 1756183382.103652}}}
{"text": "2025-08-26T12:43:02.103652+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.281394", "seconds": 4.281394}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.103652+08:00", "timestamp": 1756183382.103652}}}
{"text": "2025-08-26T12:43:02.104746+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:04.282488", "seconds": 4.282488}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.104746+08:00", "timestamp": 1756183382.104746}}}
{"text": "2025-08-26T12:43:02.712585+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.890327", "seconds": 4.890327}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.712585+08:00", "timestamp": 1756183382.712585}}}
{"text": "2025-08-26T12:43:02.713586+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.891328", "seconds": 4.891328}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.713586+08:00", "timestamp": 1756183382.713586}}}
{"text": "2025-08-26T12:43:02.719925+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.897667", "seconds": 4.897667}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.719925+08:00", "timestamp": 1756183382.719925}}}
{"text": "2025-08-26T12:43:02.722565+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.900307", "seconds": 4.900307}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:02.722565+08:00", "timestamp": 1756183382.722565}}}
{"text": "2025-08-26T12:43:04.870275+0800 | INFO | app.main | lifespan | 28 | 🚀 JQData量化平台启动中...\n", "record": {"elapsed": {"repr": "0:00:07.048017", "seconds": 7.048017}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "🚀 JQData量化平台启动中...", "module": "main", "name": "app.main", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:04.870275+08:00", "timestamp": 1756183384.870275}}}
{"text": "2025-08-26T12:43:04.875903+0800 | INFO | app.core.database | init_db | 206 | 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:07.053645", "seconds": 7.053645}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 206, "message": "数据库初始化完成", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:04.875903+08:00", "timestamp": 1756183384.875903}}}
{"text": "2025-08-26T12:43:04.875903+0800 | INFO | app.main | lifespan | 33 | ✅ 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:07.053645", "seconds": 7.053645}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 33, "message": "✅ 数据库初始化完成", "module": "main", "name": "app.main", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:04.875903+08:00", "timestamp": 1756183384.875903}}}
{"text": "2025-08-26T12:43:08.945533+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:00:11.123275", "seconds": 11.123275}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:08.945533+08:00", "timestamp": 1756183388.945533}}}
{"text": "2025-08-26T12:43:08.947532+0800 | WARNING | app.core.database | connect | 165 | Redis不可用，缓存功能将被禁用\n", "record": {"elapsed": {"repr": "0:00:11.125274", "seconds": 11.125274}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 165, "message": "Redis不可用，缓存功能将被禁用", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:08.947532+08:00", "timestamp": 1756183388.947532}}}
{"text": "2025-08-26T12:43:08.948528+0800 | INFO | app.main | lifespan | 37 | ✅ Redis连接成功\n", "record": {"elapsed": {"repr": "0:00:11.126270", "seconds": 11.12627}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 37, "message": "✅ Redis连接成功", "module": "main", "name": "app.main", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:08.948528+08:00", "timestamp": 1756183388.948528}}}
{"text": "2025-08-26T12:43:08.949526+0800 | INFO | app.main | lifespan | 39 | 🎉 JQData量化平台启动完成\n", "record": {"elapsed": {"repr": "0:00:11.127268", "seconds": 11.127268}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 39, "message": "🎉 JQData量化平台启动完成", "module": "main", "name": "app.main", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:08.949526+08:00", "timestamp": 1756183388.949526}}}
{"text": "2025-08-26T12:43:48.191649+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:00:50.369391", "seconds": 50.369391}, "exception": null, "extra": {"extra": {"request_id": "2735c47b-07c3-4f55-9582-3272a5072b62", "method": "GET", "url": "http://127.0.0.1:8000/", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:48.191649+08:00", "timestamp": **********.191649}}}
{"text": "2025-08-26T12:43:48.193740+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:00:50.371482", "seconds": 50.371482}, "exception": null, "extra": {"extra": {"request_id": "2735c47b-07c3-4f55-9582-3272a5072b62", "method": "GET", "url": "http://127.0.0.1:8000/", "status_code": 200, "process_time": 0.0021, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:48.193740+08:00", "timestamp": **********.19374}}}
{"text": "2025-08-26T12:43:48.679470+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:00:50.857212", "seconds": 50.857212}, "exception": null, "extra": {"extra": {"request_id": "021d0c8a-3d69-473b-80bc-cf233ff753d9", "method": "GET", "url": "http://127.0.0.1:8000/favicon.ico", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:48.679470+08:00", "timestamp": **********.67947}}}
{"text": "2025-08-26T12:43:48.680735+0800 | ERROR | app.main | http_exception_handler | 110 | HTTP异常: 404 - Not Found\n", "record": {"elapsed": {"repr": "0:00:50.858477", "seconds": 50.858477}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "http_exception_handler", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 110, "message": "HTTP异常: 404 - Not Found", "module": "main", "name": "app.main", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:48.680735+08:00", "timestamp": **********.680735}}}
{"text": "2025-08-26T12:43:48.687902+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:00:50.865644", "seconds": 50.865644}, "exception": null, "extra": {"extra": {"request_id": "021d0c8a-3d69-473b-80bc-cf233ff753d9", "method": "GET", "url": "http://127.0.0.1:8000/favicon.ico", "status_code": 404, "process_time": 0.0094, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:43:48.687902+08:00", "timestamp": **********.687902}}}
{"text": "2025-08-26T12:44:19.029771+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:01:21.207513", "seconds": 81.207513}, "exception": null, "extra": {"extra": {"request_id": "036cca75-7874-44f1-b50a-a60d3780c426", "method": "GET", "url": "http://localhost:8000/health", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4768", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:19.029771+08:00", "timestamp": **********.029771}}}
{"text": "2025-08-26T12:44:19.030767+0800 | ERROR | app.core.database | check_database_health | 235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')\n", "record": {"elapsed": {"repr": "0:01:21.208509", "seconds": 81.208509}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "check_database_health", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 235, "message": "数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:19.030767+08:00", "timestamp": **********.030767}}}
{"text": "2025-08-26T12:44:23.104179+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:01:25.281921", "seconds": 85.281921}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:23.104179+08:00", "timestamp": **********.104179}}}
{"text": "2025-08-26T12:44:23.109166+0800 | WARNING | app.core.database | connect | 165 | Redis不可用，缓存功能将被禁用\n", "record": {"elapsed": {"repr": "0:01:25.286908", "seconds": 85.286908}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 165, "message": "Redis不可用，缓存功能将被禁用", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:23.109166+08:00", "timestamp": **********.109166}}}
{"text": "2025-08-26T12:44:23.110605+0800 | ERROR | app.core.database | check_redis_health | 246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'\n", "record": {"elapsed": {"repr": "0:01:25.288347", "seconds": 85.288347}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "check_redis_health", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "Redis健康检查失败: 'NoneType' object has no attribute 'ping'", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:23.110605+08:00", "timestamp": **********.110605}}}
{"text": "2025-08-26T12:44:23.110605+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:01:25.288347", "seconds": 85.288347}, "exception": null, "extra": {"extra": {"request_id": "036cca75-7874-44f1-b50a-a60d3780c426", "method": "GET", "url": "http://localhost:8000/health", "status_code": 200, "process_time": 4.0808, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:44:23.110605+08:00", "timestamp": **********.110605}}}
{"text": "2025-08-26T12:46:21.728353+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:03:23.906095", "seconds": 203.906095}, "exception": null, "extra": {"extra": {"request_id": "a434d79f-0965-4844-8920-93e0cbf93cf5", "method": "GET", "url": "http://localhost:8000/health", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4768", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:21.728353+08:00", "timestamp": **********.728353}}}
{"text": "2025-08-26T12:46:21.729777+0800 | ERROR | app.core.database | check_database_health | 235 | 数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')\n", "record": {"elapsed": {"repr": "0:03:23.907519", "seconds": 203.907519}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "check_database_health", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 235, "message": "数据库健康检查失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:21.729777+08:00", "timestamp": **********.729777}}}
{"text": "2025-08-26T12:46:25.785162+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:03:27.962904", "seconds": 207.962904}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:25.785162+08:00", "timestamp": **********.785162}}}
{"text": "2025-08-26T12:46:25.786345+0800 | WARNING | app.core.database | connect | 165 | Redis不可用，缓存功能将被禁用\n", "record": {"elapsed": {"repr": "0:03:27.964087", "seconds": 207.964087}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 165, "message": "Redis不可用，缓存功能将被禁用", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:25.786345+08:00", "timestamp": **********.786345}}}
{"text": "2025-08-26T12:46:25.786345+0800 | ERROR | app.core.database | check_redis_health | 246 | Redis健康检查失败: 'NoneType' object has no attribute 'ping'\n", "record": {"elapsed": {"repr": "0:03:27.964087", "seconds": 207.964087}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "check_redis_health", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 246, "message": "Redis健康检查失败: 'NoneType' object has no attribute 'ping'", "module": "database", "name": "app.core.database", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:25.786345+08:00", "timestamp": **********.786345}}}
{"text": "2025-08-26T12:46:25.787383+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:03:27.965125", "seconds": 207.965125}, "exception": null, "extra": {"extra": {"request_id": "a434d79f-0965-4844-8920-93e0cbf93cf5", "method": "GET", "url": "http://localhost:8000/health", "status_code": 200, "process_time": 4.059, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 3548, "name": "MainProcess"}, "thread": {"id": 1968, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:46:25.787383+08:00", "timestamp": **********.787383}}}
{"text": "2025-08-26T12:47:02.709029+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014242", "seconds": 0.014242}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:02.709029+08:00", "timestamp": 1756183622.709029}}}
{"text": "2025-08-26T12:47:06.127866+0800 | INFO | app.services.ml_model_service | <module> | 35 | XGBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.433079", "seconds": 3.433079}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 35, "message": "XGBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.127866+08:00", "timestamp": 1756183626.127866}}}
{"text": "2025-08-26T12:47:06.293788+0800 | INFO | app.services.ml_model_service | <module> | 42 | LightGBM已加载\n", "record": {"elapsed": {"repr": "0:00:03.599001", "seconds": 3.599001}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 42, "message": "LightGBM已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.293788+08:00", "timestamp": 1756183626.293788}}}
{"text": "2025-08-26T12:47:06.321136+0800 | INFO | app.services.ml_model_service | <module> | 49 | CatBoost已加载\n", "record": {"elapsed": {"repr": "0:00:03.626349", "seconds": 3.626349}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 49, "message": "CatBoost已加载", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.321136+08:00", "timestamp": 1756183626.321136}}}
{"text": "2025-08-26T12:47:06.321136+0800 | WARNING | app.services.ml_model_service | <module> | 65 | TensorFlow未安装，TensorFlow功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.626349", "seconds": 3.626349}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 65, "message": "TensorFlow未安装，TensorFlow功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.321136+08:00", "timestamp": 1756183626.321136}}}
{"text": "2025-08-26T12:47:06.322295+0800 | WARNING | app.services.ml_model_service | <module> | 74 | PyTorch未安装，PyTorch功能不可用\n", "record": {"elapsed": {"repr": "0:00:03.627508", "seconds": 3.627508}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 74, "message": "PyTorch未安装，PyTorch功能不可用", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.322295+08:00", "timestamp": 1756183626.322295}}}
{"text": "2025-08-26T12:47:06.322295+0800 | WARNING | app.services.ml_model_service | <module> | 77 | 未安装深度学习库，将使用传统机器学习方法\n", "record": {"elapsed": {"repr": "0:00:03.627508", "seconds": 3.627508}, "exception": null, "extra": {}, "file": {"name": "ml_model_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\ml_model_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 77, "message": "未安装深度学习库，将使用传统机器学习方法", "module": "ml_model_service", "name": "app.services.ml_model_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.322295+08:00", "timestamp": 1756183626.322295}}}
{"text": "2025-08-26T12:47:06.810586+0800 | INFO | app.services.news_sentiment_service | <module> | 25 | jieba已加载\n", "record": {"elapsed": {"repr": "0:00:04.115799", "seconds": 4.115799}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 25, "message": "jieba已加载", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.810586+08:00", "timestamp": 1756183626.810586}}}
{"text": "2025-08-26T12:47:06.812097+0800 | WARNING | app.services.news_sentiment_service | <module> | 37 | transformers或snownlp未安装，高级情感分析功能不可用\n", "record": {"elapsed": {"repr": "0:00:04.117310", "seconds": 4.11731}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "<module>", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 37, "message": "transformers或snownlp未安装，高级情感分析功能不可用", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.812097+08:00", "timestamp": 1756183626.812097}}}
{"text": "2025-08-26T12:47:06.813099+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.118312", "seconds": 4.118312}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.813099+08:00", "timestamp": 1756183626.813099}}}
{"text": "2025-08-26T12:47:06.814159+0800 | INFO | app.services.news_sentiment_service | _load_models | 181 | 情感分析模型加载完成\n", "record": {"elapsed": {"repr": "0:00:04.119372", "seconds": 4.119372}, "exception": null, "extra": {}, "file": {"name": "news_sentiment_service.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\services\\news_sentiment_service.py"}, "function": "_load_models", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 181, "message": "情感分析模型加载完成", "module": "news_sentiment_service", "name": "app.services.news_sentiment_service", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:06.814159+08:00", "timestamp": 1756183626.814159}}}
{"text": "2025-08-26T12:47:08.639666+0800 | INFO | app.main | lifespan | 28 | 🚀 JQData量化平台启动中...\n", "record": {"elapsed": {"repr": "0:00:05.944879", "seconds": 5.944879}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 28, "message": "🚀 JQData量化平台启动中...", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:08.639666+08:00", "timestamp": 1756183628.639666}}}
{"text": "2025-08-26T12:47:08.642893+0800 | INFO | app.core.database | init_db | 206 | 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.948106", "seconds": 5.948106}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "init_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 206, "message": "数据库初始化完成", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:08.642893+08:00", "timestamp": 1756183628.642893}}}
{"text": "2025-08-26T12:47:08.644122+0800 | INFO | app.main | lifespan | 33 | ✅ 数据库初始化完成\n", "record": {"elapsed": {"repr": "0:00:05.949335", "seconds": 5.949335}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 33, "message": "✅ 数据库初始化完成", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:08.644122+08:00", "timestamp": 1756183628.644122}}}
{"text": "2025-08-26T12:47:12.716507+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:00:10.021720", "seconds": 10.02172}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:12.716507+08:00", "timestamp": 1756183632.716507}}}
{"text": "2025-08-26T12:47:12.718504+0800 | WARNING | app.core.database | connect | 165 | Redis不可用，缓存功能将被禁用\n", "record": {"elapsed": {"repr": "0:00:10.023717", "seconds": 10.023717}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 165, "message": "Redis不可用，缓存功能将被禁用", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:12.718504+08:00", "timestamp": 1756183632.718504}}}
{"text": "2025-08-26T12:47:12.719501+0800 | INFO | app.main | lifespan | 37 | ✅ Redis连接成功\n", "record": {"elapsed": {"repr": "0:00:10.024714", "seconds": 10.024714}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 37, "message": "✅ Redis连接成功", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:12.719501+08:00", "timestamp": 1756183632.719501}}}
{"text": "2025-08-26T12:47:12.719501+0800 | INFO | app.main | lifespan | 39 | 🎉 JQData量化平台启动完成\n", "record": {"elapsed": {"repr": "0:00:10.024714", "seconds": 10.024714}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 39, "message": "🎉 JQData量化平台启动完成", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:12.719501+08:00", "timestamp": 1756183632.719501}}}
{"text": "2025-08-26T12:47:30.426450+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:00:27.731663", "seconds": 27.731663}, "exception": null, "extra": {"extra": {"request_id": "626243f9-1384-4bb2-aa29-5077cf363fc1", "method": "GET", "url": "http://127.0.0.1:8000/", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:30.426450+08:00", "timestamp": **********.42645}}}
{"text": "2025-08-26T12:47:30.428972+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:00:27.734185", "seconds": 27.734185}, "exception": null, "extra": {"extra": {"request_id": "626243f9-1384-4bb2-aa29-5077cf363fc1", "method": "GET", "url": "http://127.0.0.1:8000/", "status_code": 200, "process_time": 0.0025, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:30.428972+08:00", "timestamp": **********.428972}}}
{"text": "2025-08-26T12:47:50.810609+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:00:48.115822", "seconds": 48.115822}, "exception": null, "extra": {"extra": {"request_id": "2351af78-0690-4311-a323-0fba5760a630", "method": "GET", "url": "http://localhost:8000/health", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4768", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:50.810609+08:00", "timestamp": **********.810609}}}
{"text": "2025-08-26T12:47:54.891905+0800 | ERROR | app.core.database | connect | 164 | Redis连接失败: Error 22 connecting to localhost:6379. 22.\n", "record": {"elapsed": {"repr": "0:00:52.197118", "seconds": 52.197118}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 164, "message": "Redis连接失败: Error 22 connecting to localhost:6379. 22.", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:54.891905+08:00", "timestamp": **********.891905}}}
{"text": "2025-08-26T12:47:54.892421+0800 | WARNING | app.core.database | connect | 165 | Redis不可用，缓存功能将被禁用\n", "record": {"elapsed": {"repr": "0:00:52.197634", "seconds": 52.197634}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "connect", "level": {"icon": "⚠️", "name": "WARNING", "no": 30}, "line": 165, "message": "Redis不可用，缓存功能将被禁用", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:54.892421+08:00", "timestamp": **********.892421}}}
{"text": "2025-08-26T12:47:54.893924+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:00:52.199137", "seconds": 52.199137}, "exception": null, "extra": {"extra": {"request_id": "2351af78-0690-4311-a323-0fba5760a630", "method": "GET", "url": "http://localhost:8000/health", "status_code": 200, "process_time": 4.0833, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:47:54.893924+08:00", "timestamp": **********.893924}}}
{"text": "2025-08-26T12:48:08.520790+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:01:05.826003", "seconds": 65.826003}, "exception": null, "extra": {"extra": {"request_id": "34aa671e-dd65-45d5-82be-ccb5d3557097", "method": "GET", "url": "http://localhost:8000/docs", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:08.520790+08:00", "timestamp": 1756183688.52079}}}
{"text": "2025-08-26T12:48:08.566189+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:01:05.871402", "seconds": 65.871402}, "exception": null, "extra": {"extra": {"request_id": "34aa671e-dd65-45d5-82be-ccb5d3557097", "method": "GET", "url": "http://localhost:8000/docs", "status_code": 200, "process_time": 0.0454, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:08.566189+08:00", "timestamp": 1756183688.566189}}}
{"text": "2025-08-26T12:48:30.245307+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:01:27.550520", "seconds": 87.55052}, "exception": null, "extra": {"extra": {"request_id": "dbd52ef9-012a-41ff-b385-158281dbfb1d", "method": "GET", "url": "http://localhost:8000/docs", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:30.245307+08:00", "timestamp": 1756183710.245307}}}
{"text": "2025-08-26T12:48:30.265693+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:01:27.570906", "seconds": 87.570906}, "exception": null, "extra": {"extra": {"request_id": "dbd52ef9-012a-41ff-b385-158281dbfb1d", "method": "GET", "url": "http://localhost:8000/docs", "status_code": 200, "process_time": 0.0204, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:30.265693+08:00", "timestamp": 1756183710.265693}}}
{"text": "2025-08-26T12:48:32.597648+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:01:29.902861", "seconds": 89.902861}, "exception": null, "extra": {"extra": {"request_id": "5164cb25-e906-4e14-8174-5b93b1d40777", "method": "GET", "url": "http://localhost:8000/docs", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:32.597648+08:00", "timestamp": 1756183712.597648}}}
{"text": "2025-08-26T12:48:32.598645+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:01:29.903858", "seconds": 89.903858}, "exception": null, "extra": {"extra": {"request_id": "5164cb25-e906-4e14-8174-5b93b1d40777", "method": "GET", "url": "http://localhost:8000/docs", "status_code": 200, "process_time": 0.001, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:48:32.598645+08:00", "timestamp": 1756183712.598645}}}
{"text": "2025-08-26T12:49:11.753713+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:02:09.058926", "seconds": 129.058926}, "exception": null, "extra": {"extra": {"request_id": "13b53a4b-a756-4524-ad68-eb94ceca200d", "method": "GET", "url": "http://localhost:8000/docs", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:49:11.753713+08:00", "timestamp": 1756183751.753713}}}
{"text": "2025-08-26T12:49:11.766196+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:02:09.071409", "seconds": 129.071409}, "exception": null, "extra": {"extra": {"request_id": "13b53a4b-a756-4524-ad68-eb94ceca200d", "method": "GET", "url": "http://localhost:8000/docs", "status_code": 200, "process_time": 0.0125, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:49:11.766196+08:00", "timestamp": 1756183751.766196}}}
{"text": "2025-08-26T12:53:10.505596+0800 | INFO | app.middleware.logging | dispatch | 36 | 请求开始\n", "record": {"elapsed": {"repr": "0:06:07.810809", "seconds": 367.810809}, "exception": null, "extra": {"extra": {"request_id": "37ecc0cb-5796-4758-816b-8c8efca8271c", "method": "GET", "url": "http://localhost:8000/docs", "client_ip": "unknown", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "event": "request_start"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 36, "message": "请求开始", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:53:10.505596+08:00", "timestamp": 1756183990.505596}}}
{"text": "2025-08-26T12:53:10.511920+0800 | INFO | app.middleware.logging | dispatch | 56 | 请求完成\n", "record": {"elapsed": {"repr": "0:06:07.817133", "seconds": 367.817133}, "exception": null, "extra": {"extra": {"request_id": "37ecc0cb-5796-4758-816b-8c8efca8271c", "method": "GET", "url": "http://localhost:8000/docs", "status_code": 200, "process_time": 0.0063, "client_ip": "unknown", "event": "request_end"}}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\middleware\\logging.py"}, "function": "dispatch", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 56, "message": "请求完成", "module": "logging", "name": "app.middleware.logging", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 12:53:10.511920+08:00", "timestamp": 1756183990.51192}}}
{"text": "2025-08-26T13:03:43.211313+0800 | INFO | app.main | lifespan | 48 | 🛑 JQData量化平台关闭中...\n", "record": {"elapsed": {"repr": "0:16:40.516526", "seconds": 1000.516526}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 48, "message": "🛑 JQData量化平台关闭中...", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 13:03:43.211313+08:00", "timestamp": 1756184623.211313}}}
{"text": "2025-08-26T13:03:43.212924+0800 | INFO | app.core.database | close_db | 218 | 数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:16:40.518137", "seconds": 1000.518137}, "exception": null, "extra": {}, "file": {"name": "database.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py"}, "function": "close_db", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 218, "message": "数据库连接已关闭", "module": "database", "name": "app.core.database", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 13:03:43.212924+08:00", "timestamp": 1756184623.212924}}}
{"text": "2025-08-26T13:03:43.213918+0800 | INFO | app.main | lifespan | 53 | ✅ 数据库连接已关闭\n", "record": {"elapsed": {"repr": "0:16:40.519131", "seconds": 1000.519131}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 53, "message": "✅ 数据库连接已关闭", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 13:03:43.213918+08:00", "timestamp": 1756184623.213918}}}
{"text": "2025-08-26T13:03:43.213918+0800 | INFO | app.main | lifespan | 57 | ✅ Redis连接已断开\n", "record": {"elapsed": {"repr": "0:16:40.519131", "seconds": 1000.519131}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 57, "message": "✅ Redis连接已断开", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 13:03:43.213918+08:00", "timestamp": 1756184623.213918}}}
{"text": "2025-08-26T13:03:43.213918+0800 | INFO | app.main | lifespan | 59 | 👋 JQData量化平台已关闭\n", "record": {"elapsed": {"repr": "0:16:40.519131", "seconds": 1000.519131}, "exception": null, "extra": {}, "file": {"name": "main.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\main.py"}, "function": "lifespan", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 59, "message": "👋 JQData量化平台已关闭", "module": "main", "name": "app.main", "process": {"id": 1676, "name": "MainProcess"}, "thread": {"id": 25064, "name": "MainThread"}, "time": {"repr": "2025-08-26 13:03:43.213918+08:00", "timestamp": 1756184623.213918}}}
{"text": "2025-08-26T15:00:00.752411+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.014510", "seconds": 0.01451}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 27124, "name": "MainProcess"}, "thread": {"id": 6536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:00:00.752411+08:00", "timestamp": 1756191600.752411}}}
{"text": "2025-08-26T15:00:14.160691+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.029366", "seconds": 0.029366}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 20928, "name": "MainProcess"}, "thread": {"id": 24164, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:00:14.160691+08:00", "timestamp": 1756191614.160691}}}
{"text": "2025-08-26T15:00:14.169629+0800 | INFO | __main__ | init_database | 120 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.038304", "seconds": 0.038304}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 120, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 20928, "name": "MainProcess"}, "thread": {"id": 24164, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:00:14.169629+08:00", "timestamp": 1756191614.169629}}}
{"text": "2025-08-26T15:00:14.240653+0800 | ERROR | __main__ | create_database_if_not_exists | 51 | 创建数据库失败: 'Settings' object has no attribute 'DATABASE_NAME'\n", "record": {"elapsed": {"repr": "0:00:00.109328", "seconds": 0.109328}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 51, "message": "创建数据库失败: 'Settings' object has no attribute 'DATABASE_NAME'", "module": "init_db", "name": "__main__", "process": {"id": 20928, "name": "MainProcess"}, "thread": {"id": 24164, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:00:14.240653+08:00", "timestamp": 1756191614.240653}}}
{"text": "2025-08-26T15:00:14.242578+0800 | ERROR | __main__ | init_database | 134 | ❌ 数据库初始化失败: 'Settings' object has no attribute 'DATABASE_NAME'\n", "record": {"elapsed": {"repr": "0:00:00.111253", "seconds": 0.111253}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 134, "message": "❌ 数据库初始化失败: 'Settings' object has no attribute 'DATABASE_NAME'", "module": "init_db", "name": "__main__", "process": {"id": 20928, "name": "MainProcess"}, "thread": {"id": 24164, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:00:14.242578+08:00", "timestamp": 1756191614.242578}}}
{"text": "2025-08-26T15:01:54.232650+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.009940", "seconds": 0.00994}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:54.232650+08:00", "timestamp": 1756191714.23265}}}
{"text": "2025-08-26T15:01:54.237377+0800 | INFO | __main__ | init_database | 136 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.014667", "seconds": 0.014667}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 136, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:54.237377+08:00", "timestamp": 1756191714.237377}}}
{"text": "2025-08-26T15:01:54.237377+0800 | INFO | __main__ | create_database_if_not_exists | 30 | 使用 SQLite 数据库，无需创建数据库\n", "record": {"elapsed": {"repr": "0:00:00.014667", "seconds": 0.014667}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "使用 SQLite 数据库，无需创建数据库", "module": "init_db", "name": "__main__", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:54.237377+08:00", "timestamp": 1756191714.237377}}}
{"text": "2025-08-26T15:01:54.239889+0800 | INFO | __main__ | run_migrations | 79 | 开始运行数据库迁移...\n", "record": {"elapsed": {"repr": "0:00:00.017179", "seconds": 0.017179}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "run_migrations", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 79, "message": "开始运行数据库迁移...", "module": "init_db", "name": "__main__", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:54.239889+08:00", "timestamp": 1756191714.239889}}}
{"text": "2025-08-26T15:01:55.235146+0800 | ERROR | __main__ | run_migrations | 84 | 数据库迁移失败: asyncio.run() cannot be called from a running event loop\n", "record": {"elapsed": {"repr": "0:00:01.012436", "seconds": 1.012436}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "run_migrations", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 84, "message": "数据库迁移失败: asyncio.run() cannot be called from a running event loop", "module": "init_db", "name": "__main__", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:55.235146+08:00", "timestamp": 1756191715.235146}}}
{"text": "2025-08-26T15:01:55.239753+0800 | ERROR | __main__ | init_database | 150 | ❌ 数据库初始化失败: asyncio.run() cannot be called from a running event loop\n", "record": {"elapsed": {"repr": "0:00:01.017043", "seconds": 1.017043}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 150, "message": "❌ 数据库初始化失败: asyncio.run() cannot be called from a running event loop", "module": "init_db", "name": "__main__", "process": {"id": 26744, "name": "MainProcess"}, "thread": {"id": 24632, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:01:55.239753+08:00", "timestamp": 1756191715.239753}}}
{"text": "2025-08-26T15:03:12.909498+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011300", "seconds": 0.0113}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:12.909498+08:00", "timestamp": 1756191792.909498}}}
{"text": "2025-08-26T15:03:12.913465+0800 | INFO | __main__ | init_database | 138 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.015267", "seconds": 0.015267}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 138, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:12.913465+08:00", "timestamp": 1756191792.913465}}}
{"text": "2025-08-26T15:03:12.915600+0800 | INFO | __main__ | create_database_if_not_exists | 30 | 使用 SQLite 数据库，无需创建数据库\n", "record": {"elapsed": {"repr": "0:00:00.017402", "seconds": 0.017402}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "使用 SQLite 数据库，无需创建数据库", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:12.915600+08:00", "timestamp": 1756191792.9156}}}
{"text": "2025-08-26T15:03:13.724243+0800 | INFO | __main__ | create_tables | 77 | 开始创建数据库表...\n", "record": {"elapsed": {"repr": "0:00:00.826045", "seconds": 0.826045}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 77, "message": "开始创建数据库表...", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:13.724243+08:00", "timestamp": 1756191793.724243}}}
{"text": "2025-08-26T15:03:15.106657+0800 | INFO | __main__ | create_tables | 83 | 数据库表创建完成\n", "record": {"elapsed": {"repr": "0:00:02.208459", "seconds": 2.208459}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 83, "message": "数据库表创建完成", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:15.106657+08:00", "timestamp": 1756191795.106657}}}
{"text": "2025-08-26T15:03:15.107836+0800 | ERROR | __main__ | create_initial_data | 131 | 创建初始数据失败: cannot import name 'get_async_session' from 'app.core.database' (C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py)\n", "record": {"elapsed": {"repr": "0:00:02.209638", "seconds": 2.209638}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_initial_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 131, "message": "创建初始数据失败: cannot import name 'get_async_session' from 'app.core.database' (C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py)", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:15.107836+08:00", "timestamp": 1756191795.107836}}}
{"text": "2025-08-26T15:03:15.107836+0800 | ERROR | __main__ | init_database | 152 | ❌ 数据库初始化失败: cannot import name 'get_async_session' from 'app.core.database' (C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py)\n", "record": {"elapsed": {"repr": "0:00:02.209638", "seconds": 2.209638}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 152, "message": "❌ 数据库初始化失败: cannot import name 'get_async_session' from 'app.core.database' (C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\database.py)", "module": "init_db", "name": "__main__", "process": {"id": 26160, "name": "MainProcess"}, "thread": {"id": 4976, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:03:15.107836+08:00", "timestamp": 1756191795.107836}}}
{"text": "2025-08-26T15:04:13.654342+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.024568", "seconds": 0.024568}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:13.654342+08:00", "timestamp": 1756191853.654342}}}
{"text": "2025-08-26T15:04:13.660339+0800 | INFO | __main__ | init_database | 138 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.030565", "seconds": 0.030565}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 138, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:13.660339+08:00", "timestamp": 1756191853.660339}}}
{"text": "2025-08-26T15:04:13.660339+0800 | INFO | __main__ | create_database_if_not_exists | 30 | 使用 SQLite 数据库，无需创建数据库\n", "record": {"elapsed": {"repr": "0:00:00.030565", "seconds": 0.030565}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "使用 SQLite 数据库，无需创建数据库", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:13.660339+08:00", "timestamp": 1756191853.660339}}}
{"text": "2025-08-26T15:04:14.642461+0800 | INFO | __main__ | create_tables | 77 | 开始创建数据库表...\n", "record": {"elapsed": {"repr": "0:00:01.012687", "seconds": 1.012687}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 77, "message": "开始创建数据库表...", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:14.642461+08:00", "timestamp": 1756191854.642461}}}
{"text": "2025-08-26T15:04:14.676910+0800 | INFO | __main__ | create_tables | 83 | 数据库表创建完成\n", "record": {"elapsed": {"repr": "0:00:01.047136", "seconds": 1.047136}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 83, "message": "数据库表创建完成", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:14.676910+08:00", "timestamp": 1756191854.67691}}}
{"text": "2025-08-26T15:04:14.716146+0800 | ERROR | __main__ | create_initial_data | 131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:01.086372", "seconds": 1.086372}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_initial_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 131, "message": "创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:14.716146+08:00", "timestamp": 1756191854.716146}}}
{"text": "2025-08-26T15:04:14.717166+0800 | ERROR | __main__ | init_database | 152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:01.087392", "seconds": 1.087392}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 152, "message": "❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'BacktestTask' failed to locate a name ('BacktestTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 26776, "name": "MainProcess"}, "thread": {"id": 27512, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:04:14.717166+08:00", "timestamp": 1756191854.717166}}}
{"text": "2025-08-26T15:05:03.348203+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.013127", "seconds": 0.013127}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:03.348203+08:00", "timestamp": 1756191903.348203}}}
{"text": "2025-08-26T15:05:03.351494+0800 | INFO | __main__ | init_database | 138 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.016418", "seconds": 0.016418}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 138, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:03.351494+08:00", "timestamp": 1756191903.351494}}}
{"text": "2025-08-26T15:05:03.351494+0800 | INFO | __main__ | create_database_if_not_exists | 30 | 使用 SQLite 数据库，无需创建数据库\n", "record": {"elapsed": {"repr": "0:00:00.016418", "seconds": 0.016418}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "使用 SQLite 数据库，无需创建数据库", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:03.351494+08:00", "timestamp": 1756191903.351494}}}
{"text": "2025-08-26T15:05:04.090705+0800 | INFO | __main__ | create_tables | 77 | 开始创建数据库表...\n", "record": {"elapsed": {"repr": "0:00:00.755629", "seconds": 0.755629}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 77, "message": "开始创建数据库表...", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:04.090705+08:00", "timestamp": 1756191904.090705}}}
{"text": "2025-08-26T15:05:04.127579+0800 | INFO | __main__ | create_tables | 83 | 数据库表创建完成\n", "record": {"elapsed": {"repr": "0:00:00.792503", "seconds": 0.792503}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 83, "message": "数据库表创建完成", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:04.127579+08:00", "timestamp": 1756191904.127579}}}
{"text": "2025-08-26T15:05:04.152381+0800 | ERROR | __main__ | create_initial_data | 131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:00.817305", "seconds": 0.817305}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_initial_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 131, "message": "创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:04.152381+08:00", "timestamp": 1756191904.152381}}}
{"text": "2025-08-26T15:05:04.152381+0800 | ERROR | __main__ | init_database | 152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:00.817305", "seconds": 0.817305}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 152, "message": "❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ReportTask' failed to locate a name ('ReportTask'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 6196, "name": "MainProcess"}, "thread": {"id": 11140, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:04.152381+08:00", "timestamp": 1756191904.152381}}}
{"text": "2025-08-26T15:05:56.399200+0800 | INFO | app.core.logging | setup_logging | 93 | 日志系统初始化完成 - 级别: INFO\n", "record": {"elapsed": {"repr": "0:00:00.011284", "seconds": 0.011284}, "exception": null, "extra": {}, "file": {"name": "logging.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\app\\core\\logging.py"}, "function": "setup_logging", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 93, "message": "日志系统初始化完成 - 级别: INFO", "module": "logging", "name": "app.core.logging", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:56.399200+08:00", "timestamp": 1756191956.3992}}}
{"text": "2025-08-26T15:05:56.403099+0800 | INFO | __main__ | init_database | 138 | 🚀 开始初始化数据库...\n", "record": {"elapsed": {"repr": "0:00:00.015183", "seconds": 0.015183}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 138, "message": "🚀 开始初始化数据库...", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:56.403099+08:00", "timestamp": 1756191956.403099}}}
{"text": "2025-08-26T15:05:56.405673+0800 | INFO | __main__ | create_database_if_not_exists | 30 | 使用 SQLite 数据库，无需创建数据库\n", "record": {"elapsed": {"repr": "0:00:00.017757", "seconds": 0.017757}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_database_if_not_exists", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 30, "message": "使用 SQLite 数据库，无需创建数据库", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:56.405673+08:00", "timestamp": 1756191956.405673}}}
{"text": "2025-08-26T15:05:57.150697+0800 | INFO | __main__ | create_tables | 77 | 开始创建数据库表...\n", "record": {"elapsed": {"repr": "0:00:00.762781", "seconds": 0.762781}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 77, "message": "开始创建数据库表...", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:57.150697+08:00", "timestamp": 1756191957.150697}}}
{"text": "2025-08-26T15:05:57.184467+0800 | INFO | __main__ | create_tables | 83 | 数据库表创建完成\n", "record": {"elapsed": {"repr": "0:00:00.796551", "seconds": 0.796551}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_tables", "level": {"icon": "ℹ️", "name": "INFO", "no": 20}, "line": 83, "message": "数据库表创建完成", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:57.184467+08:00", "timestamp": 1756191957.184467}}}
{"text": "2025-08-26T15:05:57.211972+0800 | ERROR | __main__ | create_initial_data | 131 | 创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:00.824056", "seconds": 0.824056}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "create_initial_data", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 131, "message": "创建初始数据失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:57.211972+08:00", "timestamp": 1756191957.211972}}}
{"text": "2025-08-26T15:05:57.213489+0800 | ERROR | __main__ | init_database | 152 | ❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.\n", "record": {"elapsed": {"repr": "0:00:00.825573", "seconds": 0.825573}, "exception": null, "extra": {}, "file": {"name": "init_db.py", "path": "C:\\Users\\<USER>\\Desktop\\JQData\\backend\\scripts\\init_db.py"}, "function": "init_database", "level": {"icon": "❌", "name": "ERROR", "no": 40}, "line": 152, "message": "❌ 数据库初始化失败: When initializing mapper Mapper[User(users)], expression 'ExplainabilityAnalysis' failed to locate a name ('ExplainabilityAnalysis'). If this is a class name, consider adding this relationship() to the <class 'app.models.user.User'> class after both dependent classes have been defined.", "module": "init_db", "name": "__main__", "process": {"id": 12404, "name": "MainProcess"}, "thread": {"id": 20536, "name": "MainThread"}, "time": {"repr": "2025-08-26 15:05:57.213489+08:00", "timestamp": 1756191957.213489}}}
