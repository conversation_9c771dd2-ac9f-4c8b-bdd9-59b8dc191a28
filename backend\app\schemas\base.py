"""
基础数据模式

定义通用的请求和响应模式
"""

from datetime import datetime
from typing import Any, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

# 泛型类型变量
T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模式"""
    
    code: int = Field(..., description="响应状态码")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")
    timestamp: Optional[int] = Field(None, description="时间戳")
    
    class Config:
        json_encoders = {
            datetime: lambda v: int(v.timestamp() * 1000) if v else None
        }


class PaginationInfo(BaseModel):
    """分页信息"""
    
    page: int = Field(..., ge=1, description="当前页码")
    page_size: int = Field(..., ge=1, le=100, description="每页大小")
    total: int = Field(..., ge=0, description="总记录数")
    total_pages: int = Field(..., ge=0, description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class PaginatedResponse(BaseModel, Generic[T]):
    """分页响应模式"""
    
    items: List[T] = Field(..., description="数据列表")
    pagination: PaginationInfo = Field(..., description="分页信息")


class ErrorDetail(BaseModel):
    """错误详情"""
    
    field: Optional[str] = Field(None, description="错误字段")
    message: str = Field(..., description="错误消息")
    code: Optional[str] = Field(None, description="错误代码")


class ValidationErrorResponse(BaseModel):
    """验证错误响应"""
    
    code: int = Field(422, description="状态码")
    message: str = Field("请求参数验证失败", description="错误消息")
    errors: List[ErrorDetail] = Field(..., description="错误详情列表")


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    
    status: str = Field(..., description="服务状态")
    database: str = Field(..., description="数据库状态")
    redis: str = Field(..., description="Redis状态")
    timestamp: datetime = Field(..., description="检查时间")


class AppInfoResponse(BaseModel):
    """应用信息响应"""
    
    name: str = Field(..., description="应用名称")
    version: str = Field(..., description="应用版本")
    environment: str = Field(..., description="运行环境")
    debug: bool = Field(..., description="是否调试模式")
    features: dict = Field(..., description="功能开关")


class SortOrder(BaseModel):
    """排序配置"""
    
    field: str = Field(..., description="排序字段")
    order: str = Field(..., regex="^(asc|desc)$", description="排序方向")


class FilterCondition(BaseModel):
    """过滤条件"""
    
    field: str = Field(..., description="过滤字段")
    operator: str = Field(
        ..., 
        regex="^(eq|ne|gt|gte|lt|lte|in|like)$", 
        description="操作符"
    )
    value: Any = Field(..., description="过滤值")


class QueryParams(BaseModel):
    """查询参数"""
    
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页大小")
    sort: Optional[List[SortOrder]] = Field(None, description="排序配置")
    filters: Optional[List[FilterCondition]] = Field(None, description="过滤条件")
    search: Optional[str] = Field(None, description="搜索关键词")


class BulkOperationRequest(BaseModel):
    """批量操作请求"""
    
    ids: List[int] = Field(..., min_items=1, description="ID列表")
    action: str = Field(..., description="操作类型")
    params: Optional[dict] = Field(None, description="操作参数")


class BulkOperationResponse(BaseModel):
    """批量操作响应"""
    
    success_count: int = Field(..., ge=0, description="成功数量")
    failed_count: int = Field(..., ge=0, description="失败数量")
    errors: Optional[List[ErrorDetail]] = Field(None, description="错误详情")


class FileUploadResponse(BaseModel):
    """文件上传响应"""
    
    filename: str = Field(..., description="文件名")
    original_filename: str = Field(..., description="原始文件名")
    size: int = Field(..., ge=0, description="文件大小")
    content_type: str = Field(..., description="文件类型")
    url: str = Field(..., description="文件URL")
    upload_time: datetime = Field(..., description="上传时间")


class ExportRequest(BaseModel):
    """导出请求"""
    
    format: str = Field(..., regex="^(csv|xlsx|json)$", description="导出格式")
    filters: Optional[List[FilterCondition]] = Field(None, description="过滤条件")
    fields: Optional[List[str]] = Field(None, description="导出字段")


class ExportResponse(BaseModel):
    """导出响应"""
    
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    download_url: Optional[str] = Field(None, description="下载链接")
    created_at: datetime = Field(..., description="创建时间")


class TaskStatus(BaseModel):
    """任务状态"""
    
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    progress: int = Field(..., ge=0, le=100, description="进度百分比")
    message: Optional[str] = Field(None, description="状态消息")
    result: Optional[Any] = Field(None, description="任务结果")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class CacheStats(BaseModel):
    """缓存统计"""
    
    total_keys: int = Field(..., ge=0, description="总键数")
    memory_used: str = Field(..., description="内存使用")
    connected_clients: int = Field(..., ge=0, description="连接客户端数")
    keyspace_hits: int = Field(..., ge=0, description="命中次数")
    keyspace_misses: int = Field(..., ge=0, description="未命中次数")
    hit_rate: float = Field(..., ge=0, le=100, description="命中率")


class SystemStats(BaseModel):
    """系统统计"""
    
    cpu_usage: float = Field(..., ge=0, le=100, description="CPU使用率")
    memory_usage: float = Field(..., ge=0, le=100, description="内存使用率")
    disk_usage: float = Field(..., ge=0, le=100, description="磁盘使用率")
    active_users: int = Field(..., ge=0, description="活跃用户数")
    api_calls_today: int = Field(..., ge=0, description="今日API调用数")
    cache_stats: CacheStats = Field(..., description="缓存统计")


class NotificationRequest(BaseModel):
    """通知请求"""
    
    title: str = Field(..., max_length=100, description="通知标题")
    message: str = Field(..., max_length=500, description="通知内容")
    type: str = Field(
        "info", 
        regex="^(info|success|warning|error)$", 
        description="通知类型"
    )
    user_ids: Optional[List[int]] = Field(None, description="目标用户ID列表")
    send_email: bool = Field(False, description="是否发送邮件")
    send_sms: bool = Field(False, description="是否发送短信")


class NotificationResponse(BaseModel):
    """通知响应"""
    
    id: str = Field(..., description="通知ID")
    title: str = Field(..., description="通知标题")
    message: str = Field(..., description="通知内容")
    type: str = Field(..., description="通知类型")
    read: bool = Field(..., description="是否已读")
    created_at: datetime = Field(..., description="创建时间")
    actions: Optional[List[dict]] = Field(None, description="操作按钮")
