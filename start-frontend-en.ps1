# ============================================================================
# Smart Quantitative Trading Platform - Frontend Service Startup Script
# ============================================================================

Write-Host "Starting Smart Quantitative Trading Platform Frontend..." -ForegroundColor Green

# Check Node.js environment
Write-Host "Checking Node.js environment..." -ForegroundColor Yellow
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: Node.js not found, please install Node.js 18+" -ForegroundColor Red
    Write-Host "Download: https://nodejs.org/" -ForegroundColor Cyan
    exit 1
}

$nodeVersion = node --version
Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green

# Check npm
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "ERROR: npm not found" -ForegroundColor Red
    exit 1
}

$npmVersion = npm --version
Write-Host "npm version: $npmVersion" -ForegroundColor Green

# Enter frontend directory
Set-Location frontend

# Check package.json
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json file not found" -ForegroundColor Red
    exit 1
}

# Check node_modules
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes, please wait..." -ForegroundColor Yellow
    
    try {
        npm install
        Write-Host "Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "npm install failed, trying yarn..." -ForegroundColor Yellow
        if (Get-Command yarn -ErrorAction SilentlyContinue) {
            yarn install
        } else {
            Write-Host "Dependency installation failed, please run manually: npm install" -ForegroundColor Red
            exit 1
        }
    }
} else {
    Write-Host "Dependencies already installed" -ForegroundColor Green
}

# Check environment file
if (-not (Test-Path ".env.local")) {
    Write-Host "Creating environment variables file..." -ForegroundColor Yellow
    @"
# Smart Quantitative Trading Platform - Frontend Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=Smart Quantitative Trading Platform
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development
"@ | Out-File -FilePath ".env.local" -Encoding UTF8
    Write-Host "Environment variables file created successfully" -ForegroundColor Green
}

# Start development server
Write-Host "Starting Next.js development server..." -ForegroundColor Green
Write-Host "Service URL: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop service" -ForegroundColor Yellow
Write-Host "Make sure backend service is running at http://localhost:8000" -ForegroundColor Yellow
Write-Host ""

try {
    npm run dev
} catch {
    Write-Host "Startup failed: $_" -ForegroundColor Red
    Write-Host "Please check if dependencies are correctly installed" -ForegroundColor Yellow
    Write-Host "Try deleting node_modules folder and run again" -ForegroundColor Yellow
    exit 1
}

Write-Host "Frontend service stopped" -ForegroundColor Yellow
