/**
 * 技术指标计算工具
 * 
 * 提供常用技术指标的计算函数，包括MA、MACD、RSI、BOLL等
 */

export interface PriceData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface MAData {
  date: string;
  value: number;
}

export interface MACDData {
  date: string;
  dif: number;
  dea: number;
  macd: number;
}

export interface RSIData {
  date: string;
  value: number;
}

export interface BOLLData {
  date: string;
  upper: number;
  middle: number;
  lower: number;
}

export interface KDJData {
  date: string;
  k: number;
  d: number;
  j: number;
}

/**
 * 计算简单移动平均线 (SMA)
 */
export function calculateSMA(data: PriceData[], period: number): MAData[] {
  if (data.length < period) return [];
  
  const result: MAData[] = [];
  
  for (let i = period - 1; i < data.length; i++) {
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j].close;
    }
    
    result.push({
      date: data[i].date,
      value: sum / period
    });
  }
  
  return result;
}

/**
 * 计算指数移动平均线 (EMA)
 */
export function calculateEMA(data: PriceData[], period: number): MAData[] {
  if (data.length < period) return [];
  
  const result: MAData[] = [];
  const multiplier = 2 / (period + 1);
  
  // 第一个EMA值使用SMA
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += data[i].close;
  }
  let ema = sum / period;
  
  result.push({
    date: data[period - 1].date,
    value: ema
  });
  
  // 后续EMA值
  for (let i = period; i < data.length; i++) {
    ema = (data[i].close - ema) * multiplier + ema;
    result.push({
      date: data[i].date,
      value: ema
    });
  }
  
  return result;
}

/**
 * 计算MACD指标
 */
export function calculateMACD(
  data: PriceData[], 
  fastPeriod: number = 12, 
  slowPeriod: number = 26, 
  signalPeriod: number = 9
): MACDData[] {
  if (data.length < slowPeriod) return [];
  
  // 计算快线和慢线EMA
  const fastEMA = calculateEMA(data, fastPeriod);
  const slowEMA = calculateEMA(data, slowPeriod);
  
  // 计算DIF线
  const difData: MAData[] = [];
  const startIndex = slowPeriod - fastPeriod;
  
  for (let i = 0; i < slowEMA.length; i++) {
    const fastValue = fastEMA[i + startIndex]?.value || 0;
    const slowValue = slowEMA[i].value;
    
    difData.push({
      date: slowEMA[i].date,
      value: fastValue - slowValue
    });
  }
  
  // 计算DEA线 (DIF的EMA)
  const deaData = calculateEMA(
    difData.map(d => ({ ...data[0], close: d.value, date: d.date })), 
    signalPeriod
  );
  
  // 计算MACD柱状图
  const result: MACDData[] = [];
  for (let i = 0; i < deaData.length; i++) {
    const dif = difData[i + (difData.length - deaData.length)].value;
    const dea = deaData[i].value;
    
    result.push({
      date: deaData[i].date,
      dif,
      dea,
      macd: (dif - dea) * 2
    });
  }
  
  return result;
}

/**
 * 计算RSI指标
 */
export function calculateRSI(data: PriceData[], period: number = 14): RSIData[] {
  if (data.length < period + 1) return [];
  
  const result: RSIData[] = [];
  const gains: number[] = [];
  const losses: number[] = [];
  
  // 计算价格变化
  for (let i = 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close;
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }
  
  // 计算初始平均收益和损失
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;
  
  // 计算第一个RSI值
  let rs = avgGain / (avgLoss || 1);
  let rsi = 100 - (100 / (1 + rs));
  
  result.push({
    date: data[period].date,
    value: rsi
  });
  
  // 计算后续RSI值
  for (let i = period; i < gains.length; i++) {
    avgGain = (avgGain * (period - 1) + gains[i]) / period;
    avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
    
    rs = avgGain / (avgLoss || 1);
    rsi = 100 - (100 / (1 + rs));
    
    result.push({
      date: data[i + 1].date,
      value: rsi
    });
  }
  
  return result;
}

/**
 * 计算布林带指标
 */
export function calculateBOLL(
  data: PriceData[], 
  period: number = 20, 
  multiplier: number = 2
): BOLLData[] {
  if (data.length < period) return [];
  
  const result: BOLLData[] = [];
  const sma = calculateSMA(data, period);
  
  for (let i = 0; i < sma.length; i++) {
    const dataIndex = i + period - 1;
    const middle = sma[i].value;
    
    // 计算标准差
    let sum = 0;
    for (let j = 0; j < period; j++) {
      const diff = data[dataIndex - j].close - middle;
      sum += diff * diff;
    }
    const stdDev = Math.sqrt(sum / period);
    
    result.push({
      date: data[dataIndex].date,
      upper: middle + multiplier * stdDev,
      middle,
      lower: middle - multiplier * stdDev
    });
  }
  
  return result;
}

/**
 * 计算KDJ指标
 */
export function calculateKDJ(
  data: PriceData[], 
  period: number = 9, 
  m1: number = 3, 
  m2: number = 3
): KDJData[] {
  if (data.length < period) return [];
  
  const result: KDJData[] = [];
  let k = 50; // K值初始值
  let d = 50; // D值初始值
  
  for (let i = period - 1; i < data.length; i++) {
    // 计算最高价和最低价
    let highest = data[i].high;
    let lowest = data[i].low;
    
    for (let j = 1; j < period; j++) {
      if (i - j >= 0) {
        highest = Math.max(highest, data[i - j].high);
        lowest = Math.min(lowest, data[i - j].low);
      }
    }
    
    // 计算RSV
    const rsv = highest === lowest ? 0 : 
      ((data[i].close - lowest) / (highest - lowest)) * 100;
    
    // 计算K、D、J值
    k = (rsv + (m1 - 1) * k) / m1;
    d = (k + (m2 - 1) * d) / m2;
    const j = 3 * k - 2 * d;
    
    result.push({
      date: data[i].date,
      k,
      d,
      j
    });
  }
  
  return result;
}

/**
 * 计算成交量移动平均线
 */
export function calculateVolumeMA(data: PriceData[], period: number): MAData[] {
  if (data.length < period) return [];
  
  const result: MAData[] = [];
  
  for (let i = period - 1; i < data.length; i++) {
    let sum = 0;
    for (let j = 0; j < period; j++) {
      sum += data[i - j].volume;
    }
    
    result.push({
      date: data[i].date,
      value: sum / period
    });
  }
  
  return result;
}

/**
 * 计算威廉指标 (WR)
 */
export function calculateWR(data: PriceData[], period: number = 14): RSIData[] {
  if (data.length < period) return [];
  
  const result: RSIData[] = [];
  
  for (let i = period - 1; i < data.length; i++) {
    let highest = data[i].high;
    let lowest = data[i].low;
    
    for (let j = 1; j < period; j++) {
      if (i - j >= 0) {
        highest = Math.max(highest, data[i - j].high);
        lowest = Math.min(lowest, data[i - j].low);
      }
    }
    
    const wr = highest === lowest ? 0 : 
      ((highest - data[i].close) / (highest - lowest)) * -100;
    
    result.push({
      date: data[i].date,
      value: wr
    });
  }
  
  return result;
}

/**
 * 批量计算所有技术指标
 */
export function calculateAllIndicators(data: PriceData[]) {
  return {
    ma5: calculateSMA(data, 5),
    ma10: calculateSMA(data, 10),
    ma20: calculateSMA(data, 20),
    ma60: calculateSMA(data, 60),
    ema12: calculateEMA(data, 12),
    ema26: calculateEMA(data, 26),
    macd: calculateMACD(data),
    rsi: calculateRSI(data),
    boll: calculateBOLL(data),
    kdj: calculateKDJ(data),
    wr: calculateWR(data),
    volumeMA5: calculateVolumeMA(data, 5),
    volumeMA10: calculateVolumeMA(data, 10)
  };
}
