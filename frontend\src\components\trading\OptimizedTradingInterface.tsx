'use client';

/**
 * 优化版交易界面组件
 * 
 * 提供专业的交易操作界面，包括：
 * - 实时行情展示
 * - 交易下单功能
 * - 持仓管理
 * - 订单管理
 * - K线图表
 * - 技术指标
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Input,
  Select,
  InputNumber,
  Form,
  Space,
  Tag,
  Tabs,
  Divider,
  Alert,
  Badge,
  Tooltip,
  Modal,
  Drawer,
  Switch,
  Slider,
  Progress,
  Statistic,
  Typography,
  List,
  Avatar,
  Popconfirm,
  message,
  Grid,
  Affix,
  Segmented,
  FloatButton,
  Spin
} from 'antd';
import {
  LineChartOutlined,
  BarChartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
  SellOutlined,
  EyeOutlined,
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  CompressOutlined,
  BellOutlined,
  FilterOutlined,
  ExportOutlined,
  ImportOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  CalendarOutlined,
  UserOutlined,
  TeamOutlined,
  BankOutlined,
  FundOutlined
} from '@ant-design/icons';
import { Line, Column, Candlestick, Area } from '@ant-design/plots';
import { motion, AnimatePresence } from 'framer-motion';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { useBreakpoint } = Grid;
const { Search } = Input;

interface StockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  turnover: number;
  high: number;
  low: number;
  open: number;
  prevClose: number;
  bid: number;
  ask: number;
  bidSize: number;
  askSize: number;
}

interface Position {
  symbol: string;
  name: string;
  quantity: number;
  availableQuantity: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  pnl: number;
  pnlPercent: number;
  weight: number;
}

interface Order {
  id: string;
  symbol: string;
  name: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop';
  quantity: number;
  price?: number;
  filledQuantity: number;
  status: 'pending' | 'partial' | 'filled' | 'cancelled';
  createTime: string;
  updateTime: string;
}

export const OptimizedTradingInterface: React.FC = () => {
  const screens = useBreakpoint();
  const [loading, setLoading] = useState(false);
  const [selectedStock, setSelectedStock] = useState<StockQuote | null>(null);
  const [watchlist, setWatchlist] = useState<StockQuote[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [orderForm] = Form.useForm();
  const [orderModalVisible, setOrderModalVisible] = useState(false);
  const [chartFullscreen, setChartFullscreen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5);
  const chartRef = useRef<HTMLDivElement>(null);

  // 模拟数据初始化
  useEffect(() => {
    const initData = () => {
      // 模拟自选股数据
      setWatchlist([
        {
          symbol: '000001',
          name: '平安银行',
          price: 12.45,
          change: 0.23,
          changePercent: 1.88,
          volume: 125000000,
          turnover: 1556250000,
          high: 12.67,
          low: 12.20,
          open: 12.22,
          prevClose: 12.22,
          bid: 12.44,
          ask: 12.45,
          bidSize: 15600,
          askSize: 12800
        },
        {
          symbol: '000002',
          name: '万科A',
          price: 28.67,
          change: -0.45,
          changePercent: -1.54,
          volume: 89000000,
          turnover: 2551630000,
          high: 29.20,
          low: 28.50,
          open: 29.12,
          prevClose: 29.12,
          bid: 28.66,
          ask: 28.67,
          bidSize: 8900,
          askSize: 7600
        },
        {
          symbol: '000858',
          name: '五粮液',
          price: 156.78,
          change: 2.34,
          changePercent: 1.52,
          volume: 45000000,
          turnover: 7055100000,
          high: 158.90,
          low: 154.20,
          open: 154.44,
          prevClose: 154.44,
          bid: 156.77,
          ask: 156.78,
          bidSize: 2300,
          askSize: 1900
        }
      ]);

      // 模拟持仓数据
      setPositions([
        {
          symbol: '000001',
          name: '平安银行',
          quantity: 10000,
          availableQuantity: 10000,
          avgPrice: 11.85,
          currentPrice: 12.45,
          marketValue: 124500,
          pnl: 6000,
          pnlPercent: 5.06,
          weight: 24.9
        },
        {
          symbol: '000002',
          name: '万科A',
          quantity: 5000,
          availableQuantity: 5000,
          avgPrice: 29.20,
          currentPrice: 28.67,
          marketValue: 143350,
          pnl: -2650,
          pnlPercent: -1.81,
          weight: 28.7
        }
      ]);

      // 模拟订单数据
      setOrders([
        {
          id: 'ORD001',
          symbol: '000858',
          name: '五粮液',
          side: 'buy',
          type: 'limit',
          quantity: 1000,
          price: 155.00,
          filledQuantity: 0,
          status: 'pending',
          createTime: dayjs().subtract(5, 'minute').format('HH:mm:ss'),
          updateTime: dayjs().subtract(5, 'minute').format('HH:mm:ss')
        },
        {
          id: 'ORD002',
          symbol: '002415',
          name: '海康威视',
          side: 'sell',
          type: 'market',
          quantity: 2000,
          filledQuantity: 2000,
          status: 'filled',
          createTime: dayjs().subtract(15, 'minute').format('HH:mm:ss'),
          updateTime: dayjs().subtract(10, 'minute').format('HH:mm:ss')
        }
      ]);

      setSelectedStock(watchlist[0]);
    };

    initData();
  }, []);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // 模拟数据更新
      setWatchlist(prev => prev.map(stock => ({
        ...stock,
        price: stock.price + (Math.random() - 0.5) * 0.1,
        change: stock.change + (Math.random() - 0.5) * 0.05,
        changePercent: stock.changePercent + (Math.random() - 0.5) * 0.1
      })));
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // 下单处理
  const handlePlaceOrder = async (values: any) => {
    try {
      setLoading(true);
      
      // 模拟下单API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newOrder: Order = {
        id: `ORD${Date.now()}`,
        symbol: values.symbol,
        name: selectedStock?.name || '',
        side: values.side,
        type: values.type,
        quantity: values.quantity,
        price: values.price,
        filledQuantity: 0,
        status: 'pending',
        createTime: dayjs().format('HH:mm:ss'),
        updateTime: dayjs().format('HH:mm:ss')
      };

      setOrders(prev => [newOrder, ...prev]);
      setOrderModalVisible(false);
      orderForm.resetFields();
      message.success('订单提交成功');
    } catch (error) {
      message.error('订单提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 撤单处理
  const handleCancelOrder = async (orderId: string) => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, status: 'cancelled' as const, updateTime: dayjs().format('HH:mm:ss') }
          : order
      ));
      
      message.success('撤单成功');
    } catch (error) {
      message.error('撤单失败');
    } finally {
      setLoading(false);
    }
  };

  // 自选股表格列定义
  const watchlistColumns = [
    {
      title: '代码',
      dataIndex: 'symbol',
      key: 'symbol',
      width: 80,
      render: (symbol: string) => (
        <Text strong className="text-blue-600">{symbol}</Text>
      )
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      ellipsis: true
    },
    {
      title: '现价',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      render: (price: number) => (
        <Text strong>{price.toFixed(2)}</Text>
      )
    },
    {
      title: '涨跌幅',
      dataIndex: 'changePercent',
      key: 'changePercent',
      width: 80,
      render: (changePercent: number) => (
        <Tag color={changePercent >= 0 ? 'red' : 'green'}>
          {changePercent >= 0 ? '+' : ''}{changePercent.toFixed(2)}%
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, record: StockQuote) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => setSelectedStock(record)}
          >
            选择
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedStock(record);
              setOrderModalVisible(true);
            }}
          >
            交易
          </Button>
        </Space>
      )
    }
  ];

  // 持仓表格列定义
  const positionColumns = [
    {
      title: '股票',
      key: 'stock',
      render: (_: any, record: Position) => (
        <div>
          <Text strong>{record.symbol}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.name}
          </Text>
        </div>
      )
    },
    {
      title: '持仓/可用',
      key: 'quantity',
      render: (_: any, record: Position) => (
        <div>
          <Text>{record.quantity.toLocaleString()}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.availableQuantity.toLocaleString()}
          </Text>
        </div>
      )
    },
    {
      title: '成本价',
      dataIndex: 'avgPrice',
      key: 'avgPrice',
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '市值',
      dataIndex: 'marketValue',
      key: 'marketValue',
      render: (value: number) => `¥${value.toLocaleString()}`
    },
    {
      title: '盈亏',
      key: 'pnl',
      render: (_: any, record: Position) => (
        <div>
          <Text type={record.pnl >= 0 ? 'success' : 'danger'}>
            {record.pnl >= 0 ? '+' : ''}¥{record.pnl.toLocaleString()}
          </Text>
          <br />
          <Text
            type={record.pnlPercent >= 0 ? 'success' : 'danger'}
            style={{ fontSize: '12px' }}
          >
            {record.pnlPercent >= 0 ? '+' : ''}{record.pnlPercent.toFixed(2)}%
          </Text>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Position) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedStock({
                symbol: record.symbol,
                name: record.name,
                price: record.currentPrice,
                change: 0,
                changePercent: 0,
                volume: 0,
                turnover: 0,
                high: 0,
                low: 0,
                open: 0,
                prevClose: 0,
                bid: 0,
                ask: 0,
                bidSize: 0,
                askSize: 0
              });
              orderForm.setFieldsValue({
                symbol: record.symbol,
                side: 'sell',
                type: 'market',
                quantity: record.availableQuantity
              });
              setOrderModalVisible(true);
            }}
          >
            卖出
          </Button>
        </Space>
      )
    }
  ];

  // 订单表格列定义
  const orderColumns = [
    {
      title: '股票',
      key: 'stock',
      render: (_: any, record: Order) => (
        <div>
          <Text strong>{record.symbol}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.name}
          </Text>
        </div>
      )
    },
    {
      title: '方向',
      dataIndex: 'side',
      key: 'side',
      render: (side: string) => (
        <Tag color={side === 'buy' ? 'red' : 'green'}>
          {side === 'buy' ? '买入' : '卖出'}
        </Tag>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          market: '市价',
          limit: '限价',
          stop: '止损'
        };
        return typeMap[type as keyof typeof typeMap] || type;
      }
    },
    {
      title: '数量',
      key: 'quantity',
      render: (_: any, record: Order) => (
        <div>
          <Text>{record.quantity.toLocaleString()}</Text>
          {record.filledQuantity > 0 && (
            <>
              <br />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                已成交: {record.filledQuantity.toLocaleString()}
              </Text>
            </>
          )}
        </div>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price?: number) => price ? `¥${price.toFixed(2)}` : '市价'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { color: 'processing', text: '待成交' },
          partial: { color: 'warning', text: '部分成交' },
          filled: { color: 'success', text: '已成交' },
          cancelled: { color: 'default', text: '已撤销' }
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Badge status={config.color as any} text={config.text} />;
      }
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime'
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Order) => (
        <Space size="small">
          {record.status === 'pending' && (
            <Popconfirm
              title="确定要撤销这个订单吗？"
              onConfirm={() => handleCancelOrder(record.id)}
            >
              <Button type="link" size="small" danger>
                撤单
              </Button>
            </Popconfirm>
          )}
          <Button type="link" size="small">
            详情
          </Button>
        </Space>
      )
    }
  ];

  // K线图数据
  const generateKlineData = () => {
    const data = [];
    let price = selectedStock?.price || 100;
    
    for (let i = 0; i < 100; i++) {
      const open = price;
      const change = (Math.random() - 0.5) * 4;
      const close = open + change;
      const high = Math.max(open, close) + Math.random() * 2;
      const low = Math.min(open, close) - Math.random() * 2;
      
      data.push({
        date: dayjs().subtract(99 - i, 'minute').format('HH:mm'),
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 1000000)
      });
      
      price = close;
    }
    
    return data;
  };

  const klineConfig = {
    data: generateKlineData(),
    xField: 'date',
    yField: ['open', 'close', 'high', 'low'],
    meta: {
      date: { alias: '时间' },
      open: { alias: '开盘价' },
      close: { alias: '收盘价' },
      high: { alias: '最高价' },
      low: { alias: '最低价' }
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
        <div className="flex items-center space-x-4">
          <Title level={3} className="mb-0">
            交易终端
          </Title>
          {selectedStock && (
            <div className="flex items-center space-x-2">
              <Text strong className="text-lg">{selectedStock.symbol}</Text>
              <Text type="secondary">{selectedStock.name}</Text>
              <Text strong className="text-xl">
                ¥{selectedStock.price.toFixed(2)}
              </Text>
              <Tag color={selectedStock.changePercent >= 0 ? 'red' : 'green'}>
                {selectedStock.changePercent >= 0 ? '+' : ''}
                {selectedStock.changePercent.toFixed(2)}%
              </Tag>
            </div>
          )}
        </div>

        <Space wrap>
          <Switch
            checked={autoRefresh}
            onChange={setAutoRefresh}
            checkedChildren="自动刷新"
            unCheckedChildren="手动刷新"
          />
          
          <Button
            type="primary"
            icon={<ShoppingCartOutlined />}
            onClick={() => setOrderModalVisible(true)}
            disabled={!selectedStock}
          >
            下单
          </Button>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={() => window.location.reload()}
          >
            刷新
          </Button>
        </Space>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 grid grid-cols-12 gap-4">
        {/* 左侧面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-4">
          {/* 自选股 */}
          <Card
            title="自选股"
            size="small"
            extra={
              <Button type="text" size="small" icon={<SettingOutlined />} />
            }
          >
            <Table
              columns={watchlistColumns}
              dataSource={watchlist}
              rowKey="symbol"
              size="small"
              pagination={false}
              scroll={{ y: 300 }}
              onRow={(record) => ({
                onClick: () => setSelectedStock(record),
                className: selectedStock?.symbol === record.symbol ? 'bg-blue-50' : ''
              })}
            />
          </Card>

          {/* 快速交易 */}
          <Card title="快速交易" size="small">
            <Form layout="vertical" size="small">
              <Form.Item label="股票代码">
                <Input
                  value={selectedStock?.symbol}
                  placeholder="请选择股票"
                  readOnly
                />
              </Form.Item>
              <Form.Item>
                <Space.Compact style={{ width: '100%' }}>
                  <Button
                    type="primary"
                    danger
                    style={{ width: '50%' }}
                    onClick={() => {
                      orderForm.setFieldsValue({ side: 'buy' });
                      setOrderModalVisible(true);
                    }}
                    disabled={!selectedStock}
                  >
                    买入
                  </Button>
                  <Button
                    type="primary"
                    style={{ width: '50%' }}
                    onClick={() => {
                      orderForm.setFieldsValue({ side: 'sell' });
                      setOrderModalVisible(true);
                    }}
                    disabled={!selectedStock}
                  >
                    卖出
                  </Button>
                </Space.Compact>
              </Form.Item>
            </Form>
          </Card>
        </div>

        {/* 中间主图表区域 */}
        <div className="col-span-12 lg:col-span-6">
          <Card
            title={selectedStock ? `${selectedStock.symbol} - ${selectedStock.name}` : '请选择股票'}
            extra={
              <Space>
                <Button
                  type="text"
                  size="small"
                  icon={chartFullscreen ? <CompressOutlined /> : <FullscreenOutlined />}
                  onClick={() => setChartFullscreen(!chartFullscreen)}
                />
                <Button type="text" size="small" icon={<SettingOutlined />} />
              </Space>
            }
            className="h-full"
            bodyStyle={{ height: 'calc(100% - 57px)' }}
          >
            {selectedStock ? (
              <div ref={chartRef} className="h-full">
                <Candlestick {...klineConfig} height={400} />
              </div>
            ) : (
              <div className="h-full flex items-center justify-center">
                <Text type="secondary">请从左侧自选股中选择一只股票</Text>
              </div>
            )}
          </Card>
        </div>

        {/* 右侧面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-4">
          {/* 五档行情 */}
          {selectedStock && (
            <Card title="五档行情" size="small">
              <div className="space-y-1">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="flex justify-between text-xs">
                    <Text type="success">卖{i}</Text>
                    <Text>{(selectedStock.ask + (i - 1) * 0.01).toFixed(2)}</Text>
                    <Text type="secondary">{Math.floor(Math.random() * 1000)}</Text>
                  </div>
                ))}
                <Divider style={{ margin: '8px 0' }} />
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="flex justify-between text-xs">
                    <Text type="danger">买{i}</Text>
                    <Text>{(selectedStock.bid - (i - 1) * 0.01).toFixed(2)}</Text>
                    <Text type="secondary">{Math.floor(Math.random() * 1000)}</Text>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* 交易统计 */}
          <Card title="今日统计" size="small">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Text type="secondary">成交笔数</Text>
                <Text>12</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">成交金额</Text>
                <Text>¥125,680</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">手续费</Text>
                <Text>¥15.68</Text>
              </div>
              <div className="flex justify-between">
                <Text type="secondary">净盈亏</Text>
                <Text type="success">+¥3,450</Text>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 底部面板 */}
      <div className="mt-4">
        <Card>
          <Tabs defaultActiveKey="positions">
            <TabPane tab="持仓" key="positions">
              <Table
                columns={positionColumns}
                dataSource={positions}
                rowKey="symbol"
                size="small"
                pagination={false}
                scroll={{ x: 800 }}
              />
            </TabPane>
            <TabPane tab="委托" key="orders">
              <Table
                columns={orderColumns}
                dataSource={orders}
                rowKey="id"
                size="small"
                pagination={false}
                scroll={{ x: 1000 }}
              />
            </TabPane>
            <TabPane tab="成交" key="trades">
              <Empty description="暂无成交记录" />
            </TabPane>
          </Tabs>
        </Card>
      </div>

      {/* 下单弹窗 */}
      <Modal
        title="股票交易"
        open={orderModalVisible}
        onCancel={() => setOrderModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={orderForm}
          layout="vertical"
          onFinish={handlePlaceOrder}
          initialValues={{
            symbol: selectedStock?.symbol,
            type: 'limit',
            side: 'buy'
          }}
        >
          <Form.Item name="symbol" label="股票代码">
            <Input disabled />
          </Form.Item>

          <Form.Item name="side" label="交易方向">
            <Segmented
              options={[
                { label: '买入', value: 'buy' },
                { label: '卖出', value: 'sell' }
              ]}
            />
          </Form.Item>

          <Form.Item name="type" label="订单类型">
            <Select>
              <Select.Option value="market">市价单</Select.Option>
              <Select.Option value="limit">限价单</Select.Option>
              <Select.Option value="stop">止损单</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="quantity"
            label="数量"
            rules={[{ required: true, message: '请输入交易数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={100}
              step={100}
              placeholder="请输入数量（股）"
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.type !== currentValues.type
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('type') !== 'market' && (
                <Form.Item
                  name="price"
                  label="价格"
                  rules={[{ required: true, message: '请输入交易价格' }]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    min={0.01}
                    step={0.01}
                    precision={2}
                    placeholder="请输入价格"
                  />
                </Form.Item>
              )
            }
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setOrderModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                提交订单
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
