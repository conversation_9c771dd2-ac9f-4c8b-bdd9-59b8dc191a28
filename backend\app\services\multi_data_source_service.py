"""
多数据源集成服务

整合多个数据源，提供统一的数据访问接口
"""

import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod

from app.core.logging import logger
from app.core.config import settings
from app.services.jqdata_service import JQDataService


class DataSourceInterface(ABC):
    """数据源接口"""
    
    @abstractmethod
    async def get_stock_price(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取股票价格数据"""
        pass
    
    @abstractmethod
    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        pass
    
    @abstractmethod
    async def get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        pass


class JQDataSource(DataSourceInterface):
    """聚宽数据源"""
    
    def __init__(self):
        self.jqdata_service = JQDataService()
    
    async def get_stock_price(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取股票价格数据"""
        try:
            data = await self.jqdata_service.get_price_data(symbol, start_date, end_date)
            return {
                'source': 'jqdata',
                'symbol': symbol,
                'data': data,
                'success': True
            }
        except Exception as e:
            logger.error(f"JQData price fetch failed: {e}")
            return {
                'source': 'jqdata',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            info = await self.jqdata_service.get_stock_info(symbol)
            return {
                'source': 'jqdata',
                'symbol': symbol,
                'data': info,
                'success': True
            }
        except Exception as e:
            logger.error(f"JQData stock info fetch failed: {e}")
            return {
                'source': 'jqdata',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            # 获取主要指数数据
            indices = ['000001.XSHG', '399001.XSHE', '399006.XSHE']  # 上证、深证、创业板
            market_data = {}
            
            for index in indices:
                data = await self.jqdata_service.get_current_price(index)
                market_data[index] = data
            
            return {
                'source': 'jqdata',
                'data': market_data,
                'success': True
            }
        except Exception as e:
            logger.error(f"JQData market data fetch failed: {e}")
            return {
                'source': 'jqdata',
                'data': None,
                'success': False,
                'error': str(e)
            }


class TushareDataSource(DataSourceInterface):
    """Tushare数据源"""
    
    def __init__(self, token: str = None):
        self.token = token or getattr(settings, 'TUSHARE_TOKEN', None)
        self.base_url = 'http://api.tushare.pro'
    
    async def get_stock_price(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取股票价格数据"""
        try:
            # 转换股票代码格式
            ts_code = self._convert_symbol_to_tushare(symbol)
            
            # 模拟Tushare API调用
            data = await self._mock_tushare_price_data(ts_code, start_date, end_date)
            
            return {
                'source': 'tushare',
                'symbol': symbol,
                'data': data,
                'success': True
            }
        except Exception as e:
            logger.error(f"Tushare price fetch failed: {e}")
            return {
                'source': 'tushare',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            ts_code = self._convert_symbol_to_tushare(symbol)
            info = await self._mock_tushare_stock_info(ts_code)
            
            return {
                'source': 'tushare',
                'symbol': symbol,
                'data': info,
                'success': True
            }
        except Exception as e:
            logger.error(f"Tushare stock info fetch failed: {e}")
            return {
                'source': 'tushare',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            market_data = await self._mock_tushare_market_data()
            
            return {
                'source': 'tushare',
                'data': market_data,
                'success': True
            }
        except Exception as e:
            logger.error(f"Tushare market data fetch failed: {e}")
            return {
                'source': 'tushare',
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    def _convert_symbol_to_tushare(self, symbol: str) -> str:
        """转换股票代码为Tushare格式"""
        if '.XSHE' in symbol:
            return symbol.replace('.XSHE', '.SZ')
        elif '.XSHG' in symbol:
            return symbol.replace('.XSHG', '.SH')
        return symbol
    
    async def _mock_tushare_price_data(self, ts_code: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """模拟Tushare价格数据"""
        # 生成模拟数据
        base_price = 12.50
        data = []
        
        start = datetime.strptime(start_date or '2024-08-01', '%Y-%m-%d')
        end = datetime.strptime(end_date or '2024-08-27', '%Y-%m-%d')
        
        current_date = start
        while current_date <= end:
            if current_date.weekday() < 5:  # 工作日
                price_change = (hash(str(current_date)) % 200 - 100) / 1000  # -0.1 到 0.1
                base_price *= (1 + price_change)
                
                data.append({
                    'trade_date': current_date.strftime('%Y%m%d'),
                    'open': round(base_price * 0.998, 2),
                    'high': round(base_price * 1.015, 2),
                    'low': round(base_price * 0.985, 2),
                    'close': round(base_price, 2),
                    'vol': int(abs(hash(str(current_date))) % 1000000 + 500000),
                    'amount': round(base_price * (abs(hash(str(current_date))) % 1000000 + 500000), 2)
                })
            
            current_date += timedelta(days=1)
        
        return data
    
    async def _mock_tushare_stock_info(self, ts_code: str) -> Dict[str, Any]:
        """模拟Tushare股票信息"""
        return {
            'ts_code': ts_code,
            'symbol': ts_code.split('.')[0],
            'name': f'股票{ts_code.split(".")[0]}',
            'area': '深圳',
            'industry': '银行',
            'market': 'main',
            'list_date': '20000101',
            'is_hs': 'S'
        }
    
    async def _mock_tushare_market_data(self) -> Dict[str, Any]:
        """模拟Tushare市场数据"""
        return {
            '000001.SH': {
                'close': 3234.56,
                'change': 12.34,
                'pct_chg': 0.38,
                'vol': 25678900,
                'amount': 287654321.0
            },
            '399001.SZ': {
                'close': 12456.78,
                'change': 89.12,
                'pct_chg': 0.72,
                'vol': 34567890,
                'amount': 398765432.0
            }
        }


class YahooFinanceDataSource(DataSourceInterface):
    """Yahoo Finance数据源"""
    
    def __init__(self):
        self.base_url = 'https://query1.finance.yahoo.com/v8/finance/chart'
    
    async def get_stock_price(self, symbol: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取股票价格数据"""
        try:
            # 转换股票代码为Yahoo格式
            yahoo_symbol = self._convert_symbol_to_yahoo(symbol)
            
            # 模拟Yahoo Finance数据
            data = await self._mock_yahoo_price_data(yahoo_symbol, start_date, end_date)
            
            return {
                'source': 'yahoo_finance',
                'symbol': symbol,
                'data': data,
                'success': True
            }
        except Exception as e:
            logger.error(f"Yahoo Finance price fetch failed: {e}")
            return {
                'source': 'yahoo_finance',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            yahoo_symbol = self._convert_symbol_to_yahoo(symbol)
            info = await self._mock_yahoo_stock_info(yahoo_symbol)
            
            return {
                'source': 'yahoo_finance',
                'symbol': symbol,
                'data': info,
                'success': True
            }
        except Exception as e:
            logger.error(f"Yahoo Finance stock info fetch failed: {e}")
            return {
                'source': 'yahoo_finance',
                'symbol': symbol,
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    async def get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            market_data = await self._mock_yahoo_market_data()
            
            return {
                'source': 'yahoo_finance',
                'data': market_data,
                'success': True
            }
        except Exception as e:
            logger.error(f"Yahoo Finance market data fetch failed: {e}")
            return {
                'source': 'yahoo_finance',
                'data': None,
                'success': False,
                'error': str(e)
            }
    
    def _convert_symbol_to_yahoo(self, symbol: str) -> str:
        """转换股票代码为Yahoo格式"""
        if '.XSHE' in symbol:
            return symbol.replace('.XSHE', '.SZ')
        elif '.XSHG' in symbol:
            return symbol.replace('.XSHG', '.SS')
        return symbol
    
    async def _mock_yahoo_price_data(self, yahoo_symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """模拟Yahoo Finance价格数据"""
        # 生成模拟数据
        base_price = 13.20
        data = []
        
        start = datetime.strptime(start_date or '2024-08-01', '%Y-%m-%d')
        end = datetime.strptime(end_date or '2024-08-27', '%Y-%m-%d')
        
        current_date = start
        while current_date <= end:
            if current_date.weekday() < 5:  # 工作日
                price_change = (hash(str(current_date) + yahoo_symbol) % 200 - 100) / 1000
                base_price *= (1 + price_change)
                
                data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'open': round(base_price * 0.999, 2),
                    'high': round(base_price * 1.012, 2),
                    'low': round(base_price * 0.988, 2),
                    'close': round(base_price, 2),
                    'volume': int(abs(hash(str(current_date) + yahoo_symbol)) % 800000 + 400000)
                })
            
            current_date += timedelta(days=1)
        
        return data
    
    async def _mock_yahoo_stock_info(self, yahoo_symbol: str) -> Dict[str, Any]:
        """模拟Yahoo Finance股票信息"""
        return {
            'symbol': yahoo_symbol,
            'shortName': f'Company {yahoo_symbol.split(".")[0]}',
            'longName': f'Company {yahoo_symbol.split(".")[0]} Ltd.',
            'currency': 'CNY',
            'exchange': 'SHE' if '.SZ' in yahoo_symbol else 'SHG',
            'marketCap': 12345678900,
            'sharesOutstanding': 1000000000
        }
    
    async def _mock_yahoo_market_data(self) -> Dict[str, Any]:
        """模拟Yahoo Finance市场数据"""
        return {
            '000001.SS': {
                'regularMarketPrice': 3245.67,
                'regularMarketChange': 15.23,
                'regularMarketChangePercent': 0.47,
                'regularMarketVolume': 28765432
            },
            '399001.SZ': {
                'regularMarketPrice': 12567.89,
                'regularMarketChange': 98.76,
                'regularMarketChangePercent': 0.79,
                'regularMarketVolume': 36789012
            }
        }


class MultiDataSourceService:
    """多数据源集成服务"""

    def __init__(self):
        self.data_sources = {
            'jqdata': JQDataSource(),
            'tushare': TushareDataSource(),
            'yahoo_finance': YahooFinanceDataSource()
        }
        self.primary_source = 'jqdata'
        self.fallback_sources = ['tushare', 'yahoo_finance']

    async def get_stock_price(
        self,
        symbol: str,
        start_date: str = None,
        end_date: str = None,
        sources: List[str] = None
    ) -> Dict[str, Any]:
        """获取股票价格数据，支持多数据源"""
        sources = sources or [self.primary_source] + self.fallback_sources
        results = {}

        # 并发获取多个数据源的数据
        tasks = []
        for source_name in sources:
            if source_name in self.data_sources:
                task = self.data_sources[source_name].get_stock_price(symbol, start_date, end_date)
                tasks.append((source_name, task))

        # 等待所有任务完成
        for source_name, task in tasks:
            try:
                result = await task
                results[source_name] = result
            except Exception as e:
                logger.error(f"Data source {source_name} failed: {e}")
                results[source_name] = {
                    'source': source_name,
                    'symbol': symbol,
                    'data': None,
                    'success': False,
                    'error': str(e)
                }

        # 选择最佳数据源结果
        best_result = self._select_best_result(results)

        return {
            'symbol': symbol,
            'primary_data': best_result,
            'all_sources': results,
            'data_quality': self._assess_data_quality(results)
        }

    async def get_stock_info(
        self,
        symbol: str,
        sources: List[str] = None
    ) -> Dict[str, Any]:
        """获取股票基本信息，支持多数据源"""
        sources = sources or [self.primary_source] + self.fallback_sources
        results = {}

        # 并发获取多个数据源的数据
        tasks = []
        for source_name in sources:
            if source_name in self.data_sources:
                task = self.data_sources[source_name].get_stock_info(symbol)
                tasks.append((source_name, task))

        # 等待所有任务完成
        for source_name, task in tasks:
            try:
                result = await task
                results[source_name] = result
            except Exception as e:
                logger.error(f"Data source {source_name} failed: {e}")
                results[source_name] = {
                    'source': source_name,
                    'symbol': symbol,
                    'data': None,
                    'success': False,
                    'error': str(e)
                }

        # 合并多个数据源的信息
        merged_info = self._merge_stock_info(results)

        return {
            'symbol': symbol,
            'merged_info': merged_info,
            'all_sources': results,
            'data_completeness': self._assess_info_completeness(results)
        }

    async def get_market_data(
        self,
        sources: List[str] = None
    ) -> Dict[str, Any]:
        """获取市场数据，支持多数据源"""
        sources = sources or [self.primary_source] + self.fallback_sources
        results = {}

        # 并发获取多个数据源的数据
        tasks = []
        for source_name in sources:
            if source_name in self.data_sources:
                task = self.data_sources[source_name].get_market_data()
                tasks.append((source_name, task))

        # 等待所有任务完成
        for source_name, task in tasks:
            try:
                result = await task
                results[source_name] = result
            except Exception as e:
                logger.error(f"Data source {source_name} failed: {e}")
                results[source_name] = {
                    'source': source_name,
                    'data': None,
                    'success': False,
                    'error': str(e)
                }

        # 合并市场数据
        merged_data = self._merge_market_data(results)

        return {
            'merged_data': merged_data,
            'all_sources': results,
            'data_freshness': self._assess_data_freshness(results)
        }

    async def get_aggregated_price_data(
        self,
        symbols: List[str],
        start_date: str = None,
        end_date: str = None
    ) -> Dict[str, Any]:
        """批量获取多只股票的价格数据"""
        results = {}

        # 并发获取多只股票数据
        tasks = []
        for symbol in symbols:
            task = self.get_stock_price(symbol, start_date, end_date)
            tasks.append((symbol, task))

        # 等待所有任务完成
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)

        for i, (symbol, _) in enumerate(tasks):
            result = completed_tasks[i]
            if isinstance(result, Exception):
                logger.error(f"Failed to get data for {symbol}: {result}")
                results[symbol] = {
                    'success': False,
                    'error': str(result)
                }
            else:
                results[symbol] = result

        return {
            'symbols': symbols,
            'results': results,
            'summary': self._generate_batch_summary(results)
        }

    def _select_best_result(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """选择最佳数据源结果"""
        # 优先选择成功的主数据源
        if self.primary_source in results and results[self.primary_source]['success']:
            return results[self.primary_source]

        # 选择第一个成功的备用数据源
        for source_name in self.fallback_sources:
            if source_name in results and results[source_name]['success']:
                return results[source_name]

        # 如果都失败了，返回主数据源的错误信息
        return results.get(self.primary_source, {'success': False, 'error': 'No data available'})

    def _assess_data_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        successful_sources = [name for name, result in results.items() if result['success']]
        total_sources = len(results)

        return {
            'success_rate': len(successful_sources) / total_sources if total_sources > 0 else 0,
            'successful_sources': successful_sources,
            'failed_sources': [name for name, result in results.items() if not result['success']],
            'data_consistency': self._check_data_consistency(results),
            'quality_score': self._calculate_quality_score(results)
        }

    def _merge_stock_info(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个数据源的股票信息"""
        merged = {}

        for source_name, result in results.items():
            if result['success'] and result['data']:
                data = result['data']
                for key, value in data.items():
                    if key not in merged and value is not None:
                        merged[key] = {
                            'value': value,
                            'source': source_name,
                            'confidence': 1.0
                        }
                    elif key in merged:
                        # 如果多个数据源有相同字段，记录所有来源
                        if 'alternative_sources' not in merged[key]:
                            merged[key]['alternative_sources'] = []
                        merged[key]['alternative_sources'].append({
                            'value': value,
                            'source': source_name
                        })

        return merged

    def _merge_market_data(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """合并市场数据"""
        merged = {}

        for source_name, result in results.items():
            if result['success'] and result['data']:
                for symbol, data in result['data'].items():
                    if symbol not in merged:
                        merged[symbol] = {}

                    for key, value in data.items():
                        if key not in merged[symbol]:
                            merged[symbol][key] = {
                                'value': value,
                                'source': source_name
                            }
                        else:
                            # 计算平均值或选择最新值
                            if isinstance(value, (int, float)):
                                current_value = merged[symbol][key]['value']
                                if isinstance(current_value, (int, float)):
                                    merged[symbol][key]['value'] = (current_value + value) / 2
                                    merged[symbol][key]['sources'] = merged[symbol][key].get('sources', []) + [source_name]

        return merged

    def _assess_info_completeness(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """评估信息完整性"""
        all_fields = set()
        successful_results = {}

        # 收集所有字段
        for source_name, result in results.items():
            if result['success'] and result['data']:
                all_fields.update(result['data'].keys())
                successful_results[source_name] = result['data']

        if not all_fields:
            return {'completeness_score': 0, 'missing_fields': [], 'available_fields': []}

        # 计算完整性
        field_coverage = {}
        for field in all_fields:
            sources_with_field = [
                source for source, data in successful_results.items()
                if field in data and data[field] is not None
            ]
            field_coverage[field] = len(sources_with_field)

        completeness_score = sum(field_coverage.values()) / (len(all_fields) * len(successful_results)) if successful_results else 0

        return {
            'completeness_score': completeness_score,
            'field_coverage': field_coverage,
            'total_fields': len(all_fields),
            'successful_sources': len(successful_results)
        }

    def _assess_data_freshness(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据新鲜度"""
        current_time = datetime.utcnow()
        freshness_scores = {}

        for source_name, result in results.items():
            if result['success']:
                # 假设数据都是最新的（实际应用中需要检查时间戳）
                freshness_scores[source_name] = 1.0
            else:
                freshness_scores[source_name] = 0.0

        avg_freshness = sum(freshness_scores.values()) / len(freshness_scores) if freshness_scores else 0

        return {
            'average_freshness': avg_freshness,
            'source_freshness': freshness_scores,
            'last_updated': current_time.isoformat()
        }

    def _check_data_consistency(self, results: Dict[str, Any]) -> float:
        """检查数据一致性"""
        # 简化的一致性检查
        successful_results = [result for result in results.values() if result['success']]

        if len(successful_results) < 2:
            return 1.0  # 只有一个数据源或没有成功的数据源

        # 这里可以实现更复杂的一致性检查逻辑
        return 0.85  # 模拟一致性分数

    def _calculate_quality_score(self, results: Dict[str, Any]) -> float:
        """计算数据质量分数"""
        successful_count = sum(1 for result in results.values() if result['success'])
        total_count = len(results)

        if total_count == 0:
            return 0.0

        success_rate = successful_count / total_count
        consistency_score = self._check_data_consistency(results)

        # 综合评分
        quality_score = (success_rate * 0.6 + consistency_score * 0.4)

        return quality_score

    def _generate_batch_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成批量处理摘要"""
        total_symbols = len(results)
        successful_symbols = sum(1 for result in results.values() if result.get('primary_data', {}).get('success', False))

        return {
            'total_symbols': total_symbols,
            'successful_symbols': successful_symbols,
            'success_rate': successful_symbols / total_symbols if total_symbols > 0 else 0,
            'failed_symbols': [symbol for symbol, result in results.items()
                             if not result.get('primary_data', {}).get('success', False)]
        }
