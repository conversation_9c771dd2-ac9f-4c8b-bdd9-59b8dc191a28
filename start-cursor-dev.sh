#!/bin/bash

echo "========================================"
echo "  JQData量化平台 - Cursor开发环境启动"
echo "========================================"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo ""
echo "📋 检查环境..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装或未添加到PATH"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装或未添加到PATH"
    exit 1
fi

echo "✅ Python和Node.js环境检查通过"

echo ""
echo "🔧 配置开发环境..."

# 复制Cursor开发环境配置
if [ -f "backend/.env.cursor" ]; then
    cp "backend/.env.cursor" "backend/.env"
    echo "✅ 已应用Cursor开发环境配置"
else
    echo "⚠️  Cursor配置文件不存在，使用默认配置"
fi

echo ""
echo "🚀 启动后端服务..."

# 启动后端
cd backend

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
if [ -f "requirements.txt" ]; then
    echo "📦 安装Python依赖..."
    pip install -r requirements.txt
fi

# 启动后端服务
echo "🚀 启动后端服务..."
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

echo "✅ 后端服务启动中... (PID: $BACKEND_PID)"

# 等待后端启动
sleep 3

echo ""
echo "🎨 启动前端服务..."

# 启动前端
cd ../frontend

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装Node.js依赖..."
    npm install
fi

# 启动前端服务
echo "🚀 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo "✅ 前端服务启动中... (PID: $FRONTEND_PID)"

echo ""
echo "🎉 服务启动完成！"
echo ""
echo "📍 访问地址:"
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8000"
echo "   API文档:  http://localhost:8000/docs"
echo ""
echo "🔑 默认管理员账号:"
echo "   邮箱: <EMAIL>"
echo "   密码: admin123456"
echo ""
echo "💡 特性说明:"
echo "   ✅ 使用SQLite数据库 (无需安装PostgreSQL)"
echo "   ✅ 使用MockRedis模拟 (无需安装Redis)"
echo "   ✅ 热重载开发模式"
echo "   ✅ 详细日志输出"
echo ""
echo "🛑 停止服务:"
echo "   按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "✅ 服务已停止"; exit 0' INT

# 保持脚本运行
wait
