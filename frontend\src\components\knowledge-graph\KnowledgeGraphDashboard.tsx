'use client';

/**
 * 知识图谱仪表板
 * 
 * 提供图构建、分析、GNN训练等知识图谱功能的管理界面
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Descriptions,
  Alert,
  Badge,
  Tooltip,
  List,
  Avatar,
  Timeline,
  Spin,
  Steps,
  Divider,
  Radio,
  Checkbox,
  Slider,
  Tree,
  Drawer,
  Empty,
  Typography
} from 'antd';
import {
  ShareAltOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  ClusterOutlined,
  ExperimentOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  RadarChartOutlined,
  NetworkOutlined,
  RobotOutlined,
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  ThunderboltOutlined,
  BulbOutlined,
  ApiOutlined,
  DatabaseOutlined,
  FunctionOutlined
} from '@ant-design/icons';
import { Line, Column, Radar, Network, Sankey } from '@ant-design/plots';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { Step } = Steps;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface KnowledgeGraph {
  id: number;
  name: string;
  description: string;
  graph_type: string;
  domain: string;
  status: string;
  entity_count: number;
  relation_count: number;
  completeness_score: number;
  consistency_score: number;
  accuracy_score: number;
  created_at: string;
  last_build_time?: string;
}

interface GraphAnalysis {
  id: number;
  name: string;
  analysis_type: string;
  description: string;
  status: string;
  algorithms_used: string[];
  computation_time_seconds: number;
  created_at: string;
  completed_at?: string;
}

interface GNNModel {
  id: number;
  name: string;
  architecture: string;
  model_type: string;
  description: string;
  status: string;
  knowledge_graph_id: number;
  training_epochs: number;
  test_metrics: any;
  is_deployed: boolean;
  created_at: string;
  trained_at?: string;
}

export const KnowledgeGraphDashboard: React.FC = () => {
  const [form] = Form.useForm();
  const [analysisForm] = Form.useForm();
  const [gnnForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('graphs');
  const [graphs, setGraphs] = useState<KnowledgeGraph[]>([]);
  const [analyses, setAnalyses] = useState<GraphAnalysis[]>([]);
  const [gnnModels, setGnnModels] = useState<GNNModel[]>([]);
  const [selectedGraph, setSelectedGraph] = useState<KnowledgeGraph | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [analysisModalVisible, setAnalysisModalVisible] = useState(false);
  const [gnnModalVisible, setGnnModalVisible] = useState(false);
  const [graphDetailVisible, setGraphDetailVisible] = useState(false);
  const [networkVisible, setNetworkVisible] = useState(false);
  const networkRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchGraphs();
  }, []);

  useEffect(() => {
    if (activeTab === 'analyses') {
      fetchAnalyses();
    } else if (activeTab === 'gnn') {
      fetchGNNModels();
    }
  }, [activeTab]);

  const fetchGraphs = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/knowledge-graph/graphs', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setGraphs(result.data.items);
      }
    } catch (error) {
      console.error('获取知识图谱列表失败:', error);
      message.error('获取知识图谱列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalyses = async () => {
    try {
      const response = await fetch('/api/v1/knowledge-graph/graphs/1/analyses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setAnalyses(result.data.items);
      }
    } catch (error) {
      console.error('获取图分析列表失败:', error);
      message.error('获取图分析列表失败');
    }
  };

  const fetchGNNModels = async () => {
    try {
      const response = await fetch('/api/v1/knowledge-graph/gnn/models', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setGnnModels(result.data.items);
      }
    } catch (error) {
      console.error('获取GNN模型列表失败:', error);
      message.error('获取GNN模型列表失败');
    }
  };

  const handleCreateGraph = async (values: any) => {
    try {
      const response = await fetch('/api/v1/knowledge-graph/graphs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('知识图谱创建成功');
        setCreateModalVisible(false);
        form.resetFields();
        fetchGraphs();
      } else {
        message.error(result.message || '知识图谱创建失败');
      }
    } catch (error) {
      console.error('创建知识图谱失败:', error);
      message.error('创建知识图谱失败');
    }
  };

  const handleAnalyzeGraph = async (values: any) => {
    try {
      if (!selectedGraph) {
        message.error('请先选择知识图谱');
        return;
      }

      const response = await fetch(`/api/v1/knowledge-graph/graphs/${selectedGraph.id}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('图分析已启动');
        setAnalysisModalVisible(false);
        analysisForm.resetFields();
        fetchAnalyses();
      } else {
        message.error(result.message || '图分析失败');
      }
    } catch (error) {
      console.error('图分析失败:', error);
      message.error('图分析失败');
    }
  };

  const handleTrainGNN = async (values: any) => {
    try {
      if (!selectedGraph) {
        message.error('请先选择知识图谱');
        return;
      }

      const response = await fetch(`/api/v1/knowledge-graph/graphs/${selectedGraph.id}/gnn/train`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('GNN模型训练已启动');
        setGnnModalVisible(false);
        gnnForm.resetFields();
        fetchGNNModels();
      } else {
        message.error(result.message || 'GNN模型训练失败');
      }
    } catch (error) {
      console.error('GNN模型训练失败:', error);
      message.error('GNN模型训练失败');
    }
  };

  const handleViewGraphDetails = async (graph: KnowledgeGraph) => {
    try {
      const response = await fetch(`/api/v1/knowledge-graph/graphs/${graph.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setSelectedGraph({ ...graph, details: result.data });
        setGraphDetailVisible(true);
      }
    } catch (error) {
      console.error('获取图详情失败:', error);
      message.error('获取图详情失败');
    }
  };

  const handleDeleteGraph = async (graphId: number) => {
    try {
      const response = await fetch(`/api/v1/knowledge-graph/graphs/${graphId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('知识图谱删除成功');
        fetchGraphs();
      } else {
        message.error(result.message || '知识图谱删除失败');
      }
    } catch (error) {
      console.error('删除知识图谱失败:', error);
      message.error('删除知识图谱失败');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'building':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'building':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderQualityRadar = (graph: KnowledgeGraph) => {
    const data = [
      { item: '完整性', score: (graph.completeness_score || 0) * 100 },
      { item: '一致性', score: (graph.consistency_score || 0) * 100 },
      { item: '准确性', score: (graph.accuracy_score || 0) * 100 }
    ];

    const config = {
      data,
      xField: 'item',
      yField: 'score',
      area: {},
      point: {
        size: 2,
      },
      line: {
        color: '#1890ff',
      },
    };

    return <Radar {...config} height={200} />;
  };

  const renderNetworkVisualization = () => {
    // 模拟网络数据
    const data = {
      nodes: [
        { id: 'node1', label: '平安银行', type: 'stock', value: 100 },
        { id: 'node2', label: '招商银行', type: 'stock', value: 80 },
        { id: 'node3', label: '万科A', type: 'stock', value: 90 },
        { id: 'node4', label: '五粮液', type: 'stock', value: 70 },
        { id: 'node5', label: '银行业', type: 'industry', value: 60 }
      ],
      edges: [
        { source: 'node1', target: 'node2', weight: 0.8 },
        { source: 'node1', target: 'node5', weight: 0.9 },
        { source: 'node2', target: 'node5', weight: 0.9 },
        { source: 'node3', target: 'node4', weight: 0.3 }
      ]
    };

    const config = {
      data,
      layout: {
        type: 'force',
        preventOverlap: true,
        nodeSize: 30,
      },
      nodeCfg: {
        size: 'value',
        color: 'type',
        label: {
          style: {
            fill: '#000',
            fontSize: 12,
          },
        },
      },
      edgeCfg: {
        style: {
          stroke: '#666',
          lineWidth: 'weight',
        },
      },
      behaviors: ['drag-canvas', 'zoom-canvas', 'drag-node'],
    };

    return <Network {...config} />;
  };

  const graphColumns = [
    {
      title: '图谱名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: KnowledgeGraph) => (
        <Space>
          {getStatusIcon(record.status)}
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">{record.graph_type}</div>
          </div>
        </Space>
      )
    },
    {
      title: '领域',
      dataIndex: 'domain',
      key: 'domain',
      render: (domain: string) => (
        <Tag color="blue">{domain}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: '规模',
      key: 'scale',
      render: (_, record: KnowledgeGraph) => (
        <div>
          <div>实体: {record.entity_count}</div>
          <div>关系: {record.relation_count}</div>
        </div>
      )
    },
    {
      title: '质量评分',
      key: 'quality',
      render: (_, record: KnowledgeGraph) => {
        const avgScore = ((record.completeness_score || 0) + 
                         (record.consistency_score || 0) + 
                         (record.accuracy_score || 0)) / 3;
        return (
          <Progress 
            percent={avgScore * 100} 
            size="small" 
            strokeColor={avgScore > 0.8 ? '#52c41a' : avgScore > 0.6 ? '#faad14' : '#ff4d4f'}
          />
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: KnowledgeGraph) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewGraphDetails(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<BarChartOutlined />}
            onClick={() => {
              setSelectedGraph(record);
              setAnalysisModalVisible(true);
            }}
          >
            分析
          </Button>
          <Button
            type="link"
            size="small"
            icon={<RobotOutlined />}
            onClick={() => {
              setSelectedGraph(record);
              setGnnModalVisible(true);
            }}
          >
            训练GNN
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个知识图谱吗？此操作不可恢复。',
                onOk: () => handleDeleteGraph(record.id)
              });
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  const analysisColumns = [
    {
      title: '分析名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: GraphAnalysis) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.analysis_type}</div>
        </div>
      )
    },
    {
      title: '算法',
      dataIndex: 'algorithms_used',
      key: 'algorithms_used',
      render: (algorithms: string[]) => (
        <div>
          {algorithms?.map(algo => (
            <Tag key={algo} size="small">{algo}</Tag>
          ))}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: '计算时间',
      dataIndex: 'computation_time_seconds',
      key: 'computation_time_seconds',
      render: (time: number) => time ? `${time.toFixed(2)}s` : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    }
  ];

  const gnnColumns = [
    {
      title: '模型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: GNNModel) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.architecture.toUpperCase()}</div>
        </div>
      )
    },
    {
      title: '架构',
      dataIndex: 'architecture',
      key: 'architecture',
      render: (arch: string) => (
        <Tag color="purple">{arch.toUpperCase()}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: '训练轮数',
      dataIndex: 'training_epochs',
      key: 'training_epochs'
    },
    {
      title: '部署状态',
      dataIndex: 'is_deployed',
      key: 'is_deployed',
      render: (deployed: boolean) => (
        <Tag color={deployed ? 'green' : 'default'}>
          {deployed ? '已部署' : '未部署'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">金融知识图谱</h1>
          <p className="text-gray-600">股票关系建模、图神经网络、图分析</p>
        </div>
        <Space>
          <Button
            icon={<NetworkOutlined />}
            onClick={() => setNetworkVisible(true)}
          >
            图可视化
          </Button>
          <Button
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建图谱
          </Button>
        </Space>
      </div>

      {/* 统计概览 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="知识图谱"
              value={graphs.length}
              prefix={<ShareAltOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="图分析"
              value={analyses.length}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="GNN模型"
              value={gnnModels.length}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均质量"
              value={graphs.length > 0 ? 
                (graphs.reduce((sum, g) => sum + ((g.completeness_score || 0) + (g.consistency_score || 0) + (g.accuracy_score || 0)) / 3, 0) / graphs.length * 100).toFixed(1) : 0
              }
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Badge count={graphs.length} size="small">
                <Space>
                  <ShareAltOutlined />
                  <span>知识图谱</span>
                </Space>
              </Badge>
            }
            key="graphs"
          >
            <Table
              columns={graphColumns}
              dataSource={graphs}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个图谱`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={analyses.length} size="small">
                <Space>
                  <BarChartOutlined />
                  <span>图分析</span>
                </Space>
              </Badge>
            }
            key="analyses"
          >
            <Table
              columns={analysisColumns}
              dataSource={analyses}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个分析`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={gnnModels.length} size="small">
                <Space>
                  <RobotOutlined />
                  <span>GNN模型</span>
                </Space>
              </Badge>
            }
            key="gnn"
          >
            <Table
              columns={gnnColumns}
              dataSource={gnnModels}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个模型`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建图谱弹窗 */}
      <Modal
        title="创建知识图谱"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateGraph}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="图谱名称" rules={[{ required: true }]}>
                <Input placeholder="请输入图谱名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="graph_type" label="图谱类型" initialValue="financial">
                <Select>
                  <Option value="financial">金融图谱</Option>
                  <Option value="stock">股票图谱</Option>
                  <Option value="industry">行业图谱</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="图谱描述">
            <TextArea rows={3} placeholder="请输入图谱描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="domain" label="领域" initialValue="finance">
                <Select>
                  <Option value="finance">金融</Option>
                  <Option value="stock">股票</Option>
                  <Option value="fund">基金</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="quality_threshold" label="质量阈值" initialValue={0.8}>
                <Slider min={0.1} max={1.0} step={0.1} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="update_strategy" label="更新策略" initialValue="incremental">
                <Select>
                  <Option value="incremental">增量更新</Option>
                  <Option value="full">全量更新</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider>数据源配置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="include_stocks" label="包含股票数据" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="include_companies" label="包含公司数据" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="include_relations" label="包含关系数据" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 图分析弹窗 */}
      <Modal
        title="图分析配置"
        open={analysisModalVisible}
        onCancel={() => setAnalysisModalVisible(false)}
        onOk={() => analysisForm.submit()}
        width={700}
        destroyOnClose
      >
        <Form
          form={analysisForm}
          layout="vertical"
          onFinish={handleAnalyzeGraph}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="分析名称" rules={[{ required: true }]}>
                <Input placeholder="请输入分析名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="analysis_type" label="分析类型" initialValue="comprehensive">
                <Select>
                  <Option value="comprehensive">综合分析</Option>
                  <Option value="centrality">中心性分析</Option>
                  <Option value="community">社区发现</Option>
                  <Option value="path">路径分析</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="分析描述">
            <TextArea rows={2} placeholder="请输入分析描述" />
          </Form.Item>

          <Divider>分析配置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="calculate_basic_metrics" label="基本指标" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="calculate_centrality" label="中心性分析" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="detect_communities" label="社区发现" valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="community_method" label="社区算法" initialValue="louvain">
                <Select>
                  <Option value="louvain">Louvain</Option>
                  <Option value="greedy_modularity">贪心模块度</Option>
                  <Option value="label_propagation">标签传播</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="analyze_paths" label="路径分析" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Form>
      </Modal>

      {/* GNN训练弹窗 */}
      <Modal
        title="GNN模型训练"
        open={gnnModalVisible}
        onCancel={() => setGnnModalVisible(false)}
        onOk={() => gnnForm.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={gnnForm}
          layout="vertical"
          onFinish={handleTrainGNN}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="model_name" label="模型名称" rules={[{ required: true }]}>
                <Input placeholder="请输入模型名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="architecture" label="模型架构" initialValue="gcn">
                <Select>
                  <Option value="gcn">GCN</Option>
                  <Option value="gat">GAT</Option>
                  <Option value="sage">GraphSAGE</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="模型描述">
            <TextArea rows={2} placeholder="请输入模型描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="task_type" label="任务类型" initialValue="node_classification">
                <Select>
                  <Option value="node_classification">节点分类</Option>
                  <Option value="node_regression">节点回归</Option>
                  <Option value="link_prediction">链接预测</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="hidden_dim" label="隐藏维度" initialValue={64}>
                <InputNumber min={16} max={512} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="num_layers" label="层数" initialValue={2}>
                <InputNumber min={1} max={10} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="learning_rate" label="学习率" initialValue={0.01}>
                <InputNumber min={0.0001} max={0.1} step={0.001} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="epochs" label="训练轮数" initialValue={200}>
                <InputNumber min={10} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="dropout" label="Dropout" initialValue={0.5}>
                <Slider min={0} max={1} step={0.1} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 图详情抽屉 */}
      <Drawer
        title="知识图谱详情"
        placement="right"
        width={600}
        open={graphDetailVisible}
        onClose={() => setGraphDetailVisible(false)}
      >
        {selectedGraph && (
          <div className="space-y-4">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="图谱名称">{selectedGraph.name}</Descriptions.Item>
              <Descriptions.Item label="图谱类型">{selectedGraph.graph_type}</Descriptions.Item>
              <Descriptions.Item label="领域">{selectedGraph.domain}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(selectedGraph.status)}>
                  {selectedGraph.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="实体数量">{selectedGraph.entity_count}</Descriptions.Item>
              <Descriptions.Item label="关系数量">{selectedGraph.relation_count}</Descriptions.Item>
            </Descriptions>

            <div>
              <h4 className="mb-2">质量评分</h4>
              {renderQualityRadar(selectedGraph)}
            </div>

            <div>
              <h4 className="mb-2">质量指标</h4>
              <Row gutter={16}>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="完整性"
                      value={(selectedGraph.completeness_score || 0) * 100}
                      suffix="%"
                      precision={1}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="一致性"
                      value={(selectedGraph.consistency_score || 0) * 100}
                      suffix="%"
                      precision={1}
                    />
                  </Card>
                </Col>
                <Col span={8}>
                  <Card size="small">
                    <Statistic
                      title="准确性"
                      value={(selectedGraph.accuracy_score || 0) * 100}
                      suffix="%"
                      precision={1}
                    />
                  </Card>
                </Col>
              </Row>
            </div>
          </div>
        )}
      </Drawer>

      {/* 网络可视化弹窗 */}
      <Modal
        title="知识图谱可视化"
        open={networkVisible}
        onCancel={() => setNetworkVisible(false)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <div ref={networkRef} style={{ height: 600 }}>
          {renderNetworkVisualization()}
        </div>
      </Modal>
    </div>
  );
};
