"""
数据模式模块

包含所有API请求和响应的数据模式定义
"""

from app.schemas.base import BaseResponse, PaginatedResponse
from app.schemas.auth import (
    LoginRequest,
    LoginResponse,
    RegisterRequest,
    TokenRefreshRequest,
    UserResponse,
)
from app.schemas.jqdata import (
    JQDataConfigRequest,
    JQDataConfigResponse,
    JQDataQuotaResponse,
)
from app.schemas.market import (
    StockResponse,
    PriceDataResponse,
    StockListRequest,
    PriceDataRequest,
)

__all__ = [
    "BaseResponse",
    "PaginatedResponse",
    "LoginRequest",
    "LoginResponse", 
    "RegisterRequest",
    "TokenRefreshRequest",
    "UserResponse",
    "JQDataConfigRequest",
    "JQDataConfigResponse",
    "JQDataQuotaResponse",
    "StockResponse",
    "PriceDataResponse",
    "StockListRequest",
    "PriceDataRequest",
]
