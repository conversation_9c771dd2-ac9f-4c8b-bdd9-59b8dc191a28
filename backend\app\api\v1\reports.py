"""
报告生成相关API端点

提供报告模板管理、报告生成、报告历史等API接口
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks, File, UploadFile
from fastapi.responses import FileResponse
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
import os
from pathlib import Path

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.report import ReportTemplate, ReportTask, ReportHistory, ReportSubscription
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.report_service import report_generation_service
from app.services.display_report_service import DisplayReportService
from app.services.jqdata_service import JQDataService

router = APIRouter()

# 初始化显示报告服务
jqdata_service = JQDataService()
display_report_service = DisplayReportService(jqdata_service)


# =============================================================================
# 报告模板管理
# =============================================================================

@router.get("/templates", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_report_templates(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    template_type: Optional[str] = Query(None, description="模板类型筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取报告模板列表"""
    try:
        # 构建查询条件
        conditions = [ReportTemplate.user_id == current_user.id, ReportTemplate.is_active == True]
        if template_type:
            conditions.append(ReportTemplate.template_type == template_type)
        
        # 查询总数
        count_query = select(func.count(ReportTemplate.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(ReportTemplate)
            .where(and_(*conditions))
            .order_by(desc(ReportTemplate.updated_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        templates = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        template_list = []
        for template in templates:
            template_list.append({
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "template_type": template.template_type,
                "category": template.category,
                "usage_count": template.usage_count,
                "is_public": template.is_public,
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat(),
            })
        
        return BaseResponse(
            code=200,
            message="获取报告模板列表成功",
            data=PaginatedResponse(
                items=template_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取报告模板列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取报告模板列表失败"
        )


@router.post("/templates", response_model=BaseResponse[dict])
async def create_report_template(
    template_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建报告模板"""
    try:
        template = ReportTemplate(
            user_id=current_user.id,
            name=template_data["name"],
            description=template_data.get("description"),
            template_type=template_data["template_type"],
            category=template_data.get("category"),
            template_config=template_data.get("template_config", {}),
            layout_config=template_data.get("layout_config", {}),
            style_config=template_data.get("style_config", {}),
            sections=template_data.get("sections", []),
            charts=template_data.get("charts", []),
            tables=template_data.get("tables", []),
        )
        
        db.add(template)
        await db.commit()
        await db.refresh(template)
        
        return BaseResponse(
            code=200,
            message="创建报告模板成功",
            data={
                "id": template.id,
                "name": template.name,
                "template_type": template.template_type,
                "created_at": template.created_at.isoformat(),
            }
        )
        
    except Exception as e:
        logger.error(f"创建报告模板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建报告模板失败"
        )


# =============================================================================
# 报告生成任务
# =============================================================================

@router.post("/generate", response_model=BaseResponse[dict])
async def generate_report(
    report_data: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """生成报告"""
    try:
        # 创建报告任务
        task = ReportTask(
            user_id=current_user.id,
            template_id=report_data.get("template_id"),
            name=report_data["name"],
            description=report_data.get("description"),
            report_type=report_data.get("report_type", "pdf"),
            date_range_start=datetime.fromisoformat(report_data["date_range_start"]) if report_data.get("date_range_start") else None,
            date_range_end=datetime.fromisoformat(report_data["date_range_end"]) if report_data.get("date_range_end") else None,
            parameters=report_data.get("parameters", {}),
            output_format=report_data.get("output_format", "pdf"),
            email_enabled=report_data.get("email_enabled", False),
            email_recipients=report_data.get("email_recipients", []),
            email_subject=report_data.get("email_subject"),
            email_body=report_data.get("email_body"),
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        # 添加后台任务生成报告
        background_tasks.add_task(
            report_generation_service.generate_report,
            task.id,
            db
        )
        
        return BaseResponse(
            code=200,
            message="报告生成任务已提交",
            data={
                "task_id": task.id,
                "name": task.name,
                "status": task.status,
                "created_at": task.created_at.isoformat(),
            }
        )
        
    except Exception as e:
        logger.error(f"提交报告生成任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交报告生成任务失败"
        )


@router.get("/tasks", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_report_tasks(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None, description="状态筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取报告任务列表"""
    try:
        # 构建查询条件
        conditions = [ReportTask.user_id == current_user.id]
        if status_filter:
            conditions.append(ReportTask.status == status_filter)
        
        # 查询总数
        count_query = select(func.count(ReportTask.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(ReportTask)
            .where(and_(*conditions))
            .order_by(desc(ReportTask.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        task_list = []
        for task in tasks:
            task_list.append({
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "report_type": task.report_type,
                "status": task.status,
                "progress": task.progress,
                "output_size": task.output_size,
                "email_enabled": task.email_enabled,
                "email_sent_at": task.email_sent_at.isoformat() if task.email_sent_at else None,
                "created_at": task.created_at.isoformat(),
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
            })
        
        return BaseResponse(
            code=200,
            message="获取报告任务列表成功",
            data=PaginatedResponse(
                items=task_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取报告任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取报告任务列表失败"
        )


@router.get("/tasks/{task_id}", response_model=BaseResponse[dict])
async def get_report_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取报告任务详情"""
    try:
        result = await db.execute(
            select(ReportTask).where(
                and_(
                    ReportTask.id == task_id,
                    ReportTask.user_id == current_user.id
                )
            )
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告任务不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取报告任务详情成功",
            data={
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "report_type": task.report_type,
                "status": task.status,
                "progress": task.progress,
                "parameters": task.parameters,
                "output_format": task.output_format,
                "output_size": task.output_size,
                "output_path": task.output_path,
                "email_enabled": task.email_enabled,
                "email_recipients": task.email_recipients,
                "email_subject": task.email_subject,
                "email_sent_at": task.email_sent_at.isoformat() if task.email_sent_at else None,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取报告任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取报告任务详情失败"
        )


# =============================================================================
# 报告下载
# =============================================================================

@router.get("/download/{task_id}")
async def download_report(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """下载报告文件"""
    try:
        # 获取报告任务
        result = await db.execute(
            select(ReportTask).where(
                and_(
                    ReportTask.id == task_id,
                    ReportTask.user_id == current_user.id,
                    ReportTask.status == "completed"
                )
            )
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告不存在或未完成"
            )
        
        if not task.output_path or not os.path.exists(task.output_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报告文件不存在"
            )
        
        # 更新下载统计
        history_result = await db.execute(
            select(ReportHistory).where(ReportHistory.task_id == task_id)
        )
        history = history_result.scalar_one_or_none()
        
        if history:
            history.download_count += 1
            history.last_downloaded_at = datetime.utcnow()
            await db.commit()
        
        # 返回文件
        filename = os.path.basename(task.output_path)
        return FileResponse(
            path=task.output_path,
            filename=filename,
            media_type='application/octet-stream'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载报告文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载报告文件失败"
        )


# =============================================================================
# 报告历史
# =============================================================================

@router.get("/history", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_report_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    report_type: Optional[str] = Query(None, description="报告类型筛选"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取报告历史"""
    try:
        # 构建查询条件
        conditions = [ReportHistory.user_id == current_user.id, ReportHistory.is_archived == False]
        if report_type:
            conditions.append(ReportHistory.report_type == report_type)
        
        # 查询总数
        count_query = select(func.count(ReportHistory.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(ReportHistory)
            .where(and_(*conditions))
            .order_by(desc(ReportHistory.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        histories = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        history_list = []
        for history in histories:
            history_list.append({
                "id": history.id,
                "task_id": history.task_id,
                "report_name": history.report_name,
                "report_type": history.report_type,
                "file_name": history.file_name,
                "file_size": history.file_size,
                "download_count": history.download_count,
                "view_count": history.view_count,
                "last_downloaded_at": history.last_downloaded_at.isoformat() if history.last_downloaded_at else None,
                "generation_time": history.generation_time,
                "is_shared": history.is_shared,
                "created_at": history.created_at.isoformat(),
            })
        
        return BaseResponse(
            code=200,
            message="获取报告历史成功",
            data=PaginatedResponse(
                items=history_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取报告历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取报告历史失败"
        )


# =============================================================================
# 显示报告生成 (新增)
# =============================================================================

@router.post("/display/portfolio/{portfolio_id}", response_model=BaseResponse[dict])
async def generate_display_portfolio_report(
    portfolio_id: int,
    report_type: str = Query('comprehensive', description="报告类型"),
    current_user: User = Depends(get_current_active_user)
):
    """生成投资组合显示报告"""
    try:
        result = await display_report_service.generate_portfolio_report(
            user_id=current_user.id,
            portfolio_id=portfolio_id,
            report_type=report_type
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            code=200,
            message="投资组合报告生成成功",
            data=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Portfolio display report generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="报告生成失败"
        )


@router.post("/display/strategy/{strategy_id}", response_model=BaseResponse[dict])
async def generate_display_strategy_report(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """生成策略回测显示报告"""
    try:
        result = await display_report_service.generate_strategy_report(
            user_id=current_user.id,
            strategy_id=strategy_id
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            code=200,
            message="策略报告生成成功",
            data=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Strategy display report generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="策略报告生成失败"
        )


@router.post("/display/market", response_model=BaseResponse[dict])
async def generate_display_market_report(
    market_type: str = Query('overall', description="市场类型"),
    symbols: Optional[str] = Query(None, description="股票代码列表，逗号分隔"),
    current_user: User = Depends(get_current_active_user)
):
    """生成市场分析显示报告"""
    try:
        symbol_list = []
        if symbols:
            symbol_list = [s.strip() for s in symbols.split(',') if s.strip()]

        result = await display_report_service.generate_market_report(
            market_type=market_type,
            symbols=symbol_list
        )

        if not result['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result['error']
            )

        return BaseResponse(
            code=200,
            message="市场报告生成成功",
            data=result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Market display report generation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="市场报告生成失败"
        )
