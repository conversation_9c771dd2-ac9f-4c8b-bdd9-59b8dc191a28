version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: jqdata_postgres
    environment:
      POSTGRES_DB: quantitative_trading
      POSTGRES_USER: quant_user
      POSTGRES_PASSWORD: quant_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - jqdata_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quant_user -d quantitative_trading"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: jqdata_redis
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - jqdata_network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: jqdata_backend
    environment:
      - DATABASE_URL=****************************************************/quantitative_trading
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
      - TZ=Asia/Shanghai
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    networks:
      - jqdata_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: jqdata_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - TZ=Asia/Shanghai
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - jqdata_network
    depends_on:
      - backend
    restart: unless-stopped
    command: npm run dev

  # Celery Worker (异步任务)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: jqdata_celery_worker
    environment:
      - DATABASE_URL=****************************************************/quantitative_trading
      - REDIS_URL=redis://redis:6379/0
      - TZ=Asia/Shanghai
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    networks:
      - jqdata_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.core.celery worker --loglevel=info

  # Celery Beat (定时任务)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: jqdata_celery_beat
    environment:
      - DATABASE_URL=****************************************************/quantitative_trading
      - REDIS_URL=redis://redis:6379/0
      - TZ=Asia/Shanghai
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    networks:
      - jqdata_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.core.celery beat --loglevel=info

  # Flower (Celery 监控)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: jqdata_flower
    environment:
      - REDIS_URL=redis://redis:6379/0
      - TZ=Asia/Shanghai
    ports:
      - "5555:5555"
    volumes:
      - ./backend:/app
    networks:
      - jqdata_network
    depends_on:
      - redis
    restart: unless-stopped
    command: celery -A app.core.celery flower --port=5555

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

networks:
  jqdata_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
