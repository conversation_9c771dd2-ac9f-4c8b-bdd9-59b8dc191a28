@echo off
echo ========================================
echo   JQData量化平台 - Cursor开发环境启动
echo ========================================

cd /d "%~dp0"

echo.
echo 📋 检查环境...

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python和Node.js环境检查通过

echo.
echo 🔧 配置开发环境...

REM 复制Cursor开发环境配置
if exist "backend\.env.cursor" (
    copy "backend\.env.cursor" "backend\.env" >nul
    echo ✅ 已应用Cursor开发环境配置
) else (
    echo ⚠️  Cursor配置文件不存在，使用默认配置
)

echo.
echo 🚀 启动后端服务...

REM 启动后端
cd backend
start "JQData Backend" cmd /k "python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

echo ✅ 后端服务启动中... (http://localhost:8000)

REM 等待后端启动
timeout /t 3 /nobreak >nul

echo.
echo 🎨 启动前端服务...

REM 启动前端
cd ..\frontend
start "JQData Frontend" cmd /k "npm run dev"

echo ✅ 前端服务启动中... (http://localhost:3000)

echo.
echo 🎉 服务启动完成！
echo.
echo 📍 访问地址:
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    API文档:  http://localhost:8000/docs
echo.
echo 🔑 默认管理员账号:
echo    邮箱: <EMAIL>
echo    密码: admin123456
echo.
echo 💡 特性说明:
echo    ✅ 使用SQLite数据库 (无需安装PostgreSQL)
echo    ✅ 使用MockRedis模拟 (无需安装Redis)
echo    ✅ 热重载开发模式
echo    ✅ 详细日志输出
echo.
echo 按任意键关闭此窗口...
pause >nul
