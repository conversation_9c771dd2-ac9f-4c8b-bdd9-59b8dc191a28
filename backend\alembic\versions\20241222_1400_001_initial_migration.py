"""Initial migration - Create all tables

Revision ID: 001
Revises: 
Create Date: 2024-12-22 14:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """创建所有表"""
    
    # 创建用户表
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('email', sa.String(length=320), nullable=False),
        sa.Column('hashed_password', sa.String(length=1024), nullable=False),
        sa.Column('is_active', sa.<PERSON>an(), nullable=False),
        sa.Column('is_superuser', sa.<PERSON>(), nullable=False),
        sa.Column('is_verified', sa.<PERSON>(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('full_name', sa.String(length=100), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('avatar_url', sa.String(length=500), nullable=True),
        sa.Column('subscription_type', sa.String(length=20), nullable=False),
        sa.Column('subscription_expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('api_quota_daily', sa.Integer(), nullable=False),
        sa.Column('api_quota_used_today', sa.Integer(), nullable=False),
        sa.Column('last_quota_reset', sa.DateTime(timezone=True), nullable=False),
        sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
        sa.Column('locked_until', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_login_ip', sa.String(length=45), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='用户表'
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    
    # 创建JQData配置表
    op.create_table(
        'jqdata_configs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=100), nullable=False),
        sa.Column('encrypted_password', sa.String(length=500), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('quota_total', sa.Integer(), nullable=False),
        sa.Column('quota_used', sa.Integer(), nullable=False),
        sa.Column('quota_reset_date', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('total_api_calls', sa.Integer(), nullable=False),
        sa.Column('last_auth_success', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_auth_error', sa.Text(), nullable=True),
        sa.Column('auth_failure_count', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='JQData配置表'
    )
    
    # 创建用户会话表
    op.create_table(
        'user_sessions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_token', sa.String(length=255), nullable=False),
        sa.Column('refresh_token', sa.String(length=255), nullable=False),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('ip_address', sa.String(length=45), nullable=True),
        sa.Column('user_agent', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='用户会话表'
    )
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)
    op.create_index(op.f('ix_user_sessions_refresh_token'), 'user_sessions', ['refresh_token'], unique=True)
    
    # 创建股票基本信息表
    op.create_table(
        'stocks',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=True),
        sa.Column('market', sa.String(length=10), nullable=False),
        sa.Column('exchange', sa.String(length=50), nullable=False),
        sa.Column('currency', sa.String(length=10), nullable=False),
        sa.Column('industry', sa.String(length=100), nullable=True),
        sa.Column('sector', sa.String(length=100), nullable=True),
        sa.Column('concept', sa.Text(), nullable=True),
        sa.Column('list_date', sa.Date(), nullable=True),
        sa.Column('delist_date', sa.Date(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_st', sa.Boolean(), nullable=False),
        sa.Column('total_share', sa.Numeric(precision=20, scale=2), nullable=True),
        sa.Column('float_share', sa.Numeric(precision=20, scale=2), nullable=True),
        sa.Column('market_cap', sa.Numeric(precision=20, scale=2), nullable=True),
        sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
        sa.Column('data_source', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        comment='股票基本信息表'
    )
    op.create_index(op.f('ix_stocks_symbol'), 'stocks', ['symbol'], unique=True)
    op.create_index(op.f('ix_stocks_market'), 'stocks', ['market'])
    op.create_index(op.f('ix_stocks_industry'), 'stocks', ['industry'])
    op.create_index(op.f('ix_stocks_is_active'), 'stocks', ['is_active'])
    
    # 创建日线价格数据表
    op.create_table(
        'daily_prices',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('trade_date', sa.Date(), nullable=False),
        sa.Column('open_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('high_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('low_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('close_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('volume', sa.BigInteger(), nullable=True),
        sa.Column('turnover', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('adj_close', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('change_pct', sa.Numeric(precision=8, scale=4), nullable=True),
        sa.Column('turnover_rate', sa.Numeric(precision=8, scale=4), nullable=True),
        sa.Column('is_trading_day', sa.Boolean(), nullable=False),
        sa.Column('is_suspended', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='日线价格数据表'
    )
    op.create_index(op.f('ix_daily_prices_symbol'), 'daily_prices', ['symbol'])
    op.create_index(op.f('ix_daily_prices_trade_date'), 'daily_prices', ['trade_date'])
    op.create_index(op.f('ix_daily_prices_symbol_date'), 'daily_prices', ['symbol', 'trade_date'])
    op.create_unique_constraint('uk_daily_prices_symbol_date', 'daily_prices', ['symbol', 'trade_date'])
    
    # 创建分钟级价格数据表
    op.create_table(
        'minute_prices',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('datetime', sa.DateTime(timezone=True), nullable=False),
        sa.Column('open_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('high_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('low_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('close_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('volume', sa.BigInteger(), nullable=True),
        sa.Column('turnover', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='分钟级价格数据表'
    )
    op.create_index(op.f('ix_minute_prices_symbol'), 'minute_prices', ['symbol'])
    op.create_index(op.f('ix_minute_prices_datetime'), 'minute_prices', ['datetime'])
    op.create_index(op.f('ix_minute_prices_symbol_datetime'), 'minute_prices', ['symbol', 'datetime'])
    op.create_unique_constraint('uk_minute_prices_symbol_datetime', 'minute_prices', ['symbol', 'datetime'])
    
    # 创建Tick数据表
    op.create_table(
        'tick_data',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('stock_id', sa.Integer(), nullable=False),
        sa.Column('symbol', sa.String(length=20), nullable=False),
        sa.Column('datetime', sa.DateTime(timezone=True), nullable=False),
        sa.Column('current_price', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('volume', sa.BigInteger(), nullable=True),
        sa.Column('turnover', sa.Numeric(precision=15, scale=2), nullable=True),
        sa.Column('bid_price_1', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('bid_volume_1', sa.BigInteger(), nullable=True),
        sa.Column('ask_price_1', sa.Numeric(precision=10, scale=3), nullable=True),
        sa.Column('ask_volume_1', sa.BigInteger(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['stock_id'], ['stocks.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        comment='Tick数据表'
    )
    op.create_index(op.f('ix_tick_data_symbol'), 'tick_data', ['symbol'])
    op.create_index(op.f('ix_tick_data_datetime'), 'tick_data', ['datetime'])
    op.create_index(op.f('ix_tick_data_symbol_datetime'), 'tick_data', ['symbol', 'datetime'])
    
    # 设置默认值
    op.execute("ALTER TABLE users ALTER COLUMN subscription_type SET DEFAULT 'free'")
    op.execute("ALTER TABLE users ALTER COLUMN api_quota_daily SET DEFAULT 1000")
    op.execute("ALTER TABLE users ALTER COLUMN api_quota_used_today SET DEFAULT 0")
    op.execute("ALTER TABLE users ALTER COLUMN failed_login_attempts SET DEFAULT 0")
    op.execute("ALTER TABLE users ALTER COLUMN is_active SET DEFAULT true")
    op.execute("ALTER TABLE users ALTER COLUMN is_superuser SET DEFAULT false")
    op.execute("ALTER TABLE users ALTER COLUMN is_verified SET DEFAULT false")
    
    op.execute("ALTER TABLE jqdata_configs ALTER COLUMN is_active SET DEFAULT true")
    op.execute("ALTER TABLE jqdata_configs ALTER COLUMN quota_total SET DEFAULT 10000")
    op.execute("ALTER TABLE jqdata_configs ALTER COLUMN quota_used SET DEFAULT 0")
    op.execute("ALTER TABLE jqdata_configs ALTER COLUMN total_api_calls SET DEFAULT 0")
    op.execute("ALTER TABLE jqdata_configs ALTER COLUMN auth_failure_count SET DEFAULT 0")
    
    op.execute("ALTER TABLE user_sessions ALTER COLUMN is_active SET DEFAULT true")
    
    op.execute("ALTER TABLE stocks ALTER COLUMN currency SET DEFAULT 'CNY'")
    op.execute("ALTER TABLE stocks ALTER COLUMN is_active SET DEFAULT true")
    op.execute("ALTER TABLE stocks ALTER COLUMN is_st SET DEFAULT false")
    op.execute("ALTER TABLE stocks ALTER COLUMN data_source SET DEFAULT 'jqdata'")
    
    op.execute("ALTER TABLE daily_prices ALTER COLUMN is_trading_day SET DEFAULT true")
    op.execute("ALTER TABLE daily_prices ALTER COLUMN is_suspended SET DEFAULT false")


def downgrade() -> None:
    """删除所有表"""
    op.drop_table('tick_data')
    op.drop_table('minute_prices')
    op.drop_table('daily_prices')
    op.drop_table('stocks')
    op.drop_table('user_sessions')
    op.drop_table('jqdata_configs')
    op.drop_table('users')
