"""
强化学习交易服务

实现基于强化学习的智能交易策略优化
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque
import random
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.logging import logger
from app.models.deep_learning import RLAgent, RLTradingSession, RLTrainingEpisode


class DQNNetwork(nn.Module):
    """深度Q网络"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None):
        super().__init__()
        
        if hidden_dims is None:
            hidden_dims = [256, 256, 128]
        
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, state):
        return self.network(state)


class ActorNetwork(nn.Module):
    """Actor网络（用于DDPG等连续动作算法）"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None):
        super().__init__()
        
        if hidden_dims is None:
            hidden_dims = [256, 256]
        
        layers = []
        input_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU()
            ])
            input_dim = hidden_dim
        
        layers.extend([
            nn.Linear(input_dim, action_dim),
            nn.Tanh()  # 动作范围[-1, 1]
        ])
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, state):
        return self.network(state)


class CriticNetwork(nn.Module):
    """Critic网络（用于DDPG等算法）"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dims: List[int] = None):
        super().__init__()
        
        if hidden_dims is None:
            hidden_dims = [256, 256]
        
        self.state_net = nn.Sequential(
            nn.Linear(state_dim, hidden_dims[0]),
            nn.ReLU()
        )
        
        self.action_net = nn.Sequential(
            nn.Linear(action_dim, hidden_dims[0]),
            nn.ReLU()
        )
        
        self.combined_net = nn.Sequential(
            nn.Linear(hidden_dims[0] * 2, hidden_dims[1]),
            nn.ReLU(),
            nn.Linear(hidden_dims[1], 1)
        )
    
    def forward(self, state, action):
        state_features = self.state_net(state)
        action_features = self.action_net(action)
        combined = torch.cat([state_features, action_features], dim=1)
        return self.combined_net(combined)


class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int):
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)


class TradingEnvironment:
    """交易环境"""
    
    def __init__(
        self,
        data: pd.DataFrame,
        initial_capital: float = 100000,
        transaction_cost: float = 0.001,
        lookback_window: int = 20
    ):
        self.data = data
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost
        self.lookback_window = lookback_window
        
        self.reset()
    
    def reset(self):
        """重置环境"""
        self.current_step = self.lookback_window
        self.capital = self.initial_capital
        self.position = 0.0  # 持仓比例 [-1, 1]
        self.portfolio_value = self.initial_capital
        self.trade_history = []
        
        return self._get_state()
    
    def _get_state(self):
        """获取当前状态"""
        if self.current_step >= len(self.data):
            return np.zeros(self.lookback_window * len(self.data.columns) + 3)
        
        # 价格特征（归一化）
        price_data = self.data.iloc[self.current_step - self.lookback_window:self.current_step]
        price_features = price_data.values.flatten()
        
        # 标准化
        if len(price_features) > 0:
            price_features = (price_features - np.mean(price_features)) / (np.std(price_features) + 1e-8)
        
        # 组合状态
        portfolio_features = np.array([
            self.position,  # 当前持仓
            self.portfolio_value / self.initial_capital - 1,  # 收益率
            self.capital / self.initial_capital  # 现金比例
        ])
        
        state = np.concatenate([price_features, portfolio_features])
        return state
    
    def step(self, action):
        """执行动作"""
        if self.current_step >= len(self.data) - 1:
            return self._get_state(), 0, True, {}
        
        # 解析动作（假设动作是目标持仓比例）
        target_position = np.clip(action, -1, 1)
        
        # 计算交易
        position_change = target_position - self.position
        current_price = self.data.iloc[self.current_step]['close']
        
        # 执行交易
        if abs(position_change) > 0.01:  # 最小交易阈值
            trade_value = abs(position_change) * self.portfolio_value
            transaction_cost = trade_value * self.transaction_cost
            
            self.trade_history.append({
                'step': self.current_step,
                'action': target_position,
                'position_change': position_change,
                'price': current_price,
                'cost': transaction_cost
            })
            
            self.capital -= transaction_cost
            self.position = target_position
        
        # 更新时间步
        self.current_step += 1
        
        # 计算收益
        if self.current_step < len(self.data):
            next_price = self.data.iloc[self.current_step]['close']
            price_change = (next_price - current_price) / current_price
            
            # 更新组合价值
            position_pnl = self.position * self.portfolio_value * price_change
            self.portfolio_value += position_pnl
            
            # 计算奖励
            reward = self._calculate_reward(price_change, position_change)
        else:
            reward = 0
        
        # 检查是否结束
        done = self.current_step >= len(self.data) - 1
        
        return self._get_state(), reward, done, {}
    
    def _calculate_reward(self, price_change: float, position_change: float):
        """计算奖励"""
        # 基础收益奖励
        profit_reward = self.position * price_change * 100
        
        # 风险惩罚
        risk_penalty = abs(self.position) * 0.01
        
        # 交易成本惩罚
        transaction_penalty = abs(position_change) * 0.1
        
        # 组合收益奖励
        portfolio_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        portfolio_reward = portfolio_return * 10
        
        total_reward = profit_reward - risk_penalty - transaction_penalty + portfolio_reward
        
        return total_reward


class DQNAgent:
    """DQN智能体"""
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        learning_rate: float = 0.001,
        gamma: float = 0.99,
        epsilon_start: float = 1.0,
        epsilon_end: float = 0.01,
        epsilon_decay: float = 0.995,
        memory_size: int = 10000,
        batch_size: int = 32,
        target_update: int = 100
    ):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.gamma = gamma
        self.epsilon = epsilon_start
        self.epsilon_end = epsilon_end
        self.epsilon_decay = epsilon_decay
        self.batch_size = batch_size
        self.target_update = target_update
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 网络
        self.q_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.target_network = DQNNetwork(state_dim, action_dim).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
        
        # 经验回放
        self.memory = ReplayBuffer(memory_size)
        
        # 更新目标网络
        self.update_target_network()
        
        self.steps = 0
    
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def act(self, state, training=True):
        """选择动作"""
        if training and random.random() < self.epsilon:
            return random.randrange(self.action_dim)
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return q_values.argmax().item()
    
    def remember(self, state, action, reward, next_state, done):
        """存储经验"""
        self.memory.push(state, action, reward, next_state, done)
    
    def replay(self):
        """经验回放学习"""
        if len(self.memory) < self.batch_size:
            return
        
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)
        
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # 更新epsilon
        if self.epsilon > self.epsilon_end:
            self.epsilon *= self.epsilon_decay
        
        # 更新目标网络
        self.steps += 1
        if self.steps % self.target_update == 0:
            self.update_target_network()
        
        return loss.item()


class RLService:
    """强化学习服务"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models_dir = "rl_models"
        os.makedirs(self.models_dir, exist_ok=True)
    
    async def create_agent(
        self,
        user_id: int,
        agent_config: Dict[str, Any],
        db: AsyncSession
    ) -> RLAgent:
        """创建强化学习智能体"""
        try:
            agent = RLAgent(
                user_id=user_id,
                name=agent_config["name"],
                description=agent_config.get("description", ""),
                agent_type=agent_config.get("agent_type", "dqn"),
                environment_type=agent_config.get("environment_type", "trading"),
                state_space_dim=agent_config.get("state_space_dim", 100),
                action_space_dim=agent_config.get("action_space_dim", 3),
                action_space_type=agent_config.get("action_space_type", "discrete"),
                network_architecture=agent_config.get("network_architecture", {}),
                hidden_layers=agent_config.get("hidden_layers", [256, 256, 128]),
                learning_rate=agent_config.get("learning_rate", 0.001),
                batch_size=agent_config.get("batch_size", 32),
                memory_size=agent_config.get("memory_size", 10000),
                epsilon_start=agent_config.get("epsilon_start", 1.0),
                epsilon_end=agent_config.get("epsilon_end", 0.01),
                epsilon_decay=agent_config.get("epsilon_decay", 0.995),
                gamma=agent_config.get("gamma", 0.99),
                reward_function=agent_config.get("reward_function", {}),
                transaction_cost=agent_config.get("transaction_cost", 0.001)
            )
            
            db.add(agent)
            await db.commit()
            await db.refresh(agent)
            
            logger.info(f"创建强化学习智能体成功: {agent.id}")
            return agent
            
        except Exception as e:
            logger.error(f"创建强化学习智能体失败: {e}")
            await db.rollback()
            raise
    
    async def train_agent(
        self,
        agent_id: int,
        training_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """训练强化学习智能体"""
        try:
            # 获取智能体配置
            result = await db.execute(
                select(RLAgent).where(RLAgent.id == agent_id)
            )
            agent_config = result.scalar_one_or_none()
            
            if not agent_config:
                raise ValueError(f"智能体不存在: {agent_id}")
            
            # 更新状态
            agent_config.status = "training"
            await db.commit()
            
            # 准备训练数据
            df = pd.DataFrame(training_data)
            
            # 创建环境
            env = TradingEnvironment(
                data=df,
                initial_capital=100000,
                transaction_cost=agent_config.transaction_cost,
                lookback_window=20
            )
            
            # 创建智能体
            if agent_config.agent_type == "dqn":
                # 离散动作空间：持有、买入、卖出
                action_dim = 3
                rl_agent = DQNAgent(
                    state_dim=agent_config.state_space_dim,
                    action_dim=action_dim,
                    learning_rate=agent_config.learning_rate,
                    gamma=agent_config.gamma,
                    epsilon_start=agent_config.epsilon_start,
                    epsilon_end=agent_config.epsilon_end,
                    epsilon_decay=agent_config.epsilon_decay,
                    memory_size=agent_config.memory_size,
                    batch_size=agent_config.batch_size
                )
            else:
                raise ValueError(f"不支持的智能体类型: {agent_config.agent_type}")
            
            # 训练循环
            num_episodes = training_data.get('num_episodes', 1000)
            episode_rewards = []
            episode_losses = []
            
            for episode in range(num_episodes):
                state = env.reset()
                episode_reward = 0
                episode_loss = 0
                steps = 0
                
                while True:
                    # 选择动作
                    if agent_config.agent_type == "dqn":
                        action_idx = rl_agent.act(state)
                        # 将离散动作转换为连续动作
                        action_map = {0: 0.0, 1: 1.0, 2: -1.0}  # 持有、买入、卖出
                        action = action_map[action_idx]
                    
                    # 执行动作
                    next_state, reward, done, _ = env.step(action)
                    
                    # 存储经验
                    rl_agent.remember(state, action_idx, reward, next_state, done)
                    
                    # 学习
                    if len(rl_agent.memory) > rl_agent.batch_size:
                        loss = rl_agent.replay()
                        if loss:
                            episode_loss += loss
                    
                    state = next_state
                    episode_reward += reward
                    steps += 1
                    
                    if done:
                        break
                
                episode_rewards.append(episode_reward)
                episode_losses.append(episode_loss / max(steps, 1))
                
                # 记录训练回合
                if episode % 100 == 0:
                    training_episode = RLTrainingEpisode(
                        agent_id=agent_id,
                        episode_number=episode,
                        episode_steps=steps,
                        episode_reward=episode_reward,
                        loss=episode_loss / max(steps, 1),
                        epsilon=rl_agent.epsilon,
                        portfolio_value=env.portfolio_value,
                        trades_count=len(env.trade_history)
                    )
                    db.add(training_episode)
                    
                    logger.info(f"Episode {episode}: Reward={episode_reward:.2f}, Loss={episode_loss/max(steps,1):.4f}")
            
            # 保存模型
            model_path = os.path.join(self.models_dir, f"rl_agent_{agent_id}.pth")
            torch.save({
                'q_network_state_dict': rl_agent.q_network.state_dict(),
                'target_network_state_dict': rl_agent.target_network.state_dict(),
                'optimizer_state_dict': rl_agent.optimizer.state_dict(),
                'config': {
                    'state_dim': agent_config.state_space_dim,
                    'action_dim': action_dim,
                    'agent_type': agent_config.agent_type
                }
            }, model_path)
            
            # 计算性能指标
            avg_reward = np.mean(episode_rewards[-100:])  # 最后100个回合的平均奖励
            max_reward = np.max(episode_rewards)
            
            # 更新智能体状态
            agent_config.status = "trained"
            agent_config.training_episodes = num_episodes
            agent_config.average_reward = avg_reward
            agent_config.max_reward = max_reward
            agent_config.model_path = model_path
            agent_config.model_size = os.path.getsize(model_path)
            agent_config.last_trained_at = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "agent_id": agent_id,
                "episodes_trained": num_episodes,
                "average_reward": avg_reward,
                "max_reward": max_reward,
                "final_epsilon": rl_agent.epsilon
            }
            
        except Exception as e:
            logger.error(f"训练强化学习智能体失败: {e}")
            
            # 更新失败状态
            agent_config.status = "failed"
            await db.commit()
            
            return {
                "success": False,
                "error": str(e)
            }


# 全局强化学习服务实例
rl_service = RLService()
