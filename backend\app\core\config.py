"""
核心配置模块

管理应用程序的所有配置项，包括数据库连接、安全设置、第三方服务等
严格遵循类型注解和配置验证
"""

import secrets
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, EmailStr, PostgresDsn, RedisDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用程序配置类"""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_ignore_empty=True,
        extra="ignore",
        case_sensitive=True,
    )

    # =============================================================================
    # 基础配置
    # =============================================================================
    APP_NAME: str = "JQData量化平台"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    TZ: str = "Asia/Shanghai"

    # =============================================================================
    # 数据库配置
    # =============================================================================
    DATABASE_URL: PostgresDsn
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 3600

    # =============================================================================
    # Redis 配置
    # =============================================================================
    REDIS_URL: RedisDsn
    REDIS_MAX_CONNECTIONS: int = 50
    REDIS_SOCKET_TIMEOUT: int = 5
    REDIS_SOCKET_CONNECT_TIMEOUT: int = 5

    # =============================================================================
    # 安全配置
    # =============================================================================
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ENCRYPTION_KEY: str = secrets.token_urlsafe(32)
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    @field_validator("SECRET_KEY", "ENCRYPTION_KEY")
    @classmethod
    def validate_secret_keys(cls, v: str) -> str:
        if len(v) < 32:
            raise ValueError("密钥长度必须至少32位")
        return v

    # =============================================================================
    # CORS 配置
    # =============================================================================
    CORS_ORIGINS: List[AnyHttpUrl] = []
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # =============================================================================
    # JQData 配置
    # =============================================================================
    JQDATA_DEFAULT_QUOTA: int = 10000
    JQDATA_RATE_LIMIT_PER_MINUTE: int = 100
    JQDATA_MAX_RETRY_ATTEMPTS: int = 3
    JQDATA_RETRY_DELAY: int = 1
    JQDATA_TIMEOUT: int = 30

    # =============================================================================
    # 文件上传配置
    # =============================================================================
    UPLOAD_PATH: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["csv", "xlsx", "json"]

    # =============================================================================
    # 缓存配置
    # =============================================================================
    CACHE_TTL: int = 3600  # 1小时
    CACHE_MAX_SIZE: int = 1000
    CACHE_KEY_PREFIX: str = "jqdata"

    # =============================================================================
    # 安全限制配置
    # =============================================================================
    RATE_LIMIT_PER_MINUTE: int = 60
    MAX_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_DURATION: int = 300  # 5分钟
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SYMBOLS: bool = False

    # =============================================================================
    # 邮件配置
    # =============================================================================
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    SMTP_SSL: bool = False
    EMAIL_FROM: Optional[EmailStr] = None
    EMAIL_FROM_NAME: Optional[str] = None

    # =============================================================================
    # 日志配置
    # =============================================================================
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5
    LOG_FORMAT: str = "json"

    # =============================================================================
    # Celery 配置
    # =============================================================================
    CELERY_BROKER_URL: RedisDsn
    CELERY_RESULT_BACKEND: RedisDsn
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "Asia/Shanghai"
    CELERY_ENABLE_UTC: bool = False

    # =============================================================================
    # 监控配置
    # =============================================================================
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    HEALTH_CHECK_INTERVAL: int = 30
    PERFORMANCE_MONITORING: bool = True

    # =============================================================================
    # 数据更新配置
    # =============================================================================
    DATA_UPDATE_HOUR: int = 18
    DATA_UPDATE_MINUTE: int = 30
    WEEKEND_UPDATE_ENABLED: bool = False
    HOLIDAY_UPDATE_ENABLED: bool = False

    # =============================================================================
    # 回测配置
    # =============================================================================
    BACKTEST_MAX_DURATION_DAYS: int = 3650  # 10年
    BACKTEST_DEFAULT_INITIAL_CAPITAL: float = 100000.0
    BACKTEST_MAX_CONCURRENT_JOBS: int = 5
    BACKTEST_RESULT_CACHE_HOURS: int = 24

    # =============================================================================
    # WebSocket 配置
    # =============================================================================
    WEBSOCKET_MAX_CONNECTIONS: int = 1000
    WEBSOCKET_HEARTBEAT_INTERVAL: int = 30
    WEBSOCKET_MESSAGE_MAX_SIZE: int = 1024 * 1024  # 1MB

    # =============================================================================
    # 第三方服务配置
    # =============================================================================
    SENTRY_DSN: Optional[str] = None
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    AWS_S3_BUCKET: Optional[str] = None

    # =============================================================================
    # 通知配置
    # =============================================================================
    DINGTALK_WEBHOOK: Optional[str] = None
    DINGTALK_SECRET: Optional[str] = None
    WECHAT_WEBHOOK: Optional[str] = None

    # =============================================================================
    # 功能开关
    # =============================================================================
    FEATURE_SOCIAL_TRADING: bool = False
    FEATURE_ML_PREDICTIONS: bool = False
    FEATURE_OPTIONS_PRICING: bool = False
    FEATURE_NEWS_ANALYSIS: bool = False
    FEATURE_PORTFOLIO_OPTIMIZATION: bool = True

    # =============================================================================
    # 计算属性
    # =============================================================================
    @property
    def database_url_sync(self) -> str:
        """同步数据库连接URL"""
        return str(self.DATABASE_URL).replace("postgresql://", "postgresql+psycopg2://")

    @property
    def database_url_async(self) -> str:
        """异步数据库连接URL"""
        return str(self.DATABASE_URL).replace("postgresql://", "postgresql+asyncpg://")

    @property
    def redis_url_str(self) -> str:
        """Redis连接URL字符串"""
        return str(self.REDIS_URL)

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
