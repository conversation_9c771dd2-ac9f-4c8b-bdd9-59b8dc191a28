"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extend = exports.Runtime = exports.register = exports.Chart = void 0;
var chart_1 = require("./chart");
Object.defineProperty(exports, "Chart", { enumerable: true, get: function () { return chart_1.Chart; } });
var library_1 = require("./library");
Object.defineProperty(exports, "register", { enumerable: true, get: function () { return library_1.register; } });
var runtime_1 = require("./runtime");
Object.defineProperty(exports, "Runtime", { enumerable: true, get: function () { return runtime_1.Runtime; } });
var extend_1 = require("./extend");
Object.defineProperty(exports, "extend", { enumerable: true, get: function () { return extend_1.extend; } });
//# sourceMappingURL=index.js.map