# JQData 量化数据平台开发规范

## 📋 项目概述

基于聚宽JQData API开发的企业级量化数据获取、处理和分析平台，支持A股、港股、美股、期货等多市场数据，提供模块化策略开发和多用户协作功能。

## 🛠️ 技术栈

### 后端 (Python 3.11+)
- **框架**: FastAPI + Uvicorn
- **数据库**: PostgreSQL + Redis
- **ORM**: SQLAlchemy 2.0 + Alembic
- **数据处理**: Pandas + NumPy + TA-Lib
- **任务队列**: Celery + Redis
- **认证**: FastAPI-Users + JWT

### 前端 (TypeScript + React)
- **框架**: Next.js 14 + React 18
- **UI库**: Ant Design 5 + Tailwind CSS
- **状态管理**: Zustand + TanStack Query
- **图表**: TradingView Charting + ECharts
- **拖拽**: React DnD + React Flow

## 📚 开发规范

### Python 开发规范 (严格遵循 PEP 8)

#### 代码格式化
```python
# 使用 4 空格缩进，禁用 Tab
def calculate_moving_average(data: pd.DataFrame, window: int = 20) -> pd.Series:
    """计算移动平均线
    
    Args:
        data: 价格数据DataFrame
        window: 窗口期，默认20
        
    Returns:
        移动平均线Series
    """
    return data['close'].rolling(window=window).mean()
```

#### 命名规范
```python
# 变量和函数：snake_case
stock_price_data = get_stock_data()
def fetch_market_data() -> Dict[str, Any]:
    pass

# 类名：PascalCase
class DataProcessor:
    pass

# 常量：UPPER_SNAKE_CASE
MAX_RETRY_COUNT = 3
API_BASE_URL = "https://api.joinquant.com"

# 私有属性：前缀下划线
class Strategy:
    def __init__(self):
        self._position = 0
        self.__secret_key = "xxx"
```

#### 类型注解 (必须)
```python
from typing import List, Dict, Optional, Union, Tuple
import pandas as pd

def process_stock_data(
    symbols: List[str],
    start_date: str,
    end_date: Optional[str] = None
) -> Dict[str, pd.DataFrame]:
    """处理股票数据"""
    pass

# 复杂类型定义
StockData = Dict[str, Union[float, int, str]]
PortfolioWeights = Dict[str, float]
```

#### 文档字符串 (Google Style)
```python
def backtest_strategy(
    strategy: Strategy,
    start_date: str,
    end_date: str,
    initial_capital: float = 100000.0
) -> BacktestResult:
    """执行策略回测
    
    Args:
        strategy: 交易策略实例
        start_date: 回测开始日期，格式 'YYYY-MM-DD'
        end_date: 回测结束日期，格式 'YYYY-MM-DD'
        initial_capital: 初始资金，默认10万
        
    Returns:
        BacktestResult: 回测结果对象，包含收益率、夏普比率等指标
        
    Raises:
        ValueError: 当日期格式不正确时
        DataNotFoundError: 当数据不存在时
        
    Example:
        >>> strategy = MovingAverageStrategy(short=5, long=20)
        >>> result = backtest_strategy(strategy, '2023-01-01', '2023-12-31')
        >>> print(f"总收益率: {result.total_return:.2%}")
    """
    pass
```

#### 异常处理
```python
# 自定义异常
class JQDataError(Exception):
    """JQData相关异常基类"""
    pass

class DataFetchError(JQDataError):
    """数据获取异常"""
    pass

# 异常处理示例
def fetch_stock_data(symbol: str) -> pd.DataFrame:
    try:
        data = jq.get_price(symbol)
        if data.empty:
            raise DataFetchError(f"股票 {symbol} 数据为空")
        return data
    except Exception as e:
        logger.error(f"获取股票数据失败: {symbol}, 错误: {e}")
        raise DataFetchError(f"无法获取股票 {symbol} 的数据") from e
```

### TypeScript 开发规范

#### 接口定义
```typescript
// API响应接口
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 股票数据接口
interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: Date;
}

// 策略节点接口
interface StrategyNode {
  id: string;
  type: 'data' | 'indicator' | 'signal' | 'risk';
  name: string;
  config: Record<string, any>;
  position: { x: number; y: number };
  inputs: string[];
  outputs: string[];
}
```

#### 组件规范
```typescript
// React组件
interface StockChartProps {
  data: StockData[];
  height?: number;
  showVolume?: boolean;
  onSymbolChange?: (symbol: string) => void;
}

const StockChart: React.FC<StockChartProps> = ({
  data,
  height = 400,
  showVolume = true,
  onSymbolChange
}) => {
  // 组件实现
  return <div>...</div>;
};

export default StockChart;
```

##### 前端JQData配置界面
```typescript
// frontend/src/components/JQDataConfig.tsx
interface JQDataConfigProps {
  onConfigSaved?: () => void;
}

const JQDataConfig: React.FC<JQDataConfigProps> = ({ onConfigSaved }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [config, setConfig] = useState<any>(null);

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const response = await api.get('/config/jqdata/config');
      setConfig(response.data);
      if (response.data.configured) {
        form.setFieldsValue({
          username: response.data.username
        });
      }
    } catch (error) {
      message.error('加载配置失败');
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      await api.post('/config/jqdata/config', values);
      message.success('JQData配置保存成功');
      loadConfig();
      onConfigSaved?.();
    } catch (error: any) {
      message.error(error.response?.data?.detail || '保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    try {
      await api.post('/config/jqdata/test-connection');
      message.success('连接测试成功');
    } catch (error: any) {
      message.error(error.response?.data?.detail || '连接测试失败');
    } finally {
      setTesting(false);
    }
  };

  const handleDelete = async () => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除JQData配置吗？删除后将无法获取数据。',
      onOk: async () => {
        try {
          await api.delete('/config/jqdata/config');
          message.success('配置删除成功');
          setConfig(null);
          form.resetFields();
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  return (
    <Card title="JQData 配置" className="jqdata-config">
      {config?.configured && (
        <Alert
          message="配置状态"
          description={
            <div>
              <p>用户名: {config.username}</p>
              <p>配额使用: {config.quota_used} / {config.quota_limit}</p>
              <p>最后使用: {config.last_used ? dayjs(config.last_used).format('YYYY-MM-DD HH:mm:ss') : '未使用'}</p>
            </div>
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        autoComplete="off"
      >
        <Form.Item
          label="JQData 用户名"
          name="username"
          rules={[
            { required: true, message: '请输入JQData用户名' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]}
        >
          <Input
            placeholder="请输入JQData注册邮箱"
            prefix={<UserOutlined />}
          />
        </Form.Item>

        <Form.Item
          label="JQData 密码"
          name="password"
          rules={[
            { required: true, message: '请输入JQData密码' },
            { min: 6, message: '密码至少6位' }
          ]}
        >
          <Input.Password
            placeholder="请输入JQData密码"
            prefix={<LockOutlined />}
          />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
            >
              保存配置
            </Button>

            {config?.configured && (
              <>
                <Button
                  onClick={handleTestConnection}
                  loading={testing}
                  icon={<ApiOutlined />}
                >
                  测试连接
                </Button>

                <Button
                  danger
                  onClick={handleDelete}
                  icon={<DeleteOutlined />}
                >
                  删除配置
                </Button>
              </>
            )}
          </Space>
        </Form.Item>
      </Form>

      <Divider />

      <div className="config-help">
        <Title level={5}>配置说明</Title>
        <ul>
          <li>请使用您在 <a href="https://www.joinquant.com" target="_blank" rel="noopener noreferrer">聚宽官网</a> 注册的账号</li>
          <li>密码将被加密存储，仅用于数据获取认证</li>
          <li>免费账户每日有API调用次数限制</li>
          <li>建议定期测试连接确保配置有效</li>
        </ul>
      </div>
    </Card>
  );
};

// 系统设置页面集成
const SystemSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('jqdata');

  const tabItems = [
    {
      key: 'jqdata',
      label: 'JQData配置',
      children: <JQDataConfig />
    },
    {
      key: 'database',
      label: '数据库配置',
      children: <DatabaseConfig />
    },
    {
      key: 'notification',
      label: '通知设置',
      children: <NotificationConfig />
    }
  ];

  return (
    <div className="system-settings">
      <PageHeader title="系统设置" />
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />
    </div>
  );
};
```

## 🏗️ 项目结构

```
JQData/
├── backend/                    # Python后端
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI应用入口
│   │   ├── core/              # 核心配置
│   │   │   ├── config.py      # 配置管理
│   │   │   ├── database.py    # 数据库连接
│   │   │   └── security.py    # 安全认证
│   │   ├── api/               # API路由
│   │   │   ├── v1/
│   │   │   │   ├── auth.py    # 认证接口
│   │   │   │   ├── data.py    # 数据接口
│   │   │   │   ├── strategy.py # 策略接口
│   │   │   │   └── backtest.py # 回测接口
│   │   ├── models/            # 数据模型
│   │   │   ├── user.py        # 用户模型
│   │   │   ├── stock.py       # 股票模型
│   │   │   └── strategy.py    # 策略模型
│   │   ├── services/          # 业务逻辑
│   │   │   ├── jqdata.py      # JQData服务
│   │   │   ├── backtest.py    # 回测服务
│   │   │   └── strategy.py    # 策略服务
│   │   ├── utils/             # 工具函数
│   │   │   ├── indicators.py  # 技术指标
│   │   │   ├── risk.py        # 风险管理
│   │   │   └── helpers.py     # 辅助函数
│   │   └── tests/             # 测试文件
│   ├── requirements.txt       # Python依赖
│   ├── pyproject.toml        # 项目配置
│   └── Dockerfile            # Docker配置
├── frontend/                  # TypeScript前端
│   ├── src/
│   │   ├── components/        # 通用组件
│   │   ├── pages/            # 页面组件
│   │   ├── hooks/            # 自定义Hooks
│   │   ├── services/         # API服务
│   │   ├── types/            # 类型定义
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── package.json          # Node.js依赖
│   ├── tsconfig.json         # TypeScript配置
│   └── next.config.js        # Next.js配置
├── docker-compose.yml         # 开发环境
├── docker-compose.prod.yml    # 生产环境
├── .env.example              # 环境变量示例
├── .gitignore               # Git忽略文件
└── AIreadme.md              # 本文档
```

## � 环境变量配置

### .env.example 文件内容
```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/jqdata
REDIS_URL=redis://localhost:6379/0

# JWT密钥配置
SECRET_KEY=your-super-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 密码加密密钥 (用于JQData密码加密)
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 应用配置
APP_NAME=JQData量化平台
APP_VERSION=1.0.0
DEBUG=false
CORS_ORIGINS=["http://localhost:3000"]

# JQData配置 (可选，用于系统级配置)
JQDATA_DEFAULT_QUOTA=10000
JQDATA_RATE_LIMIT=100  # 每分钟最大请求数

# 邮件配置 (用于通知)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 文件存储配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********  # 10MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# 缓存配置
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000

# 安全配置
RATE_LIMIT_PER_MINUTE=60
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=300  # 5分钟

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 安全配置类
```python
# app/core/config.py
from pydantic_settings import BaseSettings
from typing import List, Optional
import secrets

class Settings(BaseSettings):
    """应用配置"""

    # 基础配置
    APP_NAME: str = "JQData量化平台"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False

    # 数据库配置
    DATABASE_URL: str
    REDIS_URL: str

    # 安全配置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ENCRYPTION_KEY: str = secrets.token_urlsafe(32)
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS配置
    CORS_ORIGINS: List[str] = ["http://localhost:3000"]

    # JQData配置
    JQDATA_DEFAULT_QUOTA: int = 10000
    JQDATA_RATE_LIMIT: int = 100

    # 文件上传配置
    UPLOAD_PATH: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB

    # 缓存配置
    CACHE_TTL: int = 3600
    CACHE_MAX_SIZE: int = 1000

    # 安全限制
    RATE_LIMIT_PER_MINUTE: int = 60
    MAX_LOGIN_ATTEMPTS: int = 5
    ACCOUNT_LOCKOUT_DURATION: int = 300

    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()

# 验证关键配置
def validate_settings():
    """验证配置有效性"""
    if not settings.DATABASE_URL:
        raise ValueError("DATABASE_URL 必须设置")

    if not settings.REDIS_URL:
        raise ValueError("REDIS_URL 必须设置")

    if len(settings.SECRET_KEY) < 32:
        raise ValueError("SECRET_KEY 长度至少32位")

    if len(settings.ENCRYPTION_KEY) < 32:
        raise ValueError("ENCRYPTION_KEY 长度至少32位")

# 应用启动时验证
validate_settings()
```

### 安全中间件
```python
# app/middleware/security.py
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import time
from collections import defaultdict
import asyncio

class SecurityMiddleware:
    """安全中间件"""

    def __init__(self):
        self.request_counts = defaultdict(list)
        self.failed_attempts = defaultdict(int)
        self.locked_accounts = {}

    async def __call__(self, request: Request, call_next):
        # IP限流检查
        client_ip = self._get_client_ip(request)
        if not await self._check_rate_limit(client_ip):
            return JSONResponse(
                status_code=429,
                content={"detail": "请求过于频繁，请稍后再试"}
            )

        # 账户锁定检查
        if request.url.path.startswith("/auth/login"):
            user_id = await self._extract_user_id(request)
            if user_id and await self._is_account_locked(user_id):
                return JSONResponse(
                    status_code=423,
                    content={"detail": "账户已被锁定，请稍后再试"}
                )

        response = await call_next(request)

        # 记录失败的登录尝试
        if (request.url.path.startswith("/auth/login") and
            response.status_code == 401):
            user_id = await self._extract_user_id(request)
            if user_id:
                await self._record_failed_attempt(user_id)

        return response

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return forwarded.split(",")[0].strip()
        return request.client.host

    async def _check_rate_limit(self, client_ip: str) -> bool:
        """检查IP限流"""
        now = time.time()
        minute_ago = now - 60

        # 清理过期记录
        self.request_counts[client_ip] = [
            timestamp for timestamp in self.request_counts[client_ip]
            if timestamp > minute_ago
        ]

        # 检查是否超过限制
        if len(self.request_counts[client_ip]) >= settings.RATE_LIMIT_PER_MINUTE:
            return False

        # 记录当前请求
        self.request_counts[client_ip].append(now)
        return True
```
```

## �🔧 开发环境配置

### 1. 环境要求
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose

### 2. 本地开发设置
```bash
# 克隆项目
git clone <repository-url>
cd JQData

# 后端环境
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 前端环境
cd ../frontend
npm install

# 启动开发环境
docker-compose up -d  # 启动数据库
cd backend && uvicorn app.main:app --reload
cd frontend && npm run dev
```

### 3. 代码质量工具
```bash
# Python代码检查
pip install black isort flake8 mypy pytest
black .                    # 代码格式化
isort .                    # 导入排序
flake8 .                   # 代码检查
mypy .                     # 类型检查
pytest                     # 运行测试

# TypeScript代码检查
npm install -D eslint prettier @typescript-eslint/parser
npm run lint               # 代码检查
npm run format             # 代码格式化
npm run type-check         # 类型检查
npm run test               # 运行测试
```

## 🚀 详细功能规划

### 1. 数据获取与管理模块

#### 1.1 JQData API集成与配置管理

##### JQData账号配置
```python
# app/models/system_config.py
class JQDataConfig(Base):
    """JQData配置模型"""
    __tablename__ = "jqdata_configs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"))
    username: Mapped[str] = mapped_column(String(100), nullable=False)
    password: Mapped[str] = mapped_column(String(255), nullable=False)  # 加密存储
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    quota_used: Mapped[int] = mapped_column(Integer, default=0)
    quota_limit: Mapped[int] = mapped_column(Integer, default=10000)
    last_used: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

# app/services/jqdata_service.py
class JQDataService:
    """JQData服务类"""

    def __init__(self):
        self.auth_cache = {}  # 缓存认证信息

    async def authenticate_user(self, user_id: int) -> bool:
        """用户JQData认证"""
        config = await self._get_user_config(user_id)
        if not config:
            raise ValueError("请先配置JQData账号信息")

        try:
            # 解密密码
            decrypted_password = self._decrypt_password(config.password)

            # JQData认证
            import jqdatasdk as jq
            jq.auth(config.username, decrypted_password)

            # 更新最后使用时间
            await self._update_last_used(config.id)

            # 缓存认证状态
            self.auth_cache[user_id] = {
                'authenticated': True,
                'timestamp': datetime.utcnow(),
                'config_id': config.id
            }

            return True

        except Exception as e:
            logger.error(f"JQData认证失败: {e}")
            return False

    def _encrypt_password(self, password: str) -> str:
        """加密密码"""
        from cryptography.fernet import Fernet
        key = settings.ENCRYPTION_KEY.encode()
        f = Fernet(key)
        return f.encrypt(password.encode()).decode()

    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        from cryptography.fernet import Fernet
        key = settings.ENCRYPTION_KEY.encode()
        f = Fernet(key)
        return f.decrypt(encrypted_password.encode()).decode()
```

##### 用户配置API接口
```python
# app/api/v1/config.py
@router.post("/jqdata/config")
async def save_jqdata_config(
    config: JQDataConfigCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """保存JQData配置"""
    # 验证账号有效性
    try:
        import jqdatasdk as jq
        jq.auth(config.username, config.password)
        jq.logout()  # 验证后立即登出
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"JQData账号验证失败: {str(e)}")

    # 加密密码后保存
    service = JQDataService()
    encrypted_password = service._encrypt_password(config.password)

    # 检查是否已存在配置
    existing_config = await db.execute(
        select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
    )
    existing = existing_config.scalar_one_or_none()

    if existing:
        # 更新现有配置
        existing.username = config.username
        existing.password = encrypted_password
        existing.updated_at = datetime.utcnow()
        await db.commit()
        return {"message": "JQData配置更新成功"}
    else:
        # 创建新配置
        new_config = JQDataConfig(
            user_id=current_user.id,
            username=config.username,
            password=encrypted_password
        )
        db.add(new_config)
        await db.commit()
        return {"message": "JQData配置保存成功"}

@router.get("/jqdata/config")
async def get_jqdata_config(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取JQData配置（不返回密码）"""
    result = await db.execute(
        select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
    )
    config = result.scalar_one_or_none()

    if not config:
        return {"configured": False}

    return {
        "configured": True,
        "username": config.username,
        "quota_used": config.quota_used,
        "quota_limit": config.quota_limit,
        "last_used": config.last_used,
        "is_active": config.is_active
    }

@router.delete("/jqdata/config")
async def delete_jqdata_config(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除JQData配置"""
    result = await db.execute(
        select(JQDataConfig).where(JQDataConfig.user_id == current_user.id)
    )
    config = result.scalar_one_or_none()

    if config:
        await db.delete(config)
        await db.commit()
        return {"message": "JQData配置删除成功"}

    raise HTTPException(status_code=404, detail="配置不存在")

@router.post("/jqdata/test-connection")
async def test_jqdata_connection(
    current_user: User = Depends(get_current_user)
):
    """测试JQData连接"""
    service = JQDataService()
    success = await service.authenticate_user(current_user.id)

    if success:
        return {"status": "success", "message": "JQData连接测试成功"}
    else:
        raise HTTPException(status_code=400, detail="JQData连接测试失败")
```

##### 支持的数据类型
```python
# 股票基本信息 (get_all_securities)
# 日线行情数据 (get_price)
# 分钟级行情数据 (get_price, frequency='minute')
# Tick级数据 (get_ticks)
# 财务数据 (get_fundamentals)
# 除权除息数据 (get_extras)
# 指数成分股 (get_index_stocks)
# 行业分类数据 (get_industry_stocks)
# 概念板块数据 (get_concept_stocks)
# 宏观经济数据 (macro模块)
```

#### 1.2 多市场数据支持
- **A股市场**: 沪深主板、创业板、科创板、北交所
- **港股市场**: 恒生指数成分股、红筹股、H股
- **美股市场**: 纳斯达克、纽交所主要股票
- **期货市场**: 商品期货、金融期货、期权合约
- **基金市场**: ETF、LOF、分级基金、货币基金

#### 1.3 数据存储架构
```sql
-- 股票基本信息表
CREATE TABLE stocks (
    symbol VARCHAR(20) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    market VARCHAR(10) NOT NULL,
    industry VARCHAR(50),
    list_date DATE,
    delist_date DATE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 日线行情数据表 (分区表)
CREATE TABLE daily_prices (
    symbol VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    turnover DECIMAL(15,2),
    PRIMARY KEY (symbol, trade_date)
) PARTITION BY RANGE (trade_date);

-- 分钟级数据表
CREATE TABLE minute_prices (
    symbol VARCHAR(20) NOT NULL,
    datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    PRIMARY KEY (symbol, datetime)
) PARTITION BY RANGE (datetime);
```

#### 1.4 数据更新策略
- **实时数据**: WebSocket推送 + Redis缓存
- **历史数据**: 定时任务批量更新
- **增量更新**: 基于最后更新时间的差异同步
- **数据校验**: 价格异常检测、成交量合理性验证

### 2. 技术指标计算库

#### 2.1 趋势指标
```python
# 移动平均线系列
def simple_moving_average(data: pd.Series, window: int) -> pd.Series:
    """简单移动平均线 SMA"""

def exponential_moving_average(data: pd.Series, window: int) -> pd.Series:
    """指数移动平均线 EMA"""

def weighted_moving_average(data: pd.Series, window: int) -> pd.Series:
    """加权移动平均线 WMA"""

# 趋势线指标
def bollinger_bands(data: pd.Series, window: int = 20, std: float = 2) -> Dict:
    """布林带指标"""

def parabolic_sar(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """抛物线转向指标 SAR"""

def ichimoku_cloud(high: pd.Series, low: pd.Series, close: pd.Series) -> Dict:
    """一目均衡表"""
```

#### 2.2 动量指标
```python
def rsi(data: pd.Series, window: int = 14) -> pd.Series:
    """相对强弱指标 RSI"""

def stochastic_oscillator(high: pd.Series, low: pd.Series, close: pd.Series) -> Dict:
    """随机振荡器 KD"""

def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """威廉指标 %R"""

def commodity_channel_index(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """商品通道指标 CCI"""
```

#### 2.3 成交量指标
```python
def on_balance_volume(close: pd.Series, volume: pd.Series) -> pd.Series:
    """能量潮指标 OBV"""

def volume_price_trend(close: pd.Series, volume: pd.Series) -> pd.Series:
    """量价趋势指标 VPT"""

def accumulation_distribution_line(high: pd.Series, low: pd.Series,
                                 close: pd.Series, volume: pd.Series) -> pd.Series:
    """累积/派发线 A/D Line"""
```

#### 2.4 波动率指标
```python
def average_true_range(high: pd.Series, low: pd.Series, close: pd.Series) -> pd.Series:
    """真实波动幅度均值 ATR"""

def volatility_index(close: pd.Series, window: int = 20) -> pd.Series:
    """波动率指数"""

def historical_volatility(close: pd.Series, window: int = 20) -> pd.Series:
    """历史波动率"""
```

### 3. 模块化策略开发系统

#### 3.1 策略节点类型

##### 数据源节点
```typescript
interface DataSourceNode {
  type: 'data_source';
  config: {
    symbol: string;
    market: 'A' | 'HK' | 'US' | 'FUTURES';
    dataType: 'price' | 'volume' | 'fundamental';
    frequency: 'daily' | 'minute' | 'tick';
    period: number; // 历史数据天数
  };
}
```

##### 技术指标节点
```typescript
interface IndicatorNode {
  type: 'indicator';
  config: {
    indicator: 'SMA' | 'EMA' | 'RSI' | 'MACD' | 'BOLL';
    parameters: Record<string, number>;
    inputField: 'close' | 'high' | 'low' | 'volume';
  };
}
```

##### 信号生成节点
```typescript
interface SignalNode {
  type: 'signal';
  config: {
    condition: 'cross_above' | 'cross_below' | 'greater_than' | 'less_than';
    threshold?: number;
    compareField?: string;
  };
}
```

##### 风险管理节点
```typescript
interface RiskNode {
  type: 'risk';
  config: {
    riskType: 'stop_loss' | 'take_profit' | 'position_size' | 'max_drawdown';
    value: number;
    unit: 'percent' | 'absolute' | 'atr_multiple';
  };
}
```

#### 3.2 策略执行引擎
```python
class StrategyEngine:
    """策略执行引擎"""

    def __init__(self, strategy_config: Dict):
        self.nodes = self._parse_nodes(strategy_config)
        self.connections = self._parse_connections(strategy_config)
        self.data_cache = {}

    def execute(self, start_date: str, end_date: str) -> BacktestResult:
        """执行策略回测"""

    def _execute_node(self, node: StrategyNode, inputs: Dict) -> Any:
        """执行单个节点"""

    def _validate_strategy(self) -> bool:
        """验证策略配置的有效性"""
```

### 4. 回测系统

#### 4.1 回测引擎核心
```python
class BacktestEngine:
    """回测引擎"""

    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.trades = []
        self.performance_metrics = {}

    def run_backtest(self, strategy: Strategy, start_date: str, end_date: str) -> BacktestResult:
        """运行回测"""

    def execute_trade(self, symbol: str, quantity: int, price: float, trade_type: str):
        """执行交易"""

    def calculate_performance(self) -> Dict:
        """计算绩效指标"""
```

#### 4.2 绩效指标计算
```python
def calculate_returns(prices: pd.Series) -> pd.Series:
    """计算收益率"""

def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.03) -> float:
    """计算夏普比率"""

def calculate_max_drawdown(equity_curve: pd.Series) -> float:
    """计算最大回撤"""

def calculate_calmar_ratio(returns: pd.Series) -> float:
    """计算卡玛比率"""

def calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.03) -> float:
    """计算索提诺比率"""

def calculate_information_ratio(returns: pd.Series, benchmark_returns: pd.Series) -> float:
    """计算信息比率"""
```

#### 4.3 风险管理模块
```python
class RiskManager:
    """风险管理器"""

    def __init__(self, max_position_size: float = 0.1, max_drawdown: float = 0.2):
        self.max_position_size = max_position_size
        self.max_drawdown = max_drawdown

    def check_position_size(self, symbol: str, quantity: int, price: float) -> bool:
        """检查仓位大小"""

    def check_stop_loss(self, symbol: str, current_price: float) -> bool:
        """检查止损条件"""

    def check_take_profit(self, symbol: str, current_price: float) -> bool:
        """检查止盈条件"""
```

### 5. 数据可视化系统

#### 5.1 K线图组件
```typescript
interface CandlestickChartProps {
  data: OHLCV[];
  indicators?: IndicatorData[];
  volume?: boolean;
  height?: number;
  theme?: 'light' | 'dark';
  onCrosshairMove?: (data: any) => void;
}

const CandlestickChart: React.FC<CandlestickChartProps> = ({
  data,
  indicators = [],
  volume = true,
  height = 400,
  theme = 'light',
  onCrosshairMove
}) => {
  // TradingView Lightweight Charts 实现
};
```

#### 5.2 技术指标图表
```typescript
const IndicatorChart: React.FC<{
  type: 'RSI' | 'MACD' | 'STOCH';
  data: IndicatorData[];
  height?: number;
}> = ({ type, data, height = 150 }) => {
  // 根据指标类型渲染不同图表
};
```

#### 5.3 回测结果展示
```typescript
const BacktestResults: React.FC<{
  result: BacktestResult;
}> = ({ result }) => {
  return (
    <div className="backtest-results">
      <PerformanceMetrics metrics={result.metrics} />
      <EquityCurve data={result.equity_curve} />
      <TradesList trades={result.trades} />
      <DrawdownChart data={result.drawdown} />
    </div>
  );
};
```

### 6. 用户管理与权限系统

#### 6.1 用户模型
```python
class User(SQLAlchemyBaseUserTable[int], Base):
    """用户模型"""
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    email: Mapped[str] = mapped_column(String(320), unique=True, index=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    hashed_password: Mapped[str] = mapped_column(String(1024))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False)

    # 扩展字段
    subscription_type: Mapped[str] = mapped_column(String(20), default='free')
    api_quota: Mapped[int] = mapped_column(Integer, default=1000)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
```

#### 6.2 权限控制
```python
class PermissionChecker:
    """权限检查器"""

    @staticmethod
    def check_data_access(user: User, market: str) -> bool:
        """检查数据访问权限"""

    @staticmethod
    def check_api_quota(user: User) -> bool:
        """检查API配额"""

    @staticmethod
    def check_strategy_limit(user: User) -> bool:
        """检查策略数量限制"""
```

### 7. API接口设计

#### 7.1 数据接口
```python
@router.get("/stocks/{symbol}/price")
async def get_stock_price(
    symbol: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    frequency: str = "daily",
    current_user: User = Depends(get_current_user)
):
    """获取股票价格数据"""

@router.get("/markets/{market}/stocks")
async def get_market_stocks(
    market: str,
    industry: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """获取市场股票列表"""
```

#### 7.2 策略接口
```python
@router.post("/strategies")
async def create_strategy(
    strategy: StrategyCreate,
    current_user: User = Depends(get_current_user)
):
    """创建策略"""

@router.post("/strategies/{strategy_id}/backtest")
async def run_backtest(
    strategy_id: int,
    backtest_config: BacktestConfig,
    current_user: User = Depends(get_current_user)
):
    """运行策略回测"""
```

### 8. 实时数据推送

#### 8.1 WebSocket服务
```python
class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """建立连接"""

    async def disconnect(self, websocket: WebSocket):
        """断开连接"""

    async def broadcast_price_update(self, symbol: str, price_data: Dict):
        """广播价格更新"""
```

#### 8.2 数据订阅系统
```typescript
class DataSubscription {
  private ws: WebSocket;
  private subscriptions: Set<string> = new Set();

  subscribe(symbol: string, callback: (data: any) => void) {
    // 订阅股票数据
  }

  unsubscribe(symbol: string) {
    // 取消订阅
  }
}
```

### 9. 高级分析功能

#### 9.1 因子分析系统
```python
class FactorAnalyzer:
    """因子分析器"""

    def __init__(self):
        self.factors = {}

    def calculate_alpha_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Alpha因子"""
        factors = {}

        # 技术因子
        factors['momentum_5d'] = data['close'].pct_change(5)
        factors['momentum_20d'] = data['close'].pct_change(20)
        factors['volatility_20d'] = data['close'].rolling(20).std()
        factors['rsi_14d'] = self._calculate_rsi(data['close'], 14)

        # 成交量因子
        factors['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
        factors['price_volume_corr'] = data['close'].rolling(20).corr(data['volume'])

        # 价格因子
        factors['price_to_ma20'] = data['close'] / data['close'].rolling(20).mean()
        factors['high_low_ratio'] = data['high'] / data['low']

        return pd.DataFrame(factors)

    def factor_exposure_analysis(self, returns: pd.Series, factors: pd.DataFrame) -> Dict:
        """因子暴露分析"""

    def ic_analysis(self, factors: pd.DataFrame, forward_returns: pd.Series) -> pd.DataFrame:
        """IC分析 (Information Coefficient)"""
```

#### 9.2 投资组合优化
```python
class PortfolioOptimizer:
    """投资组合优化器"""

    def mean_variance_optimization(self, expected_returns: pd.Series,
                                 cov_matrix: pd.DataFrame,
                                 risk_aversion: float = 1.0) -> pd.Series:
        """均值方差优化"""

    def risk_parity_optimization(self, cov_matrix: pd.DataFrame) -> pd.Series:
        """风险平价优化"""

    def black_litterman_optimization(self, market_caps: pd.Series,
                                   views: Dict,
                                   confidence: Dict) -> pd.Series:
        """Black-Litterman模型优化"""

    def hierarchical_risk_parity(self, returns: pd.DataFrame) -> pd.Series:
        """层次风险平价 (HRP)"""
```

#### 9.3 风险模型
```python
class RiskModel:
    """风险模型"""

    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算VaR (Value at Risk)"""

    def calculate_cvar(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算CVaR (Conditional Value at Risk)"""

    def monte_carlo_simulation(self, returns: pd.Series, days: int = 252) -> pd.DataFrame:
        """蒙特卡洛模拟"""

    def stress_testing(self, portfolio: pd.Series, scenarios: Dict) -> Dict:
        """压力测试"""
```

### 10. 机器学习集成

#### 10.1 预测模型
```python
class PredictionModel:
    """预测模型基类"""

    def __init__(self, model_type: str = 'lstm'):
        self.model_type = model_type
        self.model = None

    def prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""

    def train(self, X: np.ndarray, y: np.ndarray):
        """训练模型"""

    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""

    def evaluate(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict:
        """评估模型"""

class LSTMPricePredictor(PredictionModel):
    """LSTM价格预测模型"""

class RandomForestPredictor(PredictionModel):
    """随机森林预测模型"""

class XGBoostPredictor(PredictionModel):
    """XGBoost预测模型"""
```

#### 10.2 强化学习交易
```python
class RLTradingAgent:
    """强化学习交易智能体"""

    def __init__(self, state_dim: int, action_dim: int):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.agent = None

    def create_environment(self, data: pd.DataFrame) -> gym.Env:
        """创建交易环境"""

    def train_agent(self, episodes: int = 1000):
        """训练智能体"""

    def get_action(self, state: np.ndarray) -> int:
        """获取交易动作"""
```

### 11. 数据质量管理

#### 11.1 数据验证器
```python
class DataValidator:
    """数据验证器"""

    def validate_price_data(self, data: pd.DataFrame) -> Dict[str, List]:
        """验证价格数据质量"""
        issues = {
            'missing_data': [],
            'price_anomalies': [],
            'volume_anomalies': [],
            'duplicate_records': []
        }

        # 检查缺失数据
        missing_dates = self._check_missing_dates(data)
        if missing_dates:
            issues['missing_data'] = missing_dates

        # 检查价格异常
        price_anomalies = self._check_price_anomalies(data)
        if price_anomalies:
            issues['price_anomalies'] = price_anomalies

        return issues

    def _check_price_anomalies(self, data: pd.DataFrame) -> List:
        """检查价格异常"""
        anomalies = []

        # 检查涨跌幅异常
        daily_returns = data['close'].pct_change()
        extreme_returns = daily_returns[abs(daily_returns) > 0.2]  # 20%以上涨跌幅

        # 检查价格跳空
        gaps = abs(data['open'] - data['close'].shift(1)) / data['close'].shift(1)
        large_gaps = gaps[gaps > 0.1]  # 10%以上跳空

        return anomalies
```

#### 11.2 数据清洗工具
```python
class DataCleaner:
    """数据清洗工具"""

    def fill_missing_data(self, data: pd.DataFrame, method: str = 'forward') -> pd.DataFrame:
        """填充缺失数据"""

    def remove_outliers(self, data: pd.DataFrame, method: str = 'iqr') -> pd.DataFrame:
        """移除异常值"""

    def adjust_for_splits(self, data: pd.DataFrame, split_data: pd.DataFrame) -> pd.DataFrame:
        """股票分割调整"""

    def adjust_for_dividends(self, data: pd.DataFrame, dividend_data: pd.DataFrame) -> pd.DataFrame:
        """股息调整"""
```

### 12. 报告生成系统

#### 12.1 策略报告生成器
```python
class StrategyReportGenerator:
    """策略报告生成器"""

    def generate_backtest_report(self, backtest_result: BacktestResult) -> Dict:
        """生成回测报告"""
        report = {
            'summary': self._generate_summary(backtest_result),
            'performance_metrics': self._calculate_metrics(backtest_result),
            'risk_analysis': self._analyze_risk(backtest_result),
            'trade_analysis': self._analyze_trades(backtest_result),
            'charts': self._generate_charts(backtest_result)
        }
        return report

    def export_to_pdf(self, report: Dict, filename: str):
        """导出PDF报告"""

    def export_to_excel(self, report: Dict, filename: str):
        """导出Excel报告"""
```

#### 12.2 定期报告
```python
class PeriodicReportGenerator:
    """定期报告生成器"""

    def generate_daily_report(self, date: str) -> Dict:
        """生成日报"""

    def generate_weekly_report(self, week_start: str) -> Dict:
        """生成周报"""

    def generate_monthly_report(self, month: str) -> Dict:
        """生成月报"""
```

### 13. 移动端适配

#### 13.1 响应式设计
```css
/* 移动端样式 */
@media (max-width: 768px) {
  .trading-dashboard {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .chart-container {
    height: 300px;
    margin: 10px 0;
  }

  .strategy-builder {
    padding: 10px;
  }

  .node-palette {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    overflow-x: auto;
  }
}

/* 平板端样式 */
@media (min-width: 769px) and (max-width: 1024px) {
  .trading-dashboard {
    grid-template-columns: 1fr 1fr;
  }

  .sidebar {
    width: 250px;
  }
}
```

#### 13.2 触控优化
```typescript
const TouchOptimizedChart: React.FC<ChartProps> = ({ data }) => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [scale, setScale] = useState(1);

  const handleTouchStart = (e: TouchEvent) => {
    if (e.touches.length === 1) {
      setTouchStart({ x: e.touches[0].clientX, y: e.touches[0].clientY });
    }
  };

  const handlePinchZoom = (e: TouchEvent) => {
    if (e.touches.length === 2) {
      // 实现双指缩放
    }
  };

  return (
    <div
      onTouchStart={handleTouchStart}
      onTouchMove={handlePinchZoom}
      className="touch-optimized-chart"
    >
      {/* 图表内容 */}
    </div>
  );
};
```

### 14. 性能优化

#### 14.1 数据库优化
```sql
-- 创建复合索引
CREATE INDEX idx_daily_prices_symbol_date ON daily_prices(symbol, trade_date DESC);
CREATE INDEX idx_minute_prices_symbol_datetime ON minute_prices(symbol, datetime DESC);

-- 分区表管理
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';

    EXECUTE format('CREATE TABLE %I PARTITION OF %I
                   FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

#### 14.2 缓存策略
```python
class CacheManager:
    """缓存管理器"""

    def __init__(self, redis_client):
        self.redis = redis_client

    async def get_stock_data(self, symbol: str, date: str) -> Optional[Dict]:
        """获取缓存的股票数据"""
        cache_key = f"stock:{symbol}:{date}"
        cached_data = await self.redis.get(cache_key)

        if cached_data:
            return json.loads(cached_data)
        return None

    async def set_stock_data(self, symbol: str, date: str, data: Dict, ttl: int = 3600):
        """设置股票数据缓存"""
        cache_key = f"stock:{symbol}:{date}"
        await self.redis.setex(cache_key, ttl, json.dumps(data))
```

#### 14.3 异步处理
```python
class AsyncDataProcessor:
    """异步数据处理器"""

    async def batch_fetch_stock_data(self, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """批量异步获取股票数据"""
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self._fetch_single_stock(symbol))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        data_dict = {}
        for symbol, result in zip(symbols, results):
            if not isinstance(result, Exception):
                data_dict[symbol] = result

        return data_dict
```

### 15. 监控与告警

#### 15.1 系统监控
```python
class SystemMonitor:
    """系统监控器"""

    def __init__(self):
        self.metrics = {}

    def track_api_performance(self, endpoint: str, response_time: float):
        """跟踪API性能"""

    def track_database_performance(self, query_type: str, execution_time: float):
        """跟踪数据库性能"""

    def check_system_health(self) -> Dict:
        """检查系统健康状态"""
        return {
            'database': self._check_database_health(),
            'redis': self._check_redis_health(),
            'api_performance': self._get_api_metrics(),
            'memory_usage': self._get_memory_usage(),
            'cpu_usage': self._get_cpu_usage()
        }
```

#### 15.2 交易告警
```python
class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.alert_rules = []

    def add_price_alert(self, symbol: str, condition: str, threshold: float):
        """添加价格告警"""

    def add_strategy_alert(self, strategy_id: int, metric: str, threshold: float):
        """添加策略告警"""

    async def check_alerts(self):
        """检查告警条件"""

    async def send_notification(self, alert: Dict):
        """发送通知"""
```

### 16. 扩展功能

#### 16.1 社交交易功能
```python
class SocialTradingManager:
    """社交交易管理器"""

    def follow_trader(self, follower_id: int, trader_id: int):
        """关注交易员"""

    def copy_trade(self, follower_id: int, trade: Trade):
        """复制交易"""

    def get_leaderboard(self, period: str = 'monthly') -> List[Dict]:
        """获取排行榜"""
```

#### 16.2 新闻情感分析
```python
class NewsAnalyzer:
    """新闻分析器"""

    def fetch_news(self, symbol: str, days: int = 7) -> List[Dict]:
        """获取新闻"""

    def analyze_sentiment(self, news_text: str) -> float:
        """分析情感倾向"""

    def calculate_news_impact(self, symbol: str) -> float:
        """计算新闻影响度"""
```

#### 16.3 期权定价模型
```python
class OptionPricingModel:
    """期权定价模型"""

    def black_scholes(self, S: float, K: float, T: float, r: float, sigma: float) -> Dict:
        """Black-Scholes模型"""

    def binomial_tree(self, S: float, K: float, T: float, r: float, sigma: float, n: int) -> float:
        """二叉树模型"""

    def monte_carlo_pricing(self, S: float, K: float, T: float, r: float, sigma: float, n: int) -> float:
        """蒙特卡洛定价"""
```

**最后更新**: 2024-08-22
**版本**: v1.0.0
