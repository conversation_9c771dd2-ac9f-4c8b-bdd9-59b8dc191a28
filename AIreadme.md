# JQData 量化数据平台开发规范

## 📋 项目概述

基于聚宽JQData API开发的企业级量化数据获取、处理和分析平台，支持A股、港股、美股、期货等多市场数据，提供模块化策略开发和多用户协作功能。

## 🛠️ 技术栈

### 后端技术栈 (Python 3.11+)

#### 核心框架
```python
# Web框架
FastAPI 0.104+              # 现代异步Web框架，自动API文档生成
Uvicorn 0.24+               # ASGI服务器，支持异步和WebSocket
Pydantic V2                 # 数据验证和序列化，类型安全

# 数据库
SQLAlchemy 2.0+             # 异步ORM，支持多数据库
Alembic 1.12+               # 数据库迁移工具
aiosqlite 0.19+             # 异步SQLite驱动
asyncpg 0.29+               # 异步PostgreSQL驱动（可选）

# 认证和安全
python-jose[cryptography]   # JWT Token处理
passlib[bcrypt]             # 密码哈希
python-multipart            # 表单数据处理
cryptography                # 加密解密
```

#### 数据处理
```python
# 数据科学
pandas 2.1+                 # 数据分析和处理
numpy 1.25+                 # 数值计算
scipy 1.11+                 # 科学计算

# 金融数据
jqdatasdk                   # 聚宽数据SDK
akshare                     # 开源金融数据
yfinance                    # Yahoo Finance数据
tushare                     # Tushare数据（可选）

# 技术指标
talib-binary                # 技术分析库
pandas-ta                   # Pandas技术分析扩展
```

#### 机器学习
```python
# 传统机器学习
scikit-learn 1.3+           # 机器学习算法库
xgboost 2.0+                # 梯度提升算法
lightgbm 4.1+               # 微软梯度提升
catboost 1.2+               # Yandex梯度提升

# 深度学习
torch 2.1+                  # PyTorch深度学习框架
transformers 4.35+          # Hugging Face Transformers
torch-geometric 2.4+        # 图神经网络

# AutoML
optuna 3.4+                 # 超参数优化
hyperopt 0.2+               # 贝叶斯优化
auto-sklearn 0.15+          # 自动机器学习
```

#### 任务调度和缓存
```python
# 任务队列
celery 5.3+                 # 分布式任务队列
redis 5.0+                  # 内存数据库和消息代理
flower 2.0+                 # Celery监控工具

# 缓存
redis-py 5.0+               # Redis Python客户端
diskcache 5.6+              # 磁盘缓存
```

### 前端技术栈 (Node.js 18+)

#### 核心框架
```typescript
// React生态
React 18.2+                 // 用户界面库
Next.js 14.0+               // React全栈框架
TypeScript 5.2+             // 类型安全的JavaScript

// 状态管理
Zustand 4.4+                // 轻量级状态管理
React Query 5.0+            // 服务端状态管理
```

#### UI组件库
```typescript
// 组件库
Ant Design 5.11+            // 企业级UI设计语言
@ant-design/icons 5.2+      // Ant Design图标库
@ant-design/charts 2.0+     // Ant Design图表库

// 样式
Tailwind CSS 3.3+           // 实用优先的CSS框架
styled-components 6.1+      // CSS-in-JS样式库
```

#### 数据可视化
```typescript
// 图表库
ECharts 5.4+                // 百度开源图表库
D3.js 7.8+                  // 数据驱动的文档
Plotly.js 2.26+             // 交互式图表库
React-Vis 1.12+             // Uber可视化库

// 金融图表
TradingView Charting Library // 专业金融图表（商业许可）
Lightweight Charts 4.1+     // TradingView轻量级图表
```

#### 开发工具
```typescript
// 构建工具
Webpack 5.89+               // 模块打包器
SWC 1.3+                    // Rust编写的编译器
ESLint 8.52+                // 代码质量检查
Prettier 3.0+               // 代码格式化

// 测试
Jest 29.7+                  // JavaScript测试框架
React Testing Library 13.4+ // React组件测试
Cypress 13.5+               // 端到端测试
```

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   后端服务      │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN/静态资源  │    │   负载均衡      │    │   数据库集群    │
│   (Nginx)       │    │   (Nginx)       │    │   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────┐
                    │   缓存层        │
                    │   (Redis)       │
                    └─────────────────┘
```

### 微服务架构
```
backend/
├── app/
│   ├── api/                    # API路由层
│   │   ├── v1/
│   │   │   ├── auth.py         # 用户认证
│   │   │   ├── market.py       # 市场数据
│   │   │   ├── strategy.py     # 策略管理
│   │   │   ├── backtest.py     # 回测服务
│   │   │   └── portfolio.py    # 投资组合
│   │   └── deps.py             # 依赖注入
│   ├── core/                   # 核心配置
│   │   ├── config.py           # 应用配置
│   │   ├── database.py         # 数据库连接
│   │   ├── security.py         # 安全配置
│   │   └── logging.py          # 日志配置
│   ├── models/                 # 数据模型
│   │   ├── user.py             # 用户模型
│   │   ├── market.py           # 市场数据模型
│   │   ├── strategy.py         # 策略模型
│   │   └── portfolio.py        # 投资组合模型
│   ├── schemas/                # Pydantic模式
│   │   ├── user.py             # 用户模式
│   │   ├── market.py           # 市场数据模式
│   │   └── common.py           # 通用模式
│   ├── services/               # 业务逻辑层
│   │   ├── auth_service.py     # 认证服务
│   │   ├── market_service.py   # 市场数据服务
│   │   ├── ml_service.py       # 机器学习服务
│   │   ├── backtest_service.py # 回测服务
│   │   └── notification_service.py # 通知服务
│   ├── utils/                  # 工具函数
│   │   ├── helpers.py          # 辅助函数
│   │   ├── validators.py       # 验证器
│   │   └── decorators.py       # 装饰器
│   └── main.py                 # 应用入口
├── alembic/                    # 数据库迁移
├── tests/                      # 测试用例
├── scripts/                    # 脚本文件
└── requirements.txt            # Python依赖
```

## 🎯 核心功能模块

### 1. 用户认证与权限管理
- JWT Token认证
- 基于角色的访问控制(RBAC)
- OAuth2集成(Google, GitHub)
- 多因素认证(MFA)
- 会话管理

### 2. 数据获取与处理
- 多数据源集成(JQData, Tushare, AKShare)
- 实时数据流处理
- 数据清洗和标准化
- 数据质量监控
- 历史数据存储

### 3. 机器学习平台
- 特征工程管道
- 模型训练和验证
- 超参数优化
- 模型部署和监控
- A/B测试框架

### 4. 策略开发框架
- 可视化策略编辑器
- 策略回测引擎
- 风险管理模块
- 性能分析工具
- 策略市场

### 5. 投资组合管理
- 资产配置优化
- 风险评估模型
- 业绩归因分析
- 压力测试
- 合规检查

## 📊 数据库设计

### 用户相关表
```sql
-- 用户基础信息
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB
);

-- 用户角色关联
CREATE TABLE user_roles (
    user_id INTEGER REFERENCES users(id),
    role_id INTEGER REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

### 市场数据表
```sql
-- 股票基础信息
CREATE TABLE stocks (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    market VARCHAR(10) NOT NULL,
    industry VARCHAR(100),
    sector VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    list_date DATE,
    delist_date DATE
);

-- 日线行情数据
CREATE TABLE daily_prices (
    id SERIAL PRIMARY KEY,
    stock_id INTEGER REFERENCES stocks(id),
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    amount DECIMAL(15,2),
    UNIQUE(stock_id, trade_date)
);

-- 分钟线数据(分表存储)
CREATE TABLE minute_prices_202401 (
    id SERIAL PRIMARY KEY,
    stock_id INTEGER REFERENCES stocks(id),
    datetime TIMESTAMP NOT NULL,
    open_price DECIMAL(10,3),
    high_price DECIMAL(10,3),
    low_price DECIMAL(10,3),
    close_price DECIMAL(10,3),
    volume BIGINT,
    amount DECIMAL(15,2),
    UNIQUE(stock_id, datetime)
);
```

### 策略相关表
```sql
-- 策略信息
CREATE TABLE strategies (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    code TEXT NOT NULL,
    language VARCHAR(20) DEFAULT 'python',
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 回测结果
CREATE TABLE backtest_results (
    id SERIAL PRIMARY KEY,
    strategy_id INTEGER REFERENCES strategies(id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    initial_capital DECIMAL(15,2),
    final_capital DECIMAL(15,2),
    total_return DECIMAL(8,4),
    annual_return DECIMAL(8,4),
    max_drawdown DECIMAL(8,4),
    sharpe_ratio DECIMAL(8,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 开发规范

### Python代码规范
```python
# 使用类型注解
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

class UserCreate(BaseModel):
    email: str
    username: str
    password: str
    full_name: Optional[str] = None

async def create_user(
    user_data: UserCreate,
    db: AsyncSession
) -> User:
    """创建新用户

    Args:
        user_data: 用户创建数据
        db: 数据库会话

    Returns:
        创建的用户对象

    Raises:
        ValueError: 用户已存在
    """
    # 实现逻辑
    pass
```

### TypeScript代码规范
```typescript
// 使用严格类型定义
interface User {
  id: number;
  email: string;
  username: string;
  fullName?: string;
  isActive: boolean;
  createdAt: string;
}

// 使用泛型
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// React组件规范
interface UserListProps {
  users: User[];
  onUserSelect: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({ users, onUserSelect }) => {
  return (
    <div>
      {users.map(user => (
        <div key={user.id} onClick={() => onUserSelect(user)}>
          {user.username}
        </div>
      ))}
    </div>
  );
};
```

### API设计规范
```python
# RESTful API设计
@router.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user)
):
    """获取用户列表"""
    pass

@router.post("/users", response_model=UserResponse, status_code=201)
async def create_user(user_data: UserCreate):
    """创建用户"""
    pass

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: int):
    """获取单个用户"""
    pass
```

## 🚀 部署架构

### Docker容器化
```dockerfile
# Python后端Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Node.js前端Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

CMD ["npm", "start"]
```

### Docker Compose配置
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/quantdb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=quantdb
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## 📈 性能优化

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_daily_prices_stock_date ON daily_prices(stock_id, trade_date);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_strategies_user_status ON strategies(user_id, status);

-- 分区表
CREATE TABLE daily_prices_2024 PARTITION OF daily_prices
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### 缓存策略
```python
# Redis缓存
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expire_time: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached_result = redis_client.get(cache_key)

            if cached_result:
                return json.loads(cached_result)

            result = await func(*args, **kwargs)
            redis_client.setex(
                cache_key,
                expire_time,
                json.dumps(result, default=str)
            )
            return result
        return wrapper
    return decorator
```

### 异步处理
```python
# Celery任务队列
from celery import Celery

celery_app = Celery('quantitative_trading')

@celery_app.task
def process_market_data(data: dict):
    """异步处理市场数据"""
    # 数据处理逻辑
    pass

@celery_app.task
def run_backtest(strategy_id: int, params: dict):
    """异步运行回测"""
    # 回测逻辑
    pass
```

## 🔒 安全措施

### 认证安全
```python
# JWT Token配置
from jose import JWTError, jwt
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

### 数据安全
```python
# 数据加密
from cryptography.fernet import Fernet

class DataEncryption:
    def __init__(self, key: bytes):
        self.cipher_suite = Fernet(key)

    def encrypt(self, data: str) -> str:
        return self.cipher_suite.encrypt(data.encode()).decode()

    def decrypt(self, encrypted_data: str) -> str:
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
```

## 📊 监控和日志

### 应用监控
```python
# Prometheus指标
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency')

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_LATENCY.observe(process_time)

    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### 结构化日志
```python
import structlog

logger = structlog.get_logger()

@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    start_time = time.time()

    logger.info(
        "request_started",
        method=request.method,
        path=request.url.path,
        client_ip=request.client.host
    )

    response = await call_next(request)

    logger.info(
        "request_completed",
        method=request.method,
        path=request.url.path,
        status_code=response.status_code,
        duration=time.time() - start_time
    )

    return response
```

## 🧪 测试策略

### 单元测试
```python
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_create_user():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post(
            "/api/v1/users",
            json={
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "testpass123"
            }
        )
    assert response.status_code == 201
    assert response.json()["email"] == "<EMAIL>"
```

### 集成测试
```python
@pytest.mark.asyncio
async def test_user_workflow():
    # 创建用户
    user_data = {"email": "<EMAIL>", "password": "testpass123"}

    # 登录
    login_response = await client.post("/api/v1/auth/login", data=user_data)
    token = login_response.json()["access_token"]

    # 访问受保护资源
    headers = {"Authorization": f"Bearer {token}"}
    profile_response = await client.get("/api/v1/users/me", headers=headers)

    assert profile_response.status_code == 200
```

## 📚 文档规范

### API文档
使用FastAPI自动生成的OpenAPI文档，确保所有端点都有详细的描述、参数说明和示例。

### 代码文档
```python
def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.02) -> float:
    """计算夏普比率

    夏普比率是衡量投资组合风险调整后收益的指标，计算公式为：
    (投资组合收益率 - 无风险收益率) / 投资组合收益率标准差

    Args:
        returns: 投资组合收益率序列
        risk_free_rate: 无风险收益率，默认为2%

    Returns:
        夏普比率值，越高表示风险调整后收益越好

    Raises:
        ValueError: 当收益率序列为空时

    Example:
        >>> returns = pd.Series([0.01, 0.02, -0.01, 0.03])
        >>> sharpe = calculate_sharpe_ratio(returns)
        >>> print(f"夏普比率: {sharpe:.4f}")
    """
    if returns.empty:
        raise ValueError("收益率序列不能为空")

    excess_returns = returns.mean() - risk_free_rate / 252  # 日化无风险收益率
    return excess_returns / returns.std() * np.sqrt(252)  # 年化夏普比率
```

## 🔄 持续集成/持续部署

### GitHub Actions工作流
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-asyncio

    - name: Run tests
      run: pytest

    - name: Run linting
      run: |
        flake8 app/
        black --check app/
        mypy app/

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to production
      run: |
        # 部署脚本
        echo "Deploying to production..."
```

---

## 📋 JQData官方规范集成 (2024年8月更新)

### 🎯 JQData标准化改进

基于JQData官方文档，我们已经完全按照以下规范重构了数据获取模块：

#### 核心设计理念
- **标准化**: 100%按照JQData官方文档实现所有API接口
- **规范化**: 遵循JQData数据处理规则和最佳实践
- **专业化**: 面向专业量化投资者和机构用户
- **可扩展**: 模块化设计，支持功能扩展和定制

### 📚 JQData官方文档参考

#### 核心文档
- [JQData使用指南](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10031)
- [JQData安装/登录/流量查询](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10748)
- [JQData数据范围及更新时间](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10261)
- [JQData常见报错解决方法](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10749)

#### 数据处理规则
- [JQData数据处理规则](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10276)
- [行情数据处理规则](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10279)
- [get_price和get_bars的区别](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10242)

#### 数据接口文档
- [沪深A股接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842)
- [财务数据接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9878)
- [期货接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9903)
- [基金接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9926)
- [指数接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9927)

### 🔧 JQData集成规范实现

#### 认证方式
按照[官方认证文档](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10748)实现：

```python
# 邮箱登录
jq.auth('<EMAIL>', 'your_password')

# 手机号登录
jq.auth('13812345678', 'your_password',
        host='https://dataapi.joinquant.com', port=443)
```

#### 数据获取API标准化实现

##### 1. 基础数据接口
```python
# 获取所有标的信息
async def get_all_securities(self, user_id: int, db: AsyncSession,
                            types: List[str] = None, date: str = None) -> pd.DataFrame:
    """按照JQData官方规范获取所有标的信息"""
    await self._ensure_authenticated(user_id, db)
    await self._check_and_consume_quota(user_id, db, 1)

    if types is None:
        types = ['stock']

    if date:
        data = jq.get_all_securities(types=types, date=date)
    else:
        data = jq.get_all_securities(types=types)

    return data

# 获取单支标的信息
async def get_security_info(self, user_id: int, db: AsyncSession,
                           code: str) -> Dict[str, Any]:
    """按照JQData官方规范获取单支标的信息"""
    security_info = jq.get_security_info(code)
    return {
        'display_name': security_info.display_name,
        'name': security_info.name,
        'start_date': security_info.start_date.strftime('%Y-%m-%d') if security_info.start_date else None,
        'end_date': security_info.end_date.strftime('%Y-%m-%d') if security_info.end_date else None,
        'type': security_info.type,
    }

# 标准化代码格式
async def normalize_code(self, user_id: int, db: AsyncSession,
                        code: str) -> str:
    """将标的代码转化成聚宽标准格式"""
    return jq.normalize_code(code)

# 获取交易日
async def get_trade_days(self, user_id: int, db: AsyncSession,
                        start_date: str = None, end_date: str = None,
                        count: int = None) -> List[str]:
    """获取指定范围交易日"""
    if count:
        trade_days = jq.get_trade_days(start_date=start_date, end_date=end_date, count=count)
    else:
        trade_days = jq.get_trade_days(start_date=start_date, end_date=end_date)

    return [day.strftime('%Y-%m-%d') for day in trade_days]
```

##### 2. 行情数据接口
```python
# 获取历史价格数据（按时间范围）
async def get_price(self, user_id: int, db: AsyncSession,
                   security: str, start_date: str = None, end_date: str = None,
                   frequency: str = 'daily', fields: List[str] = None,
                   skip_paused: bool = False, fq: str = 'pre') -> pd.DataFrame:
    """按照JQData官方规范获取历史价格数据"""
    if fields is None:
        fields = ['open', 'close', 'high', 'low', 'volume', 'money']

    data = jq.get_price(
        security=security,
        start_date=start_date,
        end_date=end_date,
        frequency=frequency,
        fields=fields,
        skip_paused=skip_paused,
        fq=fq
    )
    return data

# 获取历史数据（按数量）
async def get_bars(self, user_id: int, db: AsyncSession,
                  security: str, count: int, unit: str = '1d',
                  fields: List[str] = None, include_now: bool = False,
                  end_dt: str = None, fq_ref_date: str = None) -> pd.DataFrame:
    """按照JQData官方规范获取历史数据（按数量）"""
    if fields is None:
        fields = ['open', 'close', 'high', 'low', 'volume', 'money']

    data = jq.get_bars(
        security=security,
        count=count,
        unit=unit,
        fields=fields,
        include_now=include_now,
        end_dt=end_dt,
        fq_ref_date=fq_ref_date
    )
    return data

# 获取当前价格数据
async def get_current_data(self, user_id: int, db: AsyncSession,
                          security: str, fields: List[str] = None) -> pd.DataFrame:
    """按照JQData官方规范获取当前价格数据"""
    if fields is None:
        fields = ['last_price', 'high_limit', 'low_limit', 'volume', 'money', 'avg', 'bid1', 'ask1']

    data = jq.get_current_data(security, fields)
    return data
```

##### 3. 财务和基本面数据
```python
# 获取财务数据
async def get_fundamentals(self, user_id: int, db: AsyncSession,
                          query_object, date: str = None, statDate: str = None) -> pd.DataFrame:
    """按照JQData官方规范获取财务数据"""
    data = jq.get_fundamentals(query_object, date=date, statDate=statDate)
    return data

# 获取行业信息
async def get_industry(self, user_id: int, db: AsyncSession,
                      security: str, date: str = None) -> Dict[str, Any]:
    """按照JQData官方规范获取行业信息"""
    data = jq.get_industry(security, date=date)
    return data

# 获取概念信息
async def get_concept(self, user_id: int, db: AsyncSession,
                     security: str, date: str = None) -> Dict[str, Any]:
    """按照JQData官方规范获取概念信息"""
    data = jq.get_concept(security, date=date)
    return data
```

##### 4. 指数相关接口
```python
# 获取指数成分股
async def get_index_stocks(self, user_id: int, db: AsyncSession,
                          index: str, date: str = None) -> List[str]:
    """按照JQData官方规范获取指数成分股"""
    stocks = jq.get_index_stocks(index, date=date)
    return stocks

# 获取指数权重
async def get_index_weights(self, user_id: int, db: AsyncSession,
                           index: str, date: str = None) -> pd.DataFrame:
    """按照JQData官方规范获取指数成分股权重"""
    weights = jq.get_index_weights(index, date=date)
    return weights
```

##### 5. 资金流向数据
```python
# 获取资金流向数据
async def get_money_flow(self, user_id: int, db: AsyncSession,
                        security: str, start_date: str = None,
                        end_date: str = None, fields: List[str] = None) -> pd.DataFrame:
    """按照JQData官方规范获取资金流向数据"""
    if fields is None:
        fields = ['net_amount_main', 'net_pct_main', 'net_amount_xl', 'net_pct_xl',
                 'net_amount_l', 'net_pct_l', 'net_amount_m', 'net_pct_m',
                 'net_amount_s', 'net_pct_s']

    data = jq.get_money_flow(
        security=security,
        start_date=start_date,
        end_date=end_date,
        fields=fields
    )
    return data
```

### 📊 JQData数据处理规则遵循

#### 1. 行情数据处理规则
- **复权处理**: 严格按照JQData复权规则
  - 前复权(pre): 以当前价格为基准向前复权
  - 后复权(post): 以上市价格为基准向后复权
  - 不复权(None): 使用原始价格数据
- **停牌处理**: 支持跳过停牌日期选项
- **数据频率**: 支持日线、分钟线等多种频率
- **字段标准**: 完全按照官方字段定义

#### 2. 财务数据处理规则
- **报告期数据**: 按照官方报告期规则处理
- **单季度数据**: 支持单季度和年度数据查询
- **数据更新**: 遵循官方数据更新时间规则

#### 3. 缓存策略
```python
# 缓存时间设置（按数据特性）
CACHE_TTL = {
    'securities': 3600,      # 标的信息：1小时
    'daily_price': 3600,     # 日线数据：1小时
    'minute_price': 300,     # 分钟数据：5分钟
    'current_data': 0,       # 实时数据：不缓存
    'fundamentals': 86400,   # 财务数据：24小时
    'industry': 86400,       # 行业数据：24小时
    'trade_days': 86400,     # 交易日：24小时
}
```

### 🔒 JQData安全和配额管理

#### 配额监控实现
```python
class JQDataService:
    async def _check_and_consume_quota(self, user_id: int, db: AsyncSession, cost: int):
        """检查并消费API配额"""
        # 1. 检查用户配额
        config = await self._get_user_config(user_id, db)
        if not config:
            raise AuthenticationError("请先配置JQData账号信息")

        # 2. 检查今日配额使用情况
        today = datetime.now().date()
        if config.last_quota_reset != today:
            # 重置每日配额
            config.api_quota_used_today = 0
            config.last_quota_reset = today

        # 3. 检查配额是否足够
        if config.api_quota_used_today + cost > config.api_quota_daily:
            raise QuotaExceededError(f"API配额不足，今日已使用: {config.api_quota_used_today}/{config.api_quota_daily}")

        # 4. 消费配额
        config.api_quota_used_today += cost
        await db.commit()

        logger.info(f"用户 {user_id} 消费配额 {cost}，剩余: {config.api_quota_daily - config.api_quota_used_today}")
```

#### 认证安全
- **密码加密**: 使用Fernet加密存储JQData密码
- **Token管理**: JWT Token认证用户身份
- **权限控制**: 基于用户级别的API访问控制

### 📋 JQData常见数据问题处理

#### 融资融券数据
- **上交所**: 当日收盘后更新融资融券数据
- **深交所**: 下一个交易日10点更新融资融券数据
- **注意**: 深交所周五的融资融券数据需等到周一上午10点才能获取

#### 申万行业数据
- **2014年2月21日重大调整**:
  - 新增11个行业代码：['801710','801720','801730','801740','801750','801760','801770','801780','801790','801880','801890']
  - 弃用6个行业代码：['801060','801070','801090','801100','801190','801220']
- **数据规则**: 新增行业代码在2014-02-21之前没有成分股，弃用代码在2014-02-21之后没有成分股

#### 指数数据处理
- **上证指数(000001) vs A股指数(000002)**:
  - 000001包含上交所A股和B股
  - 000002只包含上交所A股
  - 由于未提供B股数据，成分股完全一致
- **申万行业指数**: 基日1999-12-31，提供自基日开始的完整行情数据

#### 财务数据规则
- **报告期数据**: 严格按照官方报告期规则处理
- **单季度数据**: 支持单季度和年度财务数据查询
- **数据更新**: 遵循JQData官方数据更新时间规则

### 📊 RESTful API端点规范

#### 标准化API设计
```python
# 按照JQData官方规范设计的API端点
GET  /api/v1/market/securities      # 获取标的信息
GET  /api/v1/market/price          # 获取价格数据
GET  /api/v1/market/current        # 获取实时数据
GET  /api/v1/market/bars           # 获取历史数据（按数量）
GET  /api/v1/market/trade-days     # 获取交易日
GET  /api/v1/market/security-info  # 获取标的详情
GET  /api/v1/market/index-stocks   # 获取指数成分股
GET  /api/v1/market/index-weights  # 获取指数权重
GET  /api/v1/market/money-flow     # 获取资金流向

POST /api/v1/jqdata/config         # 配置JQData账号
GET  /api/v1/jqdata/config         # 获取JQData配置
POST /api/v1/jqdata/test           # 测试JQData连接
```

#### 响应格式标准
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "security": "000001.XSHE",
    "frequency": "daily",
    "count": 100,
    "data": [...]
  }
}
```

### 🧪 JQData数据一致性验证

#### 测试策略
1. **与JQData官方对比**: 确保数据完全一致
2. **字段格式验证**: 验证所有字段格式符合规范
3. **边界条件测试**: 测试各种边界情况
4. **错误处理测试**: 验证错误处理机制

#### 性能测试
1. **API响应时间**: 监控API响应性能
2. **缓存效果**: 验证缓存策略有效性
3. **并发处理**: 测试多用户并发访问
4. **配额管理**: 验证配额限制机制

---

## 📝 开发历史和版本记录

### 版本 1.0.0 (2024年8月初)
- 初始项目架构设计
- 基础用户认证系统
- 多数据源集成框架
- 机器学习平台基础

### 版本 1.1.0 (2024年8月27日)
- **重大更新**: 完全按照JQData官方规范重构数据获取模块
- 添加JQData邮箱和手机号双登录方式支持
- 实现标准化的JQData API接口
- 添加JQData配额管理和监控
- 完善数据缓存策略
- 更新前端JQData配置界面

### 后续规划
- 添加更多JQData高级功能接口
- 实现JQData数据质量监控
- 添加JQData API使用统计和分析
- 完善JQData错误处理和重试机制

---

*本文档将持续更新，记录项目的发展历程和技术演进。所有修改都会保留历史记录，确保开发过程的可追溯性。*

## 📚 JQData官方文档参考

### 核心文档
- [JQData使用指南](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10031)
- [JQData安装/登录/流量查询](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10748)
- [JQData数据范围及更新时间](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10261)
- [JQData常见报错解决方法](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10749)

### 数据处理规则
- [JQData数据处理规则](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10276)
- [行情数据处理规则](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10279)
- [get_price和get_bars的区别](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10242)

### 数据接口文档
- [沪深A股接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9842)
- [财务数据接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9878)
- [期货接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9903)
- [基金接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9926)
- [指数接口](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9927)

## 🔧 JQData集成规范

### 认证方式
按照[官方认证文档](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=10748)实现：

```python
# 邮箱登录
jq.auth('<EMAIL>', 'your_password')

# 手机号登录
jq.auth('13812345678', 'your_password',
        host='https://dataapi.joinquant.com', port=443)
```

### 数据获取API实现

#### 1. 基础数据接口
```python
# 获取所有标的信息
get_all_securities(types=['stock'], date=None)

# 获取单支标的信息
get_security_info(code)

# 标准化代码格式
normalize_code(code)

# 获取交易日
get_trade_days(start_date=None, end_date=None, count=None)
```

#### 2. 行情数据接口
```python
# 获取历史价格数据（按时间范围）
get_price(security, start_date=None, end_date=None,
          frequency='daily', fields=None, skip_paused=False, fq='pre')

# 获取历史数据（按数量）
get_bars(security, count, unit='1d', fields=None,
         include_now=False, end_dt=None, fq_ref_date=None)

# 获取当前价格数据
get_current_data(security, fields=None)
```

#### 3. 财务和基本面数据
```python
# 获取财务数据
get_fundamentals(query_object, date=None, statDate=None)

# 获取行业信息
get_industry(security, date=None)

# 获取概念信息
get_concept(security, date=None)
```

#### 4. 指数相关接口
```python
# 获取指数成分股
get_index_stocks(index, date=None)

# 获取指数权重
get_index_weights(index, date=None)
```

#### 5. 资金流向数据
```python
# 获取资金流向数据
get_money_flow(security, start_date=None, end_date=None, fields=None)
```

### 数据处理规则遵循

#### 1. 行情数据处理
- **复权处理**: 严格按照JQData复权规则
- **停牌处理**: 支持跳过停牌日期选项
- **数据频率**: 支持日线、分钟线等多种频率
- **字段标准**: 完全按照官方字段定义

#### 2. 财务数据处理
- **报告期数据**: 按照官方报告期规则处理
- **单季度数据**: 支持单季度和年度数据查询
- **数据更新**: 遵循官方数据更新时间规则

#### 3. 缓存策略
```python
# 缓存时间设置（按数据特性）
CACHE_TTL = {
    'securities': 3600,      # 标的信息：1小时
    'daily_price': 3600,     # 日线数据：1小时
    'minute_price': 300,     # 分钟数据：5分钟
    'current_data': 0,       # 实时数据：不缓存
    'fundamentals': 86400,   # 财务数据：24小时
    'industry': 86400,       # 行业数据：24小时
    'trade_days': 86400,     # 交易日：24小时
}
```

## 🔒 安全和配额管理

### 配额监控
```python
class JQDataService:
    async def _check_and_consume_quota(self, user_id: int, db: AsyncSession, cost: int):
        """检查并消费API配额"""
        # 1. 检查用户配额
        # 2. 记录API调用
        # 3. 更新配额使用情况
        # 4. 配额不足时抛出异常
```

### 认证安全
- **密码加密**: 使用Fernet加密存储JQData密码
- **Token管理**: JWT Token认证用户身份
- **权限控制**: 基于用户级别的API访问控制

## 📊 API端点规范

### RESTful API设计
```
GET  /api/v1/market/securities      # 获取标的信息
GET  /api/v1/market/price          # 获取价格数据
GET  /api/v1/market/current        # 获取实时数据
GET  /api/v1/market/bars           # 获取历史数据（按数量）
GET  /api/v1/market/trade-days     # 获取交易日
GET  /api/v1/market/security-info  # 获取标的详情
GET  /api/v1/market/index-stocks   # 获取指数成分股
GET  /api/v1/market/index-weights  # 获取指数权重
GET  /api/v1/market/money-flow     # 获取资金流向

POST /api/v1/jqdata/config         # 配置JQData账号
GET  /api/v1/jqdata/config         # 获取JQData配置
POST /api/v1/jqdata/test           # 测试JQData连接
```

### 响应格式标准
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "security": "000001.XSHE",
    "frequency": "daily",
    "count": 100,
    "data": [...]
  }
}
```

## 🧪 测试和验证

### 数据一致性验证
1. **与JQData官方对比**: 确保数据完全一致
2. **字段格式验证**: 验证所有字段格式符合规范
3. **边界条件测试**: 测试各种边界情况
4. **错误处理测试**: 验证错误处理机制

### 性能测试
1. **API响应时间**: 监控API响应性能
2. **缓存效果**: 验证缓存策略有效性
3. **并发处理**: 测试多用户并发访问
4. **配额管理**: 验证配额限制机制

## 🚀 部署和运维

### 环境配置
```env
# JQData配置
JQDATA_RATE_LIMIT_PER_MINUTE=60
ENCRYPTION_KEY=your-encryption-key

# 数据库配置
DATABASE_URL=sqlite:///./quantitative_trading.db

# 缓存配置
REDIS_URL=redis://localhost:6379
```

### 监控指标
- JQData API调用次数和成功率
- 用户配额使用情况
- 缓存命中率
- 系统响应时间
- 错误率和异常监控

## 📝 开发规范

### 代码规范
1. **严格按照JQData官方文档**: 所有API参数和返回值必须与官方文档一致
2. **错误处理**: 统一的异常处理机制
3. **日志记录**: 详细的操作日志和错误日志
4. **类型注解**: 完整的Python类型注解
5. **文档字符串**: 详细的函数和类文档

### 版本管理
- 跟随JQData官方API版本更新
- 向后兼容性保证
- 变更日志记录

## 📋 常见数据问题处理

### 融资融券数据
- **上交所**: 当日收盘后更新
- **深交所**: 下一个交易日10点更新
- **周末查询**: 深交所周五数据需等到周一上午10点

### 申万行业数据
- **2014年2月21日**: 申万行业代码重大调整
- **新增行业**: 11个新行业代码，2014-02-21之前无成分股
- **弃用行业**: 6个弃用代码，2014-02-21之后无成分股

### 指数数据处理
- **上证指数vs A股指数**: 000001包含A股+B股，000002仅A股
- **申万行业指数**: 基日1999-12-31，提供自基日开始的行情数据

### 财务数据规则
- **报告期数据**: 按照官方报告期规则处理
- **单季度数据**: 支持单季度和年度数据查询
- **数据更新时间**: 遵循官方数据更新时间规则

这个AI开发文档确保了项目完全按照JQData官方规范实现，为后续开发和维护提供了清晰的指导。