{"version": 3, "file": "bbox.js", "sourceRoot": "", "sources": ["../../src/util/bbox.ts"], "names": [], "mappings": ";AAEA;IAyBE,cAAY,CAAK,EAAE,CAAK,EAAE,KAAS,EAAE,MAAU;QAAnC,kBAAA,EAAA,KAAK;QAAE,kBAAA,EAAA,KAAK;QAAE,sBAAA,EAAA,SAAS;QAAE,uBAAA,EAAA,UAAU;QAxBxC,MAAC,GAAG,CAAC,CAAC;QAEN,MAAC,GAAG,CAAC,CAAC;QAEN,UAAK,GAAG,CAAC,CAAC;QAEV,WAAM,GAAG,CAAC,CAAC;QAmBhB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IArBD,sBAAW,wBAAM;aAAjB;YACE,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC9B,CAAC;;;OAAA;IAED,sBAAW,sBAAI;aAAf;YACE,OAAO,IAAI,CAAC,CAAC,CAAC;QAChB,CAAC;;;OAAA;IAED,sBAAW,uBAAK;aAAhB;YACE,OAAO,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAC7B,CAAC;;;OAAA;IAED,sBAAW,qBAAG;aAAd;YACE,OAAO,IAAI,CAAC,CAAC,CAAC;QAChB,CAAC;;;OAAA;IASM,aAAQ,GAAf,UAAgB,KAAc;QAC5B,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAED,qBAAM,GAAN;QACE,OAAO;YACL,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,wBAAS,GAAhB,UAAiB,CAAS,EAAE,CAAS;QACnC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC;IAChF,CAAC;IACH,WAAC;AAAD,CAAC,AAxDD,IAwDC;;AAED,MAAM,UAAU,aAAa,CAAC,OAAsB;IAC5C,IAAA,KAGF,OAAO,CAAC,eAAe,EAAE,EAF3B,KAAA,iBAAiB,EAAX,IAAI,QAAA,EAAE,IAAI,QAAA,EAChB,KAAA,iBAAiB,EAAX,IAAI,QAAA,EAAE,IAAI,QACW,CAAC;IAC9B,IAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1B,IAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAC3B,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7C,CAAC"}