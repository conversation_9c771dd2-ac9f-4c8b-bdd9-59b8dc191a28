[tool.poetry]
name = "jqdata-backend"
version = "1.0.0"
description = "JQData量化数据平台后端服务"
authors = ["JQData Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"

# Web框架
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
gunicorn = "^21.2.0"

# 数据库
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
asyncpg = "^0.29.0"
psycopg2-binary = "^2.9.9"

# Redis
redis = "^5.0.1"
aioredis = "^2.0.1"

# 数据处理
pandas = "^2.1.3"
numpy = "^1.25.2"
polars = {version = "^0.19.19", optional = true}

# 技术指标
TA-Lib = "^0.4.28"

# JQData
jqdatasdk = "^1.8.11"

# 任务队列
celery = {extras = ["redis"], version = "^5.3.4"}
flower = "^2.0.1"

# 认证与安全
fastapi-users = {extras = ["sqlalchemy", "oauth"], version = "^12.1.2"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
cryptography = "^41.0.7"

# 配置管理
pydantic = "^2.5.0"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"

# HTTP客户端
httpx = "^0.25.2"
aiohttp = "^3.9.1"

# 日志
loguru = "^0.7.2"
structlog = "^23.2.0"

# 监控
prometheus-client = "^0.19.0"
psutil = "^5.9.6"

# 工具库
python-dateutil = "^2.8.2"
pytz = "^2023.3"
schedule = "^1.2.0"

# 邮件
fastapi-mail = "^1.4.1"

# 文件处理
openpyxl = "^3.1.2"
xlsxwriter = "^3.1.9"

# 数据验证
email-validator = "^2.1.0"

[tool.poetry.group.dev.dependencies]
# 测试
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
httpx = "^0.25.2"
factory-boy = "^3.3.0"

# 代码质量
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
bandit = "^1.7.5"
pre-commit = "^3.6.0"
autoflake = "^2.2.1"

# 性能分析
py-spy = "^0.3.14"
memory-profiler = "^0.61.0"

# 开发工具
ipython = "^8.17.2"
jupyter = "^1.0.0"

[tool.poetry.extras]
polars = ["polars"]
ml = ["scikit-learn", "tensorflow", "torch"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# =============================================================================
# 工具配置
# =============================================================================

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
known_third_party = ["fastapi", "sqlalchemy", "pydantic"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "jqdatasdk.*",
    "talib.*",
    "celery.*",
    "flower.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]
