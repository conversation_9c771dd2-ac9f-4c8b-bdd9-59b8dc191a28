'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Typography,
  Form,
  DatePicker,
  Select,
  InputNumber,
  Table,
  Progress,
  Spin,
  message,
  Tabs,
  Alert,
  Space
} from 'antd';
import { 
  LineChartOutlined,
  PlayCircleOutlined,
  Bar<PERSON>hartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

function BacktestContent() {
  const router = useRouter();

  const backtestStats = [
    {
      title: '总收益率',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#52c41a' },
      prefix: <RiseOutlined />
    },
    {
      title: '年化收益率',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#52c41a' },
      prefix: <BarChartOutlined />
    },
    {
      title: '最大回撤',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#f5222d' },
      prefix: <FallOutlined />
    },
    {
      title: '夏普比率',
      value: 0,
      precision: 3,
      valueStyle: { color: '#1890ff' },
      prefix: <LineChartOutlined />
    }
  ];

  const onFinish = (values: any) => {
    console.log('回测参数:', values);
    // 这里会调用回测API
  };

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <LineChartOutlined className="mr-3" />
          回测分析
        </Title>
        <Text type="secondary" className="text-lg">
          测试策略的历史表现和风险指标
        </Text>
      </div>

      {/* 回测配置 */}
      <Card title="回测配置" className="mb-6">
        <Form
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            initialCapital: 100000,
            benchmark: 'HS300'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                label="策略选择"
                name="strategy"
                rules={[{ required: true, message: '请选择策略' }]}
              >
                <Select placeholder="选择要回测的策略">
                  <Option value="strategy1">双均线策略</Option>
                  <Option value="strategy2">RSI反转策略</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                label="回测时间"
                name="dateRange"
                rules={[{ required: true, message: '请选择回测时间范围' }]}
              >
                <RangePicker className="w-full" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                label="初始资金"
                name="initialCapital"
                rules={[{ required: true, message: '请输入初始资金' }]}
              >
                <InputNumber
                  className="w-full"
                  min={10000}
                  max={10000000}
                  step={10000}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Form.Item
                label="基准指数"
                name="benchmark"
              >
                <Select>
                  <Option value="HS300">沪深300</Option>
                  <Option value="SZ50">上证50</Option>
                  <Option value="ZZ500">中证500</Option>
                  <Option value="CYB">创业板指</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<PlayCircleOutlined />}>
              开始回测
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 回测结果 */}
      <Row gutter={[16, 16]} className="mb-8">
        {backtestStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 收益曲线 */}
      <Card title="收益曲线" className="mb-6">
        <Empty 
          description="请先运行回测以查看收益曲线"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" icon={<PlayCircleOutlined />}>
            开始回测
          </Button>
        </Empty>
      </Card>

      {/* 交易记录 */}
      <Card title="交易记录">
        <Empty 
          description="请先运行回测以查看交易记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary"
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            创建策略
          </Button>
        </Empty>
      </Card>
    </div>
  );
}

export default function BacktestPage() {
  return (
    <ClientAuthWrapper>
      <BacktestContent />
    </ClientAuthWrapper>
  );
}
