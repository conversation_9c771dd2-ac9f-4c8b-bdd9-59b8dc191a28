/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/market/charts/page";
exports.ids = ["app/dashboard/market/charts/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fmarket%2Fcharts%2Fpage&page=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&appPaths=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fmarket%2Fcharts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fmarket%2Fcharts%2Fpage&page=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&appPaths=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fmarket%2Fcharts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'market',\n        {\n        children: [\n        'charts',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/market/charts/page.tsx */ \"(rsc)/./src/app/dashboard/market/charts/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/market/charts/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/market/charts/page\",\n        pathname: \"/dashboard/market/charts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fmarket%2Fcharts%2Fpage&page=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&appPaths=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fmarket%2Fcharts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEZXNrdG9wJTVDJTVDSlFEYXRhJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBeUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qcWRhdGEtZnJvbnRlbmQvP2UyODUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERlc2t0b3BcXFxcSlFEYXRhXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cmarket%5C%5Ccharts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cmarket%5C%5Ccharts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/market/charts/page.tsx */ \"(ssr)/./src/app/dashboard/market/charts/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEZXNrdG9wJTVDJTVDSlFEYXRhJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNtYXJrZXQlNUMlNUNjaGFydHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQXVJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vanFkYXRhLWZyb250ZW5kLz82ZDUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5pc3RyYXRvclxcXFxEZXNrdG9wXFxcXEpRRGF0YVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxtYXJrZXRcXFxcY2hhcnRzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cmarket%5C%5Ccharts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEZXNrdG9wJTVDJTVDSlFEYXRhJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUE2RyIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8/ZjA3NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEFkbWluaXN0cmF0b3JcXFxcRGVza3RvcFxcXFxKUURhdGFcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(ssr)/./src/app/loading.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEZXNrdG9wJTVDJTVDSlFEYXRhJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsb2FkaW5nLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0pBQStHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vanFkYXRhLWZyb250ZW5kLz8yYzgxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcQWRtaW5pc3RyYXRvclxcXFxEZXNrdG9wXFxcXEpRRGF0YVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxvYWRpbmcudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(ssr)/./src/app/not-found.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkbWluaXN0cmF0b3IlNUMlNUNEZXNrdG9wJTVDJTVDSlFEYXRhJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNub3QtZm91bmQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBaUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qcWRhdGEtZnJvbnRlbmQvP2QyOTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZG1pbmlzdHJhdG9yXFxcXERlc2t0b3BcXFxcSlFEYXRhXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdministrator%5C%5CDesktop%5C%5CJQData%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/grid/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/breadcrumb/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LineChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/FundOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ProfileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(ssr)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * 仪表板布局组件\n * \n * 提供统一的仪表板布局，包含侧边栏、顶部导航、面包屑等\n */ \n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Text } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { useBreakpoint } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst menuItems = [\n    {\n        key: \"overview\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        label: \"概览\",\n        path: \"/dashboard/overview\"\n    },\n    {\n        key: \"market\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, undefined),\n        label: \"市场数据\",\n        path: \"/dashboard/market\",\n        children: [\n            {\n                key: \"market-overview\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 15\n                }, undefined),\n                label: \"市场概览\",\n                path: \"/dashboard/market/overview\"\n            },\n            {\n                key: \"stock-list\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 15\n                }, undefined),\n                label: \"股票列表\",\n                path: \"/dashboard/market/stocks\"\n            },\n            {\n                key: \"charts\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 15\n                }, undefined),\n                label: \"图表分析\",\n                path: \"/dashboard/market/charts\"\n            }\n        ]\n    },\n    {\n        key: \"portfolio\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        label: \"投资组合\",\n        path: \"/dashboard/portfolio\",\n        children: [\n            {\n                key: \"portfolio-overview\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 15\n                }, undefined),\n                label: \"组合概览\",\n                path: \"/dashboard/portfolio/overview\"\n            },\n            {\n                key: \"positions\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 15\n                }, undefined),\n                label: \"持仓管理\",\n                path: \"/dashboard/portfolio/positions\"\n            },\n            {\n                key: \"performance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 15\n                }, undefined),\n                label: \"业绩分析\",\n                path: \"/dashboard/portfolio/performance\"\n            }\n        ]\n    },\n    {\n        key: \"strategy\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 120,\n            columnNumber: 11\n        }, undefined),\n        label: \"策略中心\",\n        path: \"/dashboard/strategy\",\n        children: [\n            {\n                key: \"strategy-list\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, undefined),\n                label: \"策略列表\",\n                path: \"/dashboard/strategy/list\"\n            },\n            {\n                key: \"strategy-editor\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 15\n                }, undefined),\n                label: \"策略编辑器\",\n                path: \"/dashboard/strategy/editor\"\n            },\n            {\n                key: \"backtest\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 15\n                }, undefined),\n                label: \"回测分析\",\n                path: \"/dashboard/strategy/backtest\"\n            }\n        ]\n    },\n    {\n        key: \"settings\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 146,\n            columnNumber: 11\n        }, undefined),\n        label: \"设置\",\n        path: \"/dashboard/settings\",\n        children: [\n            {\n                key: \"jqdata-config\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 15\n                }, undefined),\n                label: \"JQData配置\",\n                path: \"/dashboard/settings/jqdata\"\n            },\n            {\n                key: \"profile\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 15\n                }, undefined),\n                label: \"个人资料\",\n                path: \"/dashboard/settings/profile\"\n            },\n            {\n                key: \"preferences\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 15\n                }, undefined),\n                label: \"偏好设置\",\n                path: \"/dashboard/settings/preferences\"\n            }\n        ]\n    }\n];\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthActions)();\n    const screens = useBreakpoint();\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDrawerOpen, setMobileDrawerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedKeys, setSelectedKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openKeys, setOpenKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { token: { colorBgContainer, borderRadiusLG } } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].useToken();\n    const isMobile = !screens.md;\n    // 根据当前路径设置选中的菜单项\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = pathname;\n        const findSelectedKey = (items)=>{\n            for (const item of items){\n                if (item.path === currentPath) {\n                    return item.key;\n                }\n                if (item.children) {\n                    const childKey = findSelectedKey(item.children);\n                    if (childKey) {\n                        setOpenKeys((prev)=>[\n                                ...new Set([\n                                    ...prev,\n                                    item.key\n                                ])\n                            ]);\n                        return childKey;\n                    }\n                }\n            }\n            return null;\n        };\n        const selectedKey = findSelectedKey(menuItems);\n        if (selectedKey) {\n            setSelectedKeys([\n                selectedKey\n            ]);\n        }\n    }, [\n        pathname\n    ]);\n    // 生成面包屑\n    const generateBreadcrumb = ()=>{\n        const pathSegments = pathname.split(\"/\").filter(Boolean);\n        const breadcrumbItems = [\n            {\n                title: \"首页\",\n                href: \"/dashboard\"\n            }\n        ];\n        let currentPath = \"\";\n        for (const segment of pathSegments.slice(1)){\n            currentPath += `/${segment}`;\n            const fullPath = `/dashboard${currentPath}`;\n            // 查找对应的菜单项\n            const findMenuItem = (items)=>{\n                for (const item of items){\n                    if (item.path === fullPath) {\n                        return item;\n                    }\n                    if (item.children) {\n                        const child = findMenuItem(item.children);\n                        if (child) return child;\n                    }\n                }\n                return null;\n            };\n            const menuItem = findMenuItem(menuItems);\n            if (menuItem) {\n                breadcrumbItems.push({\n                    title: menuItem.label,\n                    href: menuItem.path\n                });\n            }\n        }\n        return breadcrumbItems;\n    };\n    // 处理菜单点击\n    const handleMenuClick = ({ key })=>{\n        const findMenuItem = (items)=>{\n            for (const item of items){\n                if (item.key === key) {\n                    return item;\n                }\n                if (item.children) {\n                    const child = findMenuItem(item.children);\n                    if (child) return child;\n                }\n            }\n            return null;\n        };\n        const menuItem = findMenuItem(menuItems);\n        if (menuItem) {\n            router.push(menuItem.path);\n            if (isMobile) {\n                setMobileDrawerOpen(false);\n            }\n        }\n    };\n    // 用户下拉菜单\n    const userMenuItems = [\n        {\n            key: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 288,\n                columnNumber: 13\n            }, this),\n            label: \"个人资料\",\n            onClick: ()=>router.push(\"/dashboard/settings/profile\")\n        },\n        {\n            key: \"settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 294,\n                columnNumber: 13\n            }, this),\n            label: \"设置\",\n            onClick: ()=>router.push(\"/dashboard/settings\")\n        },\n        {\n            type: \"divider\"\n        },\n        {\n            key: \"help\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 303,\n                columnNumber: 13\n            }, this),\n            label: \"帮助中心\",\n            onClick: ()=>window.open(\"/help\", \"_blank\")\n        },\n        {\n            key: \"logout\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 309,\n                columnNumber: 13\n            }, this),\n            label: \"退出登录\",\n            onClick: logout\n        }\n    ];\n    // 侧边栏内容\n    const sidebarContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 flex items-center justify-center border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.div, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"text-white text-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            strong: true,\n                            className: \"text-lg\",\n                            children: \"JQData\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    mode: \"inline\",\n                    selectedKeys: selectedKeys,\n                    openKeys: openKeys,\n                    onOpenChange: setOpenKeys,\n                    onClick: handleMenuClick,\n                    className: \"border-none\",\n                    items: menuItems.map((item)=>({\n                            key: item.key,\n                            icon: item.icon,\n                            label: item.label,\n                            children: item.children?.map((child)=>({\n                                    key: child.key,\n                                    icon: child.icon,\n                                    label: child.label\n                                }))\n                        }))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 317,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"min-h-screen\",\n        children: [\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 256,\n                className: \"shadow-lg\",\n                style: {\n                    background: colorBgContainer\n                },\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this),\n            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                title: \"导航菜单\",\n                placement: \"left\",\n                onClose: ()=>setMobileDrawerOpen(false),\n                open: mobileDrawerOpen,\n                styles: {\n                    body: {\n                        padding: 0\n                    }\n                },\n                width: 256,\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        style: {\n                            padding: \"0 24px\",\n                            background: colorBgContainer,\n                            borderBottom: \"1px solid #f0f0f0\"\n                        },\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: ()=>{\n                                            if (isMobile) {\n                                                setMobileDrawerOpen(true);\n                                            } else {\n                                                setCollapsed(!collapsed);\n                                            }\n                                        },\n                                        className: \"text-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        items: generateBreadcrumb(),\n                                        className: \"hidden sm:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                size: \"middle\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            className: \"text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        arrow: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded-lg transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                    size: \"small\",\n                                                    src: user?.avatarUrl,\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden sm:block\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            className: \"text-sm\",\n                                                            children: user?.fullName || user?.username\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: user?.subscriptionType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        style: {\n                            margin: \"24px\",\n                            padding: \"24px\",\n                            minHeight: \"calc(100vh - 112px)\",\n                            background: colorBgContainer,\n                            borderRadius: borderRadiusLG\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: children\n                        }, pathname, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/market/charts/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/market/charts/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChartsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/auto-complete/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/tabs/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AutoComplete,Button,Card,Col,Input,Row,Space,Tabs,Typography,message!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,SearchOutlined,ShareAltOutlined,StarFilled,StarOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ShareAltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,SearchOutlined,ShareAltOutlined,StarFilled,StarOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/FullscreenOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,SearchOutlined,ShareAltOutlined,StarFilled,StarOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SearchOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,SearchOutlined,ShareAltOutlined,StarFilled,StarOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/StarFilled.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,SearchOutlined,ShareAltOutlined,StarFilled,StarOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/StarOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_charts_CandlestickChart__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/charts/CandlestickChart */ \"(ssr)/./src/components/charts/CandlestickChart.tsx\");\n/* harmony import */ var _components_charts_MarketOverviewChart__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/charts/MarketOverviewChart */ \"(ssr)/./src/components/charts/MarketOverviewChart.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * 图表分析页面\n * \n * 提供专业的股票图表分析工具，包括K线图、技术指标等\n */ \n\n\n\n\n\nconst { Title, Text } = _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Option } = _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { TabPane } = _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nfunction ChartsPage() {\n    const [selectedSymbol, setSelectedSymbol] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"000001.XSHE\");\n    const [selectedStock, setSelectedStock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        symbol: \"000001.XSHE\",\n        name: \"平安银行\"\n    });\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchOptions, setSearchOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        \"000001.XSHE\",\n        \"600036.XSHG\"\n    ]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟股票搜索数据\n    const mockStocks = [\n        {\n            value: \"000001.XSHE\",\n            label: \"平安银行\",\n            symbol: \"000001\"\n        },\n        {\n            value: \"000002.XSHE\",\n            label: \"万科A\",\n            symbol: \"000002\"\n        },\n        {\n            value: \"600036.XSHG\",\n            label: \"招商银行\",\n            symbol: \"600036\"\n        },\n        {\n            value: \"600519.XSHG\",\n            label: \"贵州茅台\",\n            symbol: \"600519\"\n        },\n        {\n            value: \"000858.XSHE\",\n            label: \"五粮液\",\n            symbol: \"000858\"\n        },\n        {\n            value: \"002415.XSHE\",\n            label: \"海康威视\",\n            symbol: \"002415\"\n        },\n        {\n            value: \"300059.XSHE\",\n            label: \"东方财富\",\n            symbol: \"300059\"\n        },\n        {\n            value: \"600887.XSHG\",\n            label: \"伊利股份\",\n            symbol: \"600887\"\n        }\n    ];\n    // 搜索股票\n    const handleSearch = (value)=>{\n        setSearchValue(value);\n        if (value) {\n            const filtered = mockStocks.filter((stock)=>stock.label.includes(value) || stock.symbol.includes(value) || stock.value.includes(value));\n            setSearchOptions(filtered);\n        } else {\n            setSearchOptions([]);\n        }\n    };\n    // 选择股票\n    const handleStockSelect = (value)=>{\n        const stock = mockStocks.find((s)=>s.value === value);\n        if (stock) {\n            setSelectedSymbol(value);\n            setSelectedStock({\n                symbol: value,\n                name: stock.label\n            });\n            setSearchValue(\"\");\n            setSearchOptions([]);\n        }\n    };\n    // 切换收藏\n    const toggleFavorite = (symbol)=>{\n        setFavorites((prev)=>{\n            if (prev.includes(symbol)) {\n                return prev.filter((s)=>s !== symbol);\n            } else {\n                return [\n                    ...prev,\n                    symbol\n                ];\n            }\n        });\n        _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(favorites.includes(symbol) ? \"已取消收藏\" : \"已添加到收藏\");\n    };\n    // 分享图表\n    const handleShare = ()=>{\n        navigator.clipboard.writeText(window.location.href);\n        _barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"链接已复制到剪贴板\");\n    };\n    // 全屏显示\n    const handleFullscreen = ()=>{\n        const element = document.documentElement;\n        if (element.requestFullscreen) {\n            element.requestFullscreen();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between flex-wrap gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                level: 2,\n                                className: \"!mb-2\",\n                                children: \"图表分析\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                type: \"secondary\",\n                                children: \"专业的股票图表分析工具\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        wrap: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 19\n                                }, void 0),\n                                onClick: handleShare,\n                                children: \"分享\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 19\n                                }, void 0),\n                                onClick: handleFullscreen,\n                                children: \"全屏\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    value: searchValue,\n                                    options: searchOptions.map((option)=>({\n                                            value: option.value,\n                                            label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        type: \"secondary\",\n                                                        className: \"text-xs\",\n                                                        children: option.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        })),\n                                    onSearch: handleSearch,\n                                    onSelect: handleStockSelect,\n                                    placeholder: \"搜索股票代码或名称\",\n                                    style: {\n                                        width: 300\n                                    },\n                                    allowClear: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        placeholder: \"搜索股票代码或名称\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            className: \"text-lg\",\n                                            children: selectedStock.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            children: selectedStock.symbol\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: \"text\",\n                                            size: \"small\",\n                                            icon: favorites.includes(selectedSymbol) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_SearchOutlined_ShareAltOutlined_StarFilled_StarOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            onClick: ()=>toggleFavorite(selectedSymbol)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    className: \"text-sm\",\n                                    children: \"收藏:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    children: favorites.map((symbol)=>{\n                                        const stock = mockStocks.find((s)=>s.value === symbol);\n                                        return stock ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            type: selectedSymbol === symbol ? \"primary\" : \"default\",\n                                            size: \"small\",\n                                            onClick: ()=>handleStockSelect(symbol),\n                                            children: stock.label\n                                        }, symbol, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this) : null;\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_17__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    defaultActiveKey: \"kline\",\n                    className: \"bg-white rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"K线图\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                bodyStyle: {\n                                    padding: \"24px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_CandlestickChart__WEBPACK_IMPORTED_MODULE_2__.CandlestickChart, {\n                                    symbol: selectedSymbol,\n                                    height: 600,\n                                    onSymbolChange: handleStockSelect\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this)\n                        }, \"kline\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"分时图\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                bodyStyle: {\n                                    padding: \"24px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_charts_MarketOverviewChart__WEBPACK_IMPORTED_MODULE_3__.MarketOverviewChart, {\n                                    height: 600\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, \"timeshare\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"技术指标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                bodyStyle: {\n                                    padding: \"24px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-96\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                className: \"text-lg\",\n                                                children: \"技术指标功能开发中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: \"即将支持MACD、RSI、KDJ等常用技术指标\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, \"indicators\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPane, {\n                            tab: \"对比分析\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                bodyStyle: {\n                                    padding: \"24px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-96\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                className: \"text-lg\",\n                                                children: \"对比分析功能开发中...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: \"即将支持多股票对比、行业对比等功能\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        }, \"compare\", false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        lg: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            title: \"股票信息\",\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"股票代码:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: selectedStock.symbol\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"股票名称:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: selectedStock.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"所属市场:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"深圳证券交易所\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"所属行业:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"银行业\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        lg: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            title: \"实时数据\",\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"最新价:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                className: \"text-red-500\",\n                                                children: \"\\xa512.45\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"涨跌额:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                className: \"text-red-500\",\n                                                children: \"+0.23\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"涨跌幅:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                className: \"text-red-500\",\n                                                children: \"+1.88%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"成交量:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"1.25亿\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        xs: 24,\n                        lg: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AutoComplete_Button_Card_Col_Input_Row_Space_Tabs_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            title: \"技术指标\",\n                            size: \"small\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"MA5:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"\\xa512.38\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"MA10:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"\\xa512.25\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"MA20:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"\\xa512.15\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"RSI:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                children: \"65.2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\market\\\\charts\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/market/charts/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Result,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Result,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/result/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Result,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * 全局错误页面\n * \n * 捕获应用中的未处理错误\n */ \n\nconst { Paragraph, Text } = _barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 记录错误到监控服务\n        console.error(\"Application error:\", error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                status: \"500\",\n                title: \"500\",\n                subTitle: \"抱歉，服务器出现了一些问题。\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    type: \"primary\",\n                                    onClick: reset,\n                                    children: \"重试\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onClick: ()=>window.location.href = \"/dashboard\",\n                                    children: \"返回首页\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, void 0),\n                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    className: \"text-red-600 block mb-2\",\n                                    children: \"开发模式错误信息:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 19\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    className: \"text-red-600 text-sm font-mono mb-0\",\n                                    children: error.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 19\n                                }, void 0),\n                                error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    className: \"text-red-500 text-xs mt-2 mb-0\",\n                                    children: [\n                                        \"错误ID: \",\n                                        error.digest\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 21\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 17\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * 全局加载页面\n * \n * 在页面切换时显示的加载状态\n */ \n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spin_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    size: \"large\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFQTs7OztDQUlDLEdBRXlCO0FBQ0U7QUFFYixTQUFTRTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNILHdFQUFJQTtvQkFBQ0ksTUFBSzs7Ozs7OzhCQUNYLDhEQUFDRjtvQkFBSUMsV0FBVTs4QkFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTVDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vanFkYXRhLWZyb250ZW5kLy4vc3JjL2FwcC9sb2FkaW5nLnRzeD85Y2Q5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuLyoqXG4gKiDlhajlsYDliqDovb3pobXpnaJcbiAqIFxuICog5Zyo6aG16Z2i5YiH5o2i5pe25pi+56S655qE5Yqg6L2954q25oCBXG4gKi9cblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNwaW4gfSBmcm9tICdhbnRkJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy1ncmF5LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgIDxTcGluIHNpemU9XCJsYXJnZVwiIC8+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAg5Yqg6L295LitLi4uXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTcGluIiwiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Result_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Result!=!antd */ \"(ssr)/./node_modules/antd/es/result/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Result_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Result!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\n * 404 页面\n * \n * 当用户访问不存在的页面时显示\n */ \n\n\nfunction NotFound() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                status: \"404\",\n                title: \"404\",\n                subTitle: \"抱歉，您访问的页面不存在。\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            type: \"primary\",\n                            onClick: ()=>router.push(\"/dashboard\"),\n                            children: \"返回首页\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Result_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onClick: ()=>router.back(),\n                            children: \"返回上页\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/CandlestickChart.tsx":
/*!****************************************************!*\
  !*** ./src/components/charts/CandlestickChart.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CandlestickChart: () => (/* binding */ CandlestickChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/switch/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin,Switch,Tooltip!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartOutlined,ReloadOutlined,SettingOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var echarts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! echarts */ \"(ssr)/./node_modules/echarts/index.js\");\n/* __next_internal_client_entry_do_not_use__ CandlestickChart auto */ \n/**\n * K线图表组件\n * \n * 使用ECharts显示股票K线图，支持技术指标、成交量等\n */ \n\n\n\nconst { Option } = _barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst CandlestickChart = ({ symbol = \"000001.XSHE\", height = 600, showControls = true, data, onSymbolChange })=>{\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chartInstance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [timeframe, setTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"daily\");\n    const [showVolume, setShowVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMA, setShowMA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 生成模拟K线数据\n    const generateMockData = ()=>{\n        const data = [];\n        let basePrice = 12.5;\n        const now = new Date();\n        for(let i = 60; i >= 0; i--){\n            const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);\n            // 模拟价格波动\n            const change = (Math.random() - 0.5) * 0.8;\n            const open = basePrice;\n            const close = basePrice + change;\n            const high = Math.max(open, close) + Math.random() * 0.3;\n            const low = Math.min(open, close) - Math.random() * 0.3;\n            const volume = Math.floor(Math.random() * 50000000) + 10000000;\n            data.push({\n                date: date.toISOString().split(\"T\")[0],\n                open: Number(open.toFixed(2)),\n                high: Number(high.toFixed(2)),\n                low: Number(low.toFixed(2)),\n                close: Number(close.toFixed(2)),\n                volume\n            });\n            basePrice = close;\n        }\n        return data;\n    };\n    // 计算移动平均线\n    const calculateMA = (data, period)=>{\n        const ma = [];\n        for(let i = 0; i < data.length; i++){\n            if (i < period - 1) {\n                ma.push(NaN);\n            } else {\n                let sum = 0;\n                for(let j = 0; j < period; j++){\n                    sum += data[i - j].close;\n                }\n                ma.push(Number((sum / period).toFixed(2)));\n            }\n        }\n        return ma;\n    };\n    // 初始化图表\n    const initChart = ()=>{\n        if (!chartRef.current) return;\n        // 销毁现有图表实例\n        if (chartInstance.current) {\n            chartInstance.current.dispose();\n        }\n        // 创建新的图表实例\n        chartInstance.current = echarts__WEBPACK_IMPORTED_MODULE_2__.init(chartRef.current);\n        const chartData = data || generateMockData();\n        // 准备数据\n        const dates = chartData.map((item)=>item.date);\n        const ohlcData = chartData.map((item)=>[\n                item.open,\n                item.close,\n                item.low,\n                item.high\n            ]);\n        const volumes = chartData.map((item)=>item.volume);\n        // 计算移动平均线\n        const ma5 = calculateMA(chartData, 5);\n        const ma10 = calculateMA(chartData, 10);\n        const ma20 = calculateMA(chartData, 20);\n        const option = {\n            backgroundColor: \"transparent\",\n            animation: true,\n            legend: {\n                data: [\n                    \"K线\",\n                    \"MA5\",\n                    \"MA10\",\n                    \"MA20\",\n                    \"成交量\"\n                ],\n                top: 10,\n                textStyle: {\n                    color: \"#8392A5\",\n                    fontSize: 12\n                }\n            },\n            grid: [\n                {\n                    left: \"3%\",\n                    right: \"4%\",\n                    top: \"15%\",\n                    height: showVolume ? \"60%\" : \"75%\"\n                },\n                ...showVolume ? [\n                    {\n                        left: \"3%\",\n                        right: \"4%\",\n                        top: \"80%\",\n                        height: \"15%\"\n                    }\n                ] : []\n            ],\n            xAxis: [\n                {\n                    type: \"category\",\n                    data: dates,\n                    boundaryGap: false,\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12,\n                        formatter: (value)=>{\n                            const date = new Date(value);\n                            return `${date.getMonth() + 1}/${date.getDate()}`;\n                        }\n                    },\n                    axisTick: {\n                        show: false\n                    },\n                    splitLine: {\n                        show: false\n                    }\n                },\n                ...showVolume ? [\n                    {\n                        type: \"category\",\n                        gridIndex: 1,\n                        data: dates,\n                        boundaryGap: false,\n                        axisLine: {\n                            lineStyle: {\n                                color: \"#8392A5\"\n                            }\n                        },\n                        axisLabel: {\n                            show: false\n                        },\n                        axisTick: {\n                            show: false\n                        },\n                        splitLine: {\n                            show: false\n                        }\n                    }\n                ] : []\n            ],\n            yAxis: [\n                {\n                    type: \"value\",\n                    scale: true,\n                    position: \"right\",\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12,\n                        formatter: (value)=>value.toFixed(2)\n                    },\n                    splitLine: {\n                        show: true,\n                        lineStyle: {\n                            color: \"#E6E8EB\",\n                            type: \"dashed\"\n                        }\n                    }\n                },\n                ...showVolume ? [\n                    {\n                        type: \"value\",\n                        gridIndex: 1,\n                        position: \"right\",\n                        axisLine: {\n                            lineStyle: {\n                                color: \"#8392A5\"\n                            }\n                        },\n                        axisLabel: {\n                            color: \"#8392A5\",\n                            fontSize: 12,\n                            formatter: (value)=>(value / 10000).toFixed(0) + \"w\"\n                        },\n                        splitLine: {\n                            show: false\n                        }\n                    }\n                ] : []\n            ],\n            tooltip: {\n                trigger: \"axis\",\n                axisPointer: {\n                    type: \"cross\",\n                    crossStyle: {\n                        color: \"#999\"\n                    }\n                },\n                backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n                borderColor: \"#E6E8EB\",\n                borderWidth: 1,\n                textStyle: {\n                    color: \"#333\",\n                    fontSize: 12\n                },\n                formatter: (params)=>{\n                    const dataIndex = params[0].dataIndex;\n                    const item = chartData[dataIndex];\n                    const change = item.close - item.open;\n                    const changePct = (change / item.open * 100).toFixed(2);\n                    return `\n            <div style=\"padding: 8px;\">\n              <div style=\"margin-bottom: 8px; font-weight: bold;\">${item.date}</div>\n              <div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px;\">\n                <div>开盘: ${item.open.toFixed(2)}</div>\n                <div>收盘: ${item.close.toFixed(2)}</div>\n                <div>最高: ${item.high.toFixed(2)}</div>\n                <div>最低: ${item.low.toFixed(2)}</div>\n                <div>涨跌: <span style=\"color: ${change >= 0 ? \"#ef4444\" : \"#22c55e\"}\">${change >= 0 ? \"+\" : \"\"}${change.toFixed(2)}</span></div>\n                <div>涨幅: <span style=\"color: ${change >= 0 ? \"#ef4444\" : \"#22c55e\"}\">${change >= 0 ? \"+\" : \"\"}${changePct}%</span></div>\n                <div colspan=\"2\">成交量: ${(item.volume / 10000).toFixed(0)}万</div>\n              </div>\n            </div>\n          `;\n                }\n            },\n            series: [\n                {\n                    name: \"K线\",\n                    type: \"candlestick\",\n                    data: ohlcData,\n                    itemStyle: {\n                        color: \"#ef4444\",\n                        color0: \"#22c55e\",\n                        borderColor: \"#ef4444\",\n                        borderColor0: \"#22c55e\"\n                    },\n                    emphasis: {\n                        itemStyle: {\n                            borderWidth: 2\n                        }\n                    }\n                },\n                ...showMA ? [\n                    {\n                        name: \"MA5\",\n                        type: \"line\",\n                        data: ma5,\n                        smooth: true,\n                        lineStyle: {\n                            color: \"#1890ff\",\n                            width: 1\n                        },\n                        symbol: \"none\"\n                    },\n                    {\n                        name: \"MA10\",\n                        type: \"line\",\n                        data: ma10,\n                        smooth: true,\n                        lineStyle: {\n                            color: \"#faad14\",\n                            width: 1\n                        },\n                        symbol: \"none\"\n                    },\n                    {\n                        name: \"MA20\",\n                        type: \"line\",\n                        data: ma20,\n                        smooth: true,\n                        lineStyle: {\n                            color: \"#722ed1\",\n                            width: 1\n                        },\n                        symbol: \"none\"\n                    }\n                ] : [],\n                ...showVolume ? [\n                    {\n                        name: \"成交量\",\n                        type: \"bar\",\n                        xAxisIndex: 1,\n                        yAxisIndex: 1,\n                        data: volumes.map((vol, index)=>({\n                                value: vol,\n                                itemStyle: {\n                                    color: chartData[index].close >= chartData[index].open ? \"#ef4444\" : \"#22c55e\",\n                                    opacity: 0.7\n                                }\n                            }))\n                    }\n                ] : []\n            ],\n            dataZoom: [\n                {\n                    type: \"inside\",\n                    xAxisIndex: [\n                        0,\n                        1\n                    ],\n                    start: 70,\n                    end: 100\n                },\n                {\n                    show: true,\n                    xAxisIndex: [\n                        0,\n                        1\n                    ],\n                    type: \"slider\",\n                    top: \"90%\",\n                    start: 70,\n                    end: 100,\n                    height: 20\n                }\n            ]\n        };\n        chartInstance.current.setOption(option);\n        setLoading(false);\n    };\n    // 处理窗口大小变化\n    const handleResize = ()=>{\n        if (chartInstance.current) {\n            chartInstance.current.resize();\n        }\n    };\n    // 刷新数据\n    const handleRefresh = ()=>{\n        setLoading(true);\n        setTimeout(()=>{\n            initChart();\n        }, 500);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initChart();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n            if (chartInstance.current) {\n                chartInstance.current.dispose();\n            }\n        };\n    }, [\n        symbol,\n        timeframe,\n        showVolume,\n        showMA,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4 flex-wrap gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        wrap: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                value: symbol,\n                                onChange: onSymbolChange,\n                                style: {\n                                    width: 150\n                                },\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"000001.XSHE\",\n                                        children: \"平安银行\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"000002.XSHE\",\n                                        children: \"万科A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"600036.XSHG\",\n                                        children: \"招商银行\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"600519.XSHG\",\n                                        children: \"贵州茅台\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                value: timeframe,\n                                onChange: setTimeframe,\n                                style: {\n                                    width: 80\n                                },\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"1m\",\n                                        children: \"1分\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"5m\",\n                                        children: \"5分\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"15m\",\n                                        children: \"15分\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"30m\",\n                                        children: \"30分\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"1h\",\n                                        children: \"1时\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"daily\",\n                                        children: \"日线\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        wrap: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                title: \"显示移动平均线\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    checked: showMA,\n                                    onChange: setShowMA,\n                                    size: \"small\",\n                                    checkedChildren: \"MA\",\n                                    unCheckedChildren: \"MA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                title: \"显示成交量\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    checked: showVolume,\n                                    onChange: setShowVolume,\n                                    size: \"small\",\n                                    checkedChildren: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 34\n                                    }, void 0),\n                                    unCheckedChildren: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 36\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: handleRefresh,\n                                loading: loading,\n                                children: \"刷新\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartOutlined_ReloadOutlined_SettingOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 21\n                                }, void 0),\n                                children: \"设置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                        lineNumber: 399,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                lineNumber: 370,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_Switch_Tooltip_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chartRef,\n                        style: {\n                            height: `${height}px`,\n                            width: \"100%\"\n                        },\n                        className: \"transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\CandlestickChart.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/CandlestickChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/MarketOverviewChart.tsx":
/*!*******************************************************!*\
  !*** ./src/components/charts/MarketOverviewChart.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverviewChart: () => (/* binding */ MarketOverviewChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Select,Space,Spin!=!antd */ \"(ssr)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,ReloadOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ReloadOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_FullscreenOutlined_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FullscreenOutlined,ReloadOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/FullscreenOutlined.js\");\n/* harmony import */ var echarts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! echarts */ \"(ssr)/./node_modules/echarts/index.js\");\n/* __next_internal_client_entry_do_not_use__ MarketOverviewChart auto */ \n/**\n * 市场概览图表组件\n * \n * 使用ECharts显示市场指数走势图\n */ \n\n\n\nconst { Option } = _barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst MarketOverviewChart = ({ height = 400, showControls = true })=>{\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const chartInstance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"000300.XSHG\"); // 沪深300\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1D\");\n    // 模拟数据生成\n    const generateMockData = ()=>{\n        const now = new Date();\n        const time = [];\n        const values = [];\n        const volumes = [];\n        let baseValue = 4200;\n        let baseVolume = 50000000;\n        for(let i = 0; i < 240; i++){\n            const currentTime = new Date(now.getTime() - (240 - i) * 60 * 1000);\n            time.push(currentTime.toLocaleTimeString(\"zh-CN\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            }));\n            // 模拟价格波动\n            const change = (Math.random() - 0.5) * 20;\n            baseValue += change;\n            values.push(Number(baseValue.toFixed(2)));\n            // 模拟成交量波动\n            const volumeChange = (Math.random() - 0.5) * 10000000;\n            baseVolume += volumeChange;\n            volumes.push(Math.max(baseVolume, 10000000));\n        }\n        return {\n            time,\n            values,\n            volumes\n        };\n    };\n    // 初始化图表\n    const initChart = ()=>{\n        if (!chartRef.current) return;\n        // 销毁现有图表实例\n        if (chartInstance.current) {\n            chartInstance.current.dispose();\n        }\n        // 创建新的图表实例\n        chartInstance.current = echarts__WEBPACK_IMPORTED_MODULE_2__.init(chartRef.current);\n        const data = generateMockData();\n        const option = {\n            backgroundColor: \"transparent\",\n            grid: [\n                {\n                    left: \"3%\",\n                    right: \"4%\",\n                    top: \"10%\",\n                    height: \"60%\"\n                },\n                {\n                    left: \"3%\",\n                    right: \"4%\",\n                    top: \"75%\",\n                    height: \"20%\"\n                }\n            ],\n            xAxis: [\n                {\n                    type: \"category\",\n                    data: data.time,\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12\n                    },\n                    axisTick: {\n                        show: false\n                    },\n                    splitLine: {\n                        show: false\n                    }\n                },\n                {\n                    type: \"category\",\n                    gridIndex: 1,\n                    data: data.time,\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12\n                    },\n                    axisTick: {\n                        show: false\n                    },\n                    splitLine: {\n                        show: false\n                    }\n                }\n            ],\n            yAxis: [\n                {\n                    type: \"value\",\n                    scale: true,\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12,\n                        formatter: (value)=>value.toFixed(0)\n                    },\n                    splitLine: {\n                        show: true,\n                        lineStyle: {\n                            color: \"#E6E8EB\",\n                            type: \"dashed\"\n                        }\n                    }\n                },\n                {\n                    type: \"value\",\n                    gridIndex: 1,\n                    axisLine: {\n                        lineStyle: {\n                            color: \"#8392A5\"\n                        }\n                    },\n                    axisLabel: {\n                        color: \"#8392A5\",\n                        fontSize: 12,\n                        formatter: (value)=>(value / 100000000).toFixed(1) + \"亿\"\n                    },\n                    splitLine: {\n                        show: true,\n                        lineStyle: {\n                            color: \"#E6E8EB\",\n                            type: \"dashed\"\n                        }\n                    }\n                }\n            ],\n            tooltip: {\n                trigger: \"axis\",\n                axisPointer: {\n                    type: \"cross\",\n                    crossStyle: {\n                        color: \"#999\"\n                    }\n                },\n                backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n                borderColor: \"#E6E8EB\",\n                borderWidth: 1,\n                textStyle: {\n                    color: \"#333\",\n                    fontSize: 12\n                },\n                formatter: (params)=>{\n                    const dataIndex = params[0].dataIndex;\n                    const time = data.time[dataIndex];\n                    const value = data.values[dataIndex];\n                    const volume = data.volumes[dataIndex];\n                    return `\n            <div style=\"padding: 8px;\">\n              <div style=\"margin-bottom: 4px; font-weight: bold;\">${time}</div>\n              <div style=\"margin-bottom: 2px;\">\n                <span style=\"color: #1890ff;\">●</span> \n                指数: ${value.toFixed(2)}\n              </div>\n              <div>\n                <span style=\"color: #52c41a;\">●</span> \n                成交量: ${(volume / 100000000).toFixed(2)}亿\n              </div>\n            </div>\n          `;\n                }\n            },\n            series: [\n                {\n                    name: \"指数\",\n                    type: \"line\",\n                    data: data.values,\n                    smooth: true,\n                    lineStyle: {\n                        color: \"#1890ff\",\n                        width: 2\n                    },\n                    itemStyle: {\n                        color: \"#1890ff\"\n                    },\n                    areaStyle: {\n                        color: new echarts__WEBPACK_IMPORTED_MODULE_2__.graphic.LinearGradient(0, 0, 0, 1, [\n                            {\n                                offset: 0,\n                                color: \"rgba(24, 144, 255, 0.3)\"\n                            },\n                            {\n                                offset: 1,\n                                color: \"rgba(24, 144, 255, 0.05)\"\n                            }\n                        ])\n                    },\n                    symbol: \"none\",\n                    emphasis: {\n                        focus: \"series\"\n                    }\n                },\n                {\n                    name: \"成交量\",\n                    type: \"bar\",\n                    xAxisIndex: 1,\n                    yAxisIndex: 1,\n                    data: data.volumes,\n                    itemStyle: {\n                        color: \"#52c41a\",\n                        opacity: 0.7\n                    },\n                    emphasis: {\n                        itemStyle: {\n                            opacity: 1\n                        }\n                    }\n                }\n            ],\n            animation: true,\n            animationDuration: 1000,\n            animationEasing: \"cubicOut\"\n        };\n        chartInstance.current.setOption(option);\n        setLoading(false);\n    };\n    // 处理窗口大小变化\n    const handleResize = ()=>{\n        if (chartInstance.current) {\n            chartInstance.current.resize();\n        }\n    };\n    // 刷新数据\n    const handleRefresh = ()=>{\n        setLoading(true);\n        setTimeout(()=>{\n            initChart();\n        }, 500);\n    };\n    // 全屏显示\n    const handleFullscreen = ()=>{\n        if (chartRef.current) {\n            if (chartRef.current.requestFullscreen) {\n                chartRef.current.requestFullscreen();\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initChart();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n            if (chartInstance.current) {\n                chartInstance.current.dispose();\n            }\n        };\n    }, [\n        selectedIndex,\n        timeRange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            showControls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                value: selectedIndex,\n                                onChange: setSelectedIndex,\n                                style: {\n                                    width: 120\n                                },\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"000300.XSHG\",\n                                        children: \"沪深300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"000001.XSHG\",\n                                        children: \"上证指数\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"399001.XSHE\",\n                                        children: \"深证成指\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"399006.XSHE\",\n                                        children: \"创业板指\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                value: timeRange,\n                                onChange: setTimeRange,\n                                style: {\n                                    width: 80\n                                },\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"1D\",\n                                        children: \"日内\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"5D\",\n                                        children: \"5日\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"1M\",\n                                        children: \"1月\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"3M\",\n                                        children: \"3月\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: handleRefresh,\n                                loading: loading,\n                                children: \"刷新\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"text\",\n                                size: \"small\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FullscreenOutlined_ReloadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 21\n                                }, void 0),\n                                onClick: handleFullscreen,\n                                children: \"全屏\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                lineNumber: 285,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Select_Space_Spin_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: \"large\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: chartRef,\n                        style: {\n                            height: `${height}px`,\n                            width: \"100%\"\n                        },\n                        className: \"transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\components\\\\charts\\\\MarketOverviewChart.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/MarketOverviewChart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/**\n * API服务层\n * \n * 提供统一的HTTP请求接口，包含认证、错误处理、请求拦截等功能\n */ \n\n// import Cookies from 'js-cookie';\n// 简单的 cookie 工具函数\nconst CookieUtils = {\n    get: (name)=>{\n        if (typeof document === \"undefined\") return undefined;\n        const value = `; ${document.cookie}`;\n        const parts = value.split(`; ${name}=`);\n        if (parts.length === 2) return parts.pop()?.split(\";\").shift();\n        return undefined;\n    },\n    set: (name, value, options)=>{\n        if (typeof document === \"undefined\") return;\n        let cookieString = `${name}=${value}`;\n        if (options?.expires) {\n            const date = new Date();\n            date.setTime(date.getTime() + options.expires * 24 * 60 * 60 * 1000);\n            cookieString += `; expires=${date.toUTCString()}`;\n        }\n        cookieString += \"; path=/\";\n        document.cookie = cookieString;\n    },\n    remove: (name)=>{\n        if (typeof document === \"undefined\") return;\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n    }\n};\n// =============================================================================\n// 常量定义\n// =============================================================================\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\nconst API_TIMEOUT = 30000; // 30秒超时\nconst TOKEN_KEY = \"access_token\";\nconst REFRESH_TOKEN_KEY = \"refresh_token\";\n// =============================================================================\n// API客户端类\n// =============================================================================\nclass ApiClient {\n    constructor(){\n        this.isRefreshing = false;\n        this.failedQueue = [];\n        this.instance = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n            baseURL: API_BASE_URL,\n            timeout: API_TIMEOUT,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\"\n            }\n        });\n        this.setupInterceptors();\n    }\n    /**\n   * 设置请求和响应拦截器\n   */ setupInterceptors() {\n        // 请求拦截器\n        this.instance.interceptors.request.use((config)=>{\n            // 添加认证token\n            const token = this.getAccessToken();\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            // 添加请求ID\n            config.headers[\"X-Request-ID\"] = this.generateRequestId();\n            // 添加时间戳\n            config.headers[\"X-Timestamp\"] = Date.now().toString();\n            return config;\n        }, (error)=>{\n            return Promise.reject(error);\n        });\n        // 响应拦截器\n        this.instance.interceptors.response.use((response)=>{\n            return response;\n        }, async (error)=>{\n            const originalRequest = error.config;\n            // 处理401未授权错误\n            if (error.response?.status === 401 && !originalRequest._retry) {\n                if (this.isRefreshing) {\n                    // 如果正在刷新token，将请求加入队列\n                    return new Promise((resolve, reject)=>{\n                        this.failedQueue.push({\n                            resolve,\n                            reject\n                        });\n                    }).then(()=>{\n                        return this.instance(originalRequest);\n                    }).catch((err)=>{\n                        return Promise.reject(err);\n                    });\n                }\n                originalRequest._retry = true;\n                this.isRefreshing = true;\n                try {\n                    const newTokens = await this.refreshToken();\n                    this.setTokens(newTokens);\n                    this.processQueue(null);\n                    // 重新发送原始请求\n                    return this.instance(originalRequest);\n                } catch (refreshError) {\n                    this.processQueue(refreshError);\n                    this.clearTokens();\n                    this.redirectToLogin();\n                    return Promise.reject(refreshError);\n                } finally{\n                    this.isRefreshing = false;\n                }\n            }\n            // 处理其他错误\n            this.handleError(error);\n            return Promise.reject(error);\n        });\n    }\n    /**\n   * 处理队列中的请求\n   */ processQueue(error) {\n        this.failedQueue.forEach(({ resolve, reject })=>{\n            if (error) {\n                reject(error);\n            } else {\n                resolve();\n            }\n        });\n        this.failedQueue = [];\n    }\n    /**\n   * 处理API错误\n   */ handleError(error) {\n        const response = error.response;\n        const status = response?.status;\n        const data = response?.data;\n        let errorMessage = \"网络请求失败\";\n        if (status) {\n            switch(status){\n                case 400:\n                    errorMessage = data?.message || \"请求参数错误\";\n                    break;\n                case 401:\n                    errorMessage = \"未授权，请重新登录\";\n                    break;\n                case 403:\n                    errorMessage = \"权限不足\";\n                    break;\n                case 404:\n                    errorMessage = \"请求的资源不存在\";\n                    break;\n                case 429:\n                    errorMessage = \"请求过于频繁，请稍后重试\";\n                    break;\n                case 500:\n                    errorMessage = \"服务器内部错误\";\n                    break;\n                case 502:\n                    errorMessage = \"网关错误\";\n                    break;\n                case 503:\n                    errorMessage = \"服务暂时不可用\";\n                    break;\n                default:\n                    errorMessage = data?.message || `请求失败 (${status})`;\n            }\n        } else if (error.code === \"ECONNABORTED\") {\n            errorMessage = \"请求超时\";\n        } else if (error.message === \"Network Error\") {\n            errorMessage = \"网络连接失败\";\n        }\n        // 显示错误消息\n        _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(errorMessage);\n    }\n    /**\n   * 生成请求ID\n   */ generateRequestId() {\n        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\n   * 获取访问token\n   */ getAccessToken() {\n        return CookieUtils.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY);\n    }\n    /**\n   * 获取刷新token\n   */ getRefreshToken() {\n        return CookieUtils.get(REFRESH_TOKEN_KEY) || localStorage.getItem(REFRESH_TOKEN_KEY);\n    }\n    /**\n   * 设置tokens\n   */ setTokens(tokens) {\n        const { accessToken, refreshToken } = tokens;\n        // 设置到Cookie（7天过期）\n        CookieUtils.set(TOKEN_KEY, accessToken, {\n            expires: 7\n        });\n        CookieUtils.set(REFRESH_TOKEN_KEY, refreshToken, {\n            expires: 7\n        });\n        // 设置到localStorage作为备份\n        localStorage.setItem(TOKEN_KEY, accessToken);\n        localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);\n    }\n    /**\n   * 清除tokens\n   */ clearTokens() {\n        CookieUtils.remove(TOKEN_KEY);\n        CookieUtils.remove(REFRESH_TOKEN_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    }\n    /**\n   * 刷新token\n   */ async refreshToken() {\n        const refreshToken = this.getRefreshToken();\n        if (!refreshToken) {\n            throw new Error(\"No refresh token available\");\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${API_BASE_URL}/api/v1/auth/refresh`, {\n            refreshToken\n        });\n        return response.data.data;\n    }\n    /**\n   * 重定向到登录页\n   */ redirectToLogin() {\n        if (false) {}\n    }\n    // =============================================================================\n    // 公共方法\n    // =============================================================================\n    /**\n   * GET请求\n   */ async get(url, config) {\n        const response = await this.instance.get(url, config);\n        return response.data;\n    }\n    /**\n   * POST请求\n   */ async post(url, data, config) {\n        const response = await this.instance.post(url, data, config);\n        return response.data;\n    }\n    /**\n   * PUT请求\n   */ async put(url, data, config) {\n        const response = await this.instance.put(url, data, config);\n        return response.data;\n    }\n    /**\n   * DELETE请求\n   */ async delete(url, config) {\n        const response = await this.instance.delete(url, config);\n        return response.data;\n    }\n    /**\n   * PATCH请求\n   */ async patch(url, data, config) {\n        const response = await this.instance.patch(url, data, config);\n        return response.data;\n    }\n    /**\n   * 上传文件\n   */ async upload(url, file, onProgress) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const config = {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        };\n        const response = await this.instance.post(url, formData, config);\n        return response.data;\n    }\n    /**\n   * 下载文件\n   */ async download(url, filename, onProgress) {\n        const config = {\n            responseType: \"blob\",\n            onDownloadProgress: (progressEvent)=>{\n                if (onProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onProgress(progress);\n                }\n            }\n        };\n        const response = await this.instance.get(url, config);\n        // 创建下载链接\n        const blob = new Blob([\n            response.data\n        ]);\n        const downloadUrl = window.URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = downloadUrl;\n        link.download = filename || \"download\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(downloadUrl);\n    }\n    /**\n   * 设置认证tokens\n   */ setAuthTokens(tokens) {\n        this.setTokens(tokens);\n    }\n    /**\n   * 清除认证信息\n   */ clearAuth() {\n        this.clearTokens();\n    }\n    /**\n   * 检查是否已认证\n   */ isAuthenticated() {\n        return !!this.getAccessToken();\n    }\n    /**\n   * 获取原始axios实例\n   */ getInstance() {\n        return this.instance;\n    }\n}\n// =============================================================================\n// 导出API客户端实例\n// =============================================================================\nconst apiClient = new ApiClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/auth.ts":
/*!***************************!*\
  !*** ./src/store/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthActions: () => (/* binding */ useAuthActions),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(ssr)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/api */ \"(ssr)/./src/services/api.ts\");\n/**\n * 认证状态管理\n * \n * 使用Zustand管理用户认证状态，包括登录、登出、token管理等\n */ \n\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_2__.persist)((set, get)=>({\n        // 初始状态\n        user: null,\n        tokens: null,\n        isAuthenticated: false,\n        isLoading: false,\n        loadingState: \"idle\",\n        // 登录\n        login: async (loginData)=>{\n            try {\n                console.log(\"\\uD83D\\uDD10 Auth Store: 开始登录流程...\", loginData);\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                // 使用表单数据格式，因为后端使用 OAuth2PasswordRequestForm\n                const formData = new FormData();\n                formData.append(\"username\", loginData.email);\n                formData.append(\"password\", loginData.password);\n                console.log(\"\\uD83D\\uDCE1 Auth Store: 发送API请求...\");\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getInstance().post(\"/api/v1/auth/login\", formData, {\n                    headers: {\n                        \"Content-Type\": \"application/x-www-form-urlencoded\"\n                    }\n                });\n                console.log(\"\\uD83D\\uDCE5 Auth Store: 收到响应:\", response.data);\n                // 处理axios直接响应\n                const responseData = response.data;\n                if (responseData.code === 200 && responseData.data) {\n                    const { access_token, refresh_token, token_type, expires_in, user_info } = responseData.data;\n                    console.log(\"✅ Auth Store: 解析用户数据成功:\", user_info);\n                    const tokens = {\n                        accessToken: access_token,\n                        refreshToken: refresh_token || access_token,\n                        tokenType: token_type,\n                        expiresIn: expires_in\n                    };\n                    // 设置认证信息\n                    console.log(\"\\uD83D\\uDD11 Auth Store: 设置认证令牌...\");\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n                    console.log(\"\\uD83D\\uDCBE Auth Store: 更新状态...\");\n                    set({\n                        user: user_info,\n                        tokens,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    // 设置cookie以便中间件检查认证状态\n                    console.log(\"\\uD83C\\uDF6A Auth Store: 设置认证cookie...\");\n                    if (typeof document !== \"undefined\") {\n                        document.cookie = `auth-storage=${JSON.stringify({\n                            state: {\n                                isAuthenticated: true,\n                                tokens,\n                                user: user_info\n                            }\n                        })}; path=/; max-age=86400`; // 24小时过期\n                    }\n                    console.log(\"\\uD83C\\uDF89 Auth Store: 登录流程完成！\");\n                } else {\n                    console.error(\"❌ Auth Store: 响应格式错误:\", responseData);\n                    throw new Error(responseData.message || \"登录失败\");\n                }\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    loadingState: \"error\",\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false\n                });\n                const errorMessage = error.response?.data?.message || error.message || \"登录失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 注册\n        register: async (registerData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/register\", registerData);\n                if (response.code === 200) {\n                    set({\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"注册成功！请登录您的账号\");\n                } else {\n                    throw new Error(response.message || \"注册失败\");\n                }\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = error.response?.data?.message || error.message || \"注册失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 登出\n        logout: async ()=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                // 调用登出API\n                await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/logout\");\n            } catch (error) {\n                console.error(\"登出API调用失败:\", error);\n            } finally{\n                // 无论API调用是否成功，都清理本地状态\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n                set({\n                    user: null,\n                    tokens: null,\n                    isAuthenticated: false,\n                    isLoading: false,\n                    loadingState: \"idle\"\n                });\n                // 清除认证cookie\n                if (typeof document !== \"undefined\") {\n                    document.cookie = \"auth-storage=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT\";\n                }\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已安全登出\");\n            }\n        },\n        // 刷新Token\n        refreshToken: async ()=>{\n            try {\n                const { tokens } = get();\n                if (!tokens?.refreshToken) {\n                    throw new Error(\"没有刷新Token\");\n                }\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/auth/refresh\", {\n                    refresh_token: tokens.refreshToken\n                });\n                if (response.code === 200 && response.data) {\n                    const newTokens = {\n                        accessToken: response.data.access_token,\n                        refreshToken: tokens.refreshToken,\n                        tokenType: response.data.token_type,\n                        expiresIn: response.data.expires_in\n                    };\n                    _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(newTokens);\n                    set({\n                        tokens: newTokens\n                    });\n                } else {\n                    throw new Error(\"Token刷新失败\");\n                }\n            } catch (error) {\n                console.error(\"Token刷新失败:\", error);\n                // Token刷新失败，清理认证状态\n                get().clearAuth();\n                throw error;\n            }\n        },\n        // 获取当前用户信息\n        getCurrentUser: async ()=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/auth/me\");\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                } else {\n                    throw new Error(\"获取用户信息失败\");\n                }\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                // 如果是401错误，清理认证状态\n                if (error.response?.status === 401) {\n                    get().clearAuth();\n                }\n                throw error;\n            }\n        },\n        // 更新用户资料\n        updateProfile: async (profileData)=>{\n            try {\n                set({\n                    isLoading: true,\n                    loadingState: \"loading\"\n                });\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/api/v1/auth/profile\", profileData);\n                if (response.code === 200 && response.data) {\n                    set({\n                        user: response.data,\n                        isLoading: false,\n                        loadingState: \"success\"\n                    });\n                    _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"资料更新成功\");\n                } else {\n                    throw new Error(response.message || \"更新失败\");\n                }\n            } catch (error) {\n                set({\n                    isLoading: false,\n                    loadingState: \"error\"\n                });\n                const errorMessage = error.response?.data?.message || error.message || \"更新失败\";\n                throw new Error(errorMessage);\n            }\n        },\n        // 设置用户\n        setUser: (user)=>{\n            set({\n                user,\n                isAuthenticated: !!user\n            });\n        },\n        // 设置Token\n        setTokens: (tokens)=>{\n            set({\n                tokens\n            });\n            if (tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(tokens);\n            } else {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            }\n        },\n        // 设置加载状态\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        },\n        // 设置加载状态\n        setLoadingState: (loadingState)=>{\n            set({\n                loadingState\n            });\n        },\n        // 清理认证状态\n        clearAuth: ()=>{\n            _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuth();\n            set({\n                user: null,\n                tokens: null,\n                isAuthenticated: false,\n                isLoading: false,\n                loadingState: \"idle\"\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            tokens: state.tokens,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            // 恢复状态后，设置API客户端的认证信息\n            if (state?.tokens) {\n                _services_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].setAuthTokens(state.tokens);\n            }\n        },\n    // 添加 SSR 支持\n    skipHydration: true\n}));\n// 导出选择器函数\nconst useAuth = ()=>{\n    const { user, isAuthenticated, isLoading } = useAuthStore();\n    return {\n        user,\n        isAuthenticated,\n        isLoading\n    };\n};\nconst useAuthActions = ()=>{\n    const { login, register, logout, refreshToken, getCurrentUser, updateProfile } = useAuthStore();\n    return {\n        login,\n        register,\n        logout,\n        refreshToken,\n        getCurrentUser,\n        updateProfile\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0ab57f4695b4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vanFkYXRhLWZyb250ZW5kLy4vc3JjL3N0eWxlcy9nbG9iYWxzLmNzcz84NDFhIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGFiNTdmNDY5NWI0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\JQData\frontend\src\app\dashboard\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/market/charts/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/market/charts/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\JQData\frontend\src\app\dashboard\market\charts\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\JQData\frontend\src\app\error.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\nconst metadata = {\n    title: \"智能量化交易平台\",\n    description: \"基于AI的量化投资解决方案\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGd0I7QUFJdkIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQzdCSzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuLi9zdHlsZXMvZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfmmbrog73ph4/ljJbkuqTmmJPlubPlj7AnLFxuICBkZXNjcmlwdGlvbjogJ+WfuuS6jkFJ55qE6YeP5YyW5oqV6LWE6Kej5Yaz5pa55qGIJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\JQData\frontend\src\app\loading.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\JQData\frontend\src\app\not-found.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/antd","vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@ant-design","vendor-chunks/mime-db","vendor-chunks/@rc-component","vendor-chunks/axios","vendor-chunks/motion-dom","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-notification","vendor-chunks/zustand","vendor-chunks/@babel","vendor-chunks/rc-input","vendor-chunks/rc-textarea","vendor-chunks/follow-redirects","vendor-chunks/rc-overflow","vendor-chunks/debug","vendor-chunks/rc-drawer","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/form-data","vendor-chunks/use-sync-external-store","vendor-chunks/get-intrinsic","vendor-chunks/motion-utils","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/asynckit","vendor-chunks/rc-tooltip","vendor-chunks/@emotion","vendor-chunks/throttle-debounce","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/copy-to-clipboard","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/rc-picker","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/toggle-selection","vendor-chunks/es-errors","vendor-chunks/rc-pagination","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/echarts","vendor-chunks/zrender","vendor-chunks/rc-select","vendor-chunks/rc-virtual-list","vendor-chunks/rc-switch"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fmarket%2Fcharts%2Fpage&page=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&appPaths=%2Fdashboard%2Fmarket%2Fcharts%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fmarket%2Fcharts%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdministrator%5CDesktop%5CJQData%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();