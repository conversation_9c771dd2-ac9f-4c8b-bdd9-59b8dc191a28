"""
盈利能力优化器

专门用于优化机器学习模型的盈利能力，确保系统能够找到正确的赚钱路线
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.services.feature_engineering_service import feature_engineering_service
from app.services.ml_model_service import ml_model_service


class ProfitabilityOptimizer:
    """盈利能力优化器 - 系统的核心，专注于找到赚钱的策略"""
    
    def __init__(self):
        self.feature_service = feature_engineering_service
        self.ml_service = ml_model_service
        
        # 盈利优化参数
        self.min_profit_threshold = 0.02  # 最小盈利阈值2%
        self.max_risk_threshold = 0.15    # 最大风险阈值15%
        self.min_win_rate = 0.55          # 最小胜率55%
        self.min_sharpe_ratio = 1.0       # 最小夏普比率1.0
    
    async def optimize_for_profitability(
        self,
        user_id: int,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """优化模型以获得最佳盈利能力"""
        try:
            logger.info(f"开始为用户 {user_id} 优化盈利策略")
            
            # 1. 提取所有可用特征
            all_features = {}
            for symbol in symbols:
                features = await self.feature_service.extract_all_features(
                    symbol, start_date, end_date, db
                )
                all_features[symbol] = features
            
            # 2. 构建多个目标变量（不同的盈利策略）
            profit_targets = self._create_profit_targets(all_features)
            
            # 3. 测试多种模型配置
            best_strategies = []
            
            for target_name, target_data in profit_targets.items():
                logger.info(f"优化策略: {target_name}")
                
                # 准备训练数据
                X, y = self._prepare_optimization_data(all_features, target_data)
                
                if len(X) < 100:  # 数据量太少
                    logger.warning(f"数据量不足，跳过策略 {target_name}")
                    continue
                
                # 测试多种算法
                algorithms = ['xgboost', 'lightgbm', 'random_forest']
                
                for algorithm in algorithms:
                    try:
                        # 使用专门的盈利模型训练
                        model, metrics = await self._train_profit_optimized_model(
                            X, y, algorithm, target_name
                        )
                        
                        # 评估盈利潜力
                        profit_score = self._evaluate_profit_potential(metrics)
                        
                        if profit_score > 70:  # 只保留高盈利潜力的策略
                            best_strategies.append({
                                'target': target_name,
                                'algorithm': algorithm,
                                'model': model,
                                'metrics': metrics,
                                'profit_score': profit_score,
                                'expected_return': metrics.get('avg_predicted_return', 0),
                                'risk_adjusted_return': metrics.get('predicted_sharpe_ratio', 0),
                                'win_rate': metrics.get('win_rate', 0)
                            })
                            
                            logger.info(f"发现高盈利策略: {target_name} + {algorithm}, 评分: {profit_score}")
                    
                    except Exception as e:
                        logger.warning(f"策略 {target_name} + {algorithm} 训练失败: {e}")
                        continue
            
            # 4. 选择最佳策略组合
            optimal_strategies = self._select_optimal_strategies(best_strategies)
            
            # 5. 生成盈利优化报告
            optimization_report = self._generate_optimization_report(
                optimal_strategies, symbols, start_date, end_date
            )
            
            logger.info(f"盈利优化完成，发现 {len(optimal_strategies)} 个优质策略")
            
            return {
                'success': True,
                'optimal_strategies': optimal_strategies,
                'optimization_report': optimization_report,
                'total_strategies_tested': len(profit_targets) * len(algorithms),
                'profitable_strategies_found': len(best_strategies),
                'final_selected_strategies': len(optimal_strategies)
            }
            
        except Exception as e:
            logger.error(f"盈利优化失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'optimal_strategies': [],
                'optimization_report': {}
            }
    
    def _create_profit_targets(self, features_dict: Dict[str, Dict]) -> Dict[str, np.ndarray]:
        """创建多种盈利目标变量"""
        targets = {}
        
        for symbol, features in features_dict.items():
            if 'close_price' not in features:
                continue
            
            close_prices = np.array(features['close_price'])
            
            # 1. 短期盈利目标（1-3天）
            targets[f'{symbol}_short_profit'] = self._calculate_forward_returns(close_prices, 1, 3)
            
            # 2. 中期盈利目标（5-10天）
            targets[f'{symbol}_medium_profit'] = self._calculate_forward_returns(close_prices, 5, 10)
            
            # 3. 趋势跟踪目标（突破后的收益）
            targets[f'{symbol}_trend_profit'] = self._calculate_trend_following_returns(close_prices)
            
            # 4. 均值回归目标（超跌反弹收益）
            targets[f'{symbol}_reversion_profit'] = self._calculate_mean_reversion_returns(close_prices)
            
            # 5. 波动率突破目标
            targets[f'{symbol}_volatility_profit'] = self._calculate_volatility_breakout_returns(close_prices)
        
        return targets
    
    def _calculate_forward_returns(self, prices: np.ndarray, min_days: int, max_days: int) -> np.ndarray:
        """计算前瞻收益率"""
        returns = []
        for i in range(len(prices) - max_days):
            # 寻找最佳退出点
            future_prices = prices[i+min_days:i+max_days+1]
            current_price = prices[i]
            
            if len(future_prices) == 0:
                returns.append(0)
                continue
            
            # 选择最佳收益
            best_return = np.max((future_prices - current_price) / current_price)
            returns.append(best_return)
        
        # 填充剩余部分
        returns.extend([0] * max_days)
        return np.array(returns)
    
    def _calculate_trend_following_returns(self, prices: np.ndarray) -> np.ndarray:
        """计算趋势跟踪收益"""
        returns = np.zeros(len(prices))
        
        # 计算20日移动平均
        ma20 = pd.Series(prices).rolling(20).mean().values
        
        for i in range(20, len(prices) - 5):
            if prices[i] > ma20[i] and prices[i-1] <= ma20[i-1]:  # 突破信号
                # 计算未来5天的收益
                future_return = (prices[i+5] - prices[i]) / prices[i] if i+5 < len(prices) else 0
                returns[i] = future_return
        
        return returns
    
    def _calculate_mean_reversion_returns(self, prices: np.ndarray) -> np.ndarray:
        """计算均值回归收益"""
        returns = np.zeros(len(prices))
        
        # 计算20日移动平均和标准差
        ma20 = pd.Series(prices).rolling(20).mean().values
        std20 = pd.Series(prices).rolling(20).std().values
        
        for i in range(20, len(prices) - 3):
            # 超跌信号：价格低于均线2个标准差
            if prices[i] < ma20[i] - 2 * std20[i]:
                # 计算未来3天的反弹收益
                future_return = (prices[i+3] - prices[i]) / prices[i] if i+3 < len(prices) else 0
                returns[i] = future_return
        
        return returns
    
    def _calculate_volatility_breakout_returns(self, prices: np.ndarray) -> np.ndarray:
        """计算波动率突破收益"""
        returns = np.zeros(len(prices))
        
        # 计算20日波动率
        price_changes = np.diff(prices) / prices[:-1]
        volatility = pd.Series(price_changes).rolling(20).std().values
        
        for i in range(20, len(prices) - 7):
            # 低波动率后的突破
            if volatility[i-1] < np.percentile(volatility[max(0, i-60):i], 25):  # 低波动率
                price_change = abs(price_changes[i])
                if price_change > 2 * volatility[i-1]:  # 突破
                    # 计算未来7天的收益
                    future_return = (prices[i+7] - prices[i]) / prices[i] if i+7 < len(prices) else 0
                    returns[i] = future_return
        
        return returns
    
    def _prepare_optimization_data(
        self, 
        features_dict: Dict[str, Dict], 
        target_data: np.ndarray
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """准备优化数据"""
        # 合并所有特征
        all_features = []
        
        for symbol, features in features_dict.items():
            if 'dates' not in features:
                continue
            
            dates = features['dates']
            feature_data = {}
            
            for feature_name, values in features.items():
                if feature_name != 'dates' and isinstance(values, (list, np.ndarray)):
                    feature_data[f"{symbol}_{feature_name}"] = values
            
            if feature_data:
                df = pd.DataFrame(feature_data, index=dates)
                all_features.append(df)
        
        if not all_features:
            return pd.DataFrame(), pd.Series()
        
        # 合并特征
        combined_features = pd.concat(all_features, axis=1)
        
        # 确保目标数据长度匹配
        min_length = min(len(combined_features), len(target_data))
        X = combined_features.iloc[:min_length].fillna(0)
        y = pd.Series(target_data[:min_length])
        
        return X, y
    
    async def _train_profit_optimized_model(
        self, 
        X: pd.DataFrame, 
        y: pd.Series, 
        algorithm: str, 
        target_name: str
    ) -> Tuple[Any, Dict[str, float]]:
        """训练专门优化盈利的模型"""
        # 使用盈利优化的超参数
        profit_hyperparams = self._get_profit_optimized_hyperparams(algorithm)
        
        # 训练模型
        model, metrics = self.ml_service.trainer.train_profitability_model(
            X, y, algorithm, profit_hyperparams
        )
        
        return model, metrics
    
    def _get_profit_optimized_hyperparams(self, algorithm: str) -> Dict[str, Any]:
        """获取盈利优化的超参数"""
        if algorithm == 'xgboost':
            return {
                'n_estimators': 300,
                'max_depth': 10,
                'learning_rate': 0.03,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 0.1,
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            }
        elif algorithm == 'lightgbm':
            return {
                'n_estimators': 300,
                'max_depth': 10,
                'learning_rate': 0.03,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 0.1,
                'objective': 'regression',
                'metric': 'rmse'
            }
        else:
            return {
                'n_estimators': 200,
                'max_depth': 12,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            }
    
    def _evaluate_profit_potential(self, metrics: Dict[str, float]) -> float:
        """评估策略的盈利潜力（0-100分）"""
        try:
            # 获取关键指标
            direction_acc = metrics.get('direction_accuracy', 0.5)
            profit_precision = metrics.get('profit_precision', 0.5)
            win_rate = metrics.get('win_rate', 0.5)
            sharpe_ratio = metrics.get('predicted_sharpe_ratio', 0)
            avg_return = metrics.get('avg_predicted_return', 0)
            
            # 计算综合评分
            score = 0
            
            # 方向准确率权重30%
            if direction_acc > 0.6:
                score += 30 * (direction_acc - 0.5) / 0.5
            
            # 盈利精确度权重25%
            if profit_precision > 0.6:
                score += 25 * (profit_precision - 0.5) / 0.5
            
            # 胜率权重20%
            if win_rate > 0.55:
                score += 20 * (win_rate - 0.5) / 0.5
            
            # 夏普比率权重15%
            if sharpe_ratio > 0.5:
                score += 15 * min(sharpe_ratio / 2.0, 1.0)
            
            # 平均收益权重10%
            if avg_return > 0.01:
                score += 10 * min(avg_return / 0.05, 1.0)
            
            return min(100, max(0, score))
            
        except Exception as e:
            logger.error(f"评估盈利潜力失败: {e}")
            return 0
    
    def _select_optimal_strategies(self, strategies: List[Dict]) -> List[Dict]:
        """选择最优策略组合"""
        if not strategies:
            return []
        
        # 按盈利评分排序
        strategies.sort(key=lambda x: x['profit_score'], reverse=True)
        
        # 选择前5个最佳策略，确保多样性
        selected = []
        used_targets = set()
        used_algorithms = set()
        
        for strategy in strategies:
            target = strategy['target']
            algorithm = strategy['algorithm']
            
            # 确保策略多样性
            if len(selected) < 3:  # 前3个直接选择
                selected.append(strategy)
                used_targets.add(target)
                used_algorithms.add(algorithm)
            elif target not in used_targets or algorithm not in used_algorithms:
                selected.append(strategy)
                used_targets.add(target)
                used_algorithms.add(algorithm)
                
                if len(selected) >= 5:  # 最多5个策略
                    break
        
        return selected
    
    def _generate_optimization_report(
        self, 
        strategies: List[Dict], 
        symbols: List[str], 
        start_date: datetime, 
        end_date: datetime
    ) -> Dict[str, Any]:
        """生成优化报告"""
        if not strategies:
            return {
                'summary': '未找到符合盈利要求的策略',
                'recommendations': ['增加数据量', '调整参数', '尝试其他特征']
            }
        
        best_strategy = strategies[0]
        
        return {
            'summary': f'成功优化出 {len(strategies)} 个高盈利策略',
            'best_strategy': {
                'name': f"{best_strategy['target']} + {best_strategy['algorithm']}",
                'profit_score': best_strategy['profit_score'],
                'expected_return': best_strategy['expected_return'],
                'win_rate': best_strategy['win_rate'],
                'risk_adjusted_return': best_strategy['risk_adjusted_return']
            },
            'portfolio_metrics': {
                'avg_profit_score': np.mean([s['profit_score'] for s in strategies]),
                'avg_expected_return': np.mean([s['expected_return'] for s in strategies]),
                'avg_win_rate': np.mean([s['win_rate'] for s in strategies]),
                'strategy_diversity': len(set(s['target'] for s in strategies))
            },
            'recommendations': [
                '建议使用多策略组合降低风险',
                '定期重新训练模型以适应市场变化',
                '监控实际交易结果并调整参数',
                '考虑加入止损和仓位管理'
            ],
            'optimization_period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'symbols': symbols
            }
        }


# 全局盈利优化器实例
profitability_optimizer = ProfitabilityOptimizer()
