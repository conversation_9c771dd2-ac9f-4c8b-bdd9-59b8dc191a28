'use client';

/**
 * 登录页面
 * 
 * 用户登录界面，支持邮箱密码登录和记住登录状态
 */

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  Button, 
  Card, 
  Checkbox, 
  Form, 
  Input, 
  Typography, 
  message,
  Divider,
  Space
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  EyeInvisibleOutlined, 
  EyeTwoTone,
  LoginOutlined,
  GoogleOutlined,
  GithubOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { useAuthStore } from '@/store/auth';
import { LoginRequest } from '@/types';

const { Title, Text, Link: AntLink } = Typography;

interface LoginFormData {
  email: string;
  password: string;
  remember: boolean;
}

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading } = useAuthStore();
  const [form] = Form.useForm();

  const handleSubmit = async (values: LoginFormData) => {
    try {
      const loginData: LoginRequest = {
        email: values.email,
        password: values.password,
        remember: values.remember || false,
      };

      await login(loginData);
      
      message.success('登录成功！');
      router.push('/dashboard');
      
    } catch (error: any) {
      message.error(error.message || '登录失败，请重试');
    }
  };

  const handleSocialLogin = (provider: string) => {
    message.info(`${provider} 登录功能开发中...`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card 
          className="shadow-xl border-0 rounded-2xl"
          bodyStyle={{ padding: '2rem' }}
        >
          {/* 头部 */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4"
            >
              <LoginOutlined className="text-2xl text-white" />
            </motion.div>
            
            <Title level={2} className="!mb-2 !text-gray-800">
              欢迎回来
            </Title>
            <Text type="secondary" className="text-base">
              登录到 JQData 量化平台
            </Text>
          </div>

          {/* 登录表单 */}
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            size="large"
            layout="vertical"
            requiredMark={false}
          >
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input
                prefix={<UserOutlined className="text-gray-400" />}
                placeholder="请输入邮箱地址"
                className="rounded-lg"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码长度至少6位' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="请输入密码"
                className="rounded-lg"
                iconRender={(visible) => 
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
              />
            </Form.Item>

            <Form.Item className="mb-6">
              <div className="flex items-center justify-between">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>记住登录状态</Checkbox>
                </Form.Item>
                
                <AntLink 
                  href="/auth/forgot-password"
                  className="text-blue-600 hover:text-blue-700"
                >
                  忘记密码？
                </AntLink>
              </div>
            </Form.Item>

            <Form.Item className="mb-6">
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                className="w-full h-12 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600 border-0 hover:from-blue-600 hover:to-indigo-700 shadow-lg"
                size="large"
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          {/* 社交登录 */}
          <Divider className="!my-6">
            <Text type="secondary" className="text-sm">
              或使用以下方式登录
            </Text>
          </Divider>

          <Space className="w-full justify-center" size="large">
            <Button
              icon={<GoogleOutlined />}
              shape="circle"
              size="large"
              className="border-gray-300 hover:border-red-400 hover:text-red-500"
              onClick={() => handleSocialLogin('Google')}
            />
            <Button
              icon={<GithubOutlined />}
              shape="circle"
              size="large"
              className="border-gray-300 hover:border-gray-800 hover:text-gray-800"
              onClick={() => handleSocialLogin('GitHub')}
            />
          </Space>

          {/* 注册链接 */}
          <div className="text-center mt-8 pt-6 border-t border-gray-100">
            <Text type="secondary">
              还没有账号？{' '}
              <Link 
                href="/auth/register" 
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                立即注册
              </Link>
            </Text>
          </div>
        </Card>

        {/* 底部信息 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center mt-8"
        >
          <Text type="secondary" className="text-sm">
            登录即表示您同意我们的{' '}
            <AntLink href="/terms" className="text-blue-600">
              服务条款
            </AntLink>
            {' '}和{' '}
            <AntLink href="/privacy" className="text-blue-600">
              隐私政策
            </AntLink>
          </Text>
        </motion.div>
      </motion.div>
    </div>
  );
}
