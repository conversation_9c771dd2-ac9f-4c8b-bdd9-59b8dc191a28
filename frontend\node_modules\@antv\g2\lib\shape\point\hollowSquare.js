"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowSquare = void 0;
const color_1 = require("./color");
/**
 * □
 */
const HollowSquare = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'square' }, options), context);
};
exports.HollowSquare = HollowSquare;
exports.HollowSquare.props = Object.assign({ defaultMarker: 'hollowSquare' }, color_1.Color.props);
//# sourceMappingURL=hollowSquare.js.map