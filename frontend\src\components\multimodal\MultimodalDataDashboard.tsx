'use client';

/**
 * 多模态数据仪表板
 * 
 * 提供新闻情感分析、社交媒体数据、另类数据源等多模态数据的管理界面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Descriptions,
  Alert,
  Badge,
  Tooltip,
  DatePicker,
  List,
  Avatar,
  Timeline,
  Spin
} from 'antd';
import {
  GlobalOutlined,
  TwitterOutlined,
  WeiboOutlined,
  SatelliteOutlined,
  ThunderboltOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  MinusOutlined,
  EyeOutlined,
  SyncOutlined,
  Bar<PERSON>hartOutlined,
  LineChartOutlined,
  DashboardOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { Line, Column, Gauge } from '@ant-design/plots';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface DataSource {
  id: number;
  name: string;
  display_name: string;
  description: string;
  platform?: string;
  data_category?: string;
  reliability_score?: number;
  data_quality_score?: number;
  total_articles?: number;
  total_posts?: number;
  last_updated?: string;
  last_sync?: string;
}

interface SentimentData {
  symbol: string;
  sentiment_summary: {
    average_sentiment: number;
    positive_count: number;
    negative_count: number;
    neutral_count: number;
    total_articles: number;
  };
  sentiment_timeline: Array<{
    timestamp: string;
    title?: string;
    sentiment_label: string;
    sentiment_score: number;
    confidence: number;
    url?: string;
  }>;
}

interface MultimodalSignal {
  id: number;
  signal_name: string;
  signal_type: string;
  signal_value: number;
  signal_strength: number;
  confidence: number;
  signal_timestamp: string;
  data_sources: string[];
  source_weights: Record<string, number>;
  feature_importance: Record<string, number>;
}

export const MultimodalDataDashboard: React.FC = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('news');
  const [newsSources, setNewsSources] = useState<DataSource[]>([]);
  const [socialSources, setSocialSources] = useState<DataSource[]>([]);
  const [altSources, setAltSources] = useState<DataSource[]>([]);
  const [sentimentData, setSentimentData] = useState<SentimentData | null>(null);
  const [multimodalSignals, setMultimodalSignals] = useState<MultimodalSignal[]>([]);
  const [loading, setLoading] = useState(false);
  const [collectModalVisible, setCollectModalVisible] = useState(false);
  const [signalModalVisible, setSignalModalVisible] = useState(false);
  const [selectedSymbol, setSelectedSymbol] = useState('000001.XSHE');

  useEffect(() => {
    fetchDataSources();
  }, [activeTab]);

  useEffect(() => {
    if (selectedSymbol) {
      fetchSentimentData();
      fetchMultimodalSignals();
    }
  }, [selectedSymbol]);

  const fetchDataSources = async () => {
    setLoading(true);
    try {
      let endpoint = '';
      switch (activeTab) {
        case 'news':
          endpoint = '/api/v1/multimodal/news/sources';
          break;
        case 'social':
          endpoint = '/api/v1/multimodal/social/sources';
          break;
        case 'alternative':
          endpoint = '/api/v1/multimodal/alternative/sources';
          break;
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        switch (activeTab) {
          case 'news':
            setNewsSources(result.data);
            break;
          case 'social':
            setSocialSources(result.data);
            break;
          case 'alternative':
            setAltSources(result.data);
            break;
        }
      }
    } catch (error) {
      console.error('获取数据源失败:', error);
      message.error('获取数据源失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchSentimentData = async () => {
    try {
      const response = await fetch(`/api/v1/multimodal/news/sentiment/${selectedSymbol}?hours=24`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setSentimentData(result.data);
      }
    } catch (error) {
      console.error('获取情感数据失败:', error);
    }
  };

  const fetchMultimodalSignals = async () => {
    try {
      const response = await fetch(`/api/v1/multimodal/signals/${selectedSymbol}?hours=168`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setMultimodalSignals(result.data.items);
      }
    } catch (error) {
      console.error('获取多模态信号失败:', error);
    }
  };

  const handleCollectData = async (values: any) => {
    try {
      let endpoint = '';
      let payload = {};

      switch (activeTab) {
        case 'news':
          endpoint = '/api/v1/multimodal/news/collect';
          payload = {
            source_id: values.source_id,
            max_articles: values.max_items || 100
          };
          break;
        case 'social':
          endpoint = '/api/v1/multimodal/social/collect';
          payload = {
            source_id: values.source_id,
            keywords: values.keywords?.split(',').map((k: string) => k.trim()) || [],
            max_posts: values.max_items || 100
          };
          break;
        case 'alternative':
          endpoint = '/api/v1/multimodal/alternative/collect';
          payload = {
            source_id: values.source_id,
            collection_config: {
              data_type: values.data_type,
              region: values.region,
              days: values.days || 30
            }
          };
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('数据采集任务已提交');
        setCollectModalVisible(false);
        form.resetFields();
      } else {
        message.error(result.message || '数据采集失败');
      }
    } catch (error) {
      console.error('数据采集失败:', error);
      message.error('数据采集失败');
    }
  };

  const handleGenerateSignal = async (values: any) => {
    try {
      const response = await fetch('/api/v1/multimodal/signals/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          symbol: values.symbol,
          time_window: values.time_window || 24,
          signal_type: values.signal_type || 'composite'
        })
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('多模态信号生成成功');
        setSignalModalVisible(false);
        form.resetFields();
        fetchMultimodalSignals();
      } else {
        message.error(result.message || '信号生成失败');
      }
    } catch (error) {
      console.error('信号生成失败:', error);
      message.error('信号生成失败');
    }
  };

  const getSentimentIcon = (label: string) => {
    switch (label) {
      case 'positive':
        return <TrendingUpOutlined style={{ color: '#52c41a' }} />;
      case 'negative':
        return <TrendingDownOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <MinusOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getSentimentColor = (score: number) => {
    if (score > 0.1) return '#52c41a';
    if (score < -0.1) return '#ff4d4f';
    return '#faad14';
  };

  const renderSentimentChart = () => {
    if (!sentimentData?.sentiment_timeline) return null;

    const data = sentimentData.sentiment_timeline.map(item => ({
      timestamp: dayjs(item.timestamp).format('MM-DD HH:mm'),
      sentiment: item.sentiment_score,
      confidence: item.confidence
    }));

    const config = {
      data,
      xField: 'timestamp',
      yField: 'sentiment',
      smooth: true,
      color: '#1890ff',
      point: {
        size: 3,
        shape: 'circle'
      },
      tooltip: {
        formatter: (datum: any) => ({
          name: '情感分数',
          value: `${datum.sentiment.toFixed(3)} (置信度: ${(datum.confidence * 100).toFixed(1)}%)`
        })
      }
    };

    return <Line {...config} height={300} />;
  };

  const renderSignalGauge = (signal: MultimodalSignal) => {
    const config = {
      percent: Math.abs(signal.signal_value),
      range: {
        color: signal.signal_value > 0 ? '#52c41a' : '#ff4d4f'
      },
      indicator: {
        pointer: {
          style: {
            stroke: '#D0D0D0'
          }
        },
        pin: {
          style: {
            stroke: '#D0D0D0'
          }
        }
      },
      statistic: {
        content: {
          style: {
            fontSize: '16px',
            lineHeight: '16px'
          },
          formatter: () => `${(signal.signal_value * 100).toFixed(1)}%`
        }
      }
    };

    return <Gauge {...config} height={120} />;
  };

  const newsColumns = [
    {
      title: '数据源',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: DataSource) => (
        <Space>
          <GlobalOutlined />
          <div>
            <div>{text}</div>
            <div className="text-xs text-gray-500">{record.description}</div>
          </div>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'source_type',
      key: 'source_type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      )
    },
    {
      title: '可靠性',
      dataIndex: 'reliability_score',
      key: 'reliability_score',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          format={percent => `${percent?.toFixed(0)}%`}
        />
      )
    },
    {
      title: '文章数',
      dataIndex: 'total_articles',
      key: 'total_articles',
      render: (count: number) => count?.toLocaleString() || 0
    },
    {
      title: '最后更新',
      dataIndex: 'last_updated',
      key: 'last_updated',
      render: (date: string) => date ? dayjs(date).format('MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DataSource) => (
        <Button
          type="primary"
          size="small"
          icon={<SyncOutlined />}
          onClick={() => {
            form.setFieldsValue({ source_id: record.id });
            setCollectModalVisible(true);
          }}
        >
          采集
        </Button>
      )
    }
  ];

  const socialColumns = [
    {
      title: '平台',
      dataIndex: 'platform_name',
      key: 'platform_name',
      render: (text: string, record: DataSource) => (
        <Space>
          {record.platform === 'weibo' ? <WeiboOutlined /> : <TwitterOutlined />}
          <div>
            <div>{text}</div>
            <div className="text-xs text-gray-500">{record.description}</div>
          </div>
        </Space>
      )
    },
    {
      title: '数据质量',
      dataIndex: 'data_quality_score',
      key: 'data_quality_score',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          format={percent => `${percent?.toFixed(0)}%`}
        />
      )
    },
    {
      title: '帖子数',
      dataIndex: 'total_posts',
      key: 'total_posts',
      render: (count: number) => count?.toLocaleString() || 0
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync',
      key: 'last_sync',
      render: (date: string) => date ? dayjs(date).format('MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DataSource) => (
        <Button
          type="primary"
          size="small"
          icon={<SyncOutlined />}
          onClick={() => {
            form.setFieldsValue({ source_id: record.id });
            setCollectModalVisible(true);
          }}
        >
          采集
        </Button>
      )
    }
  ];

  const altColumns = [
    {
      title: '数据源',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: DataSource) => (
        <Space>
          <SatelliteOutlined />
          <div>
            <div>{text}</div>
            <div className="text-xs text-gray-500">{record.description}</div>
          </div>
        </Space>
      )
    },
    {
      title: '类别',
      dataIndex: 'data_category',
      key: 'data_category',
      render: (category: string) => (
        <Tag color="purple">{category}</Tag>
      )
    },
    {
      title: '数据质量',
      dataIndex: 'data_quality_score',
      key: 'data_quality_score',
      render: (score: number) => (
        <Progress 
          percent={score * 100} 
          size="small" 
          format={percent => `${percent?.toFixed(0)}%`}
        />
      )
    },
    {
      title: '请求数',
      dataIndex: 'total_requests',
      key: 'total_requests',
      render: (count: number) => count?.toLocaleString() || 0
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: DataSource) => (
        <Button
          type="primary"
          size="small"
          icon={<SyncOutlined />}
          onClick={() => {
            form.setFieldsValue({ source_id: record.id });
            setCollectModalVisible(true);
          }}
        >
          采集
        </Button>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">多模态数据中心</h1>
          <p className="text-gray-600">整合新闻、社交媒体、另类数据等多种信息源</p>
        </div>
        <Space>
          <Select
            value={selectedSymbol}
            onChange={setSelectedSymbol}
            style={{ width: 150 }}
            placeholder="选择股票"
          >
            <Option value="000001.XSHE">平安银行</Option>
            <Option value="000002.XSHE">万科A</Option>
            <Option value="600519.XSHG">贵州茅台</Option>
            <Option value="600036.XSHG">招商银行</Option>
          </Select>
          <Button
            type="primary"
            icon={<BulbOutlined />}
            onClick={() => setSignalModalVisible(true)}
          >
            生成信号
          </Button>
        </Space>
      </div>

      {/* 情感分析概览 */}
      {sentimentData && (
        <Card title="情感分析概览" extra={<Badge count={sentimentData.sentiment_summary.total_articles} />}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="平均情感"
                value={sentimentData.sentiment_summary.average_sentiment}
                precision={3}
                valueStyle={{ color: getSentimentColor(sentimentData.sentiment_summary.average_sentiment) }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="积极"
                value={sentimentData.sentiment_summary.positive_count}
                prefix={<TrendingUpOutlined style={{ color: '#52c41a' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="消极"
                value={sentimentData.sentiment_summary.negative_count}
                prefix={<TrendingDownOutlined style={{ color: '#ff4d4f' }} />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="中性"
                value={sentimentData.sentiment_summary.neutral_count}
                prefix={<MinusOutlined style={{ color: '#faad14' }} />}
              />
            </Col>
          </Row>
          
          <div className="mt-4">
            <h4 className="mb-2">情感趋势</h4>
            {renderSentimentChart()}
          </div>
        </Card>
      )}

      {/* 多模态信号 */}
      {multimodalSignals.length > 0 && (
        <Card title="多模态信号" extra={<Badge count={multimodalSignals.length} />}>
          <Row gutter={16}>
            {multimodalSignals.slice(0, 4).map((signal, index) => (
              <Col span={6} key={signal.id}>
                <Card size="small" title={signal.signal_type}>
                  {renderSignalGauge(signal)}
                  <div className="text-center mt-2">
                    <div className="text-xs text-gray-500">
                      置信度: {(signal.confidence * 100).toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500">
                      强度: {(signal.signal_strength * 100).toFixed(1)}%
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* 数据源管理 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Badge count={newsSources.length} size="small">
                <Space>
                  <GlobalOutlined />
                  <span>新闻数据</span>
                </Space>
              </Badge>
            }
            key="news"
          >
            <Table
              columns={newsColumns}
              dataSource={newsSources}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个数据源`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={socialSources.length} size="small">
                <Space>
                  <TwitterOutlined />
                  <span>社交媒体</span>
                </Space>
              </Badge>
            }
            key="social"
          >
            <Table
              columns={socialColumns}
              dataSource={socialSources}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个数据源`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={altSources.length} size="small">
                <Space>
                  <SatelliteOutlined />
                  <span>另类数据</span>
                </Space>
              </Badge>
            }
            key="alternative"
          >
            <Table
              columns={altColumns}
              dataSource={altSources}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个数据源`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 数据采集弹窗 */}
      <Modal
        title={`采集${activeTab === 'news' ? '新闻' : activeTab === 'social' ? '社交媒体' : '另类'}数据`}
        open={collectModalVisible}
        onCancel={() => setCollectModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCollectData}
        >
          <Form.Item name="source_id" label="数据源" rules={[{ required: true }]}>
            <Select placeholder="选择数据源">
              {(activeTab === 'news' ? newsSources : 
                activeTab === 'social' ? socialSources : altSources).map(source => (
                <Option key={source.id} value={source.id}>
                  {source.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {activeTab === 'social' && (
            <Form.Item name="keywords" label="关键词" rules={[{ required: true }]}>
              <Input placeholder="请输入关键词，多个关键词用逗号分隔" />
            </Form.Item>
          )}

          {activeTab === 'alternative' && (
            <>
              <Form.Item name="data_type" label="数据类型" rules={[{ required: true }]}>
                <Select placeholder="选择数据类型">
                  <Option value="economic_activity">经济活动</Option>
                  <Option value="infrastructure">基础设施</Option>
                  <Option value="daily_weather">天气数据</Option>
                  <Option value="macro_indicators">宏观指标</Option>
                </Select>
              </Form.Item>
              <Form.Item name="region" label="区域">
                <Input placeholder="请输入区域名称" />
              </Form.Item>
              <Form.Item name="days" label="天数">
                <InputNumber min={1} max={90} placeholder="数据天数" />
              </Form.Item>
            </>
          )}

          <Form.Item name="max_items" label="最大数量">
            <InputNumber min={10} max={1000} placeholder="最大采集数量" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 信号生成弹窗 */}
      <Modal
        title="生成多模态信号"
        open={signalModalVisible}
        onCancel={() => setSignalModalVisible(false)}
        onOk={() => form.submit()}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGenerateSignal}
          initialValues={{
            symbol: selectedSymbol,
            time_window: 24,
            signal_type: 'composite'
          }}
        >
          <Form.Item name="symbol" label="股票代码" rules={[{ required: true }]}>
            <Select>
              <Option value="000001.XSHE">平安银行</Option>
              <Option value="000002.XSHE">万科A</Option>
              <Option value="600519.XSHG">贵州茅台</Option>
              <Option value="600036.XSHG">招商银行</Option>
            </Select>
          </Form.Item>

          <Form.Item name="time_window" label="时间窗口（小时）">
            <InputNumber min={1} max={168} />
          </Form.Item>

          <Form.Item name="signal_type" label="信号类型">
            <Select>
              <Option value="composite">综合信号</Option>
              <Option value="sentiment">情感信号</Option>
              <Option value="trend">趋势信号</Option>
              <Option value="event">事件信号</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
