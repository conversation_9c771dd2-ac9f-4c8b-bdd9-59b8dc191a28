'use client';

/**
 * 智能选股组件
 * 
 * 基于多维度分析进行智能选股推荐
 */

import React, { useState } from 'react';
import {
  Card,
  Form,
  Select,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  Typography,
  Row,
  Col,
  Slider,
  Tooltip,
  message,
  Alert,
  Spin
} from 'antd';
import {
  BulbOutlined,
  StarOutlined,
  TrophyOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { apiClient } from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface StockScore {
  symbol: string;
  score: number;
  details: {
    total_score: number;
    individual_scores: {
      valuation_score: number;
      growth_score: number;
      profitability_score: number;
      financial_health_score: number;
      technical_score: number;
    };
    weights: {
      valuation_score: number;
      growth_score: number;
      profitability_score: number;
      financial_health_score: number;
      technical_score: number;
    };
  };
}

interface SelectionResult {
  success: boolean;
  top_stocks: StockScore[];
  total_analyzed: number;
  selection_date: string;
  criteria: any;
}

interface IntelligentSelectionProps {
  onStockSelect?: (symbol: string) => void;
}

export const IntelligentSelection: React.FC<IntelligentSelectionProps> = ({
  onStockSelect
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectionResult, setSelectionResult] = useState<SelectionResult | null>(null);
  
  // 权重配置
  const [weights, setWeights] = useState({
    valuation_score: 20,
    growth_score: 25,
    profitability_score: 20,
    financial_health_score: 15,
    technical_score: 20
  });

  const handleSelection = async (values: any) => {
    setLoading(true);
    
    try {
      const criteria = {
        weights: {
          valuation_score: weights.valuation_score / 100,
          growth_score: weights.growth_score / 100,
          profitability_score: weights.profitability_score / 100,
          financial_health_score: weights.financial_health_score / 100,
          technical_score: weights.technical_score / 100
        }
      };

      const response = await apiClient.post('/api/v1/ml/intelligent-selection', null, {
        params: {
          market: values.market,
          top_n: values.top_n,
          criteria: JSON.stringify(criteria)
        }
      });

      if (response.data.success) {
        setSelectionResult(response.data.data);
        message.success('智能选股完成！');
      } else {
        message.error(response.data.message || '选股失败');
      }
    } catch (error: any) {
      console.error('Selection error:', error);
      message.error(error.response?.data?.detail || '选股失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    if (score >= 40) return '#fa8c16';
    return '#ff4d4f';
  };

  const getScoreLevel = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    return '较差';
  };

  const columns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_: any, __: any, index: number) => (
        <div className="flex items-center">
          {index < 3 && <TrophyOutlined style={{ color: '#faad14', marginRight: 4 }} />}
          <Text strong>{index + 1}</Text>
        </div>
      )
    },
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol',
      width: 120,
      render: (symbol: string) => (
        <Button
          type="link"
          onClick={() => onStockSelect?.(symbol)}
          style={{ padding: 0 }}
        >
          {symbol}
        </Button>
      )
    },
    {
      title: '综合评分',
      dataIndex: 'score',
      key: 'score',
      width: 120,
      render: (score: number) => (
        <Space>
          <Progress
            percent={score}
            size="small"
            strokeColor={getScoreColor(score)}
            showInfo={false}
            style={{ width: 60 }}
          />
          <Text style={{ color: getScoreColor(score) }}>
            {score.toFixed(1)}
          </Text>
        </Space>
      ),
      sorter: (a: StockScore, b: StockScore) => a.score - b.score
    },
    {
      title: '评级',
      dataIndex: 'score',
      key: 'level',
      width: 80,
      render: (score: number) => (
        <Tag color={getScoreColor(score)}>
          {getScoreLevel(score)}
        </Tag>
      )
    },
    {
      title: '估值',
      key: 'valuation',
      width: 80,
      render: (record: StockScore) => (
        <Text>{record.details.individual_scores.valuation_score.toFixed(1)}</Text>
      )
    },
    {
      title: '成长性',
      key: 'growth',
      width: 80,
      render: (record: StockScore) => (
        <Text>{record.details.individual_scores.growth_score.toFixed(1)}</Text>
      )
    },
    {
      title: '盈利能力',
      key: 'profitability',
      width: 90,
      render: (record: StockScore) => (
        <Text>{record.details.individual_scores.profitability_score.toFixed(1)}</Text>
      )
    },
    {
      title: '财务健康',
      key: 'financial_health',
      width: 90,
      render: (record: StockScore) => (
        <Text>{record.details.individual_scores.financial_health_score.toFixed(1)}</Text>
      )
    },
    {
      title: '技术面',
      key: 'technical',
      width: 80,
      render: (record: StockScore) => (
        <Text>{record.details.individual_scores.technical_score.toFixed(1)}</Text>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (record: StockScore) => (
        <Space>
          <Tooltip title="添加到自选股">
            <Button
              type="text"
              size="small"
              icon={<StarOutlined />}
              onClick={() => message.info('功能开发中')}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<InfoCircleOutlined />}
              onClick={() => onStockSelect?.(record.symbol)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 配置面板 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          title={
            <Space>
              <BulbOutlined />
              <span>智能选股</span>
            </Space>
          }
        >
          <Row gutter={[24, 16]}>
            {/* 基础配置 */}
            <Col xs={24} lg={12}>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSelection}
                initialValues={{
                  market: 'all',
                  top_n: 20
                }}
              >
                <Form.Item
                  name="market"
                  label="市场范围"
                  rules={[{ required: true, message: '请选择市场范围' }]}
                >
                  <Select>
                    <Option value="all">全市场</Option>
                    <Option value="sh">上海市场</Option>
                    <Option value="sz">深圳市场</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="top_n"
                  label="返回数量"
                  rules={[{ required: true, message: '请选择返回数量' }]}
                >
                  <Select>
                    <Option value={10}>前10只</Option>
                    <Option value={20}>前20只</Option>
                    <Option value={30}>前30只</Option>
                    <Option value={50}>前50只</Option>
                  </Select>
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<BulbOutlined />}
                    block
                  >
                    开始选股
                  </Button>
                </Form.Item>
              </Form>
            </Col>

            {/* 权重配置 */}
            <Col xs={24} lg={12}>
              <div className="space-y-4">
                <Title level={5}>评分权重配置</Title>
                
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-2">
                      <Text>估值评分</Text>
                      <Text>{weights.valuation_score}%</Text>
                    </div>
                    <Slider
                      value={weights.valuation_score}
                      onChange={(value) => setWeights(prev => ({ ...prev, valuation_score: value }))}
                      max={50}
                      min={0}
                    />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <Text>成长性评分</Text>
                      <Text>{weights.growth_score}%</Text>
                    </div>
                    <Slider
                      value={weights.growth_score}
                      onChange={(value) => setWeights(prev => ({ ...prev, growth_score: value }))}
                      max={50}
                      min={0}
                    />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <Text>盈利能力评分</Text>
                      <Text>{weights.profitability_score}%</Text>
                    </div>
                    <Slider
                      value={weights.profitability_score}
                      onChange={(value) => setWeights(prev => ({ ...prev, profitability_score: value }))}
                      max={50}
                      min={0}
                    />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <Text>财务健康评分</Text>
                      <Text>{weights.financial_health_score}%</Text>
                    </div>
                    <Slider
                      value={weights.financial_health_score}
                      onChange={(value) => setWeights(prev => ({ ...prev, financial_health_score: value }))}
                      max={50}
                      min={0}
                    />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <Text>技术面评分</Text>
                      <Text>{weights.technical_score}%</Text>
                    </div>
                    <Slider
                      value={weights.technical_score}
                      onChange={(value) => setWeights(prev => ({ ...prev, technical_score: value }))}
                      max={50}
                      min={0}
                    />
                  </div>
                </div>

                <Alert
                  message="权重说明"
                  description="调整各项指标的权重来定制选股策略。权重总和不必为100%，系统会自动标准化。"
                  type="info"
                  showIcon
                  size="small"
                />
              </div>
            </Col>
          </Row>
        </Card>
      </motion.div>

      {/* 选股结果 */}
      {selectionResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card
            title={
              <Space>
                <TrophyOutlined />
                <span>选股结果</span>
                <Tag color="blue">{selectionResult.top_stocks.length}只股票</Tag>
              </Space>
            }
            extra={
              <Space>
                <Button
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => message.info('导出功能开发中')}
                >
                  导出
                </Button>
                <Button
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={() => form.submit()}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Spin spinning={loading}>
              <Table
                columns={columns}
                dataSource={selectionResult.top_stocks}
                rowKey="symbol"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 只股票`
                }}
                scroll={{ x: 800 }}
                size="small"
              />
            </Spin>

            <Alert
              message="选股说明"
              description={`基于多维度量化分析，从${selectionResult.total_analyzed}只股票中筛选出优质标的。评分综合考虑估值、成长性、盈利能力、财务健康和技术面等因素。`}
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </motion.div>
      )}
    </div>
  );
};
