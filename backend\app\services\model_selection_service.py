"""
自动模型选择服务

提供多种机器学习算法的自动选择和评估功能
"""

import numpy as np
import pandas as pd
import pickle
import joblib
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sklearn.ensemble import (
    RandomForestClassifier, RandomForestRegressor,
    GradientBoostingClassifier, GradientBoostingRegressor,
    AdaBoostClassifier, AdaBoostRegressor,
    ExtraTreesClassifier, ExtraTreesRegressor
)
from sklearn.linear_model import (
    LogisticRegression, Ridge, Lasso, ElasticNet,
    SGDClassifier, SGDRegressor
)
from sklearn.svm import SVC, SVR
from sklearn.neighbors import KNeighborsClassifier, KNeighborsRegressor
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor
from sklearn.naive_bayes import GaussianNB, MultinomialNB
from sklearn.neural_network import MLPClassifier, MLPRegressor
from sklearn.model_selection import cross_val_score, train_test_split
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score,
    classification_report, confusion_matrix
)
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# 尝试导入高级算法
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

from app.core.logging import logger
from app.models.automl import AutoMLModel
from app.services.hyperparameter_tuning_service import hyperparameter_tuning_service


class ModelRegistry:
    """模型注册表"""
    
    def __init__(self):
        self.classification_models = self._get_classification_models()
        self.regression_models = self._get_regression_models()
    
    def _get_classification_models(self) -> Dict[str, Any]:
        """获取分类模型"""
        models = {
            'random_forest': {
                'class': RandomForestClassifier,
                'default_params': {'n_estimators': 100, 'random_state': 42},
                'complexity': 'medium',
                'training_time': 'medium',
                'interpretability': 'high'
            },
            'logistic_regression': {
                'class': LogisticRegression,
                'default_params': {'random_state': 42, 'max_iter': 1000},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'svm': {
                'class': SVC,
                'default_params': {'random_state': 42, 'probability': True},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'low'
            },
            'knn': {
                'class': KNeighborsClassifier,
                'default_params': {'n_neighbors': 5},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'medium'
            },
            'decision_tree': {
                'class': DecisionTreeClassifier,
                'default_params': {'random_state': 42},
                'complexity': 'medium',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'gradient_boosting': {
                'class': GradientBoostingClassifier,
                'default_params': {'random_state': 42},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'medium'
            },
            'naive_bayes': {
                'class': GaussianNB,
                'default_params': {},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'mlp': {
                'class': MLPClassifier,
                'default_params': {'random_state': 42, 'max_iter': 1000},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'low'
            }
        }
        
        # 添加高级算法（如果可用）
        if XGBOOST_AVAILABLE:
            models['xgboost'] = {
                'class': xgb.XGBClassifier,
                'default_params': {'random_state': 42, 'eval_metric': 'logloss'},
                'complexity': 'high',
                'training_time': 'medium',
                'interpretability': 'medium'
            }
        
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = {
                'class': lgb.LGBMClassifier,
                'default_params': {'random_state': 42, 'verbose': -1},
                'complexity': 'high',
                'training_time': 'fast',
                'interpretability': 'medium'
            }
        
        if CATBOOST_AVAILABLE:
            models['catboost'] = {
                'class': cb.CatBoostClassifier,
                'default_params': {'random_state': 42, 'verbose': False},
                'complexity': 'high',
                'training_time': 'medium',
                'interpretability': 'medium'
            }
        
        return models
    
    def _get_regression_models(self) -> Dict[str, Any]:
        """获取回归模型"""
        models = {
            'random_forest': {
                'class': RandomForestRegressor,
                'default_params': {'n_estimators': 100, 'random_state': 42},
                'complexity': 'medium',
                'training_time': 'medium',
                'interpretability': 'high'
            },
            'ridge': {
                'class': Ridge,
                'default_params': {'random_state': 42},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'lasso': {
                'class': Lasso,
                'default_params': {'random_state': 42},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'elastic_net': {
                'class': ElasticNet,
                'default_params': {'random_state': 42},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'svr': {
                'class': SVR,
                'default_params': {},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'low'
            },
            'knn': {
                'class': KNeighborsRegressor,
                'default_params': {'n_neighbors': 5},
                'complexity': 'low',
                'training_time': 'fast',
                'interpretability': 'medium'
            },
            'decision_tree': {
                'class': DecisionTreeRegressor,
                'default_params': {'random_state': 42},
                'complexity': 'medium',
                'training_time': 'fast',
                'interpretability': 'high'
            },
            'gradient_boosting': {
                'class': GradientBoostingRegressor,
                'default_params': {'random_state': 42},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'medium'
            },
            'mlp': {
                'class': MLPRegressor,
                'default_params': {'random_state': 42, 'max_iter': 1000},
                'complexity': 'high',
                'training_time': 'slow',
                'interpretability': 'low'
            }
        }
        
        # 添加高级算法（如果可用）
        if XGBOOST_AVAILABLE:
            models['xgboost'] = {
                'class': xgb.XGBRegressor,
                'default_params': {'random_state': 42},
                'complexity': 'high',
                'training_time': 'medium',
                'interpretability': 'medium'
            }
        
        if LIGHTGBM_AVAILABLE:
            models['lightgbm'] = {
                'class': lgb.LGBMRegressor,
                'default_params': {'random_state': 42, 'verbose': -1},
                'complexity': 'high',
                'training_time': 'fast',
                'interpretability': 'medium'
            }
        
        if CATBOOST_AVAILABLE:
            models['catboost'] = {
                'class': cb.CatBoostRegressor,
                'default_params': {'random_state': 42, 'verbose': False},
                'complexity': 'high',
                'training_time': 'medium',
                'interpretability': 'medium'
            }
        
        return models
    
    def get_models(self, problem_type: str) -> Dict[str, Any]:
        """获取指定问题类型的模型"""
        if problem_type == 'classification':
            return self.classification_models
        elif problem_type == 'regression':
            return self.regression_models
        else:
            return {}
    
    def get_model_info(self, algorithm: str, problem_type: str) -> Dict[str, Any]:
        """获取模型信息"""
        models = self.get_models(problem_type)
        return models.get(algorithm, {})


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        pass
    
    def evaluate_classification_model(
        self,
        model,
        X_train: np.ndarray,
        X_test: np.ndarray,
        y_train: np.ndarray,
        y_test: np.ndarray,
        cv: int = 5
    ) -> Dict[str, Any]:
        """评估分类模型"""
        try:
            # 训练模型
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = None
            if hasattr(model, 'predict_proba'):
                try:
                    y_pred_proba = model.predict_proba(X_test)
                except:
                    pass
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
            recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
            f1 = f1_score(y_test, y_pred, average='weighted', zero_division=0)
            
            # ROC AUC（仅适用于二分类或有概率预测的情况）
            roc_auc = None
            if y_pred_proba is not None and len(np.unique(y_train)) == 2:
                try:
                    roc_auc = roc_auc_score(y_test, y_pred_proba[:, 1])
                except:
                    pass
            
            # 交叉验证分数
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='accuracy')
            
            # 混淆矩阵
            conf_matrix = confusion_matrix(y_test, y_pred).tolist()
            
            return {
                'accuracy': float(accuracy),
                'precision': float(precision),
                'recall': float(recall),
                'f1_score': float(f1),
                'roc_auc': float(roc_auc) if roc_auc is not None else None,
                'cv_mean': float(cv_scores.mean()),
                'cv_std': float(cv_scores.std()),
                'confusion_matrix': conf_matrix,
                'training_time': training_time,
                'test_samples': len(y_test)
            }
            
        except Exception as e:
            logger.error(f"分类模型评估失败: {e}")
            return {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'roc_auc': None,
                'cv_mean': 0.0,
                'cv_std': 0.0,
                'confusion_matrix': [],
                'training_time': 0.0,
                'error': str(e)
            }
    
    def evaluate_regression_model(
        self,
        model,
        X_train: np.ndarray,
        X_test: np.ndarray,
        y_train: np.ndarray,
        y_test: np.ndarray,
        cv: int = 5
    ) -> Dict[str, Any]:
        """评估回归模型"""
        try:
            # 训练模型
            start_time = datetime.now()
            model.fit(X_train, y_train)
            training_time = (datetime.now() - start_time).total_seconds()
            
            # 预测
            y_pred = model.predict(X_test)
            
            # 计算指标
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            rmse = np.sqrt(mse)
            
            # MAPE (Mean Absolute Percentage Error)
            mape = np.mean(np.abs((y_test - y_pred) / np.where(y_test != 0, y_test, 1))) * 100
            
            # 交叉验证分数
            cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring='r2')
            
            return {
                'mse': float(mse),
                'mae': float(mae),
                'rmse': float(rmse),
                'r2_score': float(r2),
                'mape': float(mape),
                'cv_mean': float(cv_scores.mean()),
                'cv_std': float(cv_scores.std()),
                'training_time': training_time,
                'test_samples': len(y_test)
            }
            
        except Exception as e:
            logger.error(f"回归模型评估失败: {e}")
            return {
                'mse': float('inf'),
                'mae': float('inf'),
                'rmse': float('inf'),
                'r2_score': -float('inf'),
                'mape': float('inf'),
                'cv_mean': -float('inf'),
                'cv_std': 0.0,
                'training_time': 0.0,
                'error': str(e)
            }


class ModelSelectionService:
    """模型选择服务"""
    
    def __init__(self):
        self.model_registry = ModelRegistry()
        self.model_evaluator = ModelEvaluator()
    
    async def select_best_models(
        self,
        experiment_id: int,
        X: pd.DataFrame,
        y: pd.Series,
        problem_type: str,
        selection_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """选择最佳模型"""
        try:
            # 预处理数据
            X_processed, y_processed = self._preprocess_data(X, y, problem_type)
            
            # 分割数据
            test_size = selection_config.get('test_size', 0.2)
            random_state = selection_config.get('random_state', 42)
            X_train, X_test, y_train, y_test = train_test_split(
                X_processed, y_processed, test_size=test_size, random_state=random_state
            )
            
            # 获取候选模型
            candidate_models = self._get_candidate_models(problem_type, selection_config)
            
            # 评估所有模型
            model_results = []
            
            for algorithm, model_info in candidate_models.items():
                logger.info(f"评估模型: {algorithm}")
                
                try:
                    # 创建模型实例
                    model_class = model_info['class']
                    default_params = model_info['default_params']
                    model = model_class(**default_params)
                    
                    # 超参数调优（如果启用）
                    if selection_config.get('enable_hyperparameter_tuning', True):
                        tuning_result = await self._tune_model_hyperparameters(
                            experiment_id, model, X_train, y_train, algorithm, problem_type, db
                        )
                        
                        if tuning_result['success'] and tuning_result.get('best_estimator'):
                            model = tuning_result['best_estimator']
                            hyperparameters = tuning_result['best_params']
                        else:
                            hyperparameters = default_params
                    else:
                        hyperparameters = default_params
                    
                    # 评估模型
                    if problem_type == 'classification':
                        evaluation = self.model_evaluator.evaluate_classification_model(
                            model, X_train, X_test, y_train, y_test,
                            cv=selection_config.get('cv', 5)
                        )
                        primary_score = evaluation['accuracy']
                    else:  # regression
                        evaluation = self.model_evaluator.evaluate_regression_model(
                            model, X_train, X_test, y_train, y_test,
                            cv=selection_config.get('cv', 5)
                        )
                        primary_score = evaluation['r2_score']
                    
                    # 保存模型记录
                    model_record = await self._save_model_record(
                        experiment_id, algorithm, model, hyperparameters,
                        X.columns.tolist(), evaluation, primary_score, db
                    )
                    
                    model_results.append({
                        'algorithm': algorithm,
                        'model_id': model_record.id,
                        'score': primary_score,
                        'evaluation': evaluation,
                        'model_info': model_info,
                        'hyperparameters': hyperparameters
                    })
                    
                except Exception as e:
                    logger.error(f"模型 {algorithm} 评估失败: {e}")
                    continue
            
            # 排序并选择最佳模型
            if problem_type == 'classification':
                model_results.sort(key=lambda x: x['score'], reverse=True)
            else:  # regression
                model_results.sort(key=lambda x: x['score'], reverse=True)
            
            # 更新最佳模型标记
            if model_results:
                best_model_id = model_results[0]['model_id']
                await self._update_best_model(experiment_id, best_model_id, db)
            
            return {
                'success': True,
                'total_models': len(model_results),
                'best_model': model_results[0] if model_results else None,
                'all_results': model_results,
                'problem_type': problem_type
            }
            
        except Exception as e:
            logger.error(f"模型选择失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_models': 0,
                'best_model': None,
                'all_results': []
            }
    
    def _preprocess_data(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        problem_type: str
    ) -> Tuple[np.ndarray, np.ndarray]:
        """预处理数据"""
        try:
            X_processed = X.copy()
            
            # 处理分类变量
            for col in X_processed.columns:
                if X_processed[col].dtype == 'object':
                    le = LabelEncoder()
                    X_processed[col] = le.fit_transform(X_processed[col].astype(str))
            
            # 处理缺失值
            X_processed = X_processed.fillna(X_processed.mean())
            
            # 标准化特征（对某些算法有益）
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_processed)
            
            # 处理目标变量
            y_processed = y.copy()
            if problem_type == 'classification' and y_processed.dtype == 'object':
                le = LabelEncoder()
                y_processed = le.fit_transform(y_processed)
            
            return X_scaled, y_processed.values
            
        except Exception as e:
            logger.error(f"数据预处理失败: {e}")
            return X.values, y.values
    
    def _get_candidate_models(
        self,
        problem_type: str,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取候选模型"""
        all_models = self.model_registry.get_models(problem_type)
        
        # 根据配置过滤模型
        included_algorithms = config.get('included_algorithms', [])
        excluded_algorithms = config.get('excluded_algorithms', [])
        
        if included_algorithms:
            candidate_models = {
                alg: info for alg, info in all_models.items()
                if alg in included_algorithms
            }
        else:
            candidate_models = all_models.copy()
        
        if excluded_algorithms:
            for alg in excluded_algorithms:
                candidate_models.pop(alg, None)
        
        # 根据时间限制过滤模型
        max_training_time = config.get('max_training_time', 'medium')
        if max_training_time == 'fast':
            candidate_models = {
                alg: info for alg, info in candidate_models.items()
                if info['training_time'] in ['fast']
            }
        elif max_training_time == 'medium':
            candidate_models = {
                alg: info for alg, info in candidate_models.items()
                if info['training_time'] in ['fast', 'medium']
            }
        
        return candidate_models
    
    async def _tune_model_hyperparameters(
        self,
        experiment_id: int,
        model,
        X_train: np.ndarray,
        y_train: np.ndarray,
        algorithm: str,
        problem_type: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """调优模型超参数"""
        try:
            tuning_config = hyperparameter_tuning_service.get_default_tuning_config(
                algorithm, problem_type
            )
            
            # 限制调优时间以提高效率
            tuning_config['n_iter'] = min(tuning_config.get('n_iter', 50), 30)
            
            result = await hyperparameter_tuning_service.tune_hyperparameters(
                experiment_id, model, X_train, y_train, algorithm, tuning_config, db
            )
            
            return result
            
        except Exception as e:
            logger.error(f"超参数调优失败: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _save_model_record(
        self,
        experiment_id: int,
        algorithm: str,
        model,
        hyperparameters: Dict[str, Any],
        feature_list: List[str],
        evaluation: Dict[str, Any],
        primary_score: float,
        db: AsyncSession
    ) -> AutoMLModel:
        """保存模型记录"""
        try:
            # 序列化模型
            model_binary = pickle.dumps(model)
            model_size_mb = len(model_binary) / (1024 * 1024)
            
            # 提取特征重要性（如果可用）
            feature_importance = {}
            if hasattr(model, 'feature_importances_'):
                feature_importance = dict(zip(feature_list, model.feature_importances_.tolist()))
            elif hasattr(model, 'coef_'):
                coef = model.coef_.flatten() if len(model.coef_.shape) > 1 else model.coef_
                feature_importance = dict(zip(feature_list, np.abs(coef).tolist()))
            
            # 创建模型记录
            model_record = AutoMLModel(
                experiment_id=experiment_id,
                user_id=1,  # 这里应该从上下文获取用户ID
                model_name=f"{algorithm}_model",
                algorithm=algorithm,
                model_type="base_model",
                hyperparameters=hyperparameters,
                feature_list=feature_list,
                training_time_seconds=evaluation.get('training_time', 0),
                cross_validation_score=evaluation.get('cv_mean', 0),
                cross_validation_std=evaluation.get('cv_std', 0),
                performance_metrics=evaluation,
                feature_importance=feature_importance,
                model_size_mb=model_size_mb,
                model_binary=model_binary,
                status="completed",
                trained_at=datetime.utcnow()
            )
            
            # 设置主要分数
            if 'accuracy' in evaluation:
                model_record.validation_score = evaluation['accuracy']
            elif 'r2_score' in evaluation:
                model_record.validation_score = evaluation['r2_score']
            
            db.add(model_record)
            await db.flush()  # 获取ID
            
            return model_record
            
        except Exception as e:
            logger.error(f"保存模型记录失败: {e}")
            raise
    
    async def _update_best_model(
        self,
        experiment_id: int,
        best_model_id: int,
        db: AsyncSession
    ):
        """更新最佳模型标记"""
        try:
            # 清除所有模型的最佳标记
            from sqlalchemy import update
            await db.execute(
                update(AutoMLModel)
                .where(AutoMLModel.experiment_id == experiment_id)
                .values(is_best_model=False)
            )
            
            # 设置最佳模型标记
            await db.execute(
                update(AutoMLModel)
                .where(AutoMLModel.id == best_model_id)
                .values(is_best_model=True)
            )
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"更新最佳模型标记失败: {e}")


# 全局模型选择服务实例
model_selection_service = ModelSelectionService()
