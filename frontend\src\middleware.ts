/**
 * Next.js 中间件
 * 
 * 处理路由守卫、认证检查等
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 需要认证的路径
const protectedPaths = [
  '/dashboard',
  '/api/protected',
];

// 公开路径（不需要认证）
const publicPaths = [
  '/',
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/help',
  '/terms',
  '/privacy',
];

// 检查路径是否需要认证
function isProtectedPath(pathname: string): boolean {
  return protectedPaths.some(path => pathname.startsWith(path));
}

// 检查路径是否为公开路径
function isPublicPath(pathname: string): boolean {
  return publicPaths.some(path => pathname === path || pathname.startsWith(path));
}

// 检查用户是否已认证
function isAuthenticated(request: NextRequest): boolean {
  // 检查 cookie 中的认证信息
  const authCookie = request.cookies.get('auth-storage');
  
  if (!authCookie) {
    return false;
  }

  try {
    const authData = JSON.parse(authCookie.value);
    return !!(authData?.state?.isAuthenticated && authData?.state?.tokens?.accessToken);
  } catch {
    return false;
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 跳过静态资源和 API 路由
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.') ||
    pathname.startsWith('/favicon')
  ) {
    return NextResponse.next();
  }

  const authenticated = isAuthenticated(request);

  // 如果用户已认证且访问认证页面，重定向到仪表板
  if (authenticated && pathname.startsWith('/auth')) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // 暂时禁用自动重定向以避免 Hydration 问题
  // 让客户端组件处理认证检查
  // if (!authenticated && isProtectedPath(pathname)) {
  //   const loginUrl = new URL('/auth/login', request.url);
  //   loginUrl.searchParams.set('redirect', pathname);
  //   return NextResponse.redirect(loginUrl);
  // }

  // 根路径重定向逻辑
  if (pathname === '/') {
    if (authenticated) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      return NextResponse.redirect(new URL('/auth/login', request.url));
    }
  }

  // 添加安全头
  const response = NextResponse.next();
  
  // 安全头配置
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // CSP 头（内容安全策略）
  const cspHeader = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' http://localhost:8000 ws://localhost:3000",
    "frame-ancestors 'none'",
  ].join('; ');
  
  response.headers.set('Content-Security-Policy', cspHeader);

  return response;
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
