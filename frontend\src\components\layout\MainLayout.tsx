'use client';

/**
 * 主布局组件 - 优化版本
 * 
 * 提供统一的页面布局结构，包含导航、侧边栏、内容区域等
 * 支持响应式设计和主题切换
 */

import React, { useState, useEffect } from 'react';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Space,
  Drawer,
  Switch,
  Tooltip,
  Breadcrumb,
  FloatButton,
  Affix,
  BackTop,
  ConfigProvider,
  theme
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  SunOutlined,
  MoonOutlined,
  DashboardOutlined,
  LineChartOutlined,
  RobotOutlined,
  BranchesOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  ShareAltOutlined,
  ExperimentOutlined,
  BulbOutlined,
  FileTextOutlined,
  MonitorOutlined,
  QuestionCircleOutlined,
  CustomerServiceOutlined,
  HomeOutlined,
  StockOutlined,
  TableOutlined,
  TrophyOutlined,
  UnorderedListOutlined,
  EditOutlined,
  PlayCircleOutlined,
  HeartOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

const { Header, Sider, Content, Footer } = Layout;
const { useToken } = theme;

interface MainLayoutProps {
  children: React.ReactNode;
}

interface MenuItem {
  key: string;
  icon: React.ReactNode;
  label: string;
  path: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
    path: '/dashboard'
  },
  {
    key: 'market',
    icon: <LineChartOutlined />,
    label: '市场数据',
    path: '/dashboard/market',
    children: [
      { key: 'market-overview', icon: <BarChartOutlined />, label: '市场概览', path: '/dashboard/market/overview' },
      { key: 'market-stocks', icon: <StockOutlined />, label: '股票列表', path: '/dashboard/market/stocks' },
      { key: 'market-charts', icon: <LineChartOutlined />, label: '图表分析', path: '/dashboard/market/charts' },
      { key: 'market-news', icon: <FileTextOutlined />, label: '新闻分析', path: '/dashboard/market/news' }
    ]
  },
  {
    key: 'portfolio',
    icon: <DatabaseOutlined />,
    label: '投资组合',
    path: '/dashboard/portfolio',
    children: [
      { key: 'portfolio-overview', icon: <DashboardOutlined />, label: '组合概览', path: '/dashboard/portfolio/overview' },
      { key: 'portfolio-positions', icon: <TableOutlined />, label: '持仓管理', path: '/dashboard/portfolio/positions' },
      { key: 'portfolio-performance', icon: <TrophyOutlined />, label: '绩效分析', path: '/dashboard/portfolio/performance' },
      { key: 'portfolio-risk', icon: <MonitorOutlined />, label: '风险管理', path: '/dashboard/portfolio/risk' }
    ]
  },
  {
    key: 'strategy',
    icon: <ExperimentOutlined />,
    label: '策略开发',
    path: '/dashboard/strategy',
    children: [
      { key: 'strategy-list', icon: <UnorderedListOutlined />, label: '策略列表', path: '/dashboard/strategy/list' },
      { key: 'strategy-editor', icon: <EditOutlined />, label: '策略编辑', path: '/dashboard/strategy/editor' },
      { key: 'strategy-backtest', icon: <ExperimentOutlined />, label: '策略回测', path: '/dashboard/strategy/backtest' },
      { key: 'strategy-live', icon: <PlayCircleOutlined />, label: '实盘交易', path: '/dashboard/strategy/live' }
    ]
  },
  {
    key: 'ai',
    icon: <RobotOutlined />,
    label: 'AI智能分析',
    path: '/dashboard/ai',
    children: [
      { key: 'ai-prediction', icon: <LineChartOutlined />, label: '价格预测', path: '/dashboard/ai#prediction' },
      { key: 'ai-selection', icon: <BulbOutlined />, label: '智能选股', path: '/dashboard/ai#selection' },
      { key: 'ai-sentiment', icon: <HeartOutlined />, label: '情绪分析', path: '/dashboard/ai#sentiment' },
      { key: 'ai-models', icon: <SettingOutlined />, label: '模型管理', path: '/dashboard/ai#models' }
    ]
  },
  {
    key: 'reports',
    icon: <FileTextOutlined />,
    label: '报告管理',
    path: '/dashboard/reports'
  },
  {
    key: 'data-sources',
    icon: <DatabaseOutlined />,
    label: '数据源管理',
    path: '/dashboard/data-sources'
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '系统设置',
    path: '/dashboard/settings',
    children: [
      { key: 'settings-profile', icon: <UserOutlined />, label: '个人资料', path: '/dashboard/settings/profile' },
      { key: 'settings-jqdata', icon: <ApiOutlined />, label: 'JQData配置', path: '/dashboard/settings/jqdata' },
      { key: 'settings-preferences', icon: <SettingOutlined />, label: '系统偏好', path: '/dashboard/settings/preferences' }
    ]
  }
];

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { token } = useToken();
  
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [notifications, setNotifications] = useState(3);

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 主题切换
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    if (pathSegments.length === 0) return ['dashboard'];
    
    // 查找匹配的菜单项
    for (const item of menuItems) {
      if (pathname.startsWith(item.path)) {
        if (item.children) {
          for (const child of item.children) {
            if (pathname === child.path) {
              return [child.key];
            }
          }
        }
        return [item.key];
      }
    }
    
    return [pathSegments[0]];
  };

  // 获取打开的子菜单
  const getOpenKeys = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const openKeys: string[] = [];
    
    for (const item of menuItems) {
      if (item.children && pathname.startsWith(item.path)) {
        openKeys.push(item.key);
      }
    }
    
    return openKeys;
  };

  // 渲染菜单项
  const renderMenuItems = (items: MenuItem[]) => {
    return items.map(item => {
      if (item.children) {
        return {
          key: item.key,
          icon: item.icon,
          label: item.label,
          children: item.children.map(child => ({
            key: child.key,
            icon: child.icon,
            label: (
              <Link href={child.path} className="text-inherit no-underline">
                {child.label}
              </Link>
            )
          }))
        };
      }
      
      return {
        key: item.key,
        icon: item.icon,
        label: (
          <Link href={item.path} className="text-inherit no-underline">
            {item.label}
          </Link>
        )
      };
    });
  };

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true
    }
  ];

  // 面包屑导航
  const getBreadcrumbItems = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbItems = [
      {
        title: (
          <Link href="/dashboard" className="flex items-center">
            <HomeOutlined className="mr-1" />
            首页
          </Link>
        )
      }
    ];

    let currentPath = '';
    for (const segment of pathSegments) {
      currentPath += `/${segment}`;
      
      // 查找对应的菜单项
      let title = segment;
      for (const item of menuItems) {
        if (item.path === currentPath) {
          title = item.label;
          break;
        }
        if (item.children) {
          for (const child of item.children) {
            if (child.path === currentPath) {
              title = child.label;
              break;
            }
          }
        }
      }
      
      breadcrumbItems.push({
        title: currentPath === pathname ? title : (
          <Link href={currentPath}>{title}</Link>
        )
      });
    }

    return breadcrumbItems;
  };

  const siderContent = (
    <div className="h-full flex flex-col">
      {/* Logo区域 */}
      <div className="h-16 flex items-center justify-center border-b border-gray-200 dark:border-gray-700">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center"
        >
          {!collapsed && (
            <span className="text-lg font-bold text-blue-600 dark:text-blue-400 ml-2">
              量化交易平台
            </span>
          )}
          {collapsed && (
            <span className="text-xl font-bold text-blue-600 dark:text-blue-400">
              QT
            </span>
          )}
        </motion.div>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={getOpenKeys()}
          items={renderMenuItems(menuItems)}
          className="border-none"
          inlineCollapsed={collapsed}
        />
      </div>

      {/* 底部工具栏 */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center">
          <Tooltip title={isDarkMode ? '切换到亮色模式' : '切换到暗色模式'}>
            <Button
              type="text"
              icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              className="w-full"
            />
          </Tooltip>
        </div>
      </div>
    </div>
  );

  return (
    <ConfigProvider
      theme={{
        algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        }
      }}
    >
      <Layout className="min-h-screen">
        {/* 桌面端侧边栏 */}
        {!isMobile && (
          <Sider
            trigger={null}
            collapsible
            collapsed={collapsed}
            width={240}
            collapsedWidth={64}
            className="shadow-lg"
            style={{
              background: token.colorBgContainer,
              borderRight: `1px solid ${token.colorBorder}`
            }}
          >
            {siderContent}
          </Sider>
        )}

        {/* 移动端抽屉 */}
        <Drawer
          title="导航菜单"
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={240}
        >
          {siderContent}
        </Drawer>

        <Layout>
          {/* 顶部导航栏 */}
          <Affix offsetTop={0}>
            <Header
              className="px-4 shadow-sm"
              style={{
                background: token.colorBgContainer,
                borderBottom: `1px solid ${token.colorBorder}`
              }}
            >
              <div className="flex items-center justify-between h-full">
                <div className="flex items-center">
                  <Button
                    type="text"
                    icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                    onClick={() => {
                      if (isMobile) {
                        setMobileDrawerVisible(true);
                      } else {
                        setCollapsed(!collapsed);
                      }
                    }}
                    className="mr-4"
                  />
                  
                  {/* 面包屑导航 */}
                  <Breadcrumb items={getBreadcrumbItems()} />
                </div>

                <div className="flex items-center space-x-4">
                  {/* 通知铃铛 */}
                  <Tooltip title="通知">
                    <Badge count={notifications} size="small">
                      <Button
                        type="text"
                        icon={<BellOutlined />}
                        className="flex items-center justify-center"
                      />
                    </Badge>
                  </Tooltip>

                  {/* 帮助中心 */}
                  <Tooltip title="帮助中心">
                    <Button
                      type="text"
                      icon={<CustomerServiceOutlined />}
                      className="flex items-center justify-center"
                    />
                  </Tooltip>

                  {/* 用户头像和菜单 */}
                  <Dropdown
                    menu={{ items: userMenuItems }}
                    placement="bottomRight"
                    arrow
                  >
                    <div className="flex items-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 px-2 py-1 rounded">
                      <Avatar
                        size="small"
                        icon={<UserOutlined />}
                        className="mr-2"
                      />
                      <span className="hidden sm:inline">管理员</span>
                    </div>
                  </Dropdown>
                </div>
              </div>
            </Header>
          </Affix>

          {/* 主内容区域 */}
          <Content className="p-6 overflow-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="min-h-full"
            >
              {children}
            </motion.div>
          </Content>

          {/* 页脚 */}
          <Footer className="text-center py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              智能量化交易平台 ©2024 Created by AI Assistant
            </div>
          </Footer>
        </Layout>

        {/* 浮动按钮组 */}
        <FloatButton.Group
          trigger="hover"
          type="primary"
          style={{ right: 24 }}
          icon={<CustomerServiceOutlined />}
        >
          <FloatButton
            icon={<QuestionCircleOutlined />}
            tooltip="帮助文档"
          />
          <FloatButton
            icon={<CustomerServiceOutlined />}
            tooltip="在线客服"
          />
          <BackTop />
        </FloatButton.Group>
      </Layout>
    </ConfigProvider>
  );
};
