# =============================================================================
# JQData 量化平台开发环境启动脚本
# =============================================================================

#Requires -Version 5.1

<#
.SYNOPSIS
    启动JQData量化平台开发环境
    
.DESCRIPTION
    该脚本用于启动JQData量化平台的开发环境，包括：
    - 检查环境依赖
    - 启动Docker服务
    - 初始化数据库
    - 启动后端和前端服务
    
.PARAMETER Service
    指定要启动的服务：all, backend, frontend, database
    
.PARAMETER Clean
    是否清理现有数据和容器
    
.EXAMPLE
    .\start-dev.ps1
    启动所有服务
    
.EXAMPLE
    .\start-dev.ps1 -Service backend
    只启动后端服务
    
.EXAMPLE
    .\start-dev.ps1 -Clean
    清理环境后启动所有服务
#>

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("all", "backend", "frontend", "database")]
    [string]$Service = "all",
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean
)

# =============================================================================
# 全局变量
# =============================================================================

$ErrorActionPreference = "Stop"
$ProjectRoot = $PSScriptRoot
$LogFile = Join-Path $ProjectRoot "logs\startup.log"

# 创建日志目录
$LogDir = Split-Path $LogFile -Parent
if (-not (Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# =============================================================================
# 日志函数
# =============================================================================

function Write-Log {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [ValidateSet("Info", "Warning", "Error", "Success")]
        [string]$Level = "Info"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # 控制台输出
    switch ($Level) {
        "Info"    { Write-Host $logEntry -ForegroundColor White }
        "Warning" { Write-Host $logEntry -ForegroundColor Yellow }
        "Error"   { Write-Host $logEntry -ForegroundColor Red }
        "Success" { Write-Host $logEntry -ForegroundColor Green }
    }
    
    # 写入日志文件
    Add-Content -Path $LogFile -Value $logEntry -Encoding UTF8
}

# =============================================================================
# 环境检查函数
# =============================================================================

function Test-Prerequisites {
    Write-Log "检查环境依赖..." -Level Info
    
    $prerequisites = @(
        @{ Name = "Docker"; Command = "docker --version" },
        @{ Name = "Docker Compose"; Command = "docker-compose --version" },
        @{ Name = "Python"; Command = "python --version" },
        @{ Name = "Node.js"; Command = "node --version" },
        @{ Name = "npm"; Command = "npm --version" }
    )
    
    $allGood = $true
    
    foreach ($prereq in $prerequisites) {
        try {
            $result = Invoke-Expression $prereq.Command 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Log "✅ $($prereq.Name): $result" -Level Success
            } else {
                Write-Log "❌ $($prereq.Name): 未安装或不可用" -Level Error
                $allGood = $false
            }
        } catch {
            Write-Log "❌ $($prereq.Name): 未安装或不可用" -Level Error
            $allGood = $false
        }
    }
    
    if (-not $allGood) {
        Write-Log "环境检查失败，请安装缺失的依赖" -Level Error
        exit 1
    }
    
    Write-Log "环境检查通过" -Level Success
}

function Test-EnvFile {
    $envFile = Join-Path $ProjectRoot ".env"
    $envExample = Join-Path $ProjectRoot ".env.example"
    
    if (-not (Test-Path $envFile)) {
        if (Test-Path $envExample) {
            Write-Log "创建 .env 文件..." -Level Info
            Copy-Item $envExample $envFile
            Write-Log "请编辑 .env 文件配置必要的环境变量" -Level Warning
        } else {
            Write-Log ".env.example 文件不存在" -Level Error
            exit 1
        }
    }
}

# =============================================================================
# 服务管理函数
# =============================================================================

function Start-DatabaseServices {
    Write-Log "启动数据库服务..." -Level Info
    
    try {
        # 启动PostgreSQL和Redis
        docker-compose up -d postgres redis
        
        # 等待服务就绪
        Write-Log "等待数据库服务启动..." -Level Info
        Start-Sleep -Seconds 10
        
        # 检查服务状态
        $postgresStatus = docker-compose ps postgres --format json | ConvertFrom-Json
        $redisStatus = docker-compose ps redis --format json | ConvertFrom-Json
        
        if ($postgresStatus.State -eq "running" -and $redisStatus.State -eq "running") {
            Write-Log "数据库服务启动成功" -Level Success
        } else {
            Write-Log "数据库服务启动失败" -Level Error
            exit 1
        }
        
    } catch {
        Write-Log "启动数据库服务失败: $($_.Exception.Message)" -Level Error
        exit 1
    }
}

function Start-BackendService {
    Write-Log "启动后端服务..." -Level Info
    
    try {
        # 检查后端目录
        $backendDir = Join-Path $ProjectRoot "backend"
        if (-not (Test-Path $backendDir)) {
            Write-Log "后端目录不存在: $backendDir" -Level Error
            exit 1
        }
        
        # 启动后端服务
        docker-compose up -d backend celery_worker celery_beat flower
        
        Write-Log "后端服务启动成功" -Level Success
        Write-Log "API文档: http://localhost:8000/docs" -Level Info
        Write-Log "Flower监控: http://localhost:5555" -Level Info
        
    } catch {
        Write-Log "启动后端服务失败: $($_.Exception.Message)" -Level Error
        exit 1
    }
}

function Start-FrontendService {
    Write-Log "启动前端服务..." -Level Info
    
    try {
        # 检查前端目录
        $frontendDir = Join-Path $ProjectRoot "frontend"
        if (-not (Test-Path $frontendDir)) {
            Write-Log "前端目录不存在，跳过前端服务启动" -Level Warning
            return
        }
        
        # 启动前端服务
        docker-compose up -d frontend
        
        Write-Log "前端服务启动成功" -Level Success
        Write-Log "前端地址: http://localhost:3000" -Level Info
        
    } catch {
        Write-Log "启动前端服务失败: $($_.Exception.Message)" -Level Error
        exit 1
    }
}

function Stop-AllServices {
    Write-Log "停止所有服务..." -Level Info
    
    try {
        docker-compose down
        Write-Log "所有服务已停止" -Level Success
    } catch {
        Write-Log "停止服务失败: $($_.Exception.Message)" -Level Error
    }
}

function Clean-Environment {
    Write-Log "清理开发环境..." -Level Warning
    
    try {
        # 停止并删除容器
        docker-compose down -v --remove-orphans
        
        # 删除镜像（可选）
        $images = docker images "jqdata*" -q
        if ($images) {
            docker rmi $images -f
        }
        
        # 清理日志
        if (Test-Path $LogFile) {
            Remove-Item $LogFile -Force
        }
        
        Write-Log "环境清理完成" -Level Success
        
    } catch {
        Write-Log "环境清理失败: $($_.Exception.Message)" -Level Error
    }
}

# =============================================================================
# 主函数
# =============================================================================

function Main {
    Write-Log "🚀 JQData量化平台开发环境启动" -Level Info
    Write-Log "服务: $Service" -Level Info
    
    try {
        # 清理环境（如果指定）
        if ($Clean) {
            Clean-Environment
        }
        
        # 检查环境
        Test-Prerequisites
        Test-EnvFile
        
        # 根据参数启动服务
        switch ($Service) {
            "database" {
                Start-DatabaseServices
            }
            "backend" {
                Start-DatabaseServices
                Start-BackendService
            }
            "frontend" {
                Start-FrontendService
            }
            "all" {
                Start-DatabaseServices
                Start-BackendService
                Start-FrontendService
            }
        }
        
        Write-Log "🎉 启动完成！" -Level Success
        Write-Log "使用 'docker-compose logs -f' 查看日志" -Level Info
        Write-Log "使用 'docker-compose down' 停止服务" -Level Info
        
    } catch {
        Write-Log "启动失败: $($_.Exception.Message)" -Level Error
        exit 1
    }
}

# =============================================================================
# 脚本入口
# =============================================================================

# 捕获Ctrl+C
$null = Register-EngineEvent PowerShell.Exiting -Action {
    Write-Log "接收到退出信号，清理资源..." -Level Warning
}

# 执行主函数
Main
