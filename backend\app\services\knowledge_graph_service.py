"""
知识图谱核心服务

整合图构建、分析、GNN等功能，提供完整的知识图谱管理流程
"""

import numpy as np
import pandas as pd
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc

from app.core.logging import logger
from app.models.knowledge_graph import (
    KnowledgeGraph, FinancialEntity, EntityRelation, GraphAnalysis,
    GNNModel, GraphEmbedding, EntitySimilarity
)
from app.services.graph_builder_service import graph_builder_service
from app.services.graph_analysis_service import graph_analysis_service
from app.services.gnn_service import gnn_service


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        pass
    
    def load_stock_data(self, config: Dict[str, Any]) -> pd.DataFrame:
        """加载股票数据"""
        try:
            # 模拟股票数据
            stock_data = {
                'code': ['000001', '000002', '000858', '002415', '600036'],
                'name': ['平安银行', '万科A', '五粮液', '海康威视', '招商银行'],
                'exchange': ['SZSE', 'SZSE', 'SZSE', 'SZSE', 'SSE'],
                'industry': ['银行', '房地产', '食品饮料', '电子', '银行'],
                'sector': ['金融', '房地产', '消费', '科技', '金融'],
                'market_cap': [2500000000000, 3200000000000, 1800000000000, 4500000000000, 1900000000000],
                'pe_ratio': [5.2, 8.1, 25.3, 18.7, 6.8],
                'pb_ratio': [0.8, 1.2, 4.5, 3.2, 0.9],
                'total_shares': [19405918198, 11039152001, 3868776000, 9395000000, 25220645819],
                'float_shares': [19405918198, 11039152001, 3868776000, 9395000000, 25220645819]
            }
            
            return pd.DataFrame(stock_data)
            
        except Exception as e:
            logger.error(f"股票数据加载失败: {e}")
            return pd.DataFrame()
    
    def load_company_data(self, config: Dict[str, Any]) -> pd.DataFrame:
        """加载公司数据"""
        try:
            # 模拟公司数据
            company_data = {
                'company_id': ['comp_001', 'comp_002', 'comp_003'],
                'name': ['腾讯控股', '阿里巴巴', '字节跳动'],
                'company_type': ['上市公司', '上市公司', '私人公司'],
                'country': ['中国', '中国', '中国'],
                'founded_year': [1998, 1999, 2012],
                'employee_count': [85858, 245700, 110000],
                'revenue': [560118000000, 717289000000, 580000000000],
                'headquarters': ['深圳', '杭州', '北京']
            }
            
            return pd.DataFrame(company_data)
            
        except Exception as e:
            logger.error(f"公司数据加载失败: {e}")
            return pd.DataFrame()
    
    def load_relationship_data(self, config: Dict[str, Any]) -> pd.DataFrame:
        """加载关系数据"""
        try:
            # 模拟关系数据
            relationship_data = {
                'source_entity_id': ['stock_000001', 'stock_000002', 'stock_000858'],
                'target_entity_id': ['stock_600036', 'stock_002415', 'stock_000001'],
                'relation_type': ['correlation', 'competition', 'correlation'],
                'strength': [0.75, 0.6, 0.8],
                'confidence': [0.9, 0.7, 0.85]
            }
            
            return pd.DataFrame(relationship_data)
            
        except Exception as e:
            logger.error(f"关系数据加载失败: {e}")
            return pd.DataFrame()
    
    def load_correlation_data(self, config: Dict[str, Any]) -> pd.DataFrame:
        """加载相关性数据"""
        try:
            # 模拟相关性矩阵
            correlation_matrix = {
                '000001': {'000002': 0.65, '000858': 0.45, '002415': 0.32, '600036': 0.78},
                '000002': {'000001': 0.65, '000858': 0.23, '002415': 0.18, '600036': 0.55},
                '000858': {'000001': 0.45, '000002': 0.23, '002415': 0.15, '600036': 0.38},
                '002415': {'000001': 0.32, '000002': 0.18, '000858': 0.15, '600036': 0.28},
                '600036': {'000001': 0.78, '000002': 0.55, '000858': 0.38, '002415': 0.28}
            }
            
            return pd.DataFrame({'correlation_matrix': [correlation_matrix]})
            
        except Exception as e:
            logger.error(f"相关性数据加载失败: {e}")
            return pd.DataFrame()


class GraphQualityAssessor:
    """图质量评估器"""
    
    def __init__(self):
        pass
    
    def assess_graph_quality(self, graph_data: Dict[str, Any]) -> Dict[str, float]:
        """评估图质量"""
        try:
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            quality_scores = {}
            
            # 完整性评分
            completeness_score = self._calculate_completeness(nodes, edges)
            quality_scores['completeness_score'] = completeness_score
            
            # 一致性评分
            consistency_score = self._calculate_consistency(nodes, edges)
            quality_scores['consistency_score'] = consistency_score
            
            # 准确性评分
            accuracy_score = self._calculate_accuracy(nodes, edges)
            quality_scores['accuracy_score'] = accuracy_score
            
            # 新鲜度评分
            freshness_score = self._calculate_freshness(nodes, edges)
            quality_scores['freshness_score'] = freshness_score
            
            return quality_scores
            
        except Exception as e:
            logger.error(f"图质量评估失败: {e}")
            return {}
    
    def _calculate_completeness(self, nodes: List[Dict], edges: List[Dict]) -> float:
        """计算完整性"""
        try:
            if not nodes:
                return 0.0
            
            # 检查节点属性完整性
            complete_nodes = 0
            for node in nodes:
                properties = node.get('properties', {})
                features = node.get('features', [])
                
                # 基本属性检查
                has_basic_info = bool(node.get('name') and node.get('type'))
                has_properties = len(properties) > 0
                has_features = len(features) > 0
                
                if has_basic_info and has_properties and has_features:
                    complete_nodes += 1
            
            node_completeness = complete_nodes / len(nodes)
            
            # 检查边属性完整性
            complete_edges = 0
            for edge in edges:
                has_weight = 'weight' in edge
                has_confidence = 'confidence' in edge
                has_properties = len(edge.get('properties', {})) > 0
                
                if has_weight and has_confidence and has_properties:
                    complete_edges += 1
            
            edge_completeness = complete_edges / len(edges) if edges else 1.0
            
            return (node_completeness + edge_completeness) / 2
            
        except Exception as e:
            logger.error(f"完整性计算失败: {e}")
            return 0.0
    
    def _calculate_consistency(self, nodes: List[Dict], edges: List[Dict]) -> float:
        """计算一致性"""
        try:
            if not nodes or not edges:
                return 1.0
            
            # 检查节点类型一致性
            node_types = {}
            for node in nodes:
                node_type = node.get('type', 'unknown')
                if node_type not in node_types:
                    node_types[node_type] = []
                node_types[node_type].append(node)
            
            type_consistency = 0.0
            for node_type, type_nodes in node_types.items():
                if len(type_nodes) > 1:
                    # 检查同类型节点的属性一致性
                    first_node_props = set(type_nodes[0].get('properties', {}).keys())
                    consistent_count = 1
                    
                    for node in type_nodes[1:]:
                        node_props = set(node.get('properties', {}).keys())
                        overlap = len(first_node_props & node_props)
                        total = len(first_node_props | node_props)
                        if total > 0 and overlap / total > 0.5:
                            consistent_count += 1
                    
                    type_consistency += consistent_count / len(type_nodes)
                else:
                    type_consistency += 1.0
            
            return type_consistency / len(node_types) if node_types else 1.0
            
        except Exception as e:
            logger.error(f"一致性计算失败: {e}")
            return 0.0
    
    def _calculate_accuracy(self, nodes: List[Dict], edges: List[Dict]) -> float:
        """计算准确性"""
        try:
            # 基于置信度计算准确性
            total_confidence = 0.0
            count = 0
            
            # 节点准确性（基于数据源置信度）
            for node in nodes:
                confidence = node.get('properties', {}).get('source_confidence', 0.8)
                total_confidence += confidence
                count += 1
            
            # 边准确性
            for edge in edges:
                confidence = edge.get('confidence', 0.8)
                total_confidence += confidence
                count += 1
            
            return total_confidence / count if count > 0 else 0.8
            
        except Exception as e:
            logger.error(f"准确性计算失败: {e}")
            return 0.0
    
    def _calculate_freshness(self, nodes: List[Dict], edges: List[Dict]) -> float:
        """计算新鲜度"""
        try:
            current_time = datetime.utcnow()
            total_freshness = 0.0
            count = 0
            
            # 检查节点新鲜度
            for node in nodes:
                last_updated = node.get('properties', {}).get('last_updated')
                if last_updated:
                    try:
                        update_time = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                        days_old = (current_time - update_time).days
                        freshness = max(0, 1 - days_old / 365)  # 一年内的数据认为是新鲜的
                        total_freshness += freshness
                        count += 1
                    except:
                        total_freshness += 0.5  # 默认中等新鲜度
                        count += 1
                else:
                    total_freshness += 0.5  # 默认中等新鲜度
                    count += 1
            
            return total_freshness / count if count > 0 else 0.5
            
        except Exception as e:
            logger.error(f"新鲜度计算失败: {e}")
            return 0.0


class KnowledgeGraphService:
    """知识图谱核心服务"""
    
    def __init__(self):
        self.data_source_manager = DataSourceManager()
        self.quality_assessor = GraphQualityAssessor()
    
    async def create_knowledge_graph(
        self,
        user_id: int,
        graph_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """创建知识图谱"""
        try:
            # 准备数据源
            data_sources = {}
            
            # 加载股票数据
            if graph_config.get('include_stocks', True):
                stock_data = self.data_source_manager.load_stock_data({})
                if not stock_data.empty:
                    data_sources['stock'] = stock_data
            
            # 加载公司数据
            if graph_config.get('include_companies', False):
                company_data = self.data_source_manager.load_company_data({})
                if not company_data.empty:
                    data_sources['company'] = company_data
            
            # 加载关系数据
            if graph_config.get('include_relations', True):
                # 相关性关系
                correlation_data = self.data_source_manager.load_correlation_data({})
                if not correlation_data.empty:
                    data_sources['correlation'] = correlation_data
                
                # 其他关系
                relation_data = self.data_source_manager.load_relationship_data({})
                if not relation_data.empty:
                    data_sources['partnership'] = relation_data
            
            # 构建图谱
            build_result = await graph_builder_service.build_knowledge_graph(
                user_id, graph_config, data_sources, db
            )
            
            if not build_result['success']:
                return build_result
            
            # 评估图质量
            graph_id = build_result['graph_id']
            await self._assess_and_update_graph_quality(graph_id, db)
            
            return {
                'success': True,
                'graph_id': graph_id,
                'entity_count': build_result['entity_count'],
                'relation_count': build_result['relation_count'],
                'message': '知识图谱创建成功'
            }
            
        except Exception as e:
            logger.error(f"知识图谱创建失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def analyze_knowledge_graph(
        self,
        user_id: int,
        graph_id: int,
        analysis_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """分析知识图谱"""
        try:
            # 执行图分析
            analysis_result = await graph_analysis_service.analyze_graph(
                user_id, graph_id, analysis_config, db
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"知识图谱分析失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def train_gnn_on_graph(
        self,
        user_id: int,
        graph_id: int,
        model_config: Dict[str, Any],
        training_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """在图上训练GNN"""
        try:
            # 训练GNN模型
            training_result = await gnn_service.train_gnn_model(
                user_id, graph_id, model_config, training_config, db
            )
            
            return training_result
            
        except Exception as e:
            logger.error(f"GNN训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def find_similar_entities(
        self,
        user_id: int,
        entity_id: int,
        similarity_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """寻找相似实体"""
        try:
            # 获取目标实体
            result = await db.execute(
                select(FinancialEntity).where(
                    and_(
                        FinancialEntity.id == entity_id,
                        FinancialEntity.user_id == user_id
                    )
                )
            )
            target_entity = result.scalar_one_or_none()
            
            if not target_entity:
                return {'success': False, 'error': 'Entity not found'}
            
            # 获取同类型实体
            result = await db.execute(
                select(FinancialEntity).where(
                    and_(
                        FinancialEntity.entity_type == target_entity.entity_type,
                        FinancialEntity.user_id == user_id,
                        FinancialEntity.id != entity_id
                    )
                )
            )
            candidate_entities = result.scalars().all()
            
            # 计算相似度
            similarities = []
            target_features = np.array(target_entity.node_features or [0.0] * 5)
            
            for candidate in candidate_entities:
                candidate_features = np.array(candidate.node_features or [0.0] * 5)
                
                # 计算余弦相似度
                if np.linalg.norm(target_features) > 0 and np.linalg.norm(candidate_features) > 0:
                    similarity = np.dot(target_features, candidate_features) / (
                        np.linalg.norm(target_features) * np.linalg.norm(candidate_features)
                    )
                else:
                    similarity = 0.0
                
                similarities.append({
                    'entity_id': candidate.id,
                    'entity_name': candidate.entity_name,
                    'similarity_score': float(similarity),
                    'entity_type': candidate.entity_type
                })
            
            # 排序并返回前N个
            similarities.sort(key=lambda x: x['similarity_score'], reverse=True)
            top_similarities = similarities[:similarity_config.get('top_k', 10)]
            
            # 保存相似度结果
            for sim in top_similarities:
                entity_similarity = EntitySimilarity(
                    user_id=user_id,
                    entity1_id=entity_id,
                    entity2_id=sim['entity_id'],
                    similarity_type='feature_based',
                    similarity_score=sim['similarity_score'],
                    calculation_method='cosine_similarity'
                )
                db.add(entity_similarity)
            
            await db.commit()
            
            return {
                'success': True,
                'target_entity': {
                    'id': target_entity.id,
                    'name': target_entity.entity_name,
                    'type': target_entity.entity_type
                },
                'similar_entities': top_similarities
            }
            
        except Exception as e:
            logger.error(f"相似实体查找失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_graph_statistics(
        self,
        user_id: int,
        graph_id: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """获取图统计信息"""
        try:
            # 获取知识图谱
            result = await db.execute(
                select(KnowledgeGraph).where(
                    and_(
                        KnowledgeGraph.id == graph_id,
                        KnowledgeGraph.user_id == user_id
                    )
                )
            )
            knowledge_graph = result.scalar_one_or_none()
            
            if not knowledge_graph:
                return {'success': False, 'error': 'Knowledge graph not found'}
            
            # 获取实体统计
            entity_stats = await db.execute(
                select(
                    FinancialEntity.entity_type,
                    func.count(FinancialEntity.id).label('count')
                ).where(
                    FinancialEntity.user_id == user_id
                ).group_by(FinancialEntity.entity_type)
            )
            entity_type_counts = {row.entity_type: row.count for row in entity_stats}
            
            # 获取关系统计
            relation_stats = await db.execute(
                select(
                    EntityRelation.relation_type,
                    func.count(EntityRelation.id).label('count')
                ).where(
                    EntityRelation.user_id == user_id
                ).group_by(EntityRelation.relation_type)
            )
            relation_type_counts = {row.relation_type: row.count for row in relation_stats}
            
            # 获取分析统计
            analysis_count = await db.execute(
                select(func.count(GraphAnalysis.id)).where(
                    and_(
                        GraphAnalysis.knowledge_graph_id == graph_id,
                        GraphAnalysis.user_id == user_id
                    )
                )
            )
            total_analyses = analysis_count.scalar()
            
            # 获取GNN模型统计
            gnn_count = await db.execute(
                select(func.count(GNNModel.id)).where(
                    and_(
                        GNNModel.knowledge_graph_id == graph_id,
                        GNNModel.user_id == user_id
                    )
                )
            )
            total_gnn_models = gnn_count.scalar()
            
            statistics = {
                'graph_info': {
                    'id': knowledge_graph.id,
                    'name': knowledge_graph.graph_name,
                    'type': knowledge_graph.graph_type,
                    'status': knowledge_graph.status,
                    'created_at': knowledge_graph.created_at.isoformat(),
                    'last_build_time': knowledge_graph.last_build_time.isoformat() if knowledge_graph.last_build_time else None
                },
                'entity_statistics': {
                    'total_entities': knowledge_graph.entity_count,
                    'entity_type_distribution': entity_type_counts
                },
                'relation_statistics': {
                    'total_relations': knowledge_graph.relation_count,
                    'relation_type_distribution': relation_type_counts
                },
                'analysis_statistics': {
                    'total_analyses': total_analyses,
                    'total_gnn_models': total_gnn_models
                },
                'quality_metrics': {
                    'completeness_score': knowledge_graph.completeness_score,
                    'consistency_score': knowledge_graph.consistency_score,
                    'accuracy_score': knowledge_graph.accuracy_score,
                    'freshness_score': knowledge_graph.freshness_score
                }
            }
            
            return {
                'success': True,
                'statistics': statistics
            }
            
        except Exception as e:
            logger.error(f"图统计信息获取失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _assess_and_update_graph_quality(
        self,
        graph_id: int,
        db: AsyncSession
    ):
        """评估并更新图质量"""
        try:
            # 获取图数据
            result = await db.execute(
                select(KnowledgeGraph).where(KnowledgeGraph.id == graph_id)
            )
            knowledge_graph = result.scalar_one_or_none()
            
            if not knowledge_graph or not knowledge_graph.graph_data:
                return
            
            # 评估质量
            quality_scores = self.quality_assessor.assess_graph_quality(
                knowledge_graph.graph_data
            )
            
            # 更新质量分数
            knowledge_graph.completeness_score = quality_scores.get('completeness_score')
            knowledge_graph.consistency_score = quality_scores.get('consistency_score')
            knowledge_graph.accuracy_score = quality_scores.get('accuracy_score')
            knowledge_graph.freshness_score = quality_scores.get('freshness_score')
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"图质量评估失败: {e}")


# 全局知识图谱服务实例
knowledge_graph_service = KnowledgeGraphService()
