'use client';

/**
 * 优化版仪表板组件
 * 
 * 提供全面的量化交易数据概览，包括：
 * - 核心指标统计
 * - 实时市场数据
 * - 投资组合表现
 * - AI模型状态
 * - 风险监控
 * - 最新动态
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Button,
  Select,
  DatePicker,
  Alert,
  Spin,
  Empty,
  Tooltip,
  Badge,
  Avatar,
  List,
  Timeline,
  Tabs,
  Divider,
  Grid,
  Typography,
  Segmented,
  FloatButton,
  Drawer,
  Switch,
  Slider,
  InputNumber,
  Form,
  Modal,
  Carousel,
  Skeleton
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined,
  MoreOutlined,
  BellOutlined,
  UserOutlined,
  LineChartOutlined,
  BarChartOutlined,
  PieChartOutlined,
  StockOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  RobotOutlined,
  DatabaseOutlined,
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  CompressOutlined,
  FilterOutlined,
  ExportOutlined,
  ShareAltOutlined,
  HeartOutlined,
  FireOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  GlobalOutlined,
  TeamOutlined,
  FundOutlined,
  BankOutlined
} from '@ant-design/icons';
import { Line, Column, Pie, Area, Gauge, DualAxes, Rose, Scatter, Liquid } from '@ant-design/plots';
import { motion, AnimatePresence } from 'framer-motion';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { useBreakpoint } = Grid;
const { RangePicker } = DatePicker;

interface DashboardData {
  totalAssets: number;
  totalReturn: number;
  dailyReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  activeStrategies: number;
  runningModels: number;
}

interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
}

interface PortfolioHolding {
  symbol: string;
  name: string;
  quantity: number;
  price: number;
  value: number;
  weight: number;
  pnl: number;
  pnlPercent: number;
}

export const OptimizedDashboard: React.FC = () => {
  const screens = useBreakpoint();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [portfolioData, setPortfolioData] = useState<PortfolioHolding[]>([]);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setDashboardData({
        totalAssets: 1250000,
        totalReturn: 15.8,
        dailyReturn: 0.65,
        sharpeRatio: 1.85,
        maxDrawdown: -8.2,
        winRate: 68.5,
        activeStrategies: 12,
        runningModels: 8
      });

      setMarketData([
        { symbol: '000001', price: 12.45, change: 0.23, changePercent: 1.88, volume: 125000000 },
        { symbol: '000002', price: 28.67, change: -0.45, changePercent: -1.54, volume: 89000000 },
        { symbol: '000858', price: 156.78, change: 2.34, changePercent: 1.52, volume: 45000000 },
        { symbol: '002415', price: 45.23, change: -0.67, changePercent: -1.46, volume: 78000000 },
        { symbol: '600036', price: 38.90, change: 0.89, changePercent: 2.34, volume: 156000000 }
      ]);

      setPortfolioData([
        { symbol: '000001', name: '平安银行', quantity: 10000, price: 12.45, value: 124500, weight: 9.96, pnl: 2300, pnlPercent: 1.88 },
        { symbol: '000002', name: '万科A', quantity: 5000, price: 28.67, value: 143350, weight: 11.47, pnl: -2250, pnlPercent: -1.54 },
        { symbol: '000858', name: '五粮液', quantity: 1000, price: 156.78, value: 156780, weight: 12.54, pnl: 2340, pnlPercent: 1.52 },
        { symbol: '002415', name: '海康威视', quantity: 3000, price: 45.23, value: 135690, weight: 10.86, pnl: -2010, pnlPercent: -1.46 },
        { symbol: '600036', name: '招商银行', quantity: 4000, price: 38.90, value: 155600, weight: 12.45, pnl: 3560, pnlPercent: 2.34 }
      ]);

      setLoading(false);
    };

    loadData();
  }, [timeRange]);

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  // 核心指标卡片
  const renderStatsCards = () => {
    if (!dashboardData) return null;

    const stats = [
      {
        title: '总资产',
        value: dashboardData.totalAssets,
        precision: 0,
        prefix: '¥',
        suffix: '',
        icon: <DollarOutlined />,
        color: '#1890ff'
      },
      {
        title: '总收益率',
        value: dashboardData.totalReturn,
        precision: 2,
        prefix: '',
        suffix: '%',
        icon: <TrophyOutlined />,
        color: dashboardData.totalReturn >= 0 ? '#52c41a' : '#ff4d4f'
      },
      {
        title: '日收益率',
        value: dashboardData.dailyReturn,
        precision: 2,
        prefix: '',
        suffix: '%',
        icon: dashboardData.dailyReturn >= 0 ? <RiseOutlined /> : <FallOutlined />,
        color: dashboardData.dailyReturn >= 0 ? '#52c41a' : '#ff4d4f'
      },
      {
        title: '夏普比率',
        value: dashboardData.sharpeRatio,
        precision: 2,
        prefix: '',
        suffix: '',
        icon: <BarChartOutlined />,
        color: '#722ed1'
      },
      {
        title: '最大回撤',
        value: Math.abs(dashboardData.maxDrawdown),
        precision: 2,
        prefix: '-',
        suffix: '%',
        icon: <FallOutlined />,
        color: '#ff4d4f'
      },
      {
        title: '胜率',
        value: dashboardData.winRate,
        precision: 1,
        prefix: '',
        suffix: '%',
        icon: <TrophyOutlined />,
        color: '#52c41a'
      },
      {
        title: '活跃策略',
        value: dashboardData.activeStrategies,
        precision: 0,
        prefix: '',
        suffix: '个',
        icon: <ThunderboltOutlined />,
        color: '#fa8c16'
      },
      {
        title: '运行模型',
        value: dashboardData.runningModels,
        precision: 0,
        prefix: '',
        suffix: '个',
        icon: <RobotOutlined />,
        color: '#13c2c2'
      }
    ];

    return (
      <Row gutter={[16, 16]}>
        {stats.map((stat, index) => (
          <Col xs={12} sm={12} md={6} lg={6} xl={3} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card
                hoverable
                className="text-center h-full"
                bodyStyle={{ padding: '20px 16px' }}
              >
                <div className="flex flex-col items-center">
                  <div
                    className="w-12 h-12 rounded-full flex items-center justify-center mb-3 text-white text-lg"
                    style={{ backgroundColor: stat.color }}
                  >
                    {stat.icon}
                  </div>
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    precision={stat.precision}
                    prefix={stat.prefix}
                    suffix={stat.suffix}
                    valueStyle={{ 
                      color: stat.color,
                      fontSize: screens.xs ? '18px' : '20px',
                      fontWeight: 'bold'
                    }}
                  />
                </div>
              </Card>
            </motion.div>
          </Col>
        ))}
      </Row>
    );
  };

  // 市场数据表格
  const renderMarketTable = () => {
    const columns = [
      {
        title: '股票代码',
        dataIndex: 'symbol',
        key: 'symbol',
        render: (symbol: string) => (
          <Text strong className="text-blue-600">{symbol}</Text>
        )
      },
      {
        title: '当前价格',
        dataIndex: 'price',
        key: 'price',
        render: (price: number) => `¥${price.toFixed(2)}`
      },
      {
        title: '涨跌额',
        dataIndex: 'change',
        key: 'change',
        render: (change: number) => (
          <Text type={change >= 0 ? 'success' : 'danger'}>
            {change >= 0 ? '+' : ''}{change.toFixed(2)}
          </Text>
        )
      },
      {
        title: '涨跌幅',
        dataIndex: 'changePercent',
        key: 'changePercent',
        render: (changePercent: number) => (
          <Tag color={changePercent >= 0 ? 'green' : 'red'}>
            {changePercent >= 0 ? '+' : ''}{changePercent.toFixed(2)}%
          </Tag>
        )
      },
      {
        title: '成交量',
        dataIndex: 'volume',
        key: 'volume',
        render: (volume: number) => (volume / 1000000).toFixed(1) + 'M'
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={marketData}
        rowKey="symbol"
        size="small"
        pagination={false}
        scroll={{ x: 400 }}
      />
    );
  };

  // 投资组合饼图
  const renderPortfolioPie = () => {
    const data = portfolioData.map(item => ({
      type: item.name,
      value: item.weight
    }));

    const config = {
      data,
      angleField: 'value',
      colorField: 'type',
      radius: 0.8,
      innerRadius: 0.4,
      label: {
        type: 'outer',
        content: '{name} {percentage}',
      },
      interactions: [
        {
          type: 'element-selected',
        },
        {
          type: 'element-active',
        },
      ],
      statistic: {
        title: false,
        content: {
          style: {
            whiteSpace: 'pre-wrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
          content: '投资组合\n权重分布',
        },
      },
    };

    return <Pie {...config} height={300} />;
  };

  // 收益曲线图
  const renderReturnChart = () => {
    // 模拟收益数据
    const data = Array.from({ length: 30 }, (_, i) => ({
      date: dayjs().subtract(29 - i, 'day').format('MM-DD'),
      value: Math.random() * 2 - 1 + (i * 0.1)
    }));

    const config = {
      data,
      xField: 'date',
      yField: 'value',
      smooth: true,
      color: '#1890ff',
      point: {
        size: 3,
        shape: 'circle',
      },
      area: {
        style: {
          fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
          fillOpacity: 0.3,
        },
      },
      xAxis: {
        tickCount: 5,
      },
      yAxis: {
        label: {
          formatter: (v: string) => `${v}%`,
        },
      },
    };

    return <Area {...config} height={300} />;
  };

  if (loading) {
    return (
      <div className="p-6">
        <Skeleton active paragraph={{ rows: 8 }} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <Title level={2} className="mb-2">
            仪表板
          </Title>
          <Text type="secondary">
            实时监控您的量化交易表现和市场动态
          </Text>
        </div>
        
        <Space wrap>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Select.Option value="1d">今日</Select.Option>
            <Select.Option value="7d">近7天</Select.Option>
            <Select.Option value="30d">近30天</Select.Option>
            <Select.Option value="90d">近90天</Select.Option>
          </Select>
          
          <Button
            icon={<ReloadOutlined spin={refreshing} />}
            onClick={handleRefresh}
            loading={refreshing}
          >
            刷新
          </Button>
          
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            设置
          </Button>
        </Space>
      </div>

      {/* 核心指标卡片 */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {renderStatsCards()}
      </motion.div>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 收益曲线 */}
        <Col xs={24} lg={16}>
          <Card
            title="收益曲线"
            extra={
              <Space>
                <Button type="text" size="small" icon={<FullscreenOutlined />} />
                <Button type="text" size="small" icon={<ExportOutlined />} />
              </Space>
            }
          >
            {renderReturnChart()}
          </Card>
        </Col>

        {/* 投资组合分布 */}
        <Col xs={24} lg={8}>
          <Card title="投资组合分布">
            {renderPortfolioPie()}
          </Card>
        </Col>

        {/* 实时市场数据 */}
        <Col xs={24} lg={12}>
          <Card
            title="实时市场数据"
            extra={
              <Badge status="processing" text="实时更新" />
            }
          >
            {renderMarketTable()}
          </Card>
        </Col>

        {/* 风险监控 */}
        <Col xs={24} lg={12}>
          <Card title="风险监控">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="VaR (95%)"
                  value={2.34}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Beta系数"
                  value={0.85}
                  precision={2}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
            <Divider />
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <Text>集中度风险</Text>
                <Progress percent={35} size="small" status="normal" />
              </div>
              <div className="flex justify-between items-center">
                <Text>流动性风险</Text>
                <Progress percent={20} size="small" status="active" />
              </div>
              <div className="flex justify-between items-center">
                <Text>市场风险</Text>
                <Progress percent={60} size="small" status="exception" />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 设置抽屉 */}
      <Drawer
        title="仪表板设置"
        placement="right"
        onClose={() => setSettingsVisible(false)}
        open={settingsVisible}
        width={400}
      >
        <Form layout="vertical">
          <Form.Item label="自动刷新">
            <Switch defaultChecked />
          </Form.Item>
          <Form.Item label="刷新间隔（秒）">
            <Slider min={5} max={300} defaultValue={30} />
          </Form.Item>
          <Form.Item label="显示小数位数">
            <InputNumber min={0} max={4} defaultValue={2} />
          </Form.Item>
          <Form.Item label="主题色彩">
            <Select defaultValue="blue">
              <Select.Option value="blue">蓝色</Select.Option>
              <Select.Option value="green">绿色</Select.Option>
              <Select.Option value="purple">紫色</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 浮动操作按钮 */}
      <FloatButton.Group
        trigger="hover"
        type="primary"
        style={{ right: 24 }}
        icon={<MoreOutlined />}
      >
        <FloatButton
          icon={<ExportOutlined />}
          tooltip="导出数据"
        />
        <FloatButton
          icon={<ShareAltOutlined />}
          tooltip="分享报告"
        />
        <FloatButton
          icon={<BellOutlined />}
          tooltip="设置提醒"
        />
      </FloatButton.Group>
    </div>
  );
};
