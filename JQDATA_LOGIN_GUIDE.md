# 📱 JQData登录方式说明

## 🔍 支持的登录方式

根据JQData官方文档和实际测试，JQData平台支持以下两种登录方式：

### 1. 📱 手机号登录（推荐）
- **格式**: 11位中国大陆手机号
- **示例**: `13812345678`
- **验证规则**: `/^1[3-9]\d{9}$/`
- **说明**: JQData官方推荐的主要登录方式，适用于大部分用户

### 2. 📧 邮箱登录
- **格式**: 标准邮箱格式
- **示例**: `<EMAIL>`
- **验证规则**: `/^[^\s@]+@[^\s@]+\.[^\s@]+$/`
- **说明**: 适用于早期注册的用户，部分用户可能仍在使用

## 🛠️ 技术实现

### 前端配置
- **默认登录方式**: 手机号登录
- **用户界面**: 提供单选按钮切换登录方式
- **表单验证**: 根据选择的登录方式动态验证输入格式
- **用户提示**: 提供清晰的说明和帮助信息

### 后端认证
```python
# 统一的认证接口，支持手机号和邮箱
jq.auth(username, password)
```

- **认证接口**: 使用JQData官方的`jq.auth()`方法
- **参数说明**: 
  - `username`: 手机号或邮箱
  - `password`: 对应的密码
- **安全性**: 密码使用Fernet加密存储

## 📋 配置步骤

### 1. 访问配置页面
```
http://localhost:3000/dashboard/settings/jqdata
```

### 2. 选择登录方式
- **手机号登录**: 输入11位手机号 + 密码
- **邮箱登录**: 输入邮箱地址 + 密码

### 3. 测试连接
- 点击"测试连接"按钮验证账号信息
- 系统会显示连接状态和配额信息

### 4. 保存配置
- 测试成功后保存配置
- 系统会加密存储密码信息

## ⚠️ 注意事项

### 账号要求
1. **有效的JQData账号**: 需要在[JQData官网](https://www.joinquant.com/default/index/sdk)注册
2. **激活状态**: 账号必须处于激活状态
3. **配额充足**: 确保账号有足够的API调用配额

### 安全建议
1. **密码安全**: 使用强密码，定期更换
2. **权限控制**: 不要共享账号信息
3. **监控使用**: 定期检查API调用量

### 常见问题
1. **认证失败**: 检查用户名和密码是否正确
2. **配额不足**: 联系JQData客服充值或升级套餐
3. **网络问题**: 确保服务器能访问JQData API

## 🔧 故障排除

### 登录失败
```
错误: JQData认证失败
```
**解决方案**:
1. 确认用户名格式正确（手机号11位，邮箱包含@符号）
2. 确认密码正确
3. 检查账号是否被冻结或过期
4. 尝试在JQData官网登录验证

### 配额问题
```
错误: API调用配额不足
```
**解决方案**:
1. 登录JQData官网查看配额使用情况
2. 联系客服充值或升级套餐
3. 优化API调用频率

### 网络连接
```
错误: 连接JQData服务器失败
```
**解决方案**:
1. 检查网络连接
2. 确认防火墙设置
3. 尝试使用代理服务器

## 📚 相关文档

- [JQData官方文档](https://www.joinquant.com/help/api/doc?name=JQDatadoc)
- [JQData SDK安装](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9836)
- [JQData API参考](https://www.joinquant.com/help/api/doc?name=JQDatadoc&id=9837)

## 📞 技术支持

如果遇到问题，可以通过以下方式获取帮助：

1. **JQData官方客服**: 通过官网联系客服
2. **技术文档**: 查阅官方API文档
3. **社区论坛**: 在聚宽社区提问

---

**最后更新**: 2024年8月27日  
**版本**: v1.0  
**状态**: ✅ 已验证
