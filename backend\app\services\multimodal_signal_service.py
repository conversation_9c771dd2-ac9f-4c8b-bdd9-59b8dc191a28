"""
多模态信号生成服务

整合新闻情感、社交媒体、另类数据等多种数据源，生成综合投资信号
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import asyncio

from app.core.logging import logger
from app.models.multimodal import (
    MultimodalSignal, NewsSentiment, NewsArticle, 
    SocialSentiment, SocialMediaPost, AlternativeData
)


class SignalWeightCalculator:
    """信号权重计算器"""
    
    def __init__(self):
        self.base_weights = {
            'news_sentiment': 0.3,
            'social_sentiment': 0.25,
            'alternative_data': 0.25,
            'technical_indicators': 0.2
        }
    
    def calculate_dynamic_weights(
        self,
        data_quality_scores: Dict[str, float],
        data_freshness_scores: Dict[str, float],
        historical_accuracy: Dict[str, float]
    ) -> Dict[str, float]:
        """计算动态权重"""
        try:
            weights = {}
            
            for source, base_weight in self.base_weights.items():
                quality = data_quality_scores.get(source, 0.5)
                freshness = data_freshness_scores.get(source, 0.5)
                accuracy = historical_accuracy.get(source, 0.5)
                
                # 综合评分
                composite_score = (quality * 0.4 + freshness * 0.3 + accuracy * 0.3)
                
                # 调整权重
                adjusted_weight = base_weight * (0.5 + composite_score)
                weights[source] = adjusted_weight
            
            # 归一化权重
            total_weight = sum(weights.values())
            if total_weight > 0:
                weights = {k: v / total_weight for k, v in weights.items()}
            
            return weights
            
        except Exception as e:
            logger.error(f"动态权重计算失败: {e}")
            return self.base_weights


class NewsSentimentAggregator:
    """新闻情感聚合器"""
    
    def __init__(self):
        pass
    
    async def aggregate_news_sentiment(
        self,
        symbol: str,
        time_window: int,  # 小时
        db: AsyncSession
    ) -> Dict[str, Any]:
        """聚合新闻情感"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=time_window)
            
            # 查询相关新闻情感数据
            query = (
                select(NewsSentiment, NewsArticle)
                .join(NewsArticle, NewsSentiment.article_id == NewsArticle.id)
                .where(
                    and_(
                        NewsArticle.published_at >= start_time,
                        NewsArticle.related_symbols.contains([symbol])
                    )
                )
                .order_by(desc(NewsArticle.published_at))
            )
            
            result = await db.execute(query)
            sentiment_data = result.all()
            
            if not sentiment_data:
                return {
                    'sentiment_score': 0.0,
                    'confidence': 0.0,
                    'article_count': 0,
                    'data_quality': 0.0
                }
            
            sentiments = [s.NewsSentiment for s in sentiment_data]
            articles = [s.NewsArticle for s in sentiment_data]
            
            # 计算加权情感分数
            weighted_scores = []
            weights = []
            
            for sentiment, article in zip(sentiments, articles):
                # 权重基于文章质量和时效性
                time_decay = self._calculate_time_decay(article.published_at, time_window)
                credibility_weight = article.credibility_score or 0.5
                confidence_weight = sentiment.confidence
                
                weight = time_decay * credibility_weight * confidence_weight
                
                weighted_scores.append(sentiment.sentiment_score * weight)
                weights.append(weight)
            
            # 计算最终分数
            if sum(weights) > 0:
                final_sentiment = sum(weighted_scores) / sum(weights)
                avg_confidence = np.mean([s.confidence for s in sentiments])
                data_quality = np.mean([a.credibility_score or 0.5 for a in articles])
            else:
                final_sentiment = 0.0
                avg_confidence = 0.0
                data_quality = 0.0
            
            return {
                'sentiment_score': final_sentiment,
                'confidence': avg_confidence,
                'article_count': len(sentiment_data),
                'data_quality': data_quality,
                'time_decay_avg': np.mean([self._calculate_time_decay(a.published_at, time_window) for a in articles])
            }
            
        except Exception as e:
            logger.error(f"新闻情感聚合失败: {e}")
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'article_count': 0,
                'data_quality': 0.0
            }
    
    def _calculate_time_decay(self, published_at: datetime, time_window: int) -> float:
        """计算时间衰减权重"""
        try:
            hours_ago = (datetime.utcnow() - published_at).total_seconds() / 3600
            decay_rate = 0.1  # 衰减率
            
            # 指数衰减
            time_weight = np.exp(-decay_rate * hours_ago / time_window)
            return max(0.1, time_weight)  # 最小权重0.1
            
        except Exception as e:
            logger.error(f"时间衰减计算失败: {e}")
            return 0.5


class SocialSentimentAggregator:
    """社交媒体情感聚合器"""
    
    def __init__(self):
        pass
    
    async def aggregate_social_sentiment(
        self,
        symbol: str,
        time_window: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """聚合社交媒体情感"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=time_window)
            
            # 查询相关社交媒体情感数据
            query = (
                select(SocialSentiment, SocialMediaPost)
                .join(SocialMediaPost, SocialSentiment.post_id == SocialMediaPost.id)
                .where(
                    and_(
                        SocialMediaPost.posted_at >= start_time,
                        SocialMediaPost.related_symbols.contains([symbol])
                    )
                )
                .order_by(desc(SocialMediaPost.posted_at))
            )
            
            result = await db.execute(query)
            sentiment_data = result.all()
            
            if not sentiment_data:
                return {
                    'sentiment_score': 0.0,
                    'confidence': 0.0,
                    'post_count': 0,
                    'data_quality': 0.0,
                    'viral_score': 0.0
                }
            
            sentiments = [s.SocialSentiment for s in sentiment_data]
            posts = [s.SocialMediaPost for s in sentiment_data]
            
            # 计算影响力加权情感分数
            weighted_scores = []
            weights = []
            viral_scores = []
            
            for sentiment, post in zip(sentiments, posts):
                # 权重基于用户影响力、病毒传播度和可信度
                influence_weight = post.user_influence_score or 0.1
                viral_weight = post.viral_score or 0.1
                credibility_weight = post.credibility_score or 0.5
                confidence_weight = sentiment.confidence
                
                # 时间衰减
                time_decay = self._calculate_time_decay(post.posted_at, time_window)
                
                weight = influence_weight * viral_weight * credibility_weight * confidence_weight * time_decay
                
                weighted_scores.append(sentiment.weighted_sentiment * weight)
                weights.append(weight)
                viral_scores.append(post.viral_score or 0.0)
            
            # 计算最终分数
            if sum(weights) > 0:
                final_sentiment = sum(weighted_scores) / sum(weights)
                avg_confidence = np.mean([s.confidence for s in sentiments])
                data_quality = np.mean([p.credibility_score or 0.5 for p in posts])
                avg_viral_score = np.mean(viral_scores)
            else:
                final_sentiment = 0.0
                avg_confidence = 0.0
                data_quality = 0.0
                avg_viral_score = 0.0
            
            return {
                'sentiment_score': final_sentiment,
                'confidence': avg_confidence,
                'post_count': len(sentiment_data),
                'data_quality': data_quality,
                'viral_score': avg_viral_score,
                'engagement_total': sum([p.like_count + p.share_count + p.comment_count for p in posts])
            }
            
        except Exception as e:
            logger.error(f"社交媒体情感聚合失败: {e}")
            return {
                'sentiment_score': 0.0,
                'confidence': 0.0,
                'post_count': 0,
                'data_quality': 0.0,
                'viral_score': 0.0
            }
    
    def _calculate_time_decay(self, posted_at: datetime, time_window: int) -> float:
        """计算时间衰减权重"""
        try:
            hours_ago = (datetime.utcnow() - posted_at).total_seconds() / 3600
            decay_rate = 0.15  # 社交媒体衰减更快
            
            time_weight = np.exp(-decay_rate * hours_ago / time_window)
            return max(0.05, time_weight)  # 最小权重0.05
            
        except Exception as e:
            logger.error(f"时间衰减计算失败: {e}")
            return 0.5


class AlternativeDataAggregator:
    """另类数据聚合器"""
    
    def __init__(self):
        pass
    
    async def aggregate_alternative_data(
        self,
        symbol: str,
        time_window: int,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """聚合另类数据"""
        try:
            start_time = datetime.utcnow() - timedelta(hours=time_window)
            
            # 查询相关另类数据
            query = (
                select(AlternativeData)
                .where(
                    and_(
                        AlternativeData.timestamp >= start_time,
                        AlternativeData.related_symbols.contains([symbol])
                    )
                )
                .order_by(desc(AlternativeData.timestamp))
            )
            
            result = await db.execute(query)
            alt_data = result.scalars().all()
            
            if not alt_data:
                return {
                    'composite_score': 0.0,
                    'confidence': 0.0,
                    'data_count': 0,
                    'data_quality': 0.0
                }
            
            # 按数据类型分组处理
            data_by_type = {}
            for data in alt_data:
                data_type = data.data_type
                if data_type not in data_by_type:
                    data_by_type[data_type] = []
                data_by_type[data_type].append(data)
            
            type_scores = {}
            type_weights = {}
            
            for data_type, data_list in data_by_type.items():
                score, weight = self._process_data_type(data_type, data_list, time_window)
                type_scores[data_type] = score
                type_weights[data_type] = weight
            
            # 计算综合分数
            if sum(type_weights.values()) > 0:
                composite_score = sum(
                    score * weight for score, weight in zip(type_scores.values(), type_weights.values())
                ) / sum(type_weights.values())
            else:
                composite_score = 0.0
            
            avg_quality = np.mean([d.quality_score for d in alt_data])
            avg_confidence = np.mean([d.accuracy for d in alt_data])
            
            return {
                'composite_score': composite_score,
                'confidence': avg_confidence,
                'data_count': len(alt_data),
                'data_quality': avg_quality,
                'type_breakdown': type_scores
            }
            
        except Exception as e:
            logger.error(f"另类数据聚合失败: {e}")
            return {
                'composite_score': 0.0,
                'confidence': 0.0,
                'data_count': 0,
                'data_quality': 0.0
            }
    
    def _process_data_type(
        self, 
        data_type: str, 
        data_list: List[Any], 
        time_window: int
    ) -> Tuple[float, float]:
        """处理特定类型的数据"""
        try:
            if data_type == 'economic_indicator':
                return self._process_economic_indicators(data_list, time_window)
            elif data_type == 'economic_activity':
                return self._process_economic_activity(data_list, time_window)
            elif data_type == 'weather':
                return self._process_weather_data(data_list, time_window)
            else:
                # 通用处理
                scores = []
                weights = []
                
                for data in data_list:
                    processed = data.processed_data or {}
                    
                    # 提取数值特征
                    numeric_values = [v for v in processed.values() if isinstance(v, (int, float))]
                    if numeric_values:
                        score = np.mean(numeric_values)
                        weight = data.quality_score * data.timeliness
                        
                        scores.append(score)
                        weights.append(weight)
                
                if weights and sum(weights) > 0:
                    final_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                    avg_weight = np.mean(weights)
                    return final_score, avg_weight
                else:
                    return 0.0, 0.0
                    
        except Exception as e:
            logger.error(f"数据类型处理失败: {e}")
            return 0.0, 0.0
    
    def _process_economic_indicators(self, data_list: List[Any], time_window: int) -> Tuple[float, float]:
        """处理经济指标数据"""
        try:
            scores = []
            weights = []
            
            for data in data_list:
                processed = data.processed_data or {}
                
                # 提取经济指标信号
                if 'pmi_signal' in processed:
                    score = processed['pmi_signal'] * processed.get('pmi_strength', 0.5)
                elif 'gdp_growth' in processed:
                    score = processed['gdp_growth'] * processed.get('gdp_trend', 1)
                elif 'trade_surplus_ratio' in processed:
                    score = processed['trade_surplus_ratio']
                else:
                    score = 0.0
                
                weight = data.quality_score * data.timeliness
                
                scores.append(score)
                weights.append(weight)
            
            if weights and sum(weights) > 0:
                final_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                avg_weight = np.mean(weights)
                return final_score, avg_weight
            else:
                return 0.0, 0.0
                
        except Exception as e:
            logger.error(f"经济指标处理失败: {e}")
            return 0.0, 0.0
    
    def _process_economic_activity(self, data_list: List[Any], time_window: int) -> Tuple[float, float]:
        """处理经济活动数据"""
        try:
            scores = []
            weights = []
            
            for data in data_list:
                processed = data.processed_data or {}
                
                # 经济活动指数
                activity_index = processed.get('economic_activity_index', 0)
                development_index = processed.get('development_index', 0)
                
                # 标准化分数
                score = (activity_index + development_index) / 2
                weight = data.quality_score * data.timeliness
                
                scores.append(score)
                weights.append(weight)
            
            if weights and sum(weights) > 0:
                final_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                avg_weight = np.mean(weights)
                return final_score, avg_weight
            else:
                return 0.0, 0.0
                
        except Exception as e:
            logger.error(f"经济活动处理失败: {e}")
            return 0.0, 0.0
    
    def _process_weather_data(self, data_list: List[Any], time_window: int) -> Tuple[float, float]:
        """处理天气数据"""
        try:
            scores = []
            weights = []
            
            for data in data_list:
                processed = data.processed_data or {}
                
                # 天气对经济的影响（简化模型）
                if 'overall_impact_score' in processed:
                    # 极端天气的负面影响
                    score = -processed['overall_impact_score']
                elif 'weather_comfort_index' in processed:
                    # 舒适天气的正面影响
                    score = processed['weather_comfort_index'] - 0.5
                else:
                    score = 0.0
                
                weight = data.quality_score * data.timeliness
                
                scores.append(score)
                weights.append(weight)
            
            if weights and sum(weights) > 0:
                final_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                avg_weight = np.mean(weights)
                return final_score, avg_weight
            else:
                return 0.0, 0.0
                
        except Exception as e:
            logger.error(f"天气数据处理失败: {e}")
            return 0.0, 0.0


class MultimodalSignalService:
    """多模态信号服务"""
    
    def __init__(self):
        self.weight_calculator = SignalWeightCalculator()
        self.news_aggregator = NewsSentimentAggregator()
        self.social_aggregator = SocialSentimentAggregator()
        self.alt_data_aggregator = AlternativeDataAggregator()
    
    async def generate_multimodal_signal(
        self,
        symbol: str,
        time_window: int,  # 小时
        db: AsyncSession,
        signal_type: str = "composite"
    ) -> Dict[str, Any]:
        """生成多模态信号"""
        try:
            # 并行聚合各类数据
            news_task = self.news_aggregator.aggregate_news_sentiment(symbol, time_window, db)
            social_task = self.social_aggregator.aggregate_social_sentiment(symbol, time_window, db)
            alt_data_task = self.alt_data_aggregator.aggregate_alternative_data(symbol, time_window, db)
            
            news_result, social_result, alt_data_result = await asyncio.gather(
                news_task, social_task, alt_data_task
            )
            
            # 计算数据质量和新鲜度分数
            data_quality_scores = {
                'news_sentiment': news_result['data_quality'],
                'social_sentiment': social_result['data_quality'],
                'alternative_data': alt_data_result['data_quality']
            }
            
            data_freshness_scores = {
                'news_sentiment': news_result.get('time_decay_avg', 0.5),
                'social_sentiment': 0.8,  # 社交媒体通常更新鲜
                'alternative_data': alt_data_result['confidence']
            }
            
            # 历史准确率（这里使用模拟值，实际应从历史数据计算）
            historical_accuracy = {
                'news_sentiment': 0.65,
                'social_sentiment': 0.55,
                'alternative_data': 0.70
            }
            
            # 计算动态权重
            weights = self.weight_calculator.calculate_dynamic_weights(
                data_quality_scores, data_freshness_scores, historical_accuracy
            )
            
            # 生成综合信号
            signal_components = {
                'news_sentiment': news_result['sentiment_score'],
                'social_sentiment': social_result['sentiment_score'],
                'alternative_data': alt_data_result['composite_score']
            }
            
            # 计算加权综合信号
            weighted_signal = sum(
                signal_components[source] * weight 
                for source, weight in weights.items() 
                if source in signal_components
            )
            
            # 计算信号强度
            signal_strength = self._calculate_signal_strength(
                signal_components, weights, data_quality_scores
            )
            
            # 计算置信度
            confidence = self._calculate_confidence(
                news_result, social_result, alt_data_result, weights
            )
            
            # 保存信号到数据库
            signal_record = MultimodalSignal(
                signal_name=f"{symbol}_multimodal_signal",
                signal_type=signal_type,
                signal_category="composite",
                target_symbol=symbol,
                signal_value=weighted_signal,
                signal_strength=signal_strength,
                confidence=confidence,
                signal_timestamp=datetime.utcnow(),
                valid_from=datetime.utcnow(),
                valid_until=datetime.utcnow() + timedelta(hours=time_window),
                data_sources=list(weights.keys()),
                source_weights=weights,
                calculation_method="weighted_ensemble",
                model_version="1.0",
                feature_importance=signal_components
            )
            
            db.add(signal_record)
            await db.commit()
            
            return {
                "success": True,
                "signal_id": signal_record.id,
                "symbol": symbol,
                "signal_value": weighted_signal,
                "signal_strength": signal_strength,
                "confidence": confidence,
                "components": {
                    "news_sentiment": news_result,
                    "social_sentiment": social_result,
                    "alternative_data": alt_data_result
                },
                "weights": weights,
                "data_quality": data_quality_scores,
                "timestamp": signal_record.signal_timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"多模态信号生成失败: {e}")
            await db.rollback()
            return {"success": False, "error": str(e)}
    
    def _calculate_signal_strength(
        self,
        signal_components: Dict[str, float],
        weights: Dict[str, float],
        data_quality_scores: Dict[str, float]
    ) -> float:
        """计算信号强度"""
        try:
            # 基于信号一致性和数据质量计算强度
            signals = [abs(v) for v in signal_components.values() if v is not None]
            qualities = [data_quality_scores.get(k, 0.5) for k in signal_components.keys()]
            
            if not signals:
                return 0.0
            
            # 信号强度 = 平均信号绝对值 * 平均数据质量
            avg_signal_magnitude = np.mean(signals)
            avg_quality = np.mean(qualities)
            
            strength = avg_signal_magnitude * avg_quality
            return min(strength, 1.0)
            
        except Exception as e:
            logger.error(f"信号强度计算失败: {e}")
            return 0.0
    
    def _calculate_confidence(
        self,
        news_result: Dict[str, Any],
        social_result: Dict[str, Any],
        alt_data_result: Dict[str, Any],
        weights: Dict[str, float]
    ) -> float:
        """计算置信度"""
        try:
            confidences = []
            component_weights = []
            
            # 新闻情感置信度
            if news_result['article_count'] > 0:
                news_confidence = news_result['confidence'] * (1 + np.log(news_result['article_count']) / 10)
                confidences.append(news_confidence)
                component_weights.append(weights.get('news_sentiment', 0))
            
            # 社交媒体置信度
            if social_result['post_count'] > 0:
                social_confidence = social_result['confidence'] * (1 + social_result['viral_score'])
                confidences.append(social_confidence)
                component_weights.append(weights.get('social_sentiment', 0))
            
            # 另类数据置信度
            if alt_data_result['data_count'] > 0:
                alt_confidence = alt_data_result['confidence']
                confidences.append(alt_confidence)
                component_weights.append(weights.get('alternative_data', 0))
            
            # 加权平均置信度
            if component_weights and sum(component_weights) > 0:
                weighted_confidence = sum(
                    c * w for c, w in zip(confidences, component_weights)
                ) / sum(component_weights)
                
                return min(weighted_confidence, 1.0)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0


# 全局多模态信号服务实例
multimodal_signal_service = MultimodalSignalService()
