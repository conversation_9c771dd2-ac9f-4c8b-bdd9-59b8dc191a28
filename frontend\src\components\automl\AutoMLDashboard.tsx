'use client';

/**
 * AutoML自动化机器学习仪表板
 * 
 * 提供自动特征选择、超参数优化、模型选择等AutoML功能的管理界面
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Table,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Descriptions,
  Alert,
  Badge,
  Tooltip,
  DatePicker,
  List,
  Avatar,
  Timeline,
  Spin,
  Steps,
  Divider
} from 'antd';
import {
  ExperimentOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  BarChartOutlined,
  LineChartOutlined,
  BulbOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EyeOutlined,
  DeleteOutlined,
  DownloadOutlined,
  SettingOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { Line, Column, Radar } from '@ant-design/plots';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { Step } = Steps;
const { TextArea } = Input;

interface AutoMLExperiment {
  id: number;
  name: string;
  description: string;
  experiment_type: string;
  problem_type: string;
  status: string;
  progress: number;
  current_stage: string;
  total_models_trained: number;
  best_score: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  runtime_minutes?: number;
}

interface AutoMLModel {
  id: number;
  experiment_id: number;
  algorithm: string;
  validation_score: number;
  cross_validation_score: number;
  training_time_seconds: number;
  is_best_model: boolean;
  is_deployed: boolean;
  status: string;
  hyperparameters: Record<string, any>;
  performance_metrics: Record<string, any>;
  feature_importance: Record<string, number>;
}

export const AutoMLDashboard: React.FC = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('experiments');
  const [experiments, setExperiments] = useState<AutoMLExperiment[]>([]);
  const [models, setModels] = useState<AutoMLModel[]>([]);
  const [selectedExperiment, setSelectedExperiment] = useState<AutoMLExperiment | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [resultsModalVisible, setResultsModalVisible] = useState(false);

  useEffect(() => {
    fetchExperiments();
  }, []);

  useEffect(() => {
    if (activeTab === 'models') {
      fetchModels();
    }
  }, [activeTab]);

  const fetchExperiments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/automl/experiments', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setExperiments(result.data.items);
      }
    } catch (error) {
      console.error('获取实验列表失败:', error);
      message.error('获取实验列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchModels = async (experimentId?: number) => {
    try {
      const url = experimentId 
        ? `/api/v1/automl/models?experiment_id=${experimentId}`
        : '/api/v1/automl/models';
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setModels(result.data.items);
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      message.error('获取模型列表失败');
    }
  };

  const handleCreateExperiment = async (values: any) => {
    try {
      const response = await fetch('/api/v1/automl/experiments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('AutoML实验创建成功');
        setCreateModalVisible(false);
        form.resetFields();
        fetchExperiments();
      } else {
        message.error(result.message || 'AutoML实验创建失败');
      }
    } catch (error) {
      console.error('创建实验失败:', error);
      message.error('创建实验失败');
    }
  };

  const handleViewExperimentDetails = async (experiment: AutoMLExperiment) => {
    try {
      const response = await fetch(`/api/v1/automl/experiments/${experiment.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setSelectedExperiment({ ...experiment, ...result.data });
        setDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取实验详情失败:', error);
      message.error('获取实验详情失败');
    }
  };

  const handleViewExperimentResults = async (experiment: AutoMLExperiment) => {
    try {
      const response = await fetch(`/api/v1/automl/experiments/${experiment.id}/results`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.code === 200) {
        setSelectedExperiment({ ...experiment, results: result.data });
        setResultsModalVisible(true);
      }
    } catch (error) {
      console.error('获取实验结果失败:', error);
      message.error('获取实验结果失败');
    }
  };

  const handleDeleteExperiment = async (experimentId: number) => {
    try {
      const response = await fetch(`/api/v1/automl/experiments/${experimentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const result = await response.json();
      
      if (result.code === 200) {
        message.success('实验删除成功');
        fetchExperiments();
      } else {
        message.error(result.message || '实验删除失败');
      }
    } catch (error) {
      console.error('删除实验失败:', error);
      message.error('删除实验失败');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const renderExperimentProgress = (experiment: AutoMLExperiment) => {
    const stages = [
      { key: 'loading_data', title: '数据加载' },
      { key: 'feature_selection', title: '特征选择' },
      { key: 'model_selection', title: '模型选择' },
      { key: 'ensemble', title: '模型集成' },
      { key: 'completed', title: '完成' }
    ];

    const currentStageIndex = stages.findIndex(stage => 
      experiment.current_stage?.includes(stage.key)
    );

    return (
      <Steps current={currentStageIndex} size="small">
        {stages.map(stage => (
          <Step key={stage.key} title={stage.title} />
        ))}
      </Steps>
    );
  };

  const renderFeatureImportanceChart = (featureImportance: Record<string, number>) => {
    const data = Object.entries(featureImportance)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([feature, importance]) => ({
        feature,
        importance: importance * 100
      }));

    const config = {
      data,
      xField: 'importance',
      yField: 'feature',
      seriesField: 'feature',
      color: '#1890ff',
      label: {
        position: 'middle' as const,
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          formatter: (v: string) => `${v}%`,
        },
      },
    };

    return <Column {...config} height={300} />;
  };

  const experimentColumns = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: AutoMLExperiment) => (
        <Space>
          {getStatusIcon(record.status)}
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">{record.description}</div>
          </div>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'problem_type',
      key: 'problem_type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: AutoMLExperiment) => (
        <div>
          <Tag color={getStatusColor(status)}>{status}</Tag>
          {status === 'running' && (
            <Progress 
              percent={record.progress} 
              size="small" 
              style={{ width: 100, marginTop: 4 }}
            />
          )}
        </div>
      )
    },
    {
      title: '模型数量',
      dataIndex: 'total_models_trained',
      key: 'total_models_trained',
      render: (count: number) => count || 0
    },
    {
      title: '最佳分数',
      dataIndex: 'best_score',
      key: 'best_score',
      render: (score: number) => score ? score.toFixed(4) : '-'
    },
    {
      title: '运行时间',
      dataIndex: 'runtime_minutes',
      key: 'runtime_minutes',
      render: (minutes: number) => minutes ? `${minutes.toFixed(1)}分钟` : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: AutoMLExperiment) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewExperimentDetails(record)}
          >
            详情
          </Button>
          {record.status === 'completed' && (
            <Button
              type="link"
              size="small"
              icon={<BarChartOutlined />}
              onClick={() => handleViewExperimentResults(record)}
            >
              结果
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个实验吗？此操作不可恢复。',
                onOk: () => handleDeleteExperiment(record.id)
              });
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  const modelColumns = [
    {
      title: '模型',
      dataIndex: 'algorithm',
      key: 'algorithm',
      render: (algorithm: string, record: AutoMLModel) => (
        <Space>
          {record.is_best_model && <TrophyOutlined style={{ color: '#faad14' }} />}
          <div>
            <div className="font-medium">{algorithm}</div>
            <div className="text-xs text-gray-500">ID: {record.id}</div>
          </div>
        </Space>
      )
    },
    {
      title: '验证分数',
      dataIndex: 'validation_score',
      key: 'validation_score',
      render: (score: number) => score ? score.toFixed(4) : '-',
      sorter: (a: AutoMLModel, b: AutoMLModel) => (a.validation_score || 0) - (b.validation_score || 0)
    },
    {
      title: '交叉验证',
      dataIndex: 'cross_validation_score',
      key: 'cross_validation_score',
      render: (score: number) => score ? score.toFixed(4) : '-'
    },
    {
      title: '训练时间',
      dataIndex: 'training_time_seconds',
      key: 'training_time_seconds',
      render: (seconds: number) => seconds ? `${seconds.toFixed(1)}s` : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: AutoMLModel) => (
        <Space>
          <Tag color={getStatusColor(status)}>{status}</Tag>
          {record.is_deployed && <Tag color="green">已部署</Tag>}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: AutoMLModel) => (
        <Space>
          <Button type="link" size="small" icon={<EyeOutlined />}>
            详情
          </Button>
          <Button type="link" size="small" icon={<PlayCircleOutlined />}>
            预测
          </Button>
          <Button type="link" size="small" icon={<DownloadOutlined />}>
            下载
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AutoML自动化机器学习</h1>
          <p className="text-gray-600">自动特征选择、超参数优化、模型选择</p>
        </div>
        <Button
          type="primary"
          icon={<ExperimentOutlined />}
          onClick={() => setCreateModalVisible(true)}
        >
          创建实验
        </Button>
      </div>

      {/* 统计概览 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总实验数"
              value={experiments.length}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={experiments.filter(e => e.status === 'running').length}
              prefix={<LoadingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={experiments.filter(e => e.status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总模型数"
              value={models.length}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <Badge count={experiments.length} size="small">
                <Space>
                  <ExperimentOutlined />
                  <span>实验管理</span>
                </Space>
              </Badge>
            }
            key="experiments"
          >
            <Table
              columns={experimentColumns}
              dataSource={experiments}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个实验`
              }}
            />
          </TabPane>

          <TabPane
            tab={
              <Badge count={models.length} size="small">
                <Space>
                  <RobotOutlined />
                  <span>模型管理</span>
                </Space>
              </Badge>
            }
            key="models"
          >
            <Table
              columns={modelColumns}
              dataSource={models}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个模型`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建实验弹窗 */}
      <Modal
        title="创建AutoML实验"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateExperiment}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="实验名称" rules={[{ required: true }]}>
                <Input placeholder="请输入实验名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="experiment_type" label="实验类型" rules={[{ required: true }]}>
                <Select placeholder="选择实验类型">
                  <Option value="classification">分类</Option>
                  <Option value="regression">回归</Option>
                  <Option value="time_series">时间序列</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="实验描述">
            <TextArea rows={3} placeholder="请输入实验描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="problem_type" label="问题类型" rules={[{ required: true }]}>
                <Select placeholder="选择问题类型">
                  <Option value="binary_classification">二分类</Option>
                  <Option value="multi_classification">多分类</Option>
                  <Option value="regression">回归</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="dataset_source" label="数据源" rules={[{ required: true }]}>
                <Select placeholder="选择数据源">
                  <Option value="jqdata">JQData</Option>
                  <Option value="upload">上传文件</Option>
                  <Option value="database">数据库</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="target_column" label="目标列" rules={[{ required: true }]}>
            <Input placeholder="请输入目标列名称" />
          </Form.Item>

          <Divider>高级配置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="max_runtime_minutes" label="最大运行时间（分钟）">
                <InputNumber min={5} max={480} placeholder="60" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="max_models" label="最大模型数量">
                <InputNumber min={5} max={200} placeholder="50" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="cross_validation_folds" label="交叉验证折数">
                <InputNumber min={2} max={10} placeholder="5" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="enable_feature_selection" label="特征选择" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="enable_hyperparameter_tuning" label="超参数调优" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="enable_ensemble" label="模型集成" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="dataset_config" label="数据集配置" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 实验详情弹窗 */}
      <Modal
        title="实验详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedExperiment && (
          <div className="space-y-4">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="实验名称">{selectedExperiment.name}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(selectedExperiment.status)}>
                  {selectedExperiment.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="问题类型">{selectedExperiment.problem_type}</Descriptions.Item>
              <Descriptions.Item label="进度">{selectedExperiment.progress}%</Descriptions.Item>
              <Descriptions.Item label="当前阶段" span={2}>
                {selectedExperiment.current_stage}
              </Descriptions.Item>
            </Descriptions>

            {selectedExperiment.status === 'running' && (
              <div>
                <h4 className="mb-2">实验进度</h4>
                {renderExperimentProgress(selectedExperiment)}
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 实验结果弹窗 */}
      <Modal
        title="实验结果"
        open={resultsModalVisible}
        onCancel={() => setResultsModalVisible(false)}
        footer={null}
        width={1000}
      >
        {selectedExperiment?.results && (
          <div className="space-y-4">
            <Alert
              message={`实验完成，共训练了 ${selectedExperiment.results.models?.length || 0} 个模型`}
              type="success"
              showIcon
            />

            {selectedExperiment.results.models?.length > 0 && (
              <div>
                <h4 className="mb-2">模型性能对比</h4>
                <Table
                  dataSource={selectedExperiment.results.models}
                  columns={modelColumns.slice(0, -1)}
                  rowKey="id"
                  size="small"
                  pagination={false}
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};
