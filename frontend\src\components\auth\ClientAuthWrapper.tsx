'use client';

import { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useAuthStore } from '@/store/auth';

interface ClientAuthWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ClientAuthWrapper({ children, fallback }: ClientAuthWrapperProps) {
  const [isHydrated, setIsHydrated] = useState(false);
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    // 确保组件已经在客户端完全水合
    setIsHydrated(true);
  }, []);

  // 在水合完成前显示加载状态，防止 hydration 错误
  if (!isHydrated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 如果未认证，显示 fallback 或 null
  if (!isAuthenticated) {
    return fallback || null;
  }

  return <>{children}</>;
}
