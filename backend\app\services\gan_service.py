"""
GAN生成对抗网络服务

实现用于金融数据生成和异常检测的GAN模型
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.logging import logger
from app.models.deep_learning import GANModel, GANGeneratedData


class Generator(nn.Module):
    """生成器网络"""
    
    def __init__(self, noise_dim: int, output_dim: int, hidden_dims: List[int] = None):
        super().__init__()
        
        if hidden_dims is None:
            hidden_dims = [128, 256, 512]
        
        layers = []
        input_dim = noise_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(inplace=True)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, output_dim))
        layers.append(nn.Tanh())  # 输出范围[-1, 1]
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, noise):
        return self.model(noise)


class Discriminator(nn.Module):
    """判别器网络"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int] = None):
        super().__init__()
        
        if hidden_dims is None:
            hidden_dims = [512, 256, 128]
        
        layers = []
        current_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout(0.3)
            ])
            current_dim = hidden_dim
        
        layers.append(nn.Linear(current_dim, 1))
        layers.append(nn.Sigmoid())
        
        self.model = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.model(x)


class TimeSeriesGAN(nn.Module):
    """时间序列GAN"""
    
    def __init__(
        self,
        sequence_length: int,
        feature_dim: int,
        noise_dim: int = 100,
        generator_hidden_dims: List[int] = None,
        discriminator_hidden_dims: List[int] = None
    ):
        super().__init__()
        
        self.sequence_length = sequence_length
        self.feature_dim = feature_dim
        self.noise_dim = noise_dim
        
        output_dim = sequence_length * feature_dim
        
        self.generator = Generator(
            noise_dim, 
            output_dim, 
            generator_hidden_dims
        )
        
        self.discriminator = Discriminator(
            output_dim, 
            discriminator_hidden_dims
        )
    
    def generate(self, batch_size: int, device: torch.device):
        """生成样本"""
        noise = torch.randn(batch_size, self.noise_dim, device=device)
        generated = self.generator(noise)
        return generated.view(batch_size, self.sequence_length, self.feature_dim)


class FinancialDataset(Dataset):
    """金融数据集"""
    
    def __init__(self, data: np.ndarray, sequence_length: int):
        self.data = data
        self.sequence_length = sequence_length
    
    def __len__(self):
        return len(self.data) - self.sequence_length + 1
    
    def __getitem__(self, idx):
        sequence = self.data[idx:idx + self.sequence_length]
        return torch.FloatTensor(sequence)


class GANService:
    """GAN服务"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models_dir = "gan_models"
        os.makedirs(self.models_dir, exist_ok=True)
    
    async def create_model(
        self,
        user_id: int,
        model_config: Dict[str, Any],
        db: AsyncSession
    ) -> GANModel:
        """创建GAN模型"""
        try:
            model = GANModel(
                user_id=user_id,
                name=model_config["name"],
                description=model_config.get("description", ""),
                gan_type=model_config.get("gan_type", "vanilla"),
                generator_input_dim=model_config.get("generator_input_dim", 100),
                generator_output_dim=model_config.get("generator_output_dim"),
                discriminator_input_dim=model_config.get("discriminator_input_dim"),
                generator_architecture=model_config.get("generator_architecture", {}),
                discriminator_architecture=model_config.get("discriminator_architecture", {}),
                batch_size=model_config.get("batch_size", 64),
                learning_rate_g=model_config.get("learning_rate_g", 0.0002),
                learning_rate_d=model_config.get("learning_rate_d", 0.0002),
                beta1=model_config.get("beta1", 0.5),
                beta2=model_config.get("beta2", 0.999),
                epochs=model_config.get("epochs", 200),
                loss_function=model_config.get("loss_function", "bce"),
                data_columns=model_config.get("data_columns", []),
                sequence_length=model_config.get("sequence_length", 60)
            )
            
            db.add(model)
            await db.commit()
            await db.refresh(model)
            
            logger.info(f"创建GAN模型成功: {model.id}")
            return model
            
        except Exception as e:
            logger.error(f"创建GAN模型失败: {e}")
            await db.rollback()
            raise
    
    async def train_model(
        self,
        model_id: int,
        training_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """训练GAN模型"""
        try:
            # 获取模型配置
            result = await db.execute(
                select(GANModel).where(GANModel.id == model_id)
            )
            model_config = result.scalar_one_or_none()
            
            if not model_config:
                raise ValueError(f"模型不存在: {model_id}")
            
            # 更新状态
            model_config.status = "training"
            model_config.training_progress = 0
            await db.commit()
            
            # 准备训练数据
            train_data, scaler = self._prepare_training_data(training_data, model_config)
            
            # 创建数据加载器
            dataset = FinancialDataset(train_data, model_config.sequence_length)
            dataloader = DataLoader(
                dataset, 
                batch_size=model_config.batch_size, 
                shuffle=True
            )
            
            # 创建GAN模型
            feature_dim = train_data.shape[1]
            gan_model = TimeSeriesGAN(
                sequence_length=model_config.sequence_length,
                feature_dim=feature_dim,
                noise_dim=model_config.generator_input_dim,
                generator_hidden_dims=model_config.generator_architecture.get('hidden_dims'),
                discriminator_hidden_dims=model_config.discriminator_architecture.get('hidden_dims')
            ).to(self.device)
            
            # 优化器
            optimizer_g = optim.Adam(
                gan_model.generator.parameters(),
                lr=model_config.learning_rate_g,
                betas=(model_config.beta1, model_config.beta2)
            )
            optimizer_d = optim.Adam(
                gan_model.discriminator.parameters(),
                lr=model_config.learning_rate_d,
                betas=(model_config.beta1, model_config.beta2)
            )
            
            # 损失函数
            if model_config.loss_function == "bce":
                criterion = nn.BCELoss()
            else:
                criterion = nn.MSELoss()
            
            # 训练循环
            g_losses = []
            d_losses = []
            
            for epoch in range(model_config.epochs):
                epoch_g_loss = 0.0
                epoch_d_loss = 0.0
                
                for batch_idx, real_data in enumerate(dataloader):
                    batch_size = real_data.size(0)
                    real_data = real_data.view(batch_size, -1).to(self.device)
                    
                    # 真实和虚假标签
                    real_labels = torch.ones(batch_size, 1, device=self.device)
                    fake_labels = torch.zeros(batch_size, 1, device=self.device)
                    
                    # 训练判别器
                    optimizer_d.zero_grad()
                    
                    # 真实数据
                    real_output = gan_model.discriminator(real_data)
                    d_loss_real = criterion(real_output, real_labels)
                    
                    # 生成数据
                    noise = torch.randn(batch_size, model_config.generator_input_dim, device=self.device)
                    fake_data = gan_model.generator(noise)
                    fake_output = gan_model.discriminator(fake_data.detach())
                    d_loss_fake = criterion(fake_output, fake_labels)
                    
                    d_loss = d_loss_real + d_loss_fake
                    d_loss.backward()
                    optimizer_d.step()
                    
                    # 训练生成器
                    optimizer_g.zero_grad()
                    
                    fake_output = gan_model.discriminator(fake_data)
                    g_loss = criterion(fake_output, real_labels)
                    g_loss.backward()
                    optimizer_g.step()
                    
                    epoch_g_loss += g_loss.item()
                    epoch_d_loss += d_loss.item()
                
                # 记录损失
                avg_g_loss = epoch_g_loss / len(dataloader)
                avg_d_loss = epoch_d_loss / len(dataloader)
                g_losses.append(avg_g_loss)
                d_losses.append(avg_d_loss)
                
                # 更新进度
                progress = int((epoch + 1) / model_config.epochs * 100)
                model_config.training_progress = progress
                
                if epoch % 20 == 0:  # 每20个epoch更新一次数据库
                    await db.commit()
                    logger.info(f"Epoch {epoch}: G_loss={avg_g_loss:.4f}, D_loss={avg_d_loss:.4f}")
            
            # 保存模型
            generator_path = os.path.join(self.models_dir, f"generator_{model_id}.pth")
            discriminator_path = os.path.join(self.models_dir, f"discriminator_{model_id}.pth")
            
            torch.save({
                'generator_state_dict': gan_model.generator.state_dict(),
                'discriminator_state_dict': gan_model.discriminator.state_dict(),
                'optimizer_g_state_dict': optimizer_g.state_dict(),
                'optimizer_d_state_dict': optimizer_d.state_dict(),
                'scaler': scaler,
                'config': {
                    'sequence_length': model_config.sequence_length,
                    'feature_dim': feature_dim,
                    'noise_dim': model_config.generator_input_dim,
                    'generator_hidden_dims': model_config.generator_architecture.get('hidden_dims'),
                    'discriminator_hidden_dims': model_config.discriminator_architecture.get('hidden_dims')
                }
            }, generator_path)
            
            # 计算质量指标
            quality_metrics = self._calculate_quality_metrics(gan_model, train_data)
            
            # 更新模型状态
            model_config.status = "trained"
            model_config.training_progress = 100
            model_config.generator_loss = g_losses[-1]
            model_config.discriminator_loss = d_losses[-1]
            model_config.inception_score = quality_metrics.get('inception_score')
            model_config.fid_score = quality_metrics.get('fid_score')
            model_config.generator_path = generator_path
            model_config.discriminator_path = discriminator_path
            model_config.model_size = os.path.getsize(generator_path) + os.path.getsize(discriminator_path)
            model_config.trained_at = datetime.utcnow()
            
            await db.commit()
            
            return {
                "success": True,
                "model_id": model_id,
                "generator_loss": g_losses[-1],
                "discriminator_loss": d_losses[-1],
                "quality_metrics": quality_metrics,
                "epochs_trained": model_config.epochs
            }
            
        except Exception as e:
            logger.error(f"训练GAN模型失败: {e}")
            
            # 更新失败状态
            model_config.status = "failed"
            await db.commit()
            
            return {
                "success": False,
                "error": str(e)
            }
    
    def _prepare_training_data(
        self, 
        data: Dict[str, Any], 
        model_config: GANModel
    ) -> Tuple[np.ndarray, Any]:
        """准备训练数据"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 选择数据列
            data_cols = model_config.data_columns or df.columns.tolist()
            df_data = df[data_cols]
            
            # 数据归一化到[-1, 1]范围
            scaler = MinMaxScaler(feature_range=(-1, 1))
            scaled_data = scaler.fit_transform(df_data.values)
            
            return scaled_data, scaler
            
        except Exception as e:
            logger.error(f"准备GAN训练数据失败: {e}")
            raise
    
    def _calculate_quality_metrics(
        self, 
        gan_model: TimeSeriesGAN, 
        real_data: np.ndarray
    ) -> Dict[str, float]:
        """计算生成质量指标"""
        try:
            # 生成样本
            gan_model.eval()
            with torch.no_grad():
                generated_samples = gan_model.generate(1000, self.device)
                generated_samples = generated_samples.cpu().numpy()
            
            # 简化的质量指标计算
            # 实际应用中可以使用更复杂的指标如IS、FID等
            
            # 统计相似性
            real_mean = np.mean(real_data, axis=0)
            real_std = np.std(real_data, axis=0)
            
            gen_flat = generated_samples.reshape(-1, generated_samples.shape[-1])
            gen_mean = np.mean(gen_flat, axis=0)
            gen_std = np.std(gen_flat, axis=0)
            
            mean_diff = np.mean(np.abs(real_mean - gen_mean))
            std_diff = np.mean(np.abs(real_std - gen_std))
            
            # 简化的质量分数
            quality_score = 1.0 / (1.0 + mean_diff + std_diff)
            
            return {
                'quality_score': float(quality_score),
                'mean_difference': float(mean_diff),
                'std_difference': float(std_diff),
                'inception_score': float(quality_score * 10),  # 模拟IS分数
                'fid_score': float((1 - quality_score) * 100)  # 模拟FID分数
            }
            
        except Exception as e:
            logger.error(f"计算质量指标失败: {e}")
            return {'quality_score': 0.0}
    
    async def generate_data(
        self,
        model_id: int,
        generation_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """生成数据"""
        try:
            # 获取模型配置
            result = await db.execute(
                select(GANModel).where(GANModel.id == model_id)
            )
            model_config = result.scalar_one_or_none()
            
            if not model_config or model_config.status != "trained":
                raise ValueError(f"模型不存在或未训练: {model_id}")
            
            # 加载模型
            checkpoint = torch.load(model_config.generator_path, map_location=self.device)
            config = checkpoint['config']
            scaler = checkpoint['scaler']
            
            gan_model = TimeSeriesGAN(**config).to(self.device)
            gan_model.generator.load_state_dict(checkpoint['generator_state_dict'])
            gan_model.eval()
            
            # 生成参数
            num_samples = generation_config.get('num_samples', 100)
            noise_seed = generation_config.get('noise_seed')
            
            if noise_seed:
                torch.manual_seed(noise_seed)
            
            # 生成数据
            with torch.no_grad():
                generated_samples = gan_model.generate(num_samples, self.device)
                generated_samples = generated_samples.cpu().numpy()
            
            # 反归一化
            generated_flat = generated_samples.reshape(-1, generated_samples.shape[-1])
            generated_denorm = scaler.inverse_transform(generated_flat)
            generated_final = generated_denorm.reshape(generated_samples.shape)
            
            # 计算质量指标
            quality_metrics = self._calculate_generation_quality(generated_final)
            
            # 保存生成结果
            generated_data_record = GANGeneratedData(
                model_id=model_id,
                user_id=model_config.user_id,
                generation_date=datetime.utcnow(),
                generation_type=generation_config.get('generation_type', 'synthetic_data'),
                noise_seed=noise_seed,
                conditioning_params=generation_config.get('conditioning_params', {}),
                num_samples=num_samples,
                generated_samples=generated_final.tolist(),
                quality_metrics=quality_metrics,
                purpose=generation_config.get('purpose', 'training_augmentation')
            )
            
            db.add(generated_data_record)
            await db.commit()
            
            return {
                "success": True,
                "generated_data": generated_final.tolist(),
                "quality_metrics": quality_metrics,
                "generation_id": generated_data_record.id
            }
            
        except Exception as e:
            logger.error(f"GAN数据生成失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _calculate_generation_quality(self, generated_data: np.ndarray) -> Dict[str, float]:
        """计算生成数据质量"""
        try:
            # 基本统计指标
            mean_val = np.mean(generated_data)
            std_val = np.std(generated_data)
            min_val = np.min(generated_data)
            max_val = np.max(generated_data)
            
            # 数据有效性检查
            has_nan = np.isnan(generated_data).any()
            has_inf = np.isinf(generated_data).any()
            
            # 质量分数
            quality_score = 1.0 if not (has_nan or has_inf) else 0.0
            
            return {
                'mean': float(mean_val),
                'std': float(std_val),
                'min': float(min_val),
                'max': float(max_val),
                'has_nan': bool(has_nan),
                'has_inf': bool(has_inf),
                'quality_score': quality_score
            }
            
        except Exception as e:
            logger.error(f"计算生成质量失败: {e}")
            return {'quality_score': 0.0}


# 全局GAN服务实例
gan_service = GANService()
