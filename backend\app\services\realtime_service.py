"""
实时数据推送服务

负责获取实时数据并推送给WebSocket客户端
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.core.database import get_db_session
from app.websocket.manager import manager
from app.services.jqdata_service import JQDataService


class RealtimeDataService:
    """实时数据推送服务"""
    
    def __init__(self):
        self.jqdata_service = JQDataService()
        self.is_running = False
        self.push_tasks: Dict[str, asyncio.Task] = {}
        self.last_prices: Dict[str, Dict] = {}
        
    async def start(self):
        """启动实时数据推送"""
        if self.is_running:
            return
            
        self.is_running = True
        logger.info("实时数据推送服务启动")
        
        # 启动各种推送任务
        self.push_tasks['market_data'] = asyncio.create_task(self._push_market_data())
        self.push_tasks['system_status'] = asyncio.create_task(self._push_system_status())
        self.push_tasks['cleanup'] = asyncio.create_task(self._cleanup_connections())
        
    async def stop(self):
        """停止实时数据推送"""
        if not self.is_running:
            return
            
        self.is_running = False
        logger.info("实时数据推送服务停止")
        
        # 取消所有推送任务
        for task_name, task in self.push_tasks.items():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    logger.info(f"推送任务已取消: {task_name}")
        
        self.push_tasks.clear()
    
    async def _push_market_data(self):
        """推送市场数据"""
        while self.is_running:
            try:
                # 获取所有订阅的股票
                subscribed_symbols = list(manager.symbol_subscriptions.keys())
                
                if subscribed_symbols:
                    # 获取真实价格数据
                    price_data = await self._get_realtime_prices(subscribed_symbols)

                    # 推送给订阅者
                    for symbol, data in price_data.items():
                        await manager.broadcast_to_subscribers(symbol, {
                            'type': 'price_update',
                            'symbol': symbol,
                            'data': data,
                            'timestamp': datetime.utcnow().isoformat(),
                        })
                
                # 每3秒推送一次
                await asyncio.sleep(3)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"推送市场数据失败: {e}")
                await asyncio.sleep(5)
    
    async def _push_system_status(self):
        """推送系统状态"""
        while self.is_running:
            try:
                # 获取连接统计
                stats = manager.get_connection_stats()
                
                # 推送系统状态
                await manager.broadcast_message({
                    'type': 'system_status',
                    'data': {
                        'connections': stats['total_connections'],
                        'users': stats['total_users'],
                        'subscriptions': stats['total_subscriptions'],
                        'server_time': datetime.utcnow().isoformat(),
                        'market_status': await self._get_market_status(),
                    },
                    'timestamp': datetime.utcnow().isoformat(),
                })
                
                # 每30秒推送一次
                await asyncio.sleep(30)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"推送系统状态失败: {e}")
                await asyncio.sleep(30)
    
    async def _cleanup_connections(self):
        """清理过期连接"""
        while self.is_running:
            try:
                await manager.cleanup_stale_connections()
                
                # 每5分钟清理一次
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理连接失败: {e}")
                await asyncio.sleep(300)
    
    async def _get_realtime_prices(self, symbols: List[str]) -> Dict[str, Dict]:
        """获取实时价格数据"""
        price_data = {}

        try:
            # 使用JQData获取实时价格数据
            async with get_db_session() as db:
                jqdata_result = await self.jqdata_service.get_current_data(symbols, db)

                if jqdata_result and 'data' in jqdata_result:
                    current_data = jqdata_result['data']

                    for symbol in symbols:
                        if symbol in current_data:
                            stock_data = current_data[symbol]

                            # 获取历史价格用于计算涨跌
                            last_price = self.last_prices.get(symbol, {}).get('price', stock_data.get('last_price', 0))
                            current_price = stock_data.get('last_price', 0)

                            # 计算涨跌
                            change = current_price - last_price if last_price > 0 else 0
                            change_pct = (change / last_price * 100) if last_price > 0 else 0

                            formatted_data = {
                                'symbol': symbol,
                                'price': round(float(current_price), 2),
                                'change': round(float(change), 2),
                                'change_pct': round(float(change_pct), 2),
                                'volume': int(stock_data.get('volume', 0)),
                                'turnover': round(float(stock_data.get('money', 0)), 2),
                                'high': round(float(stock_data.get('high', current_price)), 2),
                                'low': round(float(stock_data.get('low', current_price)), 2),
                                'open': round(float(stock_data.get('open', current_price)), 2),
                                'bid_price': round(float(stock_data.get('bid1', current_price)), 2),
                                'ask_price': round(float(stock_data.get('ask1', current_price)), 2),
                                'bid_volume': int(stock_data.get('bid1_size', 0)),
                                'ask_volume': int(stock_data.get('ask1_size', 0)),
                                'timestamp': datetime.utcnow().isoformat(),
                            }

                            price_data[symbol] = formatted_data
                            self.last_prices[symbol] = formatted_data
                        else:
                            logger.warning(f"未获取到股票 {symbol} 的实时数据")
                else:
                    logger.warning("JQData返回数据格式异常")

        except Exception as e:
            logger.error(f"获取实时价格数据失败: {e}")
            # 如果JQData获取失败，返回空数据而不是模拟数据

        return price_data
    
    async def _get_market_status(self) -> str:
        """获取市场状态"""
        now = datetime.now()
        hour = now.hour
        minute = now.minute
        weekday = now.weekday()
        
        # 周末
        if weekday >= 5:
            return 'closed'
        
        # 交易时间判断（简化版）
        morning_start = 9 * 60 + 30  # 9:30
        morning_end = 11 * 60 + 30   # 11:30
        afternoon_start = 13 * 60    # 13:00
        afternoon_end = 15 * 60      # 15:00
        
        current_time = hour * 60 + minute
        
        if (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end):
            return 'trading'
        elif current_time < morning_start or \
             (morning_end < current_time < afternoon_start):
            return 'pre_market'
        else:
            return 'after_market'
    
    async def push_user_notification(self, user_id: int, notification: Dict):
        """推送用户通知"""
        try:
            await manager.send_user_message(user_id, {
                'type': 'notification',
                'data': notification,
                'timestamp': datetime.utcnow().isoformat(),
            })
            logger.info(f"推送用户通知: {user_id}")
        except Exception as e:
            logger.error(f"推送用户通知失败: {e}")
    
    async def push_price_alert(self, user_id: int, symbol: str, alert_data: Dict):
        """推送价格提醒"""
        try:
            await manager.send_user_message(user_id, {
                'type': 'price_alert',
                'symbol': symbol,
                'data': alert_data,
                'timestamp': datetime.utcnow().isoformat(),
            })
            logger.info(f"推送价格提醒: {user_id} - {symbol}")
        except Exception as e:
            logger.error(f"推送价格提醒失败: {e}")
    
    async def push_portfolio_update(self, user_id: int, portfolio_data: Dict):
        """推送投资组合更新"""
        try:
            await manager.send_user_message(user_id, {
                'type': 'portfolio_update',
                'data': portfolio_data,
                'timestamp': datetime.utcnow().isoformat(),
            })
            logger.info(f"推送投资组合更新: {user_id}")
        except Exception as e:
            logger.error(f"推送投资组合更新失败: {e}")
    
    def get_service_stats(self) -> Dict:
        """获取服务统计"""
        return {
            'is_running': self.is_running,
            'active_tasks': len([t for t in self.push_tasks.values() if not t.done()]),
            'tracked_symbols': len(self.last_prices),
            'connection_stats': manager.get_connection_stats(),
        }


# 全局实时数据服务实例
realtime_service = RealtimeDataService()
