'use client';

/**
 * 图表配置器组件
 * 
 * 可视化图表配置工具
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  ColorPicker,
  Slider,
  Switch,
  Input,
  Button,
  Space,
  Tabs,
  Row,
  Col,
  Typography,
  Divider,
  message
} from 'antd';
import {
  SettingOutlined,
  EyeOutlined,
  SaveOutlined,
  ReloadOutlined,
  CopyOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface ChartConfig {
  // 基础配置
  title: {
    text: string;
    show: boolean;
    fontSize: number;
    color: string;
    position: 'left' | 'center' | 'right';
  };
  // 图例配置
  legend: {
    show: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
    orient: 'horizontal' | 'vertical';
  };
  // 网格配置
  grid: {
    left: number;
    right: number;
    top: number;
    bottom: number;
    containLabel: boolean;
  };
  // 颜色配置
  colors: string[];
  // 动画配置
  animation: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
  // 工具箱配置
  toolbox: {
    show: boolean;
    features: string[];
  };
  // 数据缩放配置
  dataZoom: {
    show: boolean;
    type: 'slider' | 'inside';
    start: number;
    end: number;
  };
}

interface ChartConfiguratorProps {
  initialConfig?: Partial<ChartConfig>;
  onConfigChange?: (config: ChartConfig) => void;
  onSave?: (config: ChartConfig) => void;
  onPreview?: (config: ChartConfig) => void;
}

export const ChartConfigurator: React.FC<ChartConfiguratorProps> = ({
  initialConfig = {},
  onConfigChange,
  onSave,
  onPreview
}) => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<ChartConfig>({
    title: {
      text: '图表标题',
      show: true,
      fontSize: 18,
      color: '#333333',
      position: 'center'
    },
    legend: {
      show: true,
      position: 'top',
      orient: 'horizontal'
    },
    grid: {
      left: 10,
      right: 10,
      top: 15,
      bottom: 10,
      containLabel: true
    },
    colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
    animation: {
      enabled: true,
      duration: 1000,
      easing: 'cubicOut'
    },
    toolbox: {
      show: true,
      features: ['saveAsImage', 'dataView', 'magicType', 'restore']
    },
    dataZoom: {
      show: false,
      type: 'slider',
      start: 0,
      end: 100
    },
    ...initialConfig
  });

  useEffect(() => {
    form.setFieldsValue(config);
  }, [config, form]);

  const handleConfigChange = (changedValues: any, allValues: any) => {
    const newConfig = { ...config, ...allValues };
    setConfig(newConfig);
    onConfigChange?.(newConfig);
  };

  const handleSave = () => {
    onSave?.(config);
    message.success('配置已保存');
  };

  const handlePreview = () => {
    onPreview?.(config);
    message.info('预览已更新');
  };

  const handleReset = () => {
    const defaultConfig: ChartConfig = {
      title: {
        text: '图表标题',
        show: true,
        fontSize: 18,
        color: '#333333',
        position: 'center'
      },
      legend: {
        show: true,
        position: 'top',
        orient: 'horizontal'
      },
      grid: {
        left: 10,
        right: 10,
        top: 15,
        bottom: 10,
        containLabel: true
      },
      colors: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
      animation: {
        enabled: true,
        duration: 1000,
        easing: 'cubicOut'
      },
      toolbox: {
        show: true,
        features: ['saveAsImage', 'dataView', 'magicType', 'restore']
      },
      dataZoom: {
        show: false,
        type: 'slider',
        start: 0,
        end: 100
      }
    };
    setConfig(defaultConfig);
    form.setFieldsValue(defaultConfig);
    message.success('配置已重置');
  };

  const handleCopyConfig = () => {
    navigator.clipboard.writeText(JSON.stringify(config, null, 2));
    message.success('配置已复制到剪贴板');
  };

  return (
    <Card
      title={
        <Space>
          <SettingOutlined />
          图表配置器
        </Space>
      }
      extra={
        <Space>
          <Button icon={<EyeOutlined />} onClick={handlePreview}>
            预览
          </Button>
          <Button icon={<CopyOutlined />} onClick={handleCopyConfig}>
            复制配置
          </Button>
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            重置
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            保存
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleConfigChange}
        initialValues={config}
      >
        <Tabs defaultActiveKey="basic">
          <TabPane tab="基础配置" key="basic">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item label="标题文本" name={['title', 'text']}>
                  <Input placeholder="请输入图表标题" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="显示标题" name={['title', 'show']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="标题字体大小" name={['title', 'fontSize']}>
                  <Slider min={12} max={32} />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="标题颜色" name={['title', 'color']}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="标题位置" name={['title', 'position']}>
                  <Select>
                    <Option value="left">左对齐</Option>
                    <Option value="center">居中</Option>
                    <Option value="right">右对齐</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="图例配置" key="legend">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item label="显示图例" name={['legend', 'show']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="图例位置" name={['legend', 'position']}>
                  <Select>
                    <Option value="top">顶部</Option>
                    <Option value="bottom">底部</Option>
                    <Option value="left">左侧</Option>
                    <Option value="right">右侧</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="图例方向" name={['legend', 'orient']}>
                  <Select>
                    <Option value="horizontal">水平</Option>
                    <Option value="vertical">垂直</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="网格配置" key="grid">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Form.Item label="左边距 (%)" name={['grid', 'left']}>
                  <Slider min={0} max={50} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="右边距 (%)" name={['grid', 'right']}>
                  <Slider min={0} max={50} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="上边距 (%)" name={['grid', 'top']}>
                  <Slider min={0} max={50} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item label="下边距 (%)" name={['grid', 'bottom']}>
                  <Slider min={0} max={50} />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item label="包含标签" name={['grid', 'containLabel']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="颜色配置" key="colors">
            <div className="space-y-4">
              <Text strong>图表配色方案</Text>
              <Row gutter={[16, 16]}>
                {config.colors.map((color, index) => (
                  <Col xs={12} sm={8} md={6} key={index}>
                    <Form.Item label={`颜色 ${index + 1}`} name={['colors', index]}>
                      <ColorPicker showText />
                    </Form.Item>
                  </Col>
                ))}
              </Row>
              <Button
                type="dashed"
                onClick={() => {
                  const newColors = [...config.colors, '#000000'];
                  setConfig({ ...config, colors: newColors });
                  form.setFieldValue(['colors'], newColors);
                }}
              >
                添加颜色
              </Button>
            </div>
          </TabPane>

          <TabPane tab="动画配置" key="animation">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item label="启用动画" name={['animation', 'enabled']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="动画时长 (ms)" name={['animation', 'duration']}>
                  <Slider min={0} max={3000} step={100} />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="缓动函数" name={['animation', 'easing']}>
                  <Select>
                    <Option value="linear">线性</Option>
                    <Option value="quadraticIn">二次缓入</Option>
                    <Option value="quadraticOut">二次缓出</Option>
                    <Option value="cubicIn">三次缓入</Option>
                    <Option value="cubicOut">三次缓出</Option>
                    <Option value="elasticOut">弹性缓出</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="工具箱" key="toolbox">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item label="显示工具箱" name={['toolbox', 'show']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={16}>
                <Form.Item label="工具箱功能" name={['toolbox', 'features']}>
                  <Select mode="multiple" placeholder="选择工具箱功能">
                    <Option value="saveAsImage">保存为图片</Option>
                    <Option value="dataView">数据视图</Option>
                    <Option value="magicType">切换图表类型</Option>
                    <Option value="restore">还原</Option>
                    <Option value="dataZoom">数据区域缩放</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="数据缩放" key="dataZoom">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Form.Item label="启用数据缩放" name={['dataZoom', 'show']} valuePropName="checked">
                  <Switch />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="缩放类型" name={['dataZoom', 'type']}>
                  <Select>
                    <Option value="slider">滑动条</Option>
                    <Option value="inside">内置</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="起始位置 (%)" name={['dataZoom', 'start']}>
                  <Slider min={0} max={100} />
                </Form.Item>
              </Col>
              <Col xs={24} md={8}>
                <Form.Item label="结束位置 (%)" name={['dataZoom', 'end']}>
                  <Slider min={0} max={100} />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Form>
    </Card>
  );
};
