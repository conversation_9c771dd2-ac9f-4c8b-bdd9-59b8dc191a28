'use client';

import React from 'react';
import { Card, Row, Col, Statistic, Empty, Button, Typography } from 'antd';
import {
  BarChartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
  PercentageOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function PerformanceContent() {
  const router = useRouter();

  const performanceStats = [
    {
      title: '总收益率',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#52c41a' },
      prefix: <RiseOutlined />
    },
    {
      title: '年化收益率',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#52c41a' },
      prefix: <PercentageOutlined />
    },
    {
      title: '最大回撤',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#f5222d' },
      prefix: <FallOutlined />
    },
    {
      title: '夏普比率',
      value: 0,
      precision: 3,
      valueStyle: { color: '#1890ff' },
      prefix: <BarChartOutlined />
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <BarChartOutlined className="mr-3" />
          业绩分析
        </Title>
        <Text type="secondary" className="text-lg">
          分析投资组合的历史表现和风险指标
        </Text>
      </div>

      {/* 业绩指标 */}
      <Row gutter={[16, 16]} className="mb-8">
        {performanceStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                suffix={stat.suffix}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 收益曲线 */}
      <Card title="收益曲线" className="mb-6">
        <Empty 
          description="暂无收益数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary">
            开始记录交易
          </Button>
        </Empty>
      </Card>

      {/* 风险分析 */}
      <Card title="风险分析" className="mb-6">
        <Empty 
          description="暂无风险分析数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary"
            onClick={() => router.push('/dashboard/portfolio/positions')}
          >
            添加持仓数据
          </Button>
        </Empty>
      </Card>

      {/* 基准对比 */}
      <Card title="基准对比">
        <Empty 
          description="暂无基准对比数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary">
            设置基准指数
          </Button>
        </Empty>
      </Card>
    </div>
  );
}

export default function PerformancePage() {
  return (
    <ClientAuthWrapper>
      <PerformanceContent />
    </ClientAuthWrapper>
  );
}
