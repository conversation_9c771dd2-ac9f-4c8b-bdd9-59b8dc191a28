'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Typography,
  Select,
  Space,
  Table,
  Progress,
  Spin,
  DatePicker,
  Tabs
} from 'antd';
import {
  BarChartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
  PercentageOutlined,
  LineChartOutlined,
  CalendarOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import * as echarts from 'echarts';
import { motion } from 'framer-motion';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

function PerformanceContent() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('1M');
  const [activeTab, setActiveTab] = useState('overview');

  // 模拟绩效数据
  const [performanceData, setPerformanceData] = useState({
    totalReturn: 25.68,
    annualizedReturn: 18.45,
    maxDrawdown: -8.32,
    sharpeRatio: 1.85,
    volatility: 12.34,
    winRate: 68.5,
    profitLossRatio: 2.1,
    totalTrades: 156
  });

  const performanceStats = [
    {
      title: '总收益率',
      value: performanceData.totalReturn,
      precision: 2,
      suffix: '%',
      valueStyle: { color: performanceData.totalReturn >= 0 ? '#52c41a' : '#f5222d' },
      prefix: performanceData.totalReturn >= 0 ? <RiseOutlined /> : <FallOutlined />
    },
    {
      title: '年化收益率',
      value: performanceData.annualizedReturn,
      precision: 2,
      suffix: '%',
      valueStyle: { color: performanceData.annualizedReturn >= 0 ? '#52c41a' : '#f5222d' },
      prefix: <PercentageOutlined />
    },
    {
      title: '最大回撤',
      value: Math.abs(performanceData.maxDrawdown),
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#f5222d' },
      prefix: <FallOutlined />
    },
    {
      title: '夏普比率',
      value: performanceData.sharpeRatio,
      precision: 3,
      valueStyle: { color: '#1890ff' },
      prefix: <BarChartOutlined />
    },
    {
      title: '波动率',
      value: performanceData.volatility,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#722ed1' },
      prefix: <LineChartOutlined />
    },
    {
      title: '胜率',
      value: performanceData.winRate,
      precision: 1,
      suffix: '%',
      valueStyle: { color: '#13c2c2' },
      prefix: <RiseOutlined />
    }
  ];

  // 月度收益数据
  const monthlyReturns = [
    { month: '2024-01', return: 3.2, benchmark: 1.8 },
    { month: '2024-02', return: -1.5, benchmark: -0.8 },
    { month: '2024-03', return: 4.8, benchmark: 2.1 },
    { month: '2024-04', return: 2.1, benchmark: 1.5 },
    { month: '2024-05', return: -2.3, benchmark: -1.2 },
    { month: '2024-06', return: 5.6, benchmark: 3.2 },
    { month: '2024-07', return: 1.8, benchmark: 0.9 },
    { month: '2024-08', return: 3.4, benchmark: 2.0 }
  ];

  // 风险指标数据
  const riskMetrics = [
    { key: '1', metric: 'VaR (95%)', value: '-2.45%', description: '95%置信度下的最大损失' },
    { key: '2', metric: 'CVaR (95%)', value: '-3.78%', description: '条件风险价值' },
    { key: '3', metric: 'Beta系数', value: '0.85', description: '相对市场的系统性风险' },
    { key: '4', metric: '信息比率', value: '1.23', description: '超额收益与跟踪误差的比值' },
    { key: '5', metric: '卡尔马比率', value: '2.21', description: '年化收益率与最大回撤的比值' },
    { key: '6', metric: '索提诺比率', value: '1.67', description: '考虑下行风险的收益风险比' }
  ];

  useEffect(() => {
    // 模拟数据加载
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, [timeRange]);

  const monthlyColumns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month'
    },
    {
      title: '组合收益',
      dataIndex: 'return',
      key: 'return',
      render: (value: number) => (
        <Text style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      )
    },
    {
      title: '基准收益',
      dataIndex: 'benchmark',
      key: 'benchmark',
      render: (value: number) => (
        <Text style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {value >= 0 ? '+' : ''}{value.toFixed(2)}%
        </Text>
      )
    },
    {
      title: '超额收益',
      key: 'excess',
      render: (record: any) => {
        const excess = record.return - record.benchmark;
        return (
          <Text style={{ color: excess >= 0 ? '#52c41a' : '#f5222d' }}>
            {excess >= 0 ? '+' : ''}{excess.toFixed(2)}%
          </Text>
        );
      }
    }
  ];

  const riskColumns = [
    {
      title: '风险指标',
      dataIndex: 'metric',
      key: 'metric',
      width: 120
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      render: (value: string) => <Text strong>{value}</Text>
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description'
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <Title level={2} className="!mb-2">投资组合绩效分析</Title>
          <Text type="secondary">分析投资组合的历史表现和风险指标</Text>
        </div>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
            options={[
              { label: '近1月', value: '1M' },
              { label: '近3月', value: '3M' },
              { label: '近6月', value: '6M' },
              { label: '近1年', value: '1Y' },
              { label: '全部', value: 'ALL' }
            ]}
          />
          <RangePicker />
          <Button icon={<DownloadOutlined />}>导出报告</Button>
        </Space>
      </motion.div>

      {/* 核心绩效指标 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Row gutter={[16, 16]}>
          {performanceStats.map((stat, index) => (
            <Col xs={24} sm={12} lg={4} key={index}>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  precision={stat.precision}
                  suffix={stat.suffix}
                  valueStyle={stat.valueStyle}
                  prefix={stat.prefix}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </motion.div>

      {/* 详细分析标签页 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="收益分析" key="returns">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Title level={4}>月度收益对比</Title>
                  <Space>
                    <Text type="secondary">组合 vs 基准</Text>
                  </Space>
                </div>

                <Table
                  dataSource={monthlyReturns}
                  columns={monthlyColumns}
                  pagination={false}
                  size="middle"
                />

                <Row gutter={16} className="mt-6">
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="累计超额收益"
                        value={8.34}
                        precision={2}
                        suffix="%"
                        valueStyle={{ color: '#52c41a' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="月胜率"
                        value={75}
                        precision={0}
                        suffix="%"
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card size="small">
                      <Statistic
                        title="信息比率"
                        value={1.23}
                        precision={2}
                        valueStyle={{ color: '#722ed1' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </div>
            </TabPane>

            <TabPane tab="风险分析" key="risk">
              <div className="space-y-4">
                <Title level={4}>风险指标详情</Title>

                <Table
                  dataSource={riskMetrics}
                  columns={riskColumns}
                  pagination={false}
                  size="middle"
                />

                <Row gutter={16} className="mt-6">
                  <Col span={12}>
                    <Card title="风险等级评估" size="small">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <Text>市场风险</Text>
                          <div className="flex items-center space-x-2">
                            <Progress percent={65} size="small" status="active" />
                            <Text>中等</Text>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <Text>流动性风险</Text>
                          <div className="flex items-center space-x-2">
                            <Progress percent={35} size="small" status="success" />
                            <Text>较低</Text>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <Text>集中度风险</Text>
                          <div className="flex items-center space-x-2">
                            <Progress percent={45} size="small" status="success" />
                            <Text>较低</Text>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="风险调整收益" size="small">
                      <div className="space-y-3">
                        <Statistic
                          title="风险调整后收益率"
                          value={15.23}
                          precision={2}
                          suffix="%"
                          valueStyle={{ color: '#52c41a' }}
                        />
                        <Text type="secondary">
                          考虑风险因素后的实际收益表现，高于市场平均水平
                        </Text>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>
            </TabPane>
          </Tabs>
        </Card>
      </motion.div>
    </div>
  );
}

export default function PerformancePage() {
  return (
    <ClientAuthWrapper>
      <PerformanceContent />
    </ClientAuthWrapper>
  );
}
