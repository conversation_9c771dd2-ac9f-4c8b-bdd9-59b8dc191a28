"""
回测相关API端点

提供策略回测功能的API接口
"""

from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy import select, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.backtest import BacktestTask, BacktestResult, Strategy
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.schemas.backtest import (
    BacktestTaskCreate,
    BacktestTaskResponse,
    BacktestResultResponse,
    StrategyCreate,
    StrategyResponse,
    BacktestTaskUpdate,
)
from app.services.backtest_service import backtest_service

router = APIRouter()


# =============================================================================
# 策略管理
# =============================================================================

@router.post("/strategies", response_model=BaseResponse[StrategyResponse])
async def create_strategy(
    strategy_data: StrategyCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建策略"""
    try:
        strategy = Strategy(
            user_id=current_user.id,
            name=strategy_data.name,
            description=strategy_data.description,
            code=strategy_data.code,
            config=strategy_data.config,
            nodes=strategy_data.nodes,
            edges=strategy_data.edges,
            status=strategy_data.status or "draft",
        )
        
        db.add(strategy)
        await db.commit()
        await db.refresh(strategy)
        
        return BaseResponse(
            code=200,
            message="策略创建成功",
            data=StrategyResponse.from_orm(strategy)
        )
        
    except Exception as e:
        logger.error(f"创建策略失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建策略失败"
        )


@router.get("/strategies", response_model=BaseResponse[PaginatedResponse[StrategyResponse]])
async def get_strategies(
    page: int = 1,
    page_size: int = 20,
    status_filter: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取策略列表"""
    try:
        # 构建查询条件
        conditions = [Strategy.user_id == current_user.id]
        if status_filter:
            conditions.append(Strategy.status == status_filter)
        
        # 查询总数
        count_query = select(func.count(Strategy.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(Strategy)
            .where(and_(*conditions))
            .order_by(desc(Strategy.updated_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        strategies = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return BaseResponse(
            code=200,
            message="获取策略列表成功",
            data=PaginatedResponse(
                items=[StrategyResponse.from_orm(s) for s in strategies],
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取策略列表失败"
        )


@router.get("/strategies/{strategy_id}", response_model=BaseResponse[StrategyResponse])
async def get_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取策略详情"""
    try:
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="策略不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取策略详情成功",
            data=StrategyResponse.from_orm(strategy)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取策略详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取策略详情失败"
        )


# =============================================================================
# 回测任务管理
# =============================================================================

@router.post("/tasks", response_model=BaseResponse[BacktestTaskResponse])
async def create_backtest_task(
    task_data: BacktestTaskCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建回测任务"""
    try:
        # 验证策略是否存在且属于当前用户
        strategy_result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == task_data.strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = strategy_result.scalar_one_or_none()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="策略不存在"
            )
        
        # 创建回测任务
        task = BacktestTask(
            user_id=current_user.id,
            strategy_id=task_data.strategy_id,
            name=task_data.name,
            description=task_data.description,
            start_date=task_data.start_date,
            end_date=task_data.end_date,
            initial_capital=task_data.initial_capital,
            benchmark=task_data.benchmark or "000300.XSHG",
            config=task_data.config,
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        # 启动回测任务
        success = await backtest_service.start_backtest(task.id, db)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="回测任务启动失败"
            )
        
        return BaseResponse(
            code=200,
            message="回测任务创建成功",
            data=BacktestTaskResponse.from_orm(task)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建回测任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建回测任务失败"
        )


@router.get("/tasks", response_model=BaseResponse[PaginatedResponse[BacktestTaskResponse]])
async def get_backtest_tasks(
    page: int = 1,
    page_size: int = 20,
    status_filter: Optional[str] = None,
    strategy_id: Optional[int] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取回测任务列表"""
    try:
        # 构建查询条件
        conditions = [BacktestTask.user_id == current_user.id]
        if status_filter:
            conditions.append(BacktestTask.status == status_filter)
        if strategy_id:
            conditions.append(BacktestTask.strategy_id == strategy_id)
        
        # 查询总数
        from sqlalchemy import func
        count_query = select(func.count(BacktestTask.id)).where(and_(*conditions))
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(BacktestTask)
            .where(and_(*conditions))
            .order_by(desc(BacktestTask.created_at))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return BaseResponse(
            code=200,
            message="获取回测任务列表成功",
            data=PaginatedResponse(
                items=[BacktestTaskResponse.from_orm(t) for t in tasks],
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取回测任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取回测任务列表失败"
        )


@router.get("/tasks/{task_id}", response_model=BaseResponse[BacktestTaskResponse])
async def get_backtest_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取回测任务详情"""
    try:
        result = await db.execute(
            select(BacktestTask).where(
                and_(
                    BacktestTask.id == task_id,
                    BacktestTask.user_id == current_user.id
                )
            )
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回测任务不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取回测任务详情成功",
            data=BacktestTaskResponse.from_orm(task)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取回测任务详情失败"
        )


@router.post("/tasks/{task_id}/cancel", response_model=BaseResponse[dict])
async def cancel_backtest_task(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """取消回测任务"""
    try:
        # 验证任务存在且属于当前用户
        result = await db.execute(
            select(BacktestTask).where(
                and_(
                    BacktestTask.id == task_id,
                    BacktestTask.user_id == current_user.id
                )
            )
        )
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回测任务不存在"
            )
        
        if task.status not in ["pending", "running"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务无法取消"
            )
        
        # 取消任务
        success = backtest_service.cancel_backtest(task_id)
        
        if success:
            # 更新数据库状态
            from sqlalchemy import update
            await db.execute(
                update(BacktestTask)
                .where(BacktestTask.id == task_id)
                .values(
                    status="cancelled",
                    completed_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            )
            await db.commit()
        
        return BaseResponse(
            code=200,
            message="回测任务已取消",
            data={"cancelled": success}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消回测任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消回测任务失败"
        )


# =============================================================================
# 回测结果
# =============================================================================

@router.get("/results/{task_id}", response_model=BaseResponse[BacktestResultResponse])
async def get_backtest_result(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取回测结果"""
    try:
        # 验证任务存在且属于当前用户
        task_result = await db.execute(
            select(BacktestTask).where(
                and_(
                    BacktestTask.id == task_id,
                    BacktestTask.user_id == current_user.id
                )
            )
        )
        task = task_result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回测任务不存在"
            )
        
        # 获取回测结果
        result_query = await db.execute(
            select(BacktestResult).where(BacktestResult.task_id == task_id)
        )
        result = result_query.scalar_one_or_none()
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回测结果不存在"
            )
        
        return BaseResponse(
            code=200,
            message="获取回测结果成功",
            data=BacktestResultResponse.from_orm(result)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取回测结果失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取回测结果失败"
        )
