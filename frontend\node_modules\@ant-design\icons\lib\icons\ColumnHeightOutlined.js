"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ColumnHeightOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ColumnHeightOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ColumnHeightOutlined = function ColumnHeightOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ColumnHeightOutlined.default
  }));
};

/**![column-height](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg0MCA4MzZIMTg0Yy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDY1NmM0LjQgMCA4LTMuNiA4LTh2LTYwYzAtNC40LTMuNi04LTgtOHptMC03MjRIMTg0Yy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDY1NmM0LjQgMCA4LTMuNiA4LTh2LTYwYzAtNC40LTMuNi04LTgtOHpNNjEwLjggMzc4YzYgMCA5LjQtNyA1LjctMTEuN0w1MTUuNyAyMzguN2E3LjE0IDcuMTQgMCAwMC0xMS4zIDBMNDAzLjYgMzY2LjNhNy4yMyA3LjIzIDAgMDA1LjcgMTEuN0g0NzZ2MjY4aC02Mi44Yy02IDAtOS40IDctNS43IDExLjdsMTAwLjggMTI3LjVjMi45IDMuNyA4LjUgMy43IDExLjMgMGwxMDAuOC0xMjcuNWMzLjctNC43LjQtMTEuNy01LjctMTEuN0g1NDhWMzc4aDYyLjh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ColumnHeightOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ColumnHeightOutlined';
}
var _default = exports.default = RefIcon;