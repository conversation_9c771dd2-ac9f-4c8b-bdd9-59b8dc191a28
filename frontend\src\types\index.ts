/**
 * 全局类型定义
 * 
 * 包含API响应、用户信息、市场数据等核心类型定义
 */

// =============================================================================
// 基础类型
// =============================================================================

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  children?: SelectOption[];
}

// =============================================================================
// 用户相关类型
// =============================================================================

export interface User {
  id: number;
  email: string;
  username: string;
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
  subscriptionType: 'free' | 'basic' | 'premium' | 'enterprise';
  subscriptionExpiresAt?: string;
  apiQuotaDaily: number;
  apiQuotaUsedToday: number;
  lastQuotaReset: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
  subscriptionType: string;
  subscriptionExpiresAt?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
  phone?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
}

// =============================================================================
// JQData配置类型
// =============================================================================

export interface JQDataConfig {
  id: number;
  userId: number;
  username: string;
  isActive: boolean;
  quotaTotal: number;
  quotaUsed: number;
  quotaRemaining: number;
  quotaResetDate?: string;
  lastUsedAt?: string;
  totalApiCalls: number;
  lastAuthSuccess?: string;
  lastAuthError?: string;
  authFailureCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface JQDataConfigRequest {
  username: string;
  password: string;
}

export interface JQDataQuotaInfo {
  configured: boolean;
  quotaTotal?: number;
  quotaUsed?: number;
  quotaRemaining?: number;
  quotaResetDate?: string;
  lastUsedAt?: string;
  totalApiCalls?: number;
}

// =============================================================================
// 市场数据类型
// =============================================================================

export interface Stock {
  id: number;
  symbol: string;
  name: string;
  displayName?: string;
  market: 'XSHE' | 'XSHG' | 'XHKG' | 'XNAS' | 'XNYS';
  exchange: string;
  currency: string;
  industry?: string;
  sector?: string;
  concept?: string[];
  listDate?: string;
  delistDate?: string;
  isActive: boolean;
  isSt: boolean;
  totalShare?: number;
  floatShare?: number;
  marketCap?: number;
  lastUpdate?: string;
  dataSource: string;
}

export interface PriceData {
  symbol: string;
  datetime: string;
  open?: number;
  high?: number;
  low?: number;
  close?: number;
  volume?: number;
  turnover?: number;
  adjClose?: number;
  changePct?: number;
  turnoverRate?: number;
  isTradingDay?: boolean;
  isSuspended?: boolean;
}

export interface OHLCV {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface TickData {
  symbol: string;
  datetime: string;
  currentPrice?: number;
  volume?: number;
  turnover?: number;
  bidPrice1?: number;
  bidVolume1?: number;
  askPrice1?: number;
  askVolume1?: number;
}

// =============================================================================
// 技术指标类型
// =============================================================================

export interface TechnicalIndicator {
  name: string;
  displayName: string;
  type: 'trend' | 'momentum' | 'volume' | 'volatility';
  parameters: Record<string, number>;
  data: number[];
  timestamps: number[];
}

export interface IndicatorConfig {
  name: string;
  parameters: Record<string, number>;
  visible: boolean;
  color?: string;
  lineWidth?: number;
  style?: 'solid' | 'dashed' | 'dotted';
}

// =============================================================================
// 策略相关类型
// =============================================================================

export interface Strategy {
  id: number;
  userId: number;
  name: string;
  description?: string;
  type: 'custom' | 'template';
  status: 'draft' | 'active' | 'paused' | 'archived';
  config: StrategyConfig;
  performance?: StrategyPerformance;
  createdAt: string;
  updatedAt: string;
}

export interface StrategyConfig {
  nodes: StrategyNode[];
  connections: StrategyConnection[];
  parameters: Record<string, any>;
}

export interface StrategyNode {
  id: string;
  type: 'data' | 'indicator' | 'signal' | 'risk' | 'output';
  name: string;
  config: Record<string, any>;
  position: { x: number; y: number };
  inputs: string[];
  outputs: string[];
}

export interface StrategyConnection {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

export interface StrategyPerformance {
  totalReturn: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  profitFactor: number;
  totalTrades: number;
  avgTradeDuration: number;
}

// =============================================================================
// 回测相关类型
// =============================================================================

export interface BacktestConfig {
  strategyId: number;
  startDate: string;
  endDate: string;
  initialCapital: number;
  benchmark?: string;
  commission: number;
  slippage: number;
  parameters?: Record<string, any>;
}

export interface BacktestResult {
  id: number;
  strategyId: number;
  config: BacktestConfig;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  startTime: string;
  endTime?: string;
  performance?: StrategyPerformance;
  equityCurve?: Array<{ date: string; value: number }>;
  trades?: Trade[];
  drawdown?: Array<{ date: string; value: number }>;
  error?: string;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: number;
  price: number;
  timestamp: string;
  commission: number;
  pnl?: number;
  pnlPct?: number;
}

// =============================================================================
// 图表相关类型
// =============================================================================

export interface ChartConfig {
  type: 'candlestick' | 'line' | 'bar' | 'area';
  theme: 'light' | 'dark';
  height: number;
  showVolume: boolean;
  showGrid: boolean;
  showCrosshair: boolean;
  showLegend: boolean;
  indicators: IndicatorConfig[];
  timeframe: '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d' | '1w' | '1M';
}

export interface ChartData {
  symbol: string;
  timeframe: string;
  data: OHLCV[];
  indicators?: Record<string, TechnicalIndicator>;
  volume?: Array<{ timestamp: number; value: number }>;
}

// =============================================================================
// WebSocket相关类型
// =============================================================================

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface PriceUpdateMessage {
  type: 'price_update';
  data: {
    symbol: string;
    price: number;
    change: number;
    changePct: number;
    volume: number;
    timestamp: number;
  };
}

export interface SystemNotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  actions?: Array<{
    label: string;
    action: string;
    type?: 'primary' | 'default';
  }>;
}

// =============================================================================
// 系统配置类型
// =============================================================================

export interface SystemConfig {
  appName: string;
  version: string;
  environment: string;
  features: {
    socialTrading: boolean;
    mlPredictions: boolean;
    optionsPricing: boolean;
    newsAnalysis: boolean;
    portfolioOptimization: boolean;
  };
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  timezone: string;
  dateFormat: string;
  numberFormat: string;
  defaultChartType: string;
  defaultTimeframe: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

// =============================================================================
// 错误类型
// =============================================================================

export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp?: number;
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// =============================================================================
// 工具类型
// =============================================================================

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type SortOrder = 'asc' | 'desc';

export interface SortConfig {
  field: string;
  order: SortOrder;
}

export interface FilterConfig {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'like';
  value: any;
}

export type ThemeMode = 'light' | 'dark' | 'auto';

export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// =============================================================================
// 导出所有类型
// =============================================================================

export * from './api';
export * from './chart';
export * from './strategy';
