"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sankey = exports.justify = exports.right = exports.left = exports.center = void 0;
/**
 * Sankey layout, fork from https://github.com/d3/d3-sankey/tree/master/src (todo, optimize algorithm)
 *
 * Major changes:
 *
 * 1. modify some set and map traverse
 * 2. fix some error caused by [empty] array
 * 3. support `nodeDepth` through align method
 */
const sankey_1 = require("./sankey");
Object.defineProperty(exports, "sankey", { enumerable: true, get: function () { return sankey_1.Sankey; } });
var align_1 = require("./align");
Object.defineProperty(exports, "center", { enumerable: true, get: function () { return align_1.center; } });
Object.defineProperty(exports, "left", { enumerable: true, get: function () { return align_1.left; } });
Object.defineProperty(exports, "right", { enumerable: true, get: function () { return align_1.right; } });
Object.defineProperty(exports, "justify", { enumerable: true, get: function () { return align_1.justify; } });
//# sourceMappingURL=index.js.map