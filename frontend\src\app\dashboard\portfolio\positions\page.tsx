'use client';

import React from 'react';
import { Card, Table, Tag, Button, Space, Empty, Typography } from 'antd';
import { 
  DatabaseOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function PositionsContent() {
  const router = useRouter();

  // 示例持仓数据
  const positions = [
    {
      key: '1',
      symbol: '000001.SZ',
      name: '平安银行',
      quantity: 1000,
      avgPrice: 12.50,
      currentPrice: 13.20,
      marketValue: 13200,
      profit: 700,
      profitRate: 5.6,
      status: 'holding'
    },
    {
      key: '2',
      symbol: '000002.SZ',
      name: '万科A',
      quantity: 500,
      avgPrice: 18.30,
      currentPrice: 17.80,
      marketValue: 8900,
      profit: -250,
      profitRate: -2.7,
      status: 'holding'
    }
  ];

  const columns = [
    {
      title: '股票代码',
      dataIndex: 'symbol',
      key: 'symbol',
    },
    {
      title: '股票名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '持仓数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => `${quantity.toLocaleString()}股`,
    },
    {
      title: '成本价',
      dataIndex: 'avgPrice',
      key: 'avgPrice',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '现价',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
    {
      title: '市值',
      dataIndex: 'marketValue',
      key: 'marketValue',
      render: (value: number) => `¥${value.toLocaleString()}`,
    },
    {
      title: '盈亏',
      dataIndex: 'profit',
      key: 'profit',
      render: (profit: number, record: any) => (
        <span className={profit >= 0 ? 'text-green-600' : 'text-red-600'}>
          ¥{profit.toFixed(2)} ({record.profitRate > 0 ? '+' : ''}{record.profitRate.toFixed(2)}%)
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'holding' ? 'green' : 'red'}>
          {status === 'holding' ? '持有' : '已清仓'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EditOutlined />} size="small">
            编辑
          </Button>
          <Button type="link" icon={<DeleteOutlined />} size="small" danger>
            清仓
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <DatabaseOutlined className="mr-3" />
          持仓管理
        </Title>
        <Text type="secondary" className="text-lg">
          管理您的股票持仓和交易记录
        </Text>
      </div>

      {/* 持仓列表 */}
      <Card 
        title="当前持仓" 
        extra={
          <Button type="primary" icon={<PlusOutlined />}>
            添加持仓
          </Button>
        }
      >
        {positions.length > 0 ? (
          <Table 
            columns={columns} 
            dataSource={positions} 
            pagination={false}
            scroll={{ x: 800 }}
          />
        ) : (
          <Empty 
            description="暂无持仓数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" icon={<PlusOutlined />}>
              添加第一个持仓
            </Button>
          </Empty>
        )}
      </Card>
    </div>
  );
}

export default function PositionsPage() {
  return (
    <ClientAuthWrapper>
      <PositionsContent />
    </ClientAuthWrapper>
  );
}
