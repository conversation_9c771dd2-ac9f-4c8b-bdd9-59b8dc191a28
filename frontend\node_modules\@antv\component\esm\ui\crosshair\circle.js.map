{"version": 3, "file": "circle.js", "sourceRoot": "", "sources": ["../../../src/ui/crosshair/circle.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AACvC,OAAO,EAAE,8BAA8B,EAAE,MAAM,YAAY,CAAC;AAK5D;IAAqC,mCAAkD;IAWrF,yBAAY,OAA+B;QACzC,OAAA,MAAK,YAAC,UAAU,CAAC,EAAE,EAAE,eAAe,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,SAAC;IACjE,CAAC;IAND,sBAAc,0CAAa;aAA3B;YACE,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACjC,CAAC;;;OAAA;IAMM,gCAAM,GAAb,UAAc,GAA8B;QAC1C,gBAAK,CAAC,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAEM,oCAAU,GAAjB,UAAkB,EAAa;YAAb,KAAA,aAAa,EAAZ,CAAC,QAAA,EAAE,CAAC,QAAA;QACrB,gBAAK,CAAC,UAAU,YAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,IAAA,KAAA,OAAW,IAAI,CAAC,YAAY,IAAA,EAA3B,EAAE,QAAA,EAAE,EAAE,QAAqB,CAAC;QAEjC,IAAA,KAAA,OACE,IAAI,CAAC,UAAU,WADD,EAAP,EAAE,QAAA,EAAE,EAAE,QAAC,CACE;QAEpB,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAA,CAAC,SAAA,CAAC,EAAE,GAAG,EAAE,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,EAAE,GAAG,EAAE,CAAC,EAAI,CAAC,CAAA,CAAC,EAAI,GAAG,CAAA,CAAQ,CAAC;QACpF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IACxC,CAAC;IAES,sCAAY,GAAtB;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtB,CAAC;IAEO,0CAAgB,GAAxB,UAAyB,MAAe;QAChC,IAAA,KAGF,IAAI,CAAC,UAAU,EAFjB,KAAA,oBAAc,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA,EACb,aAAa,mBACI,CAAC;QACpB,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,aAAa,CAAU,CAAC;IACxD,CAAC;IAvCa,mBAAG,GAAG,kBAAkB,CAAC;IAEtB,8BAAc,GAAG;QAChC,KAAK,EAAE,8BAA8B;KACtC,CAAC;IAoCJ,sBAAC;CAAA,AAzCD,CAAqC,aAAa,GAyCjD;SAzCY,eAAe"}