{"version": 3, "file": "component.js", "sourceRoot": "", "sources": ["../../src/core/component.ts"], "names": [], "mappings": ";;;;AAAA,oCAAiD;AAEjD,gCAAuE;AAGvE,SAAS,eAAe;IACtB,IAAA,iBAAU,EAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC;AAC5D,CAAC;AAED;IAAuE,qCAA0B;IAgB/F,mBAAY,OAAqC,EAAE,iBAAkC;QAAlC,kCAAA,EAAA,sBAAkC;QACnF,YAAA,MAAK,YAAC,IAAA,iBAAU,EAAC,EAAE,EAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,EAAE,OAAO,CAAC,CAAC,SAAC;QAPxD,iBAAW,GAAG,KAAK,CAAC;QAQzB,KAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;;IAC3C,CAAC;IAdD,sBAAc,qCAAc;aAA5B;YACE,IAAI,CAAC,IAAI,CAAC,UAAU;gBAAE,IAAI,CAAC,UAAU,GAAG,IAAA,2BAAoB,EAAC,IAAI,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;;;OAAA;IAID,sBAAW,qCAAc;aAAzB;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAOD,qCAAiB,GAAjB;QACE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAyB,EAAE,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,wCAAoB,GAApB;;QACE,MAAA,IAAI,CAAC,UAAU,0CAAE,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,4CAAwB,GAAxB,UAA8C,IAAS;QACrD,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YAC1B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEM,0BAAM,GAAb,UAAc,IAAiB,EAAE,OAA0B;;QACzD,IAAI,CAAC,IAAI,CAAC,IAAA,iBAAU,EAAC,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;QACvD,OAAO,MAAA,IAAI,CAAC,MAAM,qDAAG,IAAI,CAAC,UAAyB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAEM,yBAAK,GAAZ;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAQM,8BAAU,GAAjB,UAAkB,UAAa,EAAE,SAAgB,IAAS,CAAC;IAEjD,oCAAgB,GAA1B,UAA2B,UAAa;QAC9B,IAAA,CAAC,GAAgF,UAAU,EAA1F,EAAE,CAAC,GAA6E,UAAU,EAAvF,EAAE,SAAS,GAAkE,UAAU,UAA5E,EAAE,eAAe,GAAiD,UAAU,gBAA3D,EAAS,MAAM,GAAkC,UAAU,MAA5C,EAAE,SAAS,GAAuB,UAAU,UAAjC,EAAE,MAAM,GAAe,UAAU,OAAzB,EAAK,KAAK,kBAAK,UAAU,EAA7F,0EAAgF,CAAF,CAAgB;QACpG,OAAO,KAAU,CAAC;IACpB,CAAC;IACH,gBAAC;AAAD,CAAC,AA1DD,CAAuE,sBAAa,GA0DnF;AA1DqB,8BAAS"}