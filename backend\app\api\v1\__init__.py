"""
API v1 路由模块

注册所有v1版本的API路由
"""

from fastapi import APIRouter

from app.api.v1 import (
    auth, jqdata, market, backtest, portfolio, websocket,
    risk, reports, indicators, monitoring, ml, deep_learning, multimodal, automl
)

# 创建v1路由器
api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(jqdata.router, prefix="/jqdata", tags=["JQData"])
api_router.include_router(market.router, prefix="/market", tags=["市场数据"])
api_router.include_router(backtest.router, prefix="/backtest", tags=["回测"])
api_router.include_router(portfolio.router, prefix="/portfolio", tags=["投资组合"])
api_router.include_router(risk.router, prefix="/risk", tags=["风险管理"])
api_router.include_router(reports.router, prefix="/reports", tags=["报告生成"])
api_router.include_router(indicators.router, prefix="/indicators", tags=["技术指标"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["系统监控"])
api_router.include_router(ml.router, prefix="/ml", tags=["机器学习"])
api_router.include_router(deep_learning.router, prefix="/deep-learning", tags=["深度学习"])
api_router.include_router(multimodal.router, prefix="/multimodal", tags=["多模态数据"])
api_router.include_router(automl.router, prefix="/automl", tags=["AutoML"])
api_router.include_router(websocket.router, tags=["WebSocket"])
