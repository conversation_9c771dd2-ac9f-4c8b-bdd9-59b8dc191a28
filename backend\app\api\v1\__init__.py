"""
API v1 路由模块

注册所有v1版本的API路由
"""

from fastapi import APIRouter

from app.api.v1 import auth, jqdata, market, backtest, portfolio, websocket

# 创建v1路由器
api_router = APIRouter()

# 注册各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(jqdata.router, prefix="/jqdata", tags=["JQData"])
api_router.include_router(market.router, prefix="/market", tags=["市场数据"])
api_router.include_router(backtest.router, prefix="/backtest", tags=["回测"])
api_router.include_router(portfolio.router, prefix="/portfolio", tags=["投资组合"])
api_router.include_router(websocket.router, tags=["WebSocket"])
