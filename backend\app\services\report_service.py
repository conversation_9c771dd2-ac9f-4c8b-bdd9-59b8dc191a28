"""
报告生成服务

提供PDF报告生成、邮件推送、定时报告等功能
"""

import os
import asyncio
import hashlib
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json

# PDF生成相关
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.graphics.shapes import Drawing
from reportlab.graphics.charts.linecharts import HorizontalLine<PERSON>hart
from reportlab.graphics.charts.piecharts import Pie

# Excel生成相关
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.chart import LineChart, PieChart, Reference

# 邮件发送相关
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger
from app.core.config import settings
from app.models.report import ReportTask, ReportHistory, ReportTemplate
from app.models.user import User


class PDFReportGenerator:
    """PDF报告生成器"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # 居中
        )
        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12
        )
    
    def generate_portfolio_report(
        self, 
        output_path: str, 
        data: Dict[str, Any]
    ) -> bool:
        """生成投资组合报告"""
        try:
            doc = SimpleDocTemplate(output_path, pagesize=A4)
            story = []
            
            # 报告标题
            title = Paragraph("投资组合分析报告", self.title_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # 报告日期
            date_str = datetime.now().strftime("%Y年%m月%d日")
            date_para = Paragraph(f"报告日期: {date_str}", self.styles['Normal'])
            story.append(date_para)
            story.append(Spacer(1, 20))
            
            # 投资组合概览
            story.append(Paragraph("投资组合概览", self.heading_style))
            
            overview_data = data.get('overview', {})
            overview_table_data = [
                ['指标', '数值'],
                ['总资产', f"¥{overview_data.get('total_value', 0):,.2f}"],
                ['总收益', f"¥{overview_data.get('total_return', 0):,.2f}"],
                ['收益率', f"{overview_data.get('total_return_pct', 0):.2f}%"],
                ['持仓数量', f"{overview_data.get('position_count', 0)}只"],
            ]
            
            overview_table = Table(overview_table_data)
            overview_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(overview_table)
            story.append(Spacer(1, 20))
            
            # 持仓明细
            story.append(Paragraph("持仓明细", self.heading_style))
            
            positions = data.get('positions', [])
            if positions:
                position_data = [['股票代码', '股票名称', '数量', '市值', '权重']]
                for pos in positions[:10]:  # 只显示前10只
                    position_data.append([
                        pos.get('symbol', ''),
                        pos.get('name', ''),
                        f"{pos.get('quantity', 0):,}",
                        f"¥{pos.get('market_value', 0):,.2f}",
                        f"{pos.get('weight', 0):.2f}%"
                    ])
                
                position_table = Table(position_data)
                position_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(position_table)
            else:
                story.append(Paragraph("暂无持仓数据", self.styles['Normal']))
            
            story.append(Spacer(1, 20))
            
            # 风险指标
            story.append(Paragraph("风险指标", self.heading_style))
            
            risk_data = data.get('risk_metrics', {})
            risk_table_data = [
                ['风险指标', '数值'],
                ['投资组合波动率', f"{risk_data.get('portfolio_volatility', 0):.2%}"],
                ['1日VaR', f"¥{abs(risk_data.get('var_1day', 0)):,.2f}"],
                ['最大回撤', f"{risk_data.get('max_drawdown', 0):.2%}"],
                ['贝塔系数', f"{risk_data.get('market_beta', 1):.3f}"],
            ]
            
            risk_table = Table(risk_table_data)
            risk_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(risk_table)
            story.append(Spacer(1, 20))
            
            # 免责声明
            disclaimer = Paragraph(
                "免责声明：本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。",
                self.styles['Normal']
            )
            story.append(disclaimer)
            
            # 生成PDF
            doc.build(story)
            return True
            
        except Exception as e:
            logger.error(f"生成PDF报告失败: {e}")
            return False


class ExcelReportGenerator:
    """Excel报告生成器"""
    
    def generate_portfolio_report(
        self, 
        output_path: str, 
        data: Dict[str, Any]
    ) -> bool:
        """生成投资组合Excel报告"""
        try:
            wb = Workbook()
            
            # 概览工作表
            ws_overview = wb.active
            ws_overview.title = "投资组合概览"
            
            # 设置标题
            ws_overview['A1'] = "投资组合分析报告"
            ws_overview['A1'].font = Font(size=16, bold=True)
            ws_overview['A2'] = f"报告日期: {datetime.now().strftime('%Y-%m-%d')}"
            
            # 概览数据
            overview_data = data.get('overview', {})
            ws_overview['A4'] = "指标"
            ws_overview['B4'] = "数值"
            ws_overview['A4'].font = Font(bold=True)
            ws_overview['B4'].font = Font(bold=True)
            
            overview_rows = [
                ("总资产", f"¥{overview_data.get('total_value', 0):,.2f}"),
                ("总收益", f"¥{overview_data.get('total_return', 0):,.2f}"),
                ("收益率", f"{overview_data.get('total_return_pct', 0):.2f}%"),
                ("持仓数量", f"{overview_data.get('position_count', 0)}只"),
            ]
            
            for i, (metric, value) in enumerate(overview_rows, start=5):
                ws_overview[f'A{i}'] = metric
                ws_overview[f'B{i}'] = value
            
            # 持仓明细工作表
            ws_positions = wb.create_sheet("持仓明细")
            
            # 持仓表头
            headers = ["股票代码", "股票名称", "数量", "成本价", "现价", "市值", "盈亏", "权重"]
            for i, header in enumerate(headers, start=1):
                cell = ws_positions.cell(row=1, column=i, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # 持仓数据
            positions = data.get('positions', [])
            for row, pos in enumerate(positions, start=2):
                ws_positions.cell(row=row, column=1, value=pos.get('symbol', ''))
                ws_positions.cell(row=row, column=2, value=pos.get('name', ''))
                ws_positions.cell(row=row, column=3, value=pos.get('quantity', 0))
                ws_positions.cell(row=row, column=4, value=pos.get('avg_cost', 0))
                ws_positions.cell(row=row, column=5, value=pos.get('current_price', 0))
                ws_positions.cell(row=row, column=6, value=pos.get('market_value', 0))
                ws_positions.cell(row=row, column=7, value=pos.get('unrealized_pnl', 0))
                ws_positions.cell(row=row, column=8, value=f"{pos.get('weight', 0):.2f}%")
            
            # 风险指标工作表
            ws_risk = wb.create_sheet("风险指标")
            
            risk_data = data.get('risk_metrics', {})
            ws_risk['A1'] = "风险指标"
            ws_risk['B1'] = "数值"
            ws_risk['A1'].font = Font(bold=True)
            ws_risk['B1'].font = Font(bold=True)
            
            risk_rows = [
                ("投资组合波动率", f"{risk_data.get('portfolio_volatility', 0):.2%}"),
                ("1日VaR", f"¥{abs(risk_data.get('var_1day', 0)):,.2f}"),
                ("最大回撤", f"{risk_data.get('max_drawdown', 0):.2%}"),
                ("贝塔系数", f"{risk_data.get('market_beta', 1):.3f}"),
            ]
            
            for i, (metric, value) in enumerate(risk_rows, start=2):
                ws_risk[f'A{i}'] = metric
                ws_risk[f'B{i}'] = value
            
            # 保存文件
            wb.save(output_path)
            return True
            
        except Exception as e:
            logger.error(f"生成Excel报告失败: {e}")
            return False


class EmailService:
    """邮件发送服务"""
    
    def __init__(self):
        self.smtp_server = getattr(settings, 'SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = getattr(settings, 'SMTP_PORT', 587)
        self.smtp_username = getattr(settings, 'SMTP_USERNAME', '')
        self.smtp_password = getattr(settings, 'SMTP_PASSWORD', '')
        self.from_email = getattr(settings, 'FROM_EMAIL', self.smtp_username)
    
    async def send_report_email(
        self,
        recipients: List[str],
        subject: str,
        body: str,
        attachment_path: Optional[str] = None
    ) -> bool:
        """发送报告邮件"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            # 添加邮件正文
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # 添加附件
            if attachment_path and os.path.exists(attachment_path):
                with open(attachment_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                
                encoders.encode_base64(part)
                
                filename = os.path.basename(attachment_path)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {filename}',
                )
                
                msg.attach(part)
            
            # 发送邮件
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.smtp_username, self.smtp_password)
            text = msg.as_string()
            server.sendmail(self.from_email, recipients, text)
            server.quit()
            
            logger.info(f"邮件发送成功: {recipients}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False


class ReportGenerationService:
    """报告生成服务"""
    
    def __init__(self):
        self.pdf_generator = PDFReportGenerator()
        self.excel_generator = ExcelReportGenerator()
        self.email_service = EmailService()
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
    
    async def generate_report(
        self,
        task_id: int,
        db: AsyncSession
    ) -> bool:
        """生成报告"""
        try:
            # 获取报告任务
            result = await db.execute(
                select(ReportTask).where(ReportTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                logger.error(f"报告任务不存在: {task_id}")
                return False
            
            # 更新任务状态
            task.status = "running"
            task.started_at = datetime.utcnow()
            task.progress = 10
            await db.commit()
            
            # 获取报告数据
            report_data = await self._collect_report_data(task, db)
            task.progress = 50
            await db.commit()
            
            # 生成报告文件
            output_path = await self._generate_report_file(task, report_data)
            if not output_path:
                task.status = "failed"
                task.error_message = "报告文件生成失败"
                await db.commit()
                return False
            
            task.progress = 80
            task.output_path = str(output_path)
            task.output_size = os.path.getsize(output_path)
            await db.commit()
            
            # 发送邮件（如果启用）
            if task.email_enabled and task.email_recipients:
                email_sent = await self.email_service.send_report_email(
                    recipients=task.email_recipients,
                    subject=task.email_subject or f"报告: {task.name}",
                    body=task.email_body or "请查收附件中的报告。",
                    attachment_path=str(output_path)
                )
                
                if email_sent:
                    task.email_sent_at = datetime.utcnow()
            
            # 创建报告历史记录
            await self._create_report_history(task, output_path, db)
            
            # 完成任务
            task.status = "completed"
            task.completed_at = datetime.utcnow()
            task.progress = 100
            await db.commit()
            
            logger.info(f"报告生成完成: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"报告生成失败: {e}")
            
            # 更新任务状态为失败
            if 'task' in locals():
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = datetime.utcnow()
                await db.commit()
            
            return False
    
    async def _collect_report_data(
        self, 
        task: ReportTask, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """收集报告数据"""
        try:
            # 这里应该根据报告类型收集相应的数据
            # 目前返回示例数据
            
            report_data = {
                "overview": {
                    "total_value": 125680.50,
                    "total_return": 15680.50,
                    "total_return_pct": 14.23,
                    "position_count": 8,
                },
                "positions": [],
                "risk_metrics": {
                    "portfolio_volatility": 0.15,
                    "var_1day": -2500.0,
                    "max_drawdown": -0.08,
                    "market_beta": 1.2,
                },
                "performance": {
                    "dates": [],
                    "values": [],
                }
            }
            
            return report_data
            
        except Exception as e:
            logger.error(f"收集报告数据失败: {e}")
            return {}
    
    async def _generate_report_file(
        self, 
        task: ReportTask, 
        data: Dict[str, Any]
    ) -> Optional[Path]:
        """生成报告文件"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{task.name}_{timestamp}"
            
            if task.report_type == "pdf":
                output_path = self.reports_dir / f"{filename}.pdf"
                success = self.pdf_generator.generate_portfolio_report(
                    str(output_path), data
                )
            elif task.report_type == "excel":
                output_path = self.reports_dir / f"{filename}.xlsx"
                success = self.excel_generator.generate_portfolio_report(
                    str(output_path), data
                )
            else:
                logger.error(f"不支持的报告类型: {task.report_type}")
                return None
            
            if success:
                return output_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"生成报告文件失败: {e}")
            return None
    
    async def _create_report_history(
        self, 
        task: ReportTask, 
        output_path: Path, 
        db: AsyncSession
    ):
        """创建报告历史记录"""
        try:
            # 计算文件哈希
            with open(output_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            
            history = ReportHistory(
                task_id=task.id,
                user_id=task.user_id,
                report_name=task.name,
                report_type=task.report_type,
                file_name=output_path.name,
                file_path=str(output_path),
                file_size=output_path.stat().st_size,
                file_hash=file_hash,
                generation_time=int((task.completed_at - task.started_at).total_seconds()) if task.completed_at and task.started_at else 0,
            )
            
            db.add(history)
            await db.commit()
            
        except Exception as e:
            logger.error(f"创建报告历史记录失败: {e}")


# 全局报告生成服务实例
report_generation_service = ReportGenerationService()
