"""
认证API测试脚本

测试用户注册、登录、token管理等认证功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import httpx
import pytest
from fastapi.testclient import TestClient

# 导入应用
from backend.app.main import app

# 创建测试客户端
client = TestClient(app)


class TestAuthAPI:
    """认证API测试类"""
    
    def __init__(self):
        self.test_user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "Test123456",
            "confirm_password": "Test123456",
            "full_name": "测试用户"
        }
        self.access_token = None
        self.refresh_token = None
    
    def test_user_registration(self):
        """测试用户注册"""
        print("🧪 测试用户注册...")
        
        response = client.post("/api/v1/auth/register", json=self.test_user_data)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "注册成功" in data["message"]
        
        print("✅ 用户注册测试通过")
    
    def test_user_login(self):
        """测试用户登录"""
        print("🧪 测试用户登录...")
        
        # 使用OAuth2PasswordRequestForm格式
        login_data = {
            "username": self.test_user_data["email"],  # 使用邮箱作为用户名
            "password": self.test_user_data["password"]
        }
        
        response = client.post(
            "/api/v1/auth/login",
            data=login_data,  # 使用form data而不是json
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "登录成功" in data["message"]
        
        # 保存token用于后续测试
        self.access_token = data["data"]["access_token"]
        self.refresh_token = data["data"]["refresh_token"]
        
        assert self.access_token is not None
        assert self.refresh_token is not None
        
        print("✅ 用户登录测试通过")
    
    def test_get_current_user(self):
        """测试获取当前用户信息"""
        print("🧪 测试获取当前用户信息...")
        
        if not self.access_token:
            print("❌ 需要先登录获取token")
            return
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert data["data"]["email"] == self.test_user_data["email"]
        
        print("✅ 获取当前用户信息测试通过")
    
    def test_token_refresh(self):
        """测试token刷新"""
        print("🧪 测试token刷新...")
        
        if not self.refresh_token:
            print("❌ 需要先登录获取refresh token")
            return
        
        refresh_data = {"refresh_token": self.refresh_token}
        response = client.post("/api/v1/auth/refresh", json=refresh_data)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "access_token" in data["data"]
        
        # 更新access token
        self.access_token = data["data"]["access_token"]
        
        print("✅ token刷新测试通过")
    
    def test_user_logout(self):
        """测试用户登出"""
        print("🧪 测试用户登出...")
        
        if not self.access_token:
            print("❌ 需要先登录获取token")
            return
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        assert "登出成功" in data["message"]
        
        print("✅ 用户登出测试通过")
    
    def test_invalid_login(self):
        """测试无效登录"""
        print("🧪 测试无效登录...")
        
        invalid_data = {
            "username": "<EMAIL>",
            "password": "wrongpassword"
        }
        
        response = client.post(
            "/api/v1/auth/login",
            data=invalid_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 401
        data = response.json()
        assert data["code"] == 401
        
        print("✅ 无效登录测试通过")
    
    def test_unauthorized_access(self):
        """测试未授权访问"""
        print("🧪 测试未授权访问...")
        
        response = client.get("/api/v1/auth/me")
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 401
        
        print("✅ 未授权访问测试通过")
    
    def test_duplicate_registration(self):
        """测试重复注册"""
        print("🧪 测试重复注册...")
        
        # 尝试用相同邮箱再次注册
        response = client.post("/api/v1/auth/register", json=self.test_user_data)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        assert response.status_code == 400
        data = response.json()
        assert data["code"] == 400
        assert "邮箱已被注册" in data["message"]
        
        print("✅ 重复注册测试通过")


def run_auth_tests():
    """运行认证测试"""
    print("🧪 开始运行认证API测试...")
    
    test_auth = TestAuthAPI()
    
    try:
        # 按顺序执行测试
        test_auth.test_user_registration()
        test_auth.test_user_login()
        test_auth.test_get_current_user()
        test_auth.test_token_refresh()
        test_auth.test_user_logout()
        
        # 错误情况测试
        test_auth.test_invalid_login()
        test_auth.test_unauthorized_access()
        test_auth.test_duplicate_registration()
        
        print("🎉 所有认证API测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_async_auth():
    """测试异步认证端点"""
    print("🧪 测试异步认证端点...")
    
    async with httpx.AsyncClient(app=app, base_url="http://test") as ac:
        # 测试注册
        register_data = {
            "email": "<EMAIL>",
            "username": "asyncuser",
            "password": "Async123456",
            "confirm_password": "Async123456",
            "full_name": "异步测试用户"
        }
        
        response = await ac.post("/api/v1/auth/register", json=register_data)
        assert response.status_code == 200
        
        # 测试登录
        login_data = {
            "username": register_data["email"],
            "password": register_data["password"]
        }
        
        response = await ac.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 200
        
        data = response.json()
        access_token = data["data"]["access_token"]
        
        # 测试获取用户信息
        headers = {"Authorization": f"Bearer {access_token}"}
        response = await ac.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200
        
        print("✅ 异步认证测试通过")


def main():
    """主函数"""
    print("🚀 开始认证API测试...")
    
    # 运行同步测试
    sync_success = run_auth_tests()
    
    # 运行异步测试
    try:
        asyncio.run(test_async_auth())
        async_success = True
    except Exception as e:
        print(f"❌ 异步测试失败: {e}")
        async_success = False
    
    if sync_success and async_success:
        print("🎉 所有认证测试完成！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
