"""
图神经网络服务

提供GNN模型训练、预测、图嵌入等功能
"""

import numpy as np
import pandas as pd
import pickle
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import warnings
warnings.filterwarnings('ignore')

# 尝试导入深度学习库
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.optim import Adam
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import torch_geometric
    from torch_geometric.data import Data, DataLoader
    from torch_geometric.nn import GCNConv, GATConv, SAGEConv, GraphConv
    from torch_geometric.utils import to_networkx
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False

try:
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from app.core.logging import logger
from app.models.knowledge_graph import (
    KnowledgeGraph, GNNModel, GNNPrediction, GraphEmbedding
)


class GNNArchitectures:
    """GNN架构定义"""
    
    def __init__(self):
        pass
    
    def create_gcn_model(self, config: Dict[str, Any]):
        """创建GCN模型"""
        if not TORCH_AVAILABLE or not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch和PyTorch Geometric不可用")
        
        class GCNModel(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2, dropout=0.5):
                super(GCNModel, self).__init__()
                self.num_layers = num_layers
                self.dropout = dropout
                
                self.convs = nn.ModuleList()
                self.convs.append(GCNConv(input_dim, hidden_dim))
                
                for _ in range(num_layers - 2):
                    self.convs.append(GCNConv(hidden_dim, hidden_dim))
                
                self.convs.append(GCNConv(hidden_dim, output_dim))
                
            def forward(self, x, edge_index):
                for i, conv in enumerate(self.convs[:-1]):
                    x = conv(x, edge_index)
                    x = F.relu(x)
                    x = F.dropout(x, p=self.dropout, training=self.training)
                
                x = self.convs[-1](x, edge_index)
                return x
        
        return GCNModel(
            input_dim=config['input_dim'],
            hidden_dim=config.get('hidden_dim', 64),
            output_dim=config['output_dim'],
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.5)
        )
    
    def create_gat_model(self, config: Dict[str, Any]):
        """创建GAT模型"""
        if not TORCH_AVAILABLE or not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch和PyTorch Geometric不可用")
        
        class GATModel(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2, heads=8, dropout=0.5):
                super(GATModel, self).__init__()
                self.num_layers = num_layers
                self.dropout = dropout
                
                self.convs = nn.ModuleList()
                self.convs.append(GATConv(input_dim, hidden_dim, heads=heads, dropout=dropout))
                
                for _ in range(num_layers - 2):
                    self.convs.append(GATConv(hidden_dim * heads, hidden_dim, heads=heads, dropout=dropout))
                
                self.convs.append(GATConv(hidden_dim * heads, output_dim, heads=1, dropout=dropout))
                
            def forward(self, x, edge_index):
                for i, conv in enumerate(self.convs[:-1]):
                    x = conv(x, edge_index)
                    x = F.elu(x)
                    x = F.dropout(x, p=self.dropout, training=self.training)
                
                x = self.convs[-1](x, edge_index)
                return x
        
        return GATModel(
            input_dim=config['input_dim'],
            hidden_dim=config.get('hidden_dim', 64),
            output_dim=config['output_dim'],
            num_layers=config.get('num_layers', 2),
            heads=config.get('heads', 8),
            dropout=config.get('dropout', 0.5)
        )
    
    def create_sage_model(self, config: Dict[str, Any]):
        """创建GraphSAGE模型"""
        if not TORCH_AVAILABLE or not TORCH_GEOMETRIC_AVAILABLE:
            raise ImportError("PyTorch和PyTorch Geometric不可用")
        
        class SAGEModel(nn.Module):
            def __init__(self, input_dim, hidden_dim, output_dim, num_layers=2, dropout=0.5):
                super(SAGEModel, self).__init__()
                self.num_layers = num_layers
                self.dropout = dropout
                
                self.convs = nn.ModuleList()
                self.convs.append(SAGEConv(input_dim, hidden_dim))
                
                for _ in range(num_layers - 2):
                    self.convs.append(SAGEConv(hidden_dim, hidden_dim))
                
                self.convs.append(SAGEConv(hidden_dim, output_dim))
                
            def forward(self, x, edge_index):
                for i, conv in enumerate(self.convs[:-1]):
                    x = conv(x, edge_index)
                    x = F.relu(x)
                    x = F.dropout(x, p=self.dropout, training=self.training)
                
                x = self.convs[-1](x, edge_index)
                return x
        
        return SAGEModel(
            input_dim=config['input_dim'],
            hidden_dim=config.get('hidden_dim', 64),
            output_dim=config['output_dim'],
            num_layers=config.get('num_layers', 2),
            dropout=config.get('dropout', 0.5)
        )


class GraphDataProcessor:
    """图数据处理器"""
    
    def __init__(self):
        pass
    
    def prepare_graph_data(
        self,
        graph_data: Dict[str, Any],
        task_type: str,
        target_column: Optional[str] = None
    ) -> Tuple[Any, Optional[np.ndarray]]:
        """准备图数据"""
        try:
            if not TORCH_AVAILABLE or not TORCH_GEOMETRIC_AVAILABLE:
                raise ImportError("PyTorch和PyTorch Geometric不可用")
            
            nodes = graph_data.get('nodes', [])
            edges = graph_data.get('edges', [])
            
            # 构建节点特征矩阵
            node_features = []
            node_labels = []
            node_id_map = {}
            
            for i, node in enumerate(nodes):
                node_id_map[node['id']] = i
                
                # 提取节点特征
                features = node.get('features', [])
                if not features:
                    # 如果没有特征，使用默认特征
                    features = self._generate_default_features(node)
                
                node_features.append(features)
                
                # 提取标签（如果有）
                if target_column and target_column in node.get('properties', {}):
                    node_labels.append(node['properties'][target_column])
                else:
                    node_labels.append(0)  # 默认标签
            
            # 构建边索引
            edge_index = []
            edge_weights = []
            
            for edge in edges:
                source_idx = node_id_map.get(edge['source'])
                target_idx = node_id_map.get(edge['target'])
                
                if source_idx is not None and target_idx is not None:
                    edge_index.append([source_idx, target_idx])
                    edge_weights.append(edge.get('weight', 1.0))
                    
                    # 如果是无向图，添加反向边
                    if not edge.get('directed', True):
                        edge_index.append([target_idx, source_idx])
                        edge_weights.append(edge.get('weight', 1.0))
            
            # 转换为PyTorch张量
            x = torch.FloatTensor(node_features)
            edge_index = torch.LongTensor(edge_index).t().contiguous()
            edge_weight = torch.FloatTensor(edge_weights)
            
            # 处理标签
            y = None
            if task_type in ['node_classification', 'node_regression']:
                if task_type == 'node_classification':
                    # 标签编码
                    if SKLEARN_AVAILABLE:
                        le = LabelEncoder()
                        y = torch.LongTensor(le.fit_transform(node_labels))
                    else:
                        y = torch.LongTensor(node_labels)
                else:
                    y = torch.FloatTensor(node_labels)
            
            # 创建PyTorch Geometric数据对象
            data = Data(x=x, edge_index=edge_index, edge_attr=edge_weight, y=y)
            
            return data, node_id_map
            
        except Exception as e:
            logger.error(f"图数据准备失败: {e}")
            raise
    
    def _generate_default_features(self, node: Dict[str, Any]) -> List[float]:
        """生成默认节点特征"""
        try:
            features = []
            
            # 基于节点类型的特征
            node_type = node.get('type', 'unknown')
            type_encoding = hash(node_type) % 100 / 100.0
            features.append(type_encoding)
            
            # 基于节点属性的特征
            properties = node.get('properties', {})
            
            # 数值属性
            numeric_props = ['market_cap', 'revenue', 'employee_count', 'age']
            for prop in numeric_props:
                value = properties.get(prop, 0)
                if isinstance(value, (int, float)) and value > 0:
                    features.append(np.log1p(value) / 20.0)  # 归一化
                else:
                    features.append(0.0)
            
            # 确保特征向量长度一致
            while len(features) < 5:
                features.append(0.0)
            
            return features[:5]  # 限制特征维度
            
        except Exception as e:
            logger.error(f"默认特征生成失败: {e}")
            return [0.0] * 5


class GNNTrainer:
    """GNN训练器"""
    
    def __init__(self):
        self.architectures = GNNArchitectures()
        self.data_processor = GraphDataProcessor()
    
    def train_gnn_model(
        self,
        graph_data: Dict[str, Any],
        model_config: Dict[str, Any],
        training_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """训练GNN模型"""
        try:
            if not TORCH_AVAILABLE or not TORCH_GEOMETRIC_AVAILABLE:
                raise ImportError("PyTorch和PyTorch Geometric不可用")
            
            # 准备数据
            task_type = model_config.get('task_type', 'node_classification')
            target_column = model_config.get('target_column')
            
            data, node_id_map = self.data_processor.prepare_graph_data(
                graph_data, task_type, target_column
            )
            
            # 数据分割
            num_nodes = data.x.size(0)
            train_ratio = training_config.get('train_ratio', 0.6)
            val_ratio = training_config.get('val_ratio', 0.2)
            
            train_mask = torch.zeros(num_nodes, dtype=torch.bool)
            val_mask = torch.zeros(num_nodes, dtype=torch.bool)
            test_mask = torch.zeros(num_nodes, dtype=torch.bool)
            
            indices = torch.randperm(num_nodes)
            train_size = int(train_ratio * num_nodes)
            val_size = int(val_ratio * num_nodes)
            
            train_mask[indices[:train_size]] = True
            val_mask[indices[train_size:train_size + val_size]] = True
            test_mask[indices[train_size + val_size:]] = True
            
            data.train_mask = train_mask
            data.val_mask = val_mask
            data.test_mask = test_mask
            
            # 创建模型
            architecture = model_config.get('architecture', 'gcn')
            input_dim = data.x.size(1)
            
            if task_type == 'node_classification':
                output_dim = len(torch.unique(data.y)) if data.y is not None else 2
            else:
                output_dim = 1
            
            model_arch_config = {
                'input_dim': input_dim,
                'output_dim': output_dim,
                **model_config.get('layer_config', {})
            }
            
            if architecture == 'gcn':
                model = self.architectures.create_gcn_model(model_arch_config)
            elif architecture == 'gat':
                model = self.architectures.create_gat_model(model_arch_config)
            elif architecture == 'sage':
                model = self.architectures.create_sage_model(model_arch_config)
            else:
                raise ValueError(f"Unsupported architecture: {architecture}")
            
            # 训练配置
            optimizer = Adam(model.parameters(), lr=training_config.get('learning_rate', 0.01))
            epochs = training_config.get('epochs', 200)
            
            if task_type == 'node_classification':
                criterion = nn.CrossEntropyLoss()
            else:
                criterion = nn.MSELoss()
            
            # 训练循环
            model.train()
            training_history = {
                'train_loss': [],
                'val_loss': [],
                'train_acc': [],
                'val_acc': []
            }
            
            best_val_loss = float('inf')
            patience = training_config.get('patience', 20)
            patience_counter = 0
            
            for epoch in range(epochs):
                # 训练步骤
                optimizer.zero_grad()
                out = model(data.x, data.edge_index)
                
                if task_type == 'node_classification':
                    loss = criterion(out[data.train_mask], data.y[data.train_mask])
                else:
                    loss = criterion(out[data.train_mask].squeeze(), data.y[data.train_mask])
                
                loss.backward()
                optimizer.step()
                
                # 验证步骤
                model.eval()
                with torch.no_grad():
                    val_out = model(data.x, data.edge_index)
                    
                    if task_type == 'node_classification':
                        val_loss = criterion(val_out[data.val_mask], data.y[data.val_mask])
                        
                        # 计算准确率
                        train_pred = out[data.train_mask].argmax(dim=1)
                        train_acc = (train_pred == data.y[data.train_mask]).float().mean()
                        
                        val_pred = val_out[data.val_mask].argmax(dim=1)
                        val_acc = (val_pred == data.y[data.val_mask]).float().mean()
                    else:
                        val_loss = criterion(val_out[data.val_mask].squeeze(), data.y[data.val_mask])
                        train_acc = 0.0  # 回归任务不计算准确率
                        val_acc = 0.0
                
                training_history['train_loss'].append(loss.item())
                training_history['val_loss'].append(val_loss.item())
                training_history['train_acc'].append(train_acc.item() if isinstance(train_acc, torch.Tensor) else train_acc)
                training_history['val_acc'].append(val_acc.item() if isinstance(val_acc, torch.Tensor) else val_acc)
                
                # 早停检查
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    best_model_state = model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch}")
                        break
                
                model.train()
            
            # 加载最佳模型
            model.load_state_dict(best_model_state)
            
            # 测试评估
            model.eval()
            with torch.no_grad():
                test_out = model(data.x, data.edge_index)
                
                if task_type == 'node_classification':
                    test_pred = test_out[data.test_mask].argmax(dim=1)
                    test_acc = (test_pred == data.y[data.test_mask]).float().mean()
                    
                    # 计算其他指标
                    if SKLEARN_AVAILABLE:
                        y_true = data.y[data.test_mask].cpu().numpy()
                        y_pred = test_pred.cpu().numpy()
                        
                        test_metrics = {
                            'accuracy': accuracy_score(y_true, y_pred),
                            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
                            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
                            'f1': f1_score(y_true, y_pred, average='weighted', zero_division=0)
                        }
                    else:
                        test_metrics = {'accuracy': test_acc.item()}
                else:
                    test_loss = criterion(test_out[data.test_mask].squeeze(), data.y[data.test_mask])
                    test_metrics = {'mse': test_loss.item()}
            
            return {
                'success': True,
                'model': model,
                'training_history': training_history,
                'test_metrics': test_metrics,
                'node_id_map': node_id_map,
                'data': data
            }
            
        except Exception as e:
            logger.error(f"GNN模型训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


class GNNService:
    """GNN服务"""
    
    def __init__(self):
        self.trainer = GNNTrainer()
    
    async def train_gnn_model(
        self,
        user_id: int,
        knowledge_graph_id: int,
        model_config: Dict[str, Any],
        training_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """训练GNN模型"""
        try:
            # 获取知识图谱
            result = await db.execute(
                select(KnowledgeGraph).where(
                    and_(
                        KnowledgeGraph.id == knowledge_graph_id,
                        KnowledgeGraph.user_id == user_id
                    )
                )
            )
            knowledge_graph = result.scalar_one_or_none()
            
            if not knowledge_graph:
                return {'success': False, 'error': 'Knowledge graph not found'}
            
            graph_data = knowledge_graph.graph_data
            if not graph_data:
                return {'success': False, 'error': 'Graph data not available'}
            
            # 训练模型
            training_result = self.trainer.train_gnn_model(
                graph_data, model_config, training_config
            )
            
            if not training_result['success']:
                return training_result
            
            # 保存模型
            model = training_result['model']
            model_binary = pickle.dumps(model.state_dict()) if TORCH_AVAILABLE else None
            
            gnn_model = GNNModel(
                user_id=user_id,
                knowledge_graph_id=knowledge_graph_id,
                model_name=model_config.get('name', f'GNN_{datetime.now().strftime("%Y%m%d_%H%M%S")}'),
                model_type='gnn',
                architecture=model_config.get('architecture', 'gcn'),
                description=model_config.get('description', ''),
                model_config=model_config,
                training_config=training_config,
                hyperparameters=model_config.get('layer_config', {}),
                training_metrics=training_result['training_history'],
                test_metrics=training_result['test_metrics'],
                model_binary=model_binary,
                status='completed',
                trained_at=datetime.utcnow()
            )
            
            db.add(gnn_model)
            await db.commit()
            
            return {
                'success': True,
                'model_id': gnn_model.id,
                'training_history': training_result['training_history'],
                'test_metrics': training_result['test_metrics']
            }
            
        except Exception as e:
            logger.error(f"GNN模型训练失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def predict_with_gnn(
        self,
        user_id: int,
        model_id: int,
        input_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """使用GNN模型进行预测"""
        try:
            # 获取模型
            result = await db.execute(
                select(GNNModel).where(
                    and_(
                        GNNModel.id == model_id,
                        GNNModel.user_id == user_id
                    )
                )
            )
            gnn_model = result.scalar_one_or_none()
            
            if not gnn_model:
                return {'success': False, 'error': 'GNN model not found'}
            
            if not gnn_model.model_binary:
                return {'success': False, 'error': 'Model binary not available'}
            
            # 加载模型
            if not TORCH_AVAILABLE:
                return {'success': False, 'error': 'PyTorch not available'}
            
            model_state = pickle.loads(gnn_model.model_binary)
            
            # 这里需要重新创建模型架构并加载权重
            # 简化实现，返回模拟预测结果
            predictions = {
                'node_predictions': [0.8, 0.6, 0.9, 0.7],  # 示例预测结果
                'confidence_scores': [0.9, 0.8, 0.95, 0.85],
                'prediction_time_ms': 50.0
            }
            
            # 保存预测结果
            gnn_prediction = GNNPrediction(
                user_id=user_id,
                gnn_model_id=model_id,
                prediction_type='node_classification',
                task_type='classification',
                input_entities=input_data.get('entities', []),
                predictions=predictions['node_predictions'],
                prediction_probabilities=predictions['confidence_scores'],
                confidence_scores=predictions['confidence_scores'],
                prediction_time_ms=predictions['prediction_time_ms'],
                status='completed'
            )
            
            db.add(gnn_prediction)
            await db.commit()
            
            return {
                'success': True,
                'prediction_id': gnn_prediction.id,
                'predictions': predictions
            }
            
        except Exception as e:
            logger.error(f"GNN预测失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }


# 全局GNN服务实例
gnn_service = GNNService()
