"""add backtest tables

Revision ID: 004
Revises: 003
Create Date: 2024-12-22 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建策略表
    op.create_table('strategies',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('code', sa.Text(), nullable=False),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('nodes', sa.JSON(), nullable=True),
        sa.Column('edges', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_public', sa.<PERSON>(), nullable=True),
        sa.Column('backtest_count', sa.Integer(), nullable=True),
        sa.Column('avg_return', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('max_drawdown', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('sharpe_ratio', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_strategies_id'), 'strategies', ['id'], unique=False)
    op.create_index(op.f('ix_strategies_user_id'), 'strategies', ['user_id'], unique=False)

    # 创建回测任务表
    op.create_table('backtest_tasks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('strategy_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=False),
        sa.Column('initial_capital', sa.Numeric(precision=15, scale=2), nullable=False),
        sa.Column('benchmark', sa.String(length=20), nullable=True),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['strategy_id'], ['strategies.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_backtest_tasks_id'), 'backtest_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_tasks_strategy_id'), 'backtest_tasks', ['strategy_id'], unique=False)
    op.create_index(op.f('ix_backtest_tasks_user_id'), 'backtest_tasks', ['user_id'], unique=False)

    # 创建回测结果表
    op.create_table('backtest_results',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('task_id', sa.Integer(), nullable=False),
        sa.Column('total_return', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('annual_return', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('benchmark_return', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('alpha', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('beta', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('volatility', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('sharpe_ratio', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('sortino_ratio', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('max_drawdown', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('max_drawdown_duration', sa.Integer(), nullable=True),
        sa.Column('total_trades', sa.Integer(), nullable=True),
        sa.Column('winning_trades', sa.Integer(), nullable=True),
        sa.Column('losing_trades', sa.Integer(), nullable=True),
        sa.Column('win_rate', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('avg_win', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('avg_loss', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('profit_factor', sa.Numeric(precision=10, scale=4), nullable=True),
        sa.Column('daily_returns', sa.JSON(), nullable=True),
        sa.Column('portfolio_values', sa.JSON(), nullable=True),
        sa.Column('positions', sa.JSON(), nullable=True),
        sa.Column('trades', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['task_id'], ['backtest_tasks.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('task_id')
    )
    op.create_index(op.f('ix_backtest_results_id'), 'backtest_results', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_results_task_id'), 'backtest_results', ['task_id'], unique=True)

    # 创建回测模板表
    op.create_table('backtest_templates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('config', sa.JSON(), nullable=False),
        sa.Column('is_public', sa.Boolean(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_backtest_templates_id'), 'backtest_templates', ['id'], unique=False)
    op.create_index(op.f('ix_backtest_templates_user_id'), 'backtest_templates', ['user_id'], unique=False)


def downgrade() -> None:
    # 删除表
    op.drop_index(op.f('ix_backtest_templates_user_id'), table_name='backtest_templates')
    op.drop_index(op.f('ix_backtest_templates_id'), table_name='backtest_templates')
    op.drop_table('backtest_templates')
    
    op.drop_index(op.f('ix_backtest_results_task_id'), table_name='backtest_results')
    op.drop_index(op.f('ix_backtest_results_id'), table_name='backtest_results')
    op.drop_table('backtest_results')
    
    op.drop_index(op.f('ix_backtest_tasks_user_id'), table_name='backtest_tasks')
    op.drop_index(op.f('ix_backtest_tasks_strategy_id'), table_name='backtest_tasks')
    op.drop_index(op.f('ix_backtest_tasks_id'), table_name='backtest_tasks')
    op.drop_table('backtest_tasks')
    
    op.drop_index(op.f('ix_strategies_user_id'), table_name='strategies')
    op.drop_index(op.f('ix_strategies_id'), table_name='strategies')
    op.drop_table('strategies')
