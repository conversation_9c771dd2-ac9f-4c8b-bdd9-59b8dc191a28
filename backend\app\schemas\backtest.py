"""
回测相关数据模式

定义回测API的请求和响应数据结构
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional, Dict, List, Any
from pydantic import BaseModel, Field, validator


# =============================================================================
# 策略相关模式
# =============================================================================

class StrategyBase(BaseModel):
    """策略基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="策略名称")
    description: Optional[str] = Field(None, max_length=1000, description="策略描述")
    code: str = Field(..., min_length=1, description="策略代码")
    config: Optional[Dict[str, Any]] = Field(None, description="策略配置参数")
    nodes: Optional[Dict[str, Any]] = Field(None, description="可视化节点数据")
    edges: Optional[Dict[str, Any]] = Field(None, description="节点连接数据")
    status: Optional[str] = Field("draft", description="策略状态")


class StrategyCreate(StrategyBase):
    """创建策略请求"""
    pass


class StrategyUpdate(BaseModel):
    """更新策略请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    code: Optional[str] = Field(None, min_length=1)
    config: Optional[Dict[str, Any]] = None
    nodes: Optional[Dict[str, Any]] = None
    edges: Optional[Dict[str, Any]] = None
    status: Optional[str] = None


class StrategyResponse(StrategyBase):
    """策略响应"""
    id: int
    user_id: int
    is_public: bool
    backtest_count: int
    avg_return: Optional[Decimal]
    max_drawdown: Optional[Decimal]
    sharpe_ratio: Optional[Decimal]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 回测任务相关模式
# =============================================================================

class BacktestTaskBase(BaseModel):
    """回测任务基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=1000, description="任务描述")
    start_date: date = Field(..., description="回测开始日期")
    end_date: date = Field(..., description="回测结束日期")
    initial_capital: Decimal = Field(..., gt=0, description="初始资金")
    benchmark: Optional[str] = Field("000300.XSHG", description="基准指数")
    config: Optional[Dict[str, Any]] = Field(None, description="回测配置参数")

    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('结束日期必须晚于开始日期')
        return v

    @validator('initial_capital')
    def validate_initial_capital(cls, v):
        if v <= 0:
            raise ValueError('初始资金必须大于0')
        if v > 100000000:  # 1亿
            raise ValueError('初始资金不能超过1亿')
        return v


class BacktestTaskCreate(BacktestTaskBase):
    """创建回测任务请求"""
    strategy_id: int = Field(..., description="策略ID")


class BacktestTaskUpdate(BaseModel):
    """更新回测任务请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)


class BacktestTaskResponse(BacktestTaskBase):
    """回测任务响应"""
    id: int
    user_id: int
    strategy_id: int
    status: str
    progress: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 回测结果相关模式
# =============================================================================

class BacktestMetrics(BaseModel):
    """回测指标"""
    total_return: Optional[Decimal] = Field(None, description="总收益率")
    annual_return: Optional[Decimal] = Field(None, description="年化收益率")
    benchmark_return: Optional[Decimal] = Field(None, description="基准收益率")
    alpha: Optional[Decimal] = Field(None, description="阿尔法")
    beta: Optional[Decimal] = Field(None, description="贝塔")
    volatility: Optional[Decimal] = Field(None, description="波动率")
    sharpe_ratio: Optional[Decimal] = Field(None, description="夏普比率")
    sortino_ratio: Optional[Decimal] = Field(None, description="索提诺比率")
    max_drawdown: Optional[Decimal] = Field(None, description="最大回撤")
    max_drawdown_duration: Optional[int] = Field(None, description="最大回撤持续天数")
    total_trades: Optional[int] = Field(None, description="总交易次数")
    winning_trades: Optional[int] = Field(None, description="盈利交易次数")
    losing_trades: Optional[int] = Field(None, description="亏损交易次数")
    win_rate: Optional[Decimal] = Field(None, description="胜率")
    avg_win: Optional[Decimal] = Field(None, description="平均盈利")
    avg_loss: Optional[Decimal] = Field(None, description="平均亏损")
    profit_factor: Optional[Decimal] = Field(None, description="盈亏比")


class TradeRecord(BaseModel):
    """交易记录"""
    date: str = Field(..., description="交易日期")
    symbol: str = Field(..., description="股票代码")
    action: str = Field(..., description="交易动作")
    quantity: int = Field(..., description="交易数量")
    price: Decimal = Field(..., description="交易价格")
    amount: Decimal = Field(..., description="交易金额")
    remaining_capital: Optional[Decimal] = Field(None, description="剩余资金")


class PortfolioValue(BaseModel):
    """组合价值"""
    date: str = Field(..., description="日期")
    value: Decimal = Field(..., description="组合价值")


class DailyReturn(BaseModel):
    """日收益率"""
    date: str = Field(..., description="日期")
    return_rate: Decimal = Field(..., description="收益率", alias="return")


class BacktestResultResponse(BaseModel):
    """回测结果响应"""
    id: int
    task_id: int
    
    # 基本指标
    total_return: Optional[Decimal]
    annual_return: Optional[Decimal]
    benchmark_return: Optional[Decimal]
    alpha: Optional[Decimal]
    beta: Optional[Decimal]
    
    # 风险指标
    volatility: Optional[Decimal]
    sharpe_ratio: Optional[Decimal]
    sortino_ratio: Optional[Decimal]
    max_drawdown: Optional[Decimal]
    max_drawdown_duration: Optional[int]
    
    # 交易统计
    total_trades: Optional[int]
    winning_trades: Optional[int]
    losing_trades: Optional[int]
    win_rate: Optional[Decimal]
    avg_win: Optional[Decimal]
    avg_loss: Optional[Decimal]
    profit_factor: Optional[Decimal]
    
    # 详细数据
    daily_returns: Optional[List[Dict[str, Any]]]
    portfolio_values: Optional[List[Dict[str, Any]]]
    positions: Optional[Dict[str, Any]]
    trades: Optional[List[Dict[str, Any]]]
    
    created_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 回测模板相关模式
# =============================================================================

class BacktestTemplateBase(BaseModel):
    """回测模板基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="模板名称")
    description: Optional[str] = Field(None, max_length=1000, description="模板描述")
    category: Optional[str] = Field(None, max_length=50, description="模板分类")
    config: Dict[str, Any] = Field(..., description="模板配置参数")
    is_public: bool = Field(False, description="是否公开")


class BacktestTemplateCreate(BacktestTemplateBase):
    """创建回测模板请求"""
    pass


class BacktestTemplateUpdate(BaseModel):
    """更新回测模板请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=1000)
    category: Optional[str] = Field(None, max_length=50)
    config: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None


class BacktestTemplateResponse(BacktestTemplateBase):
    """回测模板响应"""
    id: int
    user_id: int
    is_active: bool
    usage_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 回测统计相关模式
# =============================================================================

class BacktestStats(BaseModel):
    """回测统计"""
    total_strategies: int = Field(..., description="总策略数")
    total_backtests: int = Field(..., description="总回测数")
    running_backtests: int = Field(..., description="运行中回测数")
    completed_backtests: int = Field(..., description="已完成回测数")
    failed_backtests: int = Field(..., description="失败回测数")
    avg_return: Optional[Decimal] = Field(None, description="平均收益率")
    best_strategy: Optional[str] = Field(None, description="最佳策略")
    worst_strategy: Optional[str] = Field(None, description="最差策略")


class BacktestComparison(BaseModel):
    """回测对比"""
    task_ids: List[int] = Field(..., description="对比的任务ID列表")
    metrics: List[BacktestMetrics] = Field(..., description="对比指标")
    chart_data: Dict[str, Any] = Field(..., description="图表数据")
