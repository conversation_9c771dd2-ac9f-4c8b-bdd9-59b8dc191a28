"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/grid/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/theme/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/menu/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/breadcrumb/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/dropdown/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Breadcrumb,Button,Drawer,Dropdown,Grid,Layout,Menu,Space,Typography,theme!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LineChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DatabaseOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/FundOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RobotOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BookOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ProfileOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LogoutOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuUnfoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuFoldOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,BellOutlined,BookOutlined,DashboardOutlined,DatabaseOutlined,FundOutlined,LineChartOutlined,LogoutOutlined,MenuFoldOutlined,MenuUnfoldOutlined,ProfileOutlined,QuestionCircleOutlined,RobotOutlined,SettingOutlined,UserOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BellOutlined.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * 仪表板布局组件\n * \n * 提供统一的仪表板布局，包含侧边栏、顶部导航、面包屑等\n */ \n\n\n\n\n\nconst { Header, Sider, Content } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Text } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { useBreakpoint } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst menuItems = [\n    {\n        key: \"overview\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 62,\n            columnNumber: 11\n        }, undefined),\n        label: \"概览\",\n        path: \"/dashboard/overview\"\n    },\n    {\n        key: \"market\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 68,\n            columnNumber: 11\n        }, undefined),\n        label: \"市场数据\",\n        path: \"/dashboard/market\",\n        children: [\n            {\n                key: \"market-overview\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 15\n                }, undefined),\n                label: \"市场概览\",\n                path: \"/dashboard/market/overview\"\n            },\n            {\n                key: \"stock-list\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 15\n                }, undefined),\n                label: \"股票列表\",\n                path: \"/dashboard/market/stocks\"\n            },\n            {\n                key: \"charts\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 15\n                }, undefined),\n                label: \"图表分析\",\n                path: \"/dashboard/market/charts\"\n            }\n        ]\n    },\n    {\n        key: \"portfolio\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 94,\n            columnNumber: 11\n        }, undefined),\n        label: \"投资组合\",\n        path: \"/dashboard/portfolio\",\n        children: [\n            {\n                key: \"portfolio-overview\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 15\n                }, undefined),\n                label: \"组合概览\",\n                path: \"/dashboard/portfolio/overview\"\n            },\n            {\n                key: \"positions\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 15\n                }, undefined),\n                label: \"持仓管理\",\n                path: \"/dashboard/portfolio/positions\"\n            },\n            {\n                key: \"performance\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 15\n                }, undefined),\n                label: \"业绩分析\",\n                path: \"/dashboard/portfolio/performance\"\n            }\n        ]\n    },\n    {\n        key: \"strategy\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 120,\n            columnNumber: 11\n        }, undefined),\n        label: \"策略中心\",\n        path: \"/dashboard/strategy\",\n        children: [\n            {\n                key: \"strategy-list\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, undefined),\n                label: \"策略列表\",\n                path: \"/dashboard/strategy/list\"\n            },\n            {\n                key: \"strategy-editor\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 15\n                }, undefined),\n                label: \"策略编辑器\",\n                path: \"/dashboard/strategy/editor\"\n            },\n            {\n                key: \"backtest\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 15\n                }, undefined),\n                label: \"回测分析\",\n                path: \"/dashboard/strategy/backtest\"\n            }\n        ]\n    },\n    {\n        key: \"settings\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 146,\n            columnNumber: 11\n        }, undefined),\n        label: \"设置\",\n        path: \"/dashboard/settings\",\n        children: [\n            {\n                key: \"jqdata-config\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 15\n                }, undefined),\n                label: \"JQData配置\",\n                path: \"/dashboard/settings/jqdata\"\n            },\n            {\n                key: \"profile\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 15\n                }, undefined),\n                label: \"个人资料\",\n                path: \"/dashboard/settings/profile\"\n            },\n            {\n                key: \"preferences\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 15\n                }, undefined),\n                label: \"偏好设置\",\n                path: \"/dashboard/settings/preferences\"\n            }\n        ]\n    }\n];\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { logout } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthActions)();\n    const screens = useBreakpoint();\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDrawerOpen, setMobileDrawerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedKeys, setSelectedKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openKeys, setOpenKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { token: { colorBgContainer, borderRadiusLG } } = _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].useToken();\n    const isMobile = !screens.md;\n    // 根据当前路径设置选中的菜单项\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const currentPath = pathname;\n        const findSelectedKey = (items)=>{\n            for (const item of items){\n                if (item.path === currentPath) {\n                    return item.key;\n                }\n                if (item.children) {\n                    const childKey = findSelectedKey(item.children);\n                    if (childKey) {\n                        setOpenKeys((prev)=>[\n                                ...new Set([\n                                    ...prev,\n                                    item.key\n                                ])\n                            ]);\n                        return childKey;\n                    }\n                }\n            }\n            return null;\n        };\n        const selectedKey = findSelectedKey(menuItems);\n        if (selectedKey) {\n            setSelectedKeys([\n                selectedKey\n            ]);\n        }\n    }, [\n        pathname\n    ]);\n    // 生成面包屑\n    const generateBreadcrumb = ()=>{\n        const pathSegments = pathname.split(\"/\").filter(Boolean);\n        const breadcrumbItems = [\n            {\n                title: \"首页\",\n                href: \"/dashboard\"\n            }\n        ];\n        let currentPath = \"\";\n        for (const segment of pathSegments.slice(1)){\n            currentPath += \"/\".concat(segment);\n            const fullPath = \"/dashboard\".concat(currentPath);\n            // 查找对应的菜单项\n            const findMenuItem = (items)=>{\n                for (const item of items){\n                    if (item.path === fullPath) {\n                        return item;\n                    }\n                    if (item.children) {\n                        const child = findMenuItem(item.children);\n                        if (child) return child;\n                    }\n                }\n                return null;\n            };\n            const menuItem = findMenuItem(menuItems);\n            if (menuItem) {\n                breadcrumbItems.push({\n                    title: menuItem.label,\n                    href: menuItem.path\n                });\n            }\n        }\n        return breadcrumbItems;\n    };\n    // 处理菜单点击\n    const handleMenuClick = (param)=>{\n        let { key } = param;\n        const findMenuItem = (items)=>{\n            for (const item of items){\n                if (item.key === key) {\n                    return item;\n                }\n                if (item.children) {\n                    const child = findMenuItem(item.children);\n                    if (child) return child;\n                }\n            }\n            return null;\n        };\n        const menuItem = findMenuItem(menuItems);\n        if (menuItem) {\n            router.push(menuItem.path);\n            if (isMobile) {\n                setMobileDrawerOpen(false);\n            }\n        }\n    };\n    // 用户下拉菜单\n    const userMenuItems = [\n        {\n            key: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 288,\n                columnNumber: 13\n            }, this),\n            label: \"个人资料\",\n            onClick: ()=>router.push(\"/dashboard/settings/profile\")\n        },\n        {\n            key: \"settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 294,\n                columnNumber: 13\n            }, this),\n            label: \"设置\",\n            onClick: ()=>router.push(\"/dashboard/settings\")\n        },\n        {\n            type: \"divider\"\n        },\n        {\n            key: \"help\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 303,\n                columnNumber: 13\n            }, this),\n            label: \"帮助中心\",\n            onClick: ()=>window.open(\"/help\", \"_blank\")\n        },\n        {\n            key: \"logout\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 309,\n                columnNumber: 13\n            }, this),\n            label: \"退出登录\",\n            onClick: logout\n        }\n    ];\n    // 侧边栏内容\n    const sidebarContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 flex items-center justify-center border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_21__.motion.div, {\n                    initial: {\n                        scale: 0\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"text-white text-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            strong: true,\n                            className: \"text-lg\",\n                            children: \"JQData\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    mode: \"inline\",\n                    selectedKeys: selectedKeys,\n                    openKeys: openKeys,\n                    onOpenChange: setOpenKeys,\n                    onClick: handleMenuClick,\n                    className: \"border-none\",\n                    items: menuItems.map((item)=>{\n                        var _item_children;\n                        return {\n                            key: item.key,\n                            icon: item.icon,\n                            label: item.label,\n                            children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>({\n                                    key: child.key,\n                                    icon: child.icon,\n                                    label: child.label\n                                }))\n                        };\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 317,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"min-h-screen\",\n        children: [\n            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Sider, {\n                trigger: null,\n                collapsible: true,\n                collapsed: collapsed,\n                width: 256,\n                className: \"shadow-lg\",\n                style: {\n                    background: colorBgContainer\n                },\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this),\n            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                title: \"导航菜单\",\n                placement: \"left\",\n                onClose: ()=>setMobileDrawerOpen(false),\n                open: mobileDrawerOpen,\n                styles: {\n                    body: {\n                        padding: 0\n                    }\n                },\n                width: 256,\n                children: sidebarContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                        style: {\n                            padding: \"0 24px\",\n                            background: colorBgContainer,\n                            borderBottom: \"1px solid #f0f0f0\"\n                        },\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        type: \"text\",\n                                        icon: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 33\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 58\n                                        }, void 0),\n                                        onClick: ()=>{\n                                            if (isMobile) {\n                                                setMobileDrawerOpen(true);\n                                            } else {\n                                                setCollapsed(!collapsed);\n                                            }\n                                        },\n                                        className: \"text-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        items: generateBreadcrumb(),\n                                        className: \"hidden sm:block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                size: \"middle\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        count: 5,\n                                        size: \"small\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            type: \"text\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            className: \"text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        menu: {\n                                            items: userMenuItems\n                                        },\n                                        placement: \"bottomRight\",\n                                        arrow: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-2 py-1 rounded-lg transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                    size: \"small\",\n                                                    src: user === null || user === void 0 ? void 0 : user.avatarUrl,\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_BellOutlined_BookOutlined_DashboardOutlined_DatabaseOutlined_FundOutlined_LineChartOutlined_LogoutOutlined_MenuFoldOutlined_MenuUnfoldOutlined_ProfileOutlined_QuestionCircleOutlined_RobotOutlined_SettingOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden sm:block\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            strong: true,\n                                                            className: \"text-sm\",\n                                                            children: (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.username)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: user === null || user === void 0 ? void 0 : user.subscriptionType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                        style: {\n                            margin: \"24px\",\n                            padding: \"24px\",\n                            minHeight: \"calc(100vh - 112px)\",\n                            background: colorBgContainer,\n                            borderRadius: borderRadiusLG\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: children\n                        }, pathname, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"ho5bmu+9clV1A7j6DBcJ38MHjxU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _store_auth__WEBPACK_IMPORTED_MODULE_3__.useAuthActions,\n        useBreakpoint,\n        _barrel_optimize_names_Avatar_Badge_Breadcrumb_Button_Drawer_Dropdown_Grid_Layout_Menu_Space_Typography_theme_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"].useToken\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/layout.tsx\n"));

/***/ })

});