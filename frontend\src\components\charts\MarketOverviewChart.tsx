'use client';

/**
 * 市场概览图表组件
 * 
 * 使用ECharts显示市场指数走势图
 */

import React, { useEffect, useRef, useState } from 'react';
import { Spin, Select, Space, Button } from 'antd';
import { ReloadOutlined, FullscreenOutlined } from '@ant-design/icons';
import * as echarts from 'echarts';

const { Option } = Select;

interface MarketOverviewChartProps {
  height?: number;
  showControls?: boolean;
}

interface ChartData {
  time: string[];
  values: number[];
  volumes: number[];
}

export const MarketOverviewChart: React.FC<MarketOverviewChartProps> = ({
  height = 400,
  showControls = true,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedIndex, setSelectedIndex] = useState('000300.XSHG'); // 沪深300
  const [timeRange, setTimeRange] = useState('1D');

  // 模拟数据生成
  const generateMockData = (): ChartData => {
    const now = new Date();
    const time: string[] = [];
    const values: number[] = [];
    const volumes: number[] = [];
    
    let baseValue = 4200;
    let baseVolume = 50000000;
    
    for (let i = 0; i < 240; i++) { // 4小时 * 60分钟
      const currentTime = new Date(now.getTime() - (240 - i) * 60 * 1000);
      time.push(currentTime.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }));
      
      // 模拟价格波动
      const change = (Math.random() - 0.5) * 20;
      baseValue += change;
      values.push(Number(baseValue.toFixed(2)));
      
      // 模拟成交量波动
      const volumeChange = (Math.random() - 0.5) * 10000000;
      baseVolume += volumeChange;
      volumes.push(Math.max(baseVolume, 10000000));
    }
    
    return { time, values, volumes };
  };

  // 初始化图表
  const initChart = () => {
    if (!chartRef.current) return;

    // 销毁现有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);
    
    const data = generateMockData();
    
    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      grid: [
        {
          left: '3%',
          right: '4%',
          top: '10%',
          height: '60%',
        },
        {
          left: '3%',
          right: '4%',
          top: '75%',
          height: '20%',
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: data.time,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
          },
          axisTick: { show: false },
          splitLine: { show: false },
        },
        {
          type: 'category',
          gridIndex: 1,
          data: data.time,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
          },
          axisTick: { show: false },
          splitLine: { show: false },
        },
      ],
      yAxis: [
        {
          type: 'value',
          scale: true,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => value.toFixed(0),
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          gridIndex: 1,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => (value / 100000000).toFixed(1) + '亿',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed',
            },
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E6E8EB',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const time = data.time[dataIndex];
          const value = data.values[dataIndex];
          const volume = data.volumes[dataIndex];
          
          return `
            <div style="padding: 8px;">
              <div style="margin-bottom: 4px; font-weight: bold;">${time}</div>
              <div style="margin-bottom: 2px;">
                <span style="color: #1890ff;">●</span> 
                指数: ${value.toFixed(2)}
              </div>
              <div>
                <span style="color: #52c41a;">●</span> 
                成交量: ${(volume / 100000000).toFixed(2)}亿
              </div>
            </div>
          `;
        },
      },
      series: [
        {
          name: '指数',
          type: 'line',
          data: data.values,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2,
          },
          itemStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
            ]),
          },
          symbol: 'none',
          emphasis: {
            focus: 'series',
          },
        },
        {
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: data.volumes,
          itemStyle: {
            color: '#52c41a',
            opacity: 0.7,
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
            },
          },
        },
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut',
    };

    chartInstance.current.setOption(option);
    setLoading(false);
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      initChart();
    }, 500);
  };

  // 全屏显示
  const handleFullscreen = () => {
    if (chartRef.current) {
      if (chartRef.current.requestFullscreen) {
        chartRef.current.requestFullscreen();
      }
    }
  };

  useEffect(() => {
    initChart();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [selectedIndex, timeRange]);

  return (
    <div className="relative">
      {/* 控制栏 */}
      {showControls && (
        <div className="flex items-center justify-between mb-4">
          <Space>
            <Select
              value={selectedIndex}
              onChange={setSelectedIndex}
              style={{ width: 120 }}
              size="small"
            >
              <Option value="000300.XSHG">沪深300</Option>
              <Option value="000001.XSHG">上证指数</Option>
              <Option value="399001.XSHE">深证成指</Option>
              <Option value="399006.XSHE">创业板指</Option>
            </Select>
            
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 80 }}
              size="small"
            >
              <Option value="1D">日内</Option>
              <Option value="5D">5日</Option>
              <Option value="1M">1月</Option>
              <Option value="3M">3月</Option>
            </Select>
          </Space>
          
          <Space>
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="text"
              size="small"
              icon={<FullscreenOutlined />}
              onClick={handleFullscreen}
            >
              全屏
            </Button>
          </Space>
        </div>
      )}

      {/* 图表容器 */}
      <div className="relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
            <Spin size="large" />
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>
    </div>
  );
};
