"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/jqdata/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/settings/jqdata/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JQDataConfigPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/steps/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/spin/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Card,Col,Descriptions,Form,Input,Modal,Progress,Row,Space,Spin,Statistic,Steps,Typography,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/descriptions/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LinkOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,CheckCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,LinkOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _store_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/auth */ \"(app-pages-browser)/./src/store/auth.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n/**\n * JQData配置页面\n * \n * 用户配置JQData账号、查看配额使用情况、测试连接等\n */ \n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Step } = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction JQDataConfigPage() {\n    var _testResult_quotaInfo_quotaUsageRate;\n    _s();\n    const { user } = (0,_store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [form] = _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testLoading, setTestLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTestModal, setShowTestModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 加载JQData配置\n    const loadConfig = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/jqdata/config\");\n            if (response.code === 200 && response.data) {\n                setConfig(response.data);\n                form.setFieldsValue({\n                    username: response.data.username\n                });\n                setCurrentStep(2); // 已配置\n            } else {\n                setCurrentStep(0); // 未配置\n            }\n        } catch (error) {\n            var _error_response;\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                setCurrentStep(0); // 未配置\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"加载配置失败\");\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 保存配置\n    const handleSave = async (values)=>{\n        try {\n            setLoading(true);\n            setCurrentStep(1); // 配置中\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/jqdata/config\", {\n                username: values.username,\n                password: values.password\n            });\n            if (response.code === 200) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"JQData配置保存成功！\");\n                setConfig(response.data);\n                setCurrentStep(2); // 配置完成\n                form.resetFields([\n                    \"password\"\n                ]);\n            } else {\n                throw new Error(response.message);\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(error.message || \"配置保存失败\");\n            setCurrentStep(0); // 回到未配置状态\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 测试连接\n    const handleTestConnection = async ()=>{\n        try {\n            setTestLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/jqdata/test-connection\");\n            setTestResult(response.data);\n            setShowTestModal(true);\n            if (response.data.success) {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"连接测试成功！\");\n            } else {\n                _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"连接测试失败\");\n            }\n        } catch (error) {\n            _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"测试连接失败\");\n            setTestResult({\n                success: false,\n                message: \"测试连接失败\",\n                errorDetails: error.message\n            });\n            setShowTestModal(true);\n        } finally{\n            setTestLoading(false);\n        }\n    };\n    // 删除配置\n    const handleDelete = ()=>{\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].confirm({\n            title: \"确认删除配置\",\n            content: \"删除后将无法获取JQData数据，确定要删除吗？\",\n            okText: \"确定删除\",\n            okType: \"danger\",\n            cancelText: \"取消\",\n            onOk: async ()=>{\n                try {\n                    await _services_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/jqdata/config\");\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"配置删除成功\");\n                    setConfig(null);\n                    setCurrentStep(0);\n                    form.resetFields();\n                } catch (error) {\n                    _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"删除配置失败\");\n                }\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadConfig();\n    }, []);\n    // 配置步骤\n    const steps = [\n        {\n            title: \"配置账号\",\n            description: \"输入JQData账号信息\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"验证连接\",\n            description: \"验证账号有效性\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: \"配置完成\",\n            description: \"开始使用JQData服务\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    if (loading && !config) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                size: \"large\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                        level: 2,\n                        className: \"!mb-2\",\n                        children: \"JQData配置\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                        type: \"secondary\",\n                        children: \"配置您的JQData账号以获取实时市场数据\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    current: currentStep,\n                    items: steps\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData已配置\",\n                description: \"账号: \".concat(config.username, \" | 状态: \").concat(config.isActive ? \"正常\" : \"异常\"),\n                type: config.isActive ? \"success\" : \"warning\",\n                showIcon: true,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: handleTestConnection,\n                            loading: testLoading,\n                            children: \"测试连接\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: \"small\",\n                            onClick: loadConfig,\n                            children: \"刷新状态\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 235,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                message: \"JQData未配置\",\n                description: \"请配置您的JQData账号以获取实时市场数据\",\n                type: \"info\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                gutter: [\n                    16,\n                    16\n                ],\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"账号配置\",\n                            extra: config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                onClick: handleDelete,\n                                children: \"删除配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    form: form,\n                                    layout: \"vertical\",\n                                    onFinish: handleSave,\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"username\",\n                                            label: \"JQData用户名\",\n                                            rules: [\n                                                {\n                                                    required: true,\n                                                    message: \"请输入JQData用户名\"\n                                                },\n                                                {\n                                                    type: \"email\",\n                                                    message: \"请输入有效的邮箱地址\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                placeholder: \"请输入JQData注册邮箱\",\n                                                prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            name: \"password\",\n                                            label: \"JQData密码\",\n                                            rules: [\n                                                {\n                                                    required: !config,\n                                                    message: \"请输入JQData密码\"\n                                                },\n                                                {\n                                                    min: 6,\n                                                    message: \"密码长度至少6位\"\n                                                }\n                                            ],\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Password, {\n                                                placeholder: config ? \"留空表示不修改密码\" : \"请输入JQData密码\",\n                                                iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 31\n                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 48\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        type: \"primary\",\n                                                        htmlType: \"submit\",\n                                                        loading: loading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        children: config ? \"更新配置\" : \"保存配置\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        onClick: handleTestConnection,\n                                                        loading: testLoading,\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_CheckCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_LinkOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 29\n                                                        }, void 0),\n                                                        children: \"测试连接\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    message: \"配置说明\",\n                                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 请使用您在JQData官网注册的邮箱和密码\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 密码将被加密存储，确保账号安全\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 配置后可获取实时股票数据和历史数据\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 如遇问题请检查账号状态或联系客服\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    type: \"info\",\n                                    showIcon: true,\n                                    className: \"mt-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        xs: 24,\n                        lg: 12,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"配额使用情况\",\n                            children: config ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        gutter: 16,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    title: \"总配额\",\n                                                    value: config.quotaTotal,\n                                                    suffix: \"次\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    title: \"已使用\",\n                                                    value: config.quotaUsed,\n                                                    suffix: \"次\",\n                                                    valueStyle: {\n                                                        color: config.quotaUsed / config.quotaTotal > 0.8 ? \"#ff4d4f\" : \"#1890ff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        children: \"使用率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        strong: true,\n                                                        children: [\n                                                            (config.quotaUsed / config.quotaTotal * 100).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                percent: config.quotaUsed / config.quotaTotal * 100,\n                                                status: config.quotaUsed / config.quotaTotal > 0.9 ? \"exception\" : \"active\",\n                                                strokeColor: {\n                                                    \"0%\": \"#108ee9\",\n                                                    \"100%\": \"#87d068\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        size: \"small\",\n                                        column: 1,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"剩余配额\",\n                                                children: [\n                                                    config.quotaRemaining,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"总调用次数\",\n                                                children: [\n                                                    config.totalApiCalls,\n                                                    \" 次\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"最后使用\",\n                                                children: config.lastUsedAt ? new Date(config.lastUsedAt).toLocaleString() : \"未使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                                label: \"配额重置\",\n                                                children: config.quotaResetDate ? new Date(config.quotaResetDate).toLocaleDateString() : \"未知\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 17\n                                    }, this),\n                                    config.authFailureCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        message: \"认证失败 \".concat(config.authFailureCount, \" 次\"),\n                                        description: config.lastAuthError,\n                                        type: \"warning\",\n                                        showIcon: true,\n                                        className: \"mt-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"配置JQData账号后查看配额信息\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                title: \"连接测试结果\",\n                open: showTestModal,\n                onCancel: ()=>setShowTestModal(false),\n                footer: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: ()=>setShowTestModal(false),\n                        children: \"关闭\"\n                    }, \"close\", false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, void 0)\n                ],\n                children: testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: testResult.success ? \"连接成功\" : \"连接失败\",\n                            description: testResult.message,\n                            type: testResult.success ? \"success\" : \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, this),\n                        testResult.responseTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    children: \"响应时间: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    children: [\n                                        testResult.responseTime.toFixed(3),\n                                        \"秒\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 15\n                        }, this),\n                        testResult.quotaInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"配额信息\",\n                            size: \"small\",\n                            column: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"总配额\",\n                                    children: testResult.quotaInfo.quotaTotal\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"已使用\",\n                                    children: testResult.quotaInfo.quotaUsed\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"剩余\",\n                                    children: testResult.quotaInfo.quotaRemaining\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"].Item, {\n                                    label: \"使用率\",\n                                    children: [\n                                        (_testResult_quotaInfo_quotaUsageRate = testResult.quotaInfo.quotaUsageRate) === null || _testResult_quotaInfo_quotaUsageRate === void 0 ? void 0 : _testResult_quotaInfo_quotaUsageRate.toFixed(1),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 15\n                        }, this),\n                        testResult.errorDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            message: \"错误详情\",\n                            description: testResult.errorDetails,\n                            type: \"error\",\n                            showIcon: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JQData\\\\frontend\\\\src\\\\app\\\\dashboard\\\\settings\\\\jqdata\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(JQDataConfigPage, \"JqQba2e1zxr+XhyG4OsdplQlJUA=\", false, function() {\n    return [\n        _store_auth__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _barrel_optimize_names_Alert_Button_Card_Col_Descriptions_Form_Input_Modal_Progress_Row_Space_Spin_Statistic_Steps_Typography_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm\n    ];\n});\n_c = JQDataConfigPage;\nvar _c;\n$RefreshReg$(_c, \"JQDataConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/settings/jqdata/page.tsx\n"));

/***/ })

});