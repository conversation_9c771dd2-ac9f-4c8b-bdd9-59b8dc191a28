Metadata-Version: 2.4
Name: igraph
Version: 0.11.9
Summary: High performance graph data structures and algorithms
Home-page: https://igraph.org/python
Author: <PERSON><PERSON>-email: <EMAIL>
License: GNU General Public License (GPL)
Project-URL: Bug Tracker, https://github.com/igraph/python-igraph/issues
Project-URL: Changelog, https://github.com/igraph/python-igraph/blob/main/CHANGELOG.md
Project-URL: CI, https://github.com/igraph/python-igraph/actions
Project-URL: Documentation, https://python.igraph.org
Project-URL: Source Code, https://github.com/igraph/python-igraph
Keywords: graph,network,mathematics,math,graph theory,discrete mathematics
Platform: ALL
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: C
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Physics
Classifier: Topic :: Scientific/Engineering :: Bio-Informatics
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
License-File: LICENSE
Requires-Dist: texttable>=1.6.2
Provides-Extra: cairo
Requires-Dist: cairocffi>=1.2.0; extra == "cairo"
Provides-Extra: matplotlib
Requires-Dist: matplotlib>=3.6.0; platform_python_implementation != "PyPy" and extra == "matplotlib"
Provides-Extra: plotly
Requires-Dist: plotly>=5.3.0; extra == "plotly"
Provides-Extra: plotting
Requires-Dist: cairocffi>=1.2.0; extra == "plotting"
Provides-Extra: test
Requires-Dist: cairocffi>=1.2.0; extra == "test"
Requires-Dist: networkx>=2.5; extra == "test"
Requires-Dist: pytest>=7.0.1; extra == "test"
Requires-Dist: pytest-timeout>=2.1.0; extra == "test"
Requires-Dist: numpy>=1.19.0; platform_python_implementation != "PyPy" and extra == "test"
Requires-Dist: pandas>=1.1.0; platform_python_implementation != "PyPy" and extra == "test"
Requires-Dist: scipy>=1.5.0; platform_python_implementation != "PyPy" and extra == "test"
Requires-Dist: matplotlib>=3.6.0; platform_python_implementation != "PyPy" and extra == "test"
Requires-Dist: plotly>=5.3.0; extra == "test"
Requires-Dist: Pillow>=9; platform_python_implementation != "PyPy" and extra == "test"
Provides-Extra: test-win-arm64
Requires-Dist: cairocffi>=1.2.0; extra == "test-win-arm64"
Requires-Dist: networkx>=2.5; extra == "test-win-arm64"
Requires-Dist: pytest>=7.0.1; extra == "test-win-arm64"
Requires-Dist: pytest-timeout>=2.1.0; extra == "test-win-arm64"
Provides-Extra: test-musl
Requires-Dist: cairocffi>=1.2.0; extra == "test-musl"
Requires-Dist: networkx>=2.5; extra == "test-musl"
Requires-Dist: pytest>=7.0.1; extra == "test-musl"
Requires-Dist: pytest-timeout>=2.1.0; extra == "test-musl"
Provides-Extra: doc
Requires-Dist: Sphinx>=7.0.0; extra == "doc"
Requires-Dist: sphinx-rtd-theme>=1.3.0; extra == "doc"
Requires-Dist: sphinx-gallery>=0.14.0; extra == "doc"
Requires-Dist: pydoctor>=23.4.0; extra == "doc"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

Python interface to the igraph high performance graph
library, primarily aimed at complex network research and analysis.

Graph plotting functionality is provided by the Cairo library, so make
sure you install the Python bindings of Cairo if you want to generate
publication-quality graph plots. You can try either `pycairo
<http://cairographics.org/pycairo>`_ or `cairocffi <https://doc.courtbouillon.org/cairocffi/>`_,
``cairocffi`` is recommended because there were bug reports affecting igraph
graph plots in Jupyter notebooks when using ``pycairo`` (but not with
``cairocffi``).
