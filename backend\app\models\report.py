"""
报告生成相关数据模型

定义报告模板、报告任务、报告历史等数据模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class ReportTemplate(Base):
    """报告模板模型"""
    
    __tablename__ = "report_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模板信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    template_type = Column(String(50), nullable=False)  # portfolio, risk, performance, custom
    category = Column(String(50))  # daily, weekly, monthly, quarterly, annual
    
    # 模板配置
    template_config = Column(JSON, nullable=False)  # 模板配置参数
    layout_config = Column(JSON)  # 布局配置
    style_config = Column(JSON)  # 样式配置
    
    # 内容配置
    sections = Column(JSON)  # 报告章节配置
    charts = Column(JSON)  # 图表配置
    tables = Column(JSON)  # 表格配置
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)  # 是否公开模板
    
    # 统计
    usage_count = Column(Integer, default=0)  # 使用次数
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="report_templates")
    report_tasks = relationship("ReportTask", back_populates="template", cascade="all, delete-orphan")


class ReportTask(Base):
    """报告任务模型"""
    
    __tablename__ = "report_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    template_id = Column(Integer, ForeignKey("report_templates.id"), nullable=False, index=True)
    
    # 任务信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    report_type = Column(String(50), nullable=False)  # pdf, excel, html, email
    
    # 生成参数
    date_range_start = Column(DateTime)
    date_range_end = Column(DateTime)
    parameters = Column(JSON)  # 报告参数
    
    # 调度配置
    schedule_type = Column(String(20), default="manual")  # manual, daily, weekly, monthly
    schedule_config = Column(JSON)  # 调度配置
    next_run_time = Column(DateTime)
    
    # 任务状态
    status = Column(String(20), default="pending")  # pending, running, completed, failed, cancelled
    progress = Column(Integer, default=0)  # 进度百分比
    
    # 执行信息
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    error_message = Column(Text)
    
    # 输出信息
    output_format = Column(String(20))  # pdf, xlsx, html
    output_size = Column(Integer)  # 文件大小（字节）
    output_path = Column(String(500))  # 文件路径
    
    # 邮件配置
    email_enabled = Column(Boolean, default=False)
    email_recipients = Column(JSON)  # 收件人列表
    email_subject = Column(String(200))
    email_body = Column(Text)
    email_sent_at = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="report_tasks")
    template = relationship("ReportTemplate", back_populates="report_tasks")
    report_history = relationship("ReportHistory", back_populates="task", uselist=False, cascade="all, delete-orphan")


class ReportHistory(Base):
    """报告历史模型"""
    
    __tablename__ = "report_history"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("report_tasks.id"), nullable=False, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 报告信息
    report_name = Column(String(100), nullable=False)
    report_type = Column(String(50), nullable=False)
    file_name = Column(String(200), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # 文件大小（字节）
    file_hash = Column(String(64))  # 文件哈希值
    
    # 报告内容摘要
    content_summary = Column(JSON)  # 报告内容摘要
    data_sources = Column(JSON)  # 数据源信息
    generation_time = Column(Integer)  # 生成耗时（秒）
    
    # 访问统计
    download_count = Column(Integer, default=0)
    last_downloaded_at = Column(DateTime)
    view_count = Column(Integer, default=0)
    last_viewed_at = Column(DateTime)
    
    # 分享设置
    is_shared = Column(Boolean, default=False)
    share_token = Column(String(64))  # 分享令牌
    share_expires_at = Column(DateTime)
    
    # 状态
    is_archived = Column(Boolean, default=False)
    archived_at = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="report_history")
    task = relationship("ReportTask", back_populates="report_history")


class ReportSubscription(Base):
    """报告订阅模型"""
    
    __tablename__ = "report_subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    template_id = Column(Integer, ForeignKey("report_templates.id"), nullable=False, index=True)
    
    # 订阅信息
    subscription_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 调度配置
    frequency = Column(String(20), nullable=False)  # daily, weekly, monthly, quarterly
    schedule_time = Column(String(10))  # HH:MM 格式
    schedule_day = Column(Integer)  # 月报告的日期或周报告的星期几
    timezone = Column(String(50), default="Asia/Shanghai")
    
    # 报告配置
    report_format = Column(String(20), default="pdf")  # pdf, excel, html
    include_charts = Column(Boolean, default=True)
    include_tables = Column(Boolean, default=True)
    include_analysis = Column(Boolean, default=True)
    
    # 邮件配置
    email_enabled = Column(Boolean, default=True)
    email_recipients = Column(JSON, nullable=False)  # 收件人列表
    email_subject_template = Column(String(200))
    email_body_template = Column(Text)
    
    # 状态
    is_active = Column(Boolean, default=True)
    last_sent_at = Column(DateTime)
    next_send_at = Column(DateTime)
    send_count = Column(Integer, default=0)
    
    # 错误处理
    failure_count = Column(Integer, default=0)
    last_error = Column(Text)
    last_error_at = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="report_subscriptions")
    template = relationship("ReportTemplate")


class ReportAnalytics(Base):
    """报告分析统计模型"""
    
    __tablename__ = "report_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 统计日期
    date = Column(DateTime, nullable=False, index=True)
    
    # 生成统计
    reports_generated = Column(Integer, default=0)
    pdf_reports = Column(Integer, default=0)
    excel_reports = Column(Integer, default=0)
    html_reports = Column(Integer, default=0)
    
    # 邮件统计
    emails_sent = Column(Integer, default=0)
    emails_opened = Column(Integer, default=0)
    emails_clicked = Column(Integer, default=0)
    
    # 下载统计
    downloads_total = Column(Integer, default=0)
    downloads_pdf = Column(Integer, default=0)
    downloads_excel = Column(Integer, default=0)
    
    # 模板使用统计
    template_usage = Column(JSON)  # 各模板使用次数
    
    # 性能统计
    avg_generation_time = Column(SQLDecimal(10, 2))  # 平均生成时间
    total_file_size = Column(Integer)  # 总文件大小
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="report_analytics")
