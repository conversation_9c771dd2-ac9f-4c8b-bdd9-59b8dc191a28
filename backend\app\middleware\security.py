"""
安全中间件

提供IP限流、账户锁定、安全头等安全功能
"""

import time
from collections import defaultdict
from typing import Dict, List

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from app.core.logging import logger


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_counts: Dict[str, List[float]] = defaultdict(list)
        self.failed_attempts: Dict[str, int] = defaultdict(int)
        self.locked_accounts: Dict[str, float] = {}
    
    async def dispatch(self, request: Request, call_next):
        """中间件处理逻辑"""
        start_time = time.time()
        request.state.start_time = start_time
        
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        request.state.client_ip = client_ip
        
        # IP限流检查
        if not self._check_rate_limit(client_ip):
            logger.warning(f"IP限流触发: {client_ip}")
            return Response(
                content='{"code": 429, "message": "请求过于频繁，请稍后再试"}',
                status_code=429,
                media_type="application/json"
            )
        
        # 处理请求
        response = await call_next(request)
        
        # 添加安全头
        self._add_security_headers(response)
        
        # 记录处理时间
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip.strip()
        
        # 使用客户端IP
        return request.client.host if request.client else "unknown"
    
    def _check_rate_limit(self, client_ip: str) -> bool:
        """检查IP限流"""
        now = time.time()
        minute_ago = now - 60
        
        # 清理过期记录
        self.request_counts[client_ip] = [
            timestamp for timestamp in self.request_counts[client_ip]
            if timestamp > minute_ago
        ]
        
        # 检查是否超过限制
        if len(self.request_counts[client_ip]) >= settings.RATE_LIMIT_PER_MINUTE:
            return False
        
        # 记录当前请求
        self.request_counts[client_ip].append(now)
        return True
    
    def _add_security_headers(self, response: Response) -> None:
        """添加安全响应头"""
        # 防止XSS攻击
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # HSTS (仅HTTPS)
        if settings.is_production:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # CSP
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' https:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none';"
        )
        
        # 隐藏服务器信息
        response.headers["Server"] = "JQData-Platform"
