"""
回测服务

提供策略回测功能，包括回测执行、结果分析等
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger
from app.models.backtest import BacktestTask, BacktestResult, Strategy
from app.services.jqdata_service import JQDataService


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self):
        self.jqdata_service = JQDataService()
    
    async def run_backtest(
        self, 
        task_id: int, 
        db: AsyncSession,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """运行回测"""
        try:
            # 获取回测任务
            result = await db.execute(
                select(BacktestTask).where(BacktestTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise ValueError(f"回测任务不存在: {task_id}")
            
            # 更新任务状态
            await self._update_task_status(db, task_id, "running", 0)
            
            # 获取策略
            strategy_result = await db.execute(
                select(Strategy).where(Strategy.id == task.strategy_id)
            )
            strategy = strategy_result.scalar_one_or_none()
            
            if not strategy:
                raise ValueError(f"策略不存在: {task.strategy_id}")
            
            # 执行回测
            backtest_result = await self._execute_backtest(
                task, strategy, progress_callback
            )
            
            # 保存结果
            await self._save_backtest_result(db, task_id, backtest_result)
            
            # 更新任务状态
            await self._update_task_status(db, task_id, "completed", 100)
            
            return backtest_result
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            await self._update_task_status(
                db, task_id, "failed", 0, str(e)
            )
            raise
    
    async def _execute_backtest(
        self, 
        task: BacktestTask, 
        strategy: Strategy,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """执行回测逻辑"""
        
        # 模拟回测数据生成
        start_date = task.start_date
        end_date = task.end_date
        initial_capital = float(task.initial_capital)
        
        # 生成日期序列
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        trading_days = [d for d in date_range if d.weekday() < 5]  # 简化：只考虑工作日
        
        # 模拟策略执行
        portfolio_values = []
        daily_returns = []
        trades = []
        positions = {}
        
        current_capital = initial_capital
        current_positions = {}
        
        for i, date in enumerate(trading_days):
            # 更新进度
            progress = int((i / len(trading_days)) * 100)
            if progress_callback:
                await progress_callback(progress)
            
            # 模拟策略信号生成
            signal = self._generate_mock_signal(date, strategy)
            
            # 执行交易
            if signal:
                trade_result = self._execute_trade(
                    signal, current_capital, current_positions, date
                )
                if trade_result:
                    trades.append(trade_result)
                    current_capital = trade_result['remaining_capital']
                    current_positions = trade_result['positions']
            
            # 计算当日组合价值
            portfolio_value = self._calculate_portfolio_value(
                current_capital, current_positions, date
            )
            portfolio_values.append({
                'date': date.isoformat(),
                'value': portfolio_value
            })
            
            # 计算日收益率
            if i > 0:
                daily_return = (portfolio_value - portfolio_values[i-1]['value']) / portfolio_values[i-1]['value']
                daily_returns.append({
                    'date': date.isoformat(),
                    'return': daily_return
                })
        
        # 计算回测指标
        metrics = self._calculate_metrics(
            portfolio_values, daily_returns, trades, initial_capital
        )
        
        return {
            'metrics': metrics,
            'portfolio_values': portfolio_values,
            'daily_returns': daily_returns,
            'trades': trades,
            'positions': current_positions,
        }
    
    def _generate_mock_signal(self, date: datetime, strategy: Strategy) -> Optional[Dict]:
        """生成模拟交易信号"""
        # 简化的信号生成逻辑
        import random
        
        if random.random() < 0.1:  # 10%概率生成信号
            return {
                'action': random.choice(['buy', 'sell']),
                'symbol': '000001.XSHE',
                'quantity': random.randint(100, 1000),
                'price': 10.0 + random.uniform(-2, 2),
                'date': date,
            }
        return None
    
    def _execute_trade(
        self, 
        signal: Dict, 
        capital: float, 
        positions: Dict, 
        date: datetime
    ) -> Optional[Dict]:
        """执行交易"""
        symbol = signal['symbol']
        action = signal['action']
        quantity = signal['quantity']
        price = signal['price']
        
        if action == 'buy':
            cost = quantity * price
            if cost <= capital:
                # 执行买入
                if symbol in positions:
                    positions[symbol]['quantity'] += quantity
                    positions[symbol]['avg_cost'] = (
                        positions[symbol]['avg_cost'] * positions[symbol]['quantity'] + cost
                    ) / (positions[symbol]['quantity'] + quantity)
                else:
                    positions[symbol] = {
                        'quantity': quantity,
                        'avg_cost': price,
                    }
                
                return {
                    'date': date.isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': price,
                    'amount': cost,
                    'remaining_capital': capital - cost,
                    'positions': positions.copy(),
                }
        
        elif action == 'sell' and symbol in positions:
            available_quantity = positions[symbol]['quantity']
            sell_quantity = min(quantity, available_quantity)
            
            if sell_quantity > 0:
                # 执行卖出
                revenue = sell_quantity * price
                positions[symbol]['quantity'] -= sell_quantity
                
                if positions[symbol]['quantity'] == 0:
                    del positions[symbol]
                
                return {
                    'date': date.isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'price': price,
                    'amount': revenue,
                    'remaining_capital': capital + revenue,
                    'positions': positions.copy(),
                }
        
        return None
    
    def _calculate_portfolio_value(
        self, 
        cash: float, 
        positions: Dict, 
        date: datetime
    ) -> float:
        """计算组合价值"""
        total_value = cash
        
        for symbol, position in positions.items():
            # 模拟当前价格（实际应该从数据源获取）
            current_price = position['avg_cost'] * (1 + np.random.normal(0, 0.02))
            total_value += position['quantity'] * current_price
        
        return total_value
    
    def _calculate_metrics(
        self, 
        portfolio_values: List[Dict], 
        daily_returns: List[Dict], 
        trades: List[Dict],
        initial_capital: float
    ) -> Dict[str, float]:
        """计算回测指标"""
        if not portfolio_values or not daily_returns:
            return {}
        
        final_value = portfolio_values[-1]['value']
        total_return = (final_value - initial_capital) / initial_capital
        
        # 计算年化收益率
        days = len(portfolio_values)
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        # 计算波动率
        returns = [r['return'] for r in daily_returns]
        volatility = np.std(returns) * np.sqrt(252) if returns else 0
        
        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        max_drawdown = 0
        peak = initial_capital
        for pv in portfolio_values:
            value = pv['value']
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 交易统计
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['action'] == 'sell' and t['price'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        return {
            'total_return': round(total_return, 4),
            'annual_return': round(annual_return, 4),
            'volatility': round(volatility, 4),
            'sharpe_ratio': round(sharpe_ratio, 4),
            'max_drawdown': round(max_drawdown, 4),
            'total_trades': total_trades,
            'win_rate': round(win_rate, 4),
        }
    
    async def _update_task_status(
        self, 
        db: AsyncSession, 
        task_id: int, 
        status: str, 
        progress: int,
        error_message: Optional[str] = None
    ):
        """更新任务状态"""
        update_data = {
            'status': status,
            'progress': progress,
            'updated_at': datetime.utcnow(),
        }
        
        if status == 'running' and progress == 0:
            update_data['started_at'] = datetime.utcnow()
        elif status in ['completed', 'failed']:
            update_data['completed_at'] = datetime.utcnow()
        
        if error_message:
            update_data['error_message'] = error_message
        
        await db.execute(
            update(BacktestTask)
            .where(BacktestTask.id == task_id)
            .values(**update_data)
        )
        await db.commit()
    
    async def _save_backtest_result(
        self, 
        db: AsyncSession, 
        task_id: int, 
        result_data: Dict[str, Any]
    ):
        """保存回测结果"""
        metrics = result_data['metrics']
        
        result = BacktestResult(
            task_id=task_id,
            total_return=Decimal(str(metrics.get('total_return', 0))),
            annual_return=Decimal(str(metrics.get('annual_return', 0))),
            volatility=Decimal(str(metrics.get('volatility', 0))),
            sharpe_ratio=Decimal(str(metrics.get('sharpe_ratio', 0))),
            max_drawdown=Decimal(str(metrics.get('max_drawdown', 0))),
            total_trades=metrics.get('total_trades', 0),
            win_rate=Decimal(str(metrics.get('win_rate', 0))),
            daily_returns=result_data['daily_returns'],
            portfolio_values=result_data['portfolio_values'],
            trades=result_data['trades'],
            positions=result_data['positions'],
        )
        
        db.add(result)
        await db.commit()


class BacktestService:
    """回测服务"""
    
    def __init__(self):
        self.engine = BacktestEngine()
        self.running_tasks: Dict[int, asyncio.Task] = {}
    
    async def start_backtest(
        self, 
        task_id: int, 
        db: AsyncSession,
        progress_callback: Optional[callable] = None
    ) -> bool:
        """启动回测任务"""
        if task_id in self.running_tasks:
            return False  # 任务已在运行
        
        # 创建异步任务
        task = asyncio.create_task(
            self.engine.run_backtest(task_id, db, progress_callback)
        )
        self.running_tasks[task_id] = task
        
        # 任务完成后清理
        def cleanup(future):
            self.running_tasks.pop(task_id, None)
        
        task.add_done_callback(cleanup)
        
        return True
    
    def cancel_backtest(self, task_id: int) -> bool:
        """取消回测任务"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            return True
        return False
    
    def get_running_tasks(self) -> List[int]:
        """获取正在运行的任务"""
        return list(self.running_tasks.keys())
    
    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否在运行"""
        return task_id in self.running_tasks


# 全局回测服务实例
backtest_service = BacktestService()
