"""
回测服务

提供策略回测功能，包括回测执行、结果分析等
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import logger
from app.models.backtest import BacktestTask, BacktestResult, Strategy
from app.services.jqdata_service import JQDataService


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self):
        self.jqdata_service = JQDataService()
    
    async def run_backtest(
        self, 
        task_id: int, 
        db: AsyncSession,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """运行回测"""
        try:
            # 获取回测任务
            result = await db.execute(
                select(BacktestTask).where(BacktestTask.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if not task:
                raise ValueError(f"回测任务不存在: {task_id}")
            
            # 更新任务状态
            await self._update_task_status(db, task_id, "running", 0)
            
            # 获取策略
            strategy_result = await db.execute(
                select(Strategy).where(Strategy.id == task.strategy_id)
            )
            strategy = strategy_result.scalar_one_or_none()
            
            if not strategy:
                raise ValueError(f"策略不存在: {task.strategy_id}")
            
            # 执行回测
            backtest_result = await self._execute_backtest(
                task, strategy, progress_callback
            )
            
            # 保存结果
            await self._save_backtest_result(db, task_id, backtest_result)
            
            # 更新任务状态
            await self._update_task_status(db, task_id, "completed", 100)
            
            return backtest_result
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            await self._update_task_status(
                db, task_id, "failed", 0, str(e)
            )
            raise
    
    async def _execute_backtest(
        self,
        task: BacktestTask,
        strategy: Strategy,
        progress_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """执行回测逻辑"""

        start_date = task.start_date
        end_date = task.end_date
        initial_capital = float(task.initial_capital)

        # 从策略配置中获取股票池
        strategy_config = strategy.config or {}
        stock_pool = strategy_config.get('stock_pool', ['000001.XSHE', '000002.XSHE'])

        # 获取历史数据
        logger.info(f"获取历史数据: {stock_pool}, {start_date} - {end_date}")
        historical_data = await self._get_historical_data(stock_pool, start_date, end_date)

        if not historical_data:
            raise ValueError("无法获取历史数据，请检查JQData配置")

        # 获取交易日历
        trading_days = list(historical_data.keys())
        trading_days.sort()

        # 执行策略回测
        portfolio_values = []
        daily_returns = []
        trades = []

        current_capital = initial_capital
        current_positions = {}

        for i, date_str in enumerate(trading_days):
            # 更新进度
            progress = int((i / len(trading_days)) * 100)
            if progress_callback:
                await progress_callback(progress)

            date = datetime.fromisoformat(date_str)
            day_data = historical_data[date_str]

            # 执行策略逻辑
            signals = await self._execute_strategy_logic(
                strategy, date, day_data, current_positions
            )

            # 执行交易
            for signal in signals:
                trade_result = self._execute_trade(
                    signal, current_capital, current_positions, date, day_data
                )
                if trade_result:
                    trades.append(trade_result)
                    current_capital = trade_result['remaining_capital']
                    current_positions = trade_result['positions']

            # 计算当日组合价值
            portfolio_value = self._calculate_portfolio_value(
                current_capital, current_positions, day_data
            )
            portfolio_values.append({
                'date': date_str,
                'value': portfolio_value
            })

            # 计算日收益率
            if i > 0:
                prev_value = portfolio_values[i-1]['value']
                daily_return = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0
                daily_returns.append({
                    'date': date_str,
                    'return': daily_return
                })

        # 计算回测指标
        metrics = self._calculate_metrics(
            portfolio_values, daily_returns, trades, initial_capital
        )

        return {
            'metrics': metrics,
            'portfolio_values': portfolio_values,
            'daily_returns': daily_returns,
            'trades': trades,
            'positions': current_positions,
        }
    
    async def _get_historical_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Dict]:
        """获取历史数据"""
        try:
            # 使用JQData获取历史数据
            historical_data = await self.jqdata_service.get_price_data(
                symbols=symbols,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                frequency='daily'
            )

            if not historical_data or 'data' not in historical_data:
                logger.error("获取历史数据失败")
                return {}

            # 重新组织数据结构：按日期分组
            organized_data = {}
            for symbol, symbol_data in historical_data['data'].items():
                for record in symbol_data:
                    date_str = record['date']
                    if date_str not in organized_data:
                        organized_data[date_str] = {}
                    organized_data[date_str][symbol] = record

            return organized_data

        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return {}

    async def _execute_strategy_logic(
        self,
        strategy: Strategy,
        date: datetime,
        day_data: Dict,
        current_positions: Dict
    ) -> List[Dict]:
        """执行策略逻辑生成交易信号"""
        signals = []

        try:
            # 这里应该执行真实的策略代码
            # 目前简化为基于移动平均线的策略
            strategy_config = strategy.config or {}

            for symbol, stock_data in day_data.items():
                # 简单的移动平均策略示例
                current_price = float(stock_data.get('close', 0))

                if current_price > 0:
                    # 如果没有持仓且价格上涨，买入
                    if symbol not in current_positions and current_price > float(stock_data.get('open', 0)):
                        signals.append({
                            'action': 'buy',
                            'symbol': symbol,
                            'quantity': 100,  # 固定数量
                            'price': current_price,
                            'date': date,
                        })
                    # 如果有持仓且价格下跌，卖出
                    elif symbol in current_positions and current_price < float(stock_data.get('open', 0)):
                        signals.append({
                            'action': 'sell',
                            'symbol': symbol,
                            'quantity': current_positions[symbol]['quantity'],
                            'price': current_price,
                            'date': date,
                        })

        except Exception as e:
            logger.error(f"执行策略逻辑失败: {e}")

        return signals
    
    def _execute_trade(
        self,
        signal: Dict,
        capital: float,
        positions: Dict,
        date: datetime,
        day_data: Dict
    ) -> Optional[Dict]:
        """执行交易"""
        symbol = signal['symbol']
        action = signal['action']
        quantity = signal['quantity']
        price = signal['price']

        # 添加交易成本（手续费等）
        commission_rate = 0.0003  # 0.03% 手续费

        if action == 'buy':
            cost = quantity * price
            commission = cost * commission_rate
            total_cost = cost + commission

            if total_cost <= capital:
                # 执行买入
                if symbol in positions:
                    old_quantity = positions[symbol]['quantity']
                    old_cost = positions[symbol]['avg_cost'] * old_quantity
                    new_quantity = old_quantity + quantity
                    positions[symbol]['quantity'] = new_quantity
                    positions[symbol]['avg_cost'] = (old_cost + cost) / new_quantity
                else:
                    positions[symbol] = {
                        'quantity': quantity,
                        'avg_cost': price,
                    }

                return {
                    'date': date.isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': quantity,
                    'price': price,
                    'amount': cost,
                    'commission': commission,
                    'remaining_capital': capital - total_cost,
                    'positions': positions.copy(),
                }

        elif action == 'sell' and symbol in positions:
            available_quantity = positions[symbol]['quantity']
            sell_quantity = min(quantity, available_quantity)

            if sell_quantity > 0:
                # 执行卖出
                revenue = sell_quantity * price
                commission = revenue * commission_rate
                net_revenue = revenue - commission

                positions[symbol]['quantity'] -= sell_quantity

                if positions[symbol]['quantity'] == 0:
                    del positions[symbol]

                return {
                    'date': date.isoformat(),
                    'symbol': symbol,
                    'action': action,
                    'quantity': sell_quantity,
                    'price': price,
                    'amount': revenue,
                    'commission': commission,
                    'remaining_capital': capital + net_revenue,
                    'positions': positions.copy(),
                }

        return None
    
    def _calculate_portfolio_value(
        self,
        cash: float,
        positions: Dict,
        day_data: Dict
    ) -> float:
        """计算组合价值"""
        total_value = cash

        for symbol, position in positions.items():
            if symbol in day_data:
                # 使用真实的收盘价
                current_price = float(day_data[symbol].get('close', position['avg_cost']))
                total_value += position['quantity'] * current_price
            else:
                # 如果没有当日数据，使用成本价
                total_value += position['quantity'] * position['avg_cost']

        return total_value
    
    def _calculate_metrics(
        self, 
        portfolio_values: List[Dict], 
        daily_returns: List[Dict], 
        trades: List[Dict],
        initial_capital: float
    ) -> Dict[str, float]:
        """计算回测指标"""
        if not portfolio_values or not daily_returns:
            return {}
        
        final_value = portfolio_values[-1]['value']
        total_return = (final_value - initial_capital) / initial_capital
        
        # 计算年化收益率
        days = len(portfolio_values)
        annual_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
        
        # 计算波动率
        returns = [r['return'] for r in daily_returns]
        volatility = np.std(returns) * np.sqrt(252) if returns else 0
        
        # 计算夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
        
        # 计算最大回撤
        max_drawdown = 0
        peak = initial_capital
        for pv in portfolio_values:
            value = pv['value']
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 交易统计
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['action'] == 'sell' and t['price'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        return {
            'total_return': round(total_return, 4),
            'annual_return': round(annual_return, 4),
            'volatility': round(volatility, 4),
            'sharpe_ratio': round(sharpe_ratio, 4),
            'max_drawdown': round(max_drawdown, 4),
            'total_trades': total_trades,
            'win_rate': round(win_rate, 4),
        }
    
    async def _update_task_status(
        self, 
        db: AsyncSession, 
        task_id: int, 
        status: str, 
        progress: int,
        error_message: Optional[str] = None
    ):
        """更新任务状态"""
        update_data = {
            'status': status,
            'progress': progress,
            'updated_at': datetime.utcnow(),
        }
        
        if status == 'running' and progress == 0:
            update_data['started_at'] = datetime.utcnow()
        elif status in ['completed', 'failed']:
            update_data['completed_at'] = datetime.utcnow()
        
        if error_message:
            update_data['error_message'] = error_message
        
        await db.execute(
            update(BacktestTask)
            .where(BacktestTask.id == task_id)
            .values(**update_data)
        )
        await db.commit()
    
    async def _save_backtest_result(
        self, 
        db: AsyncSession, 
        task_id: int, 
        result_data: Dict[str, Any]
    ):
        """保存回测结果"""
        metrics = result_data['metrics']
        
        result = BacktestResult(
            task_id=task_id,
            total_return=Decimal(str(metrics.get('total_return', 0))),
            annual_return=Decimal(str(metrics.get('annual_return', 0))),
            volatility=Decimal(str(metrics.get('volatility', 0))),
            sharpe_ratio=Decimal(str(metrics.get('sharpe_ratio', 0))),
            max_drawdown=Decimal(str(metrics.get('max_drawdown', 0))),
            total_trades=metrics.get('total_trades', 0),
            win_rate=Decimal(str(metrics.get('win_rate', 0))),
            daily_returns=result_data['daily_returns'],
            portfolio_values=result_data['portfolio_values'],
            trades=result_data['trades'],
            positions=result_data['positions'],
        )
        
        db.add(result)
        await db.commit()


class BacktestService:
    """回测服务"""
    
    def __init__(self):
        self.engine = BacktestEngine()
        self.running_tasks: Dict[int, asyncio.Task] = {}
    
    async def start_backtest(
        self, 
        task_id: int, 
        db: AsyncSession,
        progress_callback: Optional[callable] = None
    ) -> bool:
        """启动回测任务"""
        if task_id in self.running_tasks:
            return False  # 任务已在运行
        
        # 创建异步任务
        task = asyncio.create_task(
            self.engine.run_backtest(task_id, db, progress_callback)
        )
        self.running_tasks[task_id] = task
        
        # 任务完成后清理
        def cleanup(future):
            self.running_tasks.pop(task_id, None)
        
        task.add_done_callback(cleanup)
        
        return True
    
    def cancel_backtest(self, task_id: int) -> bool:
        """取消回测任务"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            return True
        return False
    
    def get_running_tasks(self) -> List[int]:
        """获取正在运行的任务"""
        return list(self.running_tasks.keys())
    
    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否在运行"""
        return task_id in self.running_tasks


# 全局回测服务实例
backtest_service = BacktestService()
