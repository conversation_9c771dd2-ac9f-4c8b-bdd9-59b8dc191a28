"""
动态再平衡API路由
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Any, Optional
from pydantic import BaseModel

from app.core.database import get_db
from app.core.logging import logger
from app.services.rebalancing_service import RebalancingService
from app.services.jqdata_service import JQDataService

router = APIRouter()

# 依赖注入
async def get_rebalancing_service() -> RebalancingService:
    jqdata_service = JQDataService()
    return RebalancingService(jqdata_service)

# 请求模型
class AnalyzeDriftRequest(BaseModel):
    portfolio_id: int
    target_weights: Dict[str, float]

class CreateRebalancePlanRequest(BaseModel):
    portfolio_id: int
    target_weights: Dict[str, float]
    rebalance_threshold: float = 0.05
    min_trade_amount: float = 1000.0

class ExecuteRebalancingRequest(BaseModel):
    portfolio_id: int
    trade_plan: List[Dict[str, Any]]
    execution_mode: str = 'simulation'  # simulation, live

class ScheduleAutoRebalanceRequest(BaseModel):
    portfolio_id: int
    target_weights: Dict[str, float]
    schedule_config: Dict[str, Any]

# 响应模型
class DriftAnalysisResponse(BaseModel):
    success: bool
    portfolio_id: Optional[int] = None
    current_weights: Optional[Dict[str, float]] = None
    target_weights: Optional[Dict[str, float]] = None
    drift_analysis: Optional[Dict[str, Any]] = None
    rebalance_suggestions: Optional[List[Dict[str, Any]]] = None
    analysis_date: Optional[str] = None
    error: Optional[str] = None

class RebalancePlanResponse(BaseModel):
    success: bool
    rebalance_needed: Optional[bool] = None
    max_drift: Optional[float] = None
    threshold: Optional[float] = None
    trade_plan: Optional[List[Dict[str, Any]]] = None
    estimated_costs: Optional[Dict[str, float]] = None
    risk_assessment: Optional[Dict[str, Any]] = None
    execution_priority: Optional[List[str]] = None
    plan_created_at: Optional[str] = None
    message: Optional[str] = None
    error: Optional[str] = None

@router.post("/analyze-drift", response_model=DriftAnalysisResponse)
async def analyze_portfolio_drift(
    request: AnalyzeDriftRequest,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """分析投资组合偏离度"""
    try:
        logger.info(f"Analyzing drift for portfolio {request.portfolio_id}")
        
        result = await service.analyze_portfolio_drift(
            portfolio_id=request.portfolio_id,
            target_weights=request.target_weights,
            db=db
        )
        
        return DriftAnalysisResponse(**result)
        
    except Exception as e:
        logger.error(f"Drift analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create-plan", response_model=RebalancePlanResponse)
async def create_rebalancing_plan(
    request: CreateRebalancePlanRequest,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """创建再平衡计划"""
    try:
        logger.info(f"Creating rebalancing plan for portfolio {request.portfolio_id}")
        
        result = await service.create_rebalancing_plan(
            portfolio_id=request.portfolio_id,
            target_weights=request.target_weights,
            rebalance_threshold=request.rebalance_threshold,
            min_trade_amount=request.min_trade_amount,
            db=db
        )
        
        return RebalancePlanResponse(**result)
        
    except Exception as e:
        logger.error(f"Rebalancing plan creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/execute")
async def execute_rebalancing(
    request: ExecuteRebalancingRequest,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """执行再平衡计划"""
    try:
        logger.info(f"Executing rebalancing for portfolio {request.portfolio_id}")
        
        result = await service.execute_rebalancing(
            portfolio_id=request.portfolio_id,
            trade_plan=request.trade_plan,
            execution_mode=request.execution_mode,
            db=db
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Rebalancing execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/schedule-auto")
async def schedule_automatic_rebalancing(
    request: ScheduleAutoRebalanceRequest,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """设置自动再平衡计划"""
    try:
        logger.info(f"Scheduling auto rebalancing for portfolio {request.portfolio_id}")
        
        result = await service.schedule_automatic_rebalancing(
            portfolio_id=request.portfolio_id,
            target_weights=request.target_weights,
            schedule_config=request.schedule_config,
            db=db
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Auto rebalancing scheduling failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/portfolio/{portfolio_id}/status")
async def get_rebalancing_status(
    portfolio_id: int,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """获取投资组合再平衡状态"""
    try:
        # 简化的状态信息
        status = {
            "portfolio_id": portfolio_id,
            "last_rebalance": "2024-08-20T10:00:00Z",
            "next_check": "2024-08-30T10:00:00Z",
            "auto_rebalance_enabled": True,
            "current_drift": 0.03,
            "threshold": 0.05,
            "status": "within_threshold"
        }
        
        return {
            "success": True,
            "status": status
        }
        
    except Exception as e:
        logger.error(f"Failed to get rebalancing status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/portfolio/{portfolio_id}/history")
async def get_rebalancing_history(
    portfolio_id: int,
    limit: int = 10,
    db: AsyncSession = Depends(get_db)
):
    """获取再平衡历史记录"""
    try:
        # 模拟历史记录
        history = [
            {
                "id": 1,
                "date": "2024-08-20T10:00:00Z",
                "type": "automatic",
                "trades_executed": 3,
                "total_cost": 125.50,
                "max_drift_before": 0.08,
                "max_drift_after": 0.01,
                "status": "completed"
            },
            {
                "id": 2,
                "date": "2024-08-15T14:30:00Z",
                "type": "manual",
                "trades_executed": 2,
                "total_cost": 89.20,
                "max_drift_before": 0.06,
                "max_drift_after": 0.02,
                "status": "completed"
            }
        ]
        
        return {
            "success": True,
            "portfolio_id": portfolio_id,
            "history": history[:limit],
            "total_count": len(history)
        }
        
    except Exception as e:
        logger.error(f"Failed to get rebalancing history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/simulate")
async def simulate_rebalancing(
    request: CreateRebalancePlanRequest,
    db: AsyncSession = Depends(get_db),
    service: RebalancingService = Depends(get_rebalancing_service)
):
    """模拟再平衡效果"""
    try:
        logger.info(f"Simulating rebalancing for portfolio {request.portfolio_id}")
        
        # 创建再平衡计划
        plan_result = await service.create_rebalancing_plan(
            portfolio_id=request.portfolio_id,
            target_weights=request.target_weights,
            rebalance_threshold=request.rebalance_threshold,
            min_trade_amount=request.min_trade_amount,
            db=db
        )
        
        if not plan_result['success'] or not plan_result.get('rebalance_needed'):
            return plan_result
        
        # 模拟执行
        simulation_result = await service.execute_rebalancing(
            portfolio_id=request.portfolio_id,
            trade_plan=plan_result['trade_plan'],
            execution_mode='simulation',
            db=db
        )
        
        return {
            "success": True,
            "simulation_mode": True,
            "plan": plan_result,
            "execution": simulation_result
        }
        
    except Exception as e:
        logger.error(f"Rebalancing simulation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/settings/default")
async def get_default_rebalancing_settings():
    """获取默认再平衡设置"""
    try:
        default_settings = {
            "rebalance_threshold": 0.05,  # 5%
            "min_trade_amount": 1000.0,   # 最小交易金额
            "auto_rebalance_frequency": "monthly",
            "risk_tolerance": "medium",
            "cost_optimization": True,
            "market_timing": False,
            "notification_enabled": True
        }
        
        return {
            "success": True,
            "default_settings": default_settings
        }
        
    except Exception as e:
        logger.error(f"Failed to get default settings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/optimize-weights")
async def optimize_target_weights(
    portfolio_id: int,
    optimization_method: str = "mean_variance",
    constraints: Optional[Dict[str, Any]] = None,
    db: AsyncSession = Depends(get_db)
):
    """优化目标权重"""
    try:
        logger.info(f"Optimizing weights for portfolio {request.portfolio_id}")
        
        # 简化的权重优化
        optimized_weights = {
            "000001.XSHE": 0.25,
            "600036.XSHG": 0.35,
            "600519.XSHG": 0.40
        }
        
        optimization_metrics = {
            "expected_return": 0.12,
            "volatility": 0.18,
            "sharpe_ratio": 0.67,
            "max_drawdown": 0.15
        }
        
        return {
            "success": True,
            "portfolio_id": portfolio_id,
            "optimization_method": optimization_method,
            "optimized_weights": optimized_weights,
            "metrics": optimization_metrics,
            "optimization_date": "2024-08-28T10:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"Weight optimization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
