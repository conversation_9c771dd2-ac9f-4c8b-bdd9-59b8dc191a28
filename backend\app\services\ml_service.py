"""
机器学习服务

提供股票价格预测、智能选股、情绪分析等AI功能
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import os
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import logger
from app.services.jqdata_service import JQDataService
from app.core.config import settings


class MLService:
    """机器学习服务"""
    
    def __init__(self, jqdata_service: JQDataService):
        self.jqdata_service = jqdata_service
        self.models_dir = os.path.join(settings.BASE_DIR, "ml_models")
        self.scalers_dir = os.path.join(settings.BASE_DIR, "ml_scalers")
        
        # 确保目录存在
        os.makedirs(self.models_dir, exist_ok=True)
        os.makedirs(self.scalers_dir, exist_ok=True)
        
        # 模型缓存
        self.models_cache: Dict[str, Any] = {}
        self.scalers_cache: Dict[str, Any] = {}
    
    async def predict_stock_price(
        self, 
        symbol: str, 
        days_ahead: int = 5,
        model_type: str = 'random_forest'
    ) -> Dict[str, Any]:
        """预测股票价格"""
        try:
            # 获取历史数据
            historical_data = await self._get_training_data(symbol, days=252)  # 一年数据
            
            if len(historical_data) < 60:  # 至少需要60天数据
                return {
                    'success': False,
                    'error': '历史数据不足，无法进行预测'
                }
            
            # 准备特征数据
            features, targets = self._prepare_features(historical_data)
            
            if len(features) == 0:
                return {
                    'success': False,
                    'error': '特征数据准备失败'
                }
            
            # 训练或加载模型
            model, scaler = await self._get_or_train_model(
                symbol, features, targets, model_type
            )
            
            # 生成预测
            predictions = self._generate_predictions(
                model, scaler, features, days_ahead
            )
            
            # 计算置信度
            confidence = self._calculate_confidence(model, features, targets)
            
            return {
                'success': True,
                'symbol': symbol,
                'predictions': predictions,
                'confidence': confidence,
                'model_type': model_type,
                'prediction_date': datetime.utcnow().isoformat(),
                'days_ahead': days_ahead
            }
            
        except Exception as e:
            logger.error(f"Price prediction failed for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def intelligent_stock_selection(
        self, 
        market: str = 'all',
        top_n: int = 20,
        criteria: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """智能选股"""
        try:
            # 获取股票池
            stock_pool = await self._get_stock_pool(market)
            
            if not stock_pool:
                return {
                    'success': False,
                    'error': '无法获取股票池数据'
                }
            
            # 计算股票评分
            scored_stocks = []
            
            for symbol in stock_pool[:100]:  # 限制处理数量以提高性能
                try:
                    score = await self._calculate_stock_score(symbol, criteria)
                    if score is not None:
                        scored_stocks.append({
                            'symbol': symbol,
                            'score': score['total_score'],
                            'details': score
                        })
                except Exception as e:
                    logger.warning(f"Failed to score stock {symbol}: {e}")
                    continue
            
            # 排序并返回前N只
            scored_stocks.sort(key=lambda x: x['score'], reverse=True)
            top_stocks = scored_stocks[:top_n]
            
            return {
                'success': True,
                'top_stocks': top_stocks,
                'total_analyzed': len(scored_stocks),
                'selection_date': datetime.utcnow().isoformat(),
                'criteria': criteria or {}
            }
            
        except Exception as e:
            logger.error(f"Intelligent stock selection failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def sentiment_analysis(
        self, 
        symbol: str,
        news_count: int = 50
    ) -> Dict[str, Any]:
        """情绪分析"""
        try:
            # 获取新闻数据
            news_data = await self._get_news_data(symbol, news_count)
            
            if not news_data:
                return {
                    'success': False,
                    'error': '无法获取新闻数据'
                }
            
            # 分析情绪
            sentiment_scores = []
            positive_count = 0
            negative_count = 0
            neutral_count = 0
            
            for news in news_data:
                sentiment = self._analyze_text_sentiment(news['content'])
                sentiment_scores.append(sentiment)
                
                if sentiment['compound'] > 0.1:
                    positive_count += 1
                elif sentiment['compound'] < -0.1:
                    negative_count += 1
                else:
                    neutral_count += 1
            
            # 计算总体情绪
            avg_sentiment = np.mean([s['compound'] for s in sentiment_scores])
            
            # 情绪分类
            if avg_sentiment > 0.1:
                overall_sentiment = 'positive'
                sentiment_label = '积极'
            elif avg_sentiment < -0.1:
                overall_sentiment = 'negative'
                sentiment_label = '消极'
            else:
                overall_sentiment = 'neutral'
                sentiment_label = '中性'
            
            return {
                'success': True,
                'symbol': symbol,
                'overall_sentiment': overall_sentiment,
                'sentiment_label': sentiment_label,
                'sentiment_score': avg_sentiment,
                'news_count': len(news_data),
                'sentiment_distribution': {
                    'positive': positive_count,
                    'negative': negative_count,
                    'neutral': neutral_count
                },
                'analysis_date': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed for {symbol}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _get_training_data(self, symbol: str, days: int = 252) -> pd.DataFrame:
        """获取训练数据"""
        try:
            # 从JQData获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days + 50)  # 多获取一些数据用于计算技术指标
            
            data = await self.jqdata_service.get_price_data(
                symbol, 
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if not data:
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to get training data for {symbol}: {e}")
            return pd.DataFrame()
    
    def _prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备特征数据"""
        try:
            if len(df) < 30:
                return np.array([]), np.array([])
            
            # 计算技术指标
            df = self._calculate_technical_indicators(df)
            
            # 选择特征列
            feature_columns = [
                'open', 'high', 'low', 'volume',
                'ma5', 'ma10', 'ma20',
                'rsi', 'macd', 'macd_signal',
                'bb_upper', 'bb_lower',
                'price_change', 'volume_change'
            ]
            
            # 确保所有特征列都存在
            available_features = [col for col in feature_columns if col in df.columns]
            
            if len(available_features) < 5:
                return np.array([]), np.array([])
            
            # 准备特征矩阵
            features = df[available_features].dropna()
            
            # 目标变量（下一日收盘价）
            targets = df['close'].shift(-1).dropna()
            
            # 确保特征和目标长度一致
            min_length = min(len(features), len(targets))
            features = features.iloc[:min_length]
            targets = targets.iloc[:min_length]
            
            return features.values, targets.values
            
        except Exception as e:
            logger.error(f"Feature preparation failed: {e}")
            return np.array([]), np.array([])
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = df.copy()
            
            # 移动平均线
            df['ma5'] = df['close'].rolling(window=5).mean()
            df['ma10'] = df['close'].rolling(window=10).mean()
            df['ma20'] = df['close'].rolling(window=20).mean()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['macd'] = exp1 - exp2
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            
            # 布林带
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
            # 价格和成交量变化
            df['price_change'] = df['close'].pct_change()
            df['volume_change'] = df['volume'].pct_change()
            
            return df
            
        except Exception as e:
            logger.error(f"Technical indicators calculation failed: {e}")
            return df
    
    async def _get_or_train_model(
        self, 
        symbol: str, 
        features: np.ndarray, 
        targets: np.ndarray,
        model_type: str
    ) -> Tuple[Any, Any]:
        """获取或训练模型"""
        try:
            model_key = f"{symbol}_{model_type}"
            model_path = os.path.join(self.models_dir, f"{model_key}.joblib")
            scaler_path = os.path.join(self.scalers_dir, f"{model_key}_scaler.joblib")
            
            # 检查是否有缓存的模型
            if (model_key in self.models_cache and 
                model_key in self.scalers_cache):
                return self.models_cache[model_key], self.scalers_cache[model_key]
            
            # 检查是否有保存的模型文件
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                try:
                    model = joblib.load(model_path)
                    scaler = joblib.load(scaler_path)
                    
                    # 缓存模型
                    self.models_cache[model_key] = model
                    self.scalers_cache[model_key] = scaler
                    
                    return model, scaler
                except Exception as e:
                    logger.warning(f"Failed to load saved model: {e}")
            
            # 训练新模型
            model, scaler = self._train_model(features, targets, model_type)
            
            # 保存模型
            try:
                joblib.dump(model, model_path)
                joblib.dump(scaler, scaler_path)
            except Exception as e:
                logger.warning(f"Failed to save model: {e}")
            
            # 缓存模型
            self.models_cache[model_key] = model
            self.scalers_cache[model_key] = scaler
            
            return model, scaler
            
        except Exception as e:
            logger.error(f"Model training/loading failed: {e}")
            # 返回简单的线性回归模型作为后备
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(features)
            model = LinearRegression()
            model.fit(X_scaled, targets)
            return model, scaler
    
    def _train_model(
        self, 
        features: np.ndarray, 
        targets: np.ndarray, 
        model_type: str
    ) -> Tuple[Any, Any]:
        """训练模型"""
        try:
            # 数据标准化
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(features)
            
            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, targets, test_size=0.2, random_state=42
            )
            
            # 选择模型
            if model_type == 'random_forest':
                model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=-1
                )
            elif model_type == 'gradient_boosting':
                model = GradientBoostingRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42
                )
            else:  # 默认使用线性回归
                model = LinearRegression()
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 评估模型
            y_pred = model.predict(X_test)
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            logger.info(f"Model trained - MSE: {mse:.4f}, MAE: {mae:.4f}, R2: {r2:.4f}")
            
            return model, scaler
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            # 返回简单模型作为后备
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(features)
            model = LinearRegression()
            model.fit(X_scaled, targets)
            return model, scaler
    
    def _generate_predictions(
        self, 
        model: Any, 
        scaler: Any, 
        features: np.ndarray, 
        days_ahead: int
    ) -> List[Dict[str, Any]]:
        """生成预测"""
        try:
            predictions = []
            
            # 使用最近的特征数据进行预测
            last_features = features[-1:].copy()
            
            for day in range(days_ahead):
                # 标准化特征
                X_scaled = scaler.transform(last_features)
                
                # 预测
                pred_price = model.predict(X_scaled)[0]
                
                # 计算预测日期
                pred_date = datetime.now() + timedelta(days=day + 1)
                
                predictions.append({
                    'date': pred_date.strftime('%Y-%m-%d'),
                    'predicted_price': round(float(pred_price), 2),
                    'day_ahead': day + 1
                })
                
                # 更新特征（简单方法：使用预测价格更新部分特征）
                # 这里可以实现更复杂的特征更新逻辑
                
            return predictions
            
        except Exception as e:
            logger.error(f"Prediction generation failed: {e}")
            return []
    
    def _calculate_confidence(
        self, 
        model: Any, 
        features: np.ndarray, 
        targets: np.ndarray
    ) -> float:
        """计算预测置信度"""
        try:
            # 使用交叉验证计算模型性能
            from sklearn.model_selection import cross_val_score
            
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(features)
            
            # 计算R2分数
            scores = cross_val_score(model, X_scaled, targets, cv=5, scoring='r2')
            confidence = max(0, min(1, np.mean(scores)))  # 限制在0-1之间
            
            return round(confidence, 3)
            
        except Exception as e:
            logger.error(f"Confidence calculation failed: {e}")
            return 0.5  # 默认置信度
    
    async def _get_stock_pool(self, market: str) -> List[str]:
        """获取股票池"""
        try:
            # 这里应该从JQData获取股票列表
            # 暂时返回一些示例股票
            if market == 'sz':
                return ['000001.XSHE', '000002.XSHE', '000858.XSHE', '002415.XSHE']
            elif market == 'sh':
                return ['600000.XSHG', '600036.XSHG', '600519.XSHG', '600887.XSHG']
            else:
                return [
                    '000001.XSHE', '000002.XSHE', '000858.XSHE', '002415.XSHE',
                    '600000.XSHG', '600036.XSHG', '600519.XSHG', '600887.XSHG'
                ]
                
        except Exception as e:
            logger.error(f"Failed to get stock pool: {e}")
            return []
    
    async def _calculate_stock_score(
        self, 
        symbol: str, 
        criteria: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """计算股票评分"""
        try:
            # 获取股票基本面数据
            fundamental_data = await self._get_fundamental_data(symbol)
            
            if not fundamental_data:
                return None
            
            # 计算各项评分
            scores = {
                'valuation_score': self._calculate_valuation_score(fundamental_data),
                'growth_score': self._calculate_growth_score(fundamental_data),
                'profitability_score': self._calculate_profitability_score(fundamental_data),
                'financial_health_score': self._calculate_financial_health_score(fundamental_data),
                'technical_score': await self._calculate_technical_score(symbol)
            }
            
            # 权重配置
            weights = criteria.get('weights', {}) if criteria else {}
            default_weights = {
                'valuation_score': 0.2,
                'growth_score': 0.25,
                'profitability_score': 0.2,
                'financial_health_score': 0.15,
                'technical_score': 0.2
            }
            
            # 计算总分
            total_score = 0
            for score_type, score_value in scores.items():
                weight = weights.get(score_type, default_weights[score_type])
                total_score += score_value * weight
            
            return {
                'total_score': round(total_score, 2),
                'individual_scores': scores,
                'weights': {**default_weights, **weights}
            }
            
        except Exception as e:
            logger.error(f"Stock scoring failed for {symbol}: {e}")
            return None
    
    async def _get_fundamental_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取基本面数据"""
        try:
            # 这里应该从JQData获取基本面数据
            # 暂时返回模拟数据
            return {
                'pe_ratio': 15.5,
                'pb_ratio': 1.2,
                'roe': 0.15,
                'debt_to_equity': 0.4,
                'revenue_growth': 0.12,
                'profit_growth': 0.18,
                'current_ratio': 1.8,
                'quick_ratio': 1.2
            }
            
        except Exception as e:
            logger.error(f"Failed to get fundamental data for {symbol}: {e}")
            return None
    
    def _calculate_valuation_score(self, data: Dict[str, Any]) -> float:
        """计算估值评分"""
        try:
            pe_ratio = data.get('pe_ratio', 20)
            pb_ratio = data.get('pb_ratio', 2)
            
            # PE评分（越低越好）
            pe_score = max(0, min(100, 100 - (pe_ratio - 10) * 2))
            
            # PB评分（越低越好）
            pb_score = max(0, min(100, 100 - (pb_ratio - 1) * 20))
            
            return (pe_score + pb_score) / 2
            
        except Exception as e:
            logger.error(f"Valuation score calculation failed: {e}")
            return 50.0
    
    def _calculate_growth_score(self, data: Dict[str, Any]) -> float:
        """计算成长性评分"""
        try:
            revenue_growth = data.get('revenue_growth', 0.1)
            profit_growth = data.get('profit_growth', 0.1)
            
            # 收入增长评分
            revenue_score = min(100, max(0, revenue_growth * 500))
            
            # 利润增长评分
            profit_score = min(100, max(0, profit_growth * 400))
            
            return (revenue_score + profit_score) / 2
            
        except Exception as e:
            logger.error(f"Growth score calculation failed: {e}")
            return 50.0
    
    def _calculate_profitability_score(self, data: Dict[str, Any]) -> float:
        """计算盈利能力评分"""
        try:
            roe = data.get('roe', 0.1)
            
            # ROE评分
            roe_score = min(100, max(0, roe * 500))
            
            return roe_score
            
        except Exception as e:
            logger.error(f"Profitability score calculation failed: {e}")
            return 50.0
    
    def _calculate_financial_health_score(self, data: Dict[str, Any]) -> float:
        """计算财务健康评分"""
        try:
            debt_to_equity = data.get('debt_to_equity', 0.5)
            current_ratio = data.get('current_ratio', 1.5)
            
            # 负债率评分（越低越好）
            debt_score = max(0, min(100, 100 - debt_to_equity * 100))
            
            # 流动比率评分
            current_score = min(100, max(0, (current_ratio - 1) * 100))
            
            return (debt_score + current_score) / 2
            
        except Exception as e:
            logger.error(f"Financial health score calculation failed: {e}")
            return 50.0
    
    async def _calculate_technical_score(self, symbol: str) -> float:
        """计算技术面评分"""
        try:
            # 获取最近的价格数据
            data = await self._get_training_data(symbol, days=60)
            
            if len(data) < 20:
                return 50.0
            
            # 计算技术指标
            data = self._calculate_technical_indicators(data)
            
            latest = data.iloc[-1]
            
            # RSI评分
            rsi = latest.get('rsi', 50)
            rsi_score = 100 - abs(rsi - 50)  # 接近50分数越高
            
            # 价格相对于移动平均线的位置
            price = latest['close']
            ma20 = latest.get('ma20', price)
            ma_score = min(100, max(0, (price / ma20 - 0.95) * 200))
            
            return (rsi_score + ma_score) / 2
            
        except Exception as e:
            logger.error(f"Technical score calculation failed for {symbol}: {e}")
            return 50.0
    
    async def _get_news_data(self, symbol: str, count: int) -> List[Dict[str, Any]]:
        """获取新闻数据"""
        try:
            # 这里应该从新闻API获取数据
            # 暂时返回模拟数据
            return [
                {
                    'title': f'{symbol}业绩超预期，股价有望上涨',
                    'content': '公司发布最新财报，营收和利润均超出市场预期...',
                    'date': '2024-08-27',
                    'source': '财经新闻'
                },
                {
                    'title': f'{symbol}获得重要合同，前景看好',
                    'content': '公司成功获得大型项目合同，预计将带来可观收益...',
                    'date': '2024-08-26',
                    'source': '行业资讯'
                }
            ]
            
        except Exception as e:
            logger.error(f"Failed to get news data for {symbol}: {e}")
            return []
    
    def _analyze_text_sentiment(self, text: str) -> Dict[str, float]:
        """分析文本情绪"""
        try:
            # 简单的情绪分析实现
            # 在实际应用中，应该使用更专业的NLP库如NLTK、spaCy或transformers
            
            positive_words = ['上涨', '增长', '盈利', '超预期', '看好', '利好', '突破', '创新高']
            negative_words = ['下跌', '亏损', '风险', '下滑', '担忧', '利空', '跌破', '创新低']
            
            positive_count = sum(1 for word in positive_words if word in text)
            negative_count = sum(1 for word in negative_words if word in text)
            
            # 计算情绪分数
            if positive_count + negative_count == 0:
                compound = 0.0
            else:
                compound = (positive_count - negative_count) / (positive_count + negative_count)
            
            return {
                'compound': compound,
                'positive': positive_count / len(text.split()) if text else 0,
                'negative': negative_count / len(text.split()) if text else 0,
                'neutral': 1 - abs(compound)
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return {
                'compound': 0.0,
                'positive': 0.0,
                'negative': 0.0,
                'neutral': 1.0
            }
