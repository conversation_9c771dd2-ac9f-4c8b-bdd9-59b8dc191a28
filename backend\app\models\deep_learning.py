"""
深度学习相关数据模型

定义Transformer、GAN、强化学习等深度学习模型的数据结构
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal as SQLDecimal, Boolean, ForeignKey, JSON, Float, LargeBinary
from sqlalchemy.orm import relationship

from app.models.base import Base


class TransformerModel(Base):
    """Transformer时间序列预测模型"""
    
    __tablename__ = "transformer_models"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模型基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    model_type = Column(String(50), default="transformer")  # transformer, informer, autoformer
    
    # 模型架构配置
    sequence_length = Column(Integer, default=60)  # 输入序列长度
    prediction_length = Column(Integer, default=1)  # 预测长度
    d_model = Column(Integer, default=512)  # 模型维度
    n_heads = Column(Integer, default=8)  # 注意力头数
    n_layers = Column(Integer, default=6)  # 编码器层数
    d_ff = Column(Integer, default=2048)  # 前馈网络维度
    dropout = Column(Float, default=0.1)  # Dropout率
    
    # 训练配置
    batch_size = Column(Integer, default=32)
    learning_rate = Column(Float, default=0.0001)
    epochs = Column(Integer, default=100)
    early_stopping_patience = Column(Integer, default=10)
    
    # 数据配置
    target_columns = Column(JSON)  # 目标列
    feature_columns = Column(JSON)  # 特征列
    normalization_method = Column(String(20), default="minmax")  # 归一化方法
    
    # 模型状态
    status = Column(String(20), default="created")  # created, training, trained, deployed, failed
    training_progress = Column(Integer, default=0)  # 训练进度
    
    # 性能指标
    train_loss = Column(Float)
    val_loss = Column(Float)
    test_mse = Column(Float)
    test_mae = Column(Float)
    test_mape = Column(Float)
    
    # 模型文件
    model_path = Column(String(500))
    model_size = Column(Integer)
    checkpoint_path = Column(String(500))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    trained_at = Column(DateTime)
    
    # 关系
    user = relationship("User", back_populates="transformer_models")
    predictions = relationship("TransformerPrediction", back_populates="model", cascade="all, delete-orphan")


class TransformerPrediction(Base):
    """Transformer预测结果"""
    
    __tablename__ = "transformer_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("transformer_models.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 预测信息
    symbol = Column(String(20), nullable=False, index=True)
    prediction_date = Column(DateTime, nullable=False, index=True)
    target_date = Column(DateTime, nullable=False)
    
    # 预测结果
    predicted_values = Column(JSON)  # 预测值序列
    confidence_intervals = Column(JSON)  # 置信区间
    attention_weights = Column(JSON)  # 注意力权重
    
    # 实际结果（用于评估）
    actual_values = Column(JSON)
    prediction_errors = Column(JSON)
    
    # 元数据
    input_sequence = Column(JSON)  # 输入序列
    model_version = Column(String(20))
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    evaluated_at = Column(DateTime)
    
    # 关系
    model = relationship("TransformerModel", back_populates="predictions")
    user = relationship("User", back_populates="transformer_predictions")


class GANModel(Base):
    """GAN生成对抗网络模型"""
    
    __tablename__ = "gan_models"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 模型基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    gan_type = Column(String(50), default="vanilla")  # vanilla, dcgan, wgan, stylegan
    
    # 生成器配置
    generator_architecture = Column(JSON)
    generator_input_dim = Column(Integer, default=100)  # 噪声维度
    generator_output_dim = Column(Integer)  # 输出维度
    
    # 判别器配置
    discriminator_architecture = Column(JSON)
    discriminator_input_dim = Column(Integer)
    
    # 训练配置
    batch_size = Column(Integer, default=64)
    learning_rate_g = Column(Float, default=0.0002)  # 生成器学习率
    learning_rate_d = Column(Float, default=0.0002)  # 判别器学习率
    beta1 = Column(Float, default=0.5)  # Adam优化器参数
    beta2 = Column(Float, default=0.999)
    epochs = Column(Integer, default=200)
    
    # 损失函数配置
    loss_function = Column(String(20), default="bce")  # bce, wgan, lsgan
    gradient_penalty_weight = Column(Float, default=10.0)  # WGAN-GP参数
    
    # 数据配置
    data_columns = Column(JSON)  # 数据列
    sequence_length = Column(Integer)  # 序列长度（时间序列GAN）
    conditioning_variables = Column(JSON)  # 条件变量
    
    # 模型状态
    status = Column(String(20), default="created")
    training_progress = Column(Integer, default=0)
    
    # 性能指标
    generator_loss = Column(Float)
    discriminator_loss = Column(Float)
    inception_score = Column(Float)  # IS分数
    fid_score = Column(Float)  # FID分数
    
    # 模型文件
    generator_path = Column(String(500))
    discriminator_path = Column(String(500))
    model_size = Column(Integer)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    trained_at = Column(DateTime)
    
    # 关系
    user = relationship("User", back_populates="gan_models")
    generated_data = relationship("GANGeneratedData", back_populates="model", cascade="all, delete-orphan")


class GANGeneratedData(Base):
    """GAN生成的数据"""
    
    __tablename__ = "gan_generated_data"
    
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey("gan_models.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 生成信息
    generation_date = Column(DateTime, nullable=False, index=True)
    generation_type = Column(String(50))  # synthetic_data, anomaly_detection, data_augmentation
    
    # 生成参数
    noise_seed = Column(Integer)  # 随机种子
    conditioning_params = Column(JSON)  # 条件参数
    num_samples = Column(Integer)  # 生成样本数
    
    # 生成结果
    generated_samples = Column(JSON)  # 生成的样本
    quality_metrics = Column(JSON)  # 质量指标
    
    # 用途标记
    purpose = Column(String(50))  # training_augmentation, backtesting, stress_testing
    is_validated = Column(Boolean, default=False)  # 是否已验证
    validation_score = Column(Float)  # 验证分数
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    model = relationship("GANModel", back_populates="generated_data")
    user = relationship("User", back_populates="gan_generated_data")


class RLAgent(Base):
    """强化学习智能体"""
    
    __tablename__ = "rl_agents"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 智能体基本信息
    name = Column(String(100), nullable=False)
    description = Column(Text)
    agent_type = Column(String(50), default="dqn")  # dqn, ddpg, ppo, a3c, sac
    
    # 环境配置
    environment_type = Column(String(50), default="trading")  # trading, portfolio_optimization
    state_space_dim = Column(Integer)  # 状态空间维度
    action_space_dim = Column(Integer)  # 动作空间维度
    action_space_type = Column(String(20), default="discrete")  # discrete, continuous
    
    # 网络架构
    network_architecture = Column(JSON)
    hidden_layers = Column(JSON)  # 隐藏层配置
    activation_function = Column(String(20), default="relu")
    
    # 训练配置
    learning_rate = Column(Float, default=0.001)
    batch_size = Column(Integer, default=32)
    memory_size = Column(Integer, default=10000)  # 经验回放缓冲区大小
    epsilon_start = Column(Float, default=1.0)  # 探索率起始值
    epsilon_end = Column(Float, default=0.01)  # 探索率结束值
    epsilon_decay = Column(Float, default=0.995)  # 探索率衰减
    gamma = Column(Float, default=0.99)  # 折扣因子
    tau = Column(Float, default=0.005)  # 软更新参数
    
    # 奖励函数配置
    reward_function = Column(JSON)  # 奖励函数参数
    risk_penalty_weight = Column(Float, default=0.1)  # 风险惩罚权重
    transaction_cost = Column(Float, default=0.001)  # 交易成本
    
    # 训练状态
    status = Column(String(20), default="created")
    training_episodes = Column(Integer, default=0)
    total_steps = Column(Integer, default=0)
    
    # 性能指标
    average_reward = Column(Float)
    max_reward = Column(Float)
    win_rate = Column(Float)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    
    # 模型文件
    model_path = Column(String(500))
    checkpoint_path = Column(String(500))
    model_size = Column(Integer)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_trained_at = Column(DateTime)
    
    # 关系
    user = relationship("User", back_populates="rl_agents")
    trading_sessions = relationship("RLTradingSession", back_populates="agent", cascade="all, delete-orphan")
    training_episodes_rel = relationship("RLTrainingEpisode", back_populates="agent", cascade="all, delete-orphan")


class RLTradingSession(Base):
    """强化学习交易会话"""
    
    __tablename__ = "rl_trading_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("rl_agents.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # 会话信息
    session_name = Column(String(100))
    session_type = Column(String(20), default="backtest")  # backtest, live, paper
    
    # 交易配置
    initial_capital = Column(SQLDecimal(15, 2), default=100000)
    symbols = Column(JSON)  # 交易标的
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    
    # 会话结果
    final_capital = Column(SQLDecimal(15, 2))
    total_return = Column(Float)
    annual_return = Column(Float)
    volatility = Column(Float)
    sharpe_ratio = Column(Float)
    max_drawdown = Column(Float)
    
    # 交易统计
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    losing_trades = Column(Integer, default=0)
    win_rate = Column(Float)
    avg_trade_return = Column(Float)
    
    # 会话状态
    status = Column(String(20), default="created")  # created, running, completed, failed
    
    # 详细记录
    trade_history = Column(JSON)  # 交易历史
    portfolio_history = Column(JSON)  # 组合历史
    action_history = Column(JSON)  # 动作历史
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # 关系
    agent = relationship("RLAgent", back_populates="trading_sessions")
    user = relationship("User", back_populates="rl_trading_sessions")


class RLTrainingEpisode(Base):
    """强化学习训练回合"""
    
    __tablename__ = "rl_training_episodes"
    
    id = Column(Integer, primary_key=True, index=True)
    agent_id = Column(Integer, ForeignKey("rl_agents.id"), nullable=False, index=True)
    
    # 回合信息
    episode_number = Column(Integer, nullable=False)
    episode_steps = Column(Integer)
    episode_reward = Column(Float)
    
    # 训练指标
    loss = Column(Float)
    epsilon = Column(Float)  # 当前探索率
    learning_rate = Column(Float)  # 当前学习率
    
    # 性能指标
    portfolio_value = Column(Float)
    trades_count = Column(Integer)
    win_rate = Column(Float)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    agent = relationship("RLAgent", back_populates="training_episodes_rel")
