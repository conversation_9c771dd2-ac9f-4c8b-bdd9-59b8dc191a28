"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtensionCategory = void 0;
var ExtensionCategory;
(function (ExtensionCategory) {
    /**
     * <zh/> 节点元素
     *
     * <en/> Node element
     */
    ExtensionCategory["NODE"] = "node";
    /**
     * <zh/> 边元素
     *
     * <en/> Edge element
     */
    ExtensionCategory["EDGE"] = "edge";
    /**
     * <zh/> 组合元素
     *
     * <en/> Combination element
     */
    ExtensionCategory["COMBO"] = "combo";
    /**
     * <zh/> 主题
     *
     * <en/> Theme
     */
    ExtensionCategory["THEME"] = "theme";
    /**
     * <zh/> 色板
     *
     * <en/> Palette
     */
    ExtensionCategory["PALETTE"] = "palette";
    /**
     * <zh/> 布局
     *
     * <en/> Layout
     */
    ExtensionCategory["LAYOUT"] = "layout";
    /**
     * <zh/> 交互
     *
     * <en/> Behavior
     */
    ExtensionCategory["BEHAVIOR"] = "behavior";
    /**
     * <zh/> 插件
     *
     * <en/> Plugin
     */
    ExtensionCategory["PLUGIN"] = "plugin";
    /**
     * <zh/> 动画
     *
     * <en/> Animation
     */
    ExtensionCategory["ANIMATION"] = "animation";
    /**
     * <zh/> 数据转换
     *
     * <en/> Data transform
     */
    ExtensionCategory["TRANSFORM"] = "transform";
    /**
     * <zh/> 图形
     *
     * <en/> Shape
     */
    ExtensionCategory["SHAPE"] = "shape";
})(ExtensionCategory || (exports.ExtensionCategory = ExtensionCategory = {}));
//# sourceMappingURL=registry.js.map