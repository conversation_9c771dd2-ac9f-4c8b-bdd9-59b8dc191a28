import { litelib, corelib, plotlib, graphlib, geolib, stdlib } from './lib';
import { extend, Runtime } from './api';
export { litelib, corelib, plotlib, graphlib, geolib, stdlib };
export * from './exports';
/**
 * G2 standard library initial all the libs except 3D and auto.
 */
const library = Object.assign({}, stdlib());
export const Chart = extend(Runtime, library);
//# sourceMappingURL=index.js.map