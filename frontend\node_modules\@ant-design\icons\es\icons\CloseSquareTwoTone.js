import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/CloseSquareTwoTone";
import AntdIcon from "../components/AntdIcon";
var CloseSquareTwoTone = function CloseSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseSquareTwoToneSvg
  }));
};

/**![close-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMTYzLjktNDczLjlBNy45NSA3Ljk1IDAgMDEzNTQgMzUzaDU4LjljNC43IDAgOS4yIDIuMSAxMi4zIDUuN0w1MTIgNDYyLjJsODYuOC0xMDMuNWMzLTMuNiA3LjUtNS43IDEyLjMtNS43SDY3MGM2LjggMCAxMC41IDcuOSA2LjEgMTMuMUw1NTMuOCA1MTJsMTIyLjMgMTQ1LjljNC40IDUuMi43IDEzLjEtNi4xIDEzLjFoLTU4LjljLTQuNyAwLTkuMi0yLjEtMTIuMy01LjdMNTEyIDU2MS44bC04Ni44IDEwMy41Yy0zIDMuNi03LjUgNS43LTEyLjMgNS43SDM1NGMtNi44IDAtMTAuNS03LjktNi4xLTEzLjFMNDcwLjIgNTEyIDM0Ny45IDM2Ni4xeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNMzU0IDY3MWg1OC45YzQuOCAwIDkuMy0yLjEgMTIuMy01LjdMNTEyIDU2MS44bDg2LjggMTAzLjVjMy4xIDMuNiA3LjYgNS43IDEyLjMgNS43SDY3MGM2LjggMCAxMC41LTcuOSA2LjEtMTMuMUw1NTMuOCA1MTJsMTIyLjMtMTQ1LjljNC40LTUuMi43LTEzLjEtNi4xLTEzLjFoLTU4LjljLTQuOCAwLTkuMyAyLjEtMTIuMyA1LjdMNTEyIDQ2Mi4ybC04Ni44LTEwMy41Yy0zLjEtMy42LTcuNi01LjctMTIuMy01LjdIMzU0Yy02LjggMC0xMC41IDcuOS02LjEgMTMuMUw0NzAuMiA1MTIgMzQ3LjkgNjU3LjlBNy45NSA3Ljk1IDAgMDAzNTQgNjcxeiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseSquareTwoTone';
}
export default RefIcon;