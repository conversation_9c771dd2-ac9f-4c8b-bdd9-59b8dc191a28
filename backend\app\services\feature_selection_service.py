"""
自动特征选择服务

提供多种特征选择方法，包括统计方法、基于模型的方法、包装器方法等
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sklearn.feature_selection import (
    SelectKBest, SelectPercentile, SelectFromModel,
    RFE, RFECV, VarianceThreshold, mutual_info_classif, mutual_info_regression,
    f_classif, f_regression, chi2
)
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LassoCV, RidgeCV, LogisticRegressionCV
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

from app.core.logging import logger
from app.models.automl import FeatureSelection


class UnivariateFeatureSelector:
    """单变量特征选择器"""
    
    def __init__(self, problem_type: str = "classification"):
        self.problem_type = problem_type
        self.selectors = {}
        self._initialize_selectors()
    
    def _initialize_selectors(self):
        """初始化选择器"""
        if self.problem_type == "classification":
            self.selectors = {
                'f_classif': f_classif,
                'mutual_info': mutual_info_classif,
                'chi2': chi2
            }
        else:  # regression
            self.selectors = {
                'f_regression': f_regression,
                'mutual_info': mutual_info_regression
            }
    
    def select_features(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        method: str = "f_classif",
        k: int = 10,
        percentile: float = None
    ) -> Dict[str, Any]:
        """选择特征"""
        try:
            if method not in self.selectors:
                method = list(self.selectors.keys())[0]
            
            score_func = self.selectors[method]
            
            # 处理分类变量
            X_processed = self._preprocess_features(X)
            
            if percentile:
                selector = SelectPercentile(score_func=score_func, percentile=percentile)
            else:
                selector = SelectKBest(score_func=score_func, k=min(k, X_processed.shape[1]))
            
            # 拟合选择器
            X_selected = selector.fit_transform(X_processed, y)
            
            # 获取选择的特征
            selected_features = X.columns[selector.get_support()].tolist()
            feature_scores = selector.scores_
            feature_pvalues = getattr(selector, 'pvalues_', None)
            
            # 计算特征排名
            feature_rankings = np.argsort(feature_scores)[::-1]
            
            return {
                'selected_features': selected_features,
                'feature_scores': dict(zip(X.columns, feature_scores)),
                'feature_pvalues': dict(zip(X.columns, feature_pvalues)) if feature_pvalues is not None else None,
                'feature_rankings': dict(zip(X.columns, feature_rankings)),
                'selected_feature_count': len(selected_features),
                'method': method,
                'selector': selector
            }
            
        except Exception as e:
            logger.error(f"单变量特征选择失败: {e}")
            return {
                'selected_features': X.columns.tolist(),
                'feature_scores': {},
                'feature_rankings': {},
                'selected_feature_count': X.shape[1],
                'method': method,
                'error': str(e)
            }
    
    def _preprocess_features(self, X: pd.DataFrame) -> np.ndarray:
        """预处理特征"""
        X_processed = X.copy()
        
        # 处理分类变量
        for col in X_processed.columns:
            if X_processed[col].dtype == 'object':
                le = LabelEncoder()
                X_processed[col] = le.fit_transform(X_processed[col].astype(str))
        
        # 处理缺失值
        X_processed = X_processed.fillna(X_processed.mean())
        
        return X_processed.values


class ModelBasedFeatureSelector:
    """基于模型的特征选择器"""
    
    def __init__(self, problem_type: str = "classification"):
        self.problem_type = problem_type
        self.models = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """初始化模型"""
        if self.problem_type == "classification":
            self.models = {
                'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
                'lasso': LogisticRegressionCV(penalty='l1', solver='liblinear', random_state=42),
                'ridge': LogisticRegressionCV(penalty='l2', random_state=42)
            }
        else:  # regression
            self.models = {
                'random_forest': RandomForestRegressor(n_estimators=100, random_state=42),
                'lasso': LassoCV(random_state=42),
                'ridge': RidgeCV()
            }
    
    def select_features(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        method: str = "random_forest",
        threshold: str = "mean",
        max_features: int = None
    ) -> Dict[str, Any]:
        """基于模型选择特征"""
        try:
            if method not in self.models:
                method = "random_forest"
            
            model = self.models[method]
            
            # 预处理数据
            X_processed = self._preprocess_features(X)
            
            # 训练模型
            model.fit(X_processed, y)
            
            # 获取特征重要性
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
            elif hasattr(model, 'coef_'):
                importances = np.abs(model.coef_).flatten()
            else:
                importances = np.ones(X.shape[1])
            
            # 选择特征
            if max_features:
                # 选择前N个重要特征
                top_indices = np.argsort(importances)[::-1][:max_features]
                selected_mask = np.zeros(len(importances), dtype=bool)
                selected_mask[top_indices] = True
            else:
                # 使用阈值选择
                selector = SelectFromModel(model, threshold=threshold, prefit=True)
                selected_mask = selector.get_support()
            
            selected_features = X.columns[selected_mask].tolist()
            
            # 计算特征排名
            feature_rankings = np.argsort(importances)[::-1]
            
            return {
                'selected_features': selected_features,
                'feature_importances': dict(zip(X.columns, importances)),
                'feature_rankings': dict(zip(X.columns, feature_rankings)),
                'selected_feature_count': len(selected_features),
                'method': method,
                'threshold': threshold,
                'model': model
            }
            
        except Exception as e:
            logger.error(f"基于模型的特征选择失败: {e}")
            return {
                'selected_features': X.columns.tolist(),
                'feature_importances': {},
                'feature_rankings': {},
                'selected_feature_count': X.shape[1],
                'method': method,
                'error': str(e)
            }
    
    def _preprocess_features(self, X: pd.DataFrame) -> np.ndarray:
        """预处理特征"""
        X_processed = X.copy()
        
        # 处理分类变量
        for col in X_processed.columns:
            if X_processed[col].dtype == 'object':
                le = LabelEncoder()
                X_processed[col] = le.fit_transform(X_processed[col].astype(str))
        
        # 处理缺失值
        X_processed = X_processed.fillna(X_processed.mean())
        
        # 标准化（对于线性模型）
        if hasattr(self.models.get('lasso'), 'penalty') or hasattr(self.models.get('ridge'), 'penalty'):
            scaler = StandardScaler()
            X_processed = pd.DataFrame(
                scaler.fit_transform(X_processed),
                columns=X_processed.columns,
                index=X_processed.index
            )
        
        return X_processed.values


class WrapperFeatureSelector:
    """包装器特征选择器"""
    
    def __init__(self, problem_type: str = "classification"):
        self.problem_type = problem_type
        self.estimators = {}
        self._initialize_estimators()
    
    def _initialize_estimators(self):
        """初始化估计器"""
        if self.problem_type == "classification":
            self.estimators = {
                'random_forest': RandomForestClassifier(n_estimators=50, random_state=42),
                'logistic': LogisticRegressionCV(random_state=42, max_iter=1000)
            }
        else:  # regression
            self.estimators = {
                'random_forest': RandomForestRegressor(n_estimators=50, random_state=42),
                'ridge': RidgeCV()
            }
    
    def recursive_feature_elimination(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        estimator_name: str = "random_forest",
        n_features_to_select: int = None,
        step: int = 1,
        cv: int = 5
    ) -> Dict[str, Any]:
        """递归特征消除"""
        try:
            if estimator_name not in self.estimators:
                estimator_name = list(self.estimators.keys())[0]
            
            estimator = self.estimators[estimator_name]
            
            # 预处理数据
            X_processed = self._preprocess_features(X)
            
            # 确定要选择的特征数量
            if n_features_to_select is None:
                n_features_to_select = max(1, X.shape[1] // 2)
            
            # 使用交叉验证的RFE
            if cv > 1:
                selector = RFECV(
                    estimator=estimator,
                    step=step,
                    cv=cv,
                    scoring='accuracy' if self.problem_type == 'classification' else 'r2',
                    n_jobs=-1
                )
            else:
                selector = RFE(
                    estimator=estimator,
                    n_features_to_select=n_features_to_select,
                    step=step
                )
            
            # 拟合选择器
            selector.fit(X_processed, y)
            
            # 获取选择的特征
            selected_features = X.columns[selector.support_].tolist()
            feature_rankings = selector.ranking_
            
            # 计算交叉验证分数（如果使用RFECV）
            cv_scores = getattr(selector, 'grid_scores_', None)
            
            return {
                'selected_features': selected_features,
                'feature_rankings': dict(zip(X.columns, feature_rankings)),
                'selected_feature_count': len(selected_features),
                'optimal_feature_count': getattr(selector, 'n_features_', len(selected_features)),
                'cv_scores': cv_scores,
                'method': 'RFE',
                'estimator': estimator_name,
                'selector': selector
            }
            
        except Exception as e:
            logger.error(f"递归特征消除失败: {e}")
            return {
                'selected_features': X.columns.tolist(),
                'feature_rankings': {},
                'selected_feature_count': X.shape[1],
                'method': 'RFE',
                'error': str(e)
            }
    
    def forward_selection(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        estimator_name: str = "random_forest",
        max_features: int = None,
        cv: int = 5
    ) -> Dict[str, Any]:
        """前向选择"""
        try:
            if estimator_name not in self.estimators:
                estimator_name = list(self.estimators.keys())[0]
            
            estimator = self.estimators[estimator_name]
            
            # 预处理数据
            X_processed = self._preprocess_features(X)
            
            if max_features is None:
                max_features = min(20, X.shape[1])
            
            selected_features = []
            remaining_features = list(X.columns)
            best_scores = []
            
            # 前向选择过程
            for i in range(min(max_features, len(remaining_features))):
                best_score = -np.inf
                best_feature = None
                
                for feature in remaining_features:
                    current_features = selected_features + [feature]
                    X_subset = X[current_features]
                    X_subset_processed = self._preprocess_features(X_subset)
                    
                    # 交叉验证评分
                    scores = cross_val_score(
                        estimator, X_subset_processed, y, cv=cv,
                        scoring='accuracy' if self.problem_type == 'classification' else 'r2'
                    )
                    score = scores.mean()
                    
                    if score > best_score:
                        best_score = score
                        best_feature = feature
                
                if best_feature:
                    selected_features.append(best_feature)
                    remaining_features.remove(best_feature)
                    best_scores.append(best_score)
                else:
                    break
            
            return {
                'selected_features': selected_features,
                'feature_scores': dict(zip(selected_features, best_scores)),
                'selected_feature_count': len(selected_features),
                'selection_path': selected_features,
                'score_history': best_scores,
                'method': 'forward_selection',
                'estimator': estimator_name
            }
            
        except Exception as e:
            logger.error(f"前向选择失败: {e}")
            return {
                'selected_features': X.columns.tolist()[:10],  # 返回前10个特征作为默认
                'selected_feature_count': min(10, X.shape[1]),
                'method': 'forward_selection',
                'error': str(e)
            }
    
    def _preprocess_features(self, X: pd.DataFrame) -> np.ndarray:
        """预处理特征"""
        X_processed = X.copy()
        
        # 处理分类变量
        for col in X_processed.columns:
            if X_processed[col].dtype == 'object':
                le = LabelEncoder()
                X_processed[col] = le.fit_transform(X_processed[col].astype(str))
        
        # 处理缺失值
        X_processed = X_processed.fillna(X_processed.mean())
        
        return X_processed.values


class FeatureEngineeringService:
    """特征工程服务"""
    
    def __init__(self):
        pass
    
    def create_polynomial_features(
        self,
        X: pd.DataFrame,
        degree: int = 2,
        interaction_only: bool = False,
        include_bias: bool = False
    ) -> pd.DataFrame:
        """创建多项式特征"""
        try:
            from sklearn.preprocessing import PolynomialFeatures
            
            # 只对数值特征创建多项式特征
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            X_numeric = X[numeric_cols]
            
            if X_numeric.empty:
                return X
            
            poly = PolynomialFeatures(
                degree=degree,
                interaction_only=interaction_only,
                include_bias=include_bias
            )
            
            X_poly = poly.fit_transform(X_numeric)
            feature_names = poly.get_feature_names_out(numeric_cols)
            
            X_poly_df = pd.DataFrame(X_poly, columns=feature_names, index=X.index)
            
            # 合并原始非数值特征
            non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns
            if len(non_numeric_cols) > 0:
                X_result = pd.concat([X[non_numeric_cols], X_poly_df], axis=1)
            else:
                X_result = X_poly_df
            
            return X_result
            
        except Exception as e:
            logger.error(f"多项式特征创建失败: {e}")
            return X
    
    def create_interaction_features(
        self,
        X: pd.DataFrame,
        max_interactions: int = 2,
        feature_pairs: List[Tuple[str, str]] = None
    ) -> pd.DataFrame:
        """创建交互特征"""
        try:
            X_result = X.copy()
            
            # 只对数值特征创建交互特征
            numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_cols) < 2:
                return X_result
            
            if feature_pairs is None:
                # 自动生成特征对
                from itertools import combinations
                feature_pairs = list(combinations(numeric_cols[:min(10, len(numeric_cols))], 2))
            
            # 限制交互特征数量
            feature_pairs = feature_pairs[:max_interactions]
            
            for col1, col2 in feature_pairs:
                if col1 in X.columns and col2 in X.columns:
                    # 乘法交互
                    interaction_name = f"{col1}_x_{col2}"
                    X_result[interaction_name] = X[col1] * X[col2]
                    
                    # 除法交互（避免除零）
                    if (X[col2] != 0).all():
                        division_name = f"{col1}_div_{col2}"
                        X_result[division_name] = X[col1] / X[col2]
            
            return X_result
            
        except Exception as e:
            logger.error(f"交互特征创建失败: {e}")
            return X
    
    def create_statistical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """创建统计特征"""
        try:
            X_result = X.copy()
            
            # 只对数值特征创建统计特征
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) < 2:
                return X_result
            
            X_numeric = X[numeric_cols]
            
            # 行统计特征
            X_result['row_mean'] = X_numeric.mean(axis=1)
            X_result['row_std'] = X_numeric.std(axis=1)
            X_result['row_min'] = X_numeric.min(axis=1)
            X_result['row_max'] = X_numeric.max(axis=1)
            X_result['row_median'] = X_numeric.median(axis=1)
            X_result['row_sum'] = X_numeric.sum(axis=1)
            
            # 非零特征计数
            X_result['non_zero_count'] = (X_numeric != 0).sum(axis=1)
            
            return X_result
            
        except Exception as e:
            logger.error(f"统计特征创建失败: {e}")
            return X


class FeatureSelectionService:
    """特征选择服务"""
    
    def __init__(self):
        self.univariate_selector = None
        self.model_based_selector = None
        self.wrapper_selector = None
        self.feature_engineering = FeatureEngineeringService()
    
    async def perform_feature_selection(
        self,
        experiment_id: int,
        X: pd.DataFrame,
        y: pd.Series,
        problem_type: str,
        selection_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """执行特征选择"""
        try:
            start_time = datetime.utcnow()
            
            # 初始化选择器
            self.univariate_selector = UnivariateFeatureSelector(problem_type)
            self.model_based_selector = ModelBasedFeatureSelector(problem_type)
            self.wrapper_selector = WrapperFeatureSelector(problem_type)
            
            # 原始特征信息
            original_features = X.columns.tolist()
            original_feature_count = len(original_features)
            
            # 特征工程（如果启用）
            if selection_config.get('enable_feature_engineering', False):
                X = await self._perform_feature_engineering(X, selection_config)
            
            # 执行多种特征选择方法
            selection_results = {}
            
            # 1. 单变量选择
            if selection_config.get('enable_univariate', True):
                univariate_result = self.univariate_selector.select_features(
                    X, y,
                    method=selection_config.get('univariate_method', 'f_classif'),
                    k=selection_config.get('univariate_k', 20)
                )
                selection_results['univariate'] = univariate_result
            
            # 2. 基于模型的选择
            if selection_config.get('enable_model_based', True):
                model_based_result = self.model_based_selector.select_features(
                    X, y,
                    method=selection_config.get('model_based_method', 'random_forest'),
                    threshold=selection_config.get('model_based_threshold', 'mean')
                )
                selection_results['model_based'] = model_based_result
            
            # 3. 包装器方法
            if selection_config.get('enable_wrapper', False):
                wrapper_result = self.wrapper_selector.recursive_feature_elimination(
                    X, y,
                    estimator_name=selection_config.get('wrapper_estimator', 'random_forest'),
                    n_features_to_select=selection_config.get('wrapper_n_features', 15),
                    cv=selection_config.get('wrapper_cv', 3)
                )
                selection_results['wrapper'] = wrapper_result
            
            # 集成选择结果
            final_result = await self._ensemble_selection_results(
                selection_results, selection_config
            )
            
            # 计算性能提升
            baseline_score, selected_score = await self._evaluate_feature_selection(
                X, y, final_result['selected_features'], problem_type
            )
            
            # 保存特征选择记录
            selection_time = (datetime.utcnow() - start_time).total_seconds()
            
            feature_selection = FeatureSelection(
                experiment_id=experiment_id,
                selection_method="ensemble",
                selection_config=selection_config,
                original_features=original_features,
                original_feature_count=original_feature_count,
                selected_features=final_result['selected_features'],
                selected_feature_count=len(final_result['selected_features']),
                feature_scores=final_result.get('feature_scores', {}),
                feature_rankings=final_result.get('feature_rankings', {}),
                baseline_score=baseline_score,
                selected_score=selected_score,
                score_improvement=selected_score - baseline_score,
                selection_time_seconds=selection_time,
                status="completed"
            )
            
            db.add(feature_selection)
            await db.commit()
            
            return {
                'success': True,
                'selection_id': feature_selection.id,
                'selected_features': final_result['selected_features'],
                'feature_count': len(final_result['selected_features']),
                'score_improvement': selected_score - baseline_score,
                'selection_results': selection_results,
                'final_result': final_result
            }
            
        except Exception as e:
            logger.error(f"特征选择失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'selected_features': X.columns.tolist()
            }
    
    async def _perform_feature_engineering(
        self,
        X: pd.DataFrame,
        config: Dict[str, Any]
    ) -> pd.DataFrame:
        """执行特征工程"""
        try:
            X_engineered = X.copy()
            
            # 多项式特征
            if config.get('enable_polynomial_features', False):
                X_engineered = self.feature_engineering.create_polynomial_features(
                    X_engineered,
                    degree=config.get('polynomial_degree', 2),
                    interaction_only=config.get('polynomial_interaction_only', True)
                )
            
            # 交互特征
            if config.get('enable_interaction_features', True):
                X_engineered = self.feature_engineering.create_interaction_features(
                    X_engineered,
                    max_interactions=config.get('max_feature_interactions', 10)
                )
            
            # 统计特征
            if config.get('enable_statistical_features', True):
                X_engineered = self.feature_engineering.create_statistical_features(
                    X_engineered
                )
            
            return X_engineered
            
        except Exception as e:
            logger.error(f"特征工程失败: {e}")
            return X
    
    async def _ensemble_selection_results(
        self,
        selection_results: Dict[str, Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """集成选择结果"""
        try:
            # 收集所有选择的特征
            all_selected_features = set()
            feature_votes = {}
            
            for method, result in selection_results.items():
                if 'selected_features' in result:
                    features = result['selected_features']
                    all_selected_features.update(features)
                    
                    for feature in features:
                        feature_votes[feature] = feature_votes.get(feature, 0) + 1
            
            # 根据投票数排序特征
            sorted_features = sorted(
                feature_votes.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # 选择最终特征
            max_features = config.get('max_final_features', 20)
            min_votes = config.get('min_votes', 1)
            
            final_features = []
            for feature, votes in sorted_features:
                if len(final_features) >= max_features:
                    break
                if votes >= min_votes:
                    final_features.append(feature)
            
            # 如果没有足够的特征，添加得分最高的特征
            if len(final_features) < config.get('min_final_features', 5):
                for method, result in selection_results.items():
                    if 'feature_scores' in result:
                        scores = result['feature_scores']
                        sorted_by_score = sorted(
                            scores.items(),
                            key=lambda x: x[1],
                            reverse=True
                        )
                        
                        for feature, score in sorted_by_score:
                            if feature not in final_features:
                                final_features.append(feature)
                                if len(final_features) >= max_features:
                                    break
                        break
            
            return {
                'selected_features': final_features,
                'feature_votes': feature_votes,
                'selection_methods': list(selection_results.keys()),
                'ensemble_method': 'voting'
            }
            
        except Exception as e:
            logger.error(f"集成选择结果失败: {e}")
            # 返回第一个方法的结果作为默认
            first_result = list(selection_results.values())[0]
            return {
                'selected_features': first_result.get('selected_features', []),
                'ensemble_method': 'fallback'
            }
    
    async def _evaluate_feature_selection(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        selected_features: List[str],
        problem_type: str
    ) -> Tuple[float, float]:
        """评估特征选择效果"""
        try:
            from sklearn.model_selection import cross_val_score
            from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
            
            # 选择评估模型
            if problem_type == "classification":
                model = RandomForestClassifier(n_estimators=50, random_state=42)
                scoring = 'accuracy'
            else:
                model = RandomForestRegressor(n_estimators=50, random_state=42)
                scoring = 'r2'
            
            # 预处理数据
            X_processed = X.copy()
            for col in X_processed.columns:
                if X_processed[col].dtype == 'object':
                    le = LabelEncoder()
                    X_processed[col] = le.fit_transform(X_processed[col].astype(str))
            X_processed = X_processed.fillna(X_processed.mean())
            
            # 基线分数（使用所有特征）
            baseline_scores = cross_val_score(model, X_processed, y, cv=3, scoring=scoring)
            baseline_score = baseline_scores.mean()
            
            # 选择特征后的分数
            available_features = [f for f in selected_features if f in X_processed.columns]
            if available_features:
                X_selected = X_processed[available_features]
                selected_scores = cross_val_score(model, X_selected, y, cv=3, scoring=scoring)
                selected_score = selected_scores.mean()
            else:
                selected_score = baseline_score
            
            return baseline_score, selected_score
            
        except Exception as e:
            logger.error(f"特征选择评估失败: {e}")
            return 0.5, 0.5


# 全局特征选择服务实例
feature_selection_service = FeatureSelectionService()
