'use client';

import { Card, Row, Col, Typography, Button, Space, Table, Tag, Divider, Empty } from 'antd';
import { 
  BarChartOutlined,
  CodeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  PlusOutlined,
  EditOutlined,
  RightOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

export default function StrategyPage() {
  const router = useRouter();

  const strategyModules = [
    {
      title: '策略编辑器',
      description: '创建和编辑量化交易策略代码',
      icon: <CodeOutlined className="text-3xl text-orange-500" />,
      path: '/dashboard/strategy/editor',
      features: ['代码编辑', '语法高亮', '实时调试', '策略模板']
    }
  ];

  // 示例策略数据
  const strategies = [
    {
      key: '1',
      name: '双均线策略',
      status: 'running',
      return: '+12.5%',
      profit: '+¥2,500',
      lastUpdate: '2024-01-15 14:30:00',
      description: '基于5日和20日移动平均线的交易策略'
    },
    {
      key: '2',
      name: 'RSI反转策略',
      status: 'stopped',
      return: '-3.2%',
      profit: '-¥640',
      lastUpdate: '2024-01-14 09:15:00',
      description: '基于RSI指标的超买超卖反转策略'
    }
  ];

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'running' ? 'green' : 'red'}>
          {status === 'running' ? (
            <>
              <PlayCircleOutlined /> 运行中
            </>
          ) : (
            <>
              <PauseCircleOutlined /> 已停止
            </>
          )}
        </Tag>
      ),
    },
    {
      title: '收益率',
      dataIndex: 'return',
      key: 'return',
      render: (text: string) => (
        <span className={text.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
          {text.startsWith('+') ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
          {' '}{text}
        </span>
      ),
    },
    {
      title: '盈亏',
      dataIndex: 'profit',
      key: 'profit',
      render: (text: string) => (
        <span className={text.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
          {text}
        </span>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdate',
      key: 'lastUpdate',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={record.status === 'running' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          >
            {record.status === 'running' ? '停止' : '启动'}
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <BarChartOutlined className="mr-3" />
          量化策略
        </Title>
        <Text type="secondary" className="text-lg">
          创建、管理和监控您的量化交易策略
        </Text>
      </div>

      {/* 功能模块 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          功能模块
        </Title>
        <Row gutter={[24, 24]}>
          {strategyModules.map((module, index) => (
            <Col xs={24} lg={12} key={index}>
              <Card 
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => router.push(module.path)}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {module.icon}
                  </div>
                  <div className="flex-1">
                    <Title level={4} className="!mb-2">
                      {module.title}
                    </Title>
                    <Text type="secondary" className="block mb-4">
                      {module.description}
                    </Text>
                    <div className="mb-4">
                      <Space wrap>
                        {module.features.map((feature, idx) => (
                          <span 
                            key={idx}
                            className="px-2 py-1 bg-orange-50 text-orange-600 rounded text-xs"
                          >
                            {feature}
                          </span>
                        ))}
                      </Space>
                    </div>
                    <Button type="link" className="p-0">
                      进入模块 <RightOutlined />
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      <Divider />

      {/* 策略列表 */}
      <Card 
        title="我的策略" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            创建策略
          </Button>
        }
        className="mb-6"
      >
        <Table 
          columns={columns} 
          dataSource={strategies} 
          pagination={false}
          locale={{
            emptyText: (
              <Empty 
                description="暂无策略"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              >
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => router.push('/dashboard/strategy/editor')}
                >
                  创建第一个策略
                </Button>
              </Empty>
            )
          }}
        />
      </Card>

      {/* 快速开始 */}
      <Card className="bg-orange-50 border-orange-200">
        <div className="text-center">
          <Title level={4} className="!mb-2 text-orange-800">
            🎯 开始量化交易
          </Title>
          <Text className="text-orange-700 block mb-4">
            使用我们的策略编辑器创建您的第一个量化交易策略
          </Text>
          <Space>
            <Button 
              type="primary" 
              icon={<CodeOutlined />}
              onClick={() => router.push('/dashboard/strategy/editor')}
            >
              策略编辑器
            </Button>
            <Button>
              查看策略模板
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
}
