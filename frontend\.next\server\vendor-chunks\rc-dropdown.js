"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-dropdown";
exports.ids = ["vendor-chunks/rc-dropdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-dropdown/es/Dropdown.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-dropdown/es/Dropdown.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _rc_component_trigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @rc-component/trigger */ \"(ssr)/./node_modules/@rc-component/trigger/es/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./hooks/useAccessibility */ \"(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js\");\n/* harmony import */ var _Overlay__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Overlay */ \"(ssr)/./node_modules/rc-dropdown/es/Overlay.js\");\n/* harmony import */ var _placements__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./placements */ \"(ssr)/./node_modules/rc-dropdown/es/placements.js\");\n\n\n\n\nvar _excluded = [\"arrow\", \"prefixCls\", \"transitionName\", \"animation\", \"align\", \"placement\", \"placements\", \"getPopupContainer\", \"showAction\", \"hideAction\", \"overlayClassName\", \"overlayStyle\", \"visible\", \"trigger\", \"autoFocus\", \"overlay\", \"children\", \"onVisibleChange\"];\n\n\n\n\n\n\n\nfunction Dropdown(props, ref) {\n  var _children$props;\n  var _props$arrow = props.arrow,\n    arrow = _props$arrow === void 0 ? false : _props$arrow,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-dropdown' : _props$prefixCls,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    align = props.align,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'bottomLeft' : _props$placement,\n    _props$placements = props.placements,\n    placements = _props$placements === void 0 ? _placements__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : _props$placements,\n    getPopupContainer = props.getPopupContainer,\n    showAction = props.showAction,\n    hideAction = props.hideAction,\n    overlayClassName = props.overlayClassName,\n    overlayStyle = props.overlayStyle,\n    visible = props.visible,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    autoFocus = props.autoFocus,\n    overlay = props.overlay,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    otherProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_7___default().useState(),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2),\n    triggerVisible = _React$useState2[0],\n    setTriggerVisible = _React$useState2[1];\n  var mergedVisible = 'visible' in props ? visible : triggerVisible;\n  var triggerRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  var overlayRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  var childRef = react__WEBPACK_IMPORTED_MODULE_7___default().useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_7___default().useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var handleVisibleChange = function handleVisibleChange(newVisible) {\n    setTriggerVisible(newVisible);\n    onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(newVisible);\n  };\n  (0,_hooks_useAccessibility__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n    visible: mergedVisible,\n    triggerRef: childRef,\n    onVisibleChange: handleVisibleChange,\n    autoFocus: autoFocus,\n    overlayRef: overlayRef\n  });\n  var onClick = function onClick(e) {\n    var onOverlayClick = props.onOverlayClick;\n    setTriggerVisible(false);\n    if (onOverlayClick) {\n      onOverlayClick(e);\n    }\n  };\n  var getMenuElement = function getMenuElement() {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().createElement(_Overlay__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n      ref: overlayRef,\n      overlay: overlay,\n      prefixCls: prefixCls,\n      arrow: arrow\n    });\n  };\n  var getMenuElementOrLambda = function getMenuElementOrLambda() {\n    if (typeof overlay === 'function') {\n      return getMenuElement;\n    }\n    return getMenuElement();\n  };\n  var getMinOverlayWidthMatchTrigger = function getMinOverlayWidthMatchTrigger() {\n    var minOverlayWidthMatchTrigger = props.minOverlayWidthMatchTrigger,\n      alignPoint = props.alignPoint;\n    if ('minOverlayWidthMatchTrigger' in props) {\n      return minOverlayWidthMatchTrigger;\n    }\n    return !alignPoint;\n  };\n  var getOpenClassName = function getOpenClassName() {\n    var openClassName = props.openClassName;\n    if (openClassName !== undefined) {\n      return openClassName;\n    }\n    return \"\".concat(prefixCls, \"-open\");\n  };\n  var childrenNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().cloneElement(children, {\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()((_children$props = children.props) === null || _children$props === void 0 ? void 0 : _children$props.className, mergedVisible && getOpenClassName()),\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.supportRef)(children) ? (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.composeRef)(childRef, (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_6__.getNodeRef)(children)) : undefined\n  });\n  var triggerHideAction = hideAction;\n  if (!triggerHideAction && trigger.indexOf('contextMenu') !== -1) {\n    triggerHideAction = ['click'];\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().createElement(_rc_component_trigger__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    builtinPlacements: placements\n  }, otherProps, {\n    prefixCls: prefixCls,\n    ref: triggerRef,\n    popupClassName: classnames__WEBPACK_IMPORTED_MODULE_5___default()(overlayClassName, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-show-arrow\"), arrow)),\n    popupStyle: overlayStyle,\n    action: trigger,\n    showAction: showAction,\n    hideAction: triggerHideAction,\n    popupPlacement: placement,\n    popupAlign: align,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupVisible: mergedVisible,\n    stretch: getMinOverlayWidthMatchTrigger() ? 'minWidth' : '',\n    popup: getMenuElementOrLambda(),\n    onPopupVisibleChange: handleVisibleChange,\n    onPopupClick: onClick,\n    getPopupContainer: getPopupContainer\n  }), childrenNode);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7___default().forwardRef(Dropdown));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/Dropdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/Overlay.js":
/*!************************************************!*\
  !*** ./node_modules/rc-dropdown/es/Overlay.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar Overlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.composeRef)(ref, (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.getNodeRef)(overlayNode));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, arrow && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().cloneElement(overlayNode, {\n    ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.supportRef)(overlayNode) ? composedRef : undefined\n  }));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Overlay);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvT3ZlcmxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9FO0FBQ2pCO0FBQ25ELDJCQUEyQixpREFBVTtBQUNyQztBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsOENBQU87QUFDM0I7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxvQkFBb0IsMERBQVUsTUFBTSwwREFBVTtBQUM5QyxzQkFBc0IsMERBQW1CLENBQUMsdURBQWMsOEJBQThCLDBEQUFtQjtBQUN6RztBQUNBLEdBQUcsZ0JBQWdCLHlEQUFrQjtBQUNyQyxTQUFTLDBEQUFVO0FBQ25CLEdBQUc7QUFDSCxDQUFDO0FBQ0QsaUVBQWUsT0FBTyIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yYy1kcm9wZG93bi9lcy9PdmVybGF5LmpzP2FkYTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29tcG9zZVJlZiwgZ2V0Tm9kZVJlZiwgc3VwcG9ydFJlZiB9IGZyb20gXCJyYy11dGlsL2VzL3JlZlwiO1xuaW1wb3J0IFJlYWN0LCB7IGZvcndhcmRSZWYsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG52YXIgT3ZlcmxheSA9IC8qI19fUFVSRV9fKi9mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBvdmVybGF5ID0gcHJvcHMub3ZlcmxheSxcbiAgICBhcnJvdyA9IHByb3BzLmFycm93LFxuICAgIHByZWZpeENscyA9IHByb3BzLnByZWZpeENscztcbiAgdmFyIG92ZXJsYXlOb2RlID0gdXNlTWVtbyhmdW5jdGlvbiAoKSB7XG4gICAgdmFyIG92ZXJsYXlFbGVtZW50O1xuICAgIGlmICh0eXBlb2Ygb3ZlcmxheSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgb3ZlcmxheUVsZW1lbnQgPSBvdmVybGF5KCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG92ZXJsYXlFbGVtZW50ID0gb3ZlcmxheTtcbiAgICB9XG4gICAgcmV0dXJuIG92ZXJsYXlFbGVtZW50O1xuICB9LCBbb3ZlcmxheV0pO1xuICB2YXIgY29tcG9zZWRSZWYgPSBjb21wb3NlUmVmKHJlZiwgZ2V0Tm9kZVJlZihvdmVybGF5Tm9kZSkpO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoUmVhY3QuRnJhZ21lbnQsIG51bGwsIGFycm93ICYmIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IFwiXCIuY29uY2F0KHByZWZpeENscywgXCItYXJyb3dcIilcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jbG9uZUVsZW1lbnQob3ZlcmxheU5vZGUsIHtcbiAgICByZWY6IHN1cHBvcnRSZWYob3ZlcmxheU5vZGUpID8gY29tcG9zZWRSZWYgOiB1bmRlZmluZWRcbiAgfSkpO1xufSk7XG5leHBvcnQgZGVmYXVsdCBPdmVybGF5OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/Overlay.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js":
/*!***************************************************************!*\
  !*** ./node_modules/rc-dropdown/es/hooks/useAccessibility.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAccessibility)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/KeyCode */ \"(ssr)/./node_modules/rc-util/es/KeyCode.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nvar ESC = rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__[\"default\"].ESC,\n  TAB = rc_util_es_KeyCode__WEBPACK_IMPORTED_MODULE_0__[\"default\"].TAB;\nfunction useAccessibility(_ref) {\n  var visible = _ref.visible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus,\n    overlayRef = _ref.overlayRef;\n  var focusMenuRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible) {\n      var _triggerRef$current, _triggerRef$current$f;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 || (_triggerRef$current$f = _triggerRef$current.focus) === null || _triggerRef$current$f === void 0 || _triggerRef$current$f.call(_triggerRef$current);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(false);\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _overlayRef$current;\n    if ((_overlayRef$current = overlayRef.current) !== null && _overlayRef$current !== void 0 && _overlayRef$current.focus) {\n      overlayRef.current.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  react__WEBPACK_IMPORTED_MODULE_2__.useEffect(function () {\n    if (visible) {\n      window.addEventListener(\"keydown\", handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener(\"keydown\", handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/hooks/useAccessibility.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-dropdown/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Dropdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Dropdown */ \"(ssr)/./node_modules/rc-dropdown/es/Dropdown.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Dropdown__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9qcWRhdGEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvaW5kZXguanM/ZTI2YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRHJvcGRvd24gZnJvbSBcIi4vRHJvcGRvd25cIjtcbmV4cG9ydCBkZWZhdWx0IERyb3Bkb3duOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-dropdown/es/placements.js":
/*!***************************************************!*\
  !*** ./node_modules/rc-dropdown/es/placements.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nvar targetOffset = [0, 0];\nvar placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (placements);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtZHJvcGRvd24vZXMvcGxhY2VtZW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWUsVUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL2pxZGF0YS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yYy1kcm9wZG93bi9lcy9wbGFjZW1lbnRzLmpzPzE4ZGMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGF1dG9BZGp1c3RPdmVyZmxvdyA9IHtcbiAgYWRqdXN0WDogMSxcbiAgYWRqdXN0WTogMVxufTtcbnZhciB0YXJnZXRPZmZzZXQgPSBbMCwgMF07XG52YXIgcGxhY2VtZW50cyA9IHtcbiAgdG9wTGVmdDoge1xuICAgIHBvaW50czogWydibCcsICd0bCddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgLTRdLFxuICAgIHRhcmdldE9mZnNldDogdGFyZ2V0T2Zmc2V0XG4gIH0sXG4gIHRvcDoge1xuICAgIHBvaW50czogWydiYycsICd0YyddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgLTRdLFxuICAgIHRhcmdldE9mZnNldDogdGFyZ2V0T2Zmc2V0XG4gIH0sXG4gIHRvcFJpZ2h0OiB7XG4gICAgcG9pbnRzOiBbJ2JyJywgJ3RyJ10sXG4gICAgb3ZlcmZsb3c6IGF1dG9BZGp1c3RPdmVyZmxvdyxcbiAgICBvZmZzZXQ6IFswLCAtNF0sXG4gICAgdGFyZ2V0T2Zmc2V0OiB0YXJnZXRPZmZzZXRcbiAgfSxcbiAgYm90dG9tTGVmdDoge1xuICAgIHBvaW50czogWyd0bCcsICdibCddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgNF0sXG4gICAgdGFyZ2V0T2Zmc2V0OiB0YXJnZXRPZmZzZXRcbiAgfSxcbiAgYm90dG9tOiB7XG4gICAgcG9pbnRzOiBbJ3RjJywgJ2JjJ10sXG4gICAgb3ZlcmZsb3c6IGF1dG9BZGp1c3RPdmVyZmxvdyxcbiAgICBvZmZzZXQ6IFswLCA0XSxcbiAgICB0YXJnZXRPZmZzZXQ6IHRhcmdldE9mZnNldFxuICB9LFxuICBib3R0b21SaWdodDoge1xuICAgIHBvaW50czogWyd0cicsICdiciddLFxuICAgIG92ZXJmbG93OiBhdXRvQWRqdXN0T3ZlcmZsb3csXG4gICAgb2Zmc2V0OiBbMCwgNF0sXG4gICAgdGFyZ2V0T2Zmc2V0OiB0YXJnZXRPZmZzZXRcbiAgfVxufTtcbmV4cG9ydCBkZWZhdWx0IHBsYWNlbWVudHM7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-dropdown/es/placements.js\n");

/***/ })

};
;