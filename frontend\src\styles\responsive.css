/* ============================================================================
 * 响应式设计样式
 * ============================================================================ */

/* 基础响应式断点 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1600px;
}

/* 容器最大宽度 */
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 576px) {
  .container-responsive {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 720px;
    padding: 0 24px;
  }
}

@media (min-width: 992px) {
  .container-responsive {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    max-width: 1140px;
  }
}

@media (min-width: 1600px) {
  .container-responsive {
    max-width: 1520px;
  }
}

/* 响应式网格系统 */
.grid-responsive {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .grid-responsive {
    gap: 20px;
  }
  
  .grid-responsive.cols-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .grid-responsive {
    gap: 24px;
  }
  
  .grid-responsive.cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.cols-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-responsive.cols-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 992px) {
  .grid-responsive.cols-lg-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-responsive.cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-responsive.cols-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .grid-responsive.cols-lg-5 {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .grid-responsive.cols-lg-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (min-width: 1200px) {
  .grid-responsive.cols-xl-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .grid-responsive.cols-xl-8 {
    grid-template-columns: repeat(8, 1fr);
  }
}

/* 响应式字体大小 */
.text-responsive {
  font-size: 14px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 16px;
  }
}

.text-responsive-sm {
  font-size: 12px;
  line-height: 1.4;
}

@media (min-width: 768px) {
  .text-responsive-sm {
    font-size: 14px;
  }
}

.text-responsive-lg {
  font-size: 16px;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .text-responsive-lg {
    font-size: 20px;
  }
}

/* 响应式间距 */
.spacing-responsive {
  padding: 12px;
  margin: 8px 0;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 16px;
    margin: 12px 0;
  }
}

@media (min-width: 1200px) {
  .spacing-responsive {
    padding: 24px;
    margin: 16px 0;
  }
}

/* 响应式卡片 */
.card-responsive {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background: white;
  overflow: hidden;
}

@media (min-width: 768px) {
  .card-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 响应式表格 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 767px) {
  .table-responsive table {
    font-size: 12px;
  }
  
  .table-responsive th,
  .table-responsive td {
    padding: 8px 4px;
    white-space: nowrap;
  }
}

/* 响应式按钮 */
.button-responsive {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 4px;
  min-height: 32px;
}

@media (min-width: 768px) {
  .button-responsive {
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 6px;
    min-height: 36px;
  }
}

.button-responsive-sm {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 3px;
  min-height: 24px;
}

@media (min-width: 768px) {
  .button-responsive-sm {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    min-height: 28px;
  }
}

/* 响应式导航 */
.nav-responsive {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (min-width: 768px) {
  .nav-responsive {
    flex-direction: row;
    gap: 16px;
  }
}

/* 响应式侧边栏 */
.sidebar-responsive {
  width: 100%;
  position: relative;
}

@media (min-width: 768px) {
  .sidebar-responsive {
    width: 240px;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
  }
}

@media (min-width: 1200px) {
  .sidebar-responsive {
    width: 280px;
  }
}

/* 响应式主内容区 */
.main-content-responsive {
  width: 100%;
  padding: 16px;
}

@media (min-width: 768px) {
  .main-content-responsive {
    margin-left: 240px;
    padding: 24px;
  }
}

@media (min-width: 1200px) {
  .main-content-responsive {
    margin-left: 280px;
    padding: 32px;
  }
}

/* 响应式图表容器 */
.chart-responsive {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

@media (min-width: 576px) {
  .chart-responsive {
    height: 250px;
    min-height: 250px;
  }
}

@media (min-width: 768px) {
  .chart-responsive {
    height: 300px;
    min-height: 300px;
  }
}

@media (min-width: 1200px) {
  .chart-responsive {
    height: 400px;
    min-height: 400px;
  }
}

/* 响应式模态框 */
.modal-responsive {
  width: 95%;
  max-width: 520px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modal-responsive {
    width: 80%;
    max-width: 720px;
  }
}

@media (min-width: 1200px) {
  .modal-responsive {
    width: 60%;
    max-width: 900px;
  }
}

/* 响应式抽屉 */
.drawer-responsive {
  width: 100%;
}

@media (min-width: 768px) {
  .drawer-responsive {
    width: 400px;
  }
}

@media (min-width: 1200px) {
  .drawer-responsive {
    width: 500px;
  }
}

/* 隐藏/显示工具类 */
.hidden-xs {
  display: none;
}

@media (min-width: 576px) {
  .hidden-xs {
    display: block;
  }
}

.hidden-sm {
  display: block;
}

@media (min-width: 576px) and (max-width: 767px) {
  .hidden-sm {
    display: none;
  }
}

.hidden-md {
  display: block;
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-md {
    display: none;
  }
}

.hidden-lg {
  display: block;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-lg {
    display: none;
  }
}

.hidden-xl {
  display: block;
}

@media (min-width: 1200px) {
  .hidden-xl {
    display: none;
  }
}

/* 仅在特定尺寸显示 */
.visible-xs {
  display: block;
}

@media (min-width: 576px) {
  .visible-xs {
    display: none;
  }
}

.visible-sm {
  display: none;
}

@media (min-width: 576px) and (max-width: 767px) {
  .visible-sm {
    display: block;
  }
}

.visible-md {
  display: none;
}

@media (min-width: 768px) and (max-width: 991px) {
  .visible-md {
    display: block;
  }
}

.visible-lg {
  display: none;
}

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-lg {
    display: block;
  }
}

.visible-xl {
  display: none;
}

@media (min-width: 1200px) {
  .visible-xl {
    display: block;
  }
}

/* 响应式Flex布局 */
.flex-responsive {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
    gap: 16px;
  }
}

.flex-responsive-reverse {
  display: flex;
  flex-direction: column-reverse;
  gap: 12px;
}

@media (min-width: 768px) {
  .flex-responsive-reverse {
    flex-direction: row-reverse;
    gap: 16px;
  }
}

/* 响应式对齐 */
.text-center-mobile {
  text-align: center;
}

@media (min-width: 768px) {
  .text-center-mobile {
    text-align: left;
  }
}

/* 响应式滚动 */
.scroll-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

.scroll-responsive::-webkit-scrollbar {
  height: 6px;
}

.scroll-responsive::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-responsive::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.scroll-responsive::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}
