'use client';

import { Card, Row, Col, Typography, Button, Space, Statistic, Divider, Empty, Table, Tag, Progress } from 'antd';
import {
  WalletOutlined,
  PieChartOutlined,
  RiseOutlined,
  FundOutlined,
  RightOutlined,
  PlusOutlined,
  BarChartOutlined,
  FallOutlined,
  StockOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

export default function PortfolioPage() {
  const router = useRouter();

  const portfolioStats = [
    {
      title: '总资产',
      value: 0,
      precision: 2,
      prefix: '¥',
      valueStyle: { color: '#1890ff' },
      icon: <FundOutlined />
    },
    {
      title: '今日收益',
      value: 0,
      precision: 2,
      prefix: '¥',
      valueStyle: { color: '#52c41a' },
      icon: <RiseOutlined />
    },
    {
      title: '总收益率',
      value: 0,
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#52c41a' },
      icon: <BarChartOutlined />
    },
    {
      title: '持仓股票',
      value: 0,
      icon: <PieChartOutlined />
    }
  ];

  const portfolioModules = [
    {
      title: '投资组合概览',
      description: '查看投资组合整体表现和资产分布',
      icon: <PieChartOutlined className="text-3xl text-purple-500" />,
      path: '/dashboard/portfolio/overview',
      features: ['资产分布', '收益分析', '风险评估', '历史表现']
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <WalletOutlined className="mr-3" />
          投资组合
        </Title>
        <Text type="secondary" className="text-lg">
          管理和分析您的投资组合表现
        </Text>
      </div>

      {/* 投资组合统计 */}
      <Row gutter={[16, 16]} className="mb-8">
        {portfolioStats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={stat.valueStyle}
                prefix={stat.icon}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Divider />

      {/* 功能模块 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          功能模块
        </Title>
        <Row gutter={[24, 24]}>
          {portfolioModules.map((module, index) => (
            <Col xs={24} lg={12} key={index}>
              <Card 
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => router.push(module.path)}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {module.icon}
                  </div>
                  <div className="flex-1">
                    <Title level={4} className="!mb-2">
                      {module.title}
                    </Title>
                    <Text type="secondary" className="block mb-4">
                      {module.description}
                    </Text>
                    <div className="mb-4">
                      <Space wrap>
                        {module.features.map((feature, idx) => (
                          <span 
                            key={idx}
                            className="px-2 py-1 bg-purple-50 text-purple-600 rounded text-xs"
                          >
                            {feature}
                          </span>
                        ))}
                      </Space>
                    </div>
                    <Button type="link" className="p-0">
                      进入模块 <RightOutlined />
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 持仓列表 */}
      <Card title="当前持仓" className="mb-6">
        <Empty 
          description="暂无持仓数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" icon={<PlusOutlined />}>
            添加持仓
          </Button>
        </Empty>
      </Card>

      {/* 快速操作 */}
      <Card className="bg-purple-50 border-purple-200">
        <div className="text-center">
          <Title level={4} className="!mb-2 text-purple-800">
            🚀 开始投资组合管理
          </Title>
          <Text className="text-purple-700 block mb-4">
            创建您的第一个投资组合，开始跟踪投资表现
          </Text>
          <Space>
            <Button type="primary" icon={<PlusOutlined />}>
              创建投资组合
            </Button>
            <Button 
              onClick={() => router.push('/dashboard/portfolio/overview')}
            >
              查看详细分析
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
}
