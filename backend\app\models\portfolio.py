"""
投资组合数据模型

包含投资组合、持仓、交易记录等相关的数据库模型
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import (
    Column, Integer, String, DateTime, Numeric, Text, Boolean, 
    ForeignKey, Index, CheckConstraint
)
from sqlalchemy.orm import relationship, Mapped, mapped_column

from app.models.base import Base


class Portfolio(Base):
    """投资组合模型"""
    __tablename__ = "portfolios"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # 投资组合基本信息
    initial_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    current_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    available_cash: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    total_market_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    
    # 收益统计
    total_pnl: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    total_pnl_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), nullable=False, default=0)
    today_pnl: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    today_pnl_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), nullable=False, default=0)
    
    # 风险指标
    max_drawdown: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    sharpe_ratio: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    volatility: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    
    # 状态和配置
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="portfolios")
    positions = relationship("Position", back_populates="portfolio", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="portfolio", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_portfolio_user_id', 'user_id'),
        Index('idx_portfolio_user_active', 'user_id', 'is_active'),
        CheckConstraint('initial_capital >= 0', name='check_initial_capital_positive'),
        CheckConstraint('current_capital >= 0', name='check_current_capital_positive'),
        CheckConstraint('available_cash >= 0', name='check_available_cash_positive'),
    )


class Position(Base):
    """持仓模型"""
    __tablename__ = "positions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, ForeignKey("portfolios.id"), nullable=False)
    stock_code: Mapped[str] = mapped_column(String(20), nullable=False)
    stock_name: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # 持仓信息
    shares: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    available_shares: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    avg_cost: Mapped[Decimal] = mapped_column(Numeric(10, 4), nullable=False, default=0)
    current_price: Mapped[Decimal] = mapped_column(Numeric(10, 4), nullable=False, default=0)
    
    # 市值和收益
    market_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    cost_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    pnl: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False, default=0)
    pnl_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), nullable=False, default=0)
    
    # 权重和风险
    weight: Mapped[Decimal] = mapped_column(Numeric(8, 4), nullable=False, default=0)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_price_update: Mapped[Optional[datetime]] = mapped_column(DateTime)
    
    # 关系
    portfolio = relationship("Portfolio", back_populates="positions")
    
    # 索引
    __table_args__ = (
        Index('idx_position_portfolio_id', 'portfolio_id'),
        Index('idx_position_stock_code', 'stock_code'),
        Index('idx_position_portfolio_stock', 'portfolio_id', 'stock_code'),
        CheckConstraint('shares >= 0', name='check_shares_positive'),
        CheckConstraint('available_shares >= 0', name='check_available_shares_positive'),
        CheckConstraint('available_shares <= shares', name='check_available_shares_valid'),
    )


class Transaction(Base):
    """交易记录模型"""
    __tablename__ = "transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, ForeignKey("portfolios.id"), nullable=False)
    stock_code: Mapped[str] = mapped_column(String(20), nullable=False)
    stock_name: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # 交易信息
    transaction_type: Mapped[str] = mapped_column(String(10), nullable=False)  # BUY, SELL
    shares: Mapped[int] = mapped_column(Integer, nullable=False)
    price: Mapped[Decimal] = mapped_column(Numeric(10, 4), nullable=False)
    amount: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    
    # 费用
    commission: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False, default=0)
    tax: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False, default=0)
    total_cost: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    
    # 交易状态
    status: Mapped[str] = mapped_column(String(20), nullable=False, default='COMPLETED')  # PENDING, COMPLETED, CANCELLED
    
    # 备注和来源
    notes: Mapped[Optional[str]] = mapped_column(Text)
    source: Mapped[str] = mapped_column(String(50), default='MANUAL')  # MANUAL, STRATEGY, API
    
    # 时间戳
    transaction_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    portfolio = relationship("Portfolio", back_populates="transactions")
    
    # 索引
    __table_args__ = (
        Index('idx_transaction_portfolio_id', 'portfolio_id'),
        Index('idx_transaction_stock_code', 'stock_code'),
        Index('idx_transaction_date', 'transaction_date'),
        Index('idx_transaction_portfolio_date', 'portfolio_id', 'transaction_date'),
        CheckConstraint('shares > 0', name='check_transaction_shares_positive'),
        CheckConstraint('price > 0', name='check_transaction_price_positive'),
        CheckConstraint('amount > 0', name='check_transaction_amount_positive'),
        CheckConstraint("transaction_type IN ('BUY', 'SELL')", name='check_transaction_type_valid'),
        CheckConstraint("status IN ('PENDING', 'COMPLETED', 'CANCELLED')", name='check_transaction_status_valid'),
    )


class PortfolioSnapshot(Base):
    """投资组合快照模型 - 用于记录历史数据"""
    __tablename__ = "portfolio_snapshots"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    portfolio_id: Mapped[int] = mapped_column(Integer, ForeignKey("portfolios.id"), nullable=False)
    
    # 快照数据
    snapshot_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    total_capital: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    available_cash: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    total_market_value: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    total_pnl: Mapped[Decimal] = mapped_column(Numeric(15, 2), nullable=False)
    total_pnl_percent: Mapped[Decimal] = mapped_column(Numeric(8, 4), nullable=False)
    
    # 风险指标
    position_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    max_position_weight: Mapped[Optional[Decimal]] = mapped_column(Numeric(8, 4))
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # 关系
    portfolio = relationship("Portfolio")
    
    # 索引
    __table_args__ = (
        Index('idx_snapshot_portfolio_id', 'portfolio_id'),
        Index('idx_snapshot_date', 'snapshot_date'),
        Index('idx_snapshot_portfolio_date', 'portfolio_id', 'snapshot_date'),
    )


class WatchList(Base):
    """自选股模型"""
    __tablename__ = "watchlists"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id"), nullable=False)
    stock_code: Mapped[str] = mapped_column(String(20), nullable=False)
    stock_name: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # 添加信息
    add_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 4))
    notes: Mapped[Optional[str]] = mapped_column(Text)
    
    # 排序和分组
    sort_order: Mapped[int] = mapped_column(Integer, default=0)
    group_name: Mapped[Optional[str]] = mapped_column(String(50))
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User")
    
    # 索引
    __table_args__ = (
        Index('idx_watchlist_user_id', 'user_id'),
        Index('idx_watchlist_stock_code', 'stock_code'),
        Index('idx_watchlist_user_stock', 'user_id', 'stock_code'),
    )
