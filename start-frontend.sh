#!/bin/bash
# ============================================================================
# 智能量化交易平台 - 前端服务启动脚本 (Linux/Mac)
# ============================================================================

echo "🚀 启动智能量化交易平台前端服务..."

# 检查Node.js环境
echo "📋 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到Node.js，请先安装Node.js 18+"
    echo "💡 下载地址: https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js版本: $NODE_VERSION"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到npm"
    exit 1
fi

NPM_VERSION=$(npm --version)
echo "✅ npm版本: $NPM_VERSION"

# 进入前端目录
cd frontend || {
    echo "❌ 错误: 无法进入frontend目录"
    exit 1
}

# 检查package.json
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    exit 1
fi

# 检查node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    echo "⏳ 这可能需要几分钟时间，请耐心等待..."
    
    if npm install; then
        echo "✅ 依赖安装完成"
    else
        echo "⚠️  npm install失败，尝试使用yarn..."
        if command -v yarn &> /dev/null; then
            yarn install
        else
            echo "❌ 依赖安装失败，请手动运行: npm install"
            exit 1
        fi
    fi
else
    echo "✅ 依赖已安装"
fi

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "📝 创建环境变量文件..."
    cat > .env.local << EOF
# 智能量化交易平台 - 前端环境变量
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_APP_NAME=智能量化交易平台
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development
EOF
    echo "✅ 环境变量文件创建完成"
fi

# 启动开发服务器
echo "🌟 启动Next.js开发服务器..."
echo "📍 服务地址: http://localhost:3000"
echo "🔄 按 Ctrl+C 停止服务"
echo "💡 确保后端服务已在 http://localhost:8000 运行"
echo ""

if npm run dev; then
    echo "👋 前端服务已停止"
else
    echo "❌ 启动失败"
    echo "💡 请检查依赖是否正确安装"
    echo "💡 尝试删除node_modules文件夹后重新运行"
    exit 1
fi
