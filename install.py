#!/usr/bin/env python3
"""
JQData量化数据平台 - 自动安装脚本

这个脚本帮助用户快速设置开发环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return None


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print("❌ 需要Python 3.11或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def check_node_version():
    """检查Node.js版本"""
    result = run_command("node --version", check=False)
    if result is None or result.returncode != 0:
        print("❌ 未找到Node.js，请先安装Node.js 18+")
        return False
    
    version = result.stdout.strip()
    print(f"✅ Node.js版本: {version}")
    return True


def install_backend_deps(install_type="basic"):
    """安装后端依赖"""
    print("\n📦 安装后端依赖...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 基础依赖
    if not run_command("pip install -r requirements.txt", cwd=backend_dir):
        return False
    
    # 开发依赖
    if install_type in ["dev", "full"]:
        print("📦 安装开发依赖...")
        if not run_command("pip install -r requirements-dev.txt", cwd=backend_dir):
            print("⚠️ 开发依赖安装失败，继续...")
    
    # 完整依赖
    if install_type == "full":
        print("📦 安装完整功能依赖...")
        if not run_command("pip install -r requirements-full.txt", cwd=backend_dir):
            print("⚠️ 完整功能依赖安装失败，继续...")
    
    print("✅ 后端依赖安装完成")
    return True


def install_frontend_deps():
    """安装前端依赖"""
    print("\n📦 安装前端依赖...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("✅ 前端依赖安装完成")
    return True


def setup_environment():
    """设置环境变量"""
    print("\n⚙️ 设置环境变量...")
    
    env_example = Path("backend/.env.example")
    env_file = Path("backend/.env")
    
    if env_example.exists() and not env_file.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("✅ 已创建.env文件，请根据需要修改配置")
    else:
        print("ℹ️ .env文件已存在或.env.example不存在")


def main():
    """主函数"""
    print("🚀 JQData量化数据平台 - 自动安装脚本")
    print("=" * 50)
    
    # 检查系统要求
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        print("⚠️ 前端功能将无法使用")
    
    # 选择安装类型
    print("\n请选择安装类型:")
    print("1. 基础安装 (仅核心功能)")
    print("2. 开发安装 (包含开发工具)")
    print("3. 完整安装 (包含所有功能)")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    install_types = {
        "1": "basic",
        "2": "dev", 
        "3": "full"
    }
    
    install_type = install_types.get(choice, "basic")
    print(f"选择了: {install_type} 安装")
    
    # 安装依赖
    success = True
    
    if not install_backend_deps(install_type):
        success = False
    
    if not install_frontend_deps():
        success = False
    
    # 设置环境
    setup_environment()
    
    # 完成提示
    print("\n" + "=" * 50)
    if success:
        print("🎉 安装完成！")
        print("\n下一步:")
        print("1. 配置 backend/.env 文件")
        print("2. 启动后端: cd backend && python -m uvicorn app.main:app --reload")
        print("3. 启动前端: cd frontend && npm run dev")
        print("4. 访问 http://localhost:3000")
    else:
        print("❌ 安装过程中出现错误，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
