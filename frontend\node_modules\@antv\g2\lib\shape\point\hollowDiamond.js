"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HollowDiamond = void 0;
const color_1 = require("./color");
/**
 * ◇
 */
const HollowDiamond = (options, context) => {
    return (0, color_1.Color)(Object.assign({ colorAttribute: 'stroke', symbol: 'diamond' }, options), context);
};
exports.HollowDiamond = HollowDiamond;
exports.HollowDiamond.props = Object.assign({ defaultMarker: 'hollowDiamond' }, color_1.Color.props);
//# sourceMappingURL=hollowDiamond.js.map