'use client';

import { Card, Row, Col, Typography, Button, Space, Divider } from 'antd';
import { 
  LineChartOutlined,
  StockOutlined,
  BarChartOutlined,
  TrendingUpOutlined,
  RightOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Title, Text } = Typography;

export default function MarketPage() {
  const router = useRouter();

  const marketModules = [
    {
      title: '股票行情',
      description: '实时股票价格、涨跌幅、成交量等数据',
      icon: <StockOutlined className="text-3xl text-blue-500" />,
      path: '/dashboard/market/stocks',
      features: ['实时行情', 'K线图表', '技术指标', '资金流向']
    },
    {
      title: '图表分析',
      description: '专业的技术分析图表和指标工具',
      icon: <LineChartOutlined className="text-3xl text-green-500" />,
      path: '/dashboard/market/charts',
      features: ['K线图', '技术指标', '画线工具', '多周期分析']
    }
  ];

  const quickStats = [
    { label: '上证指数', value: '3,234.56', change: '+1.23%', positive: true },
    { label: '深证成指', value: '12,345.67', change: '-0.45%', positive: false },
    { label: '创业板指', value: '2,567.89', change: '+2.10%', positive: true },
    { label: '沪深300', value: '4,123.45', change: '+0.78%', positive: true },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <BarChartOutlined className="mr-3" />
          市场数据
        </Title>
        <Text type="secondary" className="text-lg">
          实时市场行情、技术分析和数据洞察
        </Text>
      </div>

      {/* 市场概览 */}
      <Card title="市场概览" className="mb-6">
        <Row gutter={[16, 16]}>
          {quickStats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <div className="text-center p-4 border rounded-lg">
                <Text type="secondary" className="block mb-1">{stat.label}</Text>
                <div className="text-xl font-semibold mb-1">{stat.value}</div>
                <div className={`text-sm ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                  <TrendingUpOutlined className={`mr-1 ${stat.positive ? '' : 'rotate-180'}`} />
                  {stat.change}
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      <Divider />

      {/* 功能模块 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          功能模块
        </Title>
        <Row gutter={[24, 24]}>
          {marketModules.map((module, index) => (
            <Col xs={24} lg={12} key={index}>
              <Card 
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => router.push(module.path)}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {module.icon}
                  </div>
                  <div className="flex-1">
                    <Title level={4} className="!mb-2">
                      {module.title}
                    </Title>
                    <Text type="secondary" className="block mb-4">
                      {module.description}
                    </Text>
                    <div className="mb-4">
                      <Space wrap>
                        {module.features.map((feature, idx) => (
                          <span 
                            key={idx}
                            className="px-2 py-1 bg-blue-50 text-blue-600 rounded text-xs"
                          >
                            {feature}
                          </span>
                        ))}
                      </Space>
                    </div>
                    <Button type="link" className="p-0">
                      进入模块 <RightOutlined />
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 快速提示 */}
      <Card className="bg-blue-50 border-blue-200">
        <div className="text-center">
          <Title level={4} className="!mb-2 text-blue-800">
            💡 提示
          </Title>
          <Text className="text-blue-700">
            市场数据功能需要配置JQData API密钥。请前往 
            <Button 
              type="link" 
              className="px-1"
              onClick={() => router.push('/dashboard/settings/jqdata')}
            >
              系统设置
            </Button> 
            进行配置。
          </Text>
        </div>
      </Card>
    </div>
  );
}
