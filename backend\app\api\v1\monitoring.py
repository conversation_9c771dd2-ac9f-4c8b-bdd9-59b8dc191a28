"""
系统监控相关API端点

提供系统健康检查、性能监控、告警管理等API接口
"""

from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.schemas.base import BaseResponse
from app.services.monitoring_service import monitoring_service

router = APIRouter()


@router.get("/health", response_model=BaseResponse[dict])
async def health_check():
    """系统健康检查"""
    try:
        health_data = await monitoring_service.get_health_check()
        
        return BaseResponse(
            code=200,
            message="健康检查完成",
            data=health_data
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return BaseResponse(
            code=500,
            message="健康检查失败",
            data={
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )


@router.get("/metrics", response_model=BaseResponse[dict])
async def get_system_metrics(
    hours: int = Query(1, ge=1, le=24, description="获取最近N小时的数据"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取系统指标"""
    try:
        # 检查用户权限（只有管理员可以查看系统指标）
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以查看系统指标"
            )
        
        # 获取当前状态
        current_status = monitoring_service.system_monitor.get_current_status()
        
        # 获取历史数据
        history_data = monitoring_service.system_monitor.get_metrics_history(hours)
        
        # 获取应用统计
        app_stats = monitoring_service.app_monitor.get_stats()
        
        return BaseResponse(
            code=200,
            message="获取系统指标成功",
            data={
                "current": current_status,
                "history": history_data,
                "application": app_stats,
                "time_range": f"最近{hours}小时"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统指标失败"
        )


@router.get("/alerts", response_model=BaseResponse[dict])
async def get_active_alerts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取活跃告警"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以查看系统告警"
            )
        
        active_alerts = []
        for alert_key, alert_info in monitoring_service.system_monitor.active_alerts.items():
            rule = alert_info["rule"]
            duration = (datetime.utcnow() - alert_info["first_triggered"]).total_seconds()
            
            active_alerts.append({
                "id": alert_key,
                "name": rule.name,
                "metric": rule.metric,
                "severity": rule.severity,
                "threshold": rule.threshold,
                "operator": rule.operator,
                "first_triggered": alert_info["first_triggered"].isoformat(),
                "last_triggered": alert_info["last_triggered"].isoformat(),
                "duration": int(duration),
                "count": alert_info["count"],
                "notified": alert_info["notified"]
            })
        
        # 按严重程度排序
        severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
        active_alerts.sort(key=lambda x: severity_order.get(x["severity"], 4))
        
        return BaseResponse(
            code=200,
            message="获取活跃告警成功",
            data={
                "alerts": active_alerts,
                "total": len(active_alerts),
                "critical": len([a for a in active_alerts if a["severity"] == "critical"]),
                "high": len([a for a in active_alerts if a["severity"] == "high"]),
                "medium": len([a for a in active_alerts if a["severity"] == "medium"]),
                "low": len([a for a in active_alerts if a["severity"] == "low"])
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取活跃告警失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取活跃告警失败"
        )


@router.get("/performance", response_model=BaseResponse[dict])
async def get_performance_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取性能统计"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以查看性能统计"
            )
        
        # 获取应用性能统计
        app_stats = monitoring_service.app_monitor.get_stats()
        
        # 获取数据库统计
        db_stats = await monitoring_service.db_monitor.get_database_stats()
        
        # 获取系统资源使用情况
        system_status = monitoring_service.system_monitor.get_current_status()
        
        return BaseResponse(
            code=200,
            message="获取性能统计成功",
            data={
                "application": app_stats,
                "database": db_stats,
                "system": {
                    "cpu_percent": system_status.get("cpu_percent", 0),
                    "memory_percent": system_status.get("memory_percent", 0),
                    "disk_percent": system_status.get("disk_percent", 0),
                    "active_connections": system_status.get("active_connections", 0),
                    "uptime": system_status.get("uptime", 0)
                },
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取性能统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能统计失败"
        )


@router.post("/alerts/acknowledge/{alert_id}", response_model=BaseResponse[dict])
async def acknowledge_alert(
    alert_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """确认告警"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以确认告警"
            )
        
        if alert_id in monitoring_service.system_monitor.active_alerts:
            monitoring_service.system_monitor.active_alerts[alert_id]["acknowledged"] = True
            monitoring_service.system_monitor.active_alerts[alert_id]["acknowledged_by"] = current_user.id
            monitoring_service.system_monitor.active_alerts[alert_id]["acknowledged_at"] = datetime.utcnow()
            
            logger.info(f"用户 {current_user.username} 确认了告警: {alert_id}")
            
            return BaseResponse(
                code=200,
                message="告警确认成功",
                data={"alert_id": alert_id, "acknowledged": True}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="告警不存在或已恢复"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认告警失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="确认告警失败"
        )


@router.get("/dashboard", response_model=BaseResponse[dict])
async def get_monitoring_dashboard(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取监控仪表板数据"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以查看监控仪表板"
            )
        
        # 获取综合数据
        health_data = await monitoring_service.get_health_check()
        
        # 获取最近1小时的指标历史
        metrics_history = monitoring_service.system_monitor.get_metrics_history(1)
        
        # 获取告警统计
        active_alerts = monitoring_service.system_monitor.active_alerts
        alert_stats = {
            "total": len(active_alerts),
            "critical": len([a for a in active_alerts.values() if a["rule"].severity == "critical"]),
            "high": len([a for a in active_alerts.values() if a["rule"].severity == "high"]),
            "medium": len([a for a in active_alerts.values() if a["rule"].severity == "medium"]),
            "low": len([a for a in active_alerts.values() if a["rule"].severity == "low"])
        }
        
        # 计算趋势数据
        trends = {}
        if len(metrics_history) >= 2:
            latest = metrics_history[-1]
            previous = metrics_history[-2]
            
            trends = {
                "cpu_trend": latest["cpu_percent"] - previous["cpu_percent"],
                "memory_trend": latest["memory_percent"] - previous["memory_percent"],
                "connections_trend": latest["active_connections"] - previous["active_connections"]
            }
        
        return BaseResponse(
            code=200,
            message="获取监控仪表板数据成功",
            data={
                "health": health_data,
                "metrics_history": metrics_history[-60:],  # 最近60个数据点
                "alert_stats": alert_stats,
                "trends": trends,
                "summary": {
                    "system_status": health_data["status"],
                    "total_alerts": alert_stats["total"],
                    "critical_alerts": alert_stats["critical"],
                    "monitoring_active": health_data["services"]["monitoring"],
                    "last_updated": datetime.utcnow().isoformat()
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取监控仪表板数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取监控仪表板数据失败"
        )


@router.post("/start", response_model=BaseResponse[dict])
async def start_monitoring(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """启动监控服务"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以控制监控服务"
            )
        
        await monitoring_service.start_monitoring()
        
        return BaseResponse(
            code=200,
            message="监控服务启动成功",
            data={"monitoring_active": True}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动监控服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动监控服务失败"
        )


@router.post("/stop", response_model=BaseResponse[dict])
async def stop_monitoring(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """停止监控服务"""
    try:
        # 检查用户权限
        if not getattr(current_user, 'is_admin', False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，只有管理员可以控制监控服务"
            )
        
        await monitoring_service.stop_monitoring()
        
        return BaseResponse(
            code=200,
            message="监控服务停止成功",
            data={"monitoring_active": False}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止监控服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止监控服务失败"
        )
