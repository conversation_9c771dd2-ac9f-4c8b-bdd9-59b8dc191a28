'use client';

/**
 * 推荐系统仪表板
 * 
 * 显示策略推荐、股票推荐和个性化推荐
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Rate,
  Tag,
  Space,
  Avatar,
  Progress,
  Statistic,
  List,
  Empty,
  Spin,
  message,
  Modal,
  Descriptions,
  Alert,
  Tabs,
  Badge
} from 'antd';
import {
  BulbOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  HeartOutlined,
  HeartFilled,
  EyeOutlined,
  ShareAltOutlined,
  ThunderboltOutlined,
  StarOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;

interface Recommendation {
  strategy_id?: number;
  symbol?: string;
  name?: string;
  strategy_name?: string;
  score: number;
  confidence: number;
  reasons: string[];
  performance?: {
    annual_return?: number;
    max_drawdown?: number;
    sharpe_ratio?: number;
    backtest_count?: number;
  };
  risk_metrics?: {
    volatility?: number;
    beta?: number;
  };
  predicted_return?: number;
  risk_level?: string;
}

interface UserFeedback {
  rating: number;
  feedback: string;
  is_clicked: boolean;
  is_adopted: boolean;
}

export const RecommendationDashboard: React.FC = () => {
  const [strategyRecommendations, setStrategyRecommendations] = useState<Recommendation[]>([]);
  const [stockRecommendations, setStockRecommendations] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRecommendation, setSelectedRecommendation] = useState<Recommendation | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [feedbackVisible, setFeedbackVisible] = useState(false);
  const [likedItems, setLikedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchRecommendations();
  }, []);

  const fetchRecommendations = async () => {
    setLoading(true);
    try {
      // 获取策略推荐
      const strategyResponse = await fetch('/api/v1/ml/recommendations/strategies?n_recommendations=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const strategyResult = await strategyResponse.json();
      
      if (strategyResult.code === 200) {
        setStrategyRecommendations(strategyResult.data);
      }

      // 获取股票推荐
      const stockResponse = await fetch('/api/v1/ml/recommendations/stocks?n_recommendations=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const stockResult = await stockResponse.json();
      
      if (stockResult.code === 200) {
        setStockRecommendations(stockResult.data);
      }
    } catch (error) {
      console.error('获取推荐失败:', error);
      message.error('获取推荐失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = (id: string) => {
    const newLikedItems = new Set(likedItems);
    if (likedItems.has(id)) {
      newLikedItems.delete(id);
    } else {
      newLikedItems.add(id);
    }
    setLikedItems(newLikedItems);
  };

  const showDetail = (recommendation: Recommendation) => {
    setSelectedRecommendation(recommendation);
    setDetailVisible(true);
  };

  const submitFeedback = async (feedback: UserFeedback) => {
    try {
      // 这里应该提交反馈到后端
      message.success('反馈提交成功');
      setFeedbackVisible(false);
    } catch (error) {
      message.error('反馈提交失败');
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const getRiskLevelColor = (level: string) => {
    const colors = {
      'low': 'green',
      'medium': 'orange',
      'high': 'red'
    };
    return colors[level as keyof typeof colors] || 'default';
  };

  const getRiskLevelText = (level: string) => {
    const texts = {
      'low': '低风险',
      'medium': '中风险',
      'high': '高风险'
    };
    return texts[level as keyof typeof texts] || level;
  };

  const StrategyRecommendationCard: React.FC<{ recommendation: Recommendation }> = ({ recommendation }) => (
    <Card
      size="small"
      className="mb-4 hover:shadow-md transition-shadow"
      actions={[
        <Button
          key="like"
          type="text"
          icon={likedItems.has(`strategy_${recommendation.strategy_id}`) ? 
            <HeartFilled style={{ color: '#ff4d4f' }} /> : 
            <HeartOutlined />
          }
          onClick={() => handleLike(`strategy_${recommendation.strategy_id}`)}
        >
          收藏
        </Button>,
        <Button
          key="detail"
          type="text"
          icon={<EyeOutlined />}
          onClick={() => showDetail(recommendation)}
        >
          详情
        </Button>,
        <Button
          key="share"
          type="text"
          icon={<ShareAltOutlined />}
        >
          分享
        </Button>
      ]}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h4 className="font-medium text-lg mb-1">{recommendation.strategy_name}</h4>
          <div className="flex items-center space-x-2 mb-2">
            <Progress
              percent={recommendation.score * 100}
              size="small"
              strokeColor={getScoreColor(recommendation.score)}
              showInfo={false}
              className="flex-1"
            />
            <span className="text-sm font-medium" style={{ color: getScoreColor(recommendation.score) }}>
              {(recommendation.score * 100).toFixed(0)}%
            </span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-500 mb-1">置信度</div>
          <div className="font-medium">{(recommendation.confidence * 100).toFixed(0)}%</div>
        </div>
      </div>

      {recommendation.performance && (
        <Row gutter={16} className="mb-3">
          <Col span={8}>
            <Statistic
              title="年化收益"
              value={recommendation.performance.annual_return}
              precision={2}
              suffix="%"
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="最大回撤"
              value={Math.abs(recommendation.performance.max_drawdown || 0)}
              precision={2}
              suffix="%"
              valueStyle={{ fontSize: '14px', color: '#ff4d4f' }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="夏普比率"
              value={recommendation.performance.sharpe_ratio}
              precision={2}
              valueStyle={{ fontSize: '14px' }}
            />
          </Col>
        </Row>
      )}

      <div className="mb-2">
        <div className="text-xs text-gray-500 mb-1">推荐理由</div>
        <div className="flex flex-wrap gap-1">
          {recommendation.reasons.map((reason, index) => (
            <Tag key={index} color="blue" className="text-xs">
              {reason}
            </Tag>
          ))}
        </div>
      </div>
    </Card>
  );

  const StockRecommendationCard: React.FC<{ recommendation: Recommendation }> = ({ recommendation }) => (
    <Card
      size="small"
      className="mb-4 hover:shadow-md transition-shadow"
      actions={[
        <Button
          key="like"
          type="text"
          icon={likedItems.has(`stock_${recommendation.symbol}`) ? 
            <HeartFilled style={{ color: '#ff4d4f' }} /> : 
            <HeartOutlined />
          }
          onClick={() => handleLike(`stock_${recommendation.symbol}`)}
        >
          收藏
        </Button>,
        <Button
          key="detail"
          type="text"
          icon={<EyeOutlined />}
          onClick={() => showDetail(recommendation)}
        >
          详情
        </Button>,
        <Button
          key="trade"
          type="text"
          icon={<ThunderboltOutlined />}
        >
          交易
        </Button>
      ]}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="font-medium text-lg">{recommendation.name}</h4>
            <Tag color="blue">{recommendation.symbol}</Tag>
          </div>
          <div className="flex items-center space-x-2 mb-2">
            <Progress
              percent={recommendation.score * 100}
              size="small"
              strokeColor={getScoreColor(recommendation.score)}
              showInfo={false}
              className="flex-1"
            />
            <span className="text-sm font-medium" style={{ color: getScoreColor(recommendation.score) }}>
              {(recommendation.score * 100).toFixed(0)}%
            </span>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-500 mb-1">风险等级</div>
          <Tag color={getRiskLevelColor(recommendation.risk_level || 'medium')}>
            {getRiskLevelText(recommendation.risk_level || 'medium')}
          </Tag>
        </div>
      </div>

      <Row gutter={16} className="mb-3">
        <Col span={12}>
          <Statistic
            title="预期收益"
            value={recommendation.predicted_return ? recommendation.predicted_return * 100 : 0}
            precision={2}
            suffix="%"
            valueStyle={{ 
              fontSize: '14px',
              color: (recommendation.predicted_return || 0) > 0 ? '#52c41a' : '#ff4d4f'
            }}
            prefix={(recommendation.predicted_return || 0) > 0 ? <RiseOutlined /> : <FallOutlined />}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="置信度"
            value={recommendation.confidence * 100}
            precision={0}
            suffix="%"
            valueStyle={{ fontSize: '14px' }}
          />
        </Col>
      </Row>

      <div className="mb-2">
        <div className="text-xs text-gray-500 mb-1">推荐理由</div>
        <div className="flex flex-wrap gap-1">
          {recommendation.reasons.map((reason, index) => (
            <Tag key={index} color="green" className="text-xs">
              {reason}
            </Tag>
          ))}
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">智能推荐</h1>
          <p className="text-gray-600">基于AI算法的个性化策略和股票推荐</p>
        </div>
        <Button
          type="primary"
          icon={<BulbOutlined />}
          onClick={fetchRecommendations}
          loading={loading}
        >
          刷新推荐
        </Button>
      </div>

      {/* 推荐统计 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="策略推荐"
              value={strategyRecommendations.length}
              prefix={<TrophyOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="股票推荐"
              value={stockRecommendations.length}
              prefix={<RiseOutlined />}
              suffix="只"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="收藏数量"
              value={likedItems.size}
              prefix={<HeartOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="推荐准确率"
              value={85.6}
              prefix={<StarOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* 推荐内容 */}
      <Card>
        <Tabs defaultActiveKey="strategies">
          <TabPane
            tab={
              <Badge count={strategyRecommendations.length} size="small">
                <span>策略推荐</span>
              </Badge>
            }
            key="strategies"
          >
            <Spin spinning={loading}>
              {strategyRecommendations.length > 0 ? (
                <Row gutter={16}>
                  {strategyRecommendations.map((recommendation, index) => (
                    <Col span={12} key={index}>
                      <StrategyRecommendationCard recommendation={recommendation} />
                    </Col>
                  ))}
                </Row>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无策略推荐"
                />
              )}
            </Spin>
          </TabPane>

          <TabPane
            tab={
              <Badge count={stockRecommendations.length} size="small">
                <span>股票推荐</span>
              </Badge>
            }
            key="stocks"
          >
            <Spin spinning={loading}>
              {stockRecommendations.length > 0 ? (
                <Row gutter={16}>
                  {stockRecommendations.map((recommendation, index) => (
                    <Col span={12} key={index}>
                      <StockRecommendationCard recommendation={recommendation} />
                    </Col>
                  ))}
                </Row>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无股票推荐"
                />
              )}
            </Spin>
          </TabPane>
        </Tabs>
      </Card>

      {/* 推荐详情弹窗 */}
      <Modal
        title="推荐详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="feedback" onClick={() => setFeedbackVisible(true)}>
            提供反馈
          </Button>,
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedRecommendation && (
          <div className="space-y-4">
            <Alert
              message="推荐说明"
              description="此推荐基于您的历史行为、风险偏好和市场分析生成，仅供参考，投资有风险。"
              type="info"
              showIcon
            />

            <Descriptions column={2} bordered>
              <Descriptions.Item label="名称">
                {selectedRecommendation.strategy_name || selectedRecommendation.name}
              </Descriptions.Item>
              <Descriptions.Item label="推荐分数">
                <Progress
                  percent={selectedRecommendation.score * 100}
                  size="small"
                  strokeColor={getScoreColor(selectedRecommendation.score)}
                />
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                {(selectedRecommendation.confidence * 100).toFixed(1)}%
              </Descriptions.Item>
              <Descriptions.Item label="推荐理由" span={2}>
                <Space wrap>
                  {selectedRecommendation.reasons.map((reason, index) => (
                    <Tag key={index} icon={<CheckCircleOutlined />}>
                      {reason}
                    </Tag>
                  ))}
                </Space>
              </Descriptions.Item>
            </Descriptions>

            {selectedRecommendation.performance && (
              <div>
                <h4 className="font-medium mb-2">历史表现</h4>
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="年化收益率"
                      value={selectedRecommendation.performance.annual_return}
                      precision={2}
                      suffix="%"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="最大回撤"
                      value={Math.abs(selectedRecommendation.performance.max_drawdown || 0)}
                      precision={2}
                      suffix="%"
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="夏普比率"
                      value={selectedRecommendation.performance.sharpe_ratio}
                      precision={2}
                    />
                  </Col>
                </Row>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 反馈弹窗 */}
      <Modal
        title="推荐反馈"
        open={feedbackVisible}
        onCancel={() => setFeedbackVisible(false)}
        onOk={() => {
          // 提交反馈逻辑
          submitFeedback({
            rating: 5,
            feedback: '',
            is_clicked: true,
            is_adopted: false
          });
        }}
      >
        <div className="space-y-4">
          <div>
            <div className="mb-2">推荐质量评分</div>
            <Rate defaultValue={5} />
          </div>
          <div>
            <div className="mb-2">反馈意见</div>
            <Input.TextArea
              rows={4}
              placeholder="请输入您的反馈意见..."
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};
