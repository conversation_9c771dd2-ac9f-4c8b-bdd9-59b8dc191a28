"""
投资组合相关的Pydantic模型

定义投资组合、持仓、交易记录等的请求和响应模型
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from pydantic import BaseModel, Field, validator


# =============================================================================
# 投资组合模型
# =============================================================================

class PortfolioBase(BaseModel):
    """投资组合基础模型"""
    name: str = Field(..., min_length=1, max_length=100, description="投资组合名称")
    description: Optional[str] = Field(None, max_length=500, description="投资组合描述")


class PortfolioCreateRequest(PortfolioBase):
    """创建投资组合请求模型"""
    initial_capital: Decimal = Field(..., gt=0, description="初始资金")
    is_default: bool = Field(False, description="是否为默认投资组合")


class PortfolioUpdateRequest(BaseModel):
    """更新投资组合请求模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="投资组合名称")
    description: Optional[str] = Field(None, max_length=500, description="投资组合描述")
    is_default: Optional[bool] = Field(None, description="是否为默认投资组合")


class PortfolioResponse(PortfolioBase):
    """投资组合响应模型"""
    id: int
    user_id: int
    initial_capital: Decimal
    current_capital: Decimal
    available_cash: Decimal
    total_market_value: Decimal
    total_pnl: Decimal
    total_pnl_percent: Decimal
    today_pnl: Decimal
    today_pnl_percent: Decimal
    max_drawdown: Optional[Decimal]
    sharpe_ratio: Optional[Decimal]
    volatility: Optional[Decimal]
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PortfolioSummaryResponse(BaseModel):
    """投资组合摘要响应模型"""
    portfolio_id: int
    name: str
    total_capital: float
    available_cash: float
    total_market_value: float
    total_pnl: float
    total_pnl_percent: float
    today_pnl: float
    today_pnl_percent: float
    position_count: int
    transaction_count: int
    created_at: datetime
    updated_at: datetime


# =============================================================================
# 持仓模型
# =============================================================================

class PositionBase(BaseModel):
    """持仓基础模型"""
    stock_code: str = Field(..., min_length=1, max_length=20, description="股票代码")
    stock_name: str = Field(..., min_length=1, max_length=100, description="股票名称")


class PositionResponse(PositionBase):
    """持仓响应模型"""
    id: int
    portfolio_id: int
    shares: int
    available_shares: int
    avg_cost: Decimal
    current_price: Decimal
    market_value: Decimal
    cost_value: Decimal
    pnl: Decimal
    pnl_percent: Decimal
    weight: Decimal
    created_at: datetime
    updated_at: datetime
    last_price_update: Optional[datetime]

    class Config:
        from_attributes = True


# =============================================================================
# 交易记录模型
# =============================================================================

class TransactionBase(BaseModel):
    """交易记录基础模型"""
    stock_code: str = Field(..., min_length=1, max_length=20, description="股票代码")
    stock_name: str = Field(..., min_length=1, max_length=100, description="股票名称")
    transaction_type: str = Field(..., regex="^(BUY|SELL)$", description="交易类型")
    shares: int = Field(..., gt=0, description="交易股数")
    price: Decimal = Field(..., gt=0, description="交易价格")


class TransactionCreateRequest(TransactionBase):
    """创建交易记录请求模型"""
    commission: Optional[Decimal] = Field(0, ge=0, description="手续费")
    tax: Optional[Decimal] = Field(0, ge=0, description="税费")
    notes: Optional[str] = Field(None, max_length=500, description="备注")
    transaction_date: Optional[datetime] = Field(None, description="交易日期")


class TransactionResponse(TransactionBase):
    """交易记录响应模型"""
    id: int
    portfolio_id: int
    amount: Decimal
    commission: Decimal
    tax: Decimal
    total_cost: Decimal
    status: str
    notes: Optional[str]
    source: str
    transaction_date: datetime
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 自选股模型
# =============================================================================

class WatchListBase(BaseModel):
    """自选股基础模型"""
    stock_code: str = Field(..., min_length=1, max_length=20, description="股票代码")
    stock_name: str = Field(..., min_length=1, max_length=100, description="股票名称")


class WatchListCreateRequest(WatchListBase):
    """添加自选股请求模型"""
    add_price: Optional[Decimal] = Field(None, gt=0, description="添加时价格")
    notes: Optional[str] = Field(None, max_length=500, description="备注")
    group_name: Optional[str] = Field(None, max_length=50, description="分组名称")


class WatchListResponse(WatchListBase):
    """自选股响应模型"""
    id: int
    user_id: int
    add_price: Optional[Decimal]
    notes: Optional[str]
    sort_order: int
    group_name: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# =============================================================================
# 投资组合分析模型
# =============================================================================

class PortfolioAnalysisResponse(BaseModel):
    """投资组合分析响应模型"""
    portfolio_id: int
    analysis_date: datetime
    
    # 收益分析
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    
    # 风险分析
    var_95: float  # 95% VaR
    var_99: float  # 99% VaR
    beta: Optional[float]
    
    # 持仓分析
    position_count: int
    concentration_ratio: float  # 前5大持仓占比
    sector_diversification: dict  # 行业分布
    
    # 交易分析
    total_trades: int
    win_rate: float
    avg_holding_period: float


class PortfolioPerformanceResponse(BaseModel):
    """投资组合表现响应模型"""
    portfolio_id: int
    date_range: dict
    
    # 净值曲线
    nav_curve: List[dict]  # [{"date": "2024-01-01", "nav": 1.0, "benchmark": 1.0}]
    
    # 收益分布
    daily_returns: List[float]
    monthly_returns: List[float]
    
    # 回撤分析
    drawdown_curve: List[dict]  # [{"date": "2024-01-01", "drawdown": 0.0}]
    max_drawdown_period: dict  # {"start": "2024-01-01", "end": "2024-01-31", "drawdown": -0.1}
    
    # 基准比较
    benchmark_comparison: dict


# =============================================================================
# 批量操作模型
# =============================================================================

class BatchTransactionRequest(BaseModel):
    """批量交易请求模型"""
    transactions: List[TransactionCreateRequest]
    
    @validator('transactions')
    def validate_transactions(cls, v):
        if len(v) == 0:
            raise ValueError('交易列表不能为空')
        if len(v) > 100:
            raise ValueError('单次最多支持100笔交易')
        return v


class PositionAdjustmentRequest(BaseModel):
    """持仓调整请求模型"""
    adjustments: List[dict]  # [{"stock_code": "000001.XSHE", "target_weight": 0.1}]
    
    @validator('adjustments')
    def validate_adjustments(cls, v):
        total_weight = sum(adj.get('target_weight', 0) for adj in v)
        if total_weight > 1.0:
            raise ValueError('目标权重总和不能超过100%')
        return v


# =============================================================================
# 统计和报告模型
# =============================================================================

class PortfolioStatsResponse(BaseModel):
    """投资组合统计响应模型"""
    portfolio_id: int
    stats_date: datetime
    
    # 基础统计
    total_value: Decimal
    cash_ratio: float
    position_count: int
    avg_position_size: float
    
    # 收益统计
    total_return_1d: float
    total_return_1w: float
    total_return_1m: float
    total_return_3m: float
    total_return_6m: float
    total_return_1y: float
    total_return_ytd: float
    total_return_inception: float
    
    # 风险统计
    volatility_1m: float
    volatility_3m: float
    volatility_1y: float
    max_drawdown_1m: float
    max_drawdown_3m: float
    max_drawdown_1y: float
    
    # 行业分布
    sector_allocation: dict
    
    # 市值分布
    market_cap_allocation: dict
