"""
日志配置模块

统一的日志管理，支持结构化日志和多种输出格式
"""

import sys
from pathlib import Path
from typing import Any, Dict

from loguru import logger

from app.core.config import settings


def setup_logging() -> None:
    """配置日志系统"""
    
    # 移除默认处理器
    logger.remove()
    
    # 创建日志目录
    log_file_path = Path(settings.LOG_FILE)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 控制台日志格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件日志格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # JSON格式（用于生产环境）
    json_format = "{time} | {level} | {name} | {function} | {line} | {message}"
    
    # 控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 文件处理器
    if settings.LOG_FORMAT == "json":
        logger.add(
            settings.LOG_FILE,
            format=json_format,
            level=settings.LOG_LEVEL,
            rotation=settings.LOG_MAX_SIZE,
            retention=settings.LOG_BACKUP_COUNT,
            compression="gz",
            serialize=True,  # JSON格式
            backtrace=True,
            diagnose=True,
        )
    else:
        logger.add(
            settings.LOG_FILE,
            format=file_format,
            level=settings.LOG_LEVEL,
            rotation=settings.LOG_MAX_SIZE,
            retention=settings.LOG_BACKUP_COUNT,
            compression="gz",
            backtrace=True,
            diagnose=True,
        )
    
    # 错误日志单独文件
    error_log_path = log_file_path.parent / "error.log"
    logger.add(
        str(error_log_path),
        format=file_format,
        level="ERROR",
        rotation=settings.LOG_MAX_SIZE,
        retention=settings.LOG_BACKUP_COUNT,
        compression="gz",
        backtrace=True,
        diagnose=True,
    )
    
    logger.info(f"日志系统初始化完成 - 级别: {settings.LOG_LEVEL}")


def get_logger(name: str = None) -> Any:
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


# 初始化日志系统
setup_logging()

# 导出logger实例
__all__ = ["logger", "get_logger", "setup_logging"]
