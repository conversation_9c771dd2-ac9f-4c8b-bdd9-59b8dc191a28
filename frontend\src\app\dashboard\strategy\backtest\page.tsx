'use client';

/**
 * 策略回测页面 - 增强版
 * 
 * 提供完整的策略回测功能，包括参数配置、结果展示、交易记录等
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Button, 
  Typography, 
  Form, 
  DatePicker, 
  Select, 
  InputNumber,
  Table,
  Progress,
  Spin,
  message,
  Tabs,
  Alert,
  Space,
  Divider
} from 'antd';
import { 
  LineChartOutlined,
  PlayCircleOutlined,
  BarChartOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
  StopOutlined,
  DownloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';
import { motion } from 'framer-motion';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

function BacktestContent() {
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [backtestRunning, setBacktestRunning] = useState(false);
  const [backtestResults, setBacktestResults] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('config');
  const [progress, setProgress] = useState(0);

  // 模拟回测结果数据
  const mockBacktestResults = {
    totalReturn: 28.45,
    annualizedReturn: 22.18,
    maxDrawdown: -12.34,
    sharpeRatio: 1.67,
    volatility: 15.23,
    winRate: 72.5,
    totalTrades: 89,
    profitFactor: 2.34,
    startDate: '2023-01-01',
    endDate: '2024-08-27',
    initialCapital: 1000000,
    finalCapital: 1284500,
    trades: [
      { key: '1', date: '2023-01-15', symbol: '000001.XSHE', name: '平安银行', action: 'BUY', price: 11.20, quantity: 10000, pnl: 0 },
      { key: '2', date: '2023-02-20', symbol: '000001.XSHE', name: '平安银行', action: 'SELL', price: 12.45, quantity: 10000, pnl: 12500 },
      { key: '3', date: '2023-03-10', symbol: '600036.XSHG', name: '招商银行', action: 'BUY', price: 32.80, quantity: 5000, pnl: 0 },
      { key: '4', date: '2023-04-15', symbol: '600036.XSHG', name: '招商银行', action: 'SELL', price: 35.67, quantity: 5000, pnl: 14350 },
      { key: '5', date: '2023-05-20', symbol: '600519.XSHG', name: '贵州茅台', action: 'BUY', price: 1720.00, quantity: 100, pnl: 0 },
      { key: '6', date: '2023-06-25', symbol: '600519.XSHG', name: '贵州茅台', action: 'SELL', price: 1678.90, quantity: 100, pnl: -4110 }
    ],
    monthlyReturns: [
      { month: '2023-01', return: 3.2 },
      { month: '2023-02', return: 5.8 },
      { month: '2023-03', return: -1.5 },
      { month: '2023-04', return: 4.1 },
      { month: '2023-05', return: 2.3 },
      { month: '2023-06', return: -2.1 },
      { month: '2023-07', return: 6.2 },
      { month: '2023-08', return: 1.8 }
    ]
  };

  const backtestStats = backtestResults ? [
    {
      title: '总收益率',
      value: backtestResults.totalReturn,
      precision: 2,
      suffix: '%',
      valueStyle: { color: backtestResults.totalReturn >= 0 ? '#52c41a' : '#f5222d' },
      prefix: backtestResults.totalReturn >= 0 ? <RiseOutlined /> : <FallOutlined />
    },
    {
      title: '年化收益率',
      value: backtestResults.annualizedReturn,
      precision: 2,
      suffix: '%',
      valueStyle: { color: backtestResults.annualizedReturn >= 0 ? '#52c41a' : '#f5222d' },
      prefix: <BarChartOutlined />
    },
    {
      title: '最大回撤',
      value: Math.abs(backtestResults.maxDrawdown),
      precision: 2,
      suffix: '%',
      valueStyle: { color: '#f5222d' },
      prefix: <FallOutlined />
    },
    {
      title: '夏普比率',
      value: backtestResults.sharpeRatio,
      precision: 3,
      valueStyle: { color: '#1890ff' },
      prefix: <LineChartOutlined />
    },
    {
      title: '胜率',
      value: backtestResults.winRate,
      precision: 1,
      suffix: '%',
      valueStyle: { color: '#722ed1' },
      prefix: <RiseOutlined />
    },
    {
      title: '交易次数',
      value: backtestResults.totalTrades,
      precision: 0,
      valueStyle: { color: '#13c2c2' },
      prefix: <BarChartOutlined />
    }
  ] : [];

  // 运行回测
  const runBacktest = async (values: any) => {
    setBacktestRunning(true);
    setActiveTab('results');
    setProgress(0);
    
    try {
      // 模拟回测进度
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 300);

      // 模拟回测运行
      await new Promise(resolve => setTimeout(resolve, 3000));
      setBacktestResults(mockBacktestResults);
      message.success('回测完成！');
    } catch (error) {
      message.error('回测失败，请重试');
    } finally {
      setBacktestRunning(false);
      setProgress(100);
    }
  };

  const stopBacktest = () => {
    setBacktestRunning(false);
    setProgress(0);
    message.info('回测已停止');
  };

  const tradeColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 100
    },
    {
      title: '股票',
      key: 'stock',
      width: 120,
      render: (record: any) => (
        <div>
          <Text strong>{record.name}</Text>
          <br />
          <Text type="secondary" className="text-xs">{record.symbol}</Text>
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 60,
      render: (action: string) => (
        <Text style={{ color: action === 'BUY' ? '#52c41a' : '#f5222d' }}>
          {action === 'BUY' ? '买入' : '卖出'}
        </Text>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      align: 'right' as const,
      render: (price: number) => `¥${price.toFixed(2)}`
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'right' as const,
      render: (quantity: number) => `${quantity.toLocaleString()}`
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      width: 100,
      align: 'right' as const,
      render: (pnl: number) => (
        <Text style={{ color: pnl >= 0 ? '#52c41a' : '#f5222d' }}>
          {pnl >= 0 ? '+' : ''}¥{pnl.toLocaleString()}
        </Text>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <Title level={2} className="!mb-2">策略回测</Title>
          <Text type="secondary">测试和验证您的投资策略表现</Text>
        </div>
        <Space>
          <Button onClick={() => router.push('/dashboard/strategy')}>
            返回策略列表
          </Button>
          <Button icon={<DownloadOutlined />}>
            导出报告
          </Button>
        </Space>
      </motion.div>

      {/* 回测配置和结果 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="回测配置" key="config">
              <Form
                form={form}
                layout="vertical"
                onFinish={runBacktest}
                initialValues={{
                  strategy: 'ma_crossover',
                  period: ['2023-01-01', '2024-08-27'],
                  initialCapital: 1000000,
                  benchmark: 'HS300'
                }}
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="strategy"
                      label="选择策略"
                      rules={[{ required: true, message: '请选择策略' }]}
                    >
                      <Select placeholder="选择要回测的策略">
                        <Option value="ma_crossover">均线交叉策略</Option>
                        <Option value="momentum">动量策略</Option>
                        <Option value="mean_reversion">均值回归策略</Option>
                        <Option value="rsi_strategy">RSI策略</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="period"
                      label="回测周期"
                      rules={[{ required: true, message: '请选择回测周期' }]}
                    >
                      <RangePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="initialCapital"
                      label="初始资金"
                      rules={[{ required: true, message: '请输入初始资金' }]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={10000}
                        max={100000000}
                        formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/¥\s?|(,*)/g, '')}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="benchmark"
                      label="基准指数"
                    >
                      <Select placeholder="选择基准指数">
                        <Option value="HS300">沪深300</Option>
                        <Option value="CSI500">中证500</Option>
                        <Option value="SSE50">上证50</Option>
                        <Option value="SZSE100">深证100</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="commission"
                      label="手续费率"
                      initialValue={0.0003}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={0}
                        max={0.01}
                        step={0.0001}
                        formatter={value => `${(Number(value) * 100).toFixed(2)}%`}
                        parser={value => Number(value!.replace('%', '')) / 100}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="slippage"
                      label="滑点"
                      initialValue={0.001}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={0}
                        max={0.01}
                        step={0.0001}
                        formatter={value => `${(Number(value) * 100).toFixed(2)}%`}
                        parser={value => Number(value!.replace('%', '')) / 100}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />

                <div className="text-center">
                  <Space size="large">
                    <Button
                      type="primary"
                      size="large"
                      icon={<PlayCircleOutlined />}
                      htmlType="submit"
                      loading={backtestRunning}
                      disabled={backtestRunning}
                    >
                      {backtestRunning ? '回测中...' : '开始回测'}
                    </Button>
                    {backtestRunning && (
                      <Button
                        size="large"
                        icon={<StopOutlined />}
                        onClick={stopBacktest}
                      >
                        停止回测
                      </Button>
                    )}
                  </Space>
                </div>
              </Form>
            </TabPane>

            <TabPane tab="回测结果" key="results">
              {backtestRunning ? (
                <div className="text-center py-12">
                  <Spin size="large" />
                  <div className="mt-4">
                    <Text>正在运行回测...</Text>
                    <Progress percent={progress} className="mt-2" />
                  </div>
                </div>
              ) : backtestResults ? (
                <div className="space-y-6">
                  {/* 核心指标 */}
                  <Row gutter={[16, 16]}>
                    {backtestStats.map((stat, index) => (
                      <Col xs={24} sm={12} lg={4} key={index}>
                        <Card className="text-center">
                          <Statistic
                            title={stat.title}
                            value={stat.value}
                            precision={stat.precision}
                            suffix={stat.suffix}
                            valueStyle={stat.valueStyle}
                            prefix={stat.prefix}
                          />
                        </Card>
                      </Col>
                    ))}
                  </Row>

                  {/* 资金曲线 */}
                  <Card title="资金曲线">
                    <Alert
                      message="图表功能"
                      description="资金曲线图表将在此显示，展示策略的净值变化"
                      type="info"
                      showIcon
                    />
                  </Card>

                  {/* 交易记录 */}
                  <Card title={`交易记录 (${backtestResults.trades.length}笔)`}>
                    <Table
                      dataSource={backtestResults.trades}
                      columns={tradeColumns}
                      pagination={{ pageSize: 10 }}
                      size="middle"
                      scroll={{ x: 600 }}
                    />
                  </Card>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Text type="secondary">请先配置回测参数并运行回测</Text>
                </div>
              )}
            </TabPane>
          </Tabs>
        </Card>
      </motion.div>
    </div>
  );
}

export default function BacktestPage() {
  return (
    <ClientAuthWrapper>
      <BacktestContent />
    </ClientAuthWrapper>
  );
}
