"""
多模态数据相关API端点

提供新闻情感分析、社交媒体数据、另类数据源等多模态数据的API接口
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import select, desc, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1.auth import get_current_active_user
from app.core.database import get_db
from app.core.logging import logger
from app.models.user import User
from app.models.multimodal import (
    NewsSource, NewsArticle, NewsSentiment,
    SocialMediaSource, SocialMediaPost, SocialSentiment,
    AlternativeDataSource, AlternativeData, MultimodalSignal
)
from app.schemas.base import BaseResponse, PaginatedResponse, PaginationInfo
from app.services.news_sentiment_service import news_sentiment_service
from app.services.social_media_service import social_media_service
from app.services.alternative_data_service import alternative_data_service
from app.services.multimodal_signal_service import multimodal_signal_service

router = APIRouter()


# =============================================================================
# 新闻情感分析
# =============================================================================

@router.get("/news/sources", response_model=BaseResponse[List[dict]])
async def get_news_sources(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取新闻数据源列表"""
    try:
        query = select(NewsSource).where(NewsSource.is_active == True)
        result = await db.execute(query)
        sources = result.scalars().all()
        
        source_list = []
        for source in sources:
            source_list.append({
                "id": source.id,
                "name": source.name,
                "display_name": source.display_name,
                "description": source.description,
                "source_type": source.source_type,
                "supported_languages": source.supported_languages,
                "reliability_score": float(source.reliability_score),
                "update_frequency": source.update_frequency,
                "total_articles": source.total_articles,
                "last_updated": source.last_updated.isoformat() if source.last_updated else None
            })
        
        return BaseResponse(
            code=200,
            message="获取新闻数据源成功",
            data=source_list
        )
        
    except Exception as e:
        logger.error(f"获取新闻数据源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻数据源失败"
        )


@router.post("/news/collect", response_model=BaseResponse[dict])
async def collect_news_data(
    source_id: int,
    max_articles: int = Query(100, ge=1, le=1000),
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """采集新闻数据"""
    try:
        # 添加后台任务
        background_tasks.add_task(
            _collect_news_task,
            source_id,
            max_articles,
            db
        )
        
        return BaseResponse(
            code=200,
            message="新闻采集任务已提交",
            data={
                "source_id": source_id,
                "max_articles": max_articles,
                "task_submitted": True
            }
        )
        
    except Exception as e:
        logger.error(f"提交新闻采集任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交新闻采集任务失败"
        )


@router.get("/news/sentiment/{symbol}", response_model=BaseResponse[dict])
async def get_news_sentiment(
    symbol: str,
    hours: int = Query(24, ge=1, le=168),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取股票新闻情感分析"""
    try:
        start_time = datetime.utcnow() - timedelta(hours=hours)
        
        # 查询相关新闻情感
        query = (
            select(NewsSentiment, NewsArticle)
            .join(NewsArticle, NewsSentiment.article_id == NewsArticle.id)
            .where(
                and_(
                    NewsArticle.published_at >= start_time,
                    NewsArticle.related_symbols.contains([symbol])
                )
            )
            .order_by(desc(NewsArticle.published_at))
        )
        
        result = await db.execute(query)
        sentiment_data = result.all()
        
        if not sentiment_data:
            return BaseResponse(
                code=200,
                message="暂无相关新闻情感数据",
                data={
                    "symbol": symbol,
                    "sentiment_summary": {
                        "average_sentiment": 0.0,
                        "positive_count": 0,
                        "negative_count": 0,
                        "neutral_count": 0,
                        "total_articles": 0
                    },
                    "sentiment_timeline": []
                }
            )
        
        # 计算情感统计
        sentiments = [s.NewsSentiment for s in sentiment_data]
        articles = [s.NewsArticle for s in sentiment_data]
        
        positive_count = len([s for s in sentiments if s.sentiment_label == 'positive'])
        negative_count = len([s for s in sentiments if s.sentiment_label == 'negative'])
        neutral_count = len([s for s in sentiments if s.sentiment_label == 'neutral'])
        
        avg_sentiment = sum([s.sentiment_score for s in sentiments]) / len(sentiments)
        
        # 构建时间线
        timeline = []
        for sentiment, article in zip(sentiments, articles):
            timeline.append({
                "timestamp": article.published_at.isoformat(),
                "title": article.title,
                "sentiment_label": sentiment.sentiment_label,
                "sentiment_score": float(sentiment.sentiment_score),
                "confidence": float(sentiment.confidence),
                "url": article.url
            })
        
        return BaseResponse(
            code=200,
            message="获取新闻情感分析成功",
            data={
                "symbol": symbol,
                "time_range_hours": hours,
                "sentiment_summary": {
                    "average_sentiment": float(avg_sentiment),
                    "positive_count": positive_count,
                    "negative_count": negative_count,
                    "neutral_count": neutral_count,
                    "total_articles": len(sentiment_data)
                },
                "sentiment_timeline": timeline
            }
        )
        
    except Exception as e:
        logger.error(f"获取新闻情感分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取新闻情感分析失败"
        )


# =============================================================================
# 社交媒体数据
# =============================================================================

@router.get("/social/sources", response_model=BaseResponse[List[dict]])
async def get_social_media_sources(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取社交媒体数据源列表"""
    try:
        query = select(SocialMediaSource).where(SocialMediaSource.is_active == True)
        result = await db.execute(query)
        sources = result.scalars().all()
        
        source_list = []
        for source in sources:
            source_list.append({
                "id": source.id,
                "platform": source.platform,
                "platform_name": source.platform_name,
                "description": source.description,
                "supported_data_types": source.supported_data_types,
                "real_time_support": source.real_time_support,
                "data_quality_score": float(source.data_quality_score),
                "total_posts": source.total_posts,
                "active_users": source.active_users,
                "last_sync": source.last_sync.isoformat() if source.last_sync else None
            })
        
        return BaseResponse(
            code=200,
            message="获取社交媒体数据源成功",
            data=source_list
        )
        
    except Exception as e:
        logger.error(f"获取社交媒体数据源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取社交媒体数据源失败"
        )


@router.post("/social/collect", response_model=BaseResponse[dict])
async def collect_social_media_data(
    source_id: int,
    keywords: List[str],
    max_posts: int = Query(100, ge=1, le=1000),
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """采集社交媒体数据"""
    try:
        # 添加后台任务
        background_tasks.add_task(
            _collect_social_media_task,
            source_id,
            keywords,
            max_posts,
            db
        )
        
        return BaseResponse(
            code=200,
            message="社交媒体采集任务已提交",
            data={
                "source_id": source_id,
                "keywords": keywords,
                "max_posts": max_posts,
                "task_submitted": True
            }
        )
        
    except Exception as e:
        logger.error(f"提交社交媒体采集任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交社交媒体采集任务失败"
        )


@router.get("/social/sentiment/{symbol}", response_model=BaseResponse[dict])
async def get_social_sentiment_trends(
    symbol: str,
    hours: int = Query(24, ge=1, le=168),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取社交媒体情感趋势"""
    try:
        result = await social_media_service.get_sentiment_trends(symbol, hours, db)
        
        if result["success"]:
            return BaseResponse(
                code=200,
                message="获取社交媒体情感趋势成功",
                data=result
            )
        else:
            return BaseResponse(
                code=404,
                message=result["error"],
                data={}
            )
        
    except Exception as e:
        logger.error(f"获取社交媒体情感趋势失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取社交媒体情感趋势失败"
        )


# =============================================================================
# 另类数据
# =============================================================================

@router.get("/alternative/sources", response_model=BaseResponse[List[dict]])
async def get_alternative_data_sources(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取另类数据源列表"""
    try:
        query = select(AlternativeDataSource).where(AlternativeDataSource.is_active == True)
        result = await db.execute(query)
        sources = result.scalars().all()
        
        source_list = []
        for source in sources:
            source_list.append({
                "id": source.id,
                "name": source.name,
                "display_name": source.display_name,
                "description": source.description,
                "data_category": source.data_category,
                "provider": source.provider,
                "update_frequency": source.update_frequency,
                "data_quality_score": float(source.data_quality_score),
                "geographic_coverage": source.geographic_coverage,
                "temporal_coverage": source.temporal_coverage,
                "total_requests": source.total_requests,
                "successful_requests": source.successful_requests,
                "last_request": source.last_request.isoformat() if source.last_request else None
            })
        
        return BaseResponse(
            code=200,
            message="获取另类数据源成功",
            data=source_list
        )
        
    except Exception as e:
        logger.error(f"获取另类数据源失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取另类数据源失败"
        )


@router.post("/alternative/collect", response_model=BaseResponse[dict])
async def collect_alternative_data(
    source_id: int,
    collection_config: dict,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """采集另类数据"""
    try:
        # 添加后台任务
        background_tasks.add_task(
            _collect_alternative_data_task,
            source_id,
            collection_config,
            db
        )
        
        return BaseResponse(
            code=200,
            message="另类数据采集任务已提交",
            data={
                "source_id": source_id,
                "collection_config": collection_config,
                "task_submitted": True
            }
        )
        
    except Exception as e:
        logger.error(f"提交另类数据采集任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交另类数据采集任务失败"
        )


# =============================================================================
# 多模态信号
# =============================================================================

@router.post("/signals/generate", response_model=BaseResponse[dict])
async def generate_multimodal_signal(
    symbol: str,
    time_window: int = Query(24, ge=1, le=168),
    signal_type: str = Query("composite", regex="^(composite|sentiment|trend|event)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """生成多模态信号"""
    try:
        result = await multimodal_signal_service.generate_multimodal_signal(
            symbol, time_window, signal_type, db
        )
        
        if result["success"]:
            return BaseResponse(
                code=200,
                message="多模态信号生成成功",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成多模态信号失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成多模态信号失败"
        )


@router.get("/signals/{symbol}", response_model=BaseResponse[PaginatedResponse[dict]])
async def get_multimodal_signals(
    symbol: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    hours: int = Query(168, ge=1, le=720),  # 默认一周
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取多模态信号历史"""
    try:
        start_time = datetime.utcnow() - timedelta(hours=hours)
        
        # 查询总数
        count_query = select(func.count(MultimodalSignal.id)).where(
            and_(
                MultimodalSignal.target_symbol == symbol,
                MultimodalSignal.signal_timestamp >= start_time
            )
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        offset = (page - 1) * page_size
        query = (
            select(MultimodalSignal)
            .where(
                and_(
                    MultimodalSignal.target_symbol == symbol,
                    MultimodalSignal.signal_timestamp >= start_time
                )
            )
            .order_by(desc(MultimodalSignal.signal_timestamp))
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        signals = result.scalars().all()
        
        # 构建分页信息
        total_pages = (total + page_size - 1) // page_size
        pagination = PaginationInfo(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        signal_list = []
        for signal in signals:
            signal_list.append({
                "id": signal.id,
                "signal_name": signal.signal_name,
                "signal_type": signal.signal_type,
                "signal_category": signal.signal_category,
                "signal_value": float(signal.signal_value),
                "signal_strength": float(signal.signal_strength),
                "confidence": float(signal.confidence),
                "signal_timestamp": signal.signal_timestamp.isoformat(),
                "valid_from": signal.valid_from.isoformat() if signal.valid_from else None,
                "valid_until": signal.valid_until.isoformat() if signal.valid_until else None,
                "data_sources": signal.data_sources,
                "source_weights": signal.source_weights,
                "feature_importance": signal.feature_importance
            })
        
        return BaseResponse(
            code=200,
            message="获取多模态信号历史成功",
            data=PaginatedResponse(
                items=signal_list,
                pagination=pagination
            )
        )
        
    except Exception as e:
        logger.error(f"获取多模态信号历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取多模态信号历史失败"
        )


# =============================================================================
# 后台任务函数
# =============================================================================

async def _collect_news_task(source_id: int, max_articles: int, db: AsyncSession):
    """新闻采集后台任务"""
    try:
        result = await news_sentiment_service.collect_and_analyze_news(
            source_id, db, max_articles
        )
        if result["success"]:
            logger.info(f"新闻采集完成: {result}")
        else:
            logger.error(f"新闻采集失败: {result['error']}")
    except Exception as e:
        logger.error(f"新闻采集任务失败: {e}")


async def _collect_social_media_task(
    source_id: int, 
    keywords: List[str], 
    max_posts: int, 
    db: AsyncSession
):
    """社交媒体采集后台任务"""
    try:
        result = await social_media_service.collect_social_media_data(
            source_id, keywords, db, max_posts
        )
        if result["success"]:
            logger.info(f"社交媒体采集完成: {result}")
        else:
            logger.error(f"社交媒体采集失败: {result['error']}")
    except Exception as e:
        logger.error(f"社交媒体采集任务失败: {e}")


async def _collect_alternative_data_task(
    source_id: int, 
    collection_config: Dict[str, Any], 
    db: AsyncSession
):
    """另类数据采集后台任务"""
    try:
        result = await alternative_data_service.collect_alternative_data(
            source_id, collection_config, db
        )
        if result["success"]:
            logger.info(f"另类数据采集完成: {result}")
        else:
            logger.error(f"另类数据采集失败: {result['error']}")
    except Exception as e:
        logger.error(f"另类数据采集任务失败: {e}")
