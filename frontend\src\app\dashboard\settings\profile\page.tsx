'use client';

/**
 * 用户资料设置页面
 * 
 * 用户个人信息管理、头像上传、密码修改等
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Typography,
  Avatar,
  Upload,
  message,
  Row,
  Col,
  Divider,
  Space,
  Modal,
  Descriptions,
  Tag,
  Progress,
  Alert,
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  EditOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import type { UploadProps } from 'antd';

import { useAuth, useAuthActions } from '@/store/auth';
import apiClient from '@/services/api';

const { Title, Text } = Typography;

interface ProfileFormData {
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const { updateProfile } = useAuthActions();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(user?.avatarUrl);

  // 更新个人资料
  const handleProfileUpdate = async (values: ProfileFormData) => {
    try {
      setLoading(true);
      await updateProfile({
        ...values,
        avatarUrl,
      });
      message.success('个人资料更新成功！');
    } catch (error: any) {
      message.error(error.message || '更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 修改密码
  const handlePasswordChange = async (values: PasswordFormData) => {
    try {
      setPasswordLoading(true);
      
      const response = await apiClient.post('/auth/change-password', {
        current_password: values.currentPassword,
        new_password: values.newPassword,
        confirm_password: values.confirmPassword,
      });
      
      if (response.code === 200) {
        message.success('密码修改成功！');
        setShowPasswordModal(false);
        passwordForm.resetFields();
      }
    } catch (error: any) {
      message.error(error.message || '密码修改失败');
    } finally {
      setPasswordLoading(false);
    }
  };

  // 头像上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    action: '/api/v1/upload/avatar',
    headers: {
      authorization: `Bearer ${localStorage.getItem('access_token')}`,
    },
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片！');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB！');
        return false;
      }
      return true;
    },
    onChange: (info) => {
      if (info.file.status === 'uploading') {
        setLoading(true);
      }
      if (info.file.status === 'done') {
        setLoading(false);
        if (info.file.response?.code === 200) {
          setAvatarUrl(info.file.response.data.url);
          message.success('头像上传成功！');
        } else {
          message.error('头像上传失败');
        }
      }
      if (info.file.status === 'error') {
        setLoading(false);
        message.error('头像上传失败');
      }
    },
  };

  // 获取订阅状态颜色
  const getSubscriptionColor = (type: string) => {
    switch (type) {
      case 'free':
        return 'default';
      case 'basic':
        return 'blue';
      case 'premium':
        return 'gold';
      case 'enterprise':
        return 'purple';
      default:
        return 'default';
    }
  };

  // 获取订阅状态文本
  const getSubscriptionText = (type: string) => {
    switch (type) {
      case 'free':
        return '免费版';
      case 'basic':
        return '基础版';
      case 'premium':
        return '高级版';
      case 'enterprise':
        return '企业版';
      default:
        return '未知';
    }
  };

  useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        fullName: user.fullName,
        phone: user.phone,
      });
      setAvatarUrl(user.avatarUrl);
    }
  }, [user, profileForm]);

  if (!user) {
    return <div>加载中...</div>;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <Title level={2} className="!mb-2">
          个人资料
        </Title>
        <Text type="secondary">
          管理您的个人信息和账户设置
        </Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 基本信息 */}
        <Col xs={24} lg={16}>
          <Card title="基本信息" extra={
            <Button type="link" icon={<EditOutlined />}>
              编辑
            </Button>
          }>
            <Form
              form={profileForm}
              layout="vertical"
              onFinish={handleProfileUpdate}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="fullName"
                    label="真实姓名"
                    rules={[
                      { max: 50, message: '姓名长度不能超过50个字符' },
                    ]}
                  >
                    <Input
                      placeholder="请输入真实姓名"
                      prefix={<UserOutlined />}
                    />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="phone"
                    label="手机号码"
                    rules={[
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' },
                    ]}
                  >
                    <Input
                      placeholder="请输入手机号码"
                      prefix={<PhoneOutlined />}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<SaveOutlined />}
                >
                  保存更改
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 头像和账户信息 */}
        <Col xs={24} lg={8}>
          <Card title="头像设置">
            <div className="text-center space-y-4">
              <div className="relative inline-block">
                <Avatar
                  size={120}
                  src={avatarUrl}
                  icon={<UserOutlined />}
                  className="border-4 border-gray-100"
                />
                <Upload {...uploadProps} showUploadList={false}>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<CameraOutlined />}
                    className="absolute -bottom-2 -right-2"
                    size="small"
                  />
                </Upload>
              </div>
              
              <div>
                <Text strong className="text-lg block">
                  {user.fullName || user.username}
                </Text>
                <Text type="secondary" className="block">
                  {user.email}
                </Text>
              </div>
            </div>
          </Card>

          <Card title="账户信息" className="mt-4">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="用户名">
                {user.username}
              </Descriptions.Item>
              <Descriptions.Item label="邮箱">
                <Space>
                  {user.email}
                  {user.isVerified ? (
                    <Tag color="success" icon={<CheckCircleOutlined />}>
                      已验证
                    </Tag>
                  ) : (
                    <Tag color="warning" icon={<ExclamationCircleOutlined />}>
                      未验证
                    </Tag>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="订阅类型">
                <Tag color={getSubscriptionColor(user.subscriptionType)}>
                  {getSubscriptionText(user.subscriptionType)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="注册时间">
                {new Date(user.createdAt).toLocaleDateString()}
              </Descriptions.Item>
              <Descriptions.Item label="最后登录">
                {user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '未知'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* API配额使用情况 */}
      <Card title="API配额使用情况">
        <Row gutter={16}>
          <Col xs={24} sm={8}>
            <div className="text-center">
              <Progress
                type="circle"
                percent={(user.apiQuotaUsedToday / user.apiQuotaDaily) * 100}
                format={() => `${user.apiQuotaUsedToday}/${user.apiQuotaDaily}`}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <div className="mt-2">
                <Text type="secondary">今日使用量</Text>
              </div>
            </div>
          </Col>
          
          <Col xs={24} sm={16}>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <Text>使用率</Text>
                  <Text strong>
                    {((user.apiQuotaUsedToday / user.apiQuotaDaily) * 100).toFixed(1)}%
                  </Text>
                </div>
                <Progress
                  percent={(user.apiQuotaUsedToday / user.apiQuotaDaily) * 100}
                  status={user.apiQuotaUsedToday / user.apiQuotaDaily > 0.9 ? 'exception' : 'active'}
                />
              </div>
              
              <Alert
                message="配额说明"
                description={`您的${getSubscriptionText(user.subscriptionType)}每日可使用${user.apiQuotaDaily}次API调用，配额将在每日0点重置。`}
                type="info"
                showIcon
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 安全设置 */}
      <Card title="安全设置">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Text strong>登录密码</Text>
              <div>
                <Text type="secondary" className="text-sm">
                  定期更换密码可以提高账户安全性
                </Text>
              </div>
            </div>
            <Button
              icon={<LockOutlined />}
              onClick={() => setShowPasswordModal(true)}
            >
              修改密码
            </Button>
          </div>
          
          <Divider />
          
          <div className="flex items-center justify-between">
            <div>
              <Text strong>邮箱验证</Text>
              <div>
                <Text type="secondary" className="text-sm">
                  验证邮箱可以提高账户安全性并接收重要通知
                </Text>
              </div>
            </div>
            {user.isVerified ? (
              <Tag color="success" icon={<CheckCircleOutlined />}>
                已验证
              </Tag>
            ) : (
              <Button type="primary">
                发送验证邮件
              </Button>
            )}
          </div>
        </div>
      </Card>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={showPasswordModal}
        onCancel={() => {
          setShowPasswordModal(false);
          passwordForm.resetFields();
        }}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordChange}
        >
          <Form.Item
            name="currentPassword"
            label="当前密码"
            rules={[
              { required: true, message: '请输入当前密码' },
            ]}
          >
            <Input.Password
              placeholder="请输入当前密码"
              iconRender={(visible) => 
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码长度至少8位' },
              {
                pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                message: '密码必须包含大写字母、小写字母和数字',
              },
            ]}
          >
            <Input.Password
              placeholder="请输入新密码"
              iconRender={(visible) => 
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="确认新密码"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              placeholder="请再次输入新密码"
              iconRender={(visible) => 
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={passwordLoading}
              >
                确认修改
              </Button>
              <Button
                onClick={() => {
                  setShowPasswordModal(false);
                  passwordForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
