'use client';

/**
 * 全局错误页面
 * 
 * 捕获应用中的未处理错误
 */

import React, { useEffect } from 'react';
import { Button, Result, Typography } from 'antd';

const { Paragraph, Text } = Typography;

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 记录错误到监控服务
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md">
        <Result
          status="500"
          title="500"
          subTitle="抱歉，服务器出现了一些问题。"
          extra={
            <div className="space-y-4">
              <div className="space-x-4">
                <Button type="primary" onClick={reset}>
                  重试
                </Button>
                <Button onClick={() => window.location.href = '/dashboard'}>
                  返回首页
                </Button>
              </div>
              
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg text-left">
                  <Text strong className="text-red-600 block mb-2">
                    开发模式错误信息:
                  </Text>
                  <Paragraph className="text-red-600 text-sm font-mono mb-0">
                    {error.message}
                  </Paragraph>
                  {error.digest && (
                    <Paragraph className="text-red-500 text-xs mt-2 mb-0">
                      错误ID: {error.digest}
                    </Paragraph>
                  )}
                </div>
              )}
            </div>
          }
        />
      </div>
    </div>
  );
}
