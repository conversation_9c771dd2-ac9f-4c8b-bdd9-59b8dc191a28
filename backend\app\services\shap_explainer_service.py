"""
SHAP解释服务

提供基于SHAP的模型解释功能，包括全局和局部解释
"""

import numpy as np
import pandas as pd
import pickle
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
import warnings
warnings.filterwarnings('ignore')

# 尝试导入SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False

from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.tree import DecisionTreeClassifier, DecisionTreeRegressor

from app.core.logging import logger
from app.models.explainability import SHAPExplanation


class SHAPExplainerFactory:
    """SHAP解释器工厂"""
    
    def __init__(self):
        self.explainer_types = {
            'tree': self._create_tree_explainer,
            'linear': self._create_linear_explainer,
            'kernel': self._create_kernel_explainer,
            'deep': self._create_deep_explainer,
            'gradient': self._create_gradient_explainer,
            'partition': self._create_partition_explainer
        }
    
    def create_explainer(
        self,
        model,
        X_background: np.ndarray,
        explainer_type: str = 'auto',
        **kwargs
    ):
        """创建SHAP解释器"""
        try:
            if not SHAP_AVAILABLE:
                raise ImportError("SHAP library is not available")
            
            # 自动选择解释器类型
            if explainer_type == 'auto':
                explainer_type = self._auto_select_explainer_type(model)
            
            if explainer_type not in self.explainer_types:
                raise ValueError(f"Unsupported explainer type: {explainer_type}")
            
            return self.explainer_types[explainer_type](model, X_background, **kwargs)
            
        except Exception as e:
            logger.error(f"创建SHAP解释器失败: {e}")
            raise
    
    def _auto_select_explainer_type(self, model) -> str:
        """自动选择解释器类型"""
        try:
            model_type = type(model).__name__
            
            # 树模型
            if any(tree_type in model_type for tree_type in [
                'RandomForest', 'GradientBoosting', 'XGB', 'LightGBM', 
                'CatBoost', 'DecisionTree', 'ExtraTrees'
            ]):
                return 'tree'
            
            # 线性模型
            elif any(linear_type in model_type for linear_type in [
                'LinearRegression', 'LogisticRegression', 'Ridge', 'Lasso', 
                'ElasticNet', 'SGD'
            ]):
                return 'linear'
            
            # 深度学习模型
            elif any(deep_type in model_type for deep_type in [
                'Sequential', 'Model', 'MLPClassifier', 'MLPRegressor'
            ]):
                return 'deep'
            
            # 默认使用kernel解释器
            else:
                return 'kernel'
                
        except Exception as e:
            logger.error(f"自动选择解释器类型失败: {e}")
            return 'kernel'
    
    def _create_tree_explainer(self, model, X_background, **kwargs):
        """创建树解释器"""
        return shap.TreeExplainer(model, **kwargs)
    
    def _create_linear_explainer(self, model, X_background, **kwargs):
        """创建线性解释器"""
        return shap.LinearExplainer(model, X_background, **kwargs)
    
    def _create_kernel_explainer(self, model, X_background, **kwargs):
        """创建核解释器"""
        return shap.KernelExplainer(model.predict, X_background, **kwargs)
    
    def _create_deep_explainer(self, model, X_background, **kwargs):
        """创建深度解释器"""
        return shap.DeepExplainer(model, X_background, **kwargs)
    
    def _create_gradient_explainer(self, model, X_background, **kwargs):
        """创建梯度解释器"""
        return shap.GradientExplainer(model, X_background, **kwargs)
    
    def _create_partition_explainer(self, model, X_background, **kwargs):
        """创建分区解释器"""
        return shap.PartitionExplainer(model.predict, X_background, **kwargs)


class SHAPAnalyzer:
    """SHAP分析器"""
    
    def __init__(self):
        self.explainer_factory = SHAPExplainerFactory()
    
    def analyze_global_importance(
        self,
        shap_values: np.ndarray,
        feature_names: List[str],
        class_names: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """分析全局特征重要性"""
        try:
            # 处理多类分类的SHAP值
            if len(shap_values.shape) == 3:
                # 多类分类：取绝对值的平均
                global_importance = np.mean(np.abs(shap_values), axis=(0, 2))
            elif len(shap_values.shape) == 2:
                # 二分类或回归：取绝对值的平均
                global_importance = np.mean(np.abs(shap_values), axis=0)
            else:
                raise ValueError("Unexpected SHAP values shape")
            
            # 创建特征重要性字典
            feature_importance = dict(zip(feature_names, global_importance))
            
            # 排序特征
            sorted_features = sorted(
                feature_importance.items(),
                key=lambda x: x[1],
                reverse=True
            )
            
            # 计算累积重要性
            total_importance = sum(global_importance)
            cumulative_importance = []
            cumsum = 0
            for _, importance in sorted_features:
                cumsum += importance
                cumulative_importance.append(cumsum / total_importance)
            
            return {
                'feature_importance': feature_importance,
                'sorted_features': sorted_features,
                'cumulative_importance': cumulative_importance,
                'total_importance': float(total_importance),
                'top_features': sorted_features[:10]  # 前10个重要特征
            }
            
        except Exception as e:
            logger.error(f"全局重要性分析失败: {e}")
            return {}
    
    def analyze_feature_interactions(
        self,
        shap_values: np.ndarray,
        feature_names: List[str],
        max_interactions: int = 20
    ) -> Dict[str, Any]:
        """分析特征交互"""
        try:
            if not SHAP_AVAILABLE:
                return {}
            
            # 计算特征交互强度
            interactions = {}
            n_features = len(feature_names)
            
            # 简化的交互分析：计算特征对之间的协方差
            for i in range(n_features):
                for j in range(i + 1, n_features):
                    if len(shap_values.shape) == 2:
                        # 计算两个特征SHAP值的协方差
                        interaction_strength = np.abs(np.cov(
                            shap_values[:, i], 
                            shap_values[:, j]
                        )[0, 1])
                    else:
                        # 多类分类情况
                        interaction_strength = np.mean([
                            np.abs(np.cov(shap_values[:, i, k], shap_values[:, j, k])[0, 1])
                            for k in range(shap_values.shape[2])
                        ])
                    
                    interactions[(feature_names[i], feature_names[j])] = float(interaction_strength)
            
            # 排序交互
            sorted_interactions = sorted(
                interactions.items(),
                key=lambda x: x[1],
                reverse=True
            )[:max_interactions]
            
            return {
                'interactions': interactions,
                'sorted_interactions': sorted_interactions,
                'top_interactions': sorted_interactions[:5]
            }
            
        except Exception as e:
            logger.error(f"特征交互分析失败: {e}")
            return {}
    
    def generate_summary_statistics(
        self,
        shap_values: np.ndarray,
        feature_names: List[str],
        X: np.ndarray
    ) -> Dict[str, Any]:
        """生成汇总统计"""
        try:
            stats = {}
            
            # 处理不同形状的SHAP值
            if len(shap_values.shape) == 3:
                # 多类分类
                for class_idx in range(shap_values.shape[2]):
                    class_shap = shap_values[:, :, class_idx]
                    stats[f'class_{class_idx}'] = self._calculate_feature_stats(
                        class_shap, feature_names, X
                    )
            else:
                # 二分类或回归
                stats['overall'] = self._calculate_feature_stats(shap_values, feature_names, X)
            
            return stats
            
        except Exception as e:
            logger.error(f"汇总统计生成失败: {e}")
            return {}
    
    def _calculate_feature_stats(
        self,
        shap_values: np.ndarray,
        feature_names: List[str],
        X: np.ndarray
    ) -> Dict[str, Any]:
        """计算特征统计"""
        try:
            stats = {}
            
            for i, feature_name in enumerate(feature_names):
                feature_shap = shap_values[:, i]
                feature_values = X[:, i]
                
                stats[feature_name] = {
                    'mean_shap': float(np.mean(feature_shap)),
                    'std_shap': float(np.std(feature_shap)),
                    'min_shap': float(np.min(feature_shap)),
                    'max_shap': float(np.max(feature_shap)),
                    'mean_abs_shap': float(np.mean(np.abs(feature_shap))),
                    'correlation_with_feature': float(np.corrcoef(feature_shap, feature_values)[0, 1])
                    if not np.isnan(np.corrcoef(feature_shap, feature_values)[0, 1]) else 0.0
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"特征统计计算失败: {e}")
            return {}


class SHAPVisualizationDataGenerator:
    """SHAP可视化数据生成器"""
    
    def __init__(self):
        pass
    
    def generate_summary_plot_data(
        self,
        shap_values: np.ndarray,
        X: np.ndarray,
        feature_names: List[str],
        max_display: int = 20
    ) -> Dict[str, Any]:
        """生成汇总图数据"""
        try:
            # 处理多类分类
            if len(shap_values.shape) == 3:
                # 取第一个类别或平均
                plot_shap_values = shap_values[:, :, 0]
            else:
                plot_shap_values = shap_values
            
            # 计算特征重要性
            feature_importance = np.mean(np.abs(plot_shap_values), axis=0)
            
            # 选择最重要的特征
            top_indices = np.argsort(feature_importance)[::-1][:max_display]
            
            plot_data = {
                'feature_names': [feature_names[i] for i in top_indices],
                'shap_values': plot_shap_values[:, top_indices].tolist(),
                'feature_values': X[:, top_indices].tolist(),
                'feature_importance': feature_importance[top_indices].tolist()
            }
            
            return plot_data
            
        except Exception as e:
            logger.error(f"汇总图数据生成失败: {e}")
            return {}
    
    def generate_waterfall_plot_data(
        self,
        shap_values: np.ndarray,
        X: np.ndarray,
        feature_names: List[str],
        instance_idx: int,
        base_value: float,
        max_display: int = 10
    ) -> Dict[str, Any]:
        """生成瀑布图数据"""
        try:
            # 获取单个实例的SHAP值
            if len(shap_values.shape) == 3:
                instance_shap = shap_values[instance_idx, :, 0]
            else:
                instance_shap = shap_values[instance_idx, :]
            
            instance_values = X[instance_idx, :]
            
            # 按绝对值排序
            abs_shap = np.abs(instance_shap)
            top_indices = np.argsort(abs_shap)[::-1][:max_display]
            
            plot_data = {
                'feature_names': [feature_names[i] for i in top_indices],
                'shap_values': instance_shap[top_indices].tolist(),
                'feature_values': instance_values[top_indices].tolist(),
                'base_value': float(base_value),
                'prediction': float(base_value + np.sum(instance_shap))
            }
            
            return plot_data
            
        except Exception as e:
            logger.error(f"瀑布图数据生成失败: {e}")
            return {}
    
    def generate_dependence_plot_data(
        self,
        shap_values: np.ndarray,
        X: np.ndarray,
        feature_names: List[str],
        feature_idx: int,
        interaction_idx: Optional[int] = None
    ) -> Dict[str, Any]:
        """生成依赖图数据"""
        try:
            # 处理多类分类
            if len(shap_values.shape) == 3:
                plot_shap_values = shap_values[:, feature_idx, 0]
            else:
                plot_shap_values = shap_values[:, feature_idx]
            
            feature_values = X[:, feature_idx]
            
            plot_data = {
                'feature_name': feature_names[feature_idx],
                'feature_values': feature_values.tolist(),
                'shap_values': plot_shap_values.tolist()
            }
            
            # 添加交互特征
            if interaction_idx is not None and interaction_idx < len(feature_names):
                plot_data['interaction_feature'] = feature_names[interaction_idx]
                plot_data['interaction_values'] = X[:, interaction_idx].tolist()
            
            return plot_data
            
        except Exception as e:
            logger.error(f"依赖图数据生成失败: {e}")
            return {}


class SHAPExplainerService:
    """SHAP解释服务"""
    
    def __init__(self):
        self.explainer_factory = SHAPExplainerFactory()
        self.analyzer = SHAPAnalyzer()
        self.viz_generator = SHAPVisualizationDataGenerator()
    
    async def explain_model(
        self,
        analysis_id: int,
        model,
        X: np.ndarray,
        feature_names: List[str],
        explainer_config: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """解释模型"""
        try:
            start_time = datetime.utcnow()
            
            # 创建背景数据集
            background_size = min(100, len(X))
            X_background = X[:background_size]
            
            # 创建SHAP解释器
            explainer_type = explainer_config.get('explainer_type', 'auto')
            explainer = self.explainer_factory.create_explainer(
                model, X_background, explainer_type, **explainer_config.get('explainer_params', {})
            )
            
            # 计算SHAP值
            explanation_size = min(explainer_config.get('max_samples', 1000), len(X))
            X_explain = X[:explanation_size]
            
            shap_values = explainer.shap_values(X_explain)
            
            # 获取基准值
            if hasattr(explainer, 'expected_value'):
                expected_value = explainer.expected_value
                if isinstance(expected_value, np.ndarray):
                    expected_value = expected_value.tolist()
                else:
                    expected_value = float(expected_value)
            else:
                expected_value = 0.0
            
            # 转换SHAP值格式
            if isinstance(shap_values, list):
                # 多类分类情况
                shap_values_array = np.array(shap_values).transpose(1, 2, 0)
            else:
                shap_values_array = shap_values
            
            # 全局分析
            global_analysis = self.analyzer.analyze_global_importance(
                shap_values_array, feature_names
            )
            
            # 特征交互分析
            interaction_analysis = self.analyzer.analyze_feature_interactions(
                shap_values_array, feature_names
            )
            
            # 汇总统计
            summary_stats = self.analyzer.generate_summary_statistics(
                shap_values_array, feature_names, X_explain
            )
            
            # 生成可视化数据
            viz_data = self._generate_visualization_data(
                shap_values_array, X_explain, feature_names, expected_value
            )
            
            # 计算执行时间
            computation_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 保存SHAP解释结果
            shap_explanation = SHAPExplanation(
                analysis_id=analysis_id,
                explainer_type=explainer_type,
                explainer_config=explainer_config,
                explanation_scope="both",
                instance_count=explanation_size,
                shap_values=shap_values_array.tolist(),
                base_values=[expected_value] if not isinstance(expected_value, list) else expected_value,
                expected_values=[expected_value] if not isinstance(expected_value, list) else expected_value,
                global_feature_importance=global_analysis.get('feature_importance', {}),
                feature_interactions=interaction_analysis.get('interactions', {}),
                summary_statistics=summary_stats,
                summary_plot_data=viz_data.get('summary_plot', {}),
                waterfall_plot_data=viz_data.get('waterfall_plots', []),
                dependence_plot_data=viz_data.get('dependence_plots', []),
                computation_time_seconds=computation_time,
                status="completed"
            )
            
            db.add(shap_explanation)
            await db.commit()
            
            return {
                'success': True,
                'explanation_id': shap_explanation.id,
                'global_analysis': global_analysis,
                'interaction_analysis': interaction_analysis,
                'summary_statistics': summary_stats,
                'visualization_data': viz_data,
                'computation_time': computation_time
            }
            
        except Exception as e:
            logger.error(f"SHAP模型解释失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_visualization_data(
        self,
        shap_values: np.ndarray,
        X: np.ndarray,
        feature_names: List[str],
        expected_value: Union[float, List[float]]
    ) -> Dict[str, Any]:
        """生成可视化数据"""
        try:
            viz_data = {}
            
            # 汇总图数据
            viz_data['summary_plot'] = self.viz_generator.generate_summary_plot_data(
                shap_values, X, feature_names
            )
            
            # 瀑布图数据（前几个实例）
            waterfall_plots = []
            base_value = expected_value[0] if isinstance(expected_value, list) else expected_value
            
            for i in range(min(5, len(X))):
                waterfall_data = self.viz_generator.generate_waterfall_plot_data(
                    shap_values, X, feature_names, i, base_value
                )
                if waterfall_data:
                    waterfall_plots.append({
                        'instance_idx': i,
                        'data': waterfall_data
                    })
            
            viz_data['waterfall_plots'] = waterfall_plots
            
            # 依赖图数据（前几个重要特征）
            dependence_plots = []
            if 'feature_importance' in viz_data.get('summary_plot', {}):
                top_features = sorted(
                    enumerate(viz_data['summary_plot']['feature_importance']),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
                
                for feature_idx, _ in top_features:
                    dependence_data = self.viz_generator.generate_dependence_plot_data(
                        shap_values, X, feature_names, feature_idx
                    )
                    if dependence_data:
                        dependence_plots.append({
                            'feature_idx': feature_idx,
                            'data': dependence_data
                        })
            
            viz_data['dependence_plots'] = dependence_plots
            
            return viz_data
            
        except Exception as e:
            logger.error(f"可视化数据生成失败: {e}")
            return {}


# 全局SHAP解释服务实例
shap_explainer_service = SHAPExplainerService()
