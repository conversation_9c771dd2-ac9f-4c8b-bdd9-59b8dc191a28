'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, Row, Col, Statistic, Typography, Button, Space, Divider } from 'antd';
import {
  DashboardOutlined,
  RiseOutlined,
  WalletOutlined,
  BarChartOutlined,
  RightOutlined,
  StockOutlined,
  FundOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function DashboardPageContent() {
  const router = useRouter();
  const { user } = useAuthStore();

  const quickActions = [
    {
      title: '策略编辑器',
      description: '创建和编辑量化交易策略',
      icon: <BarChartOutlined className="text-2xl text-blue-500" />,
      path: '/dashboard/strategy/editor',
    },
    {
      title: '市场数据',
      description: '查看实时市场行情和数据',
      icon: <LineChartOutlined className="text-2xl text-green-500" />,
      path: '/dashboard/market',
    },
    {
      title: '投资组合',
      description: '管理和分析投资组合',
      icon: <WalletOutlined className="text-2xl text-purple-500" />,
      path: '/dashboard/portfolio',
    },
    {
      title: '系统设置',
      description: '配置JQData和个人偏好',
      icon: <StockOutlined className="text-2xl text-orange-500" />,
      path: '/dashboard/settings',
    },
  ];

  const stats = [
    {
      title: '总资产',
      value: '¥0.00',
      precision: 2,
      valueStyle: { color: '#3f8600' },
      prefix: <FundOutlined />,
    },
    {
      title: '今日收益',
      value: '¥0.00',
      precision: 2,
      valueStyle: { color: '#cf1322' },
      prefix: <RiseOutlined />,
    },
    {
      title: '活跃策略',
      value: 0,
      prefix: <BarChartOutlined />,
    },
    {
      title: '持仓股票',
      value: 0,
      prefix: <StockOutlined />,
    },
  ];

  return (
    <div className="p-6">
      {/* 欢迎区域 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <DashboardOutlined className="mr-3" />
          欢迎回来，{user?.username || '用户'}！
        </Title>
        <Text type="secondary" className="text-lg">
          今天是个适合量化交易的好日子 📈
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-8">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                valueStyle={stat.valueStyle}
                prefix={stat.prefix}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Divider />

      {/* 快速操作 */}
      <div className="mb-8">
        <Title level={3} className="!mb-6">
          快速操作
        </Title>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card 
                hoverable
                className="h-full cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() => router.push(action.path)}
              >
                <div className="text-center">
                  <div className="mb-4">
                    {action.icon}
                  </div>
                  <Title level={4} className="!mb-2">
                    {action.title}
                  </Title>
                  <Text type="secondary" className="text-sm">
                    {action.description}
                  </Text>
                  <div className="mt-4">
                    <Button type="link" className="p-0">
                      开始使用 <RightOutlined />
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* 最近活动 */}
      <Card title="最近活动" className="mb-6">
        <div className="text-center py-8">
          <Text type="secondary">
            暂无最近活动，开始您的量化交易之旅吧！
          </Text>
        </div>
      </Card>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ClientAuthWrapper>
      <DashboardPageContent />
    </ClientAuthWrapper>
  );
}
