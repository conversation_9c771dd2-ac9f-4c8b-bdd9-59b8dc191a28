'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Button, 
  Space, 
  Progress,
  Tag,
  List,
  Badge,
  Tooltip,
  Spin,
  Avatar,
  Divider
} from 'antd';
import {
  RiseOutlined,
  FallOutlined,
  WalletOutlined,
  Bar<PERSON><PERSON>Outlined,
  Line<PERSON><PERSON>Outlined,
  TrendingUpOutlined,
  BellOutlined,
  EyeOutlined,
  FireOutlined,
  <PERSON>boltOutlined,
  CrownOutlined,
  RightOutlined,
  StockOutlined
} from '@ant-design/icons';
import { useAuthStore } from '@/store/auth';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text, Paragraph } = Typography;

function DashboardPageContent() {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);

  // 模拟数据
  const [portfolioData] = useState({
    totalValue: 1250000,
    todayPnL: 15600,
    todayPnLPercent: 1.26,
    totalPnL: 250000,
    totalPnLPercent: 25.0,
    positions: 12,
    strategies: 5
  });

  const [marketIndices] = useState([
    { name: '上证指数', code: '000001', value: 3245.67, change: 12.34, changePercent: 0.38 },
    { name: '深证成指', code: '399001', value: 10876.54, change: -23.45, changePercent: -0.21 },
    { name: '创业板指', code: '399006', value: 2234.56, change: 45.67, changePercent: 2.08 },
    { name: '科创50', code: '000688', value: 1123.45, change: 8.90, changePercent: 0.80 },
  ]);

  const [hotStocks] = useState([
    { code: '000001.XSHE', name: '平安银行', price: 12.45, change: 0.23, changePercent: 1.88, volume: '2.3亿' },
    { code: '000002.XSHE', name: '万科A', price: 8.76, change: -0.12, changePercent: -1.35, volume: '1.8亿' },
    { code: '600036.XSHG', name: '招商银行', price: 35.67, change: 0.89, changePercent: 2.56, volume: '3.1亿' },
    { code: '600519.XSHG', name: '贵州茅台', price: 1678.90, change: -15.60, changePercent: -0.92, volume: '0.8亿' },
    { code: '000858.XSHE', name: '五粮液', price: 145.67, change: 3.45, changePercent: 2.42, volume: '1.5亿' },
  ]);

  const [recentNews] = useState([
    { title: 'CCTV新闻联播：经济稳中向好态势持续', time: '2小时前', sentiment: 'positive' },
    { title: '央行：继续实施稳健的货币政策', time: '4小时前', sentiment: 'neutral' },
    { title: '科技股集体上涨，AI概念持续活跃', time: '6小时前', sentiment: 'positive' },
    { title: '房地产政策调整，市场预期改善', time: '8小时前', sentiment: 'positive' },
  ]);

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000);
  }, []);

  const handleQuickAction = (path: string) => {
    router.push(path);
  };

  const quickActions = [
    {
      title: '策略开发',
      description: '创建和编辑量化交易策略',
      icon: <BarChartOutlined className="text-2xl text-blue-500" />,
      path: '/dashboard/strategy/editor',
      color: '#1890ff',
      badge: 'HOT',
      badgeColor: '#ff4d4f'
    },
    {
      title: '市场行情',
      description: '实时市场数据和K线分析',
      icon: <LineChartOutlined className="text-2xl text-green-500" />,
      path: '/dashboard/market',
      color: '#52c41a',
      badge: 'LIVE',
      badgeColor: '#52c41a'
    },
    {
      title: 'JQData配置',
      description: '配置JQData账号和API权限',
      icon: <StockOutlined className="text-2xl text-indigo-500" />,
      path: '/dashboard/settings/jqdata',
      color: '#6366f1',
      badge: 'SETUP',
      badgeColor: '#f59e0b'
    },
    {
      title: '新闻分析',
      description: 'CCTV新闻联播情绪分析',
      icon: <TrendingUpOutlined className="text-2xl text-orange-500" />,
      path: '/dashboard/market/news',
      color: '#fa8c16',
      badge: 'AI',
      badgeColor: '#722ed1'
    },
    {
      title: '投资组合',
      description: '管理和分析投资组合表现',
      icon: <WalletOutlined className="text-2xl text-purple-500" />,
      path: '/dashboard/portfolio',
      color: '#722ed1'
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* 欢迎区域 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Title level={2} className="!mb-2">
              欢迎回来，{user?.full_name || user?.username || '用户'}
              <Badge count={3} offset={[10, 0]}>
                <BellOutlined className="ml-3 text-gray-400" />
              </Badge>
            </Title>
            <Text type="secondary" className="text-base">
              今日市场活跃，把握投资机会 📈
            </Text>
          </div>
          <div className="text-right">
            <Text type="secondary">最后登录</Text>
            <br />
            <Text strong>{new Date().toLocaleString()}</Text>
          </div>
        </div>
      </div>

      {/* 核心数据卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <Statistic
              title={<span className="text-gray-600">总资产</span>}
              value={portfolioData.totalValue}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff', fontSize: '24px', fontWeight: 'bold' }}
            />
            <Progress 
              percent={75} 
              showInfo={false} 
              strokeColor="#1890ff" 
              className="mt-2"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <Statistic
              title={<span className="text-gray-600">今日盈亏</span>}
              value={portfolioData.todayPnL}
              precision={2}
              prefix="¥"
              suffix={
                <span className="text-sm">
                  ({portfolioData.todayPnLPercent > 0 ? '+' : ''}{portfolioData.todayPnLPercent}%)
                </span>
              }
              valueStyle={{ 
                color: portfolioData.todayPnL >= 0 ? '#3f8600' : '#cf1322',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
              prefix={portfolioData.todayPnL >= 0 ? <RiseOutlined /> : <FallOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <Statistic
              title={<span className="text-gray-600">持仓数量</span>}
              value={portfolioData.positions}
              suffix="只"
              valueStyle={{ color: '#722ed1', fontSize: '24px', fontWeight: 'bold' }}
            />
            <Text type="secondary" className="text-sm">活跃策略 {portfolioData.strategies} 个</Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="text-center hover:shadow-lg transition-shadow">
            <Statistic
              title={<span className="text-gray-600">总收益率</span>}
              value={portfolioData.totalPnLPercent}
              precision={1}
              suffix="%"
              valueStyle={{ 
                color: portfolioData.totalPnLPercent >= 0 ? '#3f8600' : '#cf1322',
                fontSize: '24px',
                fontWeight: 'bold'
              }}
              prefix={portfolioData.totalPnLPercent >= 0 ? <RiseOutlined /> : <FallOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Card title={
        <span>
          <ThunderboltOutlined className="mr-2" />
          快捷操作
        </span>
      } className="mb-6">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                className="text-center h-full relative overflow-hidden"
                onClick={() => handleQuickAction(action.path)}
                style={{ borderColor: action.color }}
              >
                {action.badge && (
                  <Badge.Ribbon 
                    text={action.badge} 
                    color={action.badgeColor || action.color}
                  >
                    <div className="pt-4">
                      <div className="mb-3">{action.icon}</div>
                      <Title level={5} className="!mb-2">{action.title}</Title>
                      <Text type="secondary" className="text-sm">
                        {action.description}
                      </Text>
                    </div>
                  </Badge.Ribbon>
                )}
                {!action.badge && (
                  <div className="pt-4">
                    <div className="mb-3">{action.icon}</div>
                    <Title level={5} className="!mb-2">{action.title}</Title>
                    <Text type="secondary" className="text-sm">
                      {action.description}
                    </Text>
                  </div>
                )}
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      <Row gutter={[16, 16]}>
        {/* 市场指数 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <StockOutlined className="mr-2" />
                市场指数
                <Tag color="green" className="ml-2">实时</Tag>
              </span>
            }
            extra={
              <Button 
                type="link" 
                onClick={() => handleQuickAction('/dashboard/market')}
                icon={<RightOutlined />}
              >
                查看更多
              </Button>
            }
          >
            <List
              dataSource={marketIndices}
              renderItem={(item) => (
                <List.Item className="!px-0">
                  <div className="flex justify-between items-center w-full">
                    <div>
                      <Text strong>{item.name}</Text>
                      <br />
                      <Text type="secondary" className="text-sm">{item.code}</Text>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{item.value.toFixed(2)}</div>
                      <div className={`text-sm ${item.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {item.change >= 0 ? '+' : ''}{item.change.toFixed(2)} ({item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%)
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 热门股票 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <FireOutlined className="mr-2" />
                热门股票
                <Tag color="red" className="ml-2">HOT</Tag>
              </span>
            }
            extra={
              <Button 
                type="link" 
                onClick={() => handleQuickAction('/dashboard/market/stocks')}
                icon={<RightOutlined />}
              >
                查看更多
              </Button>
            }
          >
            <List
              dataSource={hotStocks}
              renderItem={(item) => (
                <List.Item className="!px-0">
                  <div className="flex justify-between items-center w-full">
                    <div>
                      <Text strong>{item.name}</Text>
                      <br />
                      <Text type="secondary" className="text-sm">{item.code}</Text>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">¥{item.price.toFixed(2)}</div>
                      <div className={`text-sm ${item.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {item.change >= 0 ? '+' : ''}{item.change.toFixed(2)} ({item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%)
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 最新资讯 */}
      <Card 
        title={
          <span>
            <BellOutlined className="mr-2" />
            市场资讯
            <Tag color="blue" className="ml-2">AI分析</Tag>
          </span>
        }
        extra={
          <Button 
            type="link" 
            onClick={() => handleQuickAction('/dashboard/market/news')}
            icon={<RightOutlined />}
          >
            查看更多
          </Button>
        }
        className="mt-4"
      >
        <List
          dataSource={recentNews}
          renderItem={(item) => (
            <List.Item className="!px-0">
              <div className="flex justify-between items-center w-full">
                <div className="flex-1">
                  <Text strong className="text-base">{item.title}</Text>
                  <div className="mt-1">
                    <Text type="secondary" className="text-sm">{item.time}</Text>
                    <Tag 
                      color={
                        item.sentiment === 'positive' ? 'green' : 
                        item.sentiment === 'negative' ? 'red' : 'blue'
                      }
                      className="ml-2"
                    >
                      {item.sentiment === 'positive' ? '利好' : 
                       item.sentiment === 'negative' ? '利空' : '中性'}
                    </Tag>
                  </div>
                </div>
                <EyeOutlined className="text-gray-400 cursor-pointer hover:text-blue-500" />
              </div>
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
}

export default function DashboardPage() {
  return (
    <ClientAuthWrapper>
      <DashboardPageContent />
    </ClientAuthWrapper>
  );
}
