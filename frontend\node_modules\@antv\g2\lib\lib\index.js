"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.litelib = exports.stdlib = exports.plotlib = exports.graphlib = exports.geolib = exports.corelib = void 0;
var core_1 = require("./core");
Object.defineProperty(exports, "corelib", { enumerable: true, get: function () { return core_1.corelib; } });
var geo_1 = require("./geo");
Object.defineProperty(exports, "geolib", { enumerable: true, get: function () { return geo_1.geolib; } });
var graph_1 = require("./graph");
Object.defineProperty(exports, "graphlib", { enumerable: true, get: function () { return graph_1.graphlib; } });
var plot_1 = require("./plot");
Object.defineProperty(exports, "plotlib", { enumerable: true, get: function () { return plot_1.plotlib; } });
var std_1 = require("./std");
Object.defineProperty(exports, "stdlib", { enumerable: true, get: function () { return std_1.stdlib; } });
var lite_1 = require("./lite");
Object.defineProperty(exports, "litelib", { enumerable: true, get: function () { return lite_1.litelib; } });
//# sourceMappingURL=index.js.map