'use client';

import React, { useEffect, useState } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Statistic,
  Button,
  Space,
  Alert,
  Empty,
  Spin,
} from 'antd';
import {
  DashboardOutlined,
  RiseOutlined,
  DollarOutlined,
  ApiOutlined,
  BarChartOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { ClientAuthWrapper } from '@/components/auth/ClientAuthWrapper';

const { Title, Text } = Typography;

function OverviewPageContent() {
  const router = useRouter();
  const { user } = useAuthStore();

  const stats = [
    {
      title: 'API配额',
      value: user?.api_quota_used_today || 0,
      total: user?.api_quota_daily || 1000,
      suffix: `/ ${user?.api_quota_daily || 1000}`,
      icon: <ApiOutlined />,
      color: '#1890ff',
    },
    {
      title: '投资组合价值',
      value: 0,
      prefix: '¥',
      precision: 2,
      icon: <DollarOutlined />,
      color: '#52c41a',
    },
    {
      title: '总收益率',
      value: 0,
      suffix: '%',
      precision: 2,
      icon: <RiseOutlined />,
      color: '#faad14',
    },
    {
      title: '活跃策略',
      value: 0,
      icon: <BarChartOutlined />,
      color: '#722ed1',
    },
  ];

  const quickActions = [
    {
      title: '配置JQData',
      description: '设置API密钥以获取市场数据',
      action: () => router.push('/dashboard/settings/jqdata'),
      type: 'primary' as const,
    },
    {
      title: '创建策略',
      description: '开始您的第一个量化策略',
      action: () => router.push('/dashboard/strategy/editor'),
      type: 'default' as const,
    },
    {
      title: '查看市场',
      description: '浏览实时市场数据',
      action: () => router.push('/dashboard/market'),
      type: 'default' as const,
    },
  ];

  return (
    <div className="p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <Title level={2} className="!mb-2">
          <DashboardOutlined className="mr-3" />
          数据概览
        </Title>
        <Text type="secondary" className="text-lg">
          欢迎回来，{user?.username}！这里是您的量化交易数据概览
        </Text>
      </div>

      {/* JQData连接状态 */}
      <Alert
        message="JQData未配置"
        description="请先配置JQData账号以获取实时市场数据和使用量化功能"
        type="warning"
        action={
          <Button 
            size="small" 
            type="primary"
            onClick={() => router.push('/dashboard/settings/jqdata')}
          >
            立即配置
          </Button>
        }
        showIcon
        closable
        className="mb-6"
      />

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-8">
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                precision={stat.precision}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={{ color: stat.color }}
                prefix={stat.icon}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" className="mb-6">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={8} key={index}>
              <Card 
                size="small" 
                hoverable
                className="text-center cursor-pointer"
                onClick={action.action}
              >
                <Title level={4} className="!mb-2">
                  {action.title}
                </Title>
                <Text type="secondary" className="block mb-4">
                  {action.description}
                </Text>
                <Button type={action.type}>
                  开始使用
                </Button>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近活动 */}
      <Card title="最近活动" className="mb-6">
        <Empty 
          description="暂无最近活动"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary" 
            onClick={() => router.push('/dashboard/strategy/editor')}
          >
            创建第一个策略
          </Button>
        </Empty>
      </Card>

      {/* 系统状态 */}
      <Card title="系统状态">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <div className="text-center p-4">
              <div className="text-green-500 text-2xl mb-2">●</div>
              <div className="font-medium">后端服务</div>
              <div className="text-sm text-gray-500">正常运行</div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div className="text-center p-4">
              <div className="text-red-500 text-2xl mb-2">●</div>
              <div className="font-medium">JQData连接</div>
              <div className="text-sm text-gray-500">未配置</div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div className="text-center p-4">
              <div className="text-green-500 text-2xl mb-2">●</div>
              <div className="font-medium">数据库</div>
              <div className="text-sm text-gray-500">连接正常</div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
}

export default function OverviewPage() {
  return (
    <ClientAuthWrapper>
      <OverviewPageContent />
    </ClientAuthWrapper>
  );
}
