'use client';

/**
 * 仪表板概览页面
 * 
 * 显示用户的关键指标、市场概况、持仓概览等信息
 */

import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Button,
  Typography,
  Alert,
  Spin,
  Empty,
  Tooltip,
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  DollarOutlined,
  LineChartOutlined,
  DatabaseOutlined,
  ApiOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

import { useAuth } from '@/store/auth';
import { MarketOverviewChart } from '@/components/charts/MarketOverviewChart';
import { QuickStatsCard } from '@/components/dashboard/QuickStatsCard';
import { RecentActivities } from '@/components/dashboard/RecentActivities';

const { Title, Text } = Typography;

interface MarketData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePct: number;
  volume: number;
}

interface PortfolioData {
  totalValue: number;
  totalReturn: number;
  totalReturnPct: number;
  dayReturn: number;
  dayReturnPct: number;
  positions: number;
}

export default function DashboardOverview() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [jqdataConnected, setJqdataConnected] = useState(false);

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟市场数据
      setMarketData([
        {
          symbol: '000001.XSHE',
          name: '平安银行',
          price: 12.45,
          change: 0.23,
          changePct: 1.88,
          volume: 125000000,
        },
        {
          symbol: '000002.XSHE',
          name: '万科A',
          price: 18.76,
          change: -0.45,
          changePct: -2.34,
          volume: 89000000,
        },
        {
          symbol: '600036.XSHG',
          name: '招商银行',
          price: 42.18,
          change: 1.23,
          changePct: 3.01,
          volume: 156000000,
        },
        {
          symbol: '600519.XSHG',
          name: '贵州茅台',
          price: 1678.50,
          change: -12.30,
          changePct: -0.73,
          volume: 12000000,
        },
      ]);

      // 模拟投资组合数据
      setPortfolioData({
        totalValue: 125680.50,
        totalReturn: 15680.50,
        totalReturnPct: 14.23,
        dayReturn: 1234.56,
        dayReturnPct: 0.99,
        positions: 8,
      });

      setJqdataConnected(true);
      setLoading(false);
    };

    loadData();
  }, []);

  // 市场数据表格列定义
  const marketColumns = [
    {
      title: '股票',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: MarketData) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">{record.symbol}</div>
        </div>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'right' as const,
      render: (price: number) => (
        <Text className="font-mono">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: '涨跌',
      dataIndex: 'change',
      key: 'change',
      align: 'right' as const,
      render: (change: number, record: MarketData) => (
        <div className="text-right">
          <div className={`font-mono ${change >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {change >= 0 ? '+' : ''}{change.toFixed(2)}
          </div>
          <div className={`text-xs font-mono ${record.changePct >= 0 ? 'text-red-500' : 'text-green-500'}`}>
            {record.changePct >= 0 ? '+' : ''}{record.changePct.toFixed(2)}%
          </div>
        </div>
      ),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      align: 'right' as const,
      render: (volume: number) => (
        <Text className="font-mono text-xs">
          {(volume / 100000000).toFixed(2)}亿
        </Text>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title level={2} className="!mb-2">
            欢迎回来，{user?.fullName || user?.username}
          </Title>
          <Text type="secondary">
            今天是 {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            })}
          </Text>
        </div>
        <Button
          icon={<ReloadOutlined />}
          onClick={() => window.location.reload()}
        >
          刷新数据
        </Button>
      </div>

      {/* JQData连接状态 */}
      {!jqdataConnected && (
        <Alert
          message="JQData未配置"
          description="请先配置JQData账号以获取实时市场数据"
          type="warning"
          action={
            <Button size="small" type="primary">
              立即配置
            </Button>
          }
          showIcon
          closable
        />
      )}

      {/* 快速统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <QuickStatsCard
            title="API配额"
            value={user?.apiQuotaUsedToday || 0}
            total={user?.apiQuotaDaily || 1000}
            suffix={`/ ${user?.apiQuotaDaily || 1000}`}
            icon={<ApiOutlined />}
            color="#1890ff"
            showProgress
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <QuickStatsCard
            title="投资组合价值"
            value={portfolioData?.totalValue || 0}
            prefix="¥"
            precision={2}
            icon={<DollarOutlined />}
            color="#52c41a"
            trend={portfolioData?.dayReturnPct}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <QuickStatsCard
            title="总收益率"
            value={portfolioData?.totalReturnPct || 0}
            suffix="%"
            precision={2}
            icon={<TrophyOutlined />}
            color="#faad14"
            trend={portfolioData?.totalReturnPct}
          />
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <QuickStatsCard
            title="持仓数量"
            value={portfolioData?.positions || 0}
            suffix="只"
            icon={<DatabaseOutlined />}
            color="#722ed1"
          />
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 市场概览图表 */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <LineChartOutlined />
                市场概览
              </Space>
            }
            extra={
              <Space>
                <Tag color="green">沪深300</Tag>
                <Tooltip title="数据更新时间">
                  <ClockCircleOutlined className="text-gray-400" />
                </Tooltip>
              </Space>
            }
          >
            <MarketOverviewChart />
          </Card>
        </Col>

        {/* 热门股票 */}
        <Col xs={24} lg={8}>
          <Card
            title="热门股票"
            extra={
              <Button type="link" size="small">
                查看更多
              </Button>
            }
            bodyStyle={{ padding: 0 }}
          >
            <Table
              dataSource={marketData}
              columns={marketColumns}
              pagination={false}
              size="small"
              rowKey="symbol"
            />
          </Card>
        </Col>
      </Row>

      {/* 底部内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 投资组合概览 */}
        <Col xs={24} lg={12}>
          <Card
            title="投资组合概览"
            extra={
              <Button type="link" size="small">
                详细分析
              </Button>
            }
          >
            {portfolioData ? (
              <div className="space-y-4">
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="总资产"
                      value={portfolioData.totalValue}
                      precision={2}
                      prefix="¥"
                      valueStyle={{ fontSize: '20px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="今日收益"
                      value={portfolioData.dayReturn}
                      precision={2}
                      prefix="¥"
                      valueStyle={{
                        color: portfolioData.dayReturn >= 0 ? '#cf1322' : '#389e0d',
                        fontSize: '20px'
                      }}
                      suffix={
                        <span className="text-sm">
                          ({portfolioData.dayReturnPct >= 0 ? '+' : ''}{portfolioData.dayReturnPct.toFixed(2)}%)
                        </span>
                      }
                    />
                  </Col>
                </Row>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <Text>收益率</Text>
                    <Text strong>
                      {portfolioData.totalReturnPct >= 0 ? '+' : ''}{portfolioData.totalReturnPct.toFixed(2)}%
                    </Text>
                  </div>
                  <Progress
                    percent={Math.abs(portfolioData.totalReturnPct)}
                    status={portfolioData.totalReturnPct >= 0 ? 'success' : 'exception'}
                    showInfo={false}
                  />
                </div>
              </div>
            ) : (
              <Empty description="暂无投资组合数据" />
            )}
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <RecentActivities />
        </Col>
      </Row>
    </div>
  );
}
