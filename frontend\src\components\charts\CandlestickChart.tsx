'use client';

/**
 * K线图表组件
 * 
 * 使用ECharts显示股票K线图，支持技术指标、成交量等
 */

import React, { useEffect, useRef, useState } from 'react';
import { Spin, Select, Space, Button, Tooltip, Switch, Card } from 'antd';
import {
  ReloadOutlined,
  FullscreenOutlined,
  SettingOutlined,
  LineChartOutlined,
  BarChartOutlined,
  FunctionOutlined
} from '@ant-design/icons';
import * as echarts from 'echarts';

import {
  PriceData,
  calculateSMA,
  calculateEMA,
  calculateMACD,
  calculateBOLL
} from '@/utils/indicators';
import { TechnicalIndicators } from './TechnicalIndicators';

const { Option } = Select;

interface CandlestickData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface CandlestickChartProps {
  symbol?: string;
  height?: number;
  showControls?: boolean;
  data?: CandlestickData[];
  onSymbolChange?: (symbol: string) => void;
  showTechnicalIndicators?: boolean;
}

export const CandlestickChart: React.FC<CandlestickChartProps> = ({
  symbol = '000001.XSHE',
  height = 600,
  showControls = true,
  data,
  onSymbolChange,
  showTechnicalIndicators = false,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('daily');
  const [showVolume, setShowVolume] = useState(true);
  const [showMA, setShowMA] = useState(true);
  const [showBOLL, setShowBOLL] = useState(false);
  const [showIndicators, setShowIndicators] = useState(showTechnicalIndicators);
  const [selectedIndicators, setSelectedIndicators] = useState<string[]>(['macd']);
  const [showMA, setShowMA] = useState(true);

  // 生成模拟K线数据
  const generateMockData = (): CandlestickData[] => {
    const data: CandlestickData[] = [];
    let basePrice = 12.5;
    const now = new Date();
    
    for (let i = 60; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      
      // 模拟价格波动
      const change = (Math.random() - 0.5) * 0.8;
      const open = basePrice;
      const close = basePrice + change;
      const high = Math.max(open, close) + Math.random() * 0.3;
      const low = Math.min(open, close) - Math.random() * 0.3;
      const volume = Math.floor(Math.random() * 50000000) + 10000000;
      
      data.push({
        date: date.toISOString().split('T')[0],
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume,
      });
      
      basePrice = close;
    }
    
    return data;
  };

  // 计算移动平均线
  const calculateMA = (data: CandlestickData[], period: number) => {
    const ma: number[] = [];
    for (let i = 0; i < data.length; i++) {
      if (i < period - 1) {
        ma.push(NaN);
      } else {
        let sum = 0;
        for (let j = 0; j < period; j++) {
          sum += data[i - j].close;
        }
        ma.push(Number((sum / period).toFixed(2)));
      }
    }
    return ma;
  };

  // 初始化图表
  const initChart = () => {
    if (!chartRef.current) return;

    // 销毁现有图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // 创建新的图表实例
    chartInstance.current = echarts.init(chartRef.current);
    
    const chartData = data || generateMockData();
    
    // 准备数据
    const dates = chartData.map(item => item.date);
    const ohlcData = chartData.map(item => [item.open, item.close, item.low, item.high]);
    const volumes = chartData.map(item => item.volume);
    
    // 计算移动平均线
    const ma5 = calculateMA(chartData, 5);
    const ma10 = calculateMA(chartData, 10);
    const ma20 = calculateMA(chartData, 20);

    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      animation: true,
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20', '成交量'],
        top: 10,
        textStyle: {
          color: '#8392A5',
          fontSize: 12,
        },
      },
      grid: [
        {
          left: '3%',
          right: '4%',
          top: '15%',
          height: showVolume ? '60%' : '75%',
        },
        ...(showVolume ? [{
          left: '3%',
          right: '4%',
          top: '80%',
          height: '15%',
        }] : []),
      ],
      xAxis: [
        {
          type: 'category',
          data: dates,
          boundaryGap: false,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: string) => {
              const date = new Date(value);
              return `${date.getMonth() + 1}/${date.getDate()}`;
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
        },
        ...(showVolume ? [{
          type: 'category',
          gridIndex: 1,
          data: dates,
          boundaryGap: false,
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
        }] : []),
      ],
      yAxis: [
        {
          type: 'value',
          scale: true,
          position: 'right',
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => value.toFixed(2),
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E6E8EB',
              type: 'dashed',
            },
          },
        },
        ...(showVolume ? [{
          type: 'value',
          gridIndex: 1,
          position: 'right',
          axisLine: { lineStyle: { color: '#8392A5' } },
          axisLabel: { 
            color: '#8392A5',
            fontSize: 12,
            formatter: (value: number) => (value / 10000).toFixed(0) + 'w',
          },
          splitLine: {
            show: false,
          },
        }] : []),
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E6E8EB',
        borderWidth: 1,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const item = chartData[dataIndex];
          const change = item.close - item.open;
          const changePct = ((change / item.open) * 100).toFixed(2);
          
          return `
            <div style="padding: 8px;">
              <div style="margin-bottom: 8px; font-weight: bold;">${item.date}</div>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px;">
                <div>开盘: ${item.open.toFixed(2)}</div>
                <div>收盘: ${item.close.toFixed(2)}</div>
                <div>最高: ${item.high.toFixed(2)}</div>
                <div>最低: ${item.low.toFixed(2)}</div>
                <div>涨跌: <span style="color: ${change >= 0 ? '#ef4444' : '#22c55e'}">${change >= 0 ? '+' : ''}${change.toFixed(2)}</span></div>
                <div>涨幅: <span style="color: ${change >= 0 ? '#ef4444' : '#22c55e'}">${change >= 0 ? '+' : ''}${changePct}%</span></div>
                <div colspan="2">成交量: ${(item.volume / 10000).toFixed(0)}万</div>
              </div>
            </div>
          `;
        },
      },
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: ohlcData,
          itemStyle: {
            color: '#ef4444',
            color0: '#22c55e',
            borderColor: '#ef4444',
            borderColor0: '#22c55e',
          },
          emphasis: {
            itemStyle: {
              borderWidth: 2,
            },
          },
        },
        ...(showMA ? [
          {
            name: 'MA5',
            type: 'line',
            data: ma5,
            smooth: true,
            lineStyle: {
              color: '#1890ff',
              width: 1,
            },
            symbol: 'none',
          },
          {
            name: 'MA10',
            type: 'line',
            data: ma10,
            smooth: true,
            lineStyle: {
              color: '#faad14',
              width: 1,
            },
            symbol: 'none',
          },
          {
            name: 'MA20',
            type: 'line',
            data: ma20,
            smooth: true,
            lineStyle: {
              color: '#722ed1',
              width: 1,
            },
            symbol: 'none',
          },
        ] : []),
        ...(showBOLL ? (() => {
          const bollData = calculateBOLL(chartData.map(item => ({
            date: item.date,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close,
            volume: item.volume
          })));

          return [
            {
              name: 'BOLL上轨',
              type: 'line',
              data: bollData.map(d => d.upper),
              smooth: true,
              lineStyle: {
                color: '#ff4d4f',
                width: 1,
                type: 'dashed'
              },
              symbol: 'none',
            },
            {
              name: 'BOLL中轨',
              type: 'line',
              data: bollData.map(d => d.middle),
              smooth: true,
              lineStyle: {
                color: '#1890ff',
                width: 1,
              },
              symbol: 'none',
            },
            {
              name: 'BOLL下轨',
              type: 'line',
              data: bollData.map(d => d.lower),
              smooth: true,
              lineStyle: {
                color: '#52c41a',
                width: 1,
                type: 'dashed'
              },
              symbol: 'none',
            }
          ];
        })() : []),
        ...(showVolume ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: volumes.map((vol, index) => ({
            value: vol,
            itemStyle: {
              color: chartData[index].close >= chartData[index].open ? '#ef4444' : '#22c55e',
              opacity: 0.7,
            },
          })),
        }] : []),
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 70,
          end: 100,
        },
        {
          show: true,
          xAxisIndex: [0, 1],
          type: 'slider',
          top: '90%',
          start: 70,
          end: 100,
          height: 20,
        },
      ],
    };

    chartInstance.current.setOption(option);
    setLoading(false);
  };

  // 处理窗口大小变化
  const handleResize = () => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => {
      initChart();
    }, 500);
  };

  useEffect(() => {
    initChart();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [symbol, timeframe, showVolume, showMA, data]);

  return (
    <div className="relative">
      {/* 控制栏 */}
      {showControls && (
        <div className="flex items-center justify-between mb-4 flex-wrap gap-2">
          <Space wrap>
            <Select
              value={symbol}
              onChange={onSymbolChange}
              style={{ width: 150 }}
              size="small"
            >
              <Option value="000001.XSHE">平安银行</Option>
              <Option value="000002.XSHE">万科A</Option>
              <Option value="600036.XSHG">招商银行</Option>
              <Option value="600519.XSHG">贵州茅台</Option>
            </Select>
            
            <Select
              value={timeframe}
              onChange={setTimeframe}
              style={{ width: 80 }}
              size="small"
            >
              <Option value="1m">1分</Option>
              <Option value="5m">5分</Option>
              <Option value="15m">15分</Option>
              <Option value="30m">30分</Option>
              <Option value="1h">1时</Option>
              <Option value="daily">日线</Option>
            </Select>
          </Space>
          
          <Space wrap>
            <Tooltip title="显示移动平均线">
              <Switch
                checked={showMA}
                onChange={setShowMA}
                size="small"
                checkedChildren="MA"
                unCheckedChildren="MA"
              />
            </Tooltip>
            
            <Tooltip title="显示布林带">
              <Switch
                checked={showBOLL}
                onChange={setShowBOLL}
                size="small"
                checkedChildren="BOLL"
                unCheckedChildren="BOLL"
              />
            </Tooltip>

            <Tooltip title="显示成交量">
              <Switch
                checked={showVolume}
                onChange={setShowVolume}
                size="small"
                checkedChildren={<BarChartOutlined />}
                unCheckedChildren={<BarChartOutlined />}
              />
            </Tooltip>

            <Tooltip title="显示技术指标">
              <Switch
                checked={showIndicators}
                onChange={setShowIndicators}
                size="small"
                checkedChildren={<FunctionOutlined />}
                unCheckedChildren={<FunctionOutlined />}
              />
            </Tooltip>

            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
            
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
            >
              设置
            </Button>
          </Space>
        </div>
      )}

      {/* 图表容器 */}
      <div className="relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
            <Spin size="large" />
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>

      {/* 技术指标 */}
      {showIndicators && chartData.length > 0 && (
        <div className="mt-4">
          <TechnicalIndicators
            data={chartData.map(item => ({
              date: item.date,
              open: item.open,
              high: item.high,
              low: item.low,
              close: item.close,
              volume: item.volume
            }))}
            height={300}
            indicators={selectedIndicators}
            onIndicatorChange={setSelectedIndicators}
          />
        </div>
      )}
    </div>
  );
};
