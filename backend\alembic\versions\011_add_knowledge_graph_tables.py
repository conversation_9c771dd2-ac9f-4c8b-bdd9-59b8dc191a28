"""add knowledge graph tables

Revision ID: 011
Revises: 010
Create Date: 2024-12-23 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '011'
down_revision = '010'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建金融实体表
    op.create_table('financial_entities',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('entity_id', sa.String(length=50), nullable=False),
        sa.Column('entity_name', sa.String(length=200), nullable=False),
        sa.Column('entity_type', sa.String(length=50), nullable=False),
        sa.Column('entity_category', sa.String(length=50), nullable=True),
        sa.Column('properties', sa.JSO<PERSON>(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('stock_code', sa.String(length=20), nullable=True),
        sa.Column('stock_name', sa.String(length=100), nullable=True),
        sa.Column('exchange', sa.String(length=20), nullable=True),
        sa.Column('industry', sa.String(length=100), nullable=True),
        sa.Column('sector', sa.String(length=100), nullable=True),
        sa.Column('market_cap', sa.Float(), nullable=True),
        sa.Column('company_name', sa.String(length=200), nullable=True),
        sa.Column('company_type', sa.String(length=50), nullable=True),
        sa.Column('registration_country', sa.String(length=50), nullable=True),
        sa.Column('business_scope', sa.Text(), nullable=True),
        sa.Column('person_name', sa.String(length=100), nullable=True),
        sa.Column('position', sa.String(length=100), nullable=True),
        sa.Column('department', sa.String(length=100), nullable=True),
        sa.Column('event_type', sa.String(length=50), nullable=True),
        sa.Column('event_date', sa.DateTime(), nullable=True),
        sa.Column('event_description', sa.Text(), nullable=True),
        sa.Column('node_features', sa.JSON(), nullable=True),
        sa.Column('embedding_vector', sa.JSON(), nullable=True),
        sa.Column('centrality_scores', sa.JSON(), nullable=True),
        sa.Column('community_id', sa.Integer(), nullable=True),
        sa.Column('data_source', sa.String(length=100), nullable=True),
        sa.Column('source_confidence', sa.Float(), nullable=True),
        sa.Column('last_updated_from_source', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=True),
        sa.Column('verification_status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('entity_id')
    )
    op.create_index(op.f('ix_financial_entities_id'), 'financial_entities', ['id'], unique=False)
    op.create_index(op.f('ix_financial_entities_user_id'), 'financial_entities', ['user_id'], unique=False)
    op.create_index(op.f('ix_financial_entities_entity_id'), 'financial_entities', ['entity_id'], unique=False)
    op.create_index(op.f('ix_financial_entities_entity_name'), 'financial_entities', ['entity_name'], unique=False)
    op.create_index(op.f('ix_financial_entities_entity_type'), 'financial_entities', ['entity_type'], unique=False)
    op.create_index(op.f('ix_financial_entities_stock_code'), 'financial_entities', ['stock_code'], unique=False)

    # 创建实体关系表
    op.create_table('entity_relations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('relation_id', sa.String(length=100), nullable=False),
        sa.Column('source_entity_id', sa.Integer(), nullable=False),
        sa.Column('target_entity_id', sa.Integer(), nullable=False),
        sa.Column('relation_type', sa.String(length=50), nullable=False),
        sa.Column('relation_name', sa.String(length=100), nullable=False),
        sa.Column('properties', sa.JSON(), nullable=True),
        sa.Column('weight', sa.Float(), nullable=True),
        sa.Column('confidence', sa.Float(), nullable=True),
        sa.Column('strength', sa.Float(), nullable=True),
        sa.Column('is_directed', sa.Boolean(), nullable=True),
        sa.Column('is_symmetric', sa.Boolean(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=True),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('is_temporal', sa.Boolean(), nullable=True),
        sa.Column('evidence_sources', sa.JSON(), nullable=True),
        sa.Column('verification_score', sa.Float(), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=True),
        sa.Column('edge_features', sa.JSON(), nullable=True),
        sa.Column('betweenness_centrality', sa.Float(), nullable=True),
        sa.Column('closeness_centrality', sa.Float(), nullable=True),
        sa.Column('data_source', sa.String(length=100), nullable=True),
        sa.Column('extraction_method', sa.String(length=50), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_entity_id'], ['financial_entities.id'], ),
        sa.ForeignKeyConstraint(['target_entity_id'], ['financial_entities.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('relation_id')
    )
    op.create_index(op.f('ix_entity_relations_id'), 'entity_relations', ['id'], unique=False)
    op.create_index(op.f('ix_entity_relations_user_id'), 'entity_relations', ['user_id'], unique=False)
    op.create_index(op.f('ix_entity_relations_relation_id'), 'entity_relations', ['relation_id'], unique=False)
    op.create_index(op.f('ix_entity_relations_source_entity_id'), 'entity_relations', ['source_entity_id'], unique=False)
    op.create_index(op.f('ix_entity_relations_target_entity_id'), 'entity_relations', ['target_entity_id'], unique=False)
    op.create_index(op.f('ix_entity_relations_relation_type'), 'entity_relations', ['relation_type'], unique=False)

    # 创建知识图谱表
    op.create_table('knowledge_graphs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('graph_name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('graph_type', sa.String(length=50), nullable=False),
        sa.Column('domain', sa.String(length=50), nullable=True),
        sa.Column('entity_count', sa.Integer(), nullable=True),
        sa.Column('relation_count', sa.Integer(), nullable=True),
        sa.Column('node_count', sa.Integer(), nullable=True),
        sa.Column('edge_count', sa.Integer(), nullable=True),
        sa.Column('graph_schema', sa.JSON(), nullable=True),
        sa.Column('entity_types', sa.JSON(), nullable=True),
        sa.Column('relation_types', sa.JSON(), nullable=True),
        sa.Column('construction_config', sa.JSON(), nullable=True),
        sa.Column('update_strategy', sa.String(length=50), nullable=True),
        sa.Column('quality_threshold', sa.Float(), nullable=True),
        sa.Column('completeness_score', sa.Float(), nullable=True),
        sa.Column('consistency_score', sa.Float(), nullable=True),
        sa.Column('accuracy_score', sa.Float(), nullable=True),
        sa.Column('freshness_score', sa.Float(), nullable=True),
        sa.Column('graph_data', sa.JSON(), nullable=True),
        sa.Column('graph_file_path', sa.String(length=500), nullable=True),
        sa.Column('serialization_format', sa.String(length=20), nullable=True),
        sa.Column('version', sa.String(length=20), nullable=True),
        sa.Column('parent_graph_id', sa.Integer(), nullable=True),
        sa.Column('is_latest_version', sa.Boolean(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('build_progress', sa.Integer(), nullable=True),
        sa.Column('last_build_time', sa.DateTime(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=True),
        sa.Column('access_level', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['parent_graph_id'], ['knowledge_graphs.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_knowledge_graphs_id'), 'knowledge_graphs', ['id'], unique=False)
    op.create_index(op.f('ix_knowledge_graphs_user_id'), 'knowledge_graphs', ['user_id'], unique=False)

    # 创建图分析表
    op.create_table('graph_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('knowledge_graph_id', sa.Integer(), nullable=False),
        sa.Column('analysis_name', sa.String(length=100), nullable=False),
        sa.Column('analysis_type', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('analysis_config', sa.JSON(), nullable=True),
        sa.Column('algorithms_used', sa.JSON(), nullable=True),
        sa.Column('parameters', sa.JSON(), nullable=True),
        sa.Column('graph_metrics', sa.JSON(), nullable=True),
        sa.Column('centrality_measures', sa.JSON(), nullable=True),
        sa.Column('clustering_coefficients', sa.JSON(), nullable=True),
        sa.Column('path_lengths', sa.JSON(), nullable=True),
        sa.Column('community_detection_results', sa.JSON(), nullable=True),
        sa.Column('community_count', sa.Integer(), nullable=True),
        sa.Column('modularity_score', sa.Float(), nullable=True),
        sa.Column('node_importance_scores', sa.JSON(), nullable=True),
        sa.Column('influential_nodes', sa.JSON(), nullable=True),
        sa.Column('hub_nodes', sa.JSON(), nullable=True),
        sa.Column('relation_patterns', sa.JSON(), nullable=True),
        sa.Column('frequent_subgraphs', sa.JSON(), nullable=True),
        sa.Column('anomalous_patterns', sa.JSON(), nullable=True),
        sa.Column('temporal_evolution', sa.JSON(), nullable=True),
        sa.Column('trend_analysis', sa.JSON(), nullable=True),
        sa.Column('change_points', sa.JSON(), nullable=True),
        sa.Column('visualization_data', sa.JSON(), nullable=True),
        sa.Column('layout_coordinates', sa.JSON(), nullable=True),
        sa.Column('visual_properties', sa.JSON(), nullable=True),
        sa.Column('computation_time_seconds', sa.Float(), nullable=True),
        sa.Column('memory_usage_mb', sa.Float(), nullable=True),
        sa.Column('algorithm_complexity', sa.String(length=50), nullable=True),
        sa.Column('analysis_quality_score', sa.Float(), nullable=True),
        sa.Column('confidence_level', sa.Float(), nullable=True),
        sa.Column('statistical_significance', sa.JSON(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['knowledge_graph_id'], ['knowledge_graphs.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_graph_analyses_id'), 'graph_analyses', ['id'], unique=False)
    op.create_index(op.f('ix_graph_analyses_user_id'), 'graph_analyses', ['user_id'], unique=False)
    op.create_index(op.f('ix_graph_analyses_knowledge_graph_id'), 'graph_analyses', ['knowledge_graph_id'], unique=False)

    # 创建GNN模型表
    op.create_table('gnn_models',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('knowledge_graph_id', sa.Integer(), nullable=False),
        sa.Column('model_name', sa.String(length=100), nullable=False),
        sa.Column('model_type', sa.String(length=50), nullable=False),
        sa.Column('architecture', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('model_config', sa.JSON(), nullable=True),
        sa.Column('layer_config', sa.JSON(), nullable=True),
        sa.Column('hyperparameters', sa.JSON(), nullable=True),
        sa.Column('training_config', sa.JSON(), nullable=True),
        sa.Column('optimization_config', sa.JSON(), nullable=True),
        sa.Column('loss_function', sa.String(length=50), nullable=True),
        sa.Column('input_features', sa.JSON(), nullable=True),
        sa.Column('output_features', sa.JSON(), nullable=True),
        sa.Column('feature_dimensions', sa.JSON(), nullable=True),
        sa.Column('training_epochs', sa.Integer(), nullable=True),
        sa.Column('training_time_seconds', sa.Float(), nullable=True),
        sa.Column('convergence_epoch', sa.Integer(), nullable=True),
        sa.Column('training_metrics', sa.JSON(), nullable=True),
        sa.Column('validation_metrics', sa.JSON(), nullable=True),
        sa.Column('test_metrics', sa.JSON(), nullable=True),
        sa.Column('accuracy_score', sa.Float(), nullable=True),
        sa.Column('precision_score', sa.Float(), nullable=True),
        sa.Column('recall_score', sa.Float(), nullable=True),
        sa.Column('f1_score', sa.Float(), nullable=True),
        sa.Column('auc_score', sa.Float(), nullable=True),
        sa.Column('model_binary', sa.LargeBinary(), nullable=True),
        sa.Column('model_file_path', sa.String(length=500), nullable=True),
        sa.Column('model_size_mb', sa.Float(), nullable=True),
        sa.Column('is_deployed', sa.Boolean(), nullable=True),
        sa.Column('deployment_config', sa.JSON(), nullable=True),
        sa.Column('api_endpoint', sa.String(length=200), nullable=True),
        sa.Column('total_predictions', sa.Integer(), nullable=True),
        sa.Column('successful_predictions', sa.Integer(), nullable=True),
        sa.Column('average_prediction_time_ms', sa.Float(), nullable=True),
        sa.Column('version', sa.String(length=20), nullable=True),
        sa.Column('parent_model_id', sa.Integer(), nullable=True),
        sa.Column('is_latest_version', sa.Boolean(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('training_progress', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.Column('deployed_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['knowledge_graph_id'], ['knowledge_graphs.id'], ),
        sa.ForeignKeyConstraint(['parent_model_id'], ['gnn_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gnn_models_id'), 'gnn_models', ['id'], unique=False)
    op.create_index(op.f('ix_gnn_models_user_id'), 'gnn_models', ['user_id'], unique=False)
    op.create_index(op.f('ix_gnn_models_knowledge_graph_id'), 'gnn_models', ['knowledge_graph_id'], unique=False)

    # 创建GNN预测表
    op.create_table('gnn_predictions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('gnn_model_id', sa.Integer(), nullable=False),
        sa.Column('prediction_name', sa.String(length=100), nullable=True),
        sa.Column('prediction_type', sa.String(length=50), nullable=False),
        sa.Column('task_type', sa.String(length=50), nullable=False),
        sa.Column('input_entities', sa.JSON(), nullable=True),
        sa.Column('input_features', sa.JSON(), nullable=True),
        sa.Column('input_graph_structure', sa.JSON(), nullable=True),
        sa.Column('predictions', sa.JSON(), nullable=True),
        sa.Column('prediction_probabilities', sa.JSON(), nullable=True),
        sa.Column('confidence_scores', sa.JSON(), nullable=True),
        sa.Column('prediction_count', sa.Integer(), nullable=True),
        sa.Column('prediction_time_ms', sa.Float(), nullable=True),
        sa.Column('average_confidence', sa.Float(), nullable=True),
        sa.Column('actual_values', sa.JSON(), nullable=True),
        sa.Column('prediction_errors', sa.JSON(), nullable=True),
        sa.Column('accuracy_metrics', sa.JSON(), nullable=True),
        sa.Column('feature_importance', sa.JSON(), nullable=True),
        sa.Column('attention_weights', sa.JSON(), nullable=True),
        sa.Column('explanation_data', sa.JSON(), nullable=True),
        sa.Column('business_context', sa.JSON(), nullable=True),
        sa.Column('market_conditions', sa.JSON(), nullable=True),
        sa.Column('external_factors', sa.JSON(), nullable=True),
        sa.Column('quality_score', sa.Float(), nullable=True),
        sa.Column('reliability_score', sa.Float(), nullable=True),
        sa.Column('is_validated', sa.Boolean(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('prediction_date', sa.DateTime(), nullable=True),
        sa.Column('validation_date', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['gnn_model_id'], ['gnn_models.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_gnn_predictions_id'), 'gnn_predictions', ['id'], unique=False)
    op.create_index(op.f('ix_gnn_predictions_user_id'), 'gnn_predictions', ['user_id'], unique=False)
    op.create_index(op.f('ix_gnn_predictions_gnn_model_id'), 'gnn_predictions', ['gnn_model_id'], unique=False)

    # 创建图嵌入表
    op.create_table('graph_embeddings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('knowledge_graph_id', sa.Integer(), nullable=False),
        sa.Column('embedding_name', sa.String(length=100), nullable=False),
        sa.Column('embedding_method', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('embedding_config', sa.JSON(), nullable=True),
        sa.Column('algorithm_parameters', sa.JSON(), nullable=True),
        sa.Column('training_parameters', sa.JSON(), nullable=True),
        sa.Column('embedding_dimension', sa.Integer(), nullable=False),
        sa.Column('entity_count', sa.Integer(), nullable=True),
        sa.Column('relation_count', sa.Integer(), nullable=True),
        sa.Column('entity_embeddings', sa.JSON(), nullable=True),
        sa.Column('relation_embeddings', sa.JSON(), nullable=True),
        sa.Column('embedding_matrix', sa.LargeBinary(), nullable=True),
        sa.Column('embedding_quality_metrics', sa.JSON(), nullable=True),
        sa.Column('similarity_preservation', sa.Float(), nullable=True),
        sa.Column('structure_preservation', sa.Float(), nullable=True),
        sa.Column('training_epochs', sa.Integer(), nullable=True),
        sa.Column('training_time_seconds', sa.Float(), nullable=True),
        sa.Column('convergence_loss', sa.Float(), nullable=True),
        sa.Column('usage_count', sa.Integer(), nullable=True),
        sa.Column('last_used_at', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('trained_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['knowledge_graph_id'], ['knowledge_graphs.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_graph_embeddings_id'), 'graph_embeddings', ['id'], unique=False)
    op.create_index(op.f('ix_graph_embeddings_user_id'), 'graph_embeddings', ['user_id'], unique=False)
    op.create_index(op.f('ix_graph_embeddings_knowledge_graph_id'), 'graph_embeddings', ['knowledge_graph_id'], unique=False)

    # 创建实体相似性表
    op.create_table('entity_similarities',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('entity1_id', sa.Integer(), nullable=False),
        sa.Column('entity2_id', sa.Integer(), nullable=False),
        sa.Column('similarity_type', sa.String(length=50), nullable=False),
        sa.Column('similarity_score', sa.Float(), nullable=False),
        sa.Column('confidence_score', sa.Float(), nullable=True),
        sa.Column('calculation_method', sa.String(length=50), nullable=False),
        sa.Column('method_parameters', sa.JSON(), nullable=True),
        sa.Column('similarity_details', sa.JSON(), nullable=True),
        sa.Column('feature_contributions', sa.JSON(), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=True),
        sa.Column('verification_source', sa.String(length=100), nullable=True),
        sa.Column('calculated_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['entity1_id'], ['financial_entities.id'], ),
        sa.ForeignKeyConstraint(['entity2_id'], ['financial_entities.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_entity_similarities_id'), 'entity_similarities', ['id'], unique=False)
    op.create_index(op.f('ix_entity_similarities_user_id'), 'entity_similarities', ['user_id'], unique=False)
    op.create_index(op.f('ix_entity_similarities_entity1_id'), 'entity_similarities', ['entity1_id'], unique=False)
    op.create_index(op.f('ix_entity_similarities_entity2_id'), 'entity_similarities', ['entity2_id'], unique=False)

    # 创建图分析与实体的多对多关系表
    op.create_table('graph_analysis_entities',
        sa.Column('graph_analysis_id', sa.Integer(), nullable=False),
        sa.Column('financial_entity_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['financial_entity_id'], ['financial_entities.id'], ),
        sa.ForeignKeyConstraint(['graph_analysis_id'], ['graph_analyses.id'], ),
        sa.PrimaryKeyConstraint('graph_analysis_id', 'financial_entity_id')
    )


def downgrade() -> None:
    # 删除表
    op.drop_table('graph_analysis_entities')
    
    op.drop_index(op.f('ix_entity_similarities_entity2_id'), table_name='entity_similarities')
    op.drop_index(op.f('ix_entity_similarities_entity1_id'), table_name='entity_similarities')
    op.drop_index(op.f('ix_entity_similarities_user_id'), table_name='entity_similarities')
    op.drop_index(op.f('ix_entity_similarities_id'), table_name='entity_similarities')
    op.drop_table('entity_similarities')
    
    op.drop_index(op.f('ix_graph_embeddings_knowledge_graph_id'), table_name='graph_embeddings')
    op.drop_index(op.f('ix_graph_embeddings_user_id'), table_name='graph_embeddings')
    op.drop_index(op.f('ix_graph_embeddings_id'), table_name='graph_embeddings')
    op.drop_table('graph_embeddings')
    
    op.drop_index(op.f('ix_gnn_predictions_gnn_model_id'), table_name='gnn_predictions')
    op.drop_index(op.f('ix_gnn_predictions_user_id'), table_name='gnn_predictions')
    op.drop_index(op.f('ix_gnn_predictions_id'), table_name='gnn_predictions')
    op.drop_table('gnn_predictions')
    
    op.drop_index(op.f('ix_gnn_models_knowledge_graph_id'), table_name='gnn_models')
    op.drop_index(op.f('ix_gnn_models_user_id'), table_name='gnn_models')
    op.drop_index(op.f('ix_gnn_models_id'), table_name='gnn_models')
    op.drop_table('gnn_models')
    
    op.drop_index(op.f('ix_graph_analyses_knowledge_graph_id'), table_name='graph_analyses')
    op.drop_index(op.f('ix_graph_analyses_user_id'), table_name='graph_analyses')
    op.drop_index(op.f('ix_graph_analyses_id'), table_name='graph_analyses')
    op.drop_table('graph_analyses')
    
    op.drop_index(op.f('ix_knowledge_graphs_user_id'), table_name='knowledge_graphs')
    op.drop_index(op.f('ix_knowledge_graphs_id'), table_name='knowledge_graphs')
    op.drop_table('knowledge_graphs')
    
    op.drop_index(op.f('ix_entity_relations_relation_type'), table_name='entity_relations')
    op.drop_index(op.f('ix_entity_relations_target_entity_id'), table_name='entity_relations')
    op.drop_index(op.f('ix_entity_relations_source_entity_id'), table_name='entity_relations')
    op.drop_index(op.f('ix_entity_relations_relation_id'), table_name='entity_relations')
    op.drop_index(op.f('ix_entity_relations_user_id'), table_name='entity_relations')
    op.drop_index(op.f('ix_entity_relations_id'), table_name='entity_relations')
    op.drop_table('entity_relations')
    
    op.drop_index(op.f('ix_financial_entities_stock_code'), table_name='financial_entities')
    op.drop_index(op.f('ix_financial_entities_entity_type'), table_name='financial_entities')
    op.drop_index(op.f('ix_financial_entities_entity_name'), table_name='financial_entities')
    op.drop_index(op.f('ix_financial_entities_entity_id'), table_name='financial_entities')
    op.drop_index(op.f('ix_financial_entities_user_id'), table_name='financial_entities')
    op.drop_index(op.f('ix_financial_entities_id'), table_name='financial_entities')
    op.drop_table('financial_entities')
